import logging

from headers.models import Header


logger = logging.getLogger("www")


def dynamic_headers(request):
    custom_header = None

    try:
        url = request.path_info.lower().strip("/")

        for header in Header.objects.filter(active=True):
            if header.url.lower().strip("/") == url:
                custom_header = header.content
                break
    except Exception as err:
        logger.exception("Dynamic header error")

    return {
        "dynamic_custom_header": custom_header,
    }
