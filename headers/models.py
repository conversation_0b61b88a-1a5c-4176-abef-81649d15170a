from django.db import models


class Header(models.Model):
    url = models.CharField(
        "URL",
        max_length=250,
        unique=True,
        help_text='Bez domeny i protokołu. Np. /pl/kurs-java-programowanie/'
    )
    content = models.TextField(
        "sekcja head",
        help_text='<PERSON><PERSON><PERSON><PERSON><PERSON>, aby po dodaniu lub aktualizacji wpisu wyczyścić CACHE!'
    )
    active = models.BooleanField("aktywny?", default=True)       

    # Daty
    created_at = models.DateTimeField(
        "utworzono", auto_now_add=True, null=True, db_index=True
    )
    updated_at = models.DateTimeField("aktualizowano", auto_now=True, null=True)

    class Meta:
        verbose_name = "Dynamiczny nagłówek"
        verbose_name_plural = "Dynamiczne nagłówki"

    def __str__(self):
        return str(self.url)

    def save(self, *args, **kwargs):
        self.url = self.url.lower().strip()
        return super(Header, self).save(*args, **kwargs)
