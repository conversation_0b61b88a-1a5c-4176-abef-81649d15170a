# Generated by Django 2.2.16 on 2023-03-11 13:41

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Header',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('url', models.CharField(help_text='Bez domeny i protokołu. Np. /pl/kurs-java-programowanie/', max_length=250, unique=True, verbose_name='URL')),
                ('content', models.TextField(help_text='Pam<PERSON><PERSON>taj, aby po dodaniu lub aktualizacji wpisu wyczyścić CACHE!', verbose_name='sekcja head')),
                ('active', models.BooleanField(default=True, verbose_name='aktywny?')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True, null=True, verbose_name='utwo<PERSON>ono')),
                ('updated_at', models.DateTimeField(auto_now=True, null=True, verbose_name='aktualizowano')),
            ],
            options={
                'verbose_name': 'Dynamiczny nagłówek',
                'verbose_name_plural': 'Dynamiczne nagłówki',
            },
        ),
    ]
