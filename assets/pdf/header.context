\enableregime[utf]  % use UTF-8

\setupfootertexts[
Skwer kard. S. Wyszyńskiego 9, 01-015 <PERSON>zawa\\
tel. 022 63 64 164, fax 022 63 67 662\\
]

\setupcolors[state=start]
\setupinteraction[state=start, color=middleblue] % needed for hyperlinks

\setuppapersize[A4][A4]  % use letter paper
\setuplayout[width=middle, backspace=1.5in, cutspace=1.5in,
             height=middle, header=0.75in, footer=0.75in] % page layout
% \setuppagenumbering[location={footer,center}]  % number pages
\setupbodyfont[12pt]  % 11pt font
\setupwhitespace[medium]  % inter-paragraph spacing

\setuphead[section][style=\tfc]
\setuphead[subsection][style=\tfb]
\setuphead[subsubsection][style=\bf]

% define title block commands
\unprotect
\def\doctitle#1{\gdef\@title{#1}}
\def\author#1{\gdef\@author{#1}}
\def\date#1{\gdef\@date{#1}}
\date{\currentdate}  % Default to today unless specified otherwise.
\def\maketitle{%
  \startalignment[center]
    \blank[2*big]
      {\tfd \@title}
    \blank[3*medium]
      {\tfa \@author}
    \blank[2*medium]
      {\tfa \@date}
    \blank[3*medium]
  \stopalignment}
\protect

% define descr (for definition lists)
\definedescription[descr][
  headstyle=bold,style=normal,align=left,location=hanging,
  width=broad,margin=1cm]

% define defaults for bulleted lists 
\setupitemize[1][symbol=1][indentnext=no]
\setupitemize[2][symbol=2][indentnext=no]
\setupitemize[3][symbol=3][indentnext=no]
\setupitemize[4][symbol=4][indentnext=no]

\setupthinrules[width=15em]  % width of horizontal rules

% for block quotations
\unprotect

\startvariables all
blockquote: blockquote
\stopvariables

\definedelimitedtext
[\v!blockquote][\v!quotation]

\setupdelimitedtext
[\v!blockquote]
[\c!left=,
\c!right=,
before={\blank[medium]},
after={\blank[medium]},
]

\protect
	
