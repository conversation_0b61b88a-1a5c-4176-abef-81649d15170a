from django.db import models


class OptOut(models.Model):
    email = models.EmailField(
        "adres email",
        max_length=200,
        unique=True,
        help_text="Rekord opt-out z adresem email wpisanym w to pole spowoduje, że ten adres email NIE będzie otrzymywał żadnych maili z usług: a) absolwenci-kontynuacje b) powiadomienia o terminach c) newsletter oraz ew. innych maili HANDLOWYCH w przyszłości.",
    )

    internal_comments = models.TextField(
        "uwagi internal",
        blank=True,
    )
    # Daty
    created_at = models.DateTimeField(
        "utworzono", auto_now_add=True, null=True, db_index=True
    )
    updated_at = models.DateTimeField("aktualizowano", auto_now=True, null=True)

    class Meta:
        verbose_name = "opt-out email"
        verbose_name_plural = "opt-out email"

    def __str__(self):
        return self.email

    def save(self, *args, **kwargs):
        self.email = self.email.lower().strip()
        return super(OptOut, self).save(*args, **kwargs)
