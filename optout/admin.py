import csv
from io import StringIO

from django.conf import settings
from django.conf.urls import url
from django.contrib import admin, messages
from django.core.exceptions import ValidationError
from django.core.validators import validate_email
from django.shortcuts import redirect, render
from django.urls import reverse

from www.actions import export_as_csv_action_v2
from .forms import CsvImportForm
from .models import OptOut


class OptOutAdmin(admin.ModelAdmin):
    change_list_template = "optout/change_list.html"
    list_display = (
        "email",
        "internal_comments",
        "created_at",
    )
    search_fields = (
        "email",
        "internal_comments",
    )
    list_filter = ("created_at",)
    fieldsets = (
        (
            None,
            {
                "fields": (
                    "email",
                    "internal_comments",
                )
            },
        ),
    )
    save_on_top = True
    date_hierarchy = "created_at"
    actions = [
        export_as_csv_action_v2(
            "Eksport CSV (marketing)",
            fields=[
                ("ID", lambda x: x.pk),
                ("email", lambda x: x.email),
                ("internal_comments", lambda x: x.internal_comments),
                (
                    "admin",
                    lambda x: "https://{}{}".format(
                        settings.DOMENY_DLA_JEZYKOW["pl"],
                        reverse("admin:optout_optout_change", args=(x.pk,)),
                    ),
                ),
                ("created_at", lambda x: str(x.created_at)),
            ],
        )
    ]

    def get_urls(self):
        urls = super().get_urls()
        my_urls = [
            url("import-opt-outs/", self.admin_site.admin_view(self.import_opt_outs)),
        ]
        return my_urls + urls

    def import_opt_outs(self, request):
        if request.method == "POST":
            form = CsvImportForm(request.POST, request.FILES)
            if form.is_valid():
                try:
                    csv_file = StringIO(request.FILES["csv_file"].read().decode())
                    reader = csv.reader(csv_file, delimiter=";")
                except:
                    messages.error(request, "Nie można przetworzyć pliku.")
                    raise

                else:
                    self.process_opt_outs(request, reader)
                    messages.success(request, "Dane zostały zaimportowane.")
                return redirect("..")
        else:
            form = CsvImportForm()

        payload = {"form": form}
        return render(request, "optout/csv_form.html", payload)

    def process_opt_outs(self, request, reader):
        for row in reader:
            if row:
                try:
                    validate_email(row[0])
                except ValidationError:
                    messages.error(
                        request, "Nie można przetworzyć adresu={}".format(row[0])
                    )
                else:
                    OptOut.objects.get_or_create(
                        email=row[0].lower(),
                        defaults={"internal_comments": row[1] or ""},
                    )


admin.site.register(OptOut, OptOutAdmin)
