# Generated by Django 1.11.29 on 2021-04-25 11:38
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="OptOut",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "email",
                    models.EmailField(
                        max_length=200,
                        unique=True,
                        verbose_name="adres email",
                        help_text="Rekord opt-out z adresem email wpisanym w to pole spowoduje, że ten adres email NIE będzie otrzymywał żadnych maili z usług: a) absolwenci-kontynuacje b) powiadomienia o terminach c) newsletter oraz ew. innych maili HANDLOWYCH w przyszłości.",
                    ),
                ),
                (
                    "internal_comments",
                    models.TextField(blank=True, verbose_name="uwagi internal"),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True,
                        db_index=True,
                        null=True,
                        verbose_name="utworzono",
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        auto_now=True, null=True, verbose_name="aktualizowano"
                    ),
                ),
            ],
            options={
                "verbose_name": "opt-out email",
                "verbose_name_plural": "opt-out email",
            },
        ),
    ]
