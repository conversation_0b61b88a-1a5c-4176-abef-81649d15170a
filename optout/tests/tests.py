from django.db import IntegrityError

from optout.testfactories import OptOutFactory
from optout.utils import email_optout
from www.testhelpers import ALXTestCase


class OptOutCase(ALXTestCase):
    def test_uniqueness_with_email(self):
        e = OptOutFactory.create()

        self.assertRaises(
            IntegrityError,
            OptOutFactory.create,
            email=e.email.upper(),
        )

    def test_email_optout(self):
        OptOutFactory.create(email="<EMAIL>")

        self.assertTrue(email_optout("<EMAIL>"))
        self.assertTrue(email_optout("<EMAIL>"))
        self.assertFalse(email_optout("<EMAIL>"))
