
# Aktualizacja cen obiadów


```python
from django.utils import timezone
from django.urls import reverse
from www.models import *

today = timezone.now().date()

def get_admin_url(obj):
    app_label = obj._meta.app_label
    model_name = obj._meta.model_name
    object_id = obj.id
    return "https://www.alx.pl" + reverse('admin:%s_%s_change' % (app_label, model_name), args=[object_id])

terminy = TerminSzkolenia.objects.filter(obiady="obiady-wliczone", termin__gt=today)

with open("2024_04_08_linki_do_zaktualizowanych_terminow.txt", "w") as f:
    f.write("id;szkolenie,termin,link")
    for t in terminy:
        f.write(f"{t.id};{t.szkolenie.kod};{t.termin};{get_admin_url(t)}\n")

terminy.update(obiady="obiady-opcjonalne")
```