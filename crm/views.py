from django.contrib import messages
from django.contrib.auth.decorators import permission_required
from django.http import HttpResponseRedirect
from django.urls import reverse
from django.utils.decorators import method_decorator
from django.views.generic import TemplateView

from crm.models import Firma


class PolaczFirmyView(TemplateView):
    template_name = "crm/polacz_firmy.html"

    @method_decorator(permission_required("crm.change_firma"))
    def dispatch(self, request, *args, **kwargs):
        self.firmy = list(Firma.objects.filter(id__in=request.GET["firmy"].split(",")))
        if "glowna_firma" in request.GET:
            glowna_firma = Firma.objects.get(id=request.GET["glowna_firma"])
            self.firmy.remove(glowna_firma)
            glowna_firma.polacz_z(self.firmy, request)
            messages.add_message(request, messages.INFO, "Pomyślnie połączono firmy.")
            return HttpResponseRedirect(
                reverse("admin:crm_firma_change", args=(glowna_firma.id,))
            )
        return super().dispatch(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["firmy"] = self.firmy
        return context
