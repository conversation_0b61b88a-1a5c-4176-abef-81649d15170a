from django.utils.safestring import mark_safe

try:
    from collections import OrderedDict
except ImportError:  # OrderedDict jest dopiero od Pythona 2.7
    from .ordered_dict import OrderedDict

from django.contrib.auth.models import User
from django.db import models  # , DatabaseError
from django.db.models import Sum
from django.db.models.signals import post_delete, post_save
from django.dispatch import receiver

from i18n.models import Waluta


class Firma(models.Model):
    class Meta:
        verbose_name_plural = "firmy"
        ordering = ("-czas_ostatniej_notatki",)

    nazwa = models.CharField(max_length=128, unique=True)
    klient_kluczowy = models.BooleanField(default=False)
    zgoda_na_kontakt = models.NullBooleanField()
    opiekun_sprzedazowy = models.ForeignKey(
        User,
        blank=True,
        null=True,
        verbose_name="opiekun sprzedażowy",
        related_name="firmy_opieka_sprzedazowa",
        on_delete=models.SET_NULL,
    )
    opiekun_techniczny = models.ForeignKey(
        User,
        blank=True,
        null=True,
        related_name="firmy_opieka_techniczna",
        on_delete=models.SET_NULL,
    )
    osoba_do_kontaktu = models.TextField(blank=True)
    osoba_do_kontaktu_byla_uczestnikiem_szkolenia = models.BooleanField(
        "osoba do kontaktu była uczestnikiem szkolenia",
        default=False,
    )
    email = models.EmailField(blank=True, help_text="Do kontaktu handlowego")
    telefon = models.CharField(
        max_length=64, blank=True, help_text="Do kontaktu handlowego"
    )
    adres = models.TextField(blank=True, help_text="Do kontaktu handlowego")

    # Duplikujemy dane z innych tabel, ale dzięki temu nie mamy problemów z sortowaniem
    data_ostatniego_szkolenia = models.DateField(editable=False, blank=True, null=True)
    liczba_kupionych_terminow = models.IntegerField(
        "liczba kupionych terminów", editable=False, blank=True, null=True
    )
    liczba_przeszkolonych_uczestnikow = models.IntegerField(
        "liczba przeszkolonych uczestników", editable=False, blank=True, null=True
    )
    czas_ostatniej_notatki = models.DateTimeField(editable=False, blank=True, null=True)
    wydanych_pieniedzy_zl = models.DecimalField(
        "wydanych złotówek",
        editable=False,
        max_digits=30,
        decimal_places=2,
        blank=True,
        null=True,
    )
    wydanych_pieniedzy_gbp = models.DecimalField(
        "wydanych funtów",
        editable=False,
        max_digits=30,
        decimal_places=2,
        blank=True,
        null=True,
    )
    nip = models.CharField("NIP", max_length=40, blank=True)
    # Pola aktualizujemy za pomocą sygnałów przy modelu Uczestnik w www/models.py
    # ... oraz w metodzie save przy tworzeniu firmy

    def aktualizuj_pola_ze_statystykami(self):
        self.aktualizuj_date_ostatniego_szkolenia()
        self.aktualizuj_liczbe_przeszkolonych_uczestnikow()
        self.aktualizuj_liczbe_kupionych_terminow()
        self.aktualizuj_ilosc_wydanych_pieniedzy()
        self.save()

        # Do czasu ostatniej notatki jest osobny sygnał
        # self.aktualizuj_czas_ostatniej_notatki()

    def save(self, *args, **kwargs):
        new = not self.pk
        super().save(*args, **kwargs)
        if new:
            self.aktualizuj_pola_ze_statystykami()

    def __str__(self):
        return self.nazwa

    @classmethod
    def _utworz_z_uczestnikow(cls):
        # Metoda używana jednorazowo do utworzenia podstawowego zestawu firm

        from www.models import Uczestnik

        for uczestnik in (
            Uczestnik.objects.filter(firma=None)
            .exclude(faktura_firma="")
            .order_by("-termin__termin")
        ):
            cls.utworz_lub_pobierz_z_uczestnika(uczestnik)

    @classmethod
    def utworz_lub_pobierz_z_uczestnika(cls, uczestnik):
        if not uczestnik.faktura_firma:
            return None
        # Działa również w przypadku zgłoszenia
        if not uczestnik.firma:
            nazwa = uczestnik.faktura_firma
            try:
                uczestnik.firma = cls.objects.get(nazwa__iexact=nazwa)
            except cls.DoesNotExist:
                try:
                    uczestnik.firma = AlternatywnaNazwaFirmy.objects.get(
                        nazwa__iexact=nazwa
                    ).firma
                except AlternatywnaNazwaFirmy.DoesNotExist:
                    uczestnik.firma = cls.objects.create(nazwa=nazwa)
            uczestnik.save()
            if (
                not uczestnik.firma.email
                and not uczestnik.firma.telefon
                and (uczestnik.email or uczestnik.telefon)
            ):
                uczestnik.firma.adres = "{0}\n{1}".format(
                    uczestnik.faktura_adres, uczestnik.faktura_miejscowosc_kod
                )
                uczestnik.firma.email = uczestnik.email
                uczestnik.firma.telefon = uczestnik.telefon
                if uczestnik.osoba_do_kontaktu:
                    uczestnik.firma.osoba_do_kontaktu = uczestnik.osoba_do_kontaktu
                    uczestnik.firma.osoba_do_kontaktu_byla_uczestnikiem_szkolenia = True
                else:
                    uczestnik.firma.osoba_do_kontaktu = uczestnik.imie_nazwisko
                    uczestnik.firma.osoba_do_kontaktu_byla_uczestnikiem_szkolenia = (
                        False
                    )
                uczestnik.firma.save()
            uczestnik.firma.aktualizuj_pola_ze_statystykami()
        return uczestnik.firma

    def terminy_z_uczestnikami(self, tylko_przeszkoleni=False):
        """Uporządkowany słownik postaci (termin, uczestnicy) dla terminów, w których brali udział ludzie z tej firmy.

        Porządkowanie jest od najnowszego terminu."""

        filter_kwargs = {}
        if tylko_przeszkoleni:
            filter_kwargs["status"] = 3
        uczestnicy = self.uczestnik_set.filter(**filter_kwargs)
        terminy_z_uczestnikami = OrderedDict()
        for termin in sorted(
            set(uczestnik.termin for uczestnik in uczestnicy),
            key=lambda termin: termin.termin,
            reverse=True,
        ):
            terminy_z_uczestnikami[termin] = []
        for uczestnik in uczestnicy:
            terminy_z_uczestnikami[uczestnik.termin].append(uczestnik)
        return terminy_z_uczestnikami

    def ostatni_termin(self):
        """Data ostatniego terminu, na którym był przeszkolony uczestnik z tej firmy."""

        try:
            return (
                self.uczestnik_set.filter(status=3)
                .order_by("-termin__termin")[0]
                .termin
            )
        except IndexError:
            return None

    def data_ostatniej_notatki(self):
        if self.czas_ostatniej_notatki:
            return self.czas_ostatniej_notatki.date()
        else:
            return ""

    data_ostatniej_notatki.admin_order_field = "czas_ostatniej_notatki"

    def aktualizuj_czas_ostatniej_notatki(self, save=False):
        try:
            ostatnia_notatka = self.notatka_set.latest()
        except Notatka.DoesNotExist:
            ostatnia_notatka = None
        if (
            ostatnia_notatka
            and ostatnia_notatka.czas_utworzenia != self.czas_ostatniej_notatki
        ):
            self.czas_ostatniej_notatki = ostatnia_notatka.czas_utworzenia.date()
            save and self.save()
        return self.czas_ostatniej_notatki

    def aktualizuj_date_ostatniego_szkolenia(self, save=False):
        ostatni_termin = self.ostatni_termin()
        if ostatni_termin and ostatni_termin.termin != self.data_ostatniego_szkolenia:
            self.data_ostatniego_szkolenia = ostatni_termin.termin
            self.save()
        return self.data_ostatniego_szkolenia

    def aktualizuj_liczbe_kupionych_terminow(self, save=False):
        liczba_kupionych_terminow = len(
            self.terminy_z_uczestnikami(tylko_przeszkoleni=True)
        )
        if liczba_kupionych_terminow != self.liczba_kupionych_terminow:
            self.liczba_kupionych_terminow = liczba_kupionych_terminow
            self.save()
        return self.liczba_kupionych_terminow

    def aktualizuj_liczbe_przeszkolonych_uczestnikow(self, save=False):
        liczba_przeszkolonych_uczestnikow = sum(
            [
                uczestnik.ilosc_osob()
                for uczestnik in self.uczestnik_set.filter(status=3)
            ]
        )
        if liczba_przeszkolonych_uczestnikow != self.liczba_przeszkolonych_uczestnikow:
            self.liczba_przeszkolonych_uczestnikow = liczba_przeszkolonych_uczestnikow
            save and self.save()
        return self.liczba_przeszkolonych_uczestnikow

    def aktualizuj_ilosc_wydanych_pieniedzy(self, save=False):
        changed = False
        for sufix, nazwa in (
            ("zl", "złoty"),
            ("gbp", "funt"),
        ):
            waluta = Waluta.o_nazwie(nazwa)
            wydanych_pieniedzy = (
                self.uczestnik_set.filter(
                    status=3, termin__szkolenie__waluta=waluta
                ).aggregate(ilosc=Sum("kwota_do_zaplaty"))["ilosc"]
                or 0
            )
            nazwa_pola = "wydanych_pieniedzy_" + sufix
            if wydanych_pieniedzy != getattr(self, nazwa_pola):
                setattr(self, nazwa_pola, wydanych_pieniedzy)
                changed = True
            if changed and save:
                self.save()

    def polacz_z(self, firmy, request):
        """Wchłania zadane firmy do siebie."""

        tekst_notatki = "Połączono {0} z następującymi firmami:\n\n".format(self.nazwa)

        for firma in firmy:
            # for uczestnik in firma.uczestnik_set.all():
            #     uczestnik.firma = self
            #     uczestnik.save()
            # for notatka in firma.notatka_set.all():
            #     notatka.firma = self
            #     notatka.save()
            # for alternatywna_nazwa in firma.alternatywnanazwafirmy_set.all():
            #     alternatywna_nazwa.firma = self
            #     alternatywna_nazwa.save()
            firma.uczestnik_set.update(firma=self)
            firma.notatka_set.update(firma=self)
            firma.alternatywnanazwafirmy_set.update(firma=self)
            self.alternatywnanazwafirmy_set.create(nazwa=firma.nazwa)

            tekst_notatki += "\n".join(
                "{0}: {1}".format(field.verbose_name, getattr(firma, field.name))
                for field in firma._meta.fields
                if not getattr(firma, field.name) in [None, "", "\n"]
            )
            tekst_notatki += "\n\n"

            # usunieta_firma = OrderedDict()
            # for field in firma._meta.fields:
            #     usunieta_firma[field.verbose_name] = getattr(firma, field.name)
            # usuniete_firmy.append(usunieta_firma)

            self.notatka_set.create(
                opis_kontaktu=tekst_notatki, uzytkownik=request.user
            )

            firma.delete()
        self.aktualizuj_pola_ze_statystykami()
        self.aktualizuj_czas_ostatniej_notatki()

    @mark_safe
    def alternatywne_nazwy_html(self):
        return "<br/>".join(
            alternatywna_nazwa.nazwa
            for alternatywna_nazwa in self.alternatywnanazwafirmy_set.all()
        )

    alternatywne_nazwy_html.short_description = "alternatywne nazwy"

    # def data_ostatniego_szkolenia(self):
    #     return self.ostatni_termin().termin


class AlternatywnaNazwaFirmy(models.Model):
    class Meta:
        verbose_name_plural = "alternatywne nazwy firm"

    firma = models.ForeignKey(Firma, on_delete=models.CASCADE)
    nazwa = models.CharField(max_length=128, unique=True)

    def __str__(self):
        return "{0} ({1})".format(self.nazwa, self.firma)


class Notatka(models.Model):
    class Meta:
        verbose_name_plural = "notatki"
        get_latest_by = "czas_utworzenia"

    firma = models.ForeignKey(Firma, on_delete=models.CASCADE)
    opis_kontaktu = models.TextField()
    czas_utworzenia = models.DateTimeField(auto_now_add=True)
    uzytkownik = models.ForeignKey(
        User,
        related_name="notatki_o_firmach",  # Konflikt z wynajmy.models.Notatka...
        verbose_name="użytkownik",
        on_delete=models.CASCADE,
    )

    def __str__(self):
        return "{0} — {1}".format(self.uzytkownik, self.firma)


@receiver(post_save, sender=Notatka)
@receiver(post_delete, sender=Notatka)
def aktualizuj_czas_ostatniej_notatki(sender, instance, **kwargs):
    instance.firma.aktualizuj_czas_ostatniej_notatki(save=True)
