import datetime
import operator
from functools import reduce

from dateutil.relativedelta import relativedelta
from django import forms
from django.contrib import admin
from django.contrib.admin.options import IncorrectLookupParameters
from django.contrib.admin.utils import lookup_needs_distinct
from django.contrib.auth.models import User
from django.core.exceptions import ImproperlyConfigured, SuspiciousOperation
from django.db import models
from django.http import HttpResponseRedirect
from django.urls import reverse
from django.utils.safestring import mark_safe
from rangefilter.filters import DateRangeFilter


from www.actions import export_as_csv_action_v2
from .models import AlternatywnaNazwaFirmy, Firma, Notatka


class NotatkaInline(admin.TabularInline):
    model = Notatka
    readonly_fields = ("uzytkownik", "czas_utworzenia")

    fields = ("opis_kontaktu", "uzytkownik", "czas_utworzenia")


class PowyzejDatyListFilter(admin.SimpleListFilter):
    # title = u'data ostatnie'
    # parameter_name = 'ostatnie_szkolenie'
    field_name = ""
    now_function = None  # dateime.date.today lub datetime.datetime.now

    def lookups(self, request, model_admin):
        return (
            ("-1", "ostatni miesiąc"),
            ("1", "powyżej miesiąca"),
            ("6", "powyżej 6 miesięcy"),
            ("12", "powyżej roku"),
            ("None", "brak"),
        )

    def queryset(self, request, queryset):
        if not self.value():
            return queryset
        if self.value() == "None":
            return queryset.filter(**{self.field_name: None})
        value = int(self.value())
        data_graniczna = self.now_function() - relativedelta(months=abs(value))
        if value > 0:
            lookup_suffix = "lt"
        else:
            lookup_suffix = "gte"
        return queryset.filter(
            **{self.field_name + "__" + lookup_suffix: data_graniczna}
        )


class OstatnieSzkolenieListFilter(PowyzejDatyListFilter):
    title = "data ostatniego szkolenia"
    parameter_name = "ostatnie_szkolenie"
    field_name = "data_ostatniego_szkolenia"
    now_function = datetime.date.today


class OstatniaNotatkaListFilter(PowyzejDatyListFilter):
    title = "data ostatniej notatki"
    parameter_name = "ostatnia_notatka"
    field_name = "czas_ostatniej_notatki"
    now_function = datetime.datetime.now


def polacz_firmy(modeladmin, request, queryset):
    return HttpResponseRedirect(
        reverse("polacz_firmy")
        + "?firmy="
        + ",".join(str(firma.id) for firma in queryset)
    )


polacz_firmy.short_description = "Połącz wybrane firmy"


class UserChoiceField(forms.ModelChoiceField):
    def label_from_instance(self, obj):
        return obj.get_full_name() or obj.username


class FirmaChangeList(admin.views.main.ChangeList):
    def get_queryset(self, request):
        # Robimy kopiuj-wklej całej metody, żeby zmienić ją w jednym miejscu
        # i umożliwić wyszukiwanie po alternatywnych nazwach firm.

        # First, we collect all the declared list filters.
        (
            self.filter_specs,
            self.has_filters,
            remaining_lookup_params,
            use_distinct,
        ) = self.get_filters(request)

        # Then, we let every list filter modify the queryset to its liking.
        qs = self.root_queryset
        for filter_spec in self.filter_specs:
            new_qs = filter_spec.queryset(request, qs)
            if new_qs is not None:
                qs = new_qs

        try:
            # Finally, we apply the remaining lookup parameters from the query
            # string (i.e. those that haven't already been processed by the
            # filters).
            qs = qs.filter(**remaining_lookup_params)
        except (SuspiciousOperation, ImproperlyConfigured):
            # Allow certain types of errors to be re-raised as-is so that the
            # caller can treat them in a special way.
            raise
        except Exception as e:
            # Every other error is caught with a naked except, because we don't
            # have any other way of validating lookup parameters. They might be
            # invalid if the keyword arguments are incorrect, or if the values
            # are not in the correct type, so we might get FieldError,
            # ValueError, ValidationError, or ?.
            raise IncorrectLookupParameters(e)

        # Use select_related() if one of the list_display options is a field
        # with a relationship and the provided queryset doesn't already have
        # select_related defined.
        # if not qs.query.select_related:
        #     if self.list_select_related:
        #         qs = qs.select_related()
        #     else:
        #         for field_name in self.list_display:
        #             try:
        #                 field = self.lookup_opts.get_field(field_name)
        #             except models.FieldDoesNotExist:
        #                 pass
        #             else:
        #                 if isinstance(field.rel, models.ManyToOneRel):
        #                     qs = qs.select_related()
        #                     break

        qs = qs.select_related().prefetch_related('alternatywnanazwafirmy_set')

        # Set ordering.
        ordering = self.get_ordering(request, qs)
        qs = qs.order_by(*ordering)

        # Apply keyword searches.
        def construct_search(field_name):
            if field_name.startswith("^"):
                return "%s__istartswith" % field_name[1:]
            elif field_name.startswith("="):
                return "%s__iexact" % field_name[1:]
            elif field_name.startswith("@"):
                return "%s__search" % field_name[1:]
            else:
                return "%s__icontains" % field_name

        if self.search_fields and self.query:
            # Początek zmian
            q_parametr = request.GET["q"]
            alternatywne_nazwy_firm_ids = [
                nazwa.firma.id
                for nazwa in AlternatywnaNazwaFirmy.objects.filter(
                    nazwa__icontains=q_parametr
                )
            ]

            orm_lookups = [
                construct_search(str(search_field))
                for search_field in self.search_fields
            ]
            for bit in self.query.split():
                or_queries = [
                    models.Q(**{orm_lookup: bit})
                    | models.Q(**{"id__in": alternatywne_nazwy_firm_ids})
                    for orm_lookup in orm_lookups
                ]
                # Koniec zmian
                qs = qs.filter(reduce(operator.or_, or_queries))
            if not use_distinct:
                for search_spec in orm_lookups:
                    if lookup_needs_distinct(self.lookup_opts, search_spec):
                        use_distinct = True
                        break

        if use_distinct:
            return qs.distinct()
        else:
            return qs


class FirmaAdminForm(forms.ModelForm):
    class Meta:
        model = Firma
        fields = "__all__"
        exclude = ()

    opiekun_sprzedazowy = UserChoiceField(
        queryset=User.objects.filter(is_active=True).order_by("last_name"),
        required=False,
    )
    opiekun_techniczny = UserChoiceField(
        queryset=User.objects.filter(is_active=True).order_by("last_name"),
        required=False,
    )


def skroc_nazwe(nazwa_pola, skrocona_nazwa):
    def skrocona(obiekt):
        wartosc = getattr(obiekt, nazwa_pola)
        if callable(wartosc):
            return wartosc()
        else:
            return wartosc

    skrocona.short_description = mark_safe(skrocona_nazwa)
    metoda = getattr(Firma, nazwa_pola, None)
    if metoda:
        if hasattr(metoda, "admin_order_field"):
            skrocona.admin_order_field = metoda.admin_order_field
    else:
        skrocona.admin_order_field = nazwa_pola
    return skrocona


class FirmaAdmin(admin.ModelAdmin):
    inlines = [NotatkaInline]

    list_display = (
        "nazwa",
        "alternatywne_nazwy_html",
        "wydanych_pln",
        "wydanych_gbp",
        "data_ostatniego_szkolenia",
        "data_ostatniej_notatki",
        "liczba_kupionych_terminow",
        "liczba_przeszkolonych_uczestnikow",
    )
    list_filter = (
        ('data_ostatniego_szkolenia', DateRangeFilter),
        OstatniaNotatkaListFilter,
        "klient_kluczowy",
        "zgoda_na_kontakt",
    )
    search_fields = ("nazwa",)
    save_on_top = True
    ordering = ("-wydanych_pieniedzy_zl",)
    form = FirmaAdminForm
    actions = [
        polacz_firmy,
        export_as_csv_action_v2(
            "Eksport CSV (crm)",
            fields=[
                ("ID", lambda x: x.pk),
                ("nazwa", lambda x: x.nazwa),
                ("NIP", lambda x: x.nip),
                ("wydane_PLN", lambda x: x.wydanych_pieniedzy_zl),
                ("wydane_GBP", lambda x: x.wydanych_pieniedzy_gbp),
                ("liczba_kupionych_terminow", lambda x: x.liczba_kupionych_terminow),
                ("liczba_przeszkolonych_uczestnikow", lambda x: x.liczba_przeszkolonych_uczestnikow),
                ("osoba_do_kontaktu", lambda x: x.osoba_do_kontaktu),
                ("email_do_kontaktu", lambda x: x.email),
            ],
            optout_email_field="email",
        )
    ]

    def get_actions(self, request):
        actions = super().get_actions(request)
        try:
            del actions["delete_selected"]
        except KeyError:
            pass
        return actions

    def wydanych_pln(self, obj):
        return int(obj.wydanych_pieniedzy_zl) if obj.wydanych_pieniedzy_zl else 0

    wydanych_pln.short_description = mark_safe("wydanych<br/>PLN")
    wydanych_pln.admin_order_field = "wydanych_pieniedzy_zl"

    def wydanych_gbp(self, obj):
        return int(obj.wydanych_pieniedzy_gbp) if obj.wydanych_pieniedzy_gbp else 0

    wydanych_gbp.short_description = mark_safe("wydanych<br/>GBP")
    wydanych_gbp.admin_order_field = "wydanych_pieniedzy_gbp"

    def save_formset(self, request, form, formset, change):
        notatki = formset.save(commit=False)
        for notatka in notatki:
            if not notatka.pk:
                notatka.uzytkownik = request.user
                notatka.save()
        formset.save_m2m()

    def get_changelist(self, request, **kwargs):
        return FirmaChangeList


admin.site.register(Firma, FirmaAdmin)
