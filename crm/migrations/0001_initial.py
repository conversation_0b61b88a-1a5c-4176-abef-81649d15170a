import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="AlternatywnaNazwaFirmy",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("nazwa", models.CharField(unique=True, max_length=128)),
            ],
            options={
                "verbose_name_plural": "alternatywne nazwy firm",
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="Firma",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("nazwa", models.CharField(unique=True, max_length=128)),
                ("klient_kluczowy", models.BooleanField(default=False)),
                ("zgoda_na_kontakt", models.NullBooleanField()),
                ("osoba_do_kontaktu", models.TextField(blank=True)),
                (
                    "osoba_do_kontaktu_byla_uczestnikiem_szkolenia",
                    models.BooleanField(
                        default=False,
                        verbose_name="osoba do kontaktu była uczestnikiem szkolenia",
                    ),
                ),
                (
                    "email",
                    models.EmailField(
                        help_text="Do kontaktu handlowego", max_length=75, blank=True
                    ),
                ),
                (
                    "telefon",
                    models.CharField(
                        help_text="Do kontaktu handlowego", max_length=64, blank=True
                    ),
                ),
                (
                    "adres",
                    models.TextField(help_text="Do kontaktu handlowego", blank=True),
                ),
                (
                    "data_ostatniego_szkolenia",
                    models.DateField(null=True, editable=False, blank=True),
                ),
                (
                    "liczba_kupionych_terminow",
                    models.IntegerField(
                        verbose_name="liczba kupionych terminów",
                        null=True,
                        editable=False,
                        blank=True,
                    ),
                ),
                (
                    "liczba_przeszkolonych_uczestnikow",
                    models.IntegerField(
                        verbose_name="liczba przeszkolonych uczestników",
                        null=True,
                        editable=False,
                        blank=True,
                    ),
                ),
                (
                    "czas_ostatniej_notatki",
                    models.DateTimeField(null=True, editable=False, blank=True),
                ),
                (
                    "wydanych_pieniedzy_zl",
                    models.DecimalField(
                        decimal_places=2,
                        editable=False,
                        max_digits=30,
                        blank=True,
                        null=True,
                        verbose_name="wydanych złotówek",
                    ),
                ),
                (
                    "wydanych_pieniedzy_gbp",
                    models.DecimalField(
                        decimal_places=2,
                        editable=False,
                        max_digits=30,
                        blank=True,
                        null=True,
                        verbose_name="wydanych funtów",
                    ),
                ),
                (
                    "opiekun_sprzedazowy",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="firmy_opieka_sprzedazowa",
                        verbose_name="opiekun sprzedażowy",
                        blank=True,
                        to=settings.AUTH_USER_MODEL,
                        null=True,
                    ),
                ),
                (
                    "opiekun_techniczny",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="firmy_opieka_techniczna",
                        blank=True,
                        to=settings.AUTH_USER_MODEL,
                        null=True,
                    ),
                ),
            ],
            options={
                "ordering": ("-czas_ostatniej_notatki",),
                "verbose_name_plural": "firmy",
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="Notatka",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("opis_kontaktu", models.TextField()),
                ("czas_utworzenia", models.DateTimeField(auto_now_add=True)),
                (
                    "firma",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="crm.Firma"
                    ),
                ),
                (
                    "uzytkownik",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="notatki_o_firmach",
                        verbose_name="użytkownik",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "get_latest_by": "czas_utworzenia",
                "verbose_name_plural": "notatki",
            },
            bases=(models.Model,),
        ),
        migrations.AddField(
            model_name="alternatywnanazwafirmy",
            name="firma",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="crm.Firma"
            ),
            preserve_default=True,
        ),
    ]
