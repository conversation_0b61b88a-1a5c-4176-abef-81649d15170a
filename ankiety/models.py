import datetime
import hashlib
import string
from functools import reduce
from random import choice

from django.conf import settings
from django.contrib.contenttypes.fields import GenericForeignKey, GenericRelation
from django.contrib.contenttypes.models import ContentType
from django.core.exceptions import ObjectDoesNotExist
from django.db import models
from django.urls import reverse
from django.utils.encoding import force_bytes
from django.utils.safestring import mark_safe
from django.utils.translation import ugettext_lazy as _

from www.models import Prowadzacy, TerminSzkolenia


class Szablon(models.Model):
    class Meta:
        verbose_name_plural = "szablony"

    nazwa = models.CharField(max_length=50, unique=True)
    tytul = models.CharField(
        "tytuł",
        max_length=128,
        blank=True,
        help_text="Jeśli zostanie podany, wyświetli się przy wypełnianiu ankiety zamiast "
        "standardowego „Ankieta dot. szkolenia [...]”",
    )
    opis = models.TextField()
    aktywny = models.BooleanField(
        default=True, help_text="Od<PERSON><PERSON><PERSON>, jeśli nie ma być dostępny w nowych ankietach."
    )

    @staticmethod
    def znajdz(ankiety_queryset):
        """
        Znajduje wspólny szablon dla danego zbioru ankiet.
        """
        if ankiety_queryset:
            szablon = reduce(
                lambda szablon, ankieta: szablon == ankieta.szablon and szablon,
                ankiety_queryset,
                ankiety_queryset[0].szablon,
            )
            if szablon:
                return szablon
        raise Szablon.DoesNotExist

    def __str__(self):
        return self.nazwa

    def liczba_pytan(self):
        """
        Zwraca łączną liczbę pytań, uwzględniając rozbicie na podpytania.
        """
        return sum(
            [pytanie.podpytanie_set.count() or 1 for pytanie in self.pytanie_set.all()]
        )

    def splaszcz(self):
        """
        Zwraca listę (pod)pytań w formie przystosowanej do eksportu do Excela.
        """
        lista_pytan = []
        for pytanie in self.pytanie_set.all():
            if pytanie.typ > 1:
                for podpytanie in pytanie.podpytanie_set.all():
                    podpytanie.nazwa = pytanie.nazwa + " " + podpytanie.nazwa
                    lista_pytan.append(podpytanie)
            else:
                lista_pytan.append(pytanie)
        return lista_pytan

    def sklonuj(self):
        pytania = self.pytanie_set.all()
        self.id = None
        self.nazwa = self.nazwa + " (klon)"
        self.save()
        for pytanie in pytania:
            podpytania = pytanie.podpytanie_set.all()
            odpowiedzi = pytanie.odpowiedzi.all()
            pytanie.szablon = self
            pytanie.id = None
            pytanie.save()
            for podpytanie in podpytania:
                odpowiedzi2 = podpytanie.odpowiedzi.all()
                podpytanie.pytanie = pytanie
                podpytanie.id = None
                podpytanie.save()
                for odpowiedz in odpowiedzi2:
                    odpowiedz.pytanie = podpytanie
                    odpowiedz.id = None
                    odpowiedz.save()
            for odpowiedz in odpowiedzi:
                odpowiedz.pytanie = pytanie
                odpowiedz.id = None
                odpowiedz.save()


class SkladnikSzablonu(models.Model):
    class Meta:
        abstract = True
        ordering = ["kolejnosc"]

    nazwa = models.CharField(max_length=200)
    kolejnosc = models.IntegerField(
        "kolejność", help_text="Decyduje o porządku sortowania podczas wyświetlania."
    )

    def admin_url(self):
        url = reverse(
            "admin:%s_%s_change" % (self._meta.app_label, self._meta.module_name),
            args=[self.id],
        )
        return url

    def __str__(self):
        return self.nazwa


class Odpowiedz(SkladnikSzablonu):
    """
    Przypisana do (pod)pytania możliwość wyboru
    """

    class Meta(SkladnikSzablonu.Meta):
        verbose_name_plural = "odpowiedzi"

    # Polimorficzna relacja przypisująca do pytania lub podpytania
    rodzaj_pytania = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    pytanie_id = models.PositiveIntegerField()
    pytanie = GenericForeignKey("rodzaj_pytania", "pytanie_id")

    def __str__(self):
        return self.nazwa

    def jako_html(self):
        # Funkcja w szablonie używana tylko przy pytaniach typu 2
        konwersje = {
            "1": "1",
            "2": "2",
            "3": "3",
            "4": "4",
            "5": "5",
        }
        if self.nazwa in konwersje:
            return konwersje[self.nazwa]
        else:
            return self.nazwa

    def podlicz(self, ankiety_queryset, pytanie, prowadzacy=None):
        """
        Zwraca krotność zaznaczenia przez wypełniających
        dla danego zestawu ankiet.
        """
        queryset = Wybor.objects.filter(
            odpowiedz=self,
            rodzaj_pytania=ContentType.objects.get_for_model(pytanie),
            pytanie_id=pytanie.id,
            ankieta__in=ankiety_queryset,
        )
        if self.pytanie.dotyczy_prowadzacych:
            queryset = queryset.filter(prowadzacy=prowadzacy)
        return queryset.count()


class PytanieLubPodpytanie(models.Model):
    class Meta:
        abstract = True

    def podlicz_odpowiedzi(self, ankiety_queryset, prowadzacy=None):
        """
        Zwraca liczbę niepustych komentarzy/wyborów dla
        danego zestawu ankiet i prowadzącego.
        """
        if self.typ:
            if self.typ == 2:
                try:
                    # Rzucamy wyjątek, jeśli self to pytanie
                    self.pytanie

                    # Nie działa:
                    queryset = Wybor.objects.filter(odpowiedz__pytanie__pytanie=self)
                except AttributeError:
                    return None
            else:
                queryset = Wybor.objects.filter(odpowiedz__pytanie=self)
        else:
            queryset = Komentarz.objects.filter(pytanie=self).exclude(tresc="")
        if self.dotyczy_prowadzacych:
            queryset = queryset.filter(prowadzacy=prowadzacy)
        return queryset.filter(ankieta__in=ankiety_queryset).count()


TYPY_PYTAN = (
    (0, "komentarz"),
    (1, "proste"),
    (2, "z podpytaniami i jednolitym zestawem odpowiedzi"),
    (3, "z podpytaniami i zróżnicowanym zestawem odpowiedzi"),
)


class Pytanie(SkladnikSzablonu, PytanieLubPodpytanie):
    class Meta(SkladnikSzablonu.Meta):
        verbose_name_plural = "pytania"

    szablon = models.ForeignKey(Szablon, on_delete=models.PROTECT)
    dotyczy_prowadzacych = models.BooleanField(
        "dotyczy prowadzących?",
        default=False,
        help_text="Jeśli tak, odpowiedź będzie udzielana oddzielnie dla każdego prowadzącego",
    )
    odpowiedzi = GenericRelation(
        Odpowiedz, object_id_field="pytanie_id", content_type_field="rodzaj_pytania"
    )

    @property
    def typ(self):
        """
        Zwraca typ pytania w zależności od znalezionych powiązań.
        """
        if self.podpytanie_set.count():
            if self.odpowiedzi.count():
                return 2
            else:
                return 3
        else:
            if self.odpowiedzi.count():
                return 1
            else:
                return 0

    @property
    def wszystkie_odpowiedzi(self):
        """
        Zwraca menedżer odpowiedzi przypisanych do pytania,
        jeśli pytanie jest proste, pustą listę w p.p.
        """
        if self.typ == 1:
            return self.odpowiedzi
        else:
            return Odpowiedz.objects.none()

    @property
    def pelna_nazwa(self):
        return self.nazwa


class Podpytanie(SkladnikSzablonu, PytanieLubPodpytanie):
    class Meta(SkladnikSzablonu.Meta):
        verbose_name_plural = "podpytania"

    pytanie = models.ForeignKey(Pytanie, on_delete=models.CASCADE)
    odpowiedzi = GenericRelation(
        Odpowiedz, object_id_field="pytanie_id", content_type_field="rodzaj_pytania"
    )

    @property
    def dotyczy_prowadzacych(self):
        return self.pytanie.dotyczy_prowadzacych

    @property
    def typ(self):
        return self.pytanie.typ

    @property
    def wszystkie_odpowiedzi(self):
        """
        Zwraca menedżer odpowiedzi przypisanych
        do podpytania lub pytania-rodzica.
        """
        if self.typ == 2:
            return self.pytanie.odpowiedzi
        else:
            return self.odpowiedzi

    @property
    def pelna_nazwa(self):
        return "%s %s" % (self.pytanie.nazwa, self.nazwa)


class Wybor(models.Model):
    """
    Odpowiedź zaznaczona przez użytkownika.
    """

    class Meta:
        verbose_name_plural = "wybory"

    odpowiedz = models.ForeignKey(Odpowiedz, null=True, on_delete=models.CASCADE)

    # Polimorficzna relacja przypisująca do pytania lub podpytania
    rodzaj_pytania = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    pytanie_id = models.PositiveIntegerField()
    pytanie = GenericForeignKey("rodzaj_pytania", "pytanie_id")

    ankieta = models.ForeignKey("Ankieta", editable=False, on_delete=models.CASCADE)
    log = models.ForeignKey("Log", editable=False, on_delete=models.CASCADE)
    prowadzacy = models.ForeignKey(Prowadzacy, null=True, on_delete=models.SET_NULL)

    def __str__(self):
        return (self.odpowiedz and self.odpowiedz.__str__()) or ""


class Komentarz(models.Model):
    """
    Odpowiedź tekstowa użytkownika.
    """

    class Meta:
        verbose_name_plural = "komentarze"

    tresc = models.TextField("treść", blank=True)
    pytanie = models.ForeignKey(Pytanie, on_delete=models.CASCADE)
    ankieta = models.ForeignKey("Ankieta", editable=False, on_delete=models.CASCADE)
    log = models.ForeignKey("Log", editable=False, on_delete=models.CASCADE)
    prowadzacy = models.ForeignKey(
        Prowadzacy,
        null=True,
        related_name="komentarze_ankiety",
        on_delete=models.SET_NULL,
    )

    def __str__(self):
        return self.tresc


def czas_zakonczenia_default():
    return datetime.datetime.combine(
        datetime.date.today() + datetime.timedelta(days=2), datetime.time(23)
    )


class Ankieta(models.Model):
    """
    Instancja ankiety dla konkretnego terminu szkolenia.
    """

    class Meta:
        verbose_name_plural = "ankiety"
        ordering = ["-termin_szkolenia"]
        permissions = (
            ("ogladanie_wynikow_swoich_ankiet", "Może oglądać wyniki swoich ankiet"),
        )

    czas_zakonczenia = models.DateTimeField(
        "czas zakończenia", default=czas_zakonczenia_default
    )
    termin_szkolenia = models.ForeignKey(TerminSzkolenia, on_delete=models.PROTECT)
    prowadzacy = models.ManyToManyField(Prowadzacy, blank=True, related_name="ankiety")
    szablon = models.ForeignKey(Szablon, on_delete=models.PROTECT)
    lancuch_kontrolny = models.CharField(
        "łańcuch kontrolny",
        max_length=4,
        help_text="Dodawany do URL przy wypełnianiu ankiety.",
        editable=False,
    )

    def __init__(self, *args, **kwargs):
        models.Model.__init__(self, *args, **kwargs)
        if not self.lancuch_kontrolny:
            self.lancuch_kontrolny = "".join(
                [choice(string.ascii_lowercase) for i in range(4)]
            )

    def __str__(self):
        return "%s, %s, %s" % (
            self.termin_szkolenia.szkolenie,
            self.termin_szkolenia.termin,
            str(self.czas_zakonczenia),
        )

    @property
    def tytul(self):
        """
        Zwraca tytuł szablonu, jeżeli ten został podany,
        lub standardowy tekst.
        """
        return self.szablon.tytul or "%s „%s”" % (
            _("Ankieta dot. szkolenia"),
            self.termin_szkolenia.szkolenie,
        )

    def okres_waznosci(self):
        return self.czas_zakonczenia - datetime.datetime.now()

    @property
    def aktywna(self):
        return self.okres_waznosci() > datetime.timedelta(0)

    # Obecnie nieużywane
    def kolejnosc_sortowania(self):
        """
        Zwraca liczbę wyznaczającą kolejność sortowania w panelu admina.
        Najniższy priorytet otrzymują ankiety już zakończone, następnie
        ankiety aktywne, które ktoś już wypełnił, a najwyższy ankiety
        aktywne i niewypełnione.
        """
        if self.aktywna:
            if self.log_set.count():
                return -1 + 1.0 / self.log_set.count()
            else:
                try:
                    # Python 2.7
                    return self.okres_waznosci().total_seconds()
                except AttributeError:
                    return self.okres_waznosci().days
        else:
            try:
                # Python 2.7
                return self.okres_waznosci().total_seconds() - 1
            except AttributeError:
                return self.okres_waznosci().days - 1

    kolejnosc_sortowania.short_description = "kolejność sortowania"

    def liczba_wypelnien(self):
        return self.log_set.count()

    liczba_wypelnien.short_description = "liczba wypełnień"

    @mark_safe
    def przegladanie(self):
        if self.log_set.count():
            return '<a href="%s">przeglądaj</a> (%d)' % (
                reverse("przegladanie", kwargs={"log_id": self.log_set.all()[0].id}),
                self.log_set.count(),
            )
        else:
            return "brak"

    przegladanie.short_description = "wyniki"

    def relative_url(self):
        return "%s%s" % (self.id, self.lancuch_kontrolny)

    def get_absolute_url(self):
        language = self.termin_szkolenia.szkolenie.language

        return "http://%s%s" % (
            settings.DOMENY_DLA_JEZYKOW[language],
            reverse(
                "wypelnianie",
                kwargs={
                    "id": self.id,
                    "lancuch_kontrolny": self.lancuch_kontrolny,
                    "language": language,
                },
            ),
        )

    def get_thankyou_url(self):
        """
        URL do podstrony z podziękowaniem oraz przypisaniem Absolwenta do
        Uczestnika.
        """

        return reverse(
            "thankyou",
            kwargs={
                "pk": self.pk,
                "checksum": self.lancuch_kontrolny,
                "language": self.termin_szkolenia.szkolenie.language,
            },
        )

    def get_podziekowanie_url(self):
        """
        todo: po wprowadzeniu nowej wersji Absolwentów - to usunąć!
        """

        return reverse(
            "podziekowanie",
            kwargs={"language": self.termin_szkolenia.szkolenie.language},
        )

    @property
    def secret_key(self):
        """
        Stała wartość per obiekt, hashująca klucz główny. Używana do zapisu w
        cookies.
        """

        return hashlib.sha1(
            force_bytes("{0}-{1}".format(self.pk, settings.SECRET_KEY))
        ).hexdigest()

    @mark_safe
    def html_url(self):
        return '<a href="%s">%s</a>' % (
            self.get_absolute_url(),
            self.get_absolute_url(),
        )

    def szkolenie(self):
        if self.termin_szkolenia.szkolenie.kod == "SnZ":
            return "SnZ: " + self.termin_szkolenia.snz_opis
        else:
            return self.termin_szkolenia.szkolenie

    @mark_safe
    def termin_url(self):
        return '<a href="{0}">{1}</a>'.format(
            reverse(
                "admin:www_terminszkolenia_change", args=(self.termin_szkolenia.id,)
            ),
            self.termin_szkolenia.termin,
        )

    termin_url.short_description = "termin"
    termin_url.admin_order_field = "termin_szkolenia"

    @mark_safe
    def link_do_logow(self):
        return '<a href="%s">logi</a>' % (reverse("logi", args=(self.id,)),)


class Log(models.Model):
    """
    Dane wypełniającego ankietę.
    """

    class Meta:
        verbose_name_plural = "logi"

    class BrakOdpowiedzi(ObjectDoesNotExist):
        def __init__(self, log, pytanie, prowadzacy=None):
            self.log = log
            self.pytanie = pytanie
            self.prowadzacy = prowadzacy

        def __str__(self):
            return (
                "nie znaleziono odpowiedzi użytkownika dla parametrów: "
                + ", ".join(
                    [str(self.log), str(self.pytanie)]
                    + ([str(self.prowadzacy)] if self.prowadzacy else [])
                )
                + "."
            )

    ip = models.GenericIPAddressField()
    czas = models.DateTimeField()
    ankieta = models.ForeignKey(Ankieta, on_delete=models.PROTECT)

    def __str__(self):
        return "%s [%s] %s" % (self.ip, self.czas, self.ankieta.id)

    def uzyskaj_odpowiedz(self, pytanie, prowadzacy=None):
        """
        Zwraca odpowiedź użytkownika dla danego pytania i prowadzącego.
        Jeżeli pytanie nie dotyczy prowadzących, to ostatni parametr
        jest ignorowany.
        """
        (ModelOdpowiedzi, parametry_pytania) = {
            True: (
                Wybor,
                {
                    "rodzaj_pytania": ContentType.objects.get_for_model(pytanie),
                    "pytanie_id": pytanie.id,
                },
            ),
            False: (
                Komentarz,
                {
                    "pytanie": pytanie,
                },
            ),
        }[pytanie.typ > 0]
        parametry_pytania["log"] = self
        if pytanie.dotyczy_prowadzacych:
            parametry_pytania["prowadzacy"] = prowadzacy
        else:
            prowadzacy = None
        try:
            return ModelOdpowiedzi.objects.get(**parametry_pytania)
        except ObjectDoesNotExist:
            # raise Log.BrakOdpowiedzi(self, pytanie, prowadzacy)
            return ModelOdpowiedzi()

    def poprzedni_nastepny(self):
        try:
            poprzedni = Log.objects.filter(
                id__lt=self.id, ankieta=self.ankieta
            ).order_by("-id")[0]
        except IndexError:
            poprzedni = None

        try:
            nastepny = Log.objects.filter(
                id__gt=self.id, ankieta=self.ankieta
            ).order_by("id")[0]
        except IndexError:
            nastepny = None

        poprzedni_html = nastepny_html = None
        if poprzedni:
            poprzedni_html = '<a href="%s">Poprzednie wypełnienie</a>' % (
                reverse("przegladanie", kwargs={"log_id": poprzedni.id}),
            )
        if nastepny:
            nastepny_html = '<a href="%s">Następne wypełnienie</a>' % (
                reverse("przegladanie", kwargs={"log_id": nastepny.id}),
            )

        if poprzedni and nastepny:
            return " || ".join([poprzedni_html, nastepny_html])
        else:
            return poprzedni_html or nastepny_html or ""
