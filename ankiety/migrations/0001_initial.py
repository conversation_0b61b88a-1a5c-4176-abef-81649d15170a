import django.db.models.deletion
from django.db import migrations, models

import ankiety.models


class Migration(migrations.Migration):

    dependencies = [
        ("www", "0001_initial"),
        ("contenttypes", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Ankie<PERSON>",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                (
                    "czas_zakonczenia",
                    models.DateTimeField(
                        default=ankiety.models.czas_zakonczenia_default,
                        verbose_name="czas zakończenia",
                    ),
                ),
                (
                    "lancuch_kontrolny",
                    models.CharField(
                        help_text="Dodawany do URL przy wypełnianiu ankiety.",
                        verbose_name="łańcuch kontrolny",
                        max_length=4,
                        editable=False,
                    ),
                ),
                (
                    "prowadzacy",
                    models.ManyToManyField(
                        related_name="ankiety", to="www.Prowadzacy", blank=True
                    ),
                ),
            ],
            options={
                "ordering": ["-termin_szkolenia"],
                "verbose_name_plural": "ankiety",
                "permissions": (
                    (
                        "ogladanie_wynikow_swoich_ankiet",
                        "Może oglądać wyniki swoich ankiet",
                    ),
                ),
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="Komentarz",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("tresc", models.TextField(verbose_name="treść", blank=True)),
                (
                    "ankieta",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        editable=False,
                        to="ankiety.Ankieta",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "komentarze",
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="Log",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("ip", models.IPAddressField()),
                ("czas", models.DateTimeField()),
                (
                    "ankieta",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="ankiety.Ankieta",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "logi",
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="Odpowiedz",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("nazwa", models.CharField(max_length=200)),
                (
                    "kolejnosc",
                    models.IntegerField(
                        help_text="Decyduje o porządku sortowania podczas wyświetlania.",
                        verbose_name="kolejność",
                    ),
                ),
                ("pytanie_id", models.PositiveIntegerField()),
                (
                    "rodzaj_pytania",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="contenttypes.ContentType",
                    ),
                ),
            ],
            options={
                "ordering": ["kolejnosc"],
                "abstract": False,
                "verbose_name_plural": "odpowiedzi",
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="Podpytanie",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("nazwa", models.CharField(max_length=200)),
                (
                    "kolejnosc",
                    models.IntegerField(
                        help_text="Decyduje o porządku sortowania podczas wyświetlania.",
                        verbose_name="kolejność",
                    ),
                ),
            ],
            options={
                "ordering": ["kolejnosc"],
                "abstract": False,
                "verbose_name_plural": "podpytania",
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="Pytanie",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("nazwa", models.CharField(max_length=200)),
                (
                    "kolejnosc",
                    models.IntegerField(
                        help_text="Decyduje o porządku sortowania podczas wyświetlania.",
                        verbose_name="kolejność",
                    ),
                ),
                (
                    "dotyczy_prowadzacych",
                    models.BooleanField(
                        default=False,
                        help_text="Jeśli tak, odpowiedź będzie udzielana oddzielnie dla każdego prowadzącego",
                        verbose_name="dotyczy prowadzących?",
                    ),
                ),
            ],
            options={
                "ordering": ["kolejnosc"],
                "abstract": False,
                "verbose_name_plural": "pytania",
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="Szablon",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("nazwa", models.CharField(unique=True, max_length=50)),
                (
                    "tytul",
                    models.CharField(
                        help_text="Jeśli zostanie podany, wyświetli się przy wypełnianiu ankiety zamiast standardowego „Ankieta dot. szkolenia [...]”",
                        max_length=128,
                        verbose_name="tytuł",
                        blank=True,
                    ),
                ),
                ("opis", models.TextField()),
                (
                    "aktywny",
                    models.BooleanField(
                        default=True,
                        help_text="Odznacz, jeśli nie ma być dostępny w nowych ankietach.",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "szablony",
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="Wybor",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("pytanie_id", models.PositiveIntegerField()),
                (
                    "ankieta",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        editable=False,
                        to="ankiety.Ankieta",
                    ),
                ),
                (
                    "log",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        editable=False,
                        to="ankiety.Log",
                    ),
                ),
                (
                    "odpowiedz",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="ankiety.Odpowiedz",
                        null=True,
                    ),
                ),
                (
                    "prowadzacy",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="www.Prowadzacy",
                        null=True,
                    ),
                ),
                (
                    "rodzaj_pytania",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="contenttypes.ContentType",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "wybory",
            },
            bases=(models.Model,),
        ),
        migrations.AddField(
            model_name="pytanie",
            name="szablon",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT, to="ankiety.Szablon"
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="podpytanie",
            name="pytanie",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="ankiety.Pytanie"
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="komentarz",
            name="log",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                editable=False,
                to="ankiety.Log",
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="komentarz",
            name="prowadzacy",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="komentarze_ankiety",
                to="www.Prowadzacy",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="komentarz",
            name="pytanie",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="ankiety.Pytanie"
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="ankieta",
            name="szablon",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT, to="ankiety.Szablon"
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="ankieta",
            name="termin_szkolenia",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT, to="www.TerminSzkolenia"
            ),
            preserve_default=True,
        ),
    ]
