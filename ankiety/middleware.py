from django.http import HttpResponse
from django.utils.deprecation import MiddlewareMixin

from .models import Log


class ExceptionsMiddleware(MiddlewareMixin):
    def process_exception(self, request, exception):
        if isinstance(exception, Log.BrakOdpowiedzi):
            komunikat = (
                "Błąd: <PERSON>rak<PERSON><PERSON><PERSON>wiedzi: "
                + str(exception)
                + "\n\nMożliwe przyczyny:\n-dodanie nowego (pod)pytania do szablonu ankiety, który jest"
                " już w użyciu\n-błąd w bazie danych\n\nPopraw szablon lub skontaktuj się z administratorem."
            )
            return HttpResponse(komunikat, content_type="text/plain; charset=utf-8")
        return None
