{% extends "ankiety/base.html" %}
{% load i18n static %}
{% block title %}{% trans "Ankieta dot. szkolenia" %} „{{ ankieta.termin_szkolenia.szkolenie }}”{% endblock title %}

{% block h1 %}<h1>{{ ankieta.tytul }}</h1>{% endblock h1 %}

{% block body %}

<p>
    {% if log %}
        Termin szkolenia: {{ log.ankieta.termin_szkolenia }}<br/>
        Wypełniono: {{ log.czas.date }} o {{ log.czas.time }} z adresu {{ log.ip }}</p>
        <p>{{ log.poprzedni_nastepny|safe }}
    {% else %}
        {% trans "Prosimy o uważne wypełnienie ankiety." %}
    {% endif %}
</p>

<form class="ankieta" method="post" action="{% url 'wypelnianie' id=ankieta.id lancuch_kontrolny=ankieta.lancuch_kontrolny language=language %}"{% if log %} class="browse"{% endif %}>
{% csrf_token %}
<ol>
{% for dane in pytania %}

<li{# {% if dane.pytanie.dotyczy_prowadzacych and dane.prowadzacy.fotka %} class="fotka" style="background-image: url('{{ dane.prowadzacy.sylwetka_image_url }}');"{% endif %} #}>{{ dane.pytanie.nazwa }}
{% if dane.pytanie.dotyczy_prowadzacych %}
({{ dane.prowadzacy }})
  {% if dane.prowadzacy.fotka %}<br/><img src="{{ dane.prowadzacy.sylwetka_image_url }}" alt=""/>{% endif %}
{% endif %}

{{ dane.pytanie_formularz.as_p }}

{% if dane.pytanie.typ < 2 %}

{% endif %}

{% if dane.pytanie.typ == 2 %}
<table>
<thead>
<tr>
<th></th>
<th><small>{% trans "(źle)" %}</small></th>
{% for odpowiedz in dane.odpowiedzi %}
<th>{{ odpowiedz.jako_html|safe }}</th>
{% endfor %}
<th><small>{% trans "(dobrze)" %}</small></th>
</tr>
</thead>
<tbody>
{% for dane2 in dane.podpytania %}
<tr>
<td>{{ dane2.podpytanie.nazwa }}
{{ dane2.podpytanie_formularz.pytanie_id }}
{{ dane2.podpytanie_formularz.rodzaj_pytania }}
{{ dane2.podpytanie_formularz.prowadzacy }}</td>
<td></td>
{{ dane2.podpytanie_formularz.odpowiedz }}
<td></td>
</tr>
{% endfor %}
</tbody>
</table>
{% endif %}

{% if dane.pytanie.typ == 3 %}
<ul class="podpytanie">
{% for dane2 in dane.podpytania %}
<li>{{ dane2.podpytanie.nazwa }}
{{ dane2.podpytanie_formularz.as_table }}
</li>
{% endfor %}
</ul>
{% endif %}
<br/>
</li>

{% endfor %}
</ol>
{% if log %}
<p>{{ log.poprzedni_nastepny|safe }}</p>
{% else %}<input type="submit" value="{% trans 'Wyślij' context "wysylanie ankiety" %}" formnovalidate/>{% endif %}
</form>

{% endblock body %}
