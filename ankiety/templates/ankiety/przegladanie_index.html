{% extends "ankiety/base.html" %}

{% block title %}Twoje ankiety{% endblock title %}

{% block body %}

  <form method="get" action="{% url 'eksport' %}">
    <table>
      {% for a in ankiety %}
	<tr style="background-color: {% cycle 'transparent' 'white' %};">
	  <td><input type="checkbox" name="ankieta" value="{{ a.id }}"/></td>
	  <td>{{ a }}</td>
	  <td>{{ a.przegladanie|safe }}</td>
	</tr>
      {% endfor %}
    </table>
    <p>Eksportuj zaznaczone ankiety do:
    <select name="format" onchange="this.form.submit()">
      <option value="">---</option>
      <option value="xls">XLS</option>
      <option value="html">HTML</option>
    </select>
    (w przypadku niewybrania żadnych ankiet zostaną wyeksportowane wszystkie ankiety).
    </p>
  </form>



{% endblock body %}
