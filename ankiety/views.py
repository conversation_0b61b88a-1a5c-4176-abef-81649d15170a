import datetime

import xlwt
from django.conf import settings
from django.contrib import messages
from django.contrib.auth.decorators import permission_required, user_passes_test
from django.core.exceptions import PermissionDenied
from django.core.mail import EmailMessage
from django.db import transaction
from django.http import Http404, HttpResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.template.loader import render_to_string
from django.utils.translation import ugettext as _
from django.views.decorators.cache import never_cache
from ipware import get_client_ip

import www.tasks
from common import my_slugify
from common.gchart import gchart
from newsletter.forms import AnkietaEmailForm
from newsletter.models import Odbiorca
from www.models import Graduate, Prowadzacy, Uczestnik
from www.views import activate_translation

from . import csvwriter
from .forms import (
    FiltrAnkietForm,
    FormatEksportuForm,
    KomentarzForm,
    ThankYouForm,
    WyborForm,
    WyborForm2,
)
from .models import <PERSON>kie<PERSON>, Komentarz, Lo<PERSON>, <PERSON><PERSON><PERSON>


@never_cache
@user_passes_test(
    lambda u: u.has_perm("ankiety.add_ankieta")
    or u.has_perm("ankiety.ogladanie_wynikow_swoich_ankiet"),
    login_url="/admin/",
)
def przegladanie_index(request, language=settings.DEFAULT_LANGUAGE):
    moje_ankiety = Ankieta.objects.filter(prowadzacy__user=request.user)

    return render(
        request,
        "ankiety/przegladanie_index.html",
        {
            "ankiety": moje_ankiety,
        },
    )


@never_cache
@user_passes_test(
    lambda u: u.has_perm("ankiety.add_ankieta")
    or u.has_perm("ankiety.ogladanie_wynikow_swoich_ankiet"),
    login_url="/admin/",
)
def przegladanie(request, log_id, language=settings.DEFAULT_LANGUAGE):
    log = get_object_or_404(Log, pk=log_id)
    return wypelnianie(request, log.ankieta.id, log.ankieta.lancuch_kontrolny, log.id)


@activate_translation
@never_cache
def wypelnianie(
    request, id, lancuch_kontrolny, log_id=None, language=settings.DEFAULT_LANGUAGE
):

    log = get_object_or_404(Log, pk=log_id) if log_id else None
    if log and not request.user.has_perm("ankiety.add_ankieta"):
        # może przeglądać tylko wyniki swoich ankiet
        ankieta = get_object_or_404(Ankieta, pk=id, prowadzacy__user=request.user)
    else:
        ankieta = get_object_or_404(
            Ankieta.objects.prefetch_related("prowadzacy").select_related(), pk=id
        )

    # if 'new' in request.GET or 'ankieta_new' in request.COOKIES:
    redirect_url = ankieta.get_thankyou_url()
    # else:
    #    redirect_url = ankieta.get_podziekowanie_url()

    def uzyskaj_odpowiedz(log, pytanie, prowadzacy):
        if log:
            return log.uzyskaj_odpowiedz(pytanie, prowadzacy)
        return None

    if not log:
        if ankieta.lancuch_kontrolny != lancuch_kontrolny:
            raise Http404

        if ankieta.secret_key in request.COOKIES:
            return redirect(redirect_url)

        if not ankieta.aktywna:
            return HttpResponse("<h2>Nie można już wypełniać tej ankiety.</h2>")

    post = "log" if log else (request.method == "POST")
    # "log": przeglądamy wypełnioną ankietę
    # True: przetwarzamy wypełniony formularz ankiety
    # False: wyświetlamy pusty formularz ankiety

    szablon = ankieta.szablon
    prowadzacy_all = ankieta.prowadzacy.all()
    pytania = szablon.pytanie_set.prefetch_related(
        "odpowiedzi",
        "podpytanie_set",
        "podpytanie_set__odpowiedzi",
    ).select_related()
    pytania_szablon = []
    formularze = []
    licznik_pytan = 0

    for pytanie in pytania:
        for prowadzacy in {True: prowadzacy_all, False: [None]}[
            pytanie.dotyczy_prowadzacych
        ]:

            (funkcja_formularza, model_funkcji_formularza) = {
                0: (KomentarzForm, Komentarz),
                1: (WyborForm, Wybor),
                2: (WyborForm2, Wybor),
                3: (WyborForm, Wybor),
            }[pytanie.typ]
            slownik_pytania = {
                "pytanie": pytanie,
                "odpowiedzi": pytanie.odpowiedzi.all(),
                "prowadzacy": prowadzacy,
            }
            if pytanie.typ > 1:
                slownik_pytania["podpytania"] = [
                    {
                        "podpytanie": pytanie.podpytanie_set.all()[i],
                        "podpytanie_formularz": {
                            True: funkcja_formularza(
                                request.POST,
                                instance=model_funkcji_formularza(ankieta=ankieta),
                                pytanie=pytanie.podpytanie_set.all()[i],
                                prowadzacy=prowadzacy,
                                prefix=str(i + licznik_pytan),
                            ),
                            False: funkcja_formularza(
                                pytanie=pytanie.podpytanie_set.all()[i],
                                prowadzacy=prowadzacy,
                                prefix=str(i + licznik_pytan),
                            ),
                            "log": funkcja_formularza(
                                instance=uzyskaj_odpowiedz(
                                    log, pytanie.podpytanie_set.all()[i], prowadzacy
                                ),
                                pytanie=pytanie.podpytanie_set.all()[i],
                                prowadzacy=prowadzacy,
                                prefix=str(i + licznik_pytan),
                            ),
                        }[post],
                        "odpowiedzi": pytanie.podpytanie_set.all()[i].odpowiedzi.all(),
                    }
                    for i in range(0, len(pytanie.podpytanie_set.all()))
                ]
                licznik_pytan += len(pytanie.podpytanie_set.all())
                formularze += [
                    d["podpytanie_formularz"] for d in slownik_pytania["podpytania"]
                ]
            else:
                slownik_pytania["pytanie_formularz"] = {
                    True: funkcja_formularza(
                        request.POST,
                        instance=model_funkcji_formularza(ankieta=ankieta),
                        pytanie=pytanie,
                        prowadzacy=prowadzacy,
                        prefix=str(licznik_pytan),
                    ),
                    False: funkcja_formularza(
                        pytanie=pytanie,
                        prowadzacy=prowadzacy,
                        prefix=str(licznik_pytan),
                    ),
                    "log": funkcja_formularza(
                        instance=uzyskaj_odpowiedz(log, pytanie, prowadzacy),
                        pytanie=pytanie,
                        prowadzacy=prowadzacy,
                        prefix=str(licznik_pytan),
                    ),
                }[post]
                licznik_pytan += 1
                formularze.append(slownik_pytania["pytanie_formularz"])
            pytania_szablon.append(slownik_pytania)

    if post and all([formularz.is_valid() for formularz in formularze]):
        log = Log(
            ankieta=ankieta, ip=get_client_ip(request)[0], czas=datetime.datetime.now()
        )
        log.save()
        for formularz in formularze:
            odpowiedz_uzytkownika = formularz.save(commit=False)
            odpowiedz_uzytkownika.log = log
            odpowiedz_uzytkownika.save()

        response = redirect(redirect_url)

        # Dodajemy cookies informujący, że ankieta został już uzupełniona.
        response.set_cookie(
            ankieta.secret_key, value="True", max_age=30 * 24 * 60 * 60  # 1 miesiąc
        )
        return response

    response = render(
        request,
        "ankiety/wypelnianie.html",
        {
            "pytania": pytania_szablon,
            "ankieta": ankieta,
            "log": log,
            "language": language,
        },
    )
    return response


@activate_translation
@never_cache
def thankyou(request, pk, checksum, language=settings.DEFAULT_LANGUAGE):
    """
    Widok serwowany Uczestnikowi po wypełnionej Ankiecie. To tutaj podaje
    swój adres email (i ew. resztę danych), z których tworzony jest obiekt
    Absolwenta.
    Na zakończenie kierowany jest do widoku `thankyou_done`.
    """

    e = get_object_or_404(
        Ankieta.objects.select_related(), pk=pk, lancuch_kontrolny=checksum
    )

    if not e.aktywna or e.secret_key not in request.COOKIES:
        # Jeśli ankieta wygasła lub nie mam informacji w cookies, że została
        # wypełniona - nie wpuszczamy.
        raise Http404

    form_kwargs = {
        "term": e.termin_szkolenia,
    }

    if request.method == "POST":

        def save_graduate(participant, term, email, name, remote_addr):
            """
            Pomocnicza funkcja tworząca obiekt Absolwenta, uruchamiająca
            zadanie celery i zwracająca URL przekierowania.
            """

            # now = datetime.datetime.now()
            # deadline = now.replace(hour=21, minute=0, second=0, microsecond=0)

            with transaction.atomic():
                graduate = Graduate.objects.create(
                    participant=participant,
                    term=term,
                    email=email,
                    name=name,
                    remote_addr=remote_addr,
                    accepted=True if participant else False,
                )
            # Maila z linkiem do Certyfikatu wysyłamy tylko do dopasowanych
            # Absolwentów.
            if graduate.accepted:
                www.tasks.create_certificate.delay(graduate.pk)
                # # Zlecamy wyslanie maila o 21:00 (o ile jest przed 21)
                # if now < deadline:
                #     www.tasks.create_certificate.apply_async(
                #         args=(graduate.pk,),
                #         kwargs=dict(force_create_pdf=False),
                #         eta=deadline
                #     )
                # else:
                #     www.tasks.create_certificate.delay(graduate.pk,
                #                                        force_create_pdf=False)
            else:
                # Uruchamiamy zadanie celery generowania certyfikatu PDF
                # (od razu).
                www.tasks.create_certificate.delay(graduate.pk, force_send_mail=False)

                # Musimy poinformować biuro, że Uczestnik nie mógł znaleźć
                # dopasowania.
                msg_content = render_to_string(
                    "www/certificates/user_not_found_email.html",
                    {
                        "graduate": graduate,
                        "term": term,
                        "domain": settings.DOMENY_DLA_JEZYKOW["pl"],
                        "protocol": settings.FORCE_SSL and "https" or "http",
                    },
                )

                msg = EmailMessage(
                    "[Brak dopasowania Absolwenta] - {0} ({1} - {2})".format(
                        graduate.email, term.szkolenie.nazwa, term.lokalizacja.shortname
                    ),
                    msg_content,
                    settings.MAIL_FROM_ADDRESS,
                    [settings.MAIL_TO_NOTIFICATION_ALERT],
                )
                msg.content_subtype = "html"
                msg.send()

            return redirect(
                "thankyou_done",
                pk=e.pk,
                checksum=e.lancuch_kontrolny,
                uid=graduate.key.hex,
                language=language,
            )

        form = ThankYouForm(request.POST, **form_kwargs)

        if form.is_valid():
            # Rozpoczynamy wyszukiwanie od począku, czyli próbujemy
            # dopasować uczestnika tylko po adresie email.

            try:
                participant = Uczestnik.objects.filter(
                    termin=e.termin_szkolenia,
                    uczestnik_wieloosobowy_ilosc_osob__isnull=True,
                ).get(email__iexact=form.cleaned_data["email"])
            except:
                # Nie ma takiego uczestnika, czyli szukamy po imieniu i
                # nazwisku.
                pass
            else:
                # Zapisujemy uczestnika do Absolwentów i kończymy.
                return save_graduate(
                    participant,
                    e.termin_szkolenia,
                    form.cleaned_data["email"],
                    participant.imie_nazwisko,
                    get_client_ip(request)[0],
                )

            # Rozpoczynamy dopasowanie po imieniu i nazwisku
            try:
                participant = Uczestnik.objects.filter(termin=e.termin_szkolenia).get(
                    imie_nazwisko__icontains=form.cleaned_data["full_name"],
                )
            except:
                # Nie ma takiego uczestnika.
                return save_graduate(
                    None,
                    e.termin_szkolenia,
                    form.cleaned_data["email"],
                    form.cleaned_data["full_name"],
                    get_client_ip(request)[0],
                )
            else:
                # Zapisujemy uczestnika do Absolwentów i kończymy.
                return save_graduate(
                    participant,
                    e.termin_szkolenia,
                    form.cleaned_data["email"],
                    form.cleaned_data["full_name"],
                    get_client_ip(request)[0],
                )
    else:
        form = ThankYouForm(**form_kwargs)

    return render(
        request,
        "ankiety/thankyou.html",
        {
            "form": form,
        },
    )


@activate_translation
@never_cache
def thankyou_done(request, pk, checksum, uid, language=settings.DEFAULT_LANGUAGE):
    """
    Widok podziękowania po poprawnym utworzeniu obiektu Absolwenta w funkcji
    `thankyou`. Oprócz elementów statycznych, użytkownik może dołączyć do
    Newslettera (jeśli jeszcze nie jest tam zapisany).
    """

    e = get_object_or_404(
        Ankieta.objects.select_related(), pk=pk, lancuch_kontrolny=checksum
    )

    if not e.aktywna or e.secret_key not in request.COOKIES:
        # Jeśli ankieta wygasła lub nie mam informacji w cookies, że została
        # wypełniona - nie wpuszczamy.
        raise Http404

    # Sprawdzamy, czy taki Absolwent o kluczu `uid` istnieje.
    graduate = get_object_or_404(Graduate, key=uid, term=e.termin_szkolenia)

    newsletter_action = False
    newsletter_form = None

    # Sprawdzamy, czy Absolwent jest dodany do newslettera, jeśli nie należy
    # dać mu taką możliwosć.
    if not Odbiorca.objects.filter(email__iexact=graduate.email).exists():
        newsletter_action = True

        # Jeśli nastąpiła akcja POST oznacza to, że użytkownik wyraził zgodę
        # na newsletter.
        if request.method == "POST":
            newsletter_form = AnkietaEmailForm(request.POST)

            if newsletter_form.is_valid():
                Odbiorca.dodaj_potwierdzonego(
                    graduate.email,
                    uczestnik=graduate.participant,
                    zrodlo_kontaktu="ankieta",
                    lang=language,
                )
                messages.success(
                    request, _("Zostałeś poprawnie dodany do Newslettera.")
                )
                return redirect(request.path)
        else:
            newsletter_form = AnkietaEmailForm()

    return render(
        request,
        "ankiety/thankyou_done.html",
        {
            "newsletter_action": newsletter_action,
            "newsletter_form": newsletter_form,
        },
    )


def eksport_do(ankiety_queryset, format, request):
    ankiety_queryset = ankiety_queryset.select_related("szablon")
    szablony = [a.szablon for a in ankiety_queryset]

    def uniqify(seq):
        seen = set()
        seen_add = seen.add
        return [x for x in seq if x not in seen and not seen_add(x)]

    szablony = uniqify(szablony)

    funkcje_eksportujace = {
        "csv": eksport_do_csv,
        "xls": eksport_do_xls,
        "html": analiza_wynikow,
    }

    return funkcje_eksportujace[format](ankiety_queryset, szablony, request)


def analiza_wynikow(ankiety_queryset, szablony, request):
    dane = []
    for szablon in szablony:
        pytania = []
        logi = Log.objects.filter(ankieta__in=ankiety_queryset)

        # Przydałoby się zapisać poniższą linijkę w SQL
        zbior_prowadzacych = [
            prowadzacy
            for prowadzacy in Prowadzacy.objects.all()
            if any(
                [prowadzacy in ankieta.prowadzacy.all() for ankieta in ankiety_queryset]
            )
        ]
        for pytanie in szablon.pytanie_set.all():
            for prowadzacy in {True: zbior_prowadzacych, False: [None]}[
                pytanie.dotyczy_prowadzacych
            ]:
                nazwa_pytania = {
                    True: "%s (%s)" % (pytanie.nazwa, str(prowadzacy)),
                    False: pytanie.nazwa,
                }[pytanie.dotyczy_prowadzacych]

                def sformatuj_odpowiedzi(odpowiedzi_queryset, rodzic):
                    return [
                        {
                            "nazwa": odpowiedz.nazwa,
                            "podlicz": odpowiedz.podlicz(
                                ankiety_queryset, rodzic, prowadzacy
                            ),
                        }
                        for odpowiedz in odpowiedzi_queryset
                    ]

                def utworz_wykres(sformatowane_odpowiedzi, tytul=None):
                    if sformatowane_odpowiedzi:
                        ColumnChart = gchart.corechart.ColumnChartFactory()
                        schema = [("Odpowiedź", "string"), ("Ilość", "number")]
                        data = [
                            [odpowiedz["nazwa"], odpowiedz["podlicz"]]
                            for odpowiedz in sformatowane_odpowiedzi
                        ]
                        return ColumnChart(schema, data, title="Odpowiedzi")
                    else:
                        return ""

                slownik_pytania = {
                    "nazwa": nazwa_pytania,
                    "podpytania": [],
                    "odpowiedzi": sformatuj_odpowiedzi(
                        pytanie.wszystkie_odpowiedzi.all(), pytanie
                    ),
                    "komentarze": pytanie.komentarz_set.filter(
                        prowadzacy=prowadzacy, log__in=logi
                    ),
                }
                for podpytanie in pytanie.podpytanie_set.all():
                    slownik_podpytania = {
                        "nazwa": podpytanie.nazwa,
                        "odpowiedzi": sformatuj_odpowiedzi(
                            podpytanie.wszystkie_odpowiedzi.all(), podpytanie
                        ),
                    }
                    slownik_podpytania["wykres"] = utworz_wykres(
                        slownik_podpytania["odpowiedzi"], podpytanie.pelna_nazwa
                    )
                    slownik_pytania["podpytania"].append(slownik_podpytania)
                slownik_pytania["wykres"] = utworz_wykres(
                    slownik_pytania["odpowiedzi"], pytanie.nazwa
                )
                pytania.append(slownik_pytania)
        dane.append(
            (pytania, szablon, [a for a in ankiety_queryset if a.szablon == szablon])
        )
    return render(
        request,
        "ankiety/analiza.html",
        {"dane": dane},
    )


def eksport_do_csv(*args, **kwargs):
    return eksport_do_arkusza("csv", *args, **kwargs)


def eksport_do_xls(*args, **kwargs):
    return eksport_do_arkusza("xls", *args, **kwargs)


def eksport_do_arkusza(format, ankiety_queryset, szablony, request):
    def zapisz_do_csv(response, arkusze):
        writer = csvwriter.get_excel_csv_writer(response)
        for arkusz in arkusze.values():
            for wiersz in arkusz:
                writer.writerow(wiersz)
            writer.writerow([])

    def zapisz_do_xls(response, arkusze):
        def dlugosc(odpowiedz, nr_kolumny, liczba_kolumn):
            try:
                if nr_kolumny in (
                    0,
                    2,
                    3,
                    liczba_kolumn - 1,
                ):  # termin, czas zakończenia, prowadzący i dodatkowe uwagi
                    dlugosc = len(odpowiedz)
                else:
                    dlugosc = len(odpowiedz) / 2
            except TypeError:  # odpowiedź jest liczbą
                dlugosc = odpowiedz / 10 + 1
            return max(5, dlugosc)

        def styl(i, j):
            style = []
            if i == 0:
                style += ["font: bold on", "border: bottom medium"]
            return xlwt.easyxf(";".join(style))

        # style_compression zabezpiecza nas przed błędem, gdy pojawia się
        # więcej niż 4096 styli.
        wb = xlwt.Workbook("utf-8", style_compression=2)

        # Arkusz Excela może mieć nazwę złożoną maksymalnie z 31 znaków.
        # Poniżej usuwamy niejednoznaczności w tak otrzymanych nazwach.
        skrocone_nazwy = [my_slugify.slugify(nazwa[:31]) for nazwa in arkusze.keys()]
        powtarzajace_sie_nazwy = [
            nazwa for nazwa in skrocone_nazwy if skrocone_nazwy.count(nazwa) > 1
        ]
        for powtarzajaca_sie_nazwa in powtarzajace_sie_nazwy:
            nazwy_arkuszy = [
                nazwa
                for nazwa in arkusze.keys()
                if nazwa.startswith(powtarzajaca_sie_nazwa)
            ]
            for i, nazwa in enumerate(nazwy_arkuszy):
                przyrostek = "_" + str(i)
                nowa_nazwa = nazwa[: 31 - len(przyrostek)] + przyrostek
                arkusze[nowa_nazwa] = arkusze[nazwa]
                del arkusze[nazwa]

        for nazwa_arkusza, arkusz in arkusze.items():
            ws = wb.add_sheet(my_slugify.slugify(nazwa_arkusza[:31]))
            for i, wiersz in enumerate(arkusz):
                for j, komorka in enumerate(wiersz):
                    ws.write(i, j, komorka, styl(i, j))

            # Ustawianie szerokości kolumn
            for j in range(len(arkusz[0])):
                maksymalna_dlugosc = int(
                    max(
                        [dlugosc(wiersz[j], j, len(arkusz[0])) for wiersz in arkusz[1:]]
                        + [len(arkusz[0][j]) / 6 + 1]
                    )
                )
                ws.col(j).width = min(maksymalna_dlugosc, 50) * 256
        wb.save(response)

    formaty = {
        "csv": (
            "text/csv",
            zapisz_do_csv,
        ),
        "xls": (
            "application/vnd.ms-excel",
            zapisz_do_xls,
        ),
    }

    (mimetype, zapisz) = formaty[format]

    response = HttpResponse(content_type=mimetype)
    response["Content-Disposition"] = "attachment; filename=ankiety.%s" % (format,)
    arkusze = {}
    for szablon in szablony:
        arkusz = arkusze[szablon.nazwa] = []
        pytania = szablon.splaszcz()
        nazwy_pytan = [pytanie.nazwa for pytanie in pytania]
        arkusz.append(
            ["Termin", "Szkolenie", "Czas zakończenia", "Prowadzący"] + nazwy_pytan
        )

        for ankieta in [a for a in ankiety_queryset if a.szablon == szablon]:
            for log in ankieta.log_set.all():
                for prowadzacy in ankieta.prowadzacy.all() or [None]:
                    arkusz.append(
                        [
                            str(ankieta.termin_szkolenia.termin),
                            str(ankieta.termin_szkolenia.szkolenie),
                            str(ankieta.czas_zakonczenia),
                            str(prowadzacy),
                        ]
                        + [
                            int(odpowiedz) if odpowiedz.isdigit() else odpowiedz
                            for odpowiedz in [
                                str(log.uzyskaj_odpowiedz(pytanie, prowadzacy))
                                for pytanie in pytania
                            ]
                        ]
                    )
    zapisz(response, arkusze)
    return response


@never_cache
@user_passes_test(
    lambda u: u.has_perm("ankiety.add_ankieta")
    or u.has_perm("ankiety.ogladanie_wynikow_swoich_ankiet"),
    login_url="/admin/",
)
def eksport(request, language=settings.DEFAULT_LANGUAGE):
    if request.GET:
        formularz_filtrowania = FiltrAnkietForm(request.GET)
        formularz_formatu = FormatEksportuForm(request.GET)
        if formularz_filtrowania.is_valid() and formularz_formatu.is_valid():

            # Odrzucamy puste wartości w formularzu:
            queryset_filters = dict(
                (klucz, wartosc)
                for klucz, wartosc in list(formularz_filtrowania.cleaned_data.items())
                if wartosc
            )

            queryset = Ankieta.objects.filter(**queryset_filters)
            # Jeśli user nie może dodawać ankiet, to filtrujemy tylko po
            # ankietach, w których jest prowadzącym
            if not request.user.has_perm("ankiety.add_ankieta"):
                queryset = queryset.filter(prowadzacy__user=request.user)
            # Jeśli podano parametr "ankieta", to filtrujemy tylko po ankietach po określonych id.
            # Zastępujemy w ten sposób odpowiednią akcję w adminie dla tych, którzy korzystają
            # z interfejsu na stronie
            if "ankieta" in request.GET:
                queryset = queryset.filter(id__in=request.GET.getlist("ankieta"))
            if queryset:
                return eksport_do(
                    queryset, formularz_formatu.cleaned_data["format"], request
                )
            else:
                return HttpResponse(
                    "<h2>Nie znaleziono ankiet spełniających zadane kryteria.</h2>"
                )
    else:
        # Jeśli user nie może dodawać ankiet, to nie wyświetlamy w ogóle
        # formularza filtrowania
        if not request.user.has_perm("ankiety.add_ankieta"):
            raise PermissionDenied
        formularz_filtrowania = FiltrAnkietForm()
        formularz_formatu = FormatEksportuForm()
    return render(
        request,
        "ankiety/filtrowanie.html",
        {
            "formularz_filtrowania": formularz_filtrowania,
            "formularz_formatu": formularz_formatu,
        },
    )


@never_cache
@permission_required("ankiety.add_ankieta", login_url="/admin/")
def logi(request, ankieta_id, language=settings.DEFAULT_LANGUAGE):
    ankieta = get_object_or_404(Ankieta, pk=ankieta_id)
    return HttpResponse(
        ["%s\n" % (log,) for log in ankieta.log_set.all()], content_type="text/plain"
    )
