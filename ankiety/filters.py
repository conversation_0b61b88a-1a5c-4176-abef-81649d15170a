from django.contrib.admin import SimpleListFilter

from www.models import Prowadzacy, Szkolenie, TagTechnologia


class ZWypelnieniamiFilter(SimpleListFilter):
    title = "wypełnienia"
    parameter_name = "wypelnienia"

    def lookups(self, request, model_admin):
        return (("bez", "bez wypełnień"),)

    def queryset(self, request, queryset):
        if self.value() == "bez":
            return queryset.extra(
                where=[
                    "(select count(*) from ankiety_log where ankiety_log.ankieta_id = "
                    "ankiety_ankieta.id) = 0"
                ]
            )


class ProwadzacyFilter(SimpleListFilter):
    title = "prowadzacy"
    parameter_name = "prowadzacy"

    def lookups(self, request, model_admin):
        """
        2014-05-15 FIXME: Pobieramy prowadząych, którzy mają jakiekolwiek ankiety. Przerobić
        tak, aby pob<PERSON><PERSON> tylko takich, którzy mają ankiety z wypełnionymi
        komentarzami (i analogicznie poprawnie pokazywać liczbę przypsisanych ankiet
        przy nazwisku).
        """
        prowadzacy = Prowadzacy.objects.filter(ankiety__isnull=False).distinct()
        label = lambda p: "{} ({})".format(p, p.ankiety.count())
        return [(p.id, label(p)) for p in prowadzacy]

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(ankieta__prowadzacy__id__exact=self.value())
        else:
            return queryset


class TagTechnologiaFilter(SimpleListFilter):
    title = "techtag"
    parameter_name = "techtag"

    def lookups(self, request, model_admin):
        techtag = TagTechnologia.objects.exclude(widoczny_publicznie=False)
        label = lambda p: "{}".format(p.pretty_nazwa())
        return [(p.id, label(p)) for p in techtag]

    def queryset(self, request, queryset):
        """
        2014-05-15 FIXME: W chwili obecnej szkolenia specjalnie typu
        "Wynajem sali" lub "Konsultacje Indywidualne" oraz ich angielskie
        odpowiedniki są domyślnie przypisane do wszystkich tech tagów
        (precyzyjnie mówiąc, są przypisne do większości). Z tego względu
        na chwilę obecną kiedy wybrany jest dany techtag odfiltrowujemy
        szkolenia specjalne, aby nie zaśmiecać komentarzy.
        """
        if self.value():
            queryset = queryset.filter(
                ankieta__termin_szkolenia__szkolenie__tagi_technologia__id__exact=self.value()
            )
            queryset = queryset.exclude(ankieta__termin_szkolenia__szkolenie__kod="WS")
            queryset = queryset.exclude(ankieta__termin_szkolenia__szkolenie__kod="KI")
            queryset = queryset.exclude(ankieta__termin_szkolenia__szkolenie__kod="IC")
            queryset = queryset.exclude(ankieta__termin_szkolenia__szkolenie__kod="RH")
            return queryset
        else:
            return queryset


class SzkolenieFilter(SimpleListFilter):
    title = "szkolenie"
    parameter_name = "szkolenie"

    def lookups(self, request, model_admin):
        szkolenie = Szkolenie.objects.all()
        label = lambda p: "{}".format(p.kod)
        return [(p.id, label(p)) for p in szkolenie]

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(
                ankieta__termin_szkolenia__szkolenie__id__exact=self.value()
            )
        else:
            return queryset
