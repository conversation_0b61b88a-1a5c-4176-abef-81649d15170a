from django.urls import path, re_path

from ankiety import views as ankiety_views

urlpatterns = [
    re_path(
        r"^(?P<id>\d+)(?P<lancuch_kontrolny>[a-z]*)$",
        ankiety_views.wypelnianie,
        name="wypeln<PERSON>ie",
    ),
    re_path(
        r"^przegladanie/(?P<log_id>\d+)$",
        ankiety_views.przegladanie,
        name="przegladanie",
    ),
    path("przegladanie/", ankiety_views.przegladanie_index, name="przegladanie_index"),
    path("eksport", ankiety_views.eksport, name="eksport"),
    re_path(
        r"^csv/(?P<ankieta_id>\d+)$",
        ankiety_views.eksport_do_csv,
        name="eksport_do_csv",
    ),
    re_path(r"^log/(?P<ankieta_id>\d+)$", ankiety_views.logi, name="logi"),
    re_path(
        r"^podziekowanie/(?P<pk>\d+)/(?P<checksum>[a-z]{4})/$",
        ankiety_views.thankyou,
        name="thankyou",
    ),
    re_path(
        r"^podziekowanie/(?P<pk>\d+)/(?P<checksum>[a-z]{4})/(?P<uid>[a-zA-Z0-9]{32})/$",
        ankiety_views.thankyou_done,
        name="thankyou_done",
    ),
]
