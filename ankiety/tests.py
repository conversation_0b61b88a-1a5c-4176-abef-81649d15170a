import datetime

from django.core import mail
from django.test.utils import override_settings
from django.urls import reverse

from www.testfactories import (
    AnkietaFactory,
    GraduateFactory,
    OdbiorcaFactory,
    UczestnikFactory,
)
from www.testhelpers import ALXTestCase


class ThankYouViewTestCase(ALXTestCase):
    def _check_if_certificate_was_sent(self):
        # Powinień zostac wysłany email z certyfikatem
        self.assertEqual(len(mail.outbox), 1)
        self.assertIn("Certyfikat ukończenia szkolenia", mail.outbox[0].subject)

    def test_404(self):
        """
        Testujemy wszystkie przypadki, gdy dostęp do strony po wypełnieniu
        ankiety jest zabroniony.
        """

        e = AnkietaFactory.create()

        url = e.get_thankyou_url()

        # Brak cookies świadczącym o wypełnionej ankiecie.
        response = self.client.get(url)
        self.assertEqual(response.status_code, 404)

        # Jest cookie, ale ankieta została zakończona
        self.client.cookies[e.secret_key] = "True"

        e.czas_zakonczenia -= datetime.timedelta(days=365)
        e.save()

        response = self.client.get(url)
        self.assertEqual(response.status_code, 404)

    def _test_200(self, language):
        """
        Testujemy wszystkie przypadki, gdy dostęp do strony po wypełnieniu
        ankiety jest dozwolony.
        """

        e = AnkietaFactory.create()
        e.termin_szkolenia.szkolenie.language = language
        e.termin_szkolenia.szkolenie.save()

        url = e.get_thankyou_url()

        # Ustawiamy cookies

        self.client.cookies[e.secret_key] = "True"

        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_200_pl(self):
        self._test_200(language="pl")

    def test_200_en(self):
        self._test_200(language="en")

    def test_create_graduate_using_email(self):
        """
        Tworzymy obiekt Absolwenta na podstawie dopasowania email.
        """

        e = AnkietaFactory.create()
        self.client.cookies[e.secret_key] = "True"

        # Tworzymy obiekt Uczestnika
        participant = UczestnikFactory.create(
            termin=e.termin_szkolenia,
        )

        url = e.get_thankyou_url()

        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

        # Zaczynamy od stanu 1 formularza

        # Wysyłamy pusty formularz - błąd walidacji - wymagamy wszystkich
        # danych.
        response = self.client.post(url, {}, follow=True)

        self.assertEqual(
            ["To pole jest wymagane."],
            response.context["form"].errors["email"],
        )
        self.assertEqual(
            ["To pole jest wymagane."],
            response.context["form"].errors["full_name"],
        )

        # Wysyłamy poprawny email, zgodny z emailem uczestnika
        response = self.client.post(
            url,
            {
                "email": participant.email,
                "full_name": "Jacek",
            },
            follow=True,
        )

        # Powinien zostać utworzony obiekt Absolwenta
        self.assertEqual(GraduateFactory._meta.model.objects.all().count(), 1)

        graduate = GraduateFactory._meta.model.objects.all()[0]

        # Sprawdzamy poszczególne pola Absolwenta (czy zostały poprawnie
        # ustawione)
        self.assertEqual(graduate.participant, participant)
        self.assertEqual(graduate.term, e.termin_szkolenia)
        self.assertEqual(graduate.email, participant.email)
        self.assertEqual(graduate.name, participant.imie_nazwisko)
        self.assertTrue(graduate.remote_addr)
        self.assertTrue(graduate.number)
        self.assertTrue(graduate.pdf)
        self.assertTrue(graduate.accepted)

        # Sprawdzamy czy nastąpiło przekierowanie do strony podziękowań
        expected_url = reverse(
            "thankyou_done",
            kwargs={
                "pk": e.pk,
                "checksum": e.lancuch_kontrolny,
                "uid": graduate.key.hex,
                "language": "pl",
            },
        )

        self.assertRedirects(response, expected_url)

        # Powinień zostać wysłany email z certyfikatem
        self._check_if_certificate_was_sent()

    def test_create_graduate_using_full_name(self):
        """
        Tworzymy obiekt Absolwenta na podstawie dopasowania imię i nazwisko.
        """

        e = AnkietaFactory.create()
        self.client.cookies[e.secret_key] = "True"

        # Tworzymy obiekt Uczestnika
        participant = UczestnikFactory.create(
            termin=e.termin_szkolenia,
            uczestnik_wieloosobowy_ilosc_osob=2,
            imie_nazwisko="Imię nazwisko1\nImię nazwisko2",
        )

        url = e.get_thankyou_url()

        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

        # Wysyłamy poprawny email, zgodny z emailem uczestnika, ale Uczestnik
        # jest wieloosobowy, więc dopasownie, będzie musiało nastapić po
        # imieniu i nazwisku.
        response = self.client.post(
            url,
            {"email": participant.email, "full_name": "imię nazwisko2"},
            follow=True,
        )

        # Powinien zostać utworzony obiekt Absolwenta
        self.assertEqual(GraduateFactory._meta.model.objects.all().count(), 1)

        graduate = GraduateFactory._meta.model.objects.all()[0]

        # Sprawdzamy poszczególne pola Absolwenta (czy zostały poprawnie
        # ustawione)
        self.assertEqual(graduate.participant, participant)
        self.assertEqual(graduate.term, e.termin_szkolenia)
        self.assertEqual(graduate.email, participant.email)
        self.assertEqual(graduate.name, "imię nazwisko2")
        self.assertTrue(graduate.remote_addr)
        self.assertTrue(graduate.number)
        self.assertTrue(graduate.pdf)
        self.assertTrue(graduate.accepted)

        # Sprawdzamy czy nastąpiło przekierowanie do strony podziękowań
        expected_url = reverse(
            "thankyou_done",
            kwargs={
                "pk": e.pk,
                "checksum": e.lancuch_kontrolny,
                "uid": graduate.key.hex,
                "language": "pl",
            },
        )

        self.assertRedirects(response, expected_url)

        # Powinień zostać wysłany email z certyfikatem
        self._check_if_certificate_was_sent()

    def test_create_inactive_graduate_object(self):
        """
        Tworzymy obiekt Absolwenta bez dopasowania - o dalszym losie decyduje
        Biuro.
        """

        e = AnkietaFactory.create()
        self.client.cookies[e.secret_key] = "True"

        url = e.get_thankyou_url()

        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

        # Wysyłamy formularz z email i nazwiskiem
        response = self.client.post(
            url,
            {
                "email": "<EMAIL>",
                "full_name": "jakieś imię i nazwisko",
            },
            follow=True,
        )

        # Powinien zostać utworzony obiekt Absolwenta
        self.assertEqual(GraduateFactory._meta.model.objects.all().count(), 1)

        graduate = GraduateFactory._meta.model.objects.all()[0]

        # Sprawdzamy poszczególne pola Absolwenta (czy zostały poprawnie
        # ustawione)
        self.assertEqual(graduate.participant, None)
        self.assertEqual(graduate.term, e.termin_szkolenia)
        self.assertEqual(graduate.email, "<EMAIL>")
        self.assertEqual(graduate.name, "jakieś imię i nazwisko")
        self.assertTrue(graduate.remote_addr)
        self.assertTrue(graduate.number)
        self.assertTrue(graduate.pdf)
        self.assertFalse(graduate.accepted)

        # Sprawdzamy czy nastąpiło przekierowanie do strony podziękowań
        expected_url = reverse(
            "thankyou_done",
            kwargs={
                "pk": e.pk,
                "checksum": e.lancuch_kontrolny,
                "uid": graduate.key.hex,
                "language": "pl",
            },
        )

        self.assertRedirects(response, expected_url)

        # Powinień zostać wysłany email do Biura o braku dopasowania Absolwenta
        self.assertEqual(len(mail.outbox), 1)

        subject = mail.outbox[0].subject
        body = mail.outbox[0].body

        self.assertEqual(
            "[Brak dopasowania Absolwenta] - <EMAIL> "
            "({0} - {1})".format(
                graduate.term.szkolenie.nazwa,
                graduate.term.lokalizacja.shortname,
            ),
            subject,
        )

        self.assertIn("<EMAIL>", body)
        self.assertIn("jakieś imię i nazwisko", body)

    def test_email_already_exists(self):
        """
        Walidacja adresu email, podczas tworzenia Absolwenta, gdy taki email
        dla takiego terminu już istnieje.
        """

        e = AnkietaFactory.create()
        self.client.cookies[e.secret_key] = "True"

        # Tworzymy obiekt Uczestnika i Absolwenta
        participant = UczestnikFactory.create(
            termin=e.termin_szkolenia,
        )

        graduate = GraduateFactory.create(
            term=e.termin_szkolenia, participant=participant, email=participant.email
        )

        url = e.get_thankyou_url()

        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

        # łąd walidacji - email juz istnieje.
        response = self.client.post(url, {"email": graduate.email}, follow=True)

        self.assertEqual(
            ["Taki adres email już istnieje."],
            response.context["form"].errors["email"],
        )

    def test_full_name_already_exists(self):
        """
        Walidacja imienia i nazwiska, podczas tworzenia Absolwenta.
        """

        e = AnkietaFactory.create()
        self.client.cookies[e.secret_key] = "True"

        # Tworzymy obiekt Uczestnika i Absolwenta
        participant = UczestnikFactory.create(
            termin=e.termin_szkolenia,
        )

        GraduateFactory.create(
            term=e.termin_szkolenia,
            participant=participant,
            email=participant.email,
            name="Józek",
        )

        url = e.get_thankyou_url()

        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

        # Błąd walidacji - takie dane już istnieją.
        response = self.client.post(url, {"full_name": "józek"}, follow=True)

        self.assertEqual(
            ["Taki Uczestnik już istnieje."],
            response.context["form"].errors["full_name"],
        )


class ThankYouDoneViewTestCase(ALXTestCase):
    def test_404(self):
        """
        Testujemy wszystkie przypadki, gdy dostęp do strony po utworzeniu
        obiektu Absolwenta będzie zabroniony.
        """

        e = AnkietaFactory.create()
        graduate = GraduateFactory.create(term=e.termin_szkolenia)

        url = reverse(
            "thankyou_done",
            kwargs={
                "pk": e.pk,
                "checksum": e.lancuch_kontrolny,
                "uid": graduate.key.hex,
                "language": "pl",
            },
        )

        # Brak cookies świadczącym o wypełnionej ankiecie.
        response = self.client.get(url)
        self.assertEqual(response.status_code, 404)

        # Jest cookie, ale ankieta została zakończona
        self.client.cookies[e.secret_key] = "True"

        e.czas_zakonczenia -= datetime.timedelta(days=365)
        e.save()

        response = self.client.get(url)
        self.assertEqual(response.status_code, 404)

        # Brak obiektu Absolwenta
        graduate.delete()

        e.czas_zakonczenia += datetime.timedelta(days=365)
        e.save()

        response = self.client.get(url)
        self.assertEqual(response.status_code, 404)

    def _test_200(self, language):
        """
        Testujemy wszystkie przypadki, gdy dostęp do strony po utworzeniu
        obiektu Absolwenta jest dozwolony.
        """

        e = AnkietaFactory.create()
        e.termin_szkolenia.szkolenie.language = language
        e.termin_szkolenia.szkolenie.save()

        graduate = GraduateFactory.create(term=e.termin_szkolenia)

        url = reverse(
            "thankyou_done",
            kwargs={
                "pk": e.pk,
                "checksum": e.lancuch_kontrolny,
                "uid": graduate.key.hex,
                "language": language,
            },
        )

        # Ustawiamy cookies

        self.client.cookies[e.secret_key] = "True"

        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_200_pl(self):
        self._test_200(language="pl")

    def test_200_en(self):
        self._test_200(language="en")

    def test_add_me_to_newsletter(self):
        """
        Opcja dodania do Newslettera
        """

        # Opcja dodania do newslettera powinna być widoczna
        e = AnkietaFactory.create()
        graduate = GraduateFactory.create(term=e.termin_szkolenia)

        url = reverse(
            "thankyou_done",
            kwargs={
                "pk": e.pk,
                "checksum": e.lancuch_kontrolny,
                "uid": graduate.key.hex,
                "language": "pl",
            },
        )

        # Usuń odbiorcę jeśli istnieje
        OdbiorcaFactory._meta.model.objects.filter(email=graduate.email).delete()

        self.client.cookies[e.secret_key] = "True"

        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

        # Zmianna mówiąca o pojawieniu się opcji zapisu powinna mieć wartość
        # `True`
        self.assertTrue(response.context["newsletter_action"])

        # Robimy zatem zapis - zwykły pusty POST.
        response = self.client.post(url, {"tos": True}, follow=True)

        self.assertEqual(response.status_code, 200)

        self.assertIn(
            "Zostałeś poprawnie dodany do Newslettera.".encode("utf-8"),
            response.content,
        )

        self.assertTrue(
            OdbiorcaFactory._meta.model.objects.filter(
                email=graduate.email, status="potwierdzony", zrodlo_kontaktu="ankieta"
            ).exists()
        )

        # Zmianna mówiąca o pojawieniu się opcji zapisu powinna mieć teraz
        # wartość `False`
        self.assertFalse(response.context["newsletter_action"])


@override_settings(
    DOMENY_DLA_JEZYKOW={
        "pl": "pl-domena",
        "en": "en-domena",
    }
)
class SurveyTestCase(ALXTestCase):
    def test_urls(self):
        """
        Testujemy URLe w zależności od języka.
        """

        # PL

        e = AnkietaFactory.create()
        e.termin_szkolenia.szkolenie.language = "pl"
        e.termin_szkolenia.szkolenie.save()

        url = e.get_absolute_url()

        self.assertEqual(
            url, "http://pl-domena/ankiety/{0}{1}".format(e.pk, e.lancuch_kontrolny)
        )
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

        thankyou_url = e.get_thankyou_url()

        self.assertEqual(
            thankyou_url,
            "/ankiety/podziekowanie/{0}/{1}/".format(e.pk, e.lancuch_kontrolny),
        )

        # EN

        e.termin_szkolenia.szkolenie.language = "en"
        e.termin_szkolenia.szkolenie.save()

        url = e.get_absolute_url()

        self.assertEqual(
            url, "http://en-domena/en/surveys/{0}{1}".format(e.pk, e.lancuch_kontrolny)
        )
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

        thankyou_url = e.get_thankyou_url()

        self.assertEqual(
            thankyou_url,
            "/en/surveys/thankyou/{0}/{1}/".format(e.pk, e.lancuch_kontrolny),
        )
