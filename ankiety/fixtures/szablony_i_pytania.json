[{"pk": 1, "model": "ankiety.szablon", "fields": {"aktywny": true, "opis": "Przykładowy opis.", "nazwa": "Domyślny szablon"}}, {"pk": 1, "model": "ankiety.odpowiedz", "fields": {"kolejnosc": 0, "pytanie_id": 2, "rodzaj_pytania": 35, "nazwa": "<PERSON><PERSON>"}}, {"pk": 17, "model": "ankiety.odpowiedz", "fields": {"kolejnosc": 1, "pytanie_id": 7, "rodzaj_pytania": 36, "nazwa": "zrozumiały"}}, {"pk": 2, "model": "ankiety.odpowiedz", "fields": {"kolejnosc": 1, "pytanie_id": 2, "rodzaj_pytania": 35, "nazwa": "<PERSON>e mam"}}, {"pk": 20, "model": "ankiety.odpowiedz", "fields": {"kolejnosc": 1, "pytanie_id": 8, "rodzaj_pytania": 36, "nazwa": "za szy<PERSON>ko"}}, {"pk": 23, "model": "ankiety.odpowiedz", "fields": {"kolejnosc": 1, "pytanie_id": 9, "rodzaj_pytania": 36, "nazwa": "za du<PERSON>a"}}, {"pk": 24, "model": "ankiety.odpowiedz", "fields": {"kolejnosc": 2, "pytanie_id": 9, "rodzaj_pytania": 36, "nazwa": "w sam raz"}}, {"pk": 3, "model": "ankiety.odpowiedz", "fields": {"kolejnosc": 2, "pytanie_id": 2, "rodzaj_pytania": 35, "nazwa": "<PERSON><PERSON><PERSON>, ale pies go zjadł"}}, {"pk": 21, "model": "ankiety.odpowiedz", "fields": {"kolejnosc": 2, "pytanie_id": 8, "rodzaj_pytania": 36, "nazwa": "w sam raz"}}, {"pk": 18, "model": "ankiety.odpowiedz", "fields": {"kolejnosc": 2, "pytanie_id": 7, "rodzaj_pytania": 36, "nazwa": "przeciętny"}}, {"pk": 25, "model": "ankiety.odpowiedz", "fields": {"kolejnosc": 3, "pytanie_id": 9, "rodzaj_pytania": 36, "nazwa": "za mała"}}, {"pk": 19, "model": "ankiety.odpowiedz", "fields": {"kolejnosc": 3, "pytanie_id": 7, "rodzaj_pytania": 36, "nazwa": "mało zrozumiały"}}, {"pk": 22, "model": "ankiety.odpowiedz", "fields": {"kolejnosc": 3, "pytanie_id": 8, "rodzaj_pytania": 36, "nazwa": "za wolno"}}, {"pk": 15, "model": "ankiety.odpowiedz", "fields": {"kolejnosc": 5, "pytanie_id": 13, "rodzaj_pytania": 35, "nazwa": "Tak"}}, {"pk": 16, "model": "ankiety.odpowiedz", "fields": {"kolejnosc": 6, "pytanie_id": 13, "rodzaj_pytania": 35, "nazwa": "<PERSON><PERSON>"}}, {"pk": 2, "model": "ankiety.pytanie", "fields": {"kolejnosc": 1, "szablon": 1, "dotyczy_prowadzacych": false, "nazwa": "<PERSON><PERSON> masz kota?"}}, {"pk": 3, "model": "ankiety.pytanie", "fields": {"kolejnosc": 2, "szablon": 1, "dotyczy_prowadzacych": false, "nazwa": "Co sądzisz o Django?"}}, {"pk": 13, "model": "ankiety.pytanie", "fields": {"kolejnosc": 3, "szablon": 1, "dotyczy_prowadzacych": false, "nazwa": "Czy na kursie nauczyłeś się..."}}, {"pk": 14, "model": "ankiety.pytanie", "fields": {"kolejnosc": 4, "szablon": 1, "dotyczy_prowadzacych": false, "nazwa": "Oceń następujące aspekty kursu:"}}, {"pk": 16, "model": "ankiety.pytanie", "fields": {"kolejnosc": 5, "szablon": 1, "dotyczy_prowadzacych": true, "nazwa": "Co sądzisz o prowadzącym?"}}, {"pk": 15, "model": "ankiety.pytanie", "fields": {"kolejnosc": 6, "szablon": 1, "dotyczy_prowadzacych": false, "nazwa": "ccc"}}, {"pk": 4, "model": "ankiety.podpytanie", "fields": {"kolejnosc": 1, "nazwa": "SQL?", "pytanie": 13}}, {"pk": 7, "model": "ankiety.podpytanie", "fields": {"kolejnosc": 1, "nazwa": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wykładu", "pytanie": 14}}, {"pk": 5, "model": "ankiety.podpytanie", "fields": {"kolejnosc": 2, "nazwa": "Django?", "pytanie": 13}}, {"pk": 8, "model": "ankiety.podpytanie", "fields": {"kolejnosc": 2, "nazwa": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pracy", "pytanie": 14}}, {"pk": 6, "model": "ankiety.podpytanie", "fields": {"kolejnosc": 3, "nazwa": "PHP?", "pytanie": 13}}, {"pk": 9, "model": "ankiety.podpytanie", "fields": {"kolejnosc": 3, "nazwa": "wielkość czcionki na slajdach", "pytanie": 14}}]