from django.urls import re_path

from ankiety import views as ankiety_views

urlpatterns = [
    re_path(
        r"^(?P<id>\d+)(?P<lancuch_kontrolny>[a-z]*)$",
        ankiety_views.wypelnianie,
        name="wypeln<PERSON>ie",
    ),
    re_path(
        r"^thankyou/(?P<pk>\d+)/(?P<checksum>[a-z]{4})/$",
        ankiety_views.thankyou,
        name="thankyou",
    ),
    re_path(
        r"^thankyou/(?P<pk>\d+)/(?P<checksum>[a-z]{4})/(?P<uid>[a-zA-Z0-9]{32})/$",
        ankiety_views.thankyou_done,
        name="thankyou_done",
    ),
]
