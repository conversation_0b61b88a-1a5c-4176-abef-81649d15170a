import datetime
from collections import OrderedDict

from django import forms
from django.contrib import admin
from django.contrib.contenttypes.admin import GenericStackedInline
from django.core.paginator import EmptyPage, PageNotAnInteger, Paginator
from django.shortcuts import render
from django.utils.safestring import SafeText

from . import views
from .filters import (
    ProwadzacyFilter,
    SzkolenieFilter,
    TagTechnologiaFilter,
    ZWypelnieniamiFilter,
)
from .models import Ankieta, Komentarz, Odpowiedz, Podpytanie, Pytanie, Szablon


class HiddenModelAdmin(admin.ModelAdmin):
    def get_model_perms(self, *args, **kwargs):
        perms = admin.ModelAdmin.get_model_perms(self, *args, **kwargs)
        perms["list_hide"] = True
        return perms


class PytanieInline(admin.StackedInline):
    model = Pytanie
    template = "admin/edit_inline/stacked_with_links.html"


def sklonuj_szablony(modeladmin, request, queryset):
    for szablon in queryset:
        szablon.sklonuj()


sklonuj_szablony.short_description = "Sklonuj zaznaczone szablony"


class SzablonAdmin(admin.ModelAdmin):
    class Media:
        js = (
            "admin/js/jquery.init.js",
            "custom_admin/js/jquery-ui.1.12.1.min.js",
            "custom_admin/js/sort.js",
        )

    inlines = [
        PytanieInline,
    ]
    list_display = ("__str__", "aktywny")
    actions = [sklonuj_szablony]


admin.site.register(Szablon, SzablonAdmin)


class MyAnkietaAdminForm(forms.ModelForm):
    class Meta:
        model = Ankieta
        fields = "__all__"
        exclude = ()

    szablon = forms.ModelChoiceField(Szablon.objects.filter(aktywny=True))


def eksportuj_ankiety_do_csv(modeladmin, request, queryset):
    return views.eksport_do(queryset, "csv", request)


eksportuj_ankiety_do_csv.short_description = "Eksportuj zaznaczone do CSV"


def eksportuj_ankiety_do_xls(modeladmin, request, queryset):
    return views.eksport_do(queryset, "xls", request)


eksportuj_ankiety_do_xls.short_description = "Eksportuj zaznaczone do Excela"


def analizuj_wyniki_ankiet(modeladmin, request, queryset):
    return views.eksport_do(queryset, "html", request)


analizuj_wyniki_ankiet.short_description = "Analizuj wyniki online"


def zamknij_ankiety(modeladmin, request, queryset):
    queryset.filter(czas_zakonczenia__gt=datetime.datetime.now()).update(
        czas_zakonczenia=datetime.datetime.now()
    )


zamknij_ankiety.short_description = "Zamknij zaznaczone ankiety"


class KomentarzAdmin(admin.ModelAdmin):
    list_filter = (
        ProwadzacyFilter,
        TagTechnologiaFilter,
        SzkolenieFilter,
    )
    list_display = (
        "tresc",
        "termin_szkolenia",
        "szkolenie_kod",
        "prowadzacy_nazwa",
    )
    search_fields = ("tresc",)

    def get_readonly_fields(self, request, obj=None):
        readonly_fields = []
        for field in self.model._meta.fields:
            readonly_fields.append(field.name)
        return readonly_fields

    def has_add_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

    def termin_szkolenia(self, obj):
        return obj.ankieta.termin_szkolenia.termin

    def szkolenie_kod(self, obj):
        return obj.ankieta.termin_szkolenia.szkolenie.nazwa

    def prowadzacy_nazwa(self, obj):
        return SafeText(
            "<br/>".join([p.imie_naziwsko() for p in obj.ankieta.prowadzacy.all()])
        )

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return qs.exclude(tresc__isnull=True).exclude(tresc__exact="")

    termin_szkolenia.admin_order_field = "ankieta__termin_szkolenia__termin"


admin.site.register(Komentarz, KomentarzAdmin)


class AnkietaAdmin(admin.ModelAdmin):
    form = MyAnkietaAdminForm
    edit_readonly_fields = ()
    list_display = (
        "szkolenie",
        "termin_url",
        "czas_zakonczenia",
        "lancuch_kontrolny",
        "html_url",
        "przegladanie",
        "link_do_logow",
    )
    list_filter = ("prowadzacy", "szablon", ZWypelnieniamiFilter)
    filter_vertical = ("prowadzacy",)
    raw_id_fields = ("termin_szkolenia",)
    search_fields = (
        "prowadzacy__imie",
        "prowadzacy__nazwisko",
        "termin_szkolenia__szkolenie__nazwa",
        "termin_szkolenia__szkolenie__kod",
        "termin_szkolenia__szkolenie__tagi_technologia__nazwa",
        "lancuch_kontrolny"
    )
    list_select_related = True

    def get_actions(self, request):
        actions = OrderedDict()

        for action in [
            analizuj_wyniki_ankiet,
            eksportuj_ankiety_do_xls,
            eksportuj_ankiety_do_csv,
            zamknij_ankiety,
        ]:
            actions[action.__name__] = (
                action,
                action.__name__,
                action.short_description,
            )

        actions.update(super().get_actions(request))
        return actions

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return qs.prefetch_related(
            "log_set",
        ).select_related()

    def get_readonly_fields(self, request, obj=None):
        if obj:  # editing an existing object
            return self.readonly_fields + self.edit_readonly_fields
        return self.readonly_fields

    def zestawienie_view(self, request):
        lista_ankiet = Komentarz.objects.exclude(tresc__isnull=True).exclude(
            tresc__exact=""
        )
        paginator = Paginator(lista_ankiet, 25)
        page = request.GET.get("page")
        try:
            ankiety = paginator.page(page)
        except PageNotAnInteger:
            ankiety = paginator.page(1)
        except EmptyPage:
            ankiety = paginator.page(paginator.num_pages)
        context = {
            "ankiety": ankiety,
        }
        return render(request, "admin/ankiety/zestawienie.html", context)


admin.site.register(Ankieta, AnkietaAdmin)


class PodpytanieInline(admin.StackedInline):
    model = Podpytanie
    template = "admin/edit_inline/stacked_with_links.html"


class OdpowiedzInline(GenericStackedInline):
    model = Odpowiedz
    ct_field = "rodzaj_pytania"
    ct_fk_field = "pytanie_id"


class PytanieAdmin(HiddenModelAdmin):
    class Media:
        js = (
            "admin/js/jquery.init.js",
            "custom_admin/js/jquery-ui.1.12.1.min.js",
            "custom_admin/js/sort.js",
        )

    exclude = ("szablon", "kolejnosc")
    inlines = [
        PodpytanieInline,
        OdpowiedzInline,
    ]
    hidden = True


admin.site.register(Pytanie, PytanieAdmin)


class PodpytanieAdmin(HiddenModelAdmin):
    class Media:
        js = (
            "admin/js/jquery.init.js",
            "custom_admin/js/jquery-ui.1.12.1.min.js",
            "custom_admin/js/sort.js",
        )

    exclude = ("pytanie", "kolejnosc")
    inlines = [
        OdpowiedzInline,
    ]


admin.site.register(Podpytanie, PodpytanieAdmin)
