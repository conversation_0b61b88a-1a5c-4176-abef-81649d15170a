from django import forms
from django.contrib.contenttypes.models import ContentType
from django.utils.translation import ugettext_lazy as _

from www.models import Graduate, Prowadzacy, TagTechnologia, TagZawod

from .models import Komentarz, Odpowiedz, Pytanie, Szablon, Wybor


class TableRadioSelect(forms.RadioSelect):
    template_name = "widgets/table_select.html"

    def get_context(self, name, value, attrs):
        context = super().get_context(name, value, attrs)
        context["wrap_label"] = False
        return context


class WyborForm(forms.ModelForm):
    class Meta:
        model = Wybor
        fields = "__all__"
        exclude = ()

    odpowiedz = forms.ModelChoiceField(
        required=False,
        queryset=Odpowiedz.objects.all(),
        empty_label=None,
        label="",
        widget=forms.RadioSelect(),
    )
    prowadzacy = forms.ModelChoiceField(
        required=False, queryset=Prowadzacy.objects.all(), widget=forms.HiddenInput()
    )
    pytanie_id = forms.IntegerField(widget=forms.HiddenInput())
    rodzaj_pytania = forms.ModelChoiceField(
        widget=forms.HiddenInput(), queryset=ContentType.objects.all()
    )

    def __init__(self, *args, **kwargs):
        pytanie = kwargs.pop("pytanie", None)
        prowadzacy = kwargs.pop("prowadzacy", None)

        forms.ModelForm.__init__(self, *args, **kwargs)
        if self.instance.id:
            for field in self.fields:
                self.fields[field].widget.attrs["disabled"] = "disabled"

        if pytanie:
            self.fields["odpowiedz"].queryset = (
                pytanie.odpowiedzi.all() or pytanie.pytanie.odpowiedzi.all()
            )
            self.fields["pytanie_id"].initial = pytanie.id
            # self.fields['rodzaj_pytania'].initial = ContentType.objects.get(
            #     app_label="ankiety",
            #     model={
            #         1: "pytanie",
            #         2: "podpytanie",
            #         3: "podpytanie"
            #     }[pytanie.typ])

            # Brzydkie rozwiązanie, ale unikamy 40 niepotrzebnych zapytań do
            # bazy!

            rodzaj_pytania_initial = None

            model = {1: "pytanie", 2: "podpytanie", 3: "podpytanie"}[pytanie.typ]

            for ct in self.fields["rodzaj_pytania"].queryset:
                if ct.app_label == "ankiety" and ct.model == model:
                    rodzaj_pytania_initial = ct
                    break

            self.fields["rodzaj_pytania"].initial = rodzaj_pytania_initial

        if prowadzacy:
            self.fields["prowadzacy"].initial = prowadzacy


class WyborForm2(WyborForm):

    "Klasa dla pytań typu 2."

    class Meta:
        model = Wybor
        fields = "__all__"
        exclude = ()

    odpowiedz = forms.ModelChoiceField(
        required=False,
        queryset=Odpowiedz.objects.all(),
        empty_label=None,
        label="",
        widget=TableRadioSelect(),
    )


class KomentarzForm(forms.ModelForm):
    class Meta:
        model = Komentarz
        fields = "__all__"
        exclude = ()

    def __init__(self, *args, **kwargs):
        pytanie = kwargs.pop("pytanie", None)
        prowadzacy = kwargs.pop("prowadzacy", None)
        forms.ModelForm.__init__(self, *args, **kwargs)

        if self.instance.id:
            for field in self.fields:
                self.fields[field].widget.attrs["disabled"] = "disabled"

        if pytanie:
            self.fields["pytanie"].initial = pytanie.id
        if prowadzacy:
            self.fields["prowadzacy"].initial = prowadzacy.id

    pytanie = forms.ModelChoiceField(
        widget=forms.HiddenInput(), queryset=Pytanie.objects.all()
    )

    prowadzacy = forms.ModelChoiceField(
        required=False, widget=forms.HiddenInput(), queryset=Prowadzacy.objects.all()
    )

    tresc = forms.CharField(required=False, widget=forms.Textarea(), label="")


# Koniec formularzy do wypełniania ankiety


class FiltrAnkietForm(forms.Form):
    termin_szkolenia__termin__gte = forms.DateField(
        required=False, label="Termin szkolenia najwcześniej:"
    )
    termin_szkolenia__termin__lte = forms.DateField(
        required=False, label="Termin szkolenia najpóźniej:"
    )
    wybor_tekst_pomocniczy = (
        "Zostaną znalezione ankiety odpowiadające dowolnej pozycji z listy."
    )
    termin_szkolenia__szkolenie__tag_zawod__in = forms.ModelMultipleChoiceField(
        queryset=TagZawod.objects.all(),
        required=False,
        label="Zawody",
        help_text=wybor_tekst_pomocniczy,
    )
    termin_szkolenia__szkolenie__tagi_technologia__in = forms.ModelMultipleChoiceField(
        queryset=TagTechnologia.objects.all(),
        required=False,
        label="Technologie",
        help_text=wybor_tekst_pomocniczy,
    )
    prowadzacy__in = forms.ModelMultipleChoiceField(
        queryset=Prowadzacy.objects.all(),
        required=False,
        label="Prowadzący",
        help_text=wybor_tekst_pomocniczy,
    )
    szablon__in = forms.ModelMultipleChoiceField(
        queryset=Szablon.objects.all(), required=False
    )


class FormatEksportuForm(forms.Form):
    FORMAT_CHOICES = (
        ("xls", "XLS"),
        ("csv", "CSV"),
        ("html", "HTML"),
    )
    format = forms.ChoiceField(choices=FORMAT_CHOICES)


class ThankYouForm(forms.Form):
    email = forms.EmailField(max_length=70)
    full_name = forms.CharField(min_length=2, max_length=100)

    def __init__(self, *args, **kwargs):
        self.term = kwargs.pop("term")
        super().__init__(*args, **kwargs)

        self.fields["email"].widget.attrs["placeholder"] = _("Wpisz adres e-mail")
        self.fields["full_name"].widget.attrs["placeholder"] = _(
            "Wpisz imię i nazwisko"
        )

    def clean_email(self):
        email = self.cleaned_data.get("email")

        if email:
            if Graduate.objects.filter(term=self.term, email__iexact=email).exists():
                raise forms.ValidationError(_("Taki adres email już istnieje."))
        return email

    def clean_full_name(self):
        full_name = self.cleaned_data.get("full_name")

        if full_name:
            if Graduate.objects.filter(
                term=self.term, slug__iexact=Graduate.generate_slug(full_name)
            ).exists():
                raise forms.ValidationError(_("Taki Uczestnik już istnieje."))
        return full_name
