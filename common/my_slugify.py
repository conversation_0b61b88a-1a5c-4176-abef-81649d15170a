import re
import unicodedata

POLSKIE_ZNAKI = {
    "ł": "l",
}


def deogonkify(c):
    if c in POLSKIE_ZNAKI:
        return POLSKIE_ZNAKI[c]
    else:
        return unicodedata.normalize("NFKD", c)[0]


def slugify(s):
    s = unicodedata.normalize("NFKC", s)
    s = s.lower()
    s = "".join([deogonkify(c) for c in s])
    s = re.sub(r"[^a-z]+", "-", s)
    s = re.sub(r"^-*", "", s)
    s = re.sub(r"-*$", "", s)
    return s


def slugify_file_name(s):
    s = unicodedata.normalize("NFKC", s)
    s = "".join([deogonkify(c) for c in s])
    s = re.sub(r"^\.|[^\.a-zA-Z0-9_-]+", "-", s)
    return s
