import base64
import logging

from django.conf import settings
from zeep import Client

logger = logging.getLogger(__name__)


def _make_dispatch(document_files, recipients, options):
    client = Client(settings.POSTIVO_SOAP_WSDL)
    return client.service.dispatch(
        login=settings.POSTIVO_LOGIN,
        api_pass=settings.POSTIVO_PASSWORD,
        msg_type=1,
        document_files=document_files,
        recipients=recipients,
        options=options,
    )


def dispatch(recipient_data, file_buffer, callback_url=None):
    fcontent = base64.b64encode(file_buffer)

    recipient = {
        "id": 0,
        "source": "inline",
    }
    recipient.update(recipient_data)

    options = [
        {
            "name": "config_id",
            "value": settings.POSTIVO_CONFIG_ID,
        }
    ]

    if settings.POSTIVO_SENDER_ID:
        options.append({"name": "sender_id", "value": settings.POSTIVO_SENDER_ID})

    if callback_url:
        options.append({"name": "callback_url", "value": callback_url})

    resp = _make_dispatch(
        document_files=[{"file_stream": fcontent, "file_name": "faktura.pdf"}],
        recipients=[recipient],
        options=options,
    )

    if resp.result == "OK":
        shipment = resp.shipments[0]
        return {"id": str(shipment.id), "status": str(shipment.status_code)}, None
    return None, resp.result_description
