from django.contrib.sitemaps import GenericSitemap, Sitemap

from www.models import GraduateStory, MyFlatPage, Szkolenie, TagTechnologia


class MainPageSitemap(Sitemap):
    priority = 1
    changefreq = "daily"

    def items(self):
        return ["main"]

    def location(self, item):
        return "/"


sitemaps = {
    "frontpage": MainPageSitemap,
    "trainings": GenericSitemap(
        {
            "queryset": Szkolenie.objects.filter(
                language="pl", aktywne=True
            ).select_related(),
            "date_field": "updated_at",
        },
        priority=0.9,
        changefreq="daily",
    ),
    "techtags": GenericSitemap(
        {
            "queryset": TagTechnologia.objects.filter(
                language="pl", widoczny_publicznie=True
            ).select_related(),
            "date_field": "updated_at",
        },
        priority=0.8,
        changefreq="daily",
    ),
    "staticpages": GenericSitemap(
        {
            "queryset": MyFlatPage.objects.filter(
                language="pl",
                enabled=True,
                is_searchable=True,
                szkolenie__isnull=True,
            ).select_related(),
            "date_field": "updated_at",
        },
        priority=0.7,
        changefreq="daily",
    ),
    "graduatestories": GenericSitemap(
        {
            "queryset": GraduateStory.objects.filter(
                is_active=True,
            ),
            "date_field": "updated_at",
        },
        priority=0.6,
        changefreq="daily",
    ),
}
