from django.conf import settings
from django.db import transaction
from django.shortcuts import render
from django.utils.decorators import method_decorator
from django.views.decorators.cache import never_cache
from django.views.generic.base import View

from www.views import activate_translation

from . import forms
from .models import Odbiorca


# Zapisywanie się na newsletter przez wydzielony formularz.
# Do zapisywania się przez ankietę jest napisany niżej widok klasowy.
# TODO: Przydałoby się pozbyć tego widoku funkcyjnego.
@never_cache
@activate_translation
@transaction.atomic
def signup(request, language=settings.DEFAULT_LANGUAGE):
    if request.method == "POST":
        form = forms.EmailForm(request.POST)
        if form.is_valid():
            istniejacy = Odbiorca.objects.filter(
                email__iexact=form.cleaned_data["email"]
            )
            if istniejacy:
                # tu trochę ściemniamy wrzucając do jednego worka tych,
                # co czekają na potwierdzenie, tych co już potwierdzili
                # i tych, co są na liście opt-out, ale robimy to celowo,
                # ż<PERSON><PERSON> chronić prywatność naszych abonamentów
                return render(
                    request,
                    "newsletter/juz_jest_na_liscie.html",
                    {},
                )
            odbiorca = Odbiorca()
            odbiorca.email = form.cleaned_data["email"]
            odbiorca_fields = Odbiorca._meta.fields
            for field in odbiorca_fields:
                if field.name in request.POST and field.name not in ("id",):
                    setattr(odbiorca, field.name, request.POST[field.name])
            referer0 = (
                request.POST["referer"]
                if "referer" in request.POST
                else request.META.get("HTTP_REFERER")
            )
            lang0 = request.POST["lang"] if "lang" in request.POST else ""
            odbiorca.referer, odbiorca.lang = Odbiorca.podaj_refererlang(
                referer0, lang0
            )
            if "zrodlo_kontaktu" not in request.POST:
                odbiorca.zrodlo_kontaktu = "www"

            # Automatycznie potwierdzamy zapisy, które dokonały się z formularza ankiet.
            # (Aczkolwiek i tak zapisy z ankiet są obsługiwane przez inny widok).
            potwierdzony = odbiorca.zrodlo_kontaktu == "ankieta"
            if potwierdzony:
                odbiorca.token = ""
                odbiorca.status = "potwierdzony"
                szablon_odpowiedzi = "newsletter/dziekujemy_za_zapisanie_sie.html"
            else:
                odbiorca.status = "nie potwierdzony"
                szablon_odpowiedzi = "newsletter/ok_wyslalismy_mejla.html"

            odbiorca.save()

            if not potwierdzony:
                odbiorca.wyslij_mail_potwierdzajacy(request)

            return render(
                request,
                szablon_odpowiedzi,
                {},
            )
    else:
        form = forms.EmailForm()
    return render(
        request,
        "newsletter/signup.html",
        {
            "form": form,
        },
    )


@never_cache
@activate_translation
def confirm(request, token, language=settings.DEFAULT_LANGUAGE):
    oczekujace = Odbiorca.objects.filter(token=token, status="nie potwierdzony")
    if not oczekujace:
        return render(
            request,
            "newsletter/nie_ma_takiego_tokena.html",
            {},
        )
    delikwent = oczekujace[0]
    delikwent.status = "potwierdzony"
    delikwent.token = ""
    delikwent.save()
    return render(
        request,
        "newsletter/dziekujemy_za_zapisanie_sie.html",
        {"email": delikwent.email},
    )


class AnkietaSubscriptionFormView(View):
    """
    Zapisywanie się na newsletter po wypełnieniu ankiety.
    """

    form_class = forms.AnkietaSubscriptionForm
    template_name = "newsletter/subskrypcja.html"
    initial = {"zrodlo_kontaktu": "ankieta"}

    @method_decorator(activate_translation)
    def dispatch(self, *args, **kwargs):
        return super().dispatch(*args, **kwargs)

    @method_decorator(never_cache)
    def get(self, request, language=settings.DEFAULT_LANGUAGE, *args, **kwargs):
        form = self.form_class(initial=self.initial)
        return render(
            request,
            self.template_name,
            {
                "form": form,
            },
        )

    @method_decorator(never_cache)
    def post(self, request, language=settings.DEFAULT_LANGUAGE, *args, **kwargs):
        form = self.form_class(request.POST)

        if form.is_valid():
            istniejacy = Odbiorca.objects.filter(
                email__iexact=form.cleaned_data["email"]
            )
            if istniejacy:
                # tu trochę ściemniamy wrzucając do jednego worka tych,
                # co czekają na potwierdzenie, tych co już potwierdzili
                # i tych, co są na liście opt-out, ale robimy to celowo,
                # żeby chronić prywatność naszych abonentów.
                return render(
                    request,
                    "newsletter/juz_jest_na_liscie.html",
                )

            form.save()
            return render(
                request,
                "newsletter/dziekujemy_za_zapisanie_sie.html",
            )

        return render(
            request,
            self.template_name,
            {
                "form": form,
            },
        )
