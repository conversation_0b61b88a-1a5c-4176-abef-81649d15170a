import datetime
import re

from django.conf import settings
from django.core.mail import send_mail
from django.db import connection, models
from django.db.models import Case, Count, F, Func, When
from django.template.loader import render_to_string
from django.urls import reverse
from django.utils.translation import ugettext_lazy as _

from i18n.models import WersjaJezykowa

from . import utils


def random_token():
    return utils.random_string(16)


class OdbiorcaManager(models.Manager):
    def stats_query(self, **filters):
        """
        Metoda, która robi query dla statystyk (wykresy) zapisanych
        użytkowników.

        Interesuje nas liczba użytkowników dla każdego źródła pogrupowana
        według miesięcy.
        """

        # Budujemy filtr poprzez który pobierzemy sumy wpisów dla danego
        # źródła.
        sources_annotate = {}

        for source in list(dict(self.model.ZRODLA_KONTAKTU).keys()):
            sources_annotate["{0}".format(source)] = Count(
                Case(
                    When(
                        condition=models.Q(zrodlo_kontaktu__exact=source),
                        then=1,
                    )
                )
            )

        qs = (
            self.filter(**filters)
            .annotate(
                month=Func(
                    F("czas_zgloszenia"),
                    function="DATE_TRUNC",
                    template="%(function)s('month', %(expressions)s)",
                )
            )
            .values("month")
            .annotate(**sources_annotate)
        )
        qs.query.group_by = ["month"]
        return qs.order_by("month")


class Odbiorca(models.Model):
    email = models.EmailField()
    status = models.CharField(
        max_length=128,
        choices=(
            ("nie potwierdzony", "nie potwierdzony"),
            ("potwierdzony", "potwierdzony"),
            ("opt-out", "opt-out"),
        ),
    )
    token = models.CharField(max_length=64, blank=True, default=random_token)
    czas_zgloszenia = models.DateTimeField(auto_now_add=True)

    imie = models.CharField("imię", max_length=64, blank=True)
    nazwisko = models.CharField(max_length=128, blank=True)
    telefon = models.CharField(max_length=50, blank=True)
    firma = models.CharField(max_length=128, blank=True)
    ZRODLA_KONTAKTU = (
        ("reczne", "Wpisanie ręczne"),
        ("www", "Strona WWW"),
        ("ankieta", "Ankieta"),
        ("zgloszenie", "Zgłoszenie"),
        ("kontakt", "Formularz kontaktowy"),
        ("box-pytanie", "Szybkie pytanie"),
        ("termin", "Propozycja terminu"),
    )
    zrodlo_kontaktu = models.CharField(
        "źródło kontaktu",
        max_length=16,
        choices=ZRODLA_KONTAKTU,
        blank=True,
        default="reczne",
    )
    uczestnik = models.ForeignKey(
        "www.Uczestnik", blank=True, null=True, on_delete=models.SET_NULL
    )
    referer = models.CharField(
        "referer",
        max_length=200,
        blank=True,
    )
    lang = models.CharField(
        "język",
        max_length=2,
        blank=True,
    )
    tos_agreement = models.DateTimeField(
        "zgoda na przesyłanie informacji handlowej",
        null=True,
        blank=True,
        default=datetime.datetime.now,
    )

    objects = OdbiorcaManager()

    class Meta:
        verbose_name_plural = "odbiorcy"

    def __str__(self):
        return self.email

    def wyslij_mail_potwierdzajacy(self, request=None):
        """
        FIXME 2013-10-04: Ta metoda jest łatana na potrzeby wysyłania
        angielskiejwersji wiadomości. Domyślnie, jeżeli nie przekazany
        będzie kontekst, to wysłana polska wersja językowa. To by się
        przydało unormować i wyrzucić z modelu.
        """
        link = reverse(
            "newsletter_confirm",
            kwargs={
                "token": self.token,
                "language": WersjaJezykowa.biezaca().kod_jezyka,
            },
        )
        tresc_maila = render_to_string(
            "newsletter/potwierdzenie_email.txt",
            {
                "link": "http://%s%s" % (WersjaJezykowa.biezaca().domain, link),
                "email": self.email,
            },
            request,
        )

        if WersjaJezykowa.biezaca() == "en":
            mail_from = settings.MAIL_FROM_ADDRESS_EN
        else:
            mail_from = settings.MAIL_FROM_ADDRESS

        send_mail(
            _("Potwierdzenie zapisu na newsletter"),
            tresc_maila,
            mail_from,
            [self.email],
        )

    @classmethod
    def dodaj_potwierdzonego(cls, email, **kwargs):
        """
        Dodaje potwierdzonego odbiorcę lub modyfikuje istniejącego o zadanym mailu.
        Ustawia atrybuty odbiorcy na podstawie przekazanych parametrów (o ile odbiorca
        nie jest już potwierdzony).
        """

        # Tutaj powinno być .get zamiast .filter, ale wygląda na to,
        # że nie mamy ograniczeń w bazie na zduplikowane maile.
        try:
            odbiorca = cls.objects.filter(email=email)[0]
        except IndexError:
            odbiorca = cls()
            odbiorca.email = email
        # Jeśli odbiorca jest już potwierdzony, to nie chcemy nic z nim dalej robić.
        if odbiorca.status == "potwierdzony":
            return odbiorca
        odbiorca.status = "potwierdzony"
        odbiorca.token = ""
        odbiorca.czas_zgloszenia = datetime.datetime.now()
        for atrybut, wartosc in list(kwargs.items()):
            setattr(odbiorca, atrybut, wartosc)
        odbiorca.save()
        return odbiorca

    @staticmethod
    def podaj_refererlang(referer0=None, lang0=None):
        refererstring = "{0}".format(referer0) if referer0 else ""
        refererclean = re.sub(r"\?.*", "", refererstring)[:200]
        if not lang0:
            if "/en/" in refererclean:
                lang0 = "en"
            elif refererclean:
                lang0 = "pl"
            else:
                lang0 = ""
        return refererclean, lang0
