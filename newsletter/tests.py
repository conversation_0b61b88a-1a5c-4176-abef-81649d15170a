import datetime
from collections import namedtuple

from django.conf import settings
from django.core import mail
from django.http import HttpRequest
from django.test import TestCase
from django.urls import reverse

from newsletter.models import Odbiorca
from newsletter.views import signup
from www.testfactories import OdbiorcaFactory
from www.testhelpers import ALXTestCase


class NewsletterSignupTest(ALXTestCase):
    def test_newsletter_view_signup(self):
        """
        Sprawdź czy zapis produkuje odpowiedź na minimalne dane,
        a w bazie znalazł się odbiorca.
        """
        request = HttpRequest()
        request.POST["email"] = "<EMAIL>"
        request.POST["tos"] = "1"
        request.META["SERVER_NAME"] = "localhost"
        request.META["SERVER_PORT"] = 8081
        request.method = "POST"
        response = signup(request)
        self.assertContains(response, "wysłaliśmy", 1, 200)
        self.assertEqual(Odbiorca.objects.filter(email="<EMAIL>").count(), 1)

    def test_wysylanie_potwierdzenia(self):
        """
        Sprawdź czy potwierdzenie zapisu jest wysyłane w stosownym
        języku i zawiera link do właściwej strony potwierdzenia.
        """
        Lang = namedtuple("Lang", "lang response mail link")

        languages = [
            Lang(
                "pl",
                "kliknij w odnośnik, który przed chwilą wysłaliśmy"
                " na podany adres e-mail",
                "Aby potwierdzić tę chęć, proszę odwiedzić poniższy adres",
                "http://{0}".format(settings.DOMENY_DLA_JEZYKOW["pl"]),
            ),
            Lang(
                "en",
                "link in the email we just sent you",
                "To confirm that you wish to receive our Newsletter",
                "http://{0}".format(settings.DOMENY_DLA_JEZYKOW["en"]),
            ),
        ]

        for lang in languages:
            mail.outbox = []
            form_data = {"email": "jasiu_{0}@example.com".format(lang.lang), "tos": "1"}
            url = reverse("newsletter_signup", kwargs={"language": lang.lang})
            response = self.client.post(url, form_data)
            self.assertContains(response, lang.response, 1, 200)
            self.assertIn(lang.mail, mail.outbox[0].body)
            self.assertIn(lang.link, mail.outbox[0].body)

    def test_autopotwierdzanie_zapisow_z_ankiet(self):
        """Sprawdzić, że przy zapisie z ankiety nie jest wymagane potwierdzenie."""
        # Uwaga: do zapisów z ankiet jest stosowany inny widok niż w
        # pozostałych przypadkach.
        response = self.client.post(
            reverse("newsletter_subscription_form", kwargs={"language": "pl"}),
            {
                "email": "<EMAIL>",
                "tos": "1",
                "zrodlo_kontaktu": "ankieta",
            },
        )
        # self.assertRedirects(response, reverse('newsletter_podziekowanie'))
        self.assertTemplateUsed(response, "newsletter/dziekujemy_za_zapisanie_sie.html")
        odbiorca = Odbiorca.objects.get(email="<EMAIL>")
        self.assertFalse(odbiorca.token)
        self.assertEqual(odbiorca.status, "potwierdzony")
        self.assertFalse(mail.outbox)

    wymagane = {
        "pl": "To pole jest wymagane.",
        "en": "This field is required.",
    }

    def test_wejscie_ze_stopki(self):
        url_ze_stopki = {"pl": "/newsletter/", "en": "/en/newsletter/"}
        expect = "signup-error"
        msg = "Nie pojawiła się klasa dla błędu z signup.html"
        for lang in self.wymagane:
            response = self.client.post(url_ze_stopki[lang], {"email": "", "tos": ""})
            msg_text = 'Powinien pojawić się tekst w wersji: "{0}"'.format(lang)
            self.assertContains(response, expect, 2, 200, msg)
            self.assertContains(response, self.wymagane[lang], 2, 200, msg_text)

    def test_wejscie_z_formularza(self):
        expect = "signup-error"
        msg = "Nie pojawiła się klasa dla błędu z signup.html"
        for lang in self.wymagane:
            url_z_formularza = reverse("newsletter_signup", kwargs={"language": lang})
            response = self.client.post(url_z_formularza, {"email": "", "tos": ""})
            msg_text = 'Powinien pojawić się tekst w wersji: "{0}"'.format(lang)
            self.assertContains(response, expect, 2, 200, msg)
            self.assertContains(response, self.wymagane[lang], 2, 200, msg_text)


class NewsletterOdbiorcaTest(TestCase):
    def test_odbiorca_podaj_refererlang(self):
        """
        Sprawdzanie, czy produkuje właściwe rezultaty dla podstatwowych
        przypadków.
        """
        referer, lang = Odbiorca.podaj_refererlang(None, None)
        self.assertEqual("", referer, "nie zamienia Nona w blank")
        self.assertEqual("", lang, "nie zamienia Nona w blank")

        referer, lang = Odbiorca.podaj_refererlang("costam", "")
        self.assertEqual("costam", referer, "niepotrzebnie ruszyło referera")
        self.assertEqual("pl", lang, "złe wyliczenie języka")

        referer, lang = Odbiorca.podaj_refererlang("costam", "en")
        self.assertEqual("en", lang, "niepotrzebnie liczony język")

        referer, lang = Odbiorca.podaj_refererlang("cos/en/tam?ble&ble=ble%%%%", "")
        self.assertEqual("cos/en/tam", referer, "nieprawidłowe oczyszczenie referera")
        self.assertEqual("en", lang, "złe wyliczenie języka")


class OdbiorcaManagerTestCase(TestCase):
    def test_manager_stats_query(self):
        """
        Funkcja testuje metodę managera OdbiorcaManager - `stats_query`,
        która zwraca statystyki zapisów na newsletter z danego źródła. Wyniki
        są pogrupowane po miesiącach.
        """

        # Brak zapisow - nic nie zwracamy
        objects = OdbiorcaFactory._meta.model.objects  # tu siedzi OdbiorcaManager
        qs = list(objects.stats_query())

        self.assertEqual(qs, [])

        # Dodajemy trzech aktywnych subskrybentów:
        #  - jeden dopisany ze źródłem www
        #  - drugi dopisany ze źródłem ankieta
        #  - trzeci dopisany ze źródłem www
        created_at = datetime.datetime.now()

        c1 = OdbiorcaFactory.create(zrodlo_kontaktu="www")
        c2 = OdbiorcaFactory.create(zrodlo_kontaktu="ankieta")
        c3 = OdbiorcaFactory.create(zrodlo_kontaktu="www")

        for c in [c1, c2, c3]:
            c.czas_zgloszenia = created_at
            c.save()

        qs = list(objects.stats_query())

        # Powinniśmy dostać jeden rekord
        self.assertEqual(len(qs), 1)

        # Data
        self.assertEqual(
            qs[0]["month"], datetime.datetime(created_at.year, created_at.month, 1)
        )

        # Liczba zapisanych w źródle: www - 2 i ankieta - 1
        self.assertEqual(qs[0]["www"], 2)
        self.assertEqual(qs[0]["ankieta"], 1)

        # Teraz użytkownika c1 i c2 oznaczamy jako nieaktywnych i filtrujemy
        # tylko po aktywnych.
        c1.status = "opt-aut"
        c1.save()

        c2.status = "opt-aut"
        c2.save()

        qs = list(objects.stats_query(status="potwierdzony"))

        # Powinniśmy dostać jeden rekord
        self.assertEqual(len(qs), 1)

        # Data
        self.assertEqual(
            qs[0]["month"], datetime.datetime(created_at.year, created_at.month, 1)
        )

        # Liczba zapisanych w źródle: www - 1 i ankieta - 0
        self.assertEqual(qs[0]["www"], 1)
        self.assertEqual(qs[0]["ankieta"], 0)

        # Czyścimy bazę
        OdbiorcaFactory._meta.model.objects.all().delete()

        # Teraz dodajemy obiekty w różnych datach i źródłach

        c1 = OdbiorcaFactory.create(zrodlo_kontaktu="www")
        c2 = OdbiorcaFactory.create(zrodlo_kontaktu="ankieta")
        c3 = OdbiorcaFactory.create(zrodlo_kontaktu="www")
        c4 = OdbiorcaFactory.create(zrodlo_kontaktu="zgloszenie")

        c1.czas_zgloszenia = datetime.datetime(2015, 1, 23)
        c1.save()

        c2.czas_zgloszenia = datetime.datetime(2015, 1, 11)
        c2.save()

        c3.czas_zgloszenia = datetime.datetime(2015, 3, 3)
        c3.save()

        c4.czas_zgloszenia = datetime.datetime(2015, 4, 3)
        c4.save()

        qs = list(objects.stats_query(status="potwierdzony"))

        # Powinniśmy dostać trzy rekordy
        self.assertEqual(len(qs), 3)

        # Daty
        self.assertEqual(qs[0]["month"], datetime.datetime(2015, 1, 1))
        self.assertEqual(qs[1]["month"], datetime.datetime(2015, 3, 1))
        self.assertEqual(qs[2]["month"], datetime.datetime(2015, 4, 1))

        # Liczba zapisanych w źródle
        self.assertEqual(qs[0]["www"], 1)
        self.assertEqual(qs[0]["ankieta"], 1)
        self.assertEqual(qs[0]["zgloszenie"], 0)

        self.assertEqual(qs[1]["www"], 1)
        self.assertEqual(qs[1]["ankieta"], 0)
        self.assertEqual(qs[1]["zgloszenie"], 0)

        self.assertEqual(qs[2]["www"], 0)
        self.assertEqual(qs[2]["ankieta"], 0)
        self.assertEqual(qs[2]["zgloszenie"], 1)
