import django.db.models.deletion
from django.db import migrations, models

import newsletter.models


class Migration(migrations.Migration):

    dependencies = [
        ("www", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Odbiorca",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("email", models.EmailField(max_length=75)),
                (
                    "status",
                    models.CharField(
                        max_length=128,
                        choices=[
                            ("nie potwierdzony", "nie potwierdzony"),
                            ("potwierdzony", "potwierdzony"),
                            ("opt-out", "opt-out"),
                        ],
                    ),
                ),
                (
                    "token",
                    models.CharField(
                        default=newsletter.models.random_token,
                        max_length=64,
                        blank=True,
                    ),
                ),
                ("czas_zgloszenia", models.DateTimeField(auto_now_add=True)),
                (
                    "imie",
                    models.CharField(max_length=64, verbose_name="imię", blank=True),
                ),
                ("nazwisko", models.CharField(max_length=128, blank=True)),
                ("telefon", models.CharField(max_length=50, blank=True)),
                ("firma", models.CharField(max_length=128, blank=True)),
                (
                    "zrodlo_kontaktu",
                    models.CharField(
                        default="reczne",
                        max_length=16,
                        verbose_name="źródło kontaktu",
                        blank=True,
                        choices=[
                            ("reczne", "Wpisanie ręczne"),
                            ("www", "Strona WWW"),
                            ("ankieta", "Ankieta"),
                            ("zgloszenie", "Zgłoszenie"),
                            ("kontakt", "Formularz kontaktowy"),
                            ("box-pytanie", "Szybkie pytanie"),
                            ("termin", "Propozycja terminu"),
                        ],
                    ),
                ),
                (
                    "referer",
                    models.CharField(
                        max_length=200, verbose_name="referer", blank=True
                    ),
                ),
                (
                    "lang",
                    models.CharField(max_length=2, verbose_name="język", blank=True),
                ),
                (
                    "uczestnik",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.SET_NULL,
                        blank=True,
                        to="www.Uczestnik",
                        null=True,
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "odbiorcy",
            },
            bases=(models.Model,),
        ),
    ]
