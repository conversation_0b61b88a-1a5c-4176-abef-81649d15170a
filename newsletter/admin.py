import csv
from io import StringIO

from django.conf import settings
from django.conf.urls import url
from django.contrib import admin, messages
from django.contrib.admin import SimpleListFilter
from django.core.exceptions import ValidationError
from django.core.validators import validate_email
from django.urls import reverse
from django.utils.safestring import mark_safe
from django.shortcuts import redirect, render

from www.actions import export_as_csv_action_v2
from .forms import CsvImportForm
from .models import Odbiorca


class TosAgreementFilter(SimpleListFilter):
    title = "zgoda marketingowa"
    parameter_name = "tos_agreement"

    def lookups(self, request, model_admin):
        return (
            ("true", "Tak"),
            ("false", "Nie"),
        )

    def queryset(self, request, queryset):
        if self.value() == "true":
            return queryset.filter(tos_agreement__isnull=False, status="potwierdzony")
        if self.value() == "false":
            return queryset.filter(tos_agreement__isnull=True)
        return queryset


class OdbiorcaAdmin(admin.ModelAdmin):
    change_list_template = "newsletter/change_list.html"

    list_display = (
        "email",
        "czas_zgloszenia",
        "status",
        "zrodlo_kontaktu",
        "uczestnik_link",
        "tos_agreement",
    )
    list_filter = ("status", TosAgreementFilter, "zrodlo_kontaktu", "uczestnik__status")
    search_fields = ("email",)
    raw_id_fields = ("uczestnik",)
    readonly_fields = ("tos_agreement",)
    actions = [
        export_as_csv_action_v2(
            "Eksport CSV (marketing)",
            fields=[
                ("ID", lambda x: x.pk),
                ("email", lambda x: x.email),
                ("czas_zgloszenia", lambda x: x.czas_zgloszenia),
                ("status", lambda x: x.get_status_display()),
                ("zrodlo", lambda x: x.get_zrodlo_kontaktu_display()),
                (
                    "participant",
                    lambda x: "https://{}{}".format(
                        settings.DOMENY_DLA_JEZYKOW["pl"],
                        reverse("admin:www_uczestnik_change", args=(x.uczestnik_id,)),
                    )
                    if x.uczestnik_id
                    else "[brak danych]",
                ),
                (
                    "szkolenie",
                    lambda x: str(x.uczestnik.termin) if x.uczestnik_id else "[brak danych]",
                ),
                (
                    "status_przeszkolenia",
                    lambda x: x.uczestnik.get_status_display() if x.uczestnik_id else "[brak danych]",
                ),
                ("tos_agreement", lambda x: x.tos_agreement),
                ("telefon", lambda x: x.telefon),
            ],
            optout_email_field="email",
        )
    ]


    def changelist_view(self, request, extra_context=None):
        extra_context = extra_context or {}
        extra_context['allowed_fields'] = {field.name for field in Odbiorca._meta.fields} - {"id", "token", "tos_agreement", "uczestnik", "czas_zgloszenia"}
        return super().changelist_view(request, extra_context=extra_context)

    def get_queryset(self, request):
        qs = super().get_queryset(request).select_related('uczestnik', 'uczestnik__termin', 'uczestnik__termin__szkolenie')
        return qs

    def get_actions(self, request):
        actions = super(OdbiorcaAdmin, self).get_actions(request)
        try:
            del actions["delete_selected"]
        except KeyError:
            pass
        return actions

    def get_urls(self):
        urls = super().get_urls()
        my_urls = [
            url("update-receiver-as-opt-out", self.admin_site.admin_view(self.update_as_opt_out)),
            url("add-newsletter-receivers", self.admin_site.admin_view(self.add_newsletter_receivers)),
        ]
        return my_urls + urls

    def add_newsletter_receivers(self, request):

        allowed_fields = {field.name for field in Odbiorca._meta.fields}
        if request.method == "POST":
            form = CsvImportForm(request.POST, request.FILES)
            if form.is_valid():
                try:
                    csv_file = StringIO(request.FILES["csv_file"].read().decode())
                    reader = csv.DictReader(csv_file, delimiter=';')

                    # Sprawdzamy nagłówki
                    required_fields = {'email'}
                    allowed_fields = {field.name for field in Odbiorca._meta.fields}

                    csv_headers = set(reader.fieldnames)

                    if not required_fields.issubset(csv_headers):
                        missing_fields = required_fields - csv_headers
                        raise ValueError(f"Brakuje wymaganych nagłówków: {', '.join(missing_fields)}")

                    if not csv_headers.issubset(allowed_fields):
                        extra_headers = csv_headers - allowed_fields
                        raise ValueError(f"Niezgodne nagłówki: {', '.join(extra_headers)}")

                except ValueError as e:
                    messages.error(request, f"Nagłówki CSV są nieprawidłowe: {str(e)}")
                    return redirect("..")
                except Exception as e:
                    messages.error(request, f"Nie można przetworzyć pliku: {str(e)}")
                    return redirect("..")

                else:
                    self.process_new_data(request, reader)
                    messages.success(request, "Dane zostały zaimportowane.")
                return redirect("..")
        else:
            form = CsvImportForm()

        payload = {"form": form}
        return render(request, "newsletter/csv_form.html", payload)

    def process_new_data(self, request, reader):
        new_receivers = []
        updated_receivers = []

        for row in reader:
            if row:
                try:
                    email_address = row.get("email", "").strip().lower()
                    validate_email(email_address)
                except ValidationError:
                    messages.error(request, f"Nie można przetworzyć adresu: {email_address}")
                    continue

                # Pobierz opcjonalne dane

                imie = row.get("imie", "").strip()
                nazwisko = row.get("nazwisko", "").strip()
                telefon = row.get("telefon", "").strip()
                firma = row.get("firma", "").strip()
                status = row.get("status", "nie potwierdzony").strip()

                # Tworzenie lub aktualizacja rekordu
                odbiorca, created = Odbiorca.objects.get_or_create(
                    email=email_address,
                    defaults={
                        "imie": imie,
                        "nazwisko": nazwisko,
                        "telefon": telefon,
                        "firma": firma,
                        "status": status,  # Ustawienie statusu z CSV
                    },
                )

                if not created:
                    # Aktualizacja istniejącego rekordu
                    # odbiorca.imie = imie or odbiorca.imie
                    # odbiorca.nazwisko = nazwisko or odbiorca.nazwisko
                    # odbiorca.telefon = telefon or odbiorca.telefon
                    # odbiorca.firma = firma or odbiorca.firma
                    # odbiorca.status = status or odbiorca.status
                    # odbiorca.save()
                    updated_receivers.append(email_address)
                else:
                    new_receivers.append(email_address)

        messages.success(request, f"Dodano odbiorców: {new_receivers}")
        if updated_receivers:
            messages.success(request, f"Ci odbiorcy już istnieją: {updated_receivers}")



    def update_as_opt_out(self, request):
        if request.method == "POST":
            form = CsvImportForm(request.POST, request.FILES)
            if form.is_valid():
                try:
                    csv_file = StringIO(request.FILES["csv_file"].read().decode())
                    reader = csv.reader(csv_file, delimiter=';')
                except:
                    messages.error(request, "Nie można przetworzyć pliku.")
                    raise
                else:
                    self.update_receivers_as_opt_out(request, reader)
                    messages.success(request, "Dane zostały zaimportowane.")
                return redirect("..")
        else:
            form = CsvImportForm()

        payload = {"form": form}
        return render(
            request, "csvimport/csv_form.html", payload
        )

    def update_receivers_as_opt_out(self, request, reader):
        for row in reader:
            if row:
                try:
                    email_address = row[0]
                    validate_email(email_address)
                except ValidationError:
                    messages.error(request, "Nie można przetworzyć adresu={}".format(email_address))
                else:
                    if not Odbiorca.objects.filter(email__iexact=email_address).update(status='opt-out'):
                        messages.error(request, "Nie ma takiego adresu email ({}) w bazie.".format(email_address))

    def uczestnik_link(self, obj):
        if obj.uczestnik:
            return mark_safe(
                '<a href="{}">{}</a>'.format(
                    reverse("admin:www_uczestnik_change", args=(obj.uczestnik.id,)),
                    obj.uczestnik,
                )
            )
        else:
            return ""

    uczestnik_link.short_description = "uczestnik"


admin.site.register(Odbiorca, OdbiorcaAdmin)
