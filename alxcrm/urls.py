from django.conf import settings
from django.conf.urls.i18n import i18n_patterns
from django.contrib import admin
from django.contrib.sitemaps.views import sitemap
from django.shortcuts import render, redirect
from django.http import HttpResponse
from django.urls import include, path, re_path
from django.views import static
from django.views.i18n import JavaScriptCatalog
from django.contrib.admin.views.decorators import staff_member_required

from common.sitemap import sitemaps
from newsletter import views as newsletter_views
from search import views as search_views
from www import views as www_views

urlpatterns = i18n_patterns(
    path("jsi18n/", JavaScriptCatalog.as_view(), name="jsi18n"),
)



urlpatterns += [
    re_path("^admin/auth/user/(?P<user_id>[0-9]+)/password/", staff_member_required(lambda request, **kwargs: render(request, "www/admin/change_password.html"))),
    re_path("^admin/password_change/", staff_member_required(lambda request, **kwargs: render(request, "www/admin/change_password.html"))),

    path(
        "admin/reset-nginx-cache/",
        www_views.reset_nginx_cache,
        name="reset_nginx_cache",
    ),
    path("admin/", admin.site.urls),
    path("szukaj/", search_views.search, name="search_pl"),
    path(
        "en/search/",
        search_views.search,
        kwargs={"language": "en"},
        name="search_en",
    ),
    re_path(
        r"^(?P<language>pl|en)/newsletter/$",
        newsletter_views.signup,
        name="newsletter_signup",
    ),
    path("newsletter/", include("newsletter.urls"), kwargs={"language": "pl"}),
    path("en/newsletter/", include("newsletter.urls"), kwargs={"language": "en"}),
    path("captcha/", include("captcha.urls")),
    path("forum/", include("forum.urls")),
    # Ankiety
    path("en/surveys/", include("ankiety.urls_en"), kwargs={"language": "en"}),
    path("ankiety/", include("ankiety.urls"), kwargs={"language": "pl"}),
    path(
        "robots.txt",
        lambda x: HttpResponse(
            "User-agent: *\n"
            "Disallow: /zgloszenie\n"
            "Disallow: /certificate\n"
            "Disallow: /certyfikat\n"
            "sitemap: http://www.alx.pl/sitemap-alx.xml",
            content_type="text/plain",
        ),
    ),
    path(
        "sitemap-alx.xml",
        sitemap,
        {"sitemaps": sitemaps},
        name="django.contrib.sitemaps.views.sitemap",
    ),
    path("crm/", include("crm.urls")),
    path("auth/", include("alx_auth.urls", namespace="alx_auth")),
    re_path(r"^(?P<language>pl|en)/lead/", include("leads.urls", namespace="leads")),
    path("", include("www.urls")),
]

handler404 = "www.views.custom_404"

if settings.DEBUG:
    import debug_toolbar
    from django.contrib.staticfiles.urls import staticfiles_urlpatterns

    urlpatterns.extend(
        [
            re_path(
                r"^media/(?P<path>.*)$",
                static.serve,
                {"document_root": settings.ASSETS_ROOT},
            ),
        ]
    )
    urlpatterns += staticfiles_urlpatterns()

    urlpatterns.extend(
        [
            path("__debug__/", include(debug_toolbar.urls)),
        ]
    )
