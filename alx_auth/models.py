import datetime

from django.conf import settings
from django.contrib.auth.models import User
from django.db import models
from django.db.models.signals import post_save


class LoginInfo(models.Model):
    user = models.OneToOneField(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)

    # We should guarantee this to be accurate at least up to last_login datetime.
    password_change_datetime = models.DateTimeField(
        blank=True,
        null=True,
    )
    password_remind_dismissed_datetime = models.DateTimeField(
        blank=True,
        null=True,
    )
    alert_occurred_datetime = models.DateTimeField(
        blank=True,
        null=True,
    )

    @classmethod
    def create_for_user(cls, user):
        """
        A method used to create initial object for specific user.
        """
        cls.objects.create(user=user)

    def password_needs_change(self):
        if not settings.DNI_WAZNOSCI_HASLA:
            return False

        now = datetime.datetime.now()
        password_age = (self.user.last_login or now) - (
            self.password_change_datetime or datetime.datetime(1970, 1, 1)
        )
        password_old = password_age > datetime.timedelta(
            days=settings.DNI_WAZNOSCI_HASLA
        )

        password_dismissed_age = now - (
            self.password_remind_dismissed_datetime or datetime.datetime(1970, 1, 1)
        )
        password_dismissal_is_fresh = password_dismissed_age <= datetime.timedelta(
            days=settings.DNI_UCISZENIA_PRZYPOMNIENIA_ZMIANY_HASLA,
        )
        needs_change = password_old and not password_dismissal_is_fresh

        if needs_change:
            self.alert_occurred_datetime = now
            self.save()
        return needs_change


def create_login_info(sender, instance, created, **kwargs):
    if created:
        LoginInfo.create_for_user(instance)


post_save.connect(create_login_info, User)
