import datetime

from django.http import HttpResponse, HttpResponseRedirect
from django.utils.decorators import method_decorator
from django.views.decorators.cache import never_cache
from django.views.generic import View


class PasswordRemindDismissView(View):
    @method_decorator(never_cache)
    def post(self, request):
        if request.user.is_authenticated:
            request.user.logininfo.password_remind_dismissed_datetime = (
                datetime.datetime.now()
            )
            request.user.logininfo.save()
            if request.POST.get("redirect"):
                return HttpResponseRedirect(request.POST["redirect"])
            else:
                return HttpResponse(status=201)
        else:
            return HttpResponse(status=401)
