import datetime

import factory
from django.contrib.auth.hashers import make_password
from django.contrib.auth.models import User

from .models import LoginInfo


class UserFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = User

    username = factory.Sequence(lambda n: "username{0}".format(n))
    first_name = factory.Sequence(lambda n: "first name{0}".format(n))
    last_name = factory.Sequence(lambda n: "last name{0}".format(n))
    email = factory.Sequence(lambda n: "user{0}@alx.pl".format(n))
    is_active = True
    is_staff = False
    is_superuser = False
    last_login = datetime.datetime(1970, 1, 1)
    password = make_password("dummy_password")

    @classmethod
    def _create(self, model_class, *args, **kwargs):
        password = kwargs.pop("password", None)
        usr = super()._create(model_class, *args, **kwargs)
        usr.set_password(password)
        usr.save()
        return usr


class LoginInfoFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = LoginInfo

    user = factory.SubFactory(UserFactory)
