import datetime
import itertools

import factory
import mock
import pytz
from django.conf import settings
from django.contrib.auth import authenticate
from django.db.models import signals
from django.test import TestCase
from django.test.utils import override_settings
from django.urls import reverse

from .factories import LoginInfoFactory, UserFactory
from .models import LoginInfo

LDAP_DATETIME_FORMAT = "%Y%m%d%H%M%SZ"


@override_settings(
    AUTHENTICATION_BACKENDS=["alx_auth.backends.LDAPAuthBackend"],
    LDAP_SERVER_URL="ldap_server_url",
    LDAP_INITIAL_LOGIN_DN="admin",
    LDAP_INITIAL_LOGIN_PASSWORD="secret",
    LDAP_SEARCH_BASE="foo=bar",
)
class LDAPAuthTestCase(TestCase):
    @mock.patch("ldap.initialize")
    def test_ldap_login_synchronizes_password_change_time(self, ldap_initialize):
        user = UserFactory.create(username="johnny")
        login_info = user.logininfo
        login_info.password_change_datetime = datetime.datetime(2014, 1, 15)
        login_info.save()
        desired_datetime = datetime.datetime(2014, 12, 2, 11, 22, 33)
        desired_datetime_utc = (
            pytz.timezone(settings.TIME_ZONE)
            .localize(desired_datetime)
            .astimezone(pytz.utc)
        )
        ldap_initialize.return_value.search_s.return_value = [
            (
                "cn=John Doe,foo=bar",
                {
                    "pwdChangedTime": [
                        desired_datetime_utc.strftime(LDAP_DATETIME_FORMAT)
                    ],
                },
            )
        ]

        self.assertNotEqual(login_info.password_change_datetime, desired_datetime)
        self.assertTrue(authenticate(username="johnny", password="irrelevant"))
        login_info = login_info.__class__.objects.get(pk=login_info.pk)
        self.assertEqual(login_info.password_change_datetime, desired_datetime)


class LoginInfoTestCase(TestCase):
    def test_login_info_is_created_for_each_new_user_model(self):
        user = UserFactory.create()
        self.assertTrue(user.logininfo)

    @override_settings(
        DNI_WAZNOSCI_HASLA=5,
        DNI_UCISZENIA_PRZYPOMNIENIA_ZMIANY_HASLA=10,
    )
    @factory.django.mute_signals(signals.pre_save, signals.post_save)
    def test_password_needs_change_method(self):
        now = datetime.datetime.now()
        universe_beginning = datetime.datetime(1970, 1, 1)

        # In the empty case, we assume that password needs to be changed.
        for last_login in {now, now - datetime.timedelta(days=30)}:
            outdated_info1 = LoginInfoFactory(
                password_change_datetime=None,
                password_remind_dismissed_datetime=None,
                user__last_login=last_login,
            )
            self.assertFalse(outdated_info1.alert_occurred_datetime)
            self.assertTrue(outdated_info1.password_needs_change())
            self.assertTrue(outdated_info1.alert_occurred_datetime)

        # If the passwords has been changed recently, it does not need to be changed,
        # regardless of the last login and remind dismissal time.
        for last_login, dismissed_datetime in itertools.product(
            (now, now - datetime.timedelta(days=7), now - datetime.timedelta(days=12)),
            (now, now - datetime.timedelta(days=12), None),
        ):
            up_to_date_info = LoginInfoFactory(
                password_change_datetime=now - datetime.timedelta(hours=1),
                password_remind_dismissed_datetime=dismissed_datetime,
                user__last_login=last_login,
            )
            self.assertFalse(up_to_date_info.password_needs_change())
            self.assertFalse(up_to_date_info.alert_occurred_datetime)

        # If the password was changed long time ago, it needs to be changed unless the dismissal
        # has been made recently or last login was made shortly after last password change.
        for outdated_datetime in {now - datetime.timedelta(days=30), None}:
            up_to_date_info = LoginInfoFactory(
                password_change_datetime=outdated_datetime,
                password_remind_dismissed_datetime=now - datetime.timedelta(days=1),
                user__last_login=now - datetime.timedelta(minutes=5),
            )
            self.assertFalse(up_to_date_info.password_needs_change())
            self.assertFalse(up_to_date_info.alert_occurred_datetime)

            up_to_date_info2 = LoginInfoFactory(
                password_change_datetime=outdated_datetime,
                password_remind_dismissed_datetime=(
                    outdated_datetime or universe_beginning
                )
                - datetime.timedelta(days=1),
                user__last_login=(outdated_datetime or universe_beginning)
                + datetime.timedelta(days=4),
            )
            self.assertFalse(up_to_date_info2.password_needs_change())
            self.assertFalse(up_to_date_info2.alert_occurred_datetime)

            outdated_info = LoginInfoFactory(
                password_change_datetime=outdated_datetime,
                password_remind_dismissed_datetime=now - datetime.timedelta(days=15),
                user__last_login=now - datetime.timedelta(hours=1),
            )
            self.assertTrue(outdated_info.password_needs_change())
            self.assertTrue(outdated_info.alert_occurred_datetime)

    @override_settings(
        DNI_WAZNOSCI_HASLA=None,
        DNI_UCISZENIA_PRZYPOMNIENIA_ZMIANY_HASLA=10,
    )
    @factory.django.mute_signals(signals.pre_save, signals.post_save)
    def test_password_doesnt_need_change_without_expiration_time(self):
        """If there is no password expiration time, there should be no need to
        change the password."""

        info = LoginInfoFactory(
            password_change_datetime=None,
            password_remind_dismissed_datetime=None,
            user__last_login=datetime.datetime.now(),
        )
        self.assertFalse(info.password_needs_change())


class PasswordRemindDismissViewTestCase(TestCase):
    def test_dismissal_url_requires_login(self):
        response = self.client.post(reverse("alx_auth:dismiss_reminder"))
        self.assertEqual(response.status_code, 401)

    def test_password_dismissal(self):
        for redirect_url, status_code in {("/foo", 302), (None, 201)}:
            user = UserFactory.create(password="top-secret")
            login_info = user.logininfo
            self.client.login(username=user.username, password="top-secret")
            now = datetime.datetime.now()
            self.assertTrue(
                not login_info.password_remind_dismissed_datetime
                or login_info.password_remind_dismissed_datetime < now
            )
            data = {}
            if redirect_url:
                data["redirect"] = redirect_url
            response = self.client.post(reverse("alx_auth:dismiss_reminder"), data)
            self.assertEqual(response.status_code, status_code)
            login_info = LoginInfo.objects.get(pk=login_info.pk)
            self.assertTrue(login_info.password_remind_dismissed_datetime)
            self.assertGreaterEqual(login_info.password_remind_dismissed_datetime, now)
