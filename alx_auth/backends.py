import datetime
import logging

import ldap
import pytz
from django.conf import settings
from django.contrib.auth.backends import ModelBackend
from django.contrib.auth.models import User

from .models import LoginInfo

logger = logging.getLogger(__name__)

LDAP_DATETIME_FORMAT = "%Y%m%d%H%M%SZ"


class LDAPAuthBackend(ModelBackend):
    def authenticate(self, request, username=None, password=None, **kwargs):
        username = username.strip()
        u = User.objects.filter(username=username, is_active=True)
        if u:
            user = u[0]
            l = ldap.initialize(settings.LDAP_SERVER_URL)
            l.simple_bind_s(
                settings.LDAP_INITIAL_LOGIN_DN, settings.LDAP_INITIAL_LOGIN_PASSWORD
            )
            result = l.search_s(
                settings.LDAP_SEARCH_BASE,
                ldap.SCOPE_SUBTREE,
                "uid={}".format(username),
                ["pwdChangedTime"],
            )
            if len(result) == 1:
                pwdChangedTime = result[0][1].get("pwdChangedTime")

                try:
                    td = pwdChangedTime[0]
                    if td:
                        logininfo, created = LoginInfo.objects.get_or_create(user=user)
                        logininfo.password_change_datetime = pytz.utc.localize(
                            datetime.datetime.strptime(td, LDAP_DATETIME_FORMAT)
                        )
                        logininfo.save()
                except Exception as err:
                    pass

                try:
                    l.simple_bind_s(result[0][0], password)
                    return user
                except ldap.INVALID_CREDENTIALS:
                    return None
        logger.warning(
            "Zly login przy logowaniu przez LDAP. Uzytkownik: {}".format(username)
        )
        return None
