$(function() {

      // formularz zgloszeniowy - bledy
      if ($('p.errors_in_form').length > 0) {
          $('html,body').animate({scrollTop:$('p.errors_in_form').offset().top-20}, 400);
      }

    var pokaz_lub_schowaj_pola_od_doplaty_do_faktury = function(fade) {
        fade = typeof fade !== 'undefined' ? fade : true;

        var tekst_o_fakturze = $('.tekst_o_fakturze').html();
        var doplata_do_faktury = false;
        $.post("", $('form').serialize(), function(data) {
            if (data.doplata == "1") {
                $('#tekst_o_fakturze').html(tekst_o_fakturze);
                $('#tekst_o_fakturze').closest("tr").fadeIn(fade ? 400 : 0);
            } else {
                $('#tekst_o_fakturze').closest("tr").fadeOut(fade ? 400 : 0, function(){
                    $('#tekst_o_fakturze').html(tekst_o_fakturze);
                });
            }
        });
    };

    var pokaz_lub_schowaj_pola_od_doplaty_do_certyfikatu = function(fade) {
        fade = typeof fade !== 'undefined' ? fade : true;

        var tekst_o_certyfikacie = $('.tekst_o_certyfikacie').html();
        if ($('[name=drukowany_certyfikat]:checked').val() == "1"){
            $('#tekst_o_certyfikacie').html(tekst_o_certyfikacie);
            $('#tekst_o_certyfikacie').closest("tr").fadeIn(fade ? 400 : 0);
        } else {
            $('#tekst_o_certyfikacie').closest("tr").fadeOut(fade ? 400 : 0, function(){
                $('#tekst_o_certyfikacie').html(tekst_o_certyfikacie);
            });
        }
    };

    var pokaz_pole_wegetarianskie = function(fade) {
        fade = typeof fade !== 'undefined' ? fade : true;

        var termin_id = $('#id_termin')[0].value;
        if ((terminy_obiady[termin_id].obiady == 'obiady-wliczone') ||
                    ((terminy_obiady[termin_id].obiady == 'obiady-opcjonalne') &&
                    $('#id_chce_obiady_0')[0].checked)) {
            var ile_osob = parseInt($('#id_uczestnik_wieloosobowy_ilosc_osob')[0].value, 10);
            if ($('input:radio[name="prywatny"][value="1"]')[0].checked || isNaN(ile_osob) || ile_osob == 1) {
                $('#id_obiad_wegetarianski').closest("tr").fadeIn(fade ? 400 : 0);
                $('#id_ile_obiadow_wegetarianskich').closest("tr").fadeOut(0);
            } else {
                $('#id_obiad_wegetarianski').closest("tr").fadeOut(0);
                $('#id_ile_obiadow_wegetarianskich').closest("tr").fadeIn(fade ? 400 : 0);
            }
        } else {
            $('#id_obiad_wegetarianski').closest("tr").fadeOut(fade ? 400 : 0);
            $('#id_ile_obiadow_wegetarianskich').closest("tr").fadeOut(fade ? 400 : 0);
        }
    };

    var pokaz_lub_schowaj_pola_od_obiadow = function(fade) {
        fade = typeof fade !== 'undefined' ? fade : true;

        var termin_id = $('#id_termin')[0].value;
        var tekst_o_obiadach = $('.obiady[data-id='+ termin_id +']').html();

        if (tekst_o_obiadach) {
            $('#tekst_o_obiadach').html(tekst_o_obiadach);
            $('#tekst_o_obiadach').closest("tr").fadeIn(fade ? 400 : 0);
        } else{
            $('#tekst_o_obiadach').closest("tr").fadeOut(fade ? 400 : 0, function(){
                $('#tekst_o_obiadach').html(tekst_o_obiadach);
            });
        }
        if (terminy_obiady[termin_id].obiady == 'obiady-wliczone') {
            $('#id_chce_obiady_0').closest("tr").fadeOut(fade ? 400 : 0);
            pokaz_pole_wegetarianskie(fade);
        } else if (terminy_obiady[termin_id].obiady == 'obiady-opcjonalne') {
            $('#id_chce_obiady_0').closest("tr").fadeIn(fade ? 400 : 0);
            pokaz_pole_wegetarianskie(fade);
        } else if (terminy_obiady[termin_id].obiady == 'nie-ma-obiadow' ||
                    terminy_obiady[termin_id].obiady === '') {
            $('#id_chce_obiady_0').closest("tr").fadeOut(fade ? 400 : 0);
            $('#id_obiad_wegetarianski').closest("tr").fadeOut(fade ? 400 : 0);
            $('#id_ile_obiadow_wegetarianskich').closest("tr").fadeOut(fade ? 400 : 0);
        }
    };

    var zmien_pole_nazwisko_imie = function(fade, state) {
        fade = typeof fade !== 'undefined' ? fade : true;
        state = typeof state !== 'undefined' ? state : 1;

        fade_object_grupa = $('#id_uczestnik_wieloosobowy_ilosc_osob').closest('tr');
        elements = ["textarea", "input"];
        old_input = $("#id_imie_nazwisko");
        if ($('#id_czy_grupa_0').is(':checked')){
            fade_object_grupa.fadeOut(fade ? 400 : 0);
            $('[for=id_imie_nazwisko]').html(gettext("Imię i nazwisko"));
            $('[for=id_imie_nazwisko]+small').html("");
            $('#id_uczestnik_wieloosobowy_ilosc_osob').val('');
            $('#id_ile_obiadow_wegetarianskich').val('');
            pokaz_pole_wegetarianskie();
            current = elements[1];
            if (state == 1 && old_input[0].tagName == 'TEXTAREA'){
                old_input.val('');
            }
        }
        if ($('#id_czy_grupa_1').is(':checked')){
            fade_object_grupa.fadeIn(fade ? 400 : 0);
            $('[for=id_imie_nazwisko]').html(gettext("Lista osób"));
            $('[for=id_imie_nazwisko]+small').html("");
            current = elements[0];
        }
        new_input = $("<"+current+"></"+current+">").attr({
            id: old_input.attr('id'),
            name: old_input.attr('name'),
            type: "text",
            value: old_input.val()
        });
        new_input.val(old_input.val());
        old_input.after(new_input).remove();
    };

    var dodaj_gwiazdke = function(element) {
        var gw_content = $('p.form-legend').attr('data-gwiazdeczka');
        $(element).append("<span data-gw_content='" + gw_content + "'>*</span>");
    };

    var ustaw_prywatny_osoba = function (state) {
        if ($('input:radio[name="prywatny"][value="1"]')[0].checked) {
            $('#id_czy_grupa_0')[0].checked = true;
            zmien_pole_nazwisko_imie(true, state);
            dodaj_gwiazdke('[for=id_imie_nazwisko]');
        };
    };

    var zaleznosci_radio = function(pole_wlacznika, pola_zalezne, rodzaj) {
        var obiekt_radio = $(pole_wlacznika);

        if (obiekt_radio.length) {
            $.each(pola_zalezne, function (index, pole) {
                var obiekt_pola = $('#id_' + pole);
                if (obiekt_pola.attr('type') != 'hidden') {
                    var tr = obiekt_pola.closest("tr");
                    obiekt_radio.change(function () {
                        if (rodzaj && obiekt_radio[0].checked) {
                            tr.fadeOut(600);
                        } else {
                            tr.fadeIn(600);
                        }
                    });
                    if ((rodzaj && obiekt_radio[0].checked) ||
                        (!rodzaj && !obiekt_radio[0].checked)) {
                        tr.css('display', 'none');
                    }
                }
            });
        }
    };

    var ustaw_zaleznosc = function(pole_wlacznika, pola_zalezne, rodzaj) {
        obiekt_wlacznika = $("#id_" + pole_wlacznika);
        $.each(pola_zalezne, function(index, pole) {
            var obiekt_pola = $('#id_' + pole);
            if (obiekt_pola.attr('type') != 'hidden') {
                var tr = obiekt_pola.closest("tr");
                obiekt_wlacznika.change(function() {
                    tr.fadeToggle(600);
                });
                if ((!rodzaj && obiekt_wlacznika[0].checked) ||
                            (rodzaj && !obiekt_wlacznika[0].checked)) {
                    tr.css('display', 'none');
                }
            }
        });
    };

    var pokaz_lub_schowaj_pola_od_autoryzacyj = function(fade) {
        var termin_id = $('#id_termin')[0].value;
        fade_object = $('#id_chce_autoryzacje_0').closest('tr');
        termin_object = $('#id_termin').closest('tr');
        if (terminy_obiady[termin_id].ma_autoryzacje && cena_autoryzacji !== 0 ) {
            fade_object.fadeIn(fade ? 400 : 0);

        }
        else if (terminy_obiady[termin_id].ma_autoryzacje && cena_autoryzacji !== 0) {
            $('#id_chce_autoryzacje_0').attr('checked', true);
            $('#id_chce_autoryzacje_1').attr('checked', false);
            fade_object.fadeOut(fade ? 400 : 0);
            $('#id_autoryzacja0zl').remove();
            termin_object.after('<tr id="id_autoryzacja0zl"><td></td><td><small>' +
                krotka_nazwa_autoryzacji + '</small></td></tr>');
        }
        else {
            $('#id_autoryzacja0zl').remove();
            if ($('#id_chce_autoryzacje_0').is(':checked')) {
                if (cena_autoryzacji !== 0) {
                    alert(gettext('Dla wybranego terminu szkolenia autoryzacja nie jest dostępna. Pole „zamawiam autoryzację” zostanie odznaczone.'));
                }
            }
            $('#id_chce_autoryzacje_0').attr('checked', false);
            $('#id_chce_autoryzacje_1').attr('checked', false);
            fade_object.fadeOut(fade ? 400 : 0);

        }
    };

    var pola_faktury = [
                'faktura_firma',
                'faktura_adres',
                'faktura_miejscowosc_kod',
                'faktura_nip',
                'faktura_vat_id',
            ];

    var przelaczniki_radio = [
        {
            pole_wlacznika: 'input:radio[name="prywatny"][value="0"]',
            pola_zalezne: pola_faktury.concat(['podmiot_publiczny', 'czy_grupa_0']),
            rodzaj: false
        },
        {
            pole_wlacznika: 'input:radio[name="prywatny"][value="1"]',
            pola_zalezne: pola_faktury.concat(['podmiot_publiczny', 'czy_grupa_0']),
            rodzaj: true
        }
    ];

    $.each(przelaczniki_radio, function(index, przelacznik) {
        zaleznosci_radio(przelacznik.pole_wlacznika, przelacznik.pola_zalezne, przelacznik.rodzaj);
    });

    var instrukcje = [
        {
            pole_wlacznika:"bylem_wczesniej",
            pola_zalezne:['bylem_na'],
            rodzaj:true
        }
    ];
    $.each(instrukcje, function(index, instrukcja) {
        ustaw_zaleznosc(instrukcja.pole_wlacznika, instrukcja.pola_zalezne, instrukcja.rodzaj);
    });

    pola_faktury.forEach(function(pole){
        var element = $("[for='id_" + pole + "']").closest('tr');
        element.attr('class', 'required');
    });

    $('#id_chce_obiady_0').closest('tr').
        after($('<tr><td id="tekst_o_obiadach" colspan="2">...</td></tr>'));
    $('#id_chce_fakture_0').closest('tr').
        after($('<tr><td id="tekst_o_fakturze" colspan="2">...</td></tr>'));
    $('#id_drukowany_certyfikat_0').closest('tr').
        after($('<tr><td id="tekst_o_certyfikacie" colspan="2">...</td></tr>'));

    pokaz_lub_schowaj_pola_od_obiadow(false);
    pokaz_lub_schowaj_pola_od_autoryzacyj(false);
    pokaz_lub_schowaj_pola_od_doplaty_do_faktury(false);
    pokaz_lub_schowaj_pola_od_doplaty_do_certyfikatu(false);

    $('[name=termin]').change(function() {
        pokaz_lub_schowaj_pola_od_obiadow(true);
        pokaz_lub_schowaj_pola_od_autoryzacyj(true);
    });

    var pola_dane_do_faktury = [
        "[name=termin]",
        "[name=chce_autoryzacje]",
        "[name=chce_fakture]",
        "[name=czy_grupa]",
        "[name=uczestnik_wieloosobowy_ilosc_osob]"
    ];
    $(pola_dane_do_faktury.join(", ")).change(pokaz_lub_schowaj_pola_od_doplaty_do_faktury);

    $('[name=drukowany_certyfikat]').change(pokaz_lub_schowaj_pola_od_doplaty_do_certyfikatu);

    var pola_obiady_vegetarianskie = [
        "[name=chce_obiady]",
        "[name=uczestnik_wieloosobowy_ilosc_osob]",
        "[name=prywatny]"
    ];
    $(pola_obiady_vegetarianskie.join(",")).on("change click keyup", pokaz_pole_wegetarianskie);

    ustaw_prywatny_osoba(0);
    zmien_pole_nazwisko_imie(true, 0);
    dodaj_gwiazdke('.required .label label');
    dodaj_gwiazdke('[for=id_akceptuje_regulamin]');

    $('[name=prywatny]').change(function() {
        ustaw_prywatny_osoba(1);
    });
    $('[name=czy_grupa]').change(function() {
        $('#id_uczestnik_wieloosobowy_ilosc_osob').val('');
        zmien_pole_nazwisko_imie(true, 1);
        dodaj_gwiazdke('[for=id_imie_nazwisko]');
    });

    // Jesli wybrano raty i uczestnik prywatny pokaz dodatkowe pola:

    function zarzadzaj_ratami(fade_ms) {
        var platnosc = $("input:radio[name=za_kurs_zaplace]:checked").val();
        var prywatny = $("input:radio[name=prywatny]:checked").val();
        var raty_panstwo_el = $("#id_raty_panstwo");
        var raty_fields = [
            'raty_panstwo',
            'raty_nazwa_dokumentu',
            'raty_numer_dokumentu',
            'raty_pesel'
        ];

        // Ukrywamy opcje rat dla nie-firm
        if (prywatny == 0) {
            $("input:radio[name=za_kurs_zaplace][value='3']").parent().hide();
            $("input:radio[name=za_kurs_zaplace][value='6']").parent().show();
            $("input:radio[name=za_kurs_zaplace][value='10']").parent().hide();
        }
        else if (prywatny == 1) {
            $("input:radio[name=za_kurs_zaplace][value='3']").parent().show();
            $("input:radio[name=za_kurs_zaplace][value='6']").parent().hide();
            $("input:radio[name=za_kurs_zaplace][value='10']").parent().show();
        }

        // Zawsze dodaj na poczatku gwiazdke
        if (fade_ms == 0) {
            $.each(raty_fields, function(index, value){
               $('#id_' + value).closest("tr").attr('class', 'required');
                dodaj_gwiazdke('[for=id_'+ value +']');
            });
        }

        function ukryj_pola_rat(pola, fade_ms, cleanup) {
            $.each(pola, function(index, value){
                var rel = $('#id_' + value);
                if (cleanup) {
                    rel.val("");
                }
                rel.prev("ul.errorlist").remove();
                rel.closest("tr").fadeOut(fade_ms);
            });
        }

        function pokaz_pola_rat(pola, fade_ms) {
            $.each(pola, function(index, value){
                $('#id_' + value).closest("tr").fadeIn(fade_ms);
            });
        }

        if (platnosc != 1 && platnosc != 2 && platnosc != 5) {
            ukryj_pola_rat(['discount_coupon'], 0, true);
        }
        else {
            pokaz_pola_rat(['discount_coupon'], fade_ms);
        }

        if (prywatny != 1 || (platnosc != 3 && platnosc != 10)) {
            ukryj_pola_rat(raty_fields, fade_ms, true)
        }
        else {
            raty_panstwo_el.closest("tr").fadeIn(600);

            if (raty_panstwo_el.val() == 'polska') {
                $('[for=id_raty_numer_dokumentu]').html(gettext("Numer dowodu osobistego"));
                dodaj_gwiazdke('[for=id_raty_numer_dokumentu]');

                pokaz_pola_rat(['raty_numer_dokumentu', 'raty_pesel'], fade_ms);
                ukryj_pola_rat(['raty_nazwa_dokumentu'], 0, true);
            }
            else if (raty_panstwo_el.val() == 'inny') {
                $('[for=id_raty_numer_dokumentu]').html(gettext("Numer dokumentu"));
                dodaj_gwiazdke('[for=id_raty_numer_dokumentu]');

                pokaz_pola_rat(['raty_nazwa_dokumentu', 'raty_numer_dokumentu'], fade_ms);
                ukryj_pola_rat(['raty_pesel'], 0, true);
            }
            else {
                ukryj_pola_rat([
                    'raty_nazwa_dokumentu',
                    'raty_numer_dokumentu',
                    'raty_pesel'
                ], fade_ms, true)
            }
        }
    }

    var discount_coupon = $('#id_discount_coupon');
    if (!discount_coupon.val()) {
        $(document).on('click', 'a#discount_coupon_help_text_link', function (e) {
            e.preventDefault();
            $('span#discount_coupon_help_text').remove();
            $('#id_discount_coupon').show();
        });
        discount_coupon.hide();
        discount_coupon.after('<span id="discount_coupon_help_text">' + $('.tekst_o_kodzie_rabatowym').html() + '</span>');
    }

    $("input:radio[name=za_kurs_zaplace]").on('change', function() {
        zarzadzaj_ratami(600);
    });

    $("#id_raty_panstwo").on('change', function() {
        zarzadzaj_ratami(600);
    });

    $("input:radio[name=prywatny]").on('change', function() {
        zarzadzaj_ratami(600);
    });

    zarzadzaj_ratami(0);
});
