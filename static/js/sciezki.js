function zoom_out(elt) {
	big_text_visible(elt);
	var e = elt.parentNode;
	e.style.transform = e.style.webkitTransform = e.style.MozTransform = e.style.OTransform = e.style.msTransform = 'scale(0.45) translate(0px, 0px)';
}

function zoom_in(element, elt){
	small_text_visible(elt);
	var e = elt.parentNode;
	var m = elt.getSVGDocument().getElementsByTagName('svg')[0].getTransformToElement(element);
	e.style.transform = e.style.webkitTransform = e.style.MozTransform = e.style.OTransform = e.style.msTransform = 'scale(1.0) translate(' + (m.e+160) + 'px, ' + (m.f+30) + 'px)';
}

function small_text_visible(elt){
	class_visible('small', elt)
	class_almost_invisible('big', elt)
}

function big_text_visible(elt){
	class_visible('big', elt)
	class_invisible('link', elt)
	class_almost_invisible('small', elt)
}

function class_visible(className, elt) {
	var texts = elt.getSVGDocument().getElementsByTagNameNS('http://www.w3.org/2000/svg', 'text');
	for (var i = 0; i < texts.length; i++){
		if (!texts[i].getAttribute('class')) continue;
		if (texts[i].getAttribute('class').indexOf(className)!=-1) {
			texts[i].setAttribute('opacity', 1)
			texts[i].style.display = ''
		}
	}
}

function class_almost_invisible(className, elt) {
	var texts = elt.getSVGDocument().getElementsByTagNameNS('http://www.w3.org/2000/svg', 'text');
	for (var i = 0; i < texts.length; i++){
		if (!texts[i].getAttribute('class')) continue;
		if (texts[i].getAttribute('class').indexOf(className)!=-1) {
			texts[i].setAttribute('opacity', 0.05)
		}
	}
}

function class_invisible(className, elt) {
	var texts = elt.getSVGDocument().getElementsByTagNameNS('http://www.w3.org/2000/svg', 'text');
	for (var i = 0; i < texts.length; i++){
		if (!texts[i].getAttribute('class')) continue;
		if (texts[i].getAttribute('class').indexOf(className)!=-1) {
			texts[i].style.display = 'none'
		}
	}
}

function open_svg(element_id, svg_url) {
	var e = document.getElementById(element_id);
	e.parentNode.style.height = '0px';
	var embed = document.createElement('embed');
	embed.onload = function() { init_viewer(embed, element_id) }; // ???
	embed.setAttribute('style', 'width: 10000px; height: 10000px');
//	embed.setAttribute('id', 'svgc');
	embed.setAttribute('src', svg_url);
	e.innerHTML = '';
	e.appendChild(embed);
}

function init_viewer(embed, elt) {	
	var svg_doc = embed.getSVGDocument();
	var layer1 = svg_doc.getElementById('layer1');
	zoom_out(embed);
	var height = parseFloat(svg_doc.getElementsByTagName('svg')[0].getAttribute('height')); // mam nadzieje, ze nie pojawia sie tam zadne "100px", "100in" itd.
	embed.parentNode.parentNode.style.height = (0.45*height) + 'px';
	svg_doc.addEventListener('click', function(){
		zoom_out(embed);
	}, false);
	document.getElementById(elt).parentNode.addEventListener('click', function(){
		zoom_out(embed);
	}, false);
	var rects = layer1.getElementsByTagNameNS('http://www.w3.org/2000/svg', 'rect');
	for (var r = 0; r < rects.length; r++) {
		rects[r].parentNode.addEventListener('click', function(e) {
			zoom_in(this, embed);
			e.stopPropagation();
		}, false);
		rects[r].parentNode.style.cursor = 'pointer';
	}
}

function viewer_open(svg_url, svg_element_id) {
	var svgcapable = false;
	try {
		svgcapable = document.implementation.hasFeature("http://www.w3.org/TR/SVG11/feature#BasicStructure","1.1")
	} catch (e) {}
	var ds = document.createElement('div').style;
	var transform = (ds.transform !== undefined) || (ds.webkitTransform !== undefined) || (ds.MozTransform !== undefined) || (ds.OTransform !== undefined) || (ds.msTransform !== undefined);
	var ua = navigator.userAgent;
	var ios = !!ua.match(/iPad|iPhone|iPod/);
	var safari_windows = !!ua.match(/Safari/) && !!ua.match(/Windows/) && !ua.match(/Chrome/); // lapiemy rowniez wszystko co przedstawia sie jako Safari, a nie jest Chromem
	if (svgcapable && transform && !ios && !safari_windows) {
		open_svg(svg_element_id, svg_url);
	}
}
