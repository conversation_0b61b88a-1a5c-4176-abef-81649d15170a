
$(document).ready(function(){
    $(".change-certificate").submit(function(e) {
        e.preventDefault();
        var that = $(this);

        $.ajax({
             type: "POST",
             url: that.prop('action'),
             data: that.serialize(),
             dataType: "json",
             success: function(data) {
                 var errors = that.find('.errors');
                 if (data.errors.__all__) {
                     errors.text(data.errors.__all__);
                 }
                 else if (data.errors.email) {
                     errors.text(data.errors.email);
                 }
                 else {
                     errors.hide();
                     that.find('input:submit').hide();
                     that.find('.success').show();
                 }
             }
           });
    });
});
