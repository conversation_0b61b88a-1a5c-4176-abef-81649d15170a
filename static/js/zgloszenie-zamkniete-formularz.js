$(function() {

      if ($('p.errors_in_form').length > 0) {
          $('html,body').animate({scrollTop:$('p.errors_in_form').offset().top-20}, 400);
      }

    var dodaj_gwiazdke = function(element) {
        var gw_content = $('p.form-legend').attr('data-gwiazdeczka');
        $(element).append("<span data-gw_content='" + gw_content + "'>*</span>");
    };

    var ustaw_zaleznosc = function(pole_wlacznika, pola_zalezne, rodzaj) {
        obiekt_wlacznika = $("#id_" + pole_wlacznika);
        $.each(pola_zalezne, function(index, pole) {
            var obiekt_pola = $('#id_' + pole);
            if (obiekt_pola.attr('type') != 'hidden') {
                var tr = obiekt_pola.closest("tr");
                obiekt_wlacznika.change(function() {
                    tr.fadeToggle(600);
                });
                if ((!rodzaj && obiekt_wlacznika[0].checked) ||
                            (rodzaj && !obiekt_wlacznika[0].checked)) {
                    tr.css('display', 'none');
                }
            }
        });
    };

    var instrukcje = [
        {
            pole_wlacznika:"bylem_wczesniej",
            pola_zalezne:['bylem_na'],
            rodzaj:true
        }
    ];

    $.each(instrukcje, function(index, instrukcja) {
        ustaw_zaleznosc(instrukcja.pole_wlacznika, instrukcja.pola_zalezne, instrukcja.rodzaj);
    });

    dodaj_gwiazdke('.required .label label');

});
