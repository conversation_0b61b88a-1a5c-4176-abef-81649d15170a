jQuery(function ($) {
    var notifications = {
        url: null,

        init: function () {
            $('[data-action=notifications-modal-run]').click(function (e) {
                e.preventDefault();

                var req_data = {email: ""};

                var that = $(this);
                notifications.url = that.data('url');

                // Dodajemy email do initail
                var initialemail = that.data('initialemail');
                if (initialemail) {
                    var email = $(initialemail).val();
                    if (email) {
                        req_data['email'] = email;
                    }
                }

                $.ajax({
                    url: notifications.url,
                    data: req_data,
                    type: 'get',
                    cache: false,
                    dataType: 'html',
                    success: function (data) {
                        $.modal(data, {
                            position: ["40px",],
                            overlayId: 'simplemodal-overlay',
                            containerId: 'simplemodal-container',
                            dataId: 'simplemodal-data',
                            onShow: notifications.show
                        });
                    }
                });
            });
            $(document).on("click", "#subscription-container a.btn", function(e) {
                $('.simplemodal-close').click();
            })
        },

        show: function (dialog) {
            var h = 1;
            var container_h = $('#subscription-container').height();

            if (container_h) {
                h += container_h;
            }

            dialog.container.animate({
                height: h
            });

            $('#subscription-ajax-form-submit').click(function (e) {
                $('this').prop('disabled', true);
                e.preventDefault();

                var data = $('#subscription-ajax-form').serializeArray();

                $.ajax({
                    url: notifications.url,
                    data: data,
                    type: 'post',
                    cache: false,
                    dataType: 'html',
                    success: function (data) {
                        $('#simplemodal-data').html(data).fadeIn(200);
                        return notifications.show(dialog);
                    },
                    error: notifications.error
                });
            });
        },

        error: function (xhr) {
            $('#simplemodal-data').html("Error ...").fadeIn(200);
        }
    };

    notifications.init();

    if (window.location.href.indexOf("?notifications") >= 0) {
        $('[data-action=notifications-modal-run]').click();
    }
});
