const possibleDiamonds = [
  '/tech/uml/',
  '/tech/oracle/',
  '/tech/project/',
  '/tech/zarzadzanie-projektami-analiza-modelowanie/',
  '/tech/c-linux-kernel/'
]

let diamonds = document.querySelector('.forbes-diamonds');


if (!diamonds) {
  diamonds = document.querySelector('#diamonds');
}

if (possibleDiamonds.includes(location.pathname)) {
  diamonds.classList.remove('hide');

  const certifications = document.querySelector('.lp-newTop-certifications')

  diamonds.style.left = `${certifications.offsetWidth}px`;
  diamonds.style.bottom = `0px`;
}
