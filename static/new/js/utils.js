$(function () {
  $('#lp-search-btn').on('click', function (e) {
    e.preventDefault();
    $('div#site-header div.top-nav').attr("class", "top-nav searchbox-dynamic-height");
  });


  // trenerzy
  $(".more-less").click(function () {
    var that = $(this);
    var coach_id = that.data('coach');

    $(".lp-infoTrener-" + coach_id).slideToggle();
    $("#show-coach-" + coach_id).addClass("lp-hidden");
  });
  $(".lp-closeTrener").click(function () {
    var that = $(this);
    var coach_id = that.data('coach');

    $(".lp-infoTrener-" + coach_id).slideToggle(400, function () {
      $('html,body').animate({ scrollTop: $("#show-coach-" + coach_id).parent().offset().top - 20 }, 600);
    });
    $("#show-coach-" + coach_id).removeClass("lp-hidden");
  });

  var click_on_tab_if_matches = function (anchor) {
    if (/^#.*/.test(anchor)) {
      var item_anchor = anchor.replace(/^#(.*)/, ".tab$1 a");
      if ($(item_anchor).length) {
        //$(document).scrollTop(0);
        $(item_anchor).click();
        $('html,body').animate({ scrollTop: $('div#site-content').offset().top - 50 }, 400);
      } else {
        var parent = $(anchor).parents('div.tabcontent');
        if (parent.length) {
          var tab_anchor = parent.attr('id').replace(/tab-/, "#");
          $('a[href=' + tab_anchor + ']').click();
        }
      }
    }
  };

  $('.append-year').append(" 2002 - " + new Date().getFullYear());

  $('.tabcontent a').on("click", function () {
    var anchor = $(this).attr("href");
    click_on_tab_if_matches(anchor);
  });

  $(window).on("hashchange", function () {
    var anchor = window.location.hash;
    if (anchor === "") {
      $('ul.nav li.tab:first-child a').click();
    } else {
      click_on_tab_if_matches(anchor);
    }
  });

  $('form.form.formularz_zgloszeniowy').on("submit", function () {
    $('#submit_button').hide();
    $('#try_again').hide();
    $('#please_wait').show();
    setTimeout(function () {
      $('#please_wait').hide();
      $('#try_again').show();
      $('#submit_button').show();
    }, 30000);
  });

  if ($("#international_toggle").length > 0) {
    $("#international_site a").attr("href", $('.lang-hidden a').attr("href"));
    $("#international_toggle").on("click", function () {
      $("#international_site").toggle();
      return false;
    });
  }

  $("a[href^='mailto:']").on('click', function () {
    var address = $(this).attr('href').replace(/.*:/, "");

    dataLayer.push({'event': 'email-click', "address": address, "value": CENA, "id": KOD});
    // console.log('mail clicked clicked:', address);
    fbq('track', 'Lead');
  });

  $("a[href^='tel:']").on('click', function () {
    var address = $(this).attr('href').replace(/.*:/, "");

    dataLayer.push({'event': 'tel-click', "address": address,  "value": CENA, "id": KOD});
    // console.log('tel clicked:', address);
    fbq('track', 'Lead');
  });

  $('body').on('copy', function () {
    const emailRegExp = /[a-z\.-_]+@alx\.(pl|training)/i;

    const telRegExp = /(?:\+48\s?)?(?:\d{2}\s)?\d{3}\s\d{3}\s\d{3,4}|\d{2}\s\d{2}\s\d{2}\s\d{3}/g;


    if (emailRegExp.test(window.getSelection().toString())) {
      const address = window.getSelection().toString().
        replace(/[\s\S]*?([a-z\.-]+)@(alx\.pl|alx\.training)[\s\S]*/i, "$1@$2");

      dataLayer.push({'event': 'email-copy', "address": address, "value": CENA, "id": KOD});
      //console.log('email copied:', address, CENA, KOD);
      fbq('track', 'Lead');
    }

    if (telRegExp.test(window.getSelection().toString())) {
      const tel = window.getSelection().toString();

      dataLayer.push({'event': 'tel-copy', "address": tel, "value": CENA, "id": KOD});
      // console.log('tel copied:', tel, CENA, KOD);
      fbq('track', 'Lead');
    }
  });


  var $menu_sections = $('#site-header div.menusection');

  $('#main-nav').on('click', 'a[href^="#"]', function (event) {
    event.preventDefault();
    event.stopPropagation();
    var target_id = this.href.replace(/.*#/, "#");
    $menu_sections.filter(':not(' + target_id + ')').removeClass('displayed').end().filter(target_id).toggleClass('displayed');
  });

  $menu_sections.on('click', function (event) {
    event.stopPropagation();
  });

  $('html').on('click', ':not( div.secondary-nav.displayed, div.secondary-nav.displayed * )', function (event) {
    $menu_sections.filter('div.displayed').removeClass('displayed');
  });

  $("li.tab").click(switch_tab);

  $("#main-nav .menusection a").click(function (event) {
    var url_splitted = $(this).attr("href").split("#");
    if (url_splitted.length > 1) {
      var current_path = window.location.pathname + window.location.search;
      var destination_path = url_splitted[0];
      if (current_path == destination_path) {
        event.preventDefault();
        var anchor = "#" + url_splitted[1];
        var tab_link = $("a[href='" + anchor + "']");
        $(this).closest("div.menusection").prev("a").click(); // Close menu
        tab_link.click();
      }
    }
    return true;
  });

  $('a[data-tabswitch]').bind("click", function () {
    var tab_id = $(this).data('tabswitch');
    $('div#content-header ul.nav li.current').removeClass('current');
    $('div#content-header ul.nav li.tab' + tab_id).addClass('current');
    $('div.tabcontent').hide();
    $('#tab-' + tab_id).show();
  });

  refresh_tabs();

});

function getCookie(name) {
  var cookieValue = null;
  if (document.cookie && document.cookie != '') {
    var cookies = document.cookie.split(';');
    for (var i = 0; i < cookies.length; i++) {
      var cookie = jQuery.trim(cookies[i]);
      // Does this cookie string begin with the name we want?
      if (cookie.substring(0, name.length + 1) == (name + '=')) {
        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
        break;
      }
    }
  }
  return cookieValue;
}



function refresh_tabs(event) {
  if (window.location.hash) {
    $("a[href='" + window.location.hash + "']").parent("li").click();
  }
}

function toggle_menu(section_id) {
  $('.menusection').filter(':not(.' + section_id + ')').hide(0, function () {
    $('.menusection').filter('.' + section_id).toggle();
  });
}

function switch_highlight(highlight_id) {
  $('div#highlights ul.nav li.current').removeClass('current');
  $('div#highlights ul.nav li.highlight' + highlight_id).addClass('current');
  $('div#highlights div.active').removeClass('active');
  $('div#highlights div.highlight' + highlight_id).addClass('active');

}

function switch_tab(event) {
  if (!$(this).hasClass("current")) {
    anchor_id = "#tab-" + $(this).find("a").attr("href").slice(1);

    $('div#content-header ul.nav li.current').removeClass('current');
    $(this).addClass("current");

    $(this).hasClass('js-tab-anchor')
      ? $('div.tabcontent').hide()
      : $('div.tabcontent').html('<div class="loader"></div>');

    $('div.tabcontent' + anchor_id).show();
    var new_location = location.pathname + $(this).children('a').attr('href');


  }
}


$.ajaxSetup({
  beforeSend: function (xhr, settings) {
    if (!(/^http:.*/.test(settings.url) || /^https:.*/.test(settings.url))) {
      xhr.setRequestHeader("X-CSRFToken", getCookie('csrftoken'));
    }
  }
});