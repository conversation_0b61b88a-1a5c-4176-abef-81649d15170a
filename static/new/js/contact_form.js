  function onSubmitContactForm(token) {
    var form_contact = $('#quick-contact form#quick-contact-form');

    var email_phone = form_contact.find('input[name="email_telefon"]');
    var tos = form_contact.find('input[name="tos"]');

    var error_form = false;
    $.each([email_phone, tos], function (index, value) {
      value.parent().removeClass('error');
      if (!value.is(':checkbox') && value.val() === "") {
        value.parent().addClass('error');
        value.focus();
        error_form = true;
      } else if (value.is(':checkbox') && !value.is(':checked')) {
        value.parent().addClass('error');
        value.focus();
        error_form = true;
      }
    });

    if (!/(\d.*){9,}|@.*\./.test(email_phone.val())) {
      email_phone.parent().addClass('error');
      email_phone.focus();
      error_form = true;
    }

    if (error_form) {
      return false;
    }

    form_contact.find('button').attr('disabled', 'disabled');

    $.ajax({
      type: "POST",
      url: "/" + JS_LANGUAGE_CODE + "/post_contact_form/",
      data: form_contact.serialize(),
      success: function (data) {
        $("#quick-contact form#quick-contact-form").hide();
        if($("#histories-contact").length > 0){$("#histories-contact .form-header").hide();}
        $("#quick-contact #qcf-inner-thanks").show();

      },
      error: function (xhr, ajaxOptions, thrownError) {
        $("#quick-contact #qc-captcha-verification-error").show();
        form_contact.find('button').removeAttr('disabled');
      }
    });
    return false;
  }
