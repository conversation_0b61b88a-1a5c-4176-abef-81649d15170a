$(function () {
  if (!window.location.href.match(/\/zgloszenie\//) && location.href.match(/\/tech\/|\/pl\/kontakt|\/en\/contact-us|\/programowanie-javascript|\/android-complete-kurs|\/kurs-ms-excel|\/prog-java-intro|\/nauka-programowania-CNET|\/bootcamp-python|\/kurs-cpp-programowanie|\/szkolenia\/|\/szkolenie|\/pl\/administrator-linuksa|\/pl\/administrator-linux-specjalistyczny|\/pl\/www-grafika\/|\/pl\/kurs-php-sql|\/pl\/zaawansowany-deweloper-webowy|\/pl\/kurs-java-programowanie\/|\/pl\/kurs-analiza-danych|\/en\/courses|\/en\/linux-administrator\/|\/en\/advanced-linux-administrator\/|\/en\/webmaster\/|\/en\/web-php-sql\/|\/en\/advanced-web-developer\/|\/en\/java-programmer\/|\/pl\/kurs-nauka-programowania-dla-dzieci-mlodziezy\/|\/en\/courses\/android-complete\//)) {

    var s = document.createElement('script');
    s.setAttribute('src', 'https://www.google.com/recaptcha/api.js?hl=' + JS_LANGUAGE_CODE);
    s.setAttribute('async', 'true');
    document.body.appendChild(s);

    $(".zadaj-szybkie-pytanie").show();
  }

  if ($(".zadaj-szybkie-pytanie").length > 0) {
    function add_bottom_pos(ppx) {
      var el = $(".zadaj-szybkie-pytanie").find('.lp-rightSlidePanel');
      var h = el.height() + ppx;
      el.css("bottom", "-" + h + "px");
    }

    var zdp = $(".zadaj-szybkie-pytanie");
    var form_contact = zdp.find('#qcf-inner-contact form');
    var form_newsletter = zdp.find('#qcf-inner-newsletter form');

    zdp.find('input[name="email_telefon"]').on("keypress paste", function () {
      zdp.find('input[name="message_body"]').val("0df17e2c975c262d8428b03bfbe63259");
    });

    zdp.find('input[name="sendto"]').val($('p.kontakt a').html());

    form_contact.on("submit", function () {
      var email = form_contact.find('input[type="text"]');
      var message = form_contact.find('textarea[name="tresc"]');
      var captcha = form_contact.find('textarea[name="g-recaptcha-response"]');
      var tos = form_contact.find('input[name="tos"]');
      var usercode = form_contact.find('input[name="usercode"]');
      var error_form = false;

      $.each([message, email, captcha, tos, usercode], function (index, element) {
        element.parent().removeClass('error');

        if (element.prop("name") === "email_telefon" && !element.val()) {
          error_form = true;
        }

        if (element.prop("name") === "tresc" && !element.val()) {
          error_form = true;
        }

        if (element.is(':checkbox') && !element.is(':checked')) {
          error_form = true;
        }

        if (element.prop("name") === "usercode" && !!element.val()) {
          error_form = true;
        }

        if (error_form) {
          element.parent().addClass('error');
          element.focus();
        }
      });

      if (!/(\d.*){9,}|@.*\./.test(email.val())) {
        email.parent().addClass('error');
        email.focus();
        error_form = true;
      }

      if (error_form) {
        add_bottom_pos(-47);
        return false;
      }

      form_contact.find('input[type="submit"]').attr('disabled', 'disabled');
      var msg = form_contact.find('textarea').val();

      // form_contact.find('textarea').val(msg + '\n\nURL: ' + window.location.href);
      $.ajax({
        type: form_contact.attr('method'),
        url: form_contact.attr('action'),
        data: form_contact.serialize(),
        success: function (data) {

          var email_f = form_contact.find('input[name="email_telefon"]').val();

          function extractEmailAndPhone(input) {
              var emailRegex = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/;
              var phoneRegex = /\b\d{9}\b|\b(\d{2}\s?\d{2}\s?\d{2}\s?\d{3})\b|\b(\d{3}\s?\d{3}\s?\d{3})\b/; // dodano format bez spacji

              var email = input.match(emailRegex);
              var phone = input.match(phoneRegex);

              return {
                  email: email ? email[0] : null,
                  phone: phone ? phone[0].replace(/\s+/g, '') : null // usuwamy spacje z numeru telefonu
              };
          }

          var result = extractEmailAndPhone(email_f);


          dataLayer.push({
            'event': 'formularzZDP',
            'userEmail': result.email,
            'userPhone': result.phone,
            'value': CENA,
            'id': KOD,

          });



          dataLayer.push({ gtp: null });
          dataLayer.push({ gbv: null });
          dataLayer.push({
              'event': 'gbv',
              'gbv': {
                  'event_name': 'view_item',
                  'value': CENA,
                  'items': {
                      'id': KOD,
                      'google_business_vertical': 'education'
                      }
                  },


              'gtp': {
                  'edu_pagetype': 'program',
                  'edu_pid': KOD,
                  'edu_totalvalue': CENA,
                  'edu_plocid': 'Warszawa'

                          }
              });




          var re = /@/i;
          if (re.test(email.val())) {
            zdp.find('#qcf-inner-contact').hide();
            zdp.find('#qcf-inner-newsletter').show();
            form_newsletter.find('input[name=email]').val(email.val());
          }
          else {
            zdp.find('#qcf-inner-contact').hide();
            zdp.find('#qcf-inner-thanks').show();
          }
          form_contact.find('input[type="submit"]').removeAttr('disabled');
          form_contact.find('input[type=text]').val("");
          form_contact.find('textarea').val("");
          add_bottom_pos(-47);
        },
        error: function (xhr, ajaxOptions, thrownError) {
          $("#captcha-verification-error").show();
          form_contact.find('input[type="submit"]').removeAttr('disabled');
          add_bottom_pos(-47);
        }
      });
      return false;
    });

    form_newsletter.submit(function () {
      form_newsletter.find('input[type="submit"]').attr('disabled', 'disabled');
      if ($("#radio-newsletter_subscribe-y").is(':checked')) {
        $.ajax({
          type: form_newsletter.attr('method'),
          url: form_newsletter.attr('action'),
          data: form_newsletter.serialize(),
          success: function (data) {
            zdp.find('#qcf-inner-newsletter').hide();
            zdp.find('#qcf-inner-thanks').show();
            add_bottom_pos(-47);
          },
          failure: function (data) {
            zdp.find('#qcf-inner-newsletter').hide();
            zdp.find('#qcf-inner-thanks').show();
            add_bottom_pos(-47);
          }
        });
      }
      else {
        zdp.find('#qcf-inner-newsletter').hide();
        zdp.find('#qcf-inner-thanks').show();
        add_bottom_pos(-47);
      }
      return false;
    });

    $(window).scroll(function () {
      if (!zdp.is(':animated')) {
        if ($(this).scrollTop() > 100) {
          if (zdp.css('top') == "-362px") {
            zdp.animate({ top: "-425px", }, 500);
          }
        }
        else {
          if (zdp.css('top') == "-425px") {
            zdp.animate({ top: "-362px", }, 500);
          }
        }
      }
    });

    zdp.find('.footer').on("click", function () {
      if (zdp.find('div.close').is(':visible')) {
        zdp.animate({ top: "-362px", }, 500);
        zdp.find('div.open').show();
        zdp.find('div.close').hide();
      }
      else {
        zdp.animate({ top: "0px", }, 500);
        if (!zdp.find('.inner').is(':visible')) {
          zdp.find('.inner-thanks').hide();
          zdp.find('.inner-newsletter').hide();
          zdp.find('.inner').show();
        }
        zdp.find('div.open').hide();
        zdp.find('div.close').show();
        zdp.find('input[type=text]').first().focus();
      }
      return false;
    });

    add_bottom_pos(40);

  }

});
