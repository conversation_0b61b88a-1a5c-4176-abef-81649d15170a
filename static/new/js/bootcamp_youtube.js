jQuery(function($) {

	if (typeof $.alx == "undefined") {
		$.alx = {}
	}

	$.alx.youtubeClipShow = function(videoId) {
		if (typeof $.modal == "undefined") {
			console.error("$.modal did not load properly.")
		}

		$.modal("<iframe style='min-height: 550px; width: 99%; display: block; margin: 45px auto; border: none;' src='https://www.youtube.com/embed/"+videoId+"?autoplay=1&showInto=0'></iframe>", {
									'minWidth': '800px',
									'maxWidth': '800px',
									'minHeight': '650px',
									'maxHeight': '650px',
									'autoResize': true,
									'close': true,
									'onShow': function() {
										$('.simplemodal-close').css('cssText', 'top: 10px !important; background: url(/media/close-white.png) no-repeat; right: -7px !important;');
										$('.simplemodal-container').css({
											'background-color': 'transparent',
											'border': 'none'
										});
									},
									'onClose': function() {
										$('.simplemodal-container').css({
											'background-color': '#eee',
											'border': '4px solid #444'
										})

										$('.simplemodal-close').css('cssText', 'top: 42px !important; background: url(../img/close.png) no-repeat; right: 19px !important;');
										$.modal.close()
									}
								})
	}

	if($('.small-tags').outerHeight() > 25){
		$('.small-tags li').css({"border" : "none" , "padding" : "0 10px"});
	}


})