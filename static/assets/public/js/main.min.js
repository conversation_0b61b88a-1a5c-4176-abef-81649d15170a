!function n(s,r,a){function o(t,e){if(!r[t]){if(!s[t]){var i="function"==typeof require&&require;if(!e&&i)return i(t,!0);if(l)return l(t,!0);throw(e=new Error("Cannot find module '"+t+"'")).code="MODULE_NOT_FOUND",e}i=r[t]={exports:{}},s[t][0].call(i.exports,function(e){return o(s[t][1][e]||e)},i,i.exports,n,s,r,a)}return r[t].exports}for(var l="function"==typeof require&&require,e=0;e<a.length;e++)o(a[e]);return o}({1:[function(e,t,i){"use strict";e("./modules/courses-list"),e("./modules/expand-box"),e("./modules/ytShow"),e("./modules/sticky-header"),e("./modules/lightbox"),e("./modules/lazy-loading"),e("./modules/top-progress"),e("./modules/tooltip")},{"./modules/courses-list":2,"./modules/expand-box":3,"./modules/lazy-loading":4,"./modules/lightbox":5,"./modules/sticky-header":6,"./modules/tooltip":7,"./modules/top-progress":8,"./modules/ytShow":9}],2:[function(e,t,i){"use strict";var n={coursesWrapper:document.getElementById("courses-wrapper"),allElements:document.getElementsByClassName("single-course"),allProgramming:document.getElementsByClassName("js-filter-programming"),allAdvance:document.getElementsByClassName("js-filter-advance"),allAnalitics:document.getElementsByClassName("js-filter-analitics"),allSpecial:document.getElementsByClassName("js-filter-special"),allYoung:document.getElementsByClassName("js-filter-young"),allFilters:document.getElementsByClassName("js-filters"),allIterators:document.getElementsByClassName("iterator"),init:function(){0<this.allIterators.length&&(this.addAction(),this.showAll())},showAll:function(){this.showProgramming(),this.showAnalitics(),this.showYoung(),this.showSpecial()},showProgramming:function(){for(var e=0;e<this.allProgramming.length;e++)this.allProgramming[e].classList.remove("hidden");this.showSpecial()},showAnalitics:function(){for(var e=0;e<this.allAnalitics.length;e++)this.allAnalitics[e].classList.remove("hidden");this.showSpecial()},showYoung:function(){for(var e=0;e<this.allYoung.length;e++)this.allYoung[e].classList.remove("hidden");this.showSpecial()},showAdvance:function(){for(var e=0;e<this.allAdvance.length;e++)this.allAdvance[e].classList.remove("hidden");this.showSpecial()},showSpecial:function(){for(var e=0;e<this.allSpecial.length;e++)this.allSpecial[e].classList.remove("hidden")},hideAll:function(){for(var e=0;e<this.allElements.length;e++)this.allElements[e].classList.add("hidden")},clearIterators:function(){for(var e=0;e<this.allIterators.length;e++)this.allIterators[e].innerText=""},clearActiveFilters:function(){for(var e=0;e<this.allFilters.length;e++)this.allFilters[e].classList.remove("active")},addAction:function(){var e=this;this.allIterators[0].innerText="(".concat(this.allElements.length-this.allAdvance.length,")");for(var t=0;t<this.allFilters.length;t++)this.allFilters[t].addEventListener("click",function(){e.hideAll(),e.clearIterators(),e.clearActiveFilters(),this.classList.add("active"),"all"===this.dataset.filters&&(e.showAll(),this.children[0].innerText="(".concat(e.allElements.length-e.allAdvance.length-1,")")),"programming"===this.dataset.filters&&(e.showProgramming(),this.children[0].innerText="(".concat(e.allProgramming.length,")")),"analitics"===this.dataset.filters&&(e.showAnalitics(),this.children[0].innerText="(".concat(e.allAnalitics.length,")")),"young"===this.dataset.filters&&(e.showYoung(),this.children[0].innerText="(".concat(e.allYoung.length,")")),"advance"===this.dataset.filters&&(e.showAdvance(),this.children[0].innerText="(".concat(e.allAdvance.length,")"))})}};window.addEventListener("load",function(){n.init()})},{}],3:[function(e,t,i){"use strict";var n=$(".expand-box"),s=$(".expand-btn"),r=$(n).children(),a=0,o=($.each(r,function(e,t){a+=$(t).height()}),window.location.pathname.includes("/en/")),l=o?"Expand":"Rozwiń",c=o?"Collapse":"Zwiń";$(s).text(l),$(n).height($(r[0]).outerHeight(!0)+$(r[1]).outerHeight(!0)+$(r[2]).outerHeight(!0)),$(s).on("click",function(){$(this).hasClass("open")?($(this).parent().height($(r[0]).outerHeight(!0)+$(r[1]).outerHeight(!0)+$(r[2]).outerHeight(!0)),$(this).text(l),$(this).removeClass("open")):($(this).parent().height(a+28*(r.length-2)-6),$(this).text(c),$(this).addClass("open"))})},{}],4:[function(e,t,i){"use strict";function r(t,e){var i,n=Object.keys(t);return Object.getOwnPropertySymbols&&(i=Object.getOwnPropertySymbols(t),e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,i)),n}function n(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}n((s=o).prototype,[{key:"bindEvents",value:function(){this._lazyLoadAsset=this._lazyLoadAsset.bind(this)}},{key:"init",value:function(){var i=this,t=new IntersectionObserver(function(e,t){e.filter(function(e){return e.isIntersecting}).forEach(function(e){i._lazyLoadAsset(e.target),t.unobserve(e.target)})},this.options);this.resources.forEach(function(e){t.observe(e)})}},{key:"_lazyLoadAsset",value:function(e){var t=e.getAttribute(this.options.selector);t&&(e.src=t)}}]),Object.defineProperty(s,"prototype",{writable:!1});var s,a=o;function o(e,t){if(!(this instanceof o))throw new TypeError("Cannot call a class as a function");this.options=function(n){for(var e=1;e<arguments.length;e++){var s=null!=arguments[e]?arguments[e]:{};e%2?r(Object(s),!0).forEach(function(e){var t=n,i=s[e];e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(s)):r(Object(s)).forEach(function(e){Object.defineProperty(n,e,Object.getOwnPropertyDescriptor(s,e))})}return n}({selector:["data-src"],rootMargin:"0px 0px",threshold:.01},t),this.element=e,this.resources=this.element.querySelectorAll("[data-src]"),this.bindEvents(),this.init()}window.addEventListener("load",function(){new a(document.querySelector("body"))})},{}],5:[function(e,t,i){"use strict";function s(e){var t=this;this.rootElem=e,this.allImages=void 0,this.moreLink=document.createElement("button"),this.leftArrow=document.createElement("button"),this.rightArrow=document.createElement("button"),this.numbersOfImages=document.createElement("div"),this.imgDescription=document.createElement("div"),this.popupWrapper=document.createElement("div"),this.popupInner=document.createElement("div"),this.closePopupWrapper=document.createElement("div"),this.mainImg=document.createElement("div"),this.thumbWrapper=document.createElement("div"),this.currentImg=0,this.init=function(){this.allImages=e.querySelectorAll("img"),this.createThumbs(),this.moreLinkAction()},this.createThumbs=function(){this.thumbWrapper.classList.add("thumb"),this.moreLink.classList.add("more"),this.moreLink.innerText="Więcej zdjęć";var e=this.allImages[0].cloneNode(),t=this.allImages[1].cloneNode(),i=this.allImages[2].cloneNode();this.thumbWrapper.append(e),this.thumbWrapper.append(t),this.thumbWrapper.append(i),e.addEventListener("click",this.createPopup),t.addEventListener("click",this.createPopup),i.addEventListener("click",this.createPopup),this.rootElem.prepend(this.moreLink),this.rootElem.prepend(this.thumbWrapper)},this.moreLinkAction=function(){this.moreLink.addEventListener("click",this.createPopup)},this.closePopup=function(){t.popupWrapper.parentElement.removeChild(t.popupWrapper),t.currentImg=0},this.createPopup=function(){t.closePopupWrapper.classList.add("popup-close"),t.popupWrapper.classList.add("popup-wrapper"),t.popupInner.classList.add("popup-inner"),t.mainImg.classList.add("popup-main-img"),t.numbersOfImages.classList.add("popup-numbers"),t.imgDescription.classList.add("popup-description"),t.closePopupWrapper.innerHTML='<img src="https://www.alx.pl/static/assets/public/img/lightbox/close.png" />',t.closePopupWrapper.addEventListener("click",t.closePopup),t.popupInner.append(t.closePopupWrapper),t.popupInner.append(t.mainImg),t.popupInner.append(t.imgDescription),t.popupInner.append(t.numbersOfImages),t.popupWrapper.append(t.popupInner),t.rootElem.append(t.popupWrapper),t.updateNumbersOfImages(),t.createNavigation()},this.createNavigation=function(){this.leftArrow.classList.add("popup-left","disabled"),this.rightArrow.classList.add("popup-right"),this.leftArrow.innerHTML='<img src="https://www.alx.pl/static/assets/public/img/lightbox/prev.png" />',this.rightArrow.innerHTML='<img src="https://www.alx.pl/static/assets/public/img/lightbox/next.png" />',this.leftArrow.addEventListener("click",this.prevtHandler),this.rightArrow.addEventListener("click",this.nextHandler),this.popupInner.append(this.leftArrow),this.popupInner.append(this.rightArrow)},this.nextHandler=function(){t.currentImg+1<t.allImages.length&&(t.currentImg+=1,t.updateNumbersOfImages(),t.currentImg+1===t.allImages.length&&t.rightArrow.classList.add("disabled"),1<t.currentImg+1&&t.leftArrow.classList.remove("disabled"))},this.prevtHandler=function(){1<t.currentImg+1&&(--t.currentImg,t.updateNumbersOfImages(),t.currentImg+1===1&&t.leftArrow.classList.add("disabled"),t.currentImg+1<t.allImages.length&&t.rightArrow.classList.remove("disabled"))},this.updateMainImg=function(){this.mainImg.innerHTML="";var e=this.allImages[this.currentImg].cloneNode();e.setAttribute("src",this.allImages[this.currentImg].dataset.src),this.mainImg.append(e)},this.updateNumbersOfImages=function(){this.numbersOfImages.innerText="".concat(this.currentImg+1," / ").concat(this.allImages.length),this.updateDescription(),this.updateMainImg()},this.updateDescription=function(){this.imgDescription.innerText="".concat(this.allImages[this.currentImg].getAttribute("alt"))}}window.addEventListener("load",function(){for(var e=document.querySelectorAll(".alx-gallery"),t=[],i=0;i<e.length;i++)t.push(new s(e[i]));for(var n=0;n<t.length;n++)t[n].init()})},{}],6:[function(e,t,i){"use strict";var n=document.querySelector("#lp-stickyTop"),s=null;function r(){var e,t,i=window.pageYOffset;s&&n&&(e=window.matchMedia("(max-width: 1024px)").matches,e=s.offsetTop+(e?-130:10),t=s.classList.contains("fixed-header"),i<e?n.classList.remove("fixed-header"):t||n.classList.add("fixed-header"))}window.addEventListener("load",function(){s=document.querySelector("#article"),window.addEventListener("scroll",r)})},{}],7:[function(e,t,i){"use strict";window.addEventListener("load",function(){document.querySelectorAll(".tooltip-content").forEach(function(e,t){t%2==1&&(e.classList.add("tooltip-content-right"),e.querySelector(".tooltip-triangle").classList.add("tooltip-triangle-right"))})})},{}],8:[function(e,t,i){"use strict";var n={topHeader:document.querySelector(".top-menu"),barLine:document.createElement("div"),init:function(){this.topHeader.insertBefore(this.createBar(),this.topHeader.children[0]),this.getHeightPercentage(),this.checkResize()},checkResize:function(){var t=this;new ResizeObserver(function(e){return t.getHeightPercentage()}).observe(document.body)},getHeightPercentage:function(){var t=document.querySelector("html").scrollHeight,i=this;window.addEventListener("scroll",function(){var e=window.innerHeight*(this.pageYOffset/t),e=(this.pageYOffset+e)/t*100;i.barLine.style.right="".concat(100-e,"%")})},createBar:function(){var e=document.createElement("div");return e.id="progress-bar",this.barLine.id="progress-line",e.append(this.barLine),e}};window.addEventListener("load",function(){n.init()})},{}],9:[function(e,t,i){"use strict";$(".js-yt-play").click(function(e){e.preventDefault(),$.alx.youtubeClipShow($(this).data("videoid"))})},{}]},{},[1]);