{"name": "alx.assets", "private": true, "version": "1.0.0", "devDependencies": {"@babel/core": "^7.9.0", "@babel/preset-env": "^7.9.5", "autoprefixer": "^9.7.6", "babel-core": "^6.26.3", "babel-polyfill": "^6.26.0", "babel-preset-env": "^1.7.0", "babel-preset-es2015": "^6.24.1", "babelify": "^10.0.0", "browserify": "^16.5.1", "cssnano": "^4.1.10", "gulp": "^4.0.2", "gulp-babel": "^8.0.0", "gulp-dart-sass": "^1.0.2", "gulp-imagemin": "^7.1.0", "gulp-import-css": "^0.1.3", "gulp-plumber": "^1.2.1", "gulp-postcss": "^8.0.0", "gulp-rename": "^2.0.0", "gulp-sourcemaps": "^2.6.5", "gulp-uglify": "^3.0.2", "minimist": "^1.2.8", "sass-module-importer": "^1.4.0", "vinyl-buffer": "^1.0.1", "vinyl-source-stream": "^2.0.0"}, "scripts": {"build": "gulp build", "watch": "gulp watchFiles"}, "engines": {"node": "<=14.0.0"}, "dependencies": {}}