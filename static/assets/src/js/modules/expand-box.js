const container = $('.expand-box');
const button = $('.expand-btn');
const containerChildren = $(container).children();
let fullHeight = 0;
$.each(containerChildren, function (i, val) {
    fullHeight += $(val).height();
})
const isEn = window.location.pathname.includes('/en/');
const expandText = isEn ? 'Expand' : 'Rozwiń';
const collapseText = isEn ? 'Collapse' : 'Zwiń';

$(button).text(expandText);

$(container).height($(containerChildren[0]).outerHeight(true) + $(containerChildren[1]).outerHeight(true) + $(containerChildren[2]).outerHeight(true))
$(button).on('click', function () {
    if ($(this).hasClass('open')) {
        $(this).parent().height($(containerChildren[0]).outerHeight(true) + $(containerChildren[1]).outerHeight(true) + $(containerChildren[2]).outerHeight(true));
        $(this).text(expandText);
        $(this).removeClass('open')
    } else {
        $(this).parent().height(fullHeight + (containerChildren.length - 2) * 28 - 6);
        $(this).text(collapseText);
        $(this).addClass('open')
    }
})