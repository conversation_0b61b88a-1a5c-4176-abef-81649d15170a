window.addEventListener('load', () => {
  const tooltips = document.querySelectorAll('.tooltip-content');

  tooltips.forEach((tooltip, index) => {
    // Co drugi tooltip otrzyma klase, dzieki ktorej w wersji mobile bedzie sie wyswietlal po lewej stronie.
    if(index % 2 === 1) {
      tooltip.classList.add('tooltip-content-right');
      tooltip.querySelector('.tooltip-triangle').classList.add('tooltip-triangle-right')
    }
  })
})
