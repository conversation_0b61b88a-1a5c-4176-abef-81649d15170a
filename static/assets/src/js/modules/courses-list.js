const coursesList = {

    coursesWrapper: document.getElementById('courses-wrapper'),
    allElements: document.getElementsByClassName('single-course'),
    allProgramming: document.getElementsByClassName('js-filter-programming'),
    allAdvance: document.getElementsByClassName('js-filter-advance'),
    allAnalitics: document.getElementsByClassName('js-filter-analitics'),
    allSpecial: document.getElementsByClassName('js-filter-special'),
    allYoung: document.getElementsByClassName('js-filter-young'),
    allFilters: document.getElementsByClassName('js-filters'),
    allIterators: document.getElementsByClassName('iterator'),
    init: function () {
        if(this.allIterators.length > 0) {
            this.addAction();
            this.showAll();
        }
    },
    showAll: function () {
        this.showProgramming();
        this.showAnalitics();
        this.showYoung();
        this.showSpecial();
    },
    showProgramming: function () {
        for (let i = 0; i < this.allProgramming.length; i++) {
            this.allProgramming[i].classList.remove('hidden');
        }
        this.showSpecial();
    },
    showAnalitics: function () {
        for (let i = 0; i < this.allAnalitics.length; i++) {
            this.allAnalitics[i].classList.remove('hidden');
        }
        this.showSpecial();
    },
    showYoung: function () {
        for (let i = 0; i < this.allYoung.length; i++) {
            this.allYoung[i].classList.remove('hidden');
        }
        this.showSpecial();
    },
    showAdvance: function () {
        for (let i = 0; i < this.allAdvance.length; i++) {
            this.allAdvance[i].classList.remove('hidden');
        }
        this.showSpecial();
    },
    showSpecial: function () {
        for (let i = 0; i < this.allSpecial.length; i++) {
            this.allSpecial[i].classList.remove('hidden');
        }
    },
    hideAll: function () {
        for (let i = 0; i < this.allElements.length; i++) {
            this.allElements[i].classList.add('hidden');
        }
    },
    clearIterators: function () {
        for (let i = 0; i < this.allIterators.length; i++) {
            this.allIterators[i].innerText = '';
        }
    },
    clearActiveFilters: function () {
        for (let i = 0; i < this.allFilters.length; i++) {
            this.allFilters[i].classList.remove('active');
        }
    },
    addAction: function () {
        const _self = this;
        this.allIterators[0].innerText = `(${this.allElements.length - this.allAdvance.length})`;
        for (let i = 0; i < this.allFilters.length; i++) {
            this.allFilters[i].addEventListener('click', function () {
                _self.hideAll();
                _self.clearIterators();
                _self.clearActiveFilters();
                this.classList.add('active');
                if(this.dataset.filters === 'all'){
                    _self.showAll();
                    this.children[0].innerText = `(${_self.allElements.length - _self.allAdvance.length - 1})`;
                }
                if(this.dataset.filters === 'programming'){
                    _self.showProgramming();
                    this.children[0].innerText = `(${_self.allProgramming.length})`
                }
                if(this.dataset.filters === 'analitics'){
                    _self.showAnalitics();
                    this.children[0].innerText = `(${_self.allAnalitics.length})`
                }
                if(this.dataset.filters === 'young'){
                    _self.showYoung();
                    this.children[0].innerText = `(${_self.allYoung.length})`
                }
                if(this.dataset.filters === 'advance'){
                    _self.showAdvance();
                    this.children[0].innerText = `(${_self.allAdvance.length})`
                }
            })
        }
    }

}

window.addEventListener('load', () => {
    coursesList.init();
})
