let headerElement = document.querySelector('#lp-stickyTop');
let articleElement = null;

function handleScroll() {
    const pageOffset = window.pageYOffset;

    if(!articleElement || !headerElement) return;

    const isMobile = window.matchMedia('(max-width: 1024px)').matches;
    const offset = isMobile ? -130 : 10
    const articleOffset = articleElement.offsetTop + offset;
    const hasStickyClassName = articleElement.classList.contains('fixed-header');

    if(pageOffset < articleOffset) {
        headerElement.classList.remove('fixed-header')
        return;
    }

    if(pageOffset < articleOffset) return;

    if(!hasStickyClassName) {
        headerElement.classList.add('fixed-header')
        return;
    }
}

window.addEventListener('load', () => {
    articleElement = document.querySelector('#article');
    window.addEventListener('scroll', handleScroll)
})