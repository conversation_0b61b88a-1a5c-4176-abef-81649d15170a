const topProgress = {

    topHeader: document.querySelector('.top-menu'),
    barLine: document.createElement('div'),

    init: function() {
        this.topHeader.insertBefore(this.createBar(), this.topHeader.children[0]);
        this.getHeightPercentage();
        this.checkResize();
    },

    checkResize: function() {
        const resizeObserver = new ResizeObserver(entries => 
            this.getHeightPercentage()
        );
        resizeObserver.observe(document.body)
    },

    getHeightPercentage: function() {
        const pageHeight = document.querySelector('html').scrollHeight;
        const t = this;
        window.addEventListener('scroll', function() {
            const windowercent = window.innerHeight * (this.pageYOffset / pageHeight);
            const percent = ((this.pageYOffset + windowercent) / pageHeight) * 100;
            t.barLine.style.right = `${100 - percent}%`;
        });

    },

    createBar: function() {
        
        const bar = document.createElement('div');
        bar.id = 'progress-bar';
        this.barLine.id = 'progress-line';

        bar.append(this.barLine);
        
        return bar;
    }
}

window.addEventListener('load', () => {
    topProgress.init();
})
