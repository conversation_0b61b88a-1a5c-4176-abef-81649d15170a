

const AlxGallery = function (rootElem) {
    this.rootElem = rootElem;
    this.allImages = undefined;
    this.moreLink = document.createElement('button');
    this.leftArrow = document.createElement('button');
    this.rightArrow = document.createElement('button');
    this.numbersOfImages = document.createElement('div');
    this.imgDescription = document.createElement('div');
    this.popupWrapper = document.createElement('div');
    this.popupInner = document.createElement('div');
    this.closePopupWrapper = document.createElement('div');
    this.mainImg = document.createElement('div');
    this.thumbWrapper = document.createElement('div');
    this.currentImg = 0;

    this.init = function () {
        this.allImages = rootElem.querySelectorAll('img');
        this.createThumbs();
        this.moreLinkAction();
    };
    this.createThumbs = function () {
        this.thumbWrapper.classList.add('thumb');
        this.moreLink.classList.add('more');
        this.moreLink.innerText = 'Więcej zdj<PERSON>';
        const img1 = this.allImages[0].cloneNode();
        const img2 = this.allImages[1].cloneNode();
        const img3 = this.allImages[2].cloneNode();

        this.thumbWrapper.append(img1);
        this.thumbWrapper.append(img2);
        this.thumbWrapper.append(img3);

        img1.addEventListener('click', this.createPopup);
        img2.addEventListener('click', this.createPopup);
        img3.addEventListener('click', this.createPopup);

        this.rootElem.prepend(this.moreLink);
        this.rootElem.prepend(this.thumbWrapper);
        
    };
    this.moreLinkAction = function () {
        this.moreLink.addEventListener('click', this.createPopup)
    };
    this.closePopup = () => {
        this.popupWrapper.parentElement.removeChild(this.popupWrapper);
        this.currentImg = 0;
    };
    this.createPopup = () => { 
        
        this.closePopupWrapper.classList.add('popup-close');
        this.popupWrapper.classList.add('popup-wrapper');
        this.popupInner.classList.add('popup-inner');
        this.mainImg.classList.add('popup-main-img');

        this.numbersOfImages.classList.add('popup-numbers');
        this.imgDescription.classList.add('popup-description');

        this.closePopupWrapper.innerHTML = `<img src="https://www.alx.pl/static/assets/public/img/lightbox/close.png" />`;
        this.closePopupWrapper.addEventListener('click', this.closePopup)

        this.popupInner.append(this.closePopupWrapper);
        this.popupInner.append(this.mainImg);
        this.popupInner.append(this.imgDescription);
        this.popupInner.append(this.numbersOfImages);
        this.popupWrapper.append(this.popupInner);
        this.rootElem.append(this.popupWrapper);

        this.updateNumbersOfImages();
        this.createNavigation();
    };
    this.createNavigation = function(){

        this.leftArrow.classList.add('popup-left', 'disabled');
        this.rightArrow.classList.add('popup-right');

        this.leftArrow.innerHTML = `<img src="https://www.alx.pl/static/assets/public/img/lightbox/prev.png" />`;
        this.rightArrow.innerHTML = `<img src="https://www.alx.pl/static/assets/public/img/lightbox/next.png" />`;

        this.leftArrow.addEventListener('click', this.prevtHandler);
        this.rightArrow.addEventListener('click', this.nextHandler);

        this.popupInner.append(this.leftArrow);
        this.popupInner.append(this.rightArrow);
    };
    this.nextHandler = () => {
        if(this.currentImg + 1 < this.allImages.length) {
            this.currentImg += 1; 
            this.updateNumbersOfImages();
            if(this.currentImg + 1 === this.allImages.length) {
                this.rightArrow.classList.add('disabled');
            }
            if(this.currentImg + 1 > 1) {
                this.leftArrow.classList.remove('disabled');
            }
        }
    };
    this.prevtHandler = () => {
        if(this.currentImg + 1 > 1) {
            this.currentImg -= 1; 
            this.updateNumbersOfImages();
            if(this.currentImg + 1 === 1) {
                this.leftArrow.classList.add('disabled');
            }
            if(this.currentImg + 1 < this.allImages.length) {
                this.rightArrow.classList.remove('disabled');
            }
        }
    };
    this.updateMainImg = function(){
        this.mainImg.innerHTML = '';
        let newImg = this.allImages[this.currentImg].cloneNode();
        newImg.setAttribute('src', this.allImages[this.currentImg].dataset.src)
        this.mainImg.append(newImg);
    };
    this.updateNumbersOfImages = function() {
        this.numbersOfImages.innerText = `${this.currentImg + 1} / ${this.allImages.length}`;
        this.updateDescription();
        this.updateMainImg();
    }
    this.updateDescription = function() {
        this.imgDescription.innerText = `${this.allImages[this.currentImg].getAttribute('alt')}`;
    }
}

window.addEventListener('load', () => {
    const galleries = document.querySelectorAll('.alx-gallery');
    const galleryArray = [];
    for (let i = 0; i < galleries.length; i++) {
        galleryArray.push(new AlxGallery(galleries[i]));
    }
    for (let i = 0; i < galleryArray.length; i++) {
        galleryArray[i].init();
    }
})
