# ALX Frontend

Oparty na gulp 4 kombajn do mielenia całego frontu

## Style

* Używamy tylko i wyłącznie Scss
* Entry point: `src/styles/main.scss`

## JS

* Skrypty są kompilowane przez [babel][babel] (transpiler ES6 do ES5)
* Entry point to `src/js/app.js`
* Zewnętrzne biblioteki instalujemy za pomocą polecania `yarn add nazwa_biblioteki --save-dev`
* Bilblioteki możemy importować przy użyciu `import` lub `require` w następujący sposób:

  ```js
  // ES6 syntax:
  import $ from 'jquery';

  // lub:
  let $ = require('jquery');

  // własne moduły importujemy podając relatywną (od pliku app.js) ścieżkę:
  import MyCustomModule from './modules/myCustomModule';
  ```
## Narzędzia

### yarn

### gulp


## Build i instalacja

* Projekt instalujemy za pomocą polecania `yarn install`
* Build system oparty jest o [gulp][gulp]
* Build assetów wykonujemy przez polecenie `gulp build`
* Watch assetów wykonujemy przez polecenie `gulp watchFiles`