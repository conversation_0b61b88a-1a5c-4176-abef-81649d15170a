# ALX Frontend

Oparty na gulp 4 kombajn do mielenia całego frontu

## Style

* Używamy tylko i wyłącznie Scss
* Entry point: `src/styles/main.scss`

## JS

* Skrypty są kompilowane przez [babel][babel] (transpiler ES6 do ES5)
* Entry point to `src/js/app.js`
* Zewnętrzne biblioteki instalujemy za pomocą polecania `yarn add nazwa_biblioteki --save-dev`
* Bilblioteki możemy importować przy użyciu `import` lub `require` w następujący sposób:

  ```js
  // ES6 syntax:
  import $ from 'jquery';

  // lub:
  let $ = require('jquery');

  // własne moduły importujemy podając relatywną (od pliku app.js) ścieżkę:
  import MyCustomModule from './modules/myCustomModule';
  ```
## Narzędzia

### yarn

Yarn to szybki, niezawodny i bezpieczny menedżer pakietów dla JavaScript. W projekcie używamy go do:

* Instalacji zależności: `yarn install`
* Dodawania nowych pakietów: `yarn add nazwa_pakietu`
* Dodawania pakietów deweloperskich: `yarn add nazwa_pakietu --save-dev`
* Usuwania pakietów: `yarn remove nazwa_pakietu`
* Aktualizacji pakietów: `yarn upgrade`

Yarn automatycznie tworzy plik `yarn.lock`, który zapewnia deterministyczne instalacje na różnych środowiskach.

### gulp

Gulp to system budowania (build system) oparty na strumieniach Node.js. W projekcie używamy Gulp 4 do:

* Kompilacji plików SCSS do CSS
* Transpilacji kodu ES6+ do ES5 za pomocą Babel
* Bundlowania modułów JavaScript
* Minifikacji i optymalizacji assetów
* Automatycznego odświeżania przeglądarki podczas developmentu
* Kopiowania i przetwarzania plików statycznych

Główne komendy:
* `gulp build` - buduje wszystkie assety dla produkcji
* `gulp watchFiles` - uruchamia tryb watch dla developmentu


## Build i instalacja

* Projekt instalujemy za pomocą polecania `yarn install`
* Build system oparty jest o [gulp][gulp]
* Build assetów wykonujemy przez polecenie `gulp build`
* Watch assetów wykonujemy przez polecenie `gulp watchFiles`