.title a {
  text-decoration: none;
  color: inherit;
}

h2 {
  font-size: 24px;
  font-weight: 600;
  line-height: 1.5em;
  color: #525247;
}

h4 {
  margin: 10px 0 5px;
  font-size: 1em;
}

a img {
  border: none;
}

input[type="text"],
input[type="password"],
textarea,
select {
  box-sizing: content-box;
  margin: 5px 0.25em 5px 0;
  padding: 3px 3px 3px 5px;
  font: 1em/1.5em "Open Sans", sans-serif;
  vertical-align: middle;
  border: solid 1px;
  border-color: #d5d5d5 #cdcdcd #b9b9b9;
  border-radius: 2px;
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.075);
}

input[type="text"],
input[type="password"] {
  width: 180px;
}

input[type="text"]:focus,
input[type="password"]:focus,
textarea:focus {
  border-color: #202020;
}

input[type="submit"] {
  box-sizing: content-box;
  padding: 0.25em 1em;
  font: 800 16px/1.5em "Opne Sans", sans-serif;
  text-transform: uppercase;
  color: #fff;
  background: url("../img/bg-1.png") 0 0 repeat-x;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0));
  background-color: #3783bd;
  border: solid 1px;
  border-color: #68b6f3 #57a6e3 #3274a7;
  border-radius: 3px;
}

input[type="submit"]:active {
  background: #2b6da0;
  border-color: #3274a7;
  box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.9);
}

input[type="checkbox"] {
  vertical-align: middle;
}

label {
  cursor: pointer;
}

blockquote {
  quotes: "\201E""\201D";
  position: relative;
  font-style: italic;
}

blockquote:before {
  content: open-quote;
  position: absolute;
  right: 100%;
}

blockquote:after {
  content: close-quote;
}

dl {
  margin: 10px 0;
}

dt ~ dt {
  margin-top: 5px;
  padding-top: 5px;
  border-top: #eee solid 1px;
}

dd {
  margin-left: 15px;
}

button:hover,
input[type="submit"]:hover {
  cursor: pointer;
}

ul {
  margin-left: 1.35em;
}

ol {
  margin-left: 2em;
}

.short {
  margin-top: 0;
  margin-bottom: 0;
}

small {
  font-size: 12px;
  line-height: 1.5em;
}

[data-legend] {
  display: inline-block;
  position: relative;
  border-bottom: currentColor dotted 1px;
  cursor: help;
}

[data-legend]:after,
.question-mark {
  content: "?";
  display: inline-block;
  height: 12px;
  width: 12px;
  margin-left: 0.5em;
  font-size: 10px;
  font-weight: 800;
  line-height: 12px;
  text-align: center;
  text-shadow: 0 1px 0 #fff;
  vertical-align: 1px;
  color: #303030;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.1));
  background-color: #fff;
  border: #c0c0c0 solid 1px;
  border-radius: 2px;
}

.question-mark {
  color: black;
  text-decoration: none;
  line-height: 14px;
  font-weight: 700;
  font-size: 12px;
}

h2[data-legend]:after {
  vertical-align: 0.5em;
}

.info-legend[data-legend] {
  border-bottom-width: 0px;
}

.info-legend[data-legend]:after {
  content: "i";
}

[data-legend]:hover:before,
[data-legend]:active:before {
  content: attr(data-legend);
  position: absolute;
  bottom: 100%;
  left: auto;
  z-index: 10;
  right: -80px;
  width: 260px;
  margin: 0 0 10px 0;
  text-shadow: none;
  padding: 20px;
  box-shadow: none;
  background: #fff;
  color: #646464;
  font-size: 12px;
  font-family: Arial, sans-serif;
  line-height: 17px;
  text-align: left;
  border: 0;
  box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, 0.2);
}

.info-legend:hover:before {
  content: attr(data-legend);
}

table {
  border-spacing: 0;
  border-collapse: collapse;
  box-sizing: border-box;
  min-width: 100%;
  vertical-align: top;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0));
  background-color: #fafafa;
  border: #ccc solid 1px;
  border-radius: 2px;
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.05);
}

td,
th {
  padding: 0.5em 1em;
  vertical-align: top;
  border: #dadada 1px;
  border-style: solid dotted;
}

tr:last-child th,
tr:last-child td {
  border-bottom: none;
}

tr:first-child th,
tr:first-child td {
  border-top: none;
}

tr:nth-child(even) {
  background: #eee;
}

#site-header .displayed {
  visibility: visible;
}


/* SITE CONTENT ====================== */

#main-features {
  position: relative;
  list-style-type: none;
  padding: 15px 0;
  margin: 0;
  text-align: center;
  background: url("../img/main-cont-bg.png");
  border-top: #fff solid 1px;
}

#main-features:after {
  content: "";
  position: absolute;
  right: 0;
  left: 0;
  bottom: 0;
  height: 50px;
  background: url("../img/bg-12.png") 0 100% repeat-x;
  background: linear-gradient(rgba(255, 255, 255, 0), rgba(255, 255, 255, 1) 45px);
}

#main-features select {
  display: block;
  margin: 1em auto;
}

#main-features > li {
  display: inline-block;
  position: relative;
  z-index: 1;
  width: 300px;
  margin-right: -0.28em;
  text-align: left;
  vertical-align: top;
  color: #1380a4;
}

#main-features > li:after {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  bottom: 29px;
  z-index: -1;
  height: 30px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.25), 0 -30px 10px rgba(0, 0, 0, 0.2), 0 -60px 10px rgba(0, 0, 0, 0.15),
  0 -90px 10px rgba(0, 0, 0, 0.1);
}

#main-features > li:first-child + li .title {
  color: #58870e;
}

#main-features > li ~ li {
  margin-left: 20px;
}

#main-features .title {
  position: relative;
  z-index: 2;
  margin-bottom: -0.1em;
  font-size: 1.714em;
  font-weight: 400;
  line-height: 1.2em;
  color: #1380a4;
}

#main-features .figure {
  position: relative;
  width: 250px;
  padding: 10px;
  margin: 10px auto -50px;
  text-align: center;
  background: #fff;
}

#main-features .figure:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  background: #fff;
}

#main-features .figure:after {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  height: 30px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3), 0 -30px 10px rgba(0, 0, 0, 0.2), 0 -60px 10px rgba(0, 0, 0, 0.1),
  0 -90px 10px rgba(0, 0, 0, 0.05);
}

#main-features .figure img {
  position: relative;
  z-index: 2;
  max-width: 100%;
  vertical-align: middle;
}

#main-features .body {
  position: relative;
  margin-bottom: 29px;
  padding: 15px;
  font-size: 14px;
  line-height: 1.65em;
  color: #232320;
  background: #fff;
  background: linear-gradient(172deg, rgba(255, 255, 255, 0) 3px, rgba(255, 255, 255, 1) 55px);
  border: #a3d9ed solid;
  border-width: 0 1px 1px 0;
}

#main-features .body ul,
#main-features .body ol {
  list-style-position: inside;
}

#main-features .body:after,
#main-features .body:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 1px;
  background: url("../img/bg-16.png") 0 0 repeat-x;
  background: linear-gradient(rgba(163, 217, 237, 0) 3px, rgba(163, 217, 237, 1) 45px);
}

#main-features .body:after {
  bottom: auto;
  right: 0;
  width: auto;
  height: 1px;
  background: url("../img/bg-15.png") 100% 0 repeat-y;
  background: linear-gradient(to right, rgba(163, 217, 237, 0) 209px, rgba(163, 217, 237, 1) 259px);
}

#main-features > li:first-child + li .body {
  border-color: #a3c868;
}

#main-features > li:first-child + li .body:before {
  background: url("../img/bg-13.png") 0 0 repeat-x;
  background: linear-gradient(rgba(163, 200, 104, 0) 3px, rgba(163, 200, 104, 1) 45px);
}

#main-features > li:first-child + li .body:after {
  background: url("../img/bg-14.png") 100% 0 repeat-y;
  background: linear-gradient(0deg, rgba(163, 200, 104, 0) 209px, rgba(163, 200, 104, 1) 259px);
}

#main-info {
  margin: 15px 0;
  font-size: 28px;
  line-height: 1.5em;
  white-space: nowrap;
  text-align: center;
  color: #024e88;
  background: #eff9fc;
  border: solid #aad5e5;
  border-width: 1px 0;
}

table.kontakt {
  background-color: #f5f5f5;
  border-width: 1px 0;
  border-color: #eaeaea;
  margin: 0 -20px;
}

.kontakt td {
  padding: 0.5em 19px;
  border-color: #eaeaea;
  border-width: 1px 0;
}

.kontakt tr:nth-child(even) {
  background: none;
}

.kontakt strong {
  font-weight: 800;
  color: #024e88;
}

.tel {
  display: table-row;
}

.tel > strong,
.tel > em {
  display: table-cell;
  padding-right: 0.5em;
  padding-top: 0.25em;
  font-style: normal;
}

.kontakt td p {
  margin: 0.25em 0;
}

.kontakt a {
  border: none;
}

.kontakt td h3 {
  margin: 0.25em 0 0;
}

.section .kontakt em {
  display: block;
  padding-bottom: 5px;
  font-size: 12px;
  line-height: 1.2em;
  font-style: normal;
}

#main-info strong {
  display: inline-block;
  margin: 0 0.5em 0 0.25em;
  vertical-align: top;
}

#main-info strong + strong {
  margin-left: 0;
}

#main-info strong em {
  display: block;
  font-size: 12px;
  font-weight: 400;
  font-style: normal;
  line-height: 1.2em;
}

#main-info strong:last-child {
  margin-right: 0;
}

#main-info a {
  border: none;
}

#main-info p {
  padding: 10px 0;
  width: 960px;
  margin: 0 auto;
  background: url("../img/bg-18.png") 50% 0 repeat-y;
  background: linear-gradient(
                  to right,
                  rgba(208, 237, 248, 0),
                  rgba(208, 237, 248, 1) 15%,
                  rgba(208, 237, 248, 1) 85%,
                  rgba(208, 237, 248, 0)
  );
}

.section-title {
  position: relative;
  z-index: 1;
  width: 940px;
  margin: 0 auto -0.4em;
}

.section-title span {
  background: #fff;
}

.section-title:after {
  content: "";
  display: inline-block;
  height: 1.5em;
  width: 250px;
  margin: 0 -250px 0 0;
  vertical-align: bottom;
  background: linear-gradient(to right, rgba(255, 255, 255, 1) 100px, rgba(255, 255, 255, 0));
}

.recomendations {
  position: relative;
  width: 938px;
  margin: 0 auto 30px;
  padding: 25px 0 0;
  background: url("../img/bg-17.png") 0 100% repeat-x;
  background: linear-gradient(to top, rgba(245, 245, 245, 1) 14px, rgba(245, 245, 245, 0) 125px);
  border: #d4d4d4 solid 1px;
}

.recomendations:before {
  content: "";
  position: absolute;
  left: -1px;
  top: 0;
  height: 150px;
  width: 1px;
  background: linear-gradient(rgba(255, 255, 255, 1), rgba(255, 255, 255, 0));
}

.rec,
.rec + .rec + .rec {
  display: inline-block;
  width: 295px;
  padding-left: 159px;
  margin-right: 20px;
  margin-bottom: 25px;
  font-size: 14px;
  line-height: 1.65em;
  vertical-align: top;
}

.rec + .rec,
.rec + .rec + .rec + .rec {
  margin-right: 0;
}

.rec .figure {
  float: left;
  width: 159px;
  margin-left: -159px;
  padding-top: 0.25em;
  text-align: center;
}

.rec .figure img {
  max-width: 95%;
  vertical-align: middle;
}

/* end SITE CONTENT ====================== */

.see-also {
  padding: 0.75em 10px;
  margin: 1.5em -10px;
  font-size: 14px;
  line-height: 1.5em;
  background: linear-gradient(rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0) 75%);
  background-color: #f4f4f4;
  border: #e0e0e0 solid;
  border-width: 1px 0;
}

.see-also .head {
  color: #fafafa;
  background: #666;
}

.see-also ul {
  list-style-type: square;
  margin-left: 1.5em;
}

.article .spis-szkolen {
  margin-left: 0;
  list-style-type: none;
}

.spis-szkolen > li {
  font-size: 2em;
  line-height: 1.5em;
}

.spis-szkolen > li > a {
  display: block;
  padding: 0.25em 0;
  color: #5f5f5f;
}

.spis-szkolen > li ~ li {
  border-top: #c9c9c9 dashed 1px;
}

.spis-szkolen > li > ul {
  counter-reset: chapter;
  margin-left: 0;
  font-weight: 800;
}

.spis-szkolen > li > ul > li {
  display: inline-block;
  position: relative;
  counter-increment: chapter;
  width: 290px;
  margin: 5px 0 1em;
  padding-left: 40px;
  font-size: 18px;
  line-height: 1.5em;
  vertical-align: top;
  background: #fff;
}

.spis-szkolen > li > ul.kursy > li:nth-child(odd) {
  width: 270px;
}

.spis-szkolen > li > ul.kursy > li:nth-child(even) {
  padding-left: 0px;
  width: 350px;
}

.spis-szkolen > li > ul.szkolenia > li:before {
  position: absolute;
  left: 0;
  top: 0;
  font-size: 1.5em;
  line-height: 1.2em;
}

.spis-szkolen > li > ul > li > ul {
  margin-top: 5px;
  margin-bottom: 5px;
}

.spis-szkolen > li > ul > li li {
  list-style-type: circle;
  font-size: 14px;
  line-height: 1.5em;
  font-weight: 400;
}

.price-before-promotion {
  text-decoration: line-through;
}

.promotional-price {
  color: #080;
}

img.language {
  height: 1em;
  width: auto;
}

a.zapisz-sie,
a.przyszle-terminy {
  display: inline-block;
  *display: inline;
  padding: 11px 19px;
  width: 200px;
  font-size: 17.5px;
  border-radius: 4px;
  margin-bottom: 10px;
  *margin-left: 0.3em;
  line-height: 20px;
  color: #ffffff;
  text-align: center;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.5);
  vertical-align: middle;
  cursor: pointer;
  background-color: #fc8f12;
  *background-color: #fc8f12;
  background-image: linear-gradient(to bottom, #fcb212, #fc8f12);
  background-repeat: repeat-x;
  border: 1px solid #cccccc;
  *border: 0;
  border-color: #fc8f12 #fc8f12 #bfbfbf;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  border-bottom-color: #b3b3b3;
  *zoom: 1;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2), 0 1px 2px rgba(0, 0, 0, 0.05);
}

a.zapisz-sie:hover,
a.przyszle-terminy:hover {
  background-color: #fcb212;
  background-image: linear-gradient(to bottom, #fcb212, #fcb212);
}

a.zaproponuj-termin {
  display: inline-block;
  *display: inline;
  padding: 4px 12px;
  width: 214px !important;
  margin-bottom: 0;
  *margin-left: 0.3em;
  font-size: 14px;
  line-height: 20px;
  color: #333333;
  text-align: center;
  text-shadow: 0 1px 1px rgba(255, 255, 255, 0.75);
  vertical-align: middle;
  cursor: pointer;
  background-color: #f5f5f5;
  *background-color: #e6e6e6;
  background-image: linear-gradient(to bottom, #ffffff, #e6e6e6);
  background-repeat: repeat-x;
  border: 1px solid #cccccc;
  *border: 0;
  border-color: #e6e6e6 #e6e6e6 #bfbfbf;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  border-bottom-color: #b3b3b3;
  border-radius: 4px;
  *zoom: 1;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2), 0 1px 2px rgba(0, 0, 0, 0.05);
}

a.zaproponuj-termin:hover {
  background-color: #e3e3e3;
  background-image: linear-gradient(to bottom, #fff, #e3e3e3);
}

a.przyszle-terminy {
  padding: 4px 12px;
  font-size: 14px;
  line-height: 20px;
  width: 214px;
  margin-bottom: 20px;
  margin-left: -10px;
}

ul.actions[data-terminy] {
  min-height: 90px;
  width: 950px;
}

ul.actions[data-terminy="0"] {
  min-height: 20px;
}

ul.actions li.call-to-action-buttons {
  float: right;
  text-align: right;
  clear: left;
  right: 0px;
  position: absolute;
}

ul.actions .na-zyczenie {
  float: right;
  margin-top: -30px;
  color: #ffffff;
  background-color: #fc8f12;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.5);
  background-color: #fc8f12;
  background-image: linear-gradient(to bottom, #fcb212, #fc8f12);
  background-repeat: repeat-x;
}

ul.actions .na-zyczenie:hover {
  background-color: #fcb212;
  background-image: linear-gradient(to bottom, #fcb212, #fcb212);
}

table.course-calendar {
  width: 960px;
}

table.course-calendar td.zapisz-sie {
  padding-right: 0px;
  text-align: right;
}

#meta-content {
  background: url("../img/meta-bg.jpg") 10% 2%;
}

.form-newsletter .field_lower p {
  margin-bottom: 0px;
}

.form-newsletter .field_lower h3 {
  margin-top: 0px;
}

.form-newsletter .field_radio input {
  margin-top: 7px;
  margin-right: 5px;
}

.form-newsletter .field_radio label {
  width: auto;
  line-height: 22px;
}

.form-newsletter h3.ok {
  color: green;
}

.form-error {
  text-align: center;
  color: white;
  background: red;
  padding: 20px;
  font-weight: bold;
  border: 5px solid black;
}

.form-contact .field-with-error {
  background: #f52700;
}

.signup-error {
  color: #a22b0d;
}

.lang-hidden {
  display: none !important;
}


.obiady.hidden {
  display: none;
}

ol.zgloszenie li {
  margin-bottom: 10px;
}

.hidden {
  display: none;
}

.notification-management-form ul {
  list-style-type: none;
  margin-left: 0px;
}

.formularz_zgloszeniowy #id_email {
  width: 180px;
  box-sizing: content-box;
  padding: 3px 3px 3px 5px;
  font: 1em/1.5em "Open Sans", sans-serif;
  vertical-align: middle;
  border: solid 1px;
  border-color: #d5d5d5 #cdcdcd #b9b9b9;
  border-radius: 2px;
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.075);
}


div.form-row {
  display: block;
  clear: both;
  margin-bottom: 20px;
}


.contact-section {
  display: flex;
  justify-content: center; /* Wycentruje elementy poziomo */
  align-items: center; /* Wycentruje elementy pionowo */
  text-align: center; /* Wyrównuje tekst w środku */
}

.the-form {
  display: flex;
  flex-direction: column; /* Ustawia dzieci w kolumnie */
  justify-content: center; /* Wycentruje elementy pionowo */
  align-items: center; /* Wycentruje elementy poziomo */
}

.lp-btn-blue {
  margin-top: 20px; /* Dodatkowa przestrzeń nad przyciskiem */
}

h2.lp-subtitle {
  font-weight: normal;
  color: #fff;
  position: relative;
  //bottom: 10px;

}

h2.lp-subtitle-1 {
  font-weight: normal;
  color: #fff;
  position: relative;
  bottom: 10px;

}

.box-h3 {
  margin-top: 0;
  margin-bottom: 0;
  font-size: 24px;
  font-weight: 600;
  line-height: 1.3em;
  letter-spacing: -1px;
}

.box-h4 {
  margin: 0;
  font-size: 15px;
  font-weight: 400;
  line-height: 1.3em;
  letter-spacing: -.2px;
}

.box-h5 {
  float: left;
  display: block;
  padding: 0;
  width: 70px;
  margin: 0;
  font-size: 13px;
  font-weight: 600;
  text-align: right;
  letter-spacing: -.4px;
}

.aside-box-h3 {
  display: inline-block;
  font-size: 15px;
  font-weight: 600;
  line-height: 1em;
  color: #434343;
  letter-spacing: -.5px;

}


.aside-box-h4 {
  display: block;
  margin: 0 0 2px;
  font-family: Open sans, Arial, sans-serif;
  font-size: 13px;
  font-weight: 600;
  color: #394448;
  letter-spacing: -.4px;
}

.aside-box-cta {
  float: right;
  font-family: Arial, sans-serif;
  font-size: 11px;
  color: #727272;
  text-decoration: none;
}

.lista-trenerow-h3 {
  margin: 2em 0 0;
  font-size: 18px;
  font-weight: 700;
}

.lista-trenerow-h4 {
  color: #7d7d7d;
  font-style: italic;
  font-size: 14px;
  font-weight: 300;
  margin: 10px 0 5px;
}

.zdp-h3 {
  margin: 25px 0 3px;
  font-size: 20px;
  font-weight: 600;
  line-height: 1.1em;
  letter-spacing: -.5px;
}

.footer-h3 {
  font-size: .97em;
  line-height: 1.5em;
  text-transform: uppercase;
  color: #fff;
  margin: 0;
  padding: 0;
}

p.as-h2 {
  color: #2e2e2b;
  font-size: 25pk;
  line-height: 1em;
  letter-spacing: -.3px;
}

p.as-h2 strong{
  font-weight: 600;
}