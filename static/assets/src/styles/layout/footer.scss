#site-footer {
	position: relative;
	clear: both;
	min-width: 960px;
	padding: 30px 0;
	background: url("../img/footer-bg.png") 0 260px;
}

#site-footer:after,
#site-footer:before {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 1px;
	background: rgba(255, 255, 255, 0.8);
}

#site-footer:after {
	height: 260px;
	background: url("../img/footer-bg-2.png") 0 0 repeat-x;
}

.site-map {
	position: relative;
	z-index: 1;
	width: 940px;
	margin: 0 auto;
	font-size: 12px;
	font-weight: 600;
	line-height: 1.5em;
	text-align: center;
	color: #e6e5dc;
}

.site-map > li {
	display: inline-block;
	width: 170px;
	margin-right: -0.25em;
	vertical-align: top;
	text-align: left;
}

.site-map > li ~ li {
	margin-left: 22px;
}

.site-map ul {
	list-style: square outside;
	margin-left: 1em;
}

.site-map > li > ul:first-child {
	margin-top: 1.5em;
}

.site-map ul ul {
	list-style-type: square;
	list-style-position: outside;
	margin-left: 1em;
}

.site-map > li > ul {
	margin-bottom: 0.75em;
}

.site-map a {
	font-weight: 400;
	text-decoration: none;
	color: #e6e5dc;
}

.site-map a:focus,
.site-map a:hover {
	text-decoration: underline;
	color: #fff;
}

.site-map h3 {
	font-size: 1em;
	line-height: 1.5em;
	text-transform: uppercase;
	color: #fff;
}

.additional-footer {
	position: relative;
	z-index: 1;
	margin-top: 1.5em;
	font-size: 12px;
	line-height: 2.333em;
	text-align: right;
	text-shadow: 0 0 0 transparent, 0 2px 3px #000;
	color: #b2b1ae;
	background: linear-gradient(rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0) 70%);
	background-color: #151819;
	border-top: #000 solid 1px;
	border-bottom: rgba(255, 255, 255, 0.3) solid 1px;
}

.additional-footer p {
	float: left;
	position: relative;
	left: 50%;
	max-width: 50%;
	margin-left: -470px;
	white-space: nowrap;
}

.additional-footer p:first-child + p {
	float: none;
	left: -50%;
	max-width: none;
	margin: 0 -470px 0 0;
}

.copy a {
	position: relative;
	z-index: 1;
	margin: 0 5px;
}

.additional-footer a {
	color: #eff9fc;
}