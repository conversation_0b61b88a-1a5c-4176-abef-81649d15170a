$border-color: #999999;
$border-style: solid;
$boder-bottom-radius: 20px;
$error-placeholder: #ffffff;

.zadaj-szybkie-pytanie {
  display: none;
  top: -362px;
  left: 53%;
  position: fixed;
  margin-left: 30px;
  z-index: 1000;
  width: 296px;
  height: 430px;
  h1 {
    text-shadow: 0px 0px 1px rgba(0, 0, 0, 0);
    font-size: 21px;
    margin-bottom: 10px;
  }
  p {
    margin-bottom: 10px;
  }
  ul,
  li {
    list-style-type: none;
    margin: 0px;
    text-align: center;
  }
  input {
    margin-top: 20px;
    &[type="text"] {
      width: 95%;
    }
    &[name="message_body"] {
      display: none;
    }
    &[name="sendto"] {
      display: none;
    }
  }
  textarea {
    margin-top: 15px;
    width: 95%;
    height: 125px;
  }
  .field-with-error {
    background: #f52700;
    ::-webkit-input-placeholder {
      color: $error-placeholder;
    }
    :-moz-placeholder {
      color: $error-placeholder;
    }
    ::-moz-placeholder {
      color: $error-placeholder;
    }
    :-ms-input-placeholder {
      color: $error-placeholder;
    }
  }
  border: {
    width: 0px;
    color: $border-color;
    style: $border-style;
  }
  .inner,
  .inner-thanks,
  .inner-newsletter {
    padding: 15px 25px 0px 25px;
    height: 341px;
    background: {
      image: url("../img/zdp/form-back.png");
    }
  }
  .inner-thanks,
  .inner-newsletter {
    h1 {
      color: green;
    }
  }
  .inner-newsletter {
    display: none;
    h3 {
      margin-bottom: 10px;
    }
    ul,
    li {
      text-align: left;
    }
    li:last-child {
      text-align: center;
    }
  }
  .inner-thanks {
    display: none;
    text-align: center;
    h1 {
      margin-top: 140px;
    }
  }
  .footer {
    cursor: pointer;
    width: 296px;
    height: 108px;
    background-image: url("../img/zdp/form-button.png");
    h1,
    h2 {
      text-align: center;
      margin: 0px;
      line-height: 17px;
      padding-top: 20px;
    }
    h2 {
      padding-top: 10px;
      font-size: 19px;
    }
    div {
      &.close,
      &.open {
        height: 30px;
        width: 50px;
        margin: 5px auto 0px auto;
        display: none;
        background: {
          image: url("../img/zdp/form-arrow-up.png");
          repeat: no-repeat;
          position: center;
        }
      }
      &.open {
        display: block;
        background-image: url("../img/zdp/form-arrow-down.png");
      }
    }
  }
}
