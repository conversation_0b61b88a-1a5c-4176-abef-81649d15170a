#content-header {
	position: relative;
	min-height: auto;
	margin-top: 15px;
	background: #fff;
}

#content-header:before {
	display: none;
}

#content-header .nav {
	float: left;
	width: 710px;
	margin: 4px 30px 0 0;
	padding-top: 0;
	border-bottom: 1px solid #ececec;
}

#content-header .nav li {
	display: inline-block;
	margin-right: 0;
}

#content-header .nav li:first-child ~ li {
	margin-left: 12px;
}

#content-header .nav a {
	display: inline-block;
	padding: 14px 0 12px 0;
	position: relative;
	z-index: 1;
	font-family: Open sans, Arial, sans-serif;
	font-weight: 600;
	font-size: 14px;
	letter-spacing: -0.3px;
	word-spacing: normal;
	line-height: 1em;
	vertical-align: initial;
	color: #000;
}

#content-header .nav a:focus,
#content-header .nav a:hover {
	position: relative;
	top: 1px;
	padding: 14px 0 11px 0;
	margin: 0;
	background: none;
	border: 0;
	color: #b12222;
	border-bottom: 2px solid #b12222;
}

#content-header .current a:focus,
#content-header .current a:hover,
#content-header .current a {
	position: relative;
	top: 1px;
	padding: 14px 0 11px 0;
	margin: 0;
	color: #b12222;
	font-weight: 600;
	background: #fff;
	border: 0;
	border-bottom: 2px solid #b12222;
	box-shadow: none;
}

#content-header .current a:focus {
	outline: none;
}

#content-header .nav a:focus:after,
#content-header .nav a:hover:after,
#content-header .current a:after {
	display: none;
}