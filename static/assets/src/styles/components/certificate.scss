
.lp-certyfikatWrap {
	position: relative;
	z-index: 9;
	max-width: 960px;
	width: 100%;
	margin: -60px auto 50px;
	padding: 0 20px 50px 20px;
	text-align: center;
	background: #fff;
	box-shadow: 0px 0px 80px 0px rgba(0, 0, 0, 0.17);
	box-sizing: border-box;
	@media all and (min-width: 1024px) {
		padding: 0 0 50px 0;
	}
}

.lp-certyfikatStripe {
	position: absolute;
	top: 0;
	left: 45px;
	display: none;
	width: 56px;
	height: 277px;
	background: url("../img/certyfikat-pasek.jpg") no-repeat;
	@media all and (min-width: 1024px){
		display: block;
	}
}

.lp-certyfikatTop {
	padding: 50px 0 0 0;
}

.lp-certyfikatLogo {
	margin: 0 auto;
	position: relative;
	top: -10px;

	@media all and (min-width: 1024px) {
		float: left;
		margin: 0 0 0 135px;
	}
}

.lp-certyfikatShare {
	float: right;
	margin: 0 50px 0 0;
	list-style: none;
}

.lp-certyfikatShare li {
	float: left;
	margin: 0 6px 0 0;
}

.lp-certyfikatShareBtn {
	position: relative;
	font-size: 12px;
	font-weight: 600;
	line-height: 1em;
	text-transform: uppercase;
	letter-spacing: -0.2px;
	border: 1px solid #cfcfcf;
}

.lp-certyfikatShareBtn:hover {
	border: 1px solid #c92424;
}

.lp-certyfikatShareBtn:hover a,
.lp-certyfikatShareBtn a:hover {
	color: #c92424;
}

.lp-certyfikatShareBtn a {
	display: block;
	padding: 13px 13px 12px;
	color: #7d7d7d;
	text-decoration: none;
}

.lp-certyfikatPDF a {
	padding-left: 40px;
}

.lp-certyfikatMail a {
	padding-left: 35px;
}

.lp-certyfikatPDF a:before {
	position: absolute;
	top: 6px;
	left: 9px;
	display: inline-block;
	content: " ";
	width: 23px;
	height: 23px;
	background: url("../img/spriteShare.png") no-repeat;
	background-position: 0 -40px;
}

.lp-certyfikatMail a:before {
	position: absolute;
	top: 12px;
	left: 11px;
	display: inline-block;
	content: " ";
	width: 23px;
	height: 23px;
	background: url("../img/spriteShare.png") no-repeat;
	background-position: -40px -40px;
}

/* udostepnianie link */
.lp-certyfikatLink a {
	padding-left: 35px;
}

.lp-certyfikatLink a:before {
	position: absolute;
	top: 6px;
	left: 8px;
	display: inline-block;
	content: " ";
	width: 23px;
	height: 23px;
	background: url("../img/spriteShare.png") no-repeat;
	background-position: -79px -39px;
}
/* END udostepnianie link */

.lp-certyfikatShare li:last-child {
	margin: 0;
}

.lp-certyfikatSocial {
	display: block;
	width: 38px;
	height: 38px;
	overflow: hidden;
	text-indent: 6000px;
	background: url("../img/spriteShare.png") no-repeat;
}

.lp-certyfikatSocial:hover {
	opacity: 0.8;
}

.lp-certyfikatShareFb {
	background-position: 0 0;
}

.lp-certyfikatShareIn {
	background-position: -40px 0;
}

.lp-certyfikatShareGoogle {
	background-position: -80px 0;
}

.lp-certyfikatHeader {
	margin: 30px 0 0;
	@media all and (min-width: 1024px) {
		margin: 100px 0 0 0;
	}
}

.lp-certyfikatHeader h3 {
	font-family: Times New Roman, serif;
	font-size: 10vw;
	font-weight: normal;
	line-height: 1em;
	letter-spacing: 10px;
	text-transform: uppercase;
	@media all and (min-width: 1024px){
		font-size: 70px;
	}
}

.lp-certyfikatHeader p {
	margin: 0;
	font-size: 15px;
	line-height: 1em;
	color: #a4a4a4;
	font-weight: 400;
	text-transform: uppercase;
}

.lp-certyfikatHeader p strong {
	color: #000;
	font-weight: 700;
}

.lp-certyfikatPerson {
	margin: 70px 0 0;
}

.lp-certyfikatPerson h4,
.lp-certyfikatCourse h4 {
	font-family: Times New Roman, serif;
	font-size: 7vw;
	font-weight: normal;
	line-height: 1em;
	@media all and (min-width: 1024px) {
		font-size: 40px;
	}
}

.lp-certyfikatPerson p,
.lp-certyfikatCourse p {
	font-size: 18px;
	font-weight: 300;
	letter-spacing: -0.3px;
}

.lp-certyfikatCourse {
	margin: 40px 0 0;
}

.lp-certyfikatCourse h4 {
	margin: 0 0 8px 0;
}

.lp-certyfikatCourse h4:before {
	position: relative;
	top: -12px;
	right: 10px;
	content: " ";
	display: inline-block;
	width: 20px;
	height: 1px;
	background: #000;
}

.lp-certyfikatCourse h4:after {
	position: relative;
	top: -12px;
	left: 10px;
	content: " ";
	display: inline-block;
	width: 20px;
	height: 1px;
	background: #000;
}

.lp-certyfikatCourse p strong {
	font-weight: 600;
}

.lp-certyfikatInformation {
	font-size: 16px;
	font-weight: 300;
	line-height: 22px;
	letter-spacing: -0.3px;
	@media all and (min-width: 1024px){
		margin: 100px 55px 0px 55px;
	}
}

.lp-certyfikatInformation strong {
	font-weight: 600;
}

.lp-certyfikatInformation h5 {
	font-size: 23px;
	font-weight: 300;
	background: url("../img/ecertyfikatHeader_bg.jpg") repeat-x;
	background-position: 0 11px;
}

.lp-certyfikatInformation h5 span {
	padding: 0 15px;
	background: #fff;
}

.lp-certyfikatInformation ul {
	list-style-type: none;
	width: 70%;
	margin: 0 auto;
}

.lp-certyfikatInformation li:after {
	display: block;
	margin: 7px 0;
	content: "#";
	font-weight: 600;
	font-size: 20px;
	color: #c92424;
}

.lp-certyfikatInformation li:last-child:after {
	display: none;
}

.lp-certyfikatCoach h6 {
	margin: 30px 0 15px;
	font-size: 20px;
	font-weight: 600;
}

.lp-certyfikatCoachMore {
	clear: both;
	display: block;
	margin: 0px auto 40px auto;
	font-size: 12px;
	font-weight: 600;
	font-family: Open sans, Arial, sans-serif;
	text-transform: uppercase;
	text-decoration: none;
	letter-spacing: 0px;
	color: #c92424;
	border: 0;
	background: 0;
}

.lp-certyfikatCoachMore:focus {
	outline: none;
}

.lp-certyfikatCoachMore span {
	position: relative;
	top: -1px;
}

.lp-certyfikatCoachInfo {
	display: none;
	margin: 0 0 20px 0;
}

.lp-certyfikatCoachAddInfo {
	display: block;
}

.lp-lessTxt {
	display: none;
}

.lp-certyfikatCoachChangeBtn .lp-moreTxt {
	display: none;
}

.lp-certyfikatCoachChangeBtn .lp-lessTxt {
	display: block;
}

.lp-certyfikatSignature {
	width: 100%;
	clear: both;
	margin: 80px 0 0;
	font-size: 14px;
	font-weight: 300;
	letter-spacing: -0.2px;
	text-align: center;
}

.lp-certyfikatSignature h4 {
	display: block;
	margin: 0;
	font-size: 20px;
	font-weight: 300;
	line-height: 1em;
}

.lp-certyfikatSignature-left {
	margin-top: 86px;
	width: 45%;
	float: left;
}

.lp-certyfikatSignature-right {
	width: 45%;
	float: right;
}

.lp-certyfikatInformation.lp-seeProgram {
	margin: 15px 0 0;
}

/* Odbierz certyfikat */
.lp-certyfikatParagraph {
	width: 940px;
	margin: 15px 0;
	padding: 10px 0 0;
	padding-right: 450px;
	margin-left: -470px;
	line-height: 1.4em;
	box-sizing: border-box;
	font-size: 15px;
	letter-spacing: -0.2px;
}

.lp-getCertificated {
	position: relative;
	padding: 30px 280px 30px 30px !important;
	margin: 0 0 50px 0;
	font-weight: 300;
	border: 1px solid #e9e9e9;
	box-sizing: border-box;
	background: url("../img/certyfikatImg.jpg") no-repeat;
	background-position: right 20px center;
}

.lp-getCertificated h4 {
	position: absolute;
	top: -22px;
	left: 20px;
	font-size: 19px;
	font-weight: 600;
	letter-spacing: -0.2px;
	color: #076a8f;
	padding: 0 10px;
	background: #fff;
}

.lp-getCertificated p {
	margin: 0 0 20px;
}

.lp-getCertificated p strong {
	font-weight: 600;
}

.lp-getCertificatedForm {
	padding: 10px 0 0;
}

.lp-getCertificatedForm,
.lp-getCertificatedInput {
	display: block;
	width: 300px;
	margin: 0;
}

.lp-getCertificatedInput input {
	width: 100%;
	margin: 0 0 12px 0;
	padding: 10px 14px;
	box-sizing: border-box;
	font-weight: 400;
	font-size: 13px;
	color: #6d6d6d;
	letter-spacing: -0.2px;
	border: 1px solid #cdcdcd;
	border-radius: 0;
	box-shadow: none;
}

.error-message {
	display: none;
	width: 100%;
	position: relative;
	top: -12px;
	font-size: 12px;
	font-weight: 400;
	color: #b12222;
}

.lp-getCertificatedInput.error input {
	border: 1px solid #b12222;
}

.lp-getCertificatedInput.error .error-message {
	display: block;
}

.lp-getCertificated button {
	float: right;
	padding: 15px 25px;
	font-family: Open sans, Arial;
	font-size: 13px;
	font-weight: 600;
	text-transform: uppercase;
	letter-spacing: -0.1px;
	line-height: 1em;
	color: #fff;
	border: 0;
	background: #b12222;
}

.lp-getCertificated button:hover {
	background: #9d1616;
}

.lp-getCertificatedInput input::-webkit-input-placeholder {
	font-size: 13px;
	letter-spacing: -0.2px;
	color: #6d6d6d;
}

.lp-getCertificatedInput input:-moz-placeholder {
	/* Firefox 18- */
	font-size: 13px;
	letter-spacing: -0.2px;
	color: #6d6d6d;
}

.lp-getCertificatedInput input::-moz-placeholder {
	/* Firefox 19+ */
	font-size: 13px;
	letter-spacing: -0.2px;
	color: #6d6d6d;
}

.lp-getCertificatedInput input:-ms-input-placeholder {
	font-size: 13px;
	letter-spacing: -0.2px;
	color: #6d6d6d;
}

.lp-getCertificatedInput.error input::-webkit-input-placeholder {
	color: #b12222;
}

.lp-getCertificatedInput.error input:-moz-placeholder {
	color: #b12222;
}

.lp-getCertificatedInput.error input::-moz-placeholder {
	color: #b12222;
}

.lp-getCertificatedInput.error input:-ms-input-placeholder {
	color: #b12222;
}