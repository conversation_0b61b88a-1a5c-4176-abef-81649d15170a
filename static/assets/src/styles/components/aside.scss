#aside {
	float: left;
	position: relative;
	left: 50%;
	width: 260px;
	padding: 30px 10px 60px;
	margin-left: 10px;
	font-size: 14px;
	line-height: 1.5em;
}

.section {
	position: relative;
	padding: 10px;
	margin: 0 -10px 20px;
	background: url("../img/code-bg.png") #fff;
	background: linear-gradient(to top, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0) 120px),
		url("../img/code-bg.png") #fff;
}

.section .sec-ctrl {
	margin: 0.5em 0 0;
	text-align: right;
	font-size: 0.9em;
}

.section-no-header {
	padding: 0px;
	margin: 0px 0px 20px -10px;
}

.section:after {
	content: "";
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	z-index: -1;
	height: 20px;
	box-shadow: 0 0 7px rgba(0, 0, 0, 0.14), 0 -20px 7px rgba(0, 0, 0, 0.1), 0 -40px 7px rgba(0, 0, 0, 0.06);
}

.section > * {
	position: relative;
	z-index: 2;
}

.section p {
	margin: 5px 0;
}

.section .title {
	padding: 5px 10px;
	margin: -10px -10px 10px;
	font-size: 1em;
	font-weight: 600;
	color: #fff;
	background: url("../img/aside-title.png") 0 0 repeat-y;
}

.section .gallery-img,
.gallery-img-en {
	display: block;
	margin: 0 auto;
	border: #fff solid 1px;
	border-radius: 2px;
	box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
}

.section .gallery-img {
	max-width: 100%;
}

.gallery-img ~ .gallery-img {
	margin-top: 10px;
}

.tutor {
	overflow: hidden;
}

.tutor .gallery-img {
	float: left;
	margin: 10px 1em 1em 0;
}

.article ol ol {
	list-style-type: lower-latin;
	margin-top: 0;
	margin-bottom: 0;
}

.article ol ol ol {
	list-style-type: lower-roman;
}