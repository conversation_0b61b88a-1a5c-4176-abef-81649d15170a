.program > ol,
.program > ul {
    list-style-type: none;
    counter-reset: program;
    margin-left: 0;
}

.program > ol > li,
.program > ul > li {
    font-weight: 600;
}

.program > ul > li:first-child ~ li,
.program > ol > li:first-child ~ li {
    margin-top: 0.75em;
    padding-top: 0.75em;
    border-top: #d4d4d4 dashed 1px;
}

.program > ul > li:before,
.program > ol > li:before {
    counter-increment: program;
    content: counter(program, upper-roman);
    margin-right: 0.5em;
    font-weight: 800;
    color: #076a8f;
}

.program > ul > li ul,
.program > ol > li ul {
    list-style-type: square;
    margin-left: 1.25em;
    font-size: 14px;
    font-weight: 400;
}

.program > ul > li li:first-letter,
.program > ol > li li:first-letter {
    text-transform: uppercase;
}
