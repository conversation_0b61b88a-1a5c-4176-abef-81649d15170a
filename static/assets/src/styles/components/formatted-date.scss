.formatted-date {
    display: flex;
    flex-direction: column;
    .main-text {
        display: flex;
        span {
            margin: 0 0 0 5px;
        }
    }
    .warning-text {
        color: map-get($color-stack, "red");
        position: relative;
        display: block;
        padding-left: 11px;
        font-size: 11px;
        line-height: 15px;
        margin: 0 0 0 34px;
        &:after {
            position: absolute;
            top: -3px;
            left: 0;
            content: " ";
            display: block;
            float: left;
            width: 0;
            height: 0;
            margin: 7px 3px 0 0;
            border-color: transparent transparent map-get($color-stack, "red");;
            border-style: solid;
            border-width: 0 4px 6px;
        }
    }
}