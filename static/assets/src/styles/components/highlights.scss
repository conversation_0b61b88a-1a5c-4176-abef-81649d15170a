#meta-content {
	position: relative;
	margin: 1px 0 0;
	padding: 15px 0 15px 50%;
	background: url("../img/meta-bg.jpg") 0 2%;
}

#meta-content:before {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: url("../img/meta-light.png") 50% 0 no-repeat;
	background: radial-gradient(
		farthest-side circle at 50% 15%,
		rgba(255, 255, 255, 0.7) 66%,
		rgba(255, 255, 255, 0) 95%
	);
	box-shadow: inset 0 -10px 10px -10px rgba(0, 0, 0, 0.4), inset 0 10px 10px -10px rgba(0, 0, 0, 0.4);
}

#meta-content > h1,
.course-calendar,
#highlights,
.actions {
	position: relative;
	z-index: 2;
}

#meta-content h1 {
	overflow: hidden;
	width: 940px;
	margin-left: -470px;
	font-size: 2.25em;
	font-weight: 300;
	line-height: 1.4em;
	color: #076a8f;
	text-shadow: 0 2px 3px rgba(0, 0, 0, 0.15);
}

#meta-content h1:after {
	content: "";
	display: inline-block;
	height: 1px;
	width: 650px;
	margin: 0 -657px 0 10px;
	background: url("../img/bg-8.png") 0 0 repeat-y;
	background: linear-gradient(to right, rgba(1, 120, 159, 0.46) 42px, rgba(1, 120, 159, 0.05) 615px);
}

.subtitle {
	font-size: 0.583em;
	font-weight: 800;
	color: #076a8f;
}

.actions {
	list-style-type: none;
	overflow: hidden;
	width: 940px;
	margin: 0 0 10px -470px;
}

.actions strong {
	color: #525247;
}

.actions strong + .call-to-action {
	margin-left: 1em;
}

.actions > li {
	float: left;
	min-width: 300px;
}

.actions > .footer {
	float: none;
	clear: left;
	margin-left: 0;
	padding-top: 0.5em;
}

.actions[data-terminy="0"] > li {
	float: none;
}

.actions > li > * {
	width: 300px;
}

.actions > li:first-child > * {
	width: auto;
}

.meta-content .actions .courses-date {
	list-style-type: none;
	width: 420px;
	margin: 0 -5px;
	font-size: 14px;
	line-height: 1.5em;
}

.meta-content .course-calendar {
	border-spacing: 0 5px;
	border-collapse: separate;
	margin-left: -480px;
	text-shadow: 0 2px 2px rgba(0, 0, 0, 0.15);
	background: none;
	border: none;
	-webkit-box-shadow: none;
	-moz-box-shadow: none;
	box-shadow: none;
}

.course-calendar tr {
	background: none;
}

.course-calendar th,
.course-calendar td {
	padding: 0 10px;
	text-align: left;
	border: none;
}

.course-calendar .dates-list {
	padding-top: 0.25em;
	font-size: 12px;
	line-height: 1.5em;
}

.course-calendar th {
	font-weight: 600;
}

.actions[data-terminy="1"] .courses-date,
.actions[data-terminy="2"] .courses-date,
.actions[data-terminy="3"] .courses-date {
	width: auto;
}

.actions > li + li {
	margin-left: 20px;
}

.actions[data-terminy="0"] > li + li {
	margin-left: 0;
}

.actions p,
.actions ol {
	margin: 0;
}

.actions .courses-date > li {
	float: left;
	width: 200px;
	margin: 0 5px;
}

.actions[data-terminy="1"] .courses-date > li,
.actions[data-terminy="2"] .courses-date > li,
.actions[data-terminy="3"] .courses-date > li {
	float: none;
}

#meta-content h1 + * {
	margin-top: 10px;
}

#highlights {
	text-shadow: 0 0 0 transparent, 0 2px 2px rgba(0, 0, 0, 0.15);
	font-size: 16px;
	line-height: 1.5em;
	white-space: nowrap;
	color: #27261f;
}

#highlights .nav {
	display: inline-block;
	width: 270px;
	margin-left: -490px;
	padding: 0 0 0 20px;
	vertical-align: middle;
	white-space: normal;
}

#highlights .nav a {
	display: block;
	padding: 4px 0;
	color: #27261f;
}

#highlights .nav .current a {
	padding-left: 19px;
	margin-left: -20px;
	background: url("../img/bg-9.png") 0 0 repeat-y;
	background: linear-gradient(to right, rgba(1, 120, 159, 0.1), rgba(1, 120, 159, 0) 270px);
	border-left: rgba(1, 120, 159, 0.25) solid 1px;
}

#highlights .nav .current a:focus {
	outline: none;
}

#highlights .nav .current:after,
#highlights .nav .current:before {
	content: "";
	display: block;
	height: 1px;
	width: 320px;
	margin: -1px 0 0 -20px;
	background: url("../img/bg-10.png") 0 0 repeat-y;
	background: linear-gradient(to right, rgba(1, 120, 159, 0.25) 90px, rgba(1, 120, 159, 0) 249px);
}

#highlights .nav .current:after {
	margin-top: 0;
	margin-bottom: -1px;
}

.highlight {
	display: inline-block;
	visibility: hidden;
	position: relative;
	z-index: 2;
	width: 680px;
	margin: 0 -680px 0 -0.28em;
	font-size: 14px;
	line-height: 1.625em;
	vertical-align: middle;
	white-space: normal;
}

.highlight.active {
	visibility: visible;
}

.highlight .title {
	max-width: 80%;
	margin-bottom: 0.25em;
	font-size: 18px;
	font-weight: 700;
	line-height: 1.25em;
}

.highlight p,
.highlight ul {
	margin-top: 10px;
	margin-bottom: 10px;
}

.call-to-action {
	font-weight: 700;
	text-decoration: none;
	color: #f77f00;
}

.call-to-action-download {
	font-weight: normal;
	font-size: 14px;
	word-wrap: normal;
	white-space: normal;
	text-decoration: none;
	color: #000000;
	border: 2px solid silver;
	background-color: #c9ffc4;
	display: block;
	width: 212px;
	padding: 4px 12px;
	margin-left: -10px;
	margin-bottom: 20px;
	border-radius: 10px;
}

.call-to-action-download img {
	float: left;
	display: block;
	padding: 7px 0px;
}

.call-to-action-download p {
	float: left;
	max-width: 140px;
	margin-left: 10px;
	margin-top: 5px;
}

#highlights .nav a:after,
.call-to-action:after {
	content: "\2023";
	display: inline-block;
	margin-left: 0.25em;
	height: 100%;
	font-size: 2ex;
	line-height: 100%;
	vertical-align: baseline;
}

#highlights .nav a:after {
	color: #c0c0c0;
	color: rgba(0, 0, 0, 0.2);
}
