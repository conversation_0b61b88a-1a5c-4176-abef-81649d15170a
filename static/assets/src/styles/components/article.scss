#article {
    float: left;
    position: relative;
    left: 50%;
    padding: 30px 30px 50px 10px;
    margin-left: -480px;

    .tabcontentterminy,
    .tabcontentzapisy,
    .tabcontentna-zamowienie,
    .tabcontentprogram {
        padding: 0 10px;
    }
}

.article {
    width: 670px;

    p,
    ul,
    ol {
        margin-top: 1em;
        margin-bottom: 1em;
    }

    ul {
        list-style-type: square;

        ul {
            list-style-type: circle;
            margin-top: 0;
            margin-bottom: 0.5em;
        }

    }
}

.article-full {
    width: 950px;
}

.article h2 {
    margin: 0.583em 0;
    font-size: 24px;
}

.article .cse .gsc-control-cse,
.article .gsc-control-cse,
.article .gsc-control-cse,
.article .cse .gsc-control-cse {
    margin-top: 0;
    text-shadow: none;
}

table.gsc-branding,
table.gcsc-branding,
table.gsc-branding td,
table.gcsc-branding td,
.gsc-resultsHeader,
.gsc-resultsHeader td,
.gsc-above-wrapper-area-container td,
.gsc-above-wrapper-area-container {
    background: none;
    border: none;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}

.article .cse .gsc-webResult.gsc-result,
.article .gsc-webResult.gsc-result,
.article .gsc-imageResult-column,
.article .gsc-imageResult-classic,
.article .cse .gsc-webResult.gsc-result:hover,
.article .gsc-webResult.gsc-result:hover,
.article .gsc-imageResult-column:hover,
.article .gsc-imageResult-classic:hover {
    border: none;
}

.gallery {
    padding: 5px 20px;
    text-align: center;
}

.gallery img {
    margin: 5px;
    vertical-align: top;
    border: #fff solid 1px;
    border-radius: 2px;
    box-shadow: 0 2px 3px rgba(0, 0, 0, 0.2);
}

.article-head img {
    box-sizing: border-box;
    float: right;
    max-width: 30%;
    margin: 0 0 1.724em;
    border: #fff solid 2px;
    border-radius: 2px;
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.3);
}

.article-head>ul {
    clear: left;
}

.seealso {
    float: right;
    width: 200px;
    padding: 20px;
    margin: 0 -20px 21px 21px;
    font-size: 13px;
}

.tabcontent> :first-child:not(.bootcamp-header),
.article> :first-child:not(.tabcontent) {
    margin-top: 0;
    padding: 16px 0;

    @media all and (min-width: 1024px) {
        padding: 0;
    }
}

.article .box {
    padding: 0 !important;
}

.tabcontent.tabcontentzamkniete {
    padding: 16px;
}

.article h3 {
    margin: 2em 0 0;
    font-size: 18px;
}

.article .img {
    float: right;
}

.sylwetka_big {
    overflow: hidden;
    position: relative;
    margin: 1.5em 0;
    padding-left: 160px;
    border: #d0d0d0 dotted;
    border-width: 1px 0;
}

.sylwetka_big:before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    width: 139px;
    background: linear-gradient(rgba(255, 255, 255, 0.6), rgba(255, 255, 255, 0));
    background-color: #eee;
    border-right: #d0d0d0 dotted 1px;
}

.sylwetka_big img {
    float: left;
    position: relative;
    z-index: 1;
    margin: 10px 0 10px -137px;
    border: #fff solid 1px;
    border-radius: 2px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.sylwetka_big .imie_nazwisko {
    font-size: 16px;
    font-weight: 800;
    color: #024e88;
}

.article .addresses {
    list-style-type: none;
    margin: 21px -27px;
    font-size: 0;
    word-spacing: -0.25em;
}

.addresses>li {
    display: inline-block;
    width: 260px;
    margin: 0.5em;
    padding: 0.75em 19px;
    font-size: 14px;
    word-spacing: 0;
    border: #c9c9c9 dotted 1px;
    border-radius: 2px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.addresses p,
.addresses h4 {
    margin: 0.25em 0;
}


.components {
    max-width: 100%;
    overflow: hidden;

    @media all and (min-width: 1024px) {
        padding: 0;
    }

    .multi-items_component {
        display: flex;
        flex-wrap: wrap;

        .item {
            width: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 0 0 16px 0;

            img {
                margin: 0 0 12px 0;
            }

            span {
                text-transform: uppercase;
                font-size: 11px;
                padding: 0 16px;
            }
        }
    }

    .numbers_component {
        display: flex;
        margin: 0 0 20px 0;

        .number {
            display: flex;
            flex-direction: column;
            align-items: center;

            >span {
                &:first-child {
                    font-size: 34px;
                    font-weight: 700;
                    line-height: 1;

                    span {
                        font-size: 20px;
                        border-top: 3px solid #da3e3e;
                    }
                }

                &:last-child {
                    font-size: 11px;
                    margin: 5px 0 0 0;
                    padding: 0 10px;
                    text-align: center;
                }
            }
        }
    }

    .bordered-bg-right_component {
        position: relative;
        background-size: cover;
        padding: 15px 0;
        margin: 0 0 24px 0;

        &:before,
        &:after {
            content: '';
            background: url("https://www.alx.pl/media/shadowTopNew.png");
            width: 100%;
            height: 2px;
            display: block;
            background-repeat: no-repeat;
            position: absolute;
            top: 0;
        }

        &:after {
            bottom: 0;
            top: auto;
        }
    }

    .qoute_component {
        p:nth-child(1) {
            background: #F0F0F0;
            padding: 16px 86px 16px 16px;
            font-style: italic;
            border-radius: 4px;
            box-shadow: 0px 3px 3px rgba(35, 44, 71, 0.05), 0px 2px 2px rgba(35, 44, 71, 0.07), 0px 1px 1px rgba(35, 44, 71, 0.1);
            position: relative;

            &::after {
                content: '';
                display: block;
                background: url("https://www.alx.pl/media/quote.png");
                width: 70px;
                height: 70px;
                background-size: contain;
                background-repeat: no-repeat;
                position: absolute;
                right: 0;
                top: 20px;
                z-index: 0;
            }
        }
    }

    .story_component {
        overflow: hidden;
        position: relative;

        &::after {
            content: '';
            width: 1px;
            height: 100%;
            display: inline-block;
            border-left: 1px dashed #c04c4c;
            position: absolute;
            top: 20px;
            left: 10px;
        }

        h2 {
            font-weight: 900;
            padding: 0 0 0 50px;
            margin: 0 0 20px 0;
            position: relative;

            &::before {
                content: '';
                display: inline-block;
                width: 20px;
                height: 20px;
                background-color: #c04c4c;
                border-radius: 20px;
                margin-right: 10px;
                position: absolute;
                top: 10px;
                left: 0px;
            }
        }

        .post {
            box-shadow: 0px 3px 3px rgba(35, 44, 71, 0.05), 0px 2px 2px rgba(35, 44, 71, 0.07), 0px 1px 1px rgba(35, 44, 71, 0.1);
            background: #F0F0F0;
            padding: 16px 16px 16px 76px;
            margin: 0 0 20px 50px;
            border-radius: 4px;
            position: relative;

            >img {
                position: absolute;
                left: -30px;
                z-index: 9;
                top: 10px;
            }

            h4 {
                position: relative;
                margin: 0 0 30px 0;

                span {
                    position: relative;
                }

                img {
                    position: absolute;
                    left: 0;
                    top: 0;
                }
            }
        }
    }

    .video_component {
        box-shadow: 0px 19px 49px 0px rgba(0, 0, 0, 0.12);
        background-color: white;
        padding: 30px;
        display: flex;
        flex-direction: column;
        margin: 0 auto 24px auto;
        width: 90%;
        box-sizing: border-box;

        .content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-direction: column-reverse;

            @media all and (min-width: 1024px) {
                flex-direction: row;

                >* {
                    width: 48%;
                }
            }

            p {
                position: relative;

                img {
                    width: 45px;
                    height: 37px;
                    position: absolute;
                    top: 0;
                    left: -11px;
                }

                i {
                    position: relative;
                }
            }

            &.single {
                img {
                    width: 100%;
                }
            }
        }
    }

    .tabs_component {
        * {
            box-sizing: border-box;
        }

        >input,
        section>div {
            display: none;
        }

        #tab1:checked~section .tab1,
        #tab2:checked~section .tab2 {
            display: block;
        }

        #tab1:checked~nav .tab1,
        #tab2:checked~nav .tab2 {
            background: #b12222;
            color: #fff;

            &:after {
                content: '';
                position: absolute;
                width: 0;
                height: 0;
                border-top: 10px solid #b12222;
                border-right: 10px solid transparent;
                border-bottom: 10px solid transparent;
                border-left: 10px solid transparent;
                bottom: -20px;
                display: block;
                left: 0;
                right: 0;
                margin: auto;
            }
        }

        nav {
            margin: 0 0 24px 0;

            ul {
                display: flex;
                justify-content: space-between;
                margin: 0;

                li {
                    width: 50%;
                    background: #f3f3f3;
                    color: #777777;
                    list-style: none;
                    text-align: center;
                    position: relative;

                    label {
                        width: 100%;
                        height: 100%;
                        padding: 15px 0;
                        display: flex;
                        justify-content: center;
                        flex-direction: column;
                    }
                }
            }
        }
    }

    .recomendation_component {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 0 0 24px 0;

        img {
            display: none;

            @media all and (min-width: 1024px) {
                display: block;
            }
        }

        .content {
            border: 1px solid #e5e5e5;
            box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.1);
            padding: 30px;
            position: relative;

            .triangle {
                position: absolute;
                top: 0;
                right: -13px;
                bottom: 0;
                margin: auto;
            }
        }
    }

    .bulb_component {
        display: flex;
        margin: 0 auto 24px auto;
        align-items: center;
        justify-content: center;

        @media all and (min-width: 1024px) {
            width: 70%;
        }

        a {
            display: flex;
            align-items: center;
        }

        &:hover {
            .content {
                border: 1px solid #da3e3e;
                border-left: none;
            }
        }

        .content {
            border: 1px solid #cacaca;
            border-left: none;
            display: flex;
            flex-direction: column;
            padding: 10px;
            width: 100%;

            @media all and (min-width: 1024px) {
                padding: 10px 30px;
            }

            strong {
                color: #000;
            }

            span {
                font-weight: 600;
                color: #da3e3e;
            }
        }
    }

    .messages_component {
        max-width: 260px;
        margin: 0 auto;
        overflow: hidden;

        >* {
            font-style: italic;
            margin: 12px auto;
        }

        .title {
            display: block;
        }

        .left {
            background: #e6e5eb;
            color: #000;
            border-radius: 4px;
            padding: 5px 10px;
            float: left;
            clear: both;
        }

        .right {
            color: #fff;
            background: #36cc25;
            border-radius: 4px;
            padding: 5px 10px;
            float: right;
            clear: both;
        }
    }

    .source {
        font-size: 11px;
        color: #6b6b6b;
        text-align: right;
    }

    .background-left_component {
        padding: 0;
        background: #f7f7f7;
        margin: 0 0 24px 0;

        @media all and (min-width: 1024px) {
            background-size: cover;
            padding: 24px 20px 24px 40%;
        }

        @media all and (max-width:1024px) {
            background-image: none !important;
        }
    }

    .background-right_component {
        padding: 0;
        background: #f7f7f7;
        margin: 0 0 24px 0;

        @media all and (min-width: 1024px) {
            background-size: cover;
            padding: 24px 40% 24px 20px;
        }

        @media all and (max-width:1024px) {
            background-image: none !important;
        }
    }

    .left-img_right-text_component {
        display: flex;
        align-items: flex-start;
        margin: 0 0 24px 0;
        flex-direction: column;
    }

    .left-img_right-text_component.icons {
        justify-content: space-between;
        align-items: center;
    }

    .left-img_right-text_component.icons.svg img {
        width: 100px;
    }

    .left-img_right-text_component img {
        margin: 0 auto;
        max-width: 250px;
    }

    .left-img_right-text_component.icons img {
        width: 100%;
        margin: 0 auto;
        max-width: 70px;
    }

    .left-img_right-text_component .content {
        margin: 0 0 0 1.6vw;

        @media all and (min-width: 1024px) {
            width: calc(100% - 110px);
        }
    }

    .left-img_right-text_component .content h3 {
        margin: 8px 0 21px 0;
        font-size: 17px;
        line-height: 20px;
        font-weight: 700;
    }

    .left-img_right-text_component .content p {
        margin: 0 0 10px 0;
        line-height: 20px;
        font-size: 14.2px;
    }

    .full-width-text_component {
        margin: 0 0 24px 0;

        * {
            margin: 0 0 24px 0;
        }

        .check {
            color: green;
            font-size: 20px;
        }

        .none {
            color: red;
            font-size: 20px;
        }

        img {
            margin: 0 auto;
        }
    }

    .full-width-text_component p {
        font-size: 14.2px;
    }

    .full-width-text_component h2 {
        font-size: 17.5px;
        color: #000;
    }

    .tooltip-img_component {
        margin: 0 0 24px 0;
    }

    .tooltip-img_component .content {
        padding: 1vw;
        background: #ededed;
        font-size: 13.8px;
        font-style: italic;
        font-weight: 600;
        text-align: center;
        position: relative;
    }

    .tooltip-img_component img {
        width: 100%;
    }

    .tooltip-img_component .content:after {
        content: "";
        display: block;
        background: #ededed;
        width: 0;
        height: 0;
        position: absolute;
        border-top: 20px solid #ededed;
        bottom: -40px;
        right: 2.8vw;
        border-right: 20px solid white;
        border-bottom: 20px solid white;
        border-left: 20px solid #ededed;
    }

    ul.circle-point-list_component {
        list-style: none;
        margin: 0 0 24px 0;
    }

    ul.circle-point-list_component span {
        flex: 1;
    }

    ul.circle-point-list_component li {
        display: flex;
        align-items: center;
        margin: 0 0 10px 0;
    }

    ul.circle-point-list_component li img {
        margin: 0 10px 0 0;
    }

    .right-img_left-text_component {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin: 0 0 24px 0;
    }

    .right-img_left-text_component img {
        max-width: 100%;
    }

    .right-img_left-text_component .content {
        margin: 0 1.6vw 0 0;
        flex: 1;
    }

    .right-img_left-text_component .content h3 {
        margin: 8px 0 21px 0;
        font-size: 17px;
        line-height: 20px;
        font-weight: 700;
    }

    .right-img_left-text_component .content p {
        margin: 0;
        line-height: 20px;
        font-size: 14.2px;
    }

    .left-imgs_right-text_component {
        display: flex;
        margin: 0 0 24px 0;
    }

    .left-imgs_right-text_component .imgs-box {
        display: none;
        margin: 0 1.6vw 0 0;
        min-width: 210px;
    }

    .left-imgs_right-text_component .imgs-box img {
        margin: 0 0 18px 0;
        display: block;
        width: 100%;
    }

    .left-imgs_right-text_component .content {
        padding: 23px 0;
    }

    .left-imgs_right-text_component .content p {
        font-size: 14.2px;
        margin: 0;
        line-height: 20px;
    }

    .left-imgs_right-text_component .content h3 {
        margin: 45px 0 20px 0;
        font-size: 17px;
    }

    .list-with-title_component {
        margin: 0 0 24px 0;
    }

    .list-with-title_component h3 {
        margin: 0 0 16px 0;
        font-size: 17px;
    }

    .full-width-img_component {
        margin: 0 0 24px 0;
    }

    .full-width-img_component img {
        width: 100%;
        display: block;
    }

    ul.red-dot-list_component {
        list-style: none;
    }

    ul.red-dot-list_component li {
        margin: 0 0 10px 0;
        padding: 0 0 0 20px;
        position: relative;

        span.big-font {
            font-size: 20px;
            font-weight: 800;
            color: #076a8f;
            line-height: 1.4;
        }
    }

    ul.red-dot-list_component li:before {
        content: "";
        display: inline-block;
        width: 10px;
        height: 10px;
        border-radius: 100%;
        position: absolute;
        left: 0;
        top: 7px;
        background: #d34444;
    }

    @media all and (min-width: 768px) {
        .left-img_right-text_component {
            flex-direction: row;
        }

        .right-img_left-text_component {
            flex-direction: row;
        }

        .left-imgs_right-text_component .imgs-box {
            display: block;
        }
    }

    .comparison_component {
        margin: 0 0 24px;

        .title {
            font-size: 17.5px;
            margin: 0 0 24px;
            color: #000;
        }

        .table {
            display: flex;

            >div {
                width: 50%;
                display: flex;
                flex-direction: column;

                .header {
                    border: 1px solid #efefef;
                    text-align: center;
                    padding: 16px;

                    strong {
                        display: block;
                        font-size: 25px;
                        margin: 0 0 16px 0;
                    }

                    span {
                        display: block;
                        margin: 0 0 16px 0;
                        font-size: 14.2px;
                        min-height: 85px;
                    }

                    a {
                        color: #b12222;
                        margin: 0 0 16px 0;
                        text-decoration: underline;
                        display: block;

                        &.button {
                            text-decoration: none;
                            border: 2px solid #b12222;
                            padding: 4px 16px;
                        }
                    }
                }

                .content {
                    padding: 16px 5px;

                    .red-dot-list_component {
                        margin-left: 0;
                    }

                    @media all and (min-width: 1024px) {
                        padding: 16px;

                        .red-dot-list_component {
                            margin-left: 1rem;
                        }
                    }

                    h3 {
                        margin: 0;
                    }
                }

                &.left {
                    .content {
                        border-right: 1px solid #efefef;
                    }
                }

                &.right {
                    .content {
                        border-left: 1px solid #efefef;
                    }
                }
            }
        }
    }
}