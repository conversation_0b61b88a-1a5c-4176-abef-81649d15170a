#cookie-overlay {
    display: none;
    &.show {
        display: block;
    }
    &.cookie-v1 {
        text-align: center;
        opacity: 0.8;
        line-height: 12px;
        width: 100%;
        padding: 5px 0px;
        font-size: 11px;
        position: fixed;
        left: 0px;
        bottom: 0px;
        z-index: 1999;
        color: white;
        background-color: rgb(71, 84, 89);
    }

    &.cookie-v2 {
        text-align: center;
        opacity: 0.9;
        line-height: 12px;
        width: 100%;
        padding: 5px 0px;
        font-size: 11px;
        position: fixed;
        left: 0px;
        bottom: 0px;
        z-index: 1000;
        color: black;
        background-color: white;
    }

    &.cookie-v3 {
        opacity: 0.8;
        line-height: 12px;
        width: 300px;
        border-radius: 15px;
        padding: 20px 10px;
        font-size: 10px;
        position: fixed;
        display: block;
        right: 30px;
        bottom: 30px;
        z-index: 1000;
        color: white;
        background-color: black;
    }
    p {
        width: 90%;
        margin: 0 auto;
        font-size: 13px;
        line-height: 20px;
        color: #fcaf12;
    }
    a {
        color: #fcaf12;
        font-weight: bold;
    }
    button {
        background-color: #fcaf12;
        color: white;
        border: none;
        padding-top: 2px;
        padding-bottom: 2px;
        margin: 1px;
    }
}
