.form {
	margin: 0 -10px;
	background: linear-gradient(rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0));
	background-color: #eee;
	border: #cecece solid;
	border-width: 1px 0;
}

.form .fieldset {
	list-style-type: none;
	min-width: 100%;
	margin: 0;
	vertical-align: top;
	background: none;
	border: none;
	box-shadow: none;
}

.fieldset ul {
	list-style-type: none;
	display: inline-block;
	max-width: 406px;
	margin: 0;
}

.fieldset ul label {
	width: auto;
	font-weight: 400;
}

.form select,
.form input,
.form textarea {
	margin: 0;
}

.form textarea {
	width: 396px;
	height: 9em;
}

.form select {
	min-width: 180px;
	width: calc(100% - 20px);
	max-width: 450px;
}

.fieldset > li,
.field {
	padding: 10px;
	margin: 0;
}

.fieldset td:first-child {
	padding: 10px;
}

.fieldset td:last-child {
	padding-right: 10px;
}

.fieldset tr {
	background: none;
}

.fieldset td {
	display: table-cell;
	padding: 10px 0;
	vertical-align: top;
	border: none;
}

label,
.label {
	display: inline-block;
	width: 190px;
	padding: 3px 10px 3px 0;
	vertical-align: top;
}

.label label {
	display: inline;
	padding: 0;
	margin-right: 0.25em;
	vertical-align: baseline;
}

.fieldset small {
	display: block;
}

.required [data-gw_content] {
	display: inline;
	margin: 0 0.25em;
	position: relative;
	cursor: pointer;
}
.required .label {
	display: inline-block;
}
.required [data-gw_content]:hover:after,
.required [data-gw_content]:active:after {
	content: attr(data-gw_content);
	position: absolute;
	bottom: 100%;
	left: 0;
	z-index: 10;
	width: 100px;
	padding: 3px 3px;
	margin-top: -4px;
	text-align: center;
	font-size: 12px;
	font-weight: 600;
	white-space: nowrap;
	background: #fff;
	border: #909090 dotted 1px;
	border-radius: 2px;
	box-shadow: 0 2px 3px rgba(0, 0, 0, 0.3);
	line-height: 22px;
}

.required .label label {
	font-weight: 600;
}

.field-option {
	display: inline-block;
	display: table-cell;
	vertical-align: bottom;
}

.field-option ~ .field-option {
	padding-left: 0.25em;
}

.field-option .label {
	display: block;
	margin-right: 0;
}

.fieldset > li + li,
.field + .field,
.fieldset tr + tr td {
	border-top: #ddd dotted 1px;
}

.formularz_zgloszeniowy {
	margin: 0 -20px;
}

.formularz_zgloszeniowy .fieldset > li:nth-last-child(2),
.formularz_zgloszeniowy .fieldset > li:nth-last-child(3),
.fieldset .control,
.fieldset .control td {
	background-color: #d3d3d3;
	border: none;
}

.formularz_zgloszeniowy .fieldset > li:nth-last-child(2),
.formularz_zgloszeniowy .fieldset > li:nth-last-child(2) + .control,
.formularz_zgloszeniowy .fieldset > tr:nth-last-child(2) td,
.formularz_zgloszeniowy .fieldset > tr:nth-last-child(2) + .control td,
.fieldset .control + .control,
.fieldset .control + .control td {
	border-top: #fff dotted 1px;
}

.fieldset .control input[type="text"],
.fieldset .control input[type="password"] {
	border-color: #a0a0a0;
}

.fieldset .control a {
	font-weight: 600;
}

.form .form-legend {
	margin: 0;
	padding: 10px;
	color: #fff;
	background: #909090;
}

.fieldset .errorlist {
	list-style-type: none;
	display: block;
	width: 390px;
	margin-bottom: 0.5em;
	text-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
	color: #dc3b00;
}

.errors_in_form {
	padding: 10px;
	margin: 1em -10px;
	font-weight: 800;
	color: #a22b0d;
	background: linear-gradient(rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0));
	background-color: #ffe4df;
	border: #f39e9e solid;
	border-width: 1px 0;
}

.form .form-status {
	padding: 0.25em 1em;
	margin: 0;
	font-size: 16px;
	line-height: 1.5em;
	background: #fff;
	border: steelblue solid 1px;
	border-radius: 3px;
	box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

input[name="captcha"] + img {
	margin-left: 5px;
	vertical-align: top;
	border: #a0a0a0 solid 1px;
	border-radius: 2px;
	box-shadow: 0 1px 2px rgba(0, 0, 0, 0.25);
}

.helptext {
	display: inline-block;
	margin: 3px 0;
	font-size: 0.9em;
	vertical-align: top;
}

input[type="checkbox"] + .helptext {
	width: 300px;
}