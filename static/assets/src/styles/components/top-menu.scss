.top-menu {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: map-get($color-stack, "darkGray");
    padding: 0 3vw;
    flex-direction: row-reverse;
    border-bottom: 1px solid map-get($color-stack, "white");
    @media all and (min-width: 1024px) {
        flex-direction: row;
        padding: 0;
        border: none;
    }
    .desktop {
        display: none;
        @media all and (min-width: 1024px) {
            display: block;
        }
    }
    &--inner {
        width: 100%;
        align-items: center;
        padding: 12px 0;
        display: flex;
        justify-content: space-between;
        @media all and (min-width: 1024px) {
            width: 978px;
            justify-content: flex-start;
        }

        > input {
            display: block;
            width: 100px;
            height: 32px;
            position: absolute;
            top: 9px;
            right: 12px;
            cursor: pointer;
            opacity: 0;
            z-index: 2;
            -webkit-touch-callout: none;
            &:checked ~ .mobile-button span {
                opacity: 1;
                transform: rotate(45deg) translate(-5px, -17px);
                background: map-get($color-stack, "white");
            }
            &:checked ~ .mobile-button span:nth-last-child(3) {
                opacity: 0;
                transform: rotate(0deg) scale(0.2, 0.2);
            }
            &:checked ~ .mobile-button span:nth-last-child(2) {
                transform: rotate(-45deg) translate(-1px, 14px);
            }
            &:checked ~ .top-menu--nav {
                transform: none;
            }
        }
    }
    &--nav {
        box-sizing: border-box;
        list-style: none;
        z-index: 1;
        margin: 0;
        align-items: center;
        -webkit-font-smoothing: antialiased;
        transform-origin: 0% 0%;
        transform: translate(100%, 0);
        transition: transform 0.5s cubic-bezier(0.77, 0.2, 0.05, 1);
        position: absolute;
        background: linear-gradient(
            to bottom,
            map-get($color-stack, "darkGray") 52px,
            map-get($color-stack, "menuBlue") 0
        );
        top: 0;
        left: 0;
        width: 100%;
        height: 100vh;
        padding: 70px 16px 0 16px;
        @media all and (min-width: 1024px) {
            transform: none;
            display: flex;
            padding: 0;
            position: static;
            background: transparent;
            width: calc(100% - 3vw);
            height: auto;
            z-index: 99;
        }
        li {
            list-style: none;
            margin: 0 0 10px 0;
            padding: 0 0 10px 0;
            border-bottom: 1px solid map-get($color-stack, "white");
            @media all and (min-width: 1024px) {
                position: relative;
                margin: 0 2vw 0 0;
                border: none;
                padding: 0;
            }
            &.form {
                border: none;
                background: none;
                @media all and (min-width: 1024px) {
                    display: none;
                }
                form {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                }
                input {
                    &[type="submit"] {
                        background: white;
                        color: #5395e3;
                        border-radius: 4px;
                        font-weight: 600;
                        border: 1px solid #2d363a;
                    }
                    &[type="text"] {
                        background: white;
                        border-radius: 4px;
                        font-size: 14px;
                        outline: none;
                        border: 1px solid #2d363a;
                    }
                }
            }
            ~ .back {
                position: absolute;
                top: 6px;
                z-index: 9999999;
                right: 101px;
                background: #2d363a;
                font-weight: 600;
                padding: 3px 1px;
                display: none;
                pointer-events: none;
                color: white;
                font-size: 56px;
            }
            a {
                color: map-get($color-stack, "white");
                text-decoration: none;
            }
            .expand {
                /* display: none; */
                z-index: 99;
                position: absolute;
                background-color: map-get($color-stack, "menuBlue");
                left: 0;
                overflow: scroll;
                height: 84vh;
                top: 66px;
                /* top: 26px; */
                width: 100%;
                -webkit-font-smoothing: antialiased;
                transform-origin: 0% 0%;
                transform: translate(100%, 0);
                transition: transform 0.5s cubic-bezier(0.77, 0.2, 0.05, 1);
                @media all and (min-width: 1024px) {
                    top: 23px;
                    width: auto;
                    height: auto;
                    overflow: hidden;
                    min-width: 400px;
                    transform: none;
                    display: none;
                    &::before {
                        content: "";
                        background-color: map-get($color-stack, "darkGray");
                        height: 8px;
                        width: 100%;
                        display: block;
                    }
                }
            }
            .main-links {
                font-size: 16px;
                color: map-get($color-stack, "white");
                cursor: pointer;
                transition: 0.3s ease;
                display: flex;
                align-items: center;
                justify-content: space-between;
                @media all and (min-width: 1024px) {
                    display: block;
                }
                &:after {
                    content: "";
                    display: block;
                    background: url("../img/kursy_zbiorcze/kursy_zbiorcze_sprite.png") no-repeat;
                    background-position: -99px -200px;
                    width: 6px;
                    height: 10px;
                }
                @media all and (min-width: 1024px) {
                    padding: 7px 0;
                    display: inline;
                    border-top: 2px solid map-get($color-stack, "darkGray");
                    border-bottom: 2px solid map-get($color-stack, "darkGray");
                    color: map-get($color-stack, "white");
                    &:after {
                        display: none;
                    }
                    &:hover {
                        border-top: 2px solid map-get($color-stack, "white");
                        border-bottom: 2px solid map-get($color-stack, "white");
                        padding: 0;
                    }
                }
            }
            &:hover {
                .expand {
                    transform: none;
                    display: block;
                }
                ~ .back {
                    display: block;
                    @media all and (min-width: 1024px) {
                        display: none;
                    }
                }
            }
            .short-info {
                display: none;
                flex-direction: column;
                color: map-get($color-stack, "white");
                background: map-get($color-stack, "shorInfoBlue");
                padding: 8px 16px;
                font-size: 12px;
                @media all and (min-width: 1024px) {
                    display: flex;
                }
            }
            .main-nav {
                display: flex;
                margin: 0;
                padding: 16px;
                flex-direction: column;
                @media all and (min-width: 1024px) {
                    flex-direction: row;
                }
                li {
                    color: map-get($color-stack, "white");
                    margin: 0 2vw 0 0;
                    display: flex;
                    flex-direction: column;
                    width: 100%;
                    @media all and (min-width: 1024px) {
                        &:last-child {
                            margin: 0;
                            padding: 0 0 0 1vw;
                            border-left: 1px solid;
                        }
                    }
                    &:first-child {
                        border: none;
                        padding: 0;
                    }
                    &:last-child {
                        border: none;
                        padding: 0;
                    }
                    .title {
                        margin-bottom: 10px;
                        opacity: 1;
                        font-size: 14px;
                        border: none;
                        margin: 0 0 10px 0;
                    }
                    > div {
                        display: flex;
                        flex-direction: column;
                        min-width: 150px;
                        margin: 0 0 10px 0;
                    }
                    a {
                        font-size: 13px;
                        border-bottom: 1px solid rgba(255, 255, 255, 0.3);
                        opacity: 0.7;
                        margin: 0 0 5px 0;
                        padding: 0 0 5px 0;
                        transition: 0.3s ease;
                        &:last-child {
                            border: none;
                        }
                        &:hover {
                            opacity: 1;
                        }
                    }
                }
            }
            .special-link {
                padding: 8px 16px;
                display: block;
                border-top: 1px solid rgba(255, 255, 255, 0.3);
                text-align: center;
                border-bottom: 1px solid rgba(255, 255, 255, 0.3);
            }
            .bottom-nav {
                background: map-get($color-stack, "lightBlue");
                margin: 0;
                display: flex;
                justify-content: center;
                padding: 10px 0;
                flex-direction: column;
                @media all and (min-width: 1024px) {
                    flex-direction: row;
                }
                a {
                    opacity: 0.7;
                    transition: 0.3s ease;
                    font-size: 13px;
                    &:hover {
                        opacity: 1;
                    }
                }
                li {
                    padding-left: 10px;
                    @media all and (min-width: 1024px) {
                        padding-left: 0;
                    }
                }
            }
        }
    }
    .logo {
        margin: 0 3vw 0 0;
        width: 96px;
        z-index: 99;
        img {
            display: block;
        }
    }
    .contact {
        border: none;
        &.mobile {
            border: none;
        }
        @media all and (min-width: 1024px) {
            margin: 0 0 0 10px;
            padding: 0 0 0 10px;
            border-left: 2px solid map-get($color-stack, "white");
            &.mobile {
                display: none;
            }
        }
    }
    .lang {
        margin-top: 30px;
    }
    .right-links {
        @media all and (min-width: 1024px) {
            margin: 0 0 0 auto;
            display: flex;
            align-items: center;
        }
    }

    .mobile-button {
        position: relative;
        -webkit-user-select: none;
        user-select: none;
        z-index: 1;
        top: 2px;
        display: flex;
        @media all and (min-width: 1024px) {
            display: none;
        }
        .mobile-text {
            color: map-get($color-stack, "white");
            margin: 0 5px 0 0;
        }
        span.mobile-line {
            display: block;
            width: 30px;
            height: 4px;
            margin-bottom: 5px;
            position: relative;
            background-color: #cdcdcd;
            border-radius: 3px;
            z-index: 1;
            transform-origin: 4px 0px;
            transition: transform 0.5s cubic-bezier(0.77, 0.2, 0.05, 1),
                background-color 0.5s cubic-bezier(0.77, 0.2, 0.05, 1), opacity 0.55s ease;
            &:first-child {
                transform-origin: 0% 0%;
            }
            &:nth-last-child(2) {
                transform-origin: 0% 100%;
            }
        }
    }
}

.top-nav {
    display: none;
    @media all and (min-width: 1024px) {
        display: block;
    }
}
