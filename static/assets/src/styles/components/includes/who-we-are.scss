.who-we-are {
    display: flex;
    flex-wrap: wrap;
    position: relative;
    margin: 0 auto 24px auto;
    max-width: 708px;

    img {
        order: 1;

        @media all and (min-width: 768px) {
            order: 0;
        }
    }

    span {
        color: #196094;
        text-transform: uppercase;
        font-size: 13px;
        font-weight: 600;
    }

    p {
        color: #7b7b7b;
        font-size: 14px;
        margin: 3px 0 0 0;
        text-align: justify;
    }

    > div {
        padding: 5px;
        text-align: center;
        width: 60%;

        @media all and (min-width: 768px) {
            padding: 0;
            text-align: left;
            width: 250px;
            position: absolute;
            width: 50%;
        }

        &.top {
            right: 0;
            top: 0;
            order: 0;
            margin-left: auto;

            @media all and (min-width: 768px) {
                margin-left: 0;
                width: 250px;
            }
        }

        &.bottom {
            left: 0;
            bottom: 0;
            margin: 0;
            order: 2;

            @media all and (min-width: 768px) {
                width: 200px;
                bottom: 35px;
                order: 0;
            }
        }
    }
}