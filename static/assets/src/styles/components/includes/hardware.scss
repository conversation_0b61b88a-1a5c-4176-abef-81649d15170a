.hardware-include {
    position: relative;
    color: white;
    margin: 12px 0 24px 0;
    height: auto;
    overflow: hidden;

	@media only screen and (max-width: 1024px) {
		background: #454347;
        padding: 10px 0;
	}

    @media all and (min-width: 1024px) {
        height: 175px;
    }

    span, strong {
        position: relative;
        @media all and (min-width: 1024px) {
            width: 50%;
        }
    }
    div {
        padding: 8px;
        display: flex;
        flex-direction: column;
        position: relative;
        @media all and (min-width: 1024px) {
            align-items: flex-end;
            padding: 16px 16px 16px 0;
        }
    }
    strong {
        font-size: 16px;
        margin: 0 0 16px 0;

        @media all and (min-width: 768px) {
            font-size: 19px;
        }
    }
    span {
        font-size: 14px;
    }
    img {
        position: absolute;
        top: 0;
        left:0;
        width: 100%;
        height: 100%;
        display: none;

        @media all and (min-width: 1024px) {
            font-size: 14px;
            display: block;
        }
    }
}