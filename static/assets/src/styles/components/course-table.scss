.title-section {
    width: 50%;
}

.courses-data {
    width: 100%;
    min-width: 710px;
    margin: 0 0 0 -10px;
    border-collapse: collapse;
    border-spacing: 0;
    background: none;
    border: none;
    font-size: 14px;
    line-height: 1.571em;
}

.courses-data td[colspan="5"],
.courses-data td[colspan="4"] {
    overflow: hidden;
    padding: 1em 10px 0.5em;
    font-size: 16px;
    line-height: 1.5em;
    font-weight: 800;
    background: #fff;
}

.courses-data td[colspan="5"] a,
.courses-data td[colspan="4"] a {
    color: #024e88;
}

.courses-data td[colspan="5"] a:after {
    content: "";
    display: inline-block;
    height: 1px;
    width: 100%;
    margin-left: 0.5em;
    margin-right: -110%;
    vertical-align: -2px;
    background: linear-gradient(to right, rgba(255, 255, 255, 0.75), rgba(255, 255, 255, 1) 90%) #024e88;
}

.courses-data a {
    display: block;
    color: #232320;
}

.courses-data td[colspan="5"] a:focus,
.courses-data td[colspan="5"] a:hover,
.courses-data a:focus,
.courses-data a:hover {
    margin: -1px -1px -1px -6px;
    padding-left: 5px;
    color: #fff;
    background: #0a6e93;
    border: #04526f solid 1px;
}

.courses-data td[colspan="5"] a:focus:after,
.courses-data td[colspan="5"] a:hover:after {
    content: none;
}

.courses-data a:active {
    position: relative;
    top: 1px;
}

.courses-data th {
    text-align: left;
}

.courses-data td {
    vertical-align: top;
}

.courses-data ol,
.courses-data p {
    list-style-type: none;
    margin: 0;
}

.courses-data thead tr {
    background: linear-gradient(rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0));
    background-color: #edede8;
}

.courses-data thead th {
    padding: 14px 10px;
    border: #c8c8c4 solid;
    border-width: 1px 0;
}

.courses-data thead th:first-child {
    padding-left: 9px;
    border-left-width: 1px;
}

.courses-data thead th:last-child {
    padding-right: 9px;
    border-right-width: 1px;
}

.courses-data tbody tr {
    background: #f6f9fb;
}

.courses-data tbody tr:nth-child(even) {
    background: #e6f0f8;
}

.courses-data tbody td {
    padding: 10px;
    border-bottom: #9cd0e8 solid 1px;
}

.actions .courses-date,
tbody .courses-date,
.courses-details {
    font-size: 12px;
    white-space: nowrap;
}

tbody .courses-details,
tbody .courses-date p {
    line-height: 1.833em;
}

.courses-details {
    width: 10%;
    font-weight: normal;
}

.date-section {
    white-space: nowrap;
}

.course-code {
    position: relative;
    display: block;
    padding: 0 0.5em;
    font-weight: 700;
    line-height: 1.428em;
    text-decoration: none;
    color: #fff;
    word-wrap: break-word;
    text-shadow: 0 0 0 transparent, 0 -1px 0 rgba(0, 0, 0, 0.4);
    background: #0169aa url("../img/code-bg.png");
    border: solid 1px;
    border-color: rgba(255, 255, 255, 0.45) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
}

.courses-data .course-code {
    width: 118px;
    color: #fff;
}

.subtitle .course-code {
    display: inline-block;
    margin: 0 0.5em;
    font-size: 0.857em;
}

.meta-content .authorization-name {
    position: relative;
    margin: 0 0 10px -470px;
    top: -10px;
}

.course-code.example {
    background-color: #686864;
}

.courses-data .small-head {
    padding-top: 0;
    padding-bottom: 0;
    line-height: 1.2em;
    vertical-align: middle;
}

.city {
    font-style: normal;
    font-weight: 600;
    color: #0f0e04;
}

.courses-data tbody .course-code:focus,
.courses-data tbody .course-code:hover {
    margin: 0;
    padding: 0 0.5em;
    cursor: pointer;
}
.courses-data tbody .name-with-code {
    min-width: 390px;
}
.courses-data tbody .name-with-code.shorter {
    min-width: 290px;
}
.courses-data tbody .name-with-code a {
    font-weight: bold;
    text-decoration: none;
    color: #076a8f;
}
.courses-data tbody .name-with-code a:hover {
    margin: -1px -1px -1px -6px;
    padding-left: 5px;
    color: #fff;
    background: #0a6e93;
    border: #04526f solid 1px;
}
.courses-data tbody .name-with-code span {
    font-weight: normal;
    text-decoration: none;
    font-size: 11px;
    color: black;
    margin: 0px;
    margin-top: -3px;
    padding: 0px 0px;
}
