#lp-stickyTop {
    .inner-content {
        padding: 0 20px;

        @media all and (min-width:1024px) {
            width: 1020px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
        }
    }
    .lp-contentHeader-middle {
        overflow-y: hidden;

        @media all and (min-width:1024px) {
            margin-left: 40px;
        }

        ul {
            max-width:650px;
            width:650px!important;
            overflow-x: scroll;
            @media all and (min-width:1024px) {
                overflow-x: initial;
            }
        }
    }
    &.fixed-header {
        @media all and (min-width:1024px) {
            top: 0;
        }
        .inner-content{
            height: 105px;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            @media all and (min-width:1024px) {
                flex-direction: row;
                height: auto;
                justify-content: space-between;
                position: relative;
                left: -13px;
            }
        }
        .lp-contentHeader-middle {
            position: fixed;
            top: 51px;
            left: 0;
            z-index: 100;
            width: 100%;
            box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.24);
            background: #f3f3f3;
            padding: 0 10px;
            @media all and (min-width:1024px) {
                top: 0;
                box-shadow: none;
                background: none;
                width: auto;
                position: static;
            }
        }
    }
    .lp-zapisz-btn {
        width: 260px;
        font-size: 14px;
        font-weight: 600;
        line-height: 1em;
        text-transform: uppercase;
        text-align: center;
        margin: 10px auto;
        @media all and (min-width:1024px) {
            margin: 0;
        }
        a {
            display: block;
            width: 100%;
            padding: 16px 0;
            color: #fff;
            text-decoration: none;
            background: #b12222;
            &:hover {
                background: #9d1616;
            }
        }
    }
    .lp-question-btn {
        display: none;
        float: left;
        position: relative;
        left: 0px;
        width: 222px;
        font-size: 12px;
        font-weight: 600;
        line-height: 1em;
        letter-spacing: 0px;
        text-transform: uppercase;
        text-align: center;
        a {
            display: block;
            width: 100%;
            padding: 17px 0;
            color: #9b9b9b;
            text-decoration: none;
            background: #eee;
            &:hover {
                background: #dedede;
            }
        }
    }
}