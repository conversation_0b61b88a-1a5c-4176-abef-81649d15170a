.bootcamp-header {
    border: 1px solid #e3e3e3;
    padding: 10px;
    margin: 1em 0;
    background: #fff;
    background-position: bottom right;
    background-repeat: no-repeat;
    position: relative;
    background-size: cover;
    @media all and (max-width: 1024px) {
        background-image: none!important;
        background-color: #f5f5f5;
    }
    * {
        box-sizing: border-box;
    }
    h1{
        margin: 5px 0 0 0;
        font-size: 20px;
        line-height: 1em;
        color: #525247;
        font-weight: 300;
        letter-spacing: -1px;
        @media all and (min-width: 1024px){
            font-size: 30px;

        }
    }
    h2{
        margin: 10px 0 0 0;
        font-weight: 600;
        letter-spacing: -1px;
        font-size: 16px;
        line-height: 1.3;
        color: #2e2e2b;
        @media all and (min-width: 1024px) {
            width: 80%;
            line-height: 34px;
            font-size: 31px;
        }
    }
    .variant {
        display: flex;
        justify-content: space-between;
        fieldset {
            width: 48%;
            padding: 16px;
            font-size: 12px;
            text-align: center;
            @media all and (min-width: 1024px) {
                width: 40%;
            }
            legend {
                margin: 0 auto;
                padding: 0 1px;
                font-weight: 700;
            }
            strong {
                display: block;
            }
        }
        .circle {
            border: 1px #d4d4d4 solid;
            padding: 10px 11px;
            border-radius: 100%;
            font-style: italic;
            font-size: 12px;
            width: 41px;
            height: 41px;
            align-self: center;
            display: none;
            @media all and (min-width: 1024px) {
                display: block;
            }
        }
        &.price {
            > div {
                display: flex;
                flex-direction: column;
                padding: 0 16px;
                text-align: center;
                width: 40%;
                position: relative;
            }
        }
    }
    .top-red {
        padding: 0px 10px;
        background-color: #b12222;
        color: white;
        font-size: 11px;
        box-shadow: 0px 3px 3px rgba(35,44,71,0.05), 0px 2px 2px rgba(35,44,71,0.07), 0px 1px 1px rgba(35,44,71,0.1);
        margin: 0 0 10px 0;
        display: inline-block;
        max-width: auto;
    }
    .description {
        list-style: none;
        margin: 1em 0 0 0;
        @media all and (min-width: 1024px) {
            margin: 1em 0 0 1.35em;
            width: 77%;
        }
        li {
            display: flex;
            margin: 0 0 10px 0;
            line-height: 19px;
            flex-direction: column;

            @media all and (min-width: 1024px) {
                flex-direction: row;
                margin-right: 10px;
            }
            &:last-child {
                margin: 0;
            }
        }
        .label {
            color: #000;
            font-weight: 600;
            text-align: left;
            letter-spacing: -0.2px;
            padding: 0;
            width: 100%;
            margin-right: 10px;

            @media all and (min-width: 1024px) {
                text-align: right;
                width: 100px;
                .only-mobile {
                    display: none;
                }
            }
        }
        .content {
            font-weight: 400;
            padding: 0;
            width: 100%;
            @media all and (min-width: 1024px) {
                width: calc(100% - 100px);
            }
            &.with-tooltip{
                display: flex;
                align-items: center;
                .tooltip {
                    position: static;
                }
            }
            .pay-info {
                margin: 8px 0;
            }
        }
    }
    .tooltip {
        margin: 0 0 0 10px;
        position: absolute;
        right: 0;
        z-index: 1;
        &-icon{
            width: 17px;
            height: 17px;
            background: url("/static/assets/public/img/info-icon.png") no-repeat;
            cursor: pointer;
            position: relative;
            &:hover {
                .tooltip-content{
                    display: block;
                }
            }
        }
        &-content{
            display: none;
            box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, 0.2);
            line-height: 17px;
            text-align: left;
            background: #fff;
            color: #646464;
            font-size: 12px;
            padding: 20px;
            width: 330px;
            position: absolute;
            bottom: 30px;
            left: -120px;

            @media all and (min-width: 768px) {
                left: -30px;
            }
        }

        &-content-right {
            @media all and (max-width: 1024px) {
                right: -30px;
                left: auto;
            }
        }

        &-triangle {
            width: 0;
            height: 0;
            border-top: 10px solid #fff;
            border-right: 10px solid transparent;
            border-bottom: 10px solid transparent;
            border-left: 10px solid transparent;
            position: absolute;
            bottom: -20px;
            left: 120px;

            @media all and (min-width: 768px) {
                left: 28px;
            }
        }

        &-triangle-right {
            @media all and (max-width: 1024px) {
                right: 28px;
                left: auto;
            }
        }
    }
    .modes {
        margin: 0;
        li {
            margin: 0;
            flex-direction: row;
            span {
                background: url("https://www.alx.pl/media/trybyIcon_1x.png") no-repeat;
                display: inline-block;
                width: 20px;
                height: 16px;
            }
        }
        .weekly{
            background-position: -80px 4px;
        }
        .daily{
            background-position: -40px 1px;
        }
        .nightly{
            background-position: 0 1px;
        }
    }
    .pay-info {
        font-size: 12px;
        color: #7d7d7d;
        line-height: 1.3;
        @media all and (min-width: 1024px) {
            margin: 0 0 0 83px;
        }
        svg {
            width: 14px;
            margin: 0 5px 0 0;
            fill: rgb(125, 125, 125);
        }
    }
    .course_suggest {
        overflow: hidden;
    }
}