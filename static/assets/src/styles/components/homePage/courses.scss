.courses_list{
    background: #f4f4f4;
    padding: 0 0 50px 0;
    &-header {
        padding: 3.5vh 0 1vh 0;
        background: #fff;
    }
    .filters_wrapper {
        .filters {
            background-color: white;
            padding: 0 15px;
            color: #42bfe8;
            font-size: 15px;
            display: flex;
            align-items: center;
            ul {
                margin: 0 auto;
                li {
                    display: inline-block;
                    color: #969db0;
                    cursor: pointer;
                    transition: color .3s;
                    &.active {
                        color: #66c8eb;
                    }
                    &:after {
                        content: '|';
                        margin: 0 5px 0 8px;
                    }
                    &:last-child:after { 
                        display: none
                    }
                }
            }
        }
    }
    .title {
        font-size: 25px;
        text-align: center;
        color: black;
        margin: 0 0 32px 0;
    }
    .single-course {
        width: 48%;
        box-shadow: 1px 1px 4px #d0d1d1;
        border-radius: 5px;
        position: relative;
        text-decoration: none;
        background-color: white;
        align-content: center;
        margin: 1%;
        transition: box-shadow .3s;
        @media all and (min-width: 500px) {
            margin:15px 9px;
            min-width: 225px;
            width: 23%;
        }
        &:hover {
            box-shadow: 1px 1px 4px #42bfe8;
            h3 {
                color: #42bfe8;
            }
        }
        &.special {
            background-color: #b2b3b3;
            .description, .more, h3{
                color: white;
            }
            &:hover {
                h3 {
                    color: #425163;
                }
            }
        }
        img {
            width: 100%;
            display: block;
            border-radius: 4px 4px 0 0;
        }
        h3 {
            font-size: 16px;
            font-weight: bold;
            color: #425163;
            line-height: 1.15;
            margin: 0 0 15px 0;
            transition: color .3s;
            height: 40px;
        }
        .tags {
            font-size: 12px;
            color: #777;
        }
        .description {
            font-size: 13px;
            line-height: 1.25;
            padding-top: 5px;
            padding-bottom: 5px;
            text-align: left;
            color: #9da4b5;
            @media all and (min-width: 500px) {
                padding-bottom: 15px;
                height: 80px;
            }
        }
        .more {
            text-decoration: underline;
            font-size: 14px;
            text-align: center;
            color: #42bfe8;
            font-weight: 600;
            margin: 0 0 10px 0;
        }
        .ribbon {
            background-color: #bd10e0;
            position: absolute;
            left: -4px;
            top: 20px;
            padding: 3px 15px;
            color: white;
            &:after {
                width: 0px;
                height: 0px;
                content: '';
                display: block;
                display: block;
                left: 0px;
                position: absolute;
                bottom: -4px;
                border-left: 4px solid transparent;
                border-top: 4px solid #bd10e0;
            }
            &.orange {
                background-color: #7ed321;
                &:after{
                    border-top: 4px solid #5e9b1b;
                }
            }
            &.green {
                background-color: #f6aa37;
                &:after{
                    border-top: 4px solid #f6aa37;
                }
            }
        }
        .content {
            padding: 4px 3px 0px;
            @media all and (min-width: 500px) {
                padding: 12px 8px 8px 8px;

            }
            > span {
                display: block;
            }
        }
    }
    #courses-wrapper {
        display: flex;
        flex-wrap: wrap;
        margin: 0 auto;
        max-width: 978px;
        width: calc(100% - 2vw);
        padding: 0 1vw;
        justify-content: center;
        @media all and (min-width: 1024px){
            justify-content: flex-start;
        }
    }
}