.accredited {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 0 24px;
    flex-direction: column;
    @media all and (min-width: 768px) {
        flex-direction: row;
    }
    .grey {
        color: #bbbbbb;
    }
    .red {
        color: #f60a11;
    }
    .left-content {
        position: relative;
        > div {
            position: absolute;
            top: 0;
            left: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
            height: 100%;
            justify-content: center;
            span {
                text-transform: uppercase;
                &:nth-child(1) {
                    margin: 10px 0 0 0;
                }
                &:nth-child(3) {
                    border-bottom: 2px solid;
                }
                &.grey {
                    font-weight: 700;
                    font-size: 13px;
                }
                &.red {
                    font-size: 29px;
                    font-weight: 700;
                    margin: 10px 0;
                }
            }
        }
    }
    .main-content {
        width: 100%;
        display: flex;
        flex-direction: column;
        font-size: 18px;
        text-align: center;
        @media all and (min-width: 768px) {
            width: calc(100% - 360px);
            &.en {
                width: calc(100% - 180px);
            }
        }
        > span {
            &:nth-child(1) {
                font-weight: 700;
                text-transform: uppercase;
            }
            &:nth-child(2) {
                font-weight: 700;
                text-transform: uppercase;
            }
            &:nth-child(3) {
                margin: 10px 0;
            }
            &.grey {
                font-size: 15px;
                font-weight: 600;
            }
        }
    }
}