
#basic-modal-content {
    display: none;
}
/* Overlay */

#simplemodal-overlay {
    background-color: #000;
}
/* Container */

#simplemodal-container {
    height: 250px;
    width: 600px;
    color: #000;
    background-color: #eee;
    border: 4px solid #444;
    padding: 12px;
}
#simplemodal-container .simplemodal-data {
    padding: 8px;
}
#simplemodal-container code {
    background: #141414;
    border-left: 3px solid #65B43D;
    color: #bbb;
    display: block;
    font-size: 12px;
    margin-bottom: 12px;
    padding: 4px 6px 6px;
}
#simplemodal-container a {
    color: #ddd;
}
#simplemodal-container a.modalCloseImg {
    background: url('../img/close.png') no-repeat;
    width: 25px;
    height: 29px;
    display: inline;
    z-index: 3200;
    position: absolute;
    top: -15px;
    right: -16px;
    cursor: pointer;
}
#sim plemodal-container h3 {
    color: #84b8d9;
}
#subscription-container ul {
    list-style-type: none;
    margin-left: 0px;
}
#subscription-container input[type=submit] {
    margin-top: 10px;
}
#subscription-container p.legend {
    margin-bottom: 20px;
}
#subscription-container label[for=email] {
    display: block;
    float: left;
    max-width: 100px;
    margin: 7px 0px;
    line-height: 32px;
}
#subscription-container .errorlist {
    color: red;
}
#subscription-container .button {
    float: left;
    width: 100px;
}
#subscription-container .privacy {
    float: left;
    width: 420px;
}
#subscription-container .privacy p {
    margin-top: 6px;
    font-size: 11px;
    margin-left: 10px;
    line-height: 13px;
}
#subscription-container .half-left {
    float: left;
    width: 60%;
}
#subscription-container .half-right {
    float: left;
    width: 40%;
}
#subscription-container #thank-you {
    margin-bottom: 20px;
}
