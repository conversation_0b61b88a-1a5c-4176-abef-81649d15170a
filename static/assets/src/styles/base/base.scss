@import url(https://fonts.googleapis.com/css?family=Open+Sans:400italic,600italic,700italic,400,300,700,800,600&subset=latin,latin-ext);

body,
html,
ul,
ol,
li,
h1,
h2,
h3,
h4,
p,
blockquote,
table,
td,
th,
form,
dl,
dd,
dt,
select,
input[type='text'],
input[type='password'],
textarea,
input[type='checkbox'],
input[type='radio'] {
    margin: 0;
    padding: 0;
}

html {
    font: 400 14px/1.5em 'Open Sans', sans-serif;
    color: map-get($color-stack, 'darkGray');
    background: map-get($color-stack, 'white');
    scroll-behavior: smooth;
}

body {
    min-width: 960px;
    position: relative;
}

a {
    text-decoration: none;
    text-shadow: none;
    color: map-get($color-stack, 'blue');
}

a:focus,
a:hover {
    text-decoration: none;
}

img {
    max-width: 100%;
}

a,
p {
    overflow-wrap: break-word;
}

p a {
    text-decoration: underline;

    &:hover {
        text-decoration: underline;
    }
}