.quote {
    padding: 30px 30px 40px 30px;
    background-color: white;
    color: #727272;
    font-style: italic;
    box-sizing: border-box;
    width: 100%;
    margin: 30px 0;
    max-width: 730px;
    width: 100%;
    font-weight: 500;
    line-height: 1.3;
    box-shadow: 15px 15px 8vh #f3f3f3;
    position: relative;
    text-align: center;
}

.quote:before {
    content: "";
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 40px 0px 0 100px;
    border-color: #ffffff transparent transparent transparent;
    bottom: -25px;
    right: 0;
    position: absolute;
    display: block;
}

.quote::after {
    content: "\201D";
    font-weight: 600;
    font-size: 50px;
    position: absolute;
    bottom: -15px;
    right: 30px;
    font-family: Arial;
    color: #5b90d0;
    font-style: normal;
}

.important {
    font-weight: 600;
    max-width: 730px;
    width: 100%;
    margin: 4vh auto;
    position: relative;
}

.important:before,
.important:after {
    position: absolute;
    width: 15px;
    height: 15px;
    content: "";
}

.important:before {
    border-left: #5b90d0 2px solid;
    border-top: #5b90d0 2px solid;
    top: -15px;
    left: -15px;
}

.important:after {
    border-right: #5b90d0 2px solid;
    border-bottom: #5b90d0 2px solid;
    right: -15px;
    bottom: -15px;
}

.the_name {
    display: inline-block;
    font-size: 17px;
    font-weight: 600;
    border: 2px solid #256ebf;
    box-sizing: border-box;
    padding: 0 6px;
    margin: 0px 0 30px 0;
    min-height: 0;
}

.the_name span {
    background-color: white;
    margin-top: -2px;
    margin-bottom: -2px;
    padding: 4px 6px;
    display: inline-block;
}

#site-content .single_person {
    max-width: 730px;
    width: 100%;
    margin: 5vh auto;
}

.single_person > p:first-of-type {
    float: left;
}

.single_person img {
    width: 285px;
    margin: 0 15px 2vh 0px;
}

.text-right {
    font-size: 14px;
    line-height: 1.35;
    /*font-weight:300;*/
    text-align: justify;
}

.text-main {
    clear: both;
    font-size: 14px;
    line-height: 1.35;
    margin-top: 6vh;
}

.text-main > span {
    display: block;
}

.histories-slider {
    max-width: 730px;
    width: 100%;
    margin: 30px auto;
}

.graduates {
    max-width: 730px;
    width: 100%;
    margin: 0 auto;
}

.graduates h1 {
    font-size: 17px;
}

.histories-slider .slick-prev {
    left: -50px;
    background: url("/media/prev-b.png") right no-repeat !important;
}
.histories-slider .slick-arrow {
    top: calc(50% - 45px);
    font-size: 0px;
    padding: 0;
    background-repeat: no-repeat;
    background: url("/media/next-b.png") left no-repeat !important;
    width: 30px;
    height: 50px;
}

.histories-slider a {
    text-decoration: none;
    margin: 0 15px;
}

.histories-slider a {
    color: #202020;
    font-weight: 600;
    margin: 10px 0;
}
.histories p {
    font-weight: 400;
    line-height: 20px;
}

.graduates .the_name {
    font-size: 14px;
    padding: 10px 0 0 0;
    border: none;
}

.graduates .the_name span {
    background-color: transparent;
    margin-top: 0;
    margin-bottom: 0;
    padding: 0;
    display: inline-block;
}

.histories-slider .slick-next {
    right: -15px;
}
.histories-slider .slick-arrow {
    top: calc(50% - 45px);
    font-size: 0px;
    padding: 0;
    background-repeat: no-repeat;
    background: url("/media/next-b.png") left no-repeat !important;
    width: 30px;
    height: 50px;
}

.histories-slider .slick-prev {
    left: -15px;
    background: url("/media/prev-b.png") right no-repeat !important;
}

.contact-full {
    background-color: #f4f4f4;
    width: 100%;
}

.graduates .slick-slide img {
    margin: auto;
    max-width: 156px;
    width: 156px;
    height: auto;
}

@media only screen and (max-width: 768px) {
    .graduates .slick-slide {
        text-align: center;
    }

    .single_person > p:first-of-type {
        float: none;
        margin: 1vh auto 3vh auto;
        text-align: center;
    }

    .important,
    #site-content .single_person,
    .histories-slider,
    .graduates {
        box-sizing: border-box;
        padding: 10px 15px;
    }

    .the_name {
        margin-left: auto;
        margin-right: auto;
    }
}

.contact-full > div {
    margin: 0 auto;
}
.lp-homePageContent {
    width: 940px;
    margin: 20px auto;
}

.contact-section {
    display: flex;
    padding: 5.5vh 15px 5.5vh 15px;
    margin: 0 auto;
    width: 100%;
    max-width: 654px;
}

.contact-section > div {
    min-width: 300px;
    padding: 15px;
    width: 100%;
    margin: 0;
}

.contact-full h3 {
    font-size: 24px;
    text-align: center;
}

.contact-section h4 {
    margin-top: 1.5vh;
    margin-bottom: 2.5vh;
    font-size: 16px;
    font-weight: 400;
    text-align: center;
    color: #616161;
}
.lp-sendEmail,
.lp-callUs {
    width: 100%;
    display: block;
    margin-top: 25px;
    text-align: center;
}
.contact-section .lp-sendEmail > p,
.contact-section .tos {
    width: 50%;
    display: inline-block;
    vertical-align: top;
}
.contact-section .tos {
    font-size: 10px;
    line-height: 1.2;
}
.contact-section .lp-sendEmail > p,
.contact-section .tos {
    width: 50%;
    display: inline-block;
    vertical-align: top;
}
.contact-section .lp-sendEmail input[type="checkbox"] {
    display: inline-block;
    margin-right: 4px;
    vertical-align: top;
}
.contact-section .text {
    max-width: 530px;
    color: #b2b2b2;
    display: inline-block;
    width: calc(100% - 45px);
    vertical-align: top;
}
.contact-section .tos {
    font-size: 10px;
    line-height: 1.2;
}
.contact-section .tos {
    font-size: 10px;
    line-height: 1.2;
}
.info-legend[data-legend]::after {
    color: #b2b2b2;
}
.info-legend[data-legend]::after {
    color: #b2b2b2;
}
.recaptcha {
    font-size: 8px;
    color: #b2b2b2;
    margin: 10px 0 25px 0;
    line-height: 12px;
}
.lp-sendEmail button.g-recaptcha::after {
    position: absolute;
    top: 50%;
    right: 14px;
    margin-top: -5px;
    content: "";
    display: block;
    width: 5px;
    height: 9px;
    background: url("../img/kursy_zbiorcze/kursy_zbiorcze_sprite.png") no-repeat;
    background-position: -100px -200px;
}
.contact-section .lp-sendEmail button {
    display: block;
    margin-top: 10px;
    float: none;
}
.lp-sendEmail button.g-recaptcha {
    position: relative;
    margin: 20px auto 0;
    margin-top: 20px;
    padding: 15px 30px 14px 12px;
    padding-left: 12px;
    padding-left: 12px;
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    text-decoration: none;
    color: #fff;
    background: #256ebf;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    width: 120px;
    text-align: center;
    padding-left: 22px;
}
.lp-sendEmail button.g-recaptcha::after {
    position: absolute;
    top: 50%;
    right: 14px;
    margin-top: -5px;
    content: "";
    display: block;
    width: 5px;
    height: 9px;
    background: url("../img/kursy_zbiorcze/kursy_zbiorcze_sprite.png") no-repeat;
    background-position-x: 0%;
    background-position-y: 0%;
    background-position: -100px -200px;
}
.lp-sendEmail button.g-recaptcha {
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    color: #fff;
    text-align: center;
}

.lp-newTop.lp-allCourses {
    padding-top: 70px;
    padding-bottom: 70px;
    background: url("/media/climb2.jpg") no-repeat #ffffff center;
}
.lp-courseImg8 {
    background: url("/media/imgCourse-ExcelNew.jpg") no-repeat;
}

.lp-courseSingle p {
    margin-bottom: 10px;
}

#qcf-inner-thanks {
    margin: 0 !important;
}

.lp-courseSingleTime {
    margin-bottom: 0px;
    padding-left: 0px;
}
.lp-courseSingleTime:after {
    display: none;
}
.lp-courseSingleTime b {
    display: inline-block;
}

.career-routes {
    background-image: url("/media/ways.jpg");
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
}

.contact-full > div {
    margin: 0 auto;
}

.capcza {
    display: inline-block;
}

.recaptcha {
    font-size: 8px;
    color: #b2b2b2;
    margin: 10px 0 25px 0;
    line-height: 12px;
}

.contact-full {
    background-color: #f4f4f4;
    width: 100%;
}

#qcf-inner-thanks {
    text-align: center;
}

.contact-full h3 {
    font-size: 24px;
    text-align: center;
}

.info-legend[data-legend]::after {
    color: #b2b2b2;
}

.lp-sendEmail button.g-recaptcha {
    position: relative;
    margin: 20px auto 0;
    padding: 15px 30px 14px 12px;
    padding-left: 12px;
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    text-decoration: none;
    color: #fff;
    background: #256ebf;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    width: 120px;
    text-align: center;
    padding-left: 22px;
}

.lp-sendEmail button.g-recaptcha:after {
    position: absolute;
    top: 50%;
    right: 14px;
    margin-top: -5px;
    content: "";
    display: block;
    width: 5px;
    height: 9px;
    background: url("../img/kursy_zbiorcze/kursy_zbiorcze_sprite.png") no-repeat;
    background-position: -100px -200px;
}

@keyframes mymove {
    from {
        margin-left: 90px;
    }
    to {
        margin-left: calc(50% - 152px);
    }
}

@keyframes mymovemobile {
    from {
        margin-left: 90px;
    }
    to {
        margin-left: calc(50% - 100px);
    }
}

@keyframes mymoverightmobile {
    from {
        margin-right: 90px;
    }
    to {
        margin-right: calc(50% - 100px);
    }
}

@keyframes mymoveright {
    from {
        margin-right: 90px;
    }
    to {
        margin-right: calc(50% - 152px);
    }
}

@keyframes myfont {
    from {
        font-size: 1.4em;
    }
    to {
        font-size: 2.2em;
    }
}

.break-here {
    display: none;
}

.contact-section .lp-articleBottom-right {
    float: none;
    clear: both;
}

.contact-section {
    display: flex;
    padding: 5.5vh 15px 5.5vh 15px;
    margin: 0 auto;
    width: 100%;
    max-width: 654px;
}

.contact-section h4 {
    margin-top: 1.5vh;
    margin-bottom: 2.5vh;
    font-size: 16px;
    font-weight: 400;
    text-align: center;
    color: #616161;
}
.contact-section > div {
    min-width: 300px;
    padding: 15px;
    width: 100%;
    margin: 0;
}

.contact-section .text {
    max-width: 530px;
    color: #b2b2b2;
    display: inline-block;
    width: calc(100% - 45px);
    vertical-align: top;
}

.lp-sendEmail,
.lp-callUs {
    width: 100%;
    display: block;
    margin-top: 25px;
    text-align: center;
}
.lp-callUs {
    color: #4d4d49;
}

.contact-section .lp-sendEmail label {
    margin-top: 0;
    margin-bottom: 10px;
    width: 100%;
    color: #616161;
}

.contact-section .lp-sendEmail input[type="email"],
.contact-section .lp-sendEmail input[type="tel"] {
    width: 96%;
    margin: 0 4px 0 0%;
    display: inline-block;
    height: 46px;
}

.contact-section .lp-sendEmail > p,
.contact-section .tos {
    width: 50%;
    display: inline-block;
    vertical-align: top;
}

.contact-section .lp-sendEmail input[type="checkbox"] {
    display: inline-block;
    margin-right: 4px;
    vertical-align: top;
}

.contact-section .lp-sendEmail button {
    display: block;
    margin-top: 10px;
    float: none;
}

.contact-section .tos {
    font-size: 10px;
    line-height: 1.2;
}

.contact-section .lp-articleBottom-height {
    height: 274px !important;
}

.info-box {
    font-size: 8px;
    border: 1px solid #e3e3e3;
    padding: 4px;
    margin-top: 10px;
    line-height: 1.2;
}

.marketing-movie {
    display: flex;
    padding: 45px 0;
    margin: 30px 0 50px 0;
    background-color: #f4f4f4;
}

.marketing-movie > * {
    width: 50%;
}

.marketing-movie p {
    padding-right: 20px;
    padding-left: 15px;
    text-align: justify;
    /*font-size:1.2em;*/
}

.marketing-movie h3 {
    color: #256ebf;
    font-size: 2em;
    padding-bottom: 15px;
    padding-right: 20px;
    padding-left: 15px;
    line-height: 1.2;
}

.histories {
    font-family: Open sans, sans-serif;
    font-size: 14px;
    color: #2e2e2b;
}
.histories p {
    font-weight: 400;
    line-height: 20px;
}

.marketing-movie svg {
    opacity: 0.7;
    content: "";
    width: 90px;
    height: 65px;
    background-image: url("/media/ytplayblue.svg");
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    display: block;
    left: calc(50% - 45px);
    top: calc(50% - 32px);
    position: absolute;
    cursor: pointer;
}

.promo-movie {
    width: 50%;
}

.promo-movie > div {
    position: relative;
    max-width: 580px;
    margin: auto;
}

.promo-movie img {
    width: calc(100% - 30px);
    margin-left: 15px;
}

.history-single {
    border-bottom: 1px solid #e3e3e3;
    padding-bottom: 50px;
}

.history-single > div {
    display: flex;
    padding: 50px 0 0px 0;
    align-items: center;
}
.history-single > div:nth-of-type(even) {
    flex-direction: column-reverse;
}

.history-single > div > * {
    width: 60%;
}

.history-single img {
    height: 250px;
    width: 250px;
    margin: auto;
    border-radius: 100%;
}

.history-single h4 {
    font-size: 1.45em;
    margin-top: 0;
    margin-bottom: 15px;
}

.read-more-text {
    display: none;
    margin-top: 20px;
    margin-bottom: 20px;
}

.lp-articleBottom-right {
    margin-top: 0px !important;
}

.histories {
    padding: 10px 0;
}

.histories .history-single:nth-of-type(odd) > div {
    padding-left: 15px;
}
.histories .history-single:nth-of-type(even) > div {
    padding-right: 15px;
    flex-direction: row-reverse;
}
.histories .history-single:nth-of-type(odd) > a {
    margin-left: calc(40% + 10px);
}

.history-single a {
    width: 80px;
    text-align: center;
    padding-left: 22px;
}
@media only screen and (min-width: 658px) {
    .marketing-movie > * {
        box-sizing: border-box;
        padding: 0 15px;
    }
}

@media only screen and (max-width: 650px) {
    .contact-section > div {
        min-width: auto;
    }

    .history-single h4 {
        font-size: 1.3em;
        margin-top: 15px;
    }

    .history-single p {
        font-size: 13px;
        line-height: 1.3;
    }

    .histories .history-single > div:nth-of-type(even) {
        flex-direction: column-reverse;
    }

    .history-single > div > * {
        width: 100%;
    }

    .history-single img {
        width: 250px;
    }

    .history-single a {
        margin-left: calc(50% - 66px);
        box-sizing: border-box;
        width: 132px;
    }

    .histories .history-single:nth-of-type(odd) > a {
        margin-left: calc(50% - 66px);
    }

    .history-single:last-of-type {
        border-bottom: 2px solid #e3e3e3;
    }

    .histories .history-single > div > div {
        padding-left: 0 !important;
        padding-right: 0;
    }

    .history-single > div {
        flex-wrap: wrap;
        padding-bottom: 20px;
        padding-left: 0px !important;
    }
}

@media only screen and (max-width: 720px) {
    .contact-full h3 {
        font-size: 20px;
    }

    .contact-full h4 {
        font-size: 16px;
    }

    .marketing-movie p {
        font-size: 0.9em;
        line-height: 1.35;
    }
    .contact-section .tos {
        font-size: 8px;
        line-height: 1.8;
        width: 100%;
    }

    .contact-section .lp-articleBottom-height {
        height: 274px !important;
    }

    .contact-section {
        flex-wrap: wrap;
        padding: 5.5vh 0;
    }
    .contact-section > div:first-of-type {
        width: 100%;
        margin: 0;
    }
    .contact-section .lp-articleBottom-right {
        width: 100%;
        margin-left: 0px;
        margin-top: 20px !important;
    }

    .marketing-movie {
        flex-wrap: wrap;
    }
    .marketing-movie > div {
        width: 100%;
    }

    .lp-newTop.lp-allCourses {
        padding-top: 50px;
        padding-bottom: 50px;
    }

    .history-single p {
        font-size: 0.9em;
        line-height: 1.35;
    }

    .lp-allCourses h2 {
        width: 62%;
    }
}

@media only screen and (max-width: 840px) {
    .lp-newTop.lp-allCourses {
        background: url("/media/climb_mobile2.jpg") no-repeat #ffffff !important;
        background-position: 0 !important;
        background-size: cover !important;
    }
    .break-here {
        display: inline;
    }

    .history-single img {
        height: 25vw;
        width: 25vw;
        margin: auto;
        border-radius: 100%;
        min-width: 200px;
        min-height: 200px;
    }

    .history-single a {
        margin-top: 20px;
    }
    .history-single.full-bio a {
        margin-top: 0px;
    }
}

@media only screen and (max-width: 450px) {
    .contact-section .lp-sendEmail > p {
        width: 100%;
    }
    .contact-section .lp-articleBottom-height {
        height: auto !important;
    }

    #site-content .lp-newTop.lp-allCourses {
        background-position: -30px 0px !important;
    }

    .marketing-movie p span {
        display: none;
    }

    .marketing-movie h3 {
        font-size: 1.8em;
    }

    .history-single p {
        font-size: 12px;
    }

    .history-single img {
        width: 200px;
        height: 200px;
    }

    .histories .full-bio.history-single:nth-of-type(odd) > img {
        transition-property: all;
        transition-duration: 0.55s;
        transition-timing-function: cubic-bezier(0, 1, 0.5, 1);
        -webkit-animation: none;
        animation: none;
    }

    .histories .full-bio.history-single:nth-of-type(even) > img {
        transition-property: all;
        transition-duration: 0.55s;
        transition-timing-function: cubic-bezier(0, 1, 0.5, 1);
        -webkit-animation: none;
        animation: none;
    }
}

@media only screen and (max-width: 375px) {
    #site-content .lp-newTop.lp-allCourses {
        background-position: -95px 10px !important;
    }
    .lp-allCourses h2 {
        width: 72%;
    }
}

@media only screen and (max-width: 985px) {
    .lp-courseImg8 {
        background: url("/media/imgCourse-ExcelNew-mobile.jpg") no-repeat;
    }
    .lp-newTop.lp-allCourses {
        background: url("/media/climb.jpg") no-repeat #ffffff;
        background-position: 0;
        background-size: cover;
    }
}

@media only screen and (max-width: 720px) {
    #wszystkie-kursy .lp-courseSingle {
        width: 100%;
        max-width: 450px;
        margin: 0 auto 20px auto;
        display: block;
    }
}

@media only screen and (max-width: 530px) {
    .lp-allCourses h1 {
        width: 80%;
    }
    .marketing-movie {
        padding: 25px 0;
    }
    .lp-courseSingle h2 {
        min-height: auto;
    }
    .lp-courseSingle {
        padding: 15px;
    }
    .lp-courseSingleImg {
        width: calc(100% + 30px);
        margin: 15px 0;
        margin-left: -15px;
    }
    .lp-courseSingle p {
        margin-bottom: 10px;
    }
    .lp-courseSteps li:nth-child(1),
    .lp-courseSteps li:nth-child(2),
    .lp-courseSteps li:nth-child(3) {
        background-position: 50% -60px !important;
        background-size: 400px !important;
    }
    .lp-courseSteps li:nth-child(1),
    .lp-courseSteps li:nth-child(2),
    .lp-courseSteps li:nth-child(3) {
        padding-top: 200px !important;
    }
}

.lp-courseSingle h2 a {
    color: #2a3642;
    text-decoration: none;
}
.lp-courseSingleImg a {
    width: 100%;
    height: 100%;
    display: block;
}

/* HOT FIX */

.lp-courseSingle {
    padding: 10px;
    width: calc(33% - 10px);
    margin-right: 15px;
}
.lp-courseSingle:last-of-type {
    margin-right: 0;
}
.lp-courseSingleImg {
    width: calc(100% + 20px) !important;
    margin-left: -10px;
    margin-top: 10px;
    margin-bottom: 10px;
    height: 158px;
    background-size: cover !important;
}
.lp-courseSingleTime {
    margin-top: 10px;
}

/* przycisk po prawej */
.lp-courseSingleTime {
    width: 50%;
    display: inline-block;
}

.lp-courseSingle {
    position: relative;
    height: 405px;

    vertical-align: top;
}

.lp-courseSingle .lp-btn-blue {
    position: absolute;
    bottom: 9px;
    right: 9px;
}

.nauka {
    background: url("/media/18-nauka_VWq5qpz.jpg") no-repeat center;
    background-size: 290px auto;
    margin: 30px 0;
}

.left-img {
    background-position: 0px;
    padding-left: 300px;
    padding-top: 30px;
    padding-bottom: 30px;
    min-height: 250px;
    height: auto;
}

@media only screen and (max-width: 740px) {
    .left-img {
        background-size: 60%;
        padding-left: 270px;
        background-size: 260px auto;
        padding-top: 30px;
    }
}

@media only screen and (max-width: 530px) {
    .left-img h2 {
        min-height: auto;
        float: none;
        display: block;
    }

    .brain {
        background: url("/media/20error22.png") no-repeat center 20px;
    }

    .znak {
        background: url("/media/19selfie.jpg") no-repeat center 20px;
    }

    .haker {
        background: url("/media/20maszyna2.png") no-repeat center 20px;
        display: block;
    }

    .nauka {
        background: url("/media/18-nauka_VWq5qpz.jpg") no-repeat center 20px;
    }

    .left-img {
        display: block;
        height: auto;
        padding-left: 0px;
        padding-top: 270px;
        padding-bottom: 0px;
    }
}

.lp-phpshadowBottom {
    display: block;
    position: relative;
    top: -11px;
    z-index: 1;
    margin: 0;
    width: 100%;
    height: 15px;
    background: url("/media/shadowBottomNew.png") no-repeat;
    background-size: 100% auto;
}

@media only screen and (max-width: 360px) {
    .left-img h2 {
        min-height: auto;
        float: none;
        display: block;
    }

    .left-img {
        height: auto;
        display: block;
        padding-left: 0px;
        padding-top: 230px;
        background-size: auto 200px;
    }
}

@media only screen and (max-width: 985px) {
    .lp-minHeightAutoMobile h2 {
        min-height: auto;
    }
}

@media only screen and (min-width: 531px) and (max-width: 616px) {
    .lp-minHeightAutoMobile.lp-minHeightAutoSmallMobile h2 {
        min-height: 42px;
    }
}

@media only screen and (max-width: 850px) {
    .lp-courseRefLeft {
        margin: 50px auto 5px !important;
    }
}

@media only screen and (min-width: 531px) {
    .lp-courseSingle p {
        min-height: 90px;
    }
}
/* HOT FIX */

/* HOT FIX - TRENERZY */
.lp-mobileCoachBtn {
    display: none;
    margin: 20px auto 0;
    text-align: center;
}

@media only screen and (max-width: 580px) {
    .lp-mobileCoachBtn {
        display: block;
    }
    .lp-courseWork h3,
    .lp-courseWork p {
        width: 100% !important;
    }

    .lp-homepageCoachWrap ul li:nth-child(6),
    .lp-homepageCoachWrap ul li:nth-child(7),
    .lp-homepageCoachWrap ul li:nth-child(8),
    .lp-homepageCoachWrap ul li:nth-child(9),
    .lp-homepageCoachWrap ul li:nth-child(10) {
        display: none;
    }

    .lp-homepageCoachWrap ul {
        padding: 0;
    }
    .lp-homepageCoachWrap ul li {
        padding: 0;
    }
    .lp-homepageCoachWrap .lp-coachImageWrap {
        width: 80px;
        float: left;
    }
    .lp-homepageCoachWrap .lp-coachImage {
        width: 80px;
        height: 80px;
        margin: 0;
    }
    .lp-homepageCoachWrap .lp-coachName {
        width: calc(100% - 120px);
        text-align: left;
        margin: 0;
        padding-left: 20px;
        box-sizing: border-box;
        display: table-cell;
        height: 80px;
        vertical-align: middle;
    }
}

/* END HOT FIX - TRENERZY */

/* HOT FIX - REFERENCJE */
@media only screen and (max-width: 580px) {
    .lp-courseAllRef .lp-courseRefLeft {
        float: left !important;
        margin-top: 18px !important;
        margin-right: 6px !important;
    }
    .lp-courseAllRef .lp-courseRefRight {
        background-position: right bottom;
    }
    .lp-courseAll-ref p {
        margin-bottom: 10px;
    }
    .lp-courseAllRef .lp-courseRef {
        margin-bottom: 20px !important;
    }
}
/* END HOT FIX - REFERENCJE */

.lp-courseAll-ref .lp-recoManRight.lp-recoImgA,
.lp-courseAll-ref .lp-recoManRight.lp-recoImgB,
.lp-courseAll-ref .lp-recoManRight.lp-recoImgC,
.lp-courseAll-ref .lp-recoManRight.lp-recoImgD {
    background: url("/media/avatary.png") 0 0 no-repeat;
}

.lp-courseSingleTime {
    display: none;
}
.lp-courseSingle .lp-btn-blue {
    position: relative;
    bottom: 0;
    right: 0;
    display: block;
    margin: 10px auto 0;
    width: 40px;
}

.lp-courseImg9 {
    background: url("/media/imgCourse-JSNew.jpg") no-repeat;
}
.lp-courseImg10 {
    background: url("/media/imgCourse-PythonNew.jpg") no-repeat;
}

.lp-yt-movie {
    width: 100%;
    display: block;
    margin-bottom: 40px;
}
.lp-yt-movie iframe {
    width: 100%;
    min-height: 505px;
}

h2 span.lp-span {
    margin-left: 0;
    font-size: 14px;
    letter-spacing: 0;
    display: block;
}

@media only screen and (max-width: 985px) {
    .lp-courseImg9 {
        background: url("/media/imgCourse-JSNew-mobile.jpg") no-repeat;
    }
    .lp-courseImg10 {
        background: url("/media/imgCourse-PythonNew-mobile.jpg") no-repeat;
    }

    h2 span.lp-span {
        display: inline-block;
    }
}


.lp-allCourses h1 {
    font-size: 26px;
}

.lp-courseImg1 {
    background: url("/media/21min1.jpg") no-repeat;
    background-size: auto auto;
}
.lp-courseImg2 {
    background: url("/media/21min2.jpg") no-repeat;
    background-size: auto auto;
}
.lp-courseImg10 {
    background: url("/media/21min31.jpg") no-repeat;
    background-size: auto auto;
}

.ticklist li::before {
    content: "✔";
    font-size: 18px;
    color: #99c6b9;
    margin-right: 0.5em;
}
.ticklist ul {
    text-indent: 2px;
    list-style-type: none;
    margin-left: 4px;
    list-style-position: outside;
}

.ticklist li {
    color: #2e2e2b;
}

.lp-newTop-content .ticklist li {
    font-family: "Open sans", sans-serif;
    font-size: 22px;
    line-height: 29px;
    font-weight: 300;
    color: #2a3642;
}

.od-juniora::after {
    content: " ";
    width: 100%;
    height: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
    background: url("/media/21piano3.jpg") no-repeat;
    background-size: auto 100%;
}

.lp-courseSteps li:nth-child(1) {
    background: url("/media/21robot8.png") no-repeat;
    background-position-x: 0%;
    background-position-y: 0%;
    background-position: -75px -25px;
}

.lp-courseSteps li:nth-child(2) {
    background: url("/media/21learn2.png") no-repeat;
    background-position-x: 0%;
    background-position-y: 0%;
    background-position: -75px -25px;
}

.lp-courseSteps li:nth-child(3) {
    background: url("/media/21teach1.png") no-repeat;
    background-position-x: 0%;
    background-position-y: 0%;
    background-position: -75px -25px;
}

.lp-courseWork::after {
    position: absolute;
    right: 0;
    bottom: 0;
    content: " ";
    display: block;
    width: 775px;
    height: 250px;
    background: url("/media/21kidsatwork2.png") no-repeat;
}

.lp-courseWork {
    position: relative;
    width: 100%;
    padding: 20px;
    margin-bottom: 50px;
    border: 3px solid #e9e9e9;
    box-sizing: border-box;
}
.lp-courseWork::after {
    width: 100%;
    background-position: right bottom;
}

.lp-courseSingle h2 {
    min-height: 60px;
}

@media only screen and (max-width: 985px) {
    .od-juniora::after {
        content: " ";
        width: 100%;
        height: 344px;
        position: absolute;
        top: 0;
        bottom: auto;
        left: 0;
        background: url("/media/21piano3.jpg") no-repeat;
        background-size: auto;
        background-position: 100% 50% !important;
    }
    .od-juniora > h3 {
        padding-top: 290px !important;
    }

    .od-juniora > * {
        width: 98%;
        padding: 0 1% !important;
    }
}

@media only screen and (max-width: 530px) {
    .od-juniora > h3 {
        padding-top: 1% !important;
    }
}

#showHideBtn1,
#showHideBtn2,
#showHideBtn3,
#showHideBtn4 {
    display: none;
    background: transparent;
    border: 0;
    margin: 10px 0 0;
    padding: 0;
    color: #7d7d7d;
    font-size: 11px;
    text-transform: uppercase;
    font-weight: 700;
}
#hiddenReco1,
#hiddenReco2,
#hiddenReco3,
#hiddenReco4 {
    display: block;
}

@media only screen and (max-width: 580px) {
    #showHideBtn1,
    #showHideBtn2,
    #showHideBtn3,
    #showHideBtn4 {
        display: block;
    }
    #hiddenReco1,
    #hiddenReco2,
    #hiddenReco3,
    #hiddenReco4 {
        display: none;
    }

    .lp-courseAll-ref .lp-recoManRight.lp-recoImgA,
    .lp-courseAll-ref .lp-recoManRight.lp-recoImgB,
    .lp-courseAll-ref .lp-recoManRight.lp-recoImgC,
    .lp-courseAll-ref .lp-recoManRight.lp-recoImgD {
        background: url("/media/avatary.png") 0 0 no-repeat;
        position: absolute;
        top: inherit !important ;
        left: 28px !important;
        bottom: 0 !important;
        width: 60px;
        height: 81px;
        background-size: 100%;
        margin-left: 0 !important;
    }

    .lp-courseAll-ref .lp-recoManLeft {
        padding-bottom: 50px;
    }
    .lp-recoManLeft {
        margin: 0 auto 0;
    }
    .lp-courseAll-ref .lp-recoMan {
        margin: 20px 0 !important;
    }
    .lp-courseAll-ref .lp-recoManLeft p.lp-recoManPodpis {
        position: relative;
        left: 68px;
        top: 9px;
        width: calc(100% - 75px);
    }

    .lp-recoTriangle {
        display: none !important;
    }
}

.verticals {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    margin: 4.5vh auto;
    max-width: 700px;
}

.vertical-history {
    width: 100%;
    max-width: 350px;
    box-sizing: border-box;
}

.vertical-history:nth-of-type(odd) {
    margin: 0 40px 0 0;
}

.vertical-history:nth-of-type(even) {
    margin: 0 0 0 40px;
}

.histories .vertical-history p,
.histories .horizontal-history p {
    font-size: 11px;
    line-height: 1.35;
    text-align: justify;
    font-weight: 100;
    min-height: 200px;
}

.histories .horizontal-history p {
    min-height: 170px;
}

.vertical-history p.st-name,
.horizontal-history p.st-name {
    display: inline-block;
    font-size: 17px;
    font-weight: 600;
    border: 2px solid #256ebf;
    box-sizing: border-box;
    padding: 0 6px;
    margin: 30px 0 45px 0;
    min-height: 0;
}

.horizontal-history p.st-name {
    margin-top: 0px;
}

.vertical-history img {
    width: 100%;
    height: auto;
}

.vertical-history p.st-name span,
.horizontal-history p.st-name span {
    background-color: white;
    margin-top: -2px;
    margin-bottom: -2px;
    padding: 4px 6px;
    display: inline-block;
}

.st-name a {
    color: black;
    text-decoration: none;
}

.vertical-history a.lp-btn-blue,
.horizontal-history a.lp-btn-blue {
    margin-left: calc(100% - 120px);
    width: 80px;
    padding: 10px 5px 10px 35px;
}

.horizontals {
    max-width: 700px;
    margin: 20px auto;
}

.horizontals > .horizontal-history {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    margin: 4.5vh 0;
}

.horizontal-history > * {
    width: 350px;
    margin: 30px 40px;
}

.horizontal-history > img {
    margin-left: 0;
}

.horizontal-history > div {
    margin-right: 0;
}

.histories-slider img {
    height: auto;
    width: 100%;
}

.histories-slider {
    max-width: 700px;
    margin: 20px auto;
}

.histories-slider a p {
    color: #202020;
    font-weight: 600;
    margin: 10px 0;
}

.histories-slider a {
    text-decoration: none;
    margin: 0 0 0 15px;
}

.histories h4 {
    font-size: 17px;
}

.histories-slider .slick-arrow {
    top: calc(50% - 45px);
    font-size: 0px;
    padding: 0;
    background-repeat: no-repeat;
    background: url("/media/next-b.png") left no-repeat !important;
    width: 30px;
    height: 50px;
}

.histories-slider .slick-prev {
    left: -50px;
    background: url("/media/prev-b.png") right no-repeat !important;
}

.histories-slider .slick-next {
    right: -50px;
}

@media only screen and (max-width: 768px) {
    .verticals {
        flex-wrap: wrap;
    }
    .vertical-history {
        width: 100%;
        max-width: 350px;
        box-sizing: border-box;
        margin-left: auto !important;
        margin-right: auto !important;
    }
}

.lp-newTop.lp-allCourses {
    padding-top: 70px;
    padding-bottom: 70px;
    background: url("/media/climb2.jpg") no-repeat #ffffff center;
}
.lp-courseImg8 {
    background: url("/media/imgCourse-ExcelNew.jpg") no-repeat;
}

.lp-courseSingle p {
    margin-bottom: 10px;
}
#qcf-inner-thanks {
    margin: 0 !important;
}

.lp-courseSingleTime {
    margin-bottom: 0px;
    padding-left: 0px;
}
.lp-courseSingleTime:after {
    display: none;
}
.lp-courseSingleTime b {
    display: inline-block;
}

.career-routes {
    background-image: url("/media/ways.jpg");
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
}

.contact-full > div {
    margin: 0 auto;
}

.capcza {
    display: inline-block;
}

.recaptcha {
    font-size: 8px;
    color: #b2b2b2;
    margin: 10px 0 25px 0;
    line-height: 12px;
}

.contact-full {
    background-color: #f4f4f4;
    width: 100%;
}

#qcf-inner-thanks {
    text-align: center;
}

.contact-full h3 {
    font-size: 24px;
    text-align: center;
}

.info-legend[data-legend]::after {
    color: #b2b2b2;
}

.lp-sendEmail button.g-recaptcha {
    position: relative;
    margin: 20px auto 0;
    padding: 15px 30px 14px 12px;
    padding-left: 12px;
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    text-decoration: none;
    color: #fff;
    background: #256ebf;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    width: 120px;
    text-align: center;
    padding-left: 22px;
}

.lp-sendEmail button.g-recaptcha:after {
    position: absolute;
    top: 50%;
    right: 14px;
    margin-top: -5px;
    content: "";
    display: block;
    width: 5px;
    height: 9px;
    background: url("../img/kursy_zbiorcze/kursy_zbiorcze_sprite.png") no-repeat;
    background-position: -100px -200px;
}

@keyframes mymove {
    from {
        margin-left: 90px;
    }
    to {
        margin-left: calc(50% - 152px);
    }
}

@keyframes mymovemobile {
    from {
        margin-left: 90px;
    }
    to {
        margin-left: calc(50% - 100px);
    }
}

@keyframes mymoverightmobile {
    from {
        margin-right: 90px;
    }
    to {
        margin-right: calc(50% - 100px);
    }
}

@keyframes mymoveright {
    from {
        margin-right: 90px;
    }
    to {
        margin-right: calc(50% - 152px);
    }
}

@keyframes myfont {
    from {
        font-size: 1.4em;
    }
    to {
        font-size: 2.2em;
    }
}

.break-here {
    display: none;
}

.contact-section .lp-articleBottom-right {
    float: none;
    clear: both;
}

.contact-section {
    display: flex;
    padding: 5.5vh 15px 5.5vh 15px;
    margin: 0 auto;
    width: 100%;
    max-width: 654px;
}

.contact-section h4 {
    margin-top: 1.5vh;
    margin-bottom: 2.5vh;
    font-size: 16px;
    font-weight: 400;
    text-align: center;
    color: #616161;
}
.contact-section > div {
    min-width: 300px;
    padding: 15px;
    width: 100%;
    margin: 0;
}

.contact-section .text {
    max-width: 530px;
    color: #b2b2b2;
    display: inline-block;
    width: calc(100% - 45px);
    vertical-align: top;
}

.lp-sendEmail,
.lp-callUs {
    width: 100%;
    display: block;
    margin-top: 25px;
    text-align: center;
}
.lp-callUs {
    color: #4d4d49;
}

.contact-section .lp-sendEmail label {
    margin-top: 0;
    margin-bottom: 10px;
    width: 100%;
    color: #616161;
}

.contact-section .lp-sendEmail input[type="email"],
.contact-section .lp-sendEmail input[type="tel"] {
    width: 96%;
    margin: 0 4px 0 0%;
    display: inline-block;
    height: 46px;
}

.contact-section .lp-sendEmail > p,
.contact-section .tos {
    width: 50%;
    display: inline-block;
    vertical-align: top;
}

.contact-section .lp-sendEmail input[type="checkbox"] {
    display: inline-block;
    margin-right: 4px;
    vertical-align: top;
}

.contact-section .lp-sendEmail button {
    display: block;
    margin-top: 10px;
    float: none;
}

.contact-section .tos {
    font-size: 10px;
    line-height: 1.2;
}

.contact-section .lp-articleBottom-height {
    height: 274px !important;
}

.info-box {
    font-size: 8px;
    border: 1px solid #e3e3e3;
    padding: 4px;
    margin-top: 10px;
    line-height: 1.2;
}

.marketing-movie {
    display: flex;
    padding: 45px 0;
    margin: 30px 0 50px 0;
    background-color: #f4f4f4;
}

.marketing-movie > * {
    width: 50%;
    max-width: 470px;
    margin-left: auto;
}

.marketing-movie > div:nth-of-type(2) {
    margin-left: 0;
    margin-right: auto;
}

.marketing-movie p {
    padding-right: 20px;
    padding-left: 15px;
    text-align: justify;
    /*font-size:1.2em;*/
}

.marketing-movie h3 {
    color: #256ebf;
    font-size: 2em;
    padding-bottom: 15px;
    padding-right: 20px;
    padding-left: 15px;
    line-height: 1.2;
}

.histories {
    font-family: Open sans, sans-serif;
    font-size: 14px;
    color: #2e2e2b;
}
.histories p {
    font-weight: 400;
    line-height: 20px;
}

.marketing-movie svg {
    opacity: 0.7;
    content: "";
    width: 90px;
    height: 65px;
    background-image: url("/media/ytplayblue.svg");
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    display: block;
    left: calc(50% - 45px);
    top: calc(50% - 32px);
    position: absolute;
    cursor: pointer;
}

.promo-movie {
    width: 50%;
}

.promo-movie > div {
    position: relative;
    max-width: 580px;
    margin: auto;
}

.promo-movie img {
    width: calc(100% - 30px);
    margin-left: 15px;
}

.history-single {
    border-bottom: 1px solid #e3e3e3;
    padding-bottom: 50px;
}

.history-single > div {
    display: flex;
    padding: 50px 0 0px 0;
    align-items: center;
}
.history-single > div:nth-of-type(even) {
    flex-direction: column-reverse;
}

.history-single > div > * {
    width: 60%;
}

.history-single img {
    height: 250px;
    width: 250px;
    margin: auto;
    border-radius: 100%;
}

.history-single h4 {
    font-size: 1.45em;
    margin-top: 0;
    margin-bottom: 15px;
}

.read-more-text {
    display: none;
    margin-top: 20px;
    margin-bottom: 20px;
}

.lp-articleBottom-right {
    margin-top: 0px !important;
}

.histories {
    padding: 10px 0;
}

.histories .history-single:nth-of-type(odd) > div {
    padding-left: 15px;
}
.histories .history-single:nth-of-type(even) > div {
    padding-right: 15px;
    flex-direction: row-reverse;
}
.histories .history-single:nth-of-type(odd) > a {
    margin-left: calc(40% + 10px);
}

.history-single a {
    width: 80px;
    text-align: center;
    padding-left: 22px;
}
@media only screen and (min-width: 658px) {
    .marketing-movie > * {
        box-sizing: border-box;
        padding: 0 15px;
    }
}

@media only screen and (max-width: 650px) {
    .contact-section > div {
        min-width: auto;
    }

    .history-single h4 {
        font-size: 1.3em;
        margin-top: 15px;
    }

    .history-single p {
        font-size: 13px;
        line-height: 1.3;
    }

    .histories .history-single > div:nth-of-type(even) {
        flex-direction: column-reverse;
    }

    .history-single > div > * {
        width: 100%;
    }

    .history-single img {
        width: 250px;
    }

    .history-single a {
        margin-left: calc(50% - 66px);
        box-sizing: border-box;
        width: 132px;
    }

    .histories .history-single:nth-of-type(odd) > a {
        margin-left: calc(50% - 66px);
    }

    .history-single:last-of-type {
        border-bottom: 2px solid #e3e3e3;
    }

    .histories .history-single > div > div {
        padding-left: 0 !important;
        padding-right: 0;
    }

    .history-single > div {
        flex-wrap: wrap;
        padding-bottom: 20px;
        padding-left: 0px !important;
    }
}

@media only screen and (max-width: 720px) {
    .contact-full h3 {
        font-size: 20px;
    }

    .contact-full h4 {
        font-size: 16px;
    }

    .marketing-movie p {
        font-size: 0.9em;
        line-height: 1.35;
    }
    .contact-section .tos {
        font-size: 8px;
        line-height: 1.8;
        width: 100%;
    }

    .contact-section .lp-articleBottom-height {
        height: 274px !important;
    }

    .contact-section {
        flex-wrap: wrap;
        padding: 5.5vh 0;
    }
    .contact-section > div:first-of-type {
        width: 100%;
        margin: 0;
    }
    .contact-section .lp-articleBottom-right {
        width: 100%;
        margin-left: 0px;
        margin-top: 20px !important;
    }

    .marketing-movie {
        flex-wrap: wrap;
    }
    .marketing-movie > div {
        width: 100%;
    }

    .lp-newTop.lp-allCourses {
        padding-top: 50px;
        padding-bottom: 50px;
    }

    .history-single p {
        font-size: 0.9em;
        line-height: 1.35;
    }

    .lp-allCourses h2 {
        width: 62%;
    }
}

@media only screen and (max-width: 840px) {
    .lp-newTop.lp-allCourses {
        background: url("/media/climb_mobile2.jpg") no-repeat #ffffff !important;
        background-position: 0 !important;
        background-size: cover !important;
    }
    .break-here {
        display: inline;
    }

    .history-single img {
        height: 25vw;
        width: 25vw;
        margin: auto;
        border-radius: 100%;
        min-width: 200px;
        min-height: 200px;
    }

    .history-single a {
        margin-top: 20px;
    }
    .history-single.full-bio a {
        margin-top: 0px;
    }
}

@media only screen and (max-width: 450px) {
    .contact-section .lp-sendEmail > p {
        width: 100%;
    }
    .contact-section .lp-articleBottom-height {
        height: auto !important;
    }

    #site-content .lp-newTop.lp-allCourses {
        background-position: -30px 0px !important;
    }

    .marketing-movie p span {
        display: none;
    }

    .marketing-movie h3 {
        font-size: 1.8em;
    }

    .history-single p {
        font-size: 12px;
    }

    .history-single img {
        width: 200px;
        height: 200px;
    }

    .histories .full-bio.history-single:nth-of-type(odd) > img {
        /*margin-right:15px;*/

        transition-property: all;
        transition-duration: 0.55s;
        transition-timing-function: cubic-bezier(0, 1, 0.5, 1);
        -webkit-animation: none;
        animation: none;
    }

    .histories .full-bio.history-single:nth-of-type(even) > img {
        /*margin-right:15px;*/

        transition-property: all;
        transition-duration: 0.55s;
        transition-timing-function: cubic-bezier(0, 1, 0.5, 1);
        -webkit-animation: none;
        animation: none;
    }
}

@media only screen and (max-width: 375px) {
    #site-content .lp-newTop.lp-allCourses {
        background-position: -95px 10px !important;
    }
    .lp-allCourses h2 {
        width: 72%;
    }
}

@media only screen and (max-width: 985px) {
    .lp-courseImg8 {
        background: url("/media/imgCourse-ExcelNew-mobile.jpg") no-repeat;
    }
    .lp-newTop.lp-allCourses {
        background: url("/media/climb.jpg") no-repeat #ffffff;
        background-position: 0;
        background-size: cover;
    }
}

@media only screen and (max-width: 720px) {
    #wszystkie-kursy .lp-courseSingle {
        width: 100%;
        max-width: 450px;
        margin: 0 auto 20px auto;
        display: block;
    }
}

@media only screen and (max-width: 530px) {
    .lp-allCourses h1 {
        width: 80%;
    }
    .marketing-movie {
        padding: 25px 0;
    }
    .lp-courseSingle h2 {
        min-height: auto;
    }
    .lp-courseSingle {
        padding: 15px;
    }
    .lp-courseSingleImg {
        width: calc(100% + 30px);
        margin: 15px 0;
        margin-left: -15px;
    }
    .lp-courseSingle p {
        margin-bottom: 10px;
    }
    .lp-courseSteps li:nth-child(1),
    .lp-courseSteps li:nth-child(2),
    .lp-courseSteps li:nth-child(3) {
        background-position: 50% -60px !important;
        background-size: 400px !important;
    }
    .lp-courseSteps li:nth-child(1),
    .lp-courseSteps li:nth-child(2),
    .lp-courseSteps li:nth-child(3) {
        padding-top: 200px !important;
    }
}

.lp-courseSingle h2 a {
    color: #2a3642;
    text-decoration: none;
}
.lp-courseSingleImg a {
    width: 100%;
    height: 100%;
    display: block;
}

/* HOT FIX */

.lp-courseSingle {
    padding: 10px;
    width: calc(33% - 10px);
    margin-right: 15px;
}
.lp-courseSingle:last-of-type {
    margin-right: 0;
}
.lp-courseSingleImg {
    width: calc(100% + 20px) !important;
    margin-left: -10px;
    margin-top: 10px;
    margin-bottom: 10px;
    height: 158px;
    background-size: cover !important;
}
.lp-courseSingleTime {
    margin-top: 10px;
}

/*.lp-minHeightAuto h2 { min-height: auto; }*/

/* przycisk po prawej */
.lp-courseSingleTime {
    width: 50%;
    display: inline-block;
}

.lp-courseSingle {
    position: relative;
    height: 405px;

    vertical-align: top;
}

.lp-courseSingle .lp-btn-blue {
    position: absolute;
    bottom: 9px;
    right: 9px;
}

.nauka {
    background: url("/media/18-nauka_VWq5qpz.jpg") no-repeat center;
    background-size: 290px auto;
    margin: 30px 0;
}

.left-img {
    background-position: 0px;
    padding-left: 300px;
    padding-top: 30px;
    padding-bottom: 30px;
    min-height: 250px;
    height: auto;
}

@media only screen and (max-width: 740px) {
    .left-img {
        background-size: 60%;
        padding-left: 270px;
        background-size: 260px auto;
        padding-top: 30px;
    }
}

@media only screen and (max-width: 530px) {
    .left-img h2 {
        min-height: auto;
        float: none;
        display: block;
    }

    .brain {
        background: url("/media/20error22.png") no-repeat center 20px;
    }

    .znak {
        background: url("/media/19selfie.jpg") no-repeat center 20px;
    }

    .haker {
        background: url("/media/20maszyna2.png") no-repeat center 20px;
        display: block;
    }

    .nauka {
        background: url("/media/18-nauka_VWq5qpz.jpg") no-repeat center 20px;
    }

    .left-img {
        display: block;
        height: auto;
        padding-left: 0px;
        padding-top: 270px;
        padding-bottom: 0px;
    }
}

.lp-phpshadowBottom {
    display: block;
    position: relative;
    top: -11px;
    z-index: 1;
    margin: 0;
    width: 100%;
    height: 15px;
    background: url("/media/shadowBottomNew.png") no-repeat;
    background-size: 100% auto;
}

@media only screen and (max-width: 360px) {
    .left-img h2 {
        min-height: auto;
        float: none;
        display: block;
    }

    .left-img {
        height: auto;
        display: block;
        padding-left: 0px;
        padding-top: 230px;
        background-size: auto 200px;
    }
}

@media only screen and (max-width: 985px) {
    .lp-minHeightAutoMobile h2 {
        min-height: auto;
    }
}

@media only screen and (min-width: 531px) and (max-width: 616px) {
    .lp-minHeightAutoMobile.lp-minHeightAutoSmallMobile h2 {
        min-height: 42px;
    }
}

@media only screen and (max-width: 850px) {
    .lp-courseRefLeft {
        margin: 50px auto 5px !important;
    }
}

@media only screen and (min-width: 531px) {
    .lp-courseSingle p {
        min-height: 90px;
    }
}
/* HOT FIX */

/* HOT FIX - TRENERZY */
.lp-mobileCoachBtn {
    display: none;
    margin: 20px auto 0;
    text-align: center;
}

@media only screen and (max-width: 580px) {
    .lp-mobileCoachBtn {
        display: block;
    }
    .lp-courseWork h3,
    .lp-courseWork p {
        width: 100% !important;
    }

    .lp-homepageCoachWrap ul li:nth-child(6),
    .lp-homepageCoachWrap ul li:nth-child(7),
    .lp-homepageCoachWrap ul li:nth-child(8),
    .lp-homepageCoachWrap ul li:nth-child(9),
    .lp-homepageCoachWrap ul li:nth-child(10) {
        display: none;
    }

    .lp-homepageCoachWrap ul {
        padding: 0;
    }
    .lp-homepageCoachWrap ul li {
        padding: 0;
    }
    .lp-homepageCoachWrap .lp-coachImageWrap {
        width: 80px;
        float: left;
    }
    .lp-homepageCoachWrap .lp-coachImage {
        width: 80px;
        height: 80px;
        margin: 0;
    }
    .lp-homepageCoachWrap .lp-coachName {
        width: calc(100% - 120px);
        text-align: left;
        margin: 0;
        padding-left: 20px;
        box-sizing: border-box;
        display: table-cell;
        height: 80px;
        vertical-align: middle;
    }
}

/* END HOT FIX - TRENERZY */

/* HOT FIX - REFERENCJE */
@media only screen and (max-width: 580px) {
    .lp-courseAllRef .lp-courseRefLeft {
        float: left !important;
        margin-top: 18px !important;
        margin-right: 6px !important;
    }
    .lp-courseAllRef .lp-courseRefRight {
        background-position: right bottom;
    }
    .lp-courseAll-ref p {
        margin-bottom: 10px;
    }
    .lp-courseAllRef .lp-courseRef {
        margin-bottom: 20px !important;
    }
}
/* END HOT FIX - REFERENCJE */

.lp-courseAll-ref .lp-recoManRight.lp-recoImgA,
.lp-courseAll-ref .lp-recoManRight.lp-recoImgB,
.lp-courseAll-ref .lp-recoManRight.lp-recoImgC,
.lp-courseAll-ref .lp-recoManRight.lp-recoImgD {
    background: url("/media/avatary.png") 0 0 no-repeat;
}

.lp-courseSingleTime {
    display: none;
}
.lp-courseSingle .lp-btn-blue {
    position: relative;
    bottom: 0;
    right: 0;
    display: block;
    margin: 10px auto 0;
    width: 40px;
}

.lp-courseImg9 {
    background: url("/media/imgCourse-JSNew.jpg") no-repeat;
}
.lp-courseImg10 {
    background: url("/media/imgCourse-PythonNew.jpg") no-repeat;
}

.lp-yt-movie {
    width: 100%;
    display: block;
    margin-bottom: 40px;
}
.lp-yt-movie iframe {
    width: 100%;
    min-height: 505px;
}

h2 span.lp-span {
    margin-left: 0;
    font-size: 14px;
    letter-spacing: 0;
    display: block;
}

@media only screen and (max-width: 985px) {
    .lp-courseImg9 {
        background: url("/media/imgCourse-JSNew-mobile.jpg") no-repeat;
    }
    .lp-courseImg10 {
        background: url("/media/imgCourse-PythonNew-mobile.jpg") no-repeat;
    }

    h2 span.lp-span {
        display: inline-block;
    }
}

.lp-allCourses h1 {
    font-size: 26px;
}

.lp-courseImg1 {
    background: url("/media/21min1.jpg") no-repeat;
    background-size: auto auto;
}
.lp-courseImg2 {
    background: url("/media/21min2.jpg") no-repeat;
    background-size: auto auto;
}
.lp-courseImg10 {
    background: url("/media/21min31.jpg") no-repeat;
    background-size: auto auto;
}

.ticklist li::before {
    content: "✔";
    font-size: 18px;
    color: #99c6b9;
    margin-right: 0.5em;
}
.ticklist ul {
    text-indent: 2px;
    list-style-type: none;
    margin-left: 4px;
    list-style-position: outside;
}

.ticklist li {
    color: #2e2e2b;
}

.lp-newTop-content .ticklist li {
    font-family: "Open sans", sans-serif;
    font-size: 22px;
    line-height: 29px;
    font-weight: 300;
    color: #2a3642;
}

.od-juniora::after {
    content: " ";
    width: 100%;
    height: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
    background: url("/media/21piano3.jpg") no-repeat;
    background-size: auto 100%;
}

.lp-courseSteps li:nth-child(1) {
    background: url("/media/21robot8.png") no-repeat;
    background-position-x: 0%;
    background-position-y: 0%;
    background-position: -75px -25px;
}

.lp-courseSteps li:nth-child(2) {
    background: url("/media/21learn2.png") no-repeat;
    background-position-x: 0%;
    background-position-y: 0%;
    background-position: -75px -25px;
}

.lp-courseSteps li:nth-child(3) {
    background: url("/media/21teach1.png") no-repeat;
    background-position-x: 0%;
    background-position-y: 0%;
    background-position: -75px -25px;
}

.lp-courseWork::after {
    position: absolute;
    right: 0;
    bottom: 0;
    content: " ";
    display: block;
    width: 775px;
    height: 250px;
    background: url("/media/21kidsatwork2.png") no-repeat;
}

.lp-courseWork {
    position: relative;
    width: 100%;
    padding: 20px;
    margin-bottom: 50px;
    border: 3px solid #e9e9e9;
    box-sizing: border-box;
}
.lp-courseWork::after {
    width: 100%;
    background-position: right bottom;
}

.lp-courseSingle h2 {
    min-height: 60px;
}

@media only screen and (max-width: 985px) {
    .od-juniora::after {
        content: " ";
        width: 100%;
        height: 344px;
        position: absolute;
        top: 0;
        bottom: auto;
        left: 0;
        background: url("/media/21piano3.jpg") no-repeat;
        background-size: auto;
        background-position: auto 50% !important;
    }
    .od-juniora > h3 {
        padding-top: 290px !important;
    }

    .od-juniora > * {
        width: 98%;
        padding: 0 1% !important;
    }
}

@media only screen and (max-width: 530px) {
    .od-juniora > h3 {
        padding-top: 1% !important;
    }
}

#showHideBtn1,
#showHideBtn2,
#showHideBtn3,
#showHideBtn4 {
    display: none;
    background: transparent;
    border: 0;
    margin: 10px 0 0;
    padding: 0;
    color: #7d7d7d;
    font-size: 11px;
    text-transform: uppercase;
    font-weight: 700;
}
#hiddenReco1,
#hiddenReco2,
#hiddenReco3,
#hiddenReco4 {
    display: block;
}

@media only screen and (max-width: 580px) {
    #showHideBtn1,
    #showHideBtn2,
    #showHideBtn3,
    #showHideBtn4 {
        display: block;
    }
    #hiddenReco1,
    #hiddenReco2,
    #hiddenReco3,
    #hiddenReco4 {
        display: none;
    }

    .lp-courseAll-ref .lp-recoManRight.lp-recoImgA,
    .lp-courseAll-ref .lp-recoManRight.lp-recoImgB,
    .lp-courseAll-ref .lp-recoManRight.lp-recoImgC,
    .lp-courseAll-ref .lp-recoManRight.lp-recoImgD {
        background: url("/media/avatary.png") 0 0 no-repeat;
        position: absolute;
        top: inherit !important ;
        left: 28px !important;
        bottom: 0 !important;
        width: 60px;
        height: 81px;
        background-size: 100%;
        margin-left: 0 !important;
    }

    .lp-courseAll-ref .lp-recoManLeft {
        padding-bottom: 50px;
    }
    .lp-recoManLeft {
        margin: 0 auto 0;
    }
    .lp-courseAll-ref .lp-recoMan {
        margin: 20px 0 !important;
    }
    .lp-courseAll-ref .lp-recoManLeft p.lp-recoManPodpis {
        position: relative;
        left: 68px;
        top: 9px;
        width: calc(100% - 75px);
    }

    .lp-recoTriangle {
        display: none !important;
    }
}

#graduate-stories {
    .lp-newTopZbiorczy {
        .lp-newTop-header {
            h1 {
                font-size: 26px;
                width: 55%;
                margin-bottom: 15px;
                font-family: "Open sans", sans-serif;
                line-height: 30px;
                font-weight: 600;
                color: white;
            }
            h2 {
                width: 65%;
                min-height: 40px;
                margin-bottom: 40px;
                font-family: "Open sans", sans-serif;
                font-size: 22px;
                line-height: 29px;
                font-weight: 300;
                color: white;
            }
        }
        ul{
            li {
                color: #2a3642;
            }
        }
    }
    .marketing-movie {
        .new-video-desc {
            width: 40%;
            max-width: 370px;
            position: relative;
            &:after {
                border-bottom: 4px solid #256ebf;
                width: calc(100% - 60px);
                display: block;
                content: "";
                margin: auto;
                position: absolute;
                bottom: 4px;
                left: 30px;
            }
        }
        .promo-movie {
            width: 60%;
            max-width: 570px;
        }
        h3 {
            color: black;
            font-size: 1.35em;
            padding-bottom: 15px;
            padding-right: 20px;
            padding-left: 15px;
            line-height: 1.2;
            font-weight: 600;
        }
    }
    .histories {
        .vertical-history p:nth-of-type(2), .horizontal-history p:nth-of-type(2){
            font-size: 14px;
            line-height: 1.35;
            text-align: justify;
            min-height: 300px;
            font-weight: 400;
        }
        .horizontal-history img {
            width: 100%;
        }
    }
}



@media only screen and (max-width: 768px) {
    .histories .vertical-history p:nth-of-type(2),
    .histories .horizontal-history p:nth-of-type(2) {
        min-height: 0;
        padding-bottom: 30px;
    }
    .histories .vertical-history {
        margin-bottom: 8vh;
    }

    .histories .vertical-history:last-of-type {
        margin-bottom: 0;
    }

    .marketing-movie {
        margin-top: 0;
    }

    .promo-movie,
    .new-video-desc {
        width: 100%;
    }

    #site-content .marketing-movie > * {
        margin-left: auto;
        margin-right: auto;
    }

    .new-video-desc {
        max-width: 570px;
        margin-top: 3vh;
    }

    .horizontals > .horizontal-history {
        flex-wrap: wrap;
    }

    .horizontal-history > * {
        margin-left: auto !important;
        margin-right: auto !important;
        margin-top: 10px;
    }

    .histories .graduates .histories-slider {
        padding: 15px 0;
    }
}

.histories-slider .slick-prev {
    left: 0px;
}

.histories-slider .slick-next {
    right: 0px;
}

@media only screen and (max-width: 985px) {
    #graduate-stories .lp-newTopZbiorczy.lp-newTopStatic {
        background: url("/media/climb.jpg") no-repeat #ffffff;
        background-position: 0;
        background-size: cover;
    }
}
@media only screen and (max-width: 840px) {
    #graduate-stories .lp-newTopZbiorczy.lp-newTopStatic {
        background: url("/media/climb_mobile2.jpg") no-repeat #ffffff !important;
        background-position-x: 0%;
        background-position-y: 0%;
        background-size: auto;
        background-position: 0 !important;
        background-size: cover !important;
    }
}

@media only screen and (max-width: 720px) {
    #graduate-stories .lp-newTopZbiorczy.lp-newTopStatic h2 {
        width: 62%;
    }
    .histories .graduates {
        padding: 10px 0;
    }

    .horizontal-history img {
        width: 100%;
        max-width: 570px;
    }
}

@media only screen and (max-width: 530px) {
    #graduate-stories .lp-newTopZbiorczy.lp-newTopStatic h1 {
        font-size: 26px;
    }
    #graduate-stories .lp-newTopZbiorczy.lp-newTopStatic h1 {
        width: 80%;
    }
    #graduate-stories .lp-newTopZbiorczy.lp-newTopStatic h1 {
        font-size: 23px;
        line-height: 1.3em;
        width: 90%;
    }

    #graduate-stories .lp-newTopZbiorczy.lp-newTopStatic h2 {
        font-size: 16px;
        line-height: 1.3em;
        width: 90%;
    }
    #graduate-stories .lp-newTopZbiorczy.lp-newTopStatic {
        padding-left: 20px;
    }
}
@media only screen and (max-width: 450px) {
    #site-content .lp-newTopZbiorczy.lp-newTopStatic {
        background-position: -30px 0px !important;
    }
}
@media only screen and (max-width: 375px) {
    #graduate-stories .lp-newTopZbiorczy.lp-newTopStatic h2 {
        width: 72%;
    }
    #graduate-stories .lp-newTopZbiorczy.lp-newTopStatic {
        background-position: -95px 10px !important;
    }
}

#graduate-stories .lp-newTopZbiorczy h1 {
    font-size: 26px;
    width: 55%;
    margin-bottom: 15px;
    font-family: "Open sans", sans-serif;
    line-height: 30px;
    font-weight: 600;
    color: #1c262f;
}

.lp-newTopZbiorczy .lp-newTop-header p {
    margin: 15px 0;
}

.new-video-desc:after {
    border-bottom: 4px solid #256ebf;
    width: calc(100% - 60px);
    display: block;
    content: "";
    margin: auto;
    position: absolute;
    bottom: 4px;
    left: 30px;
}

.new-video-desc {
    width: 40%;
    max-width: 370px;
    position: relative;
}

.promo-movie {
    width: 60%;
    max-width: 570px;
}

.marketing-movie .new-video-desc h3 {
    color: black;
    font-size: 1.35em;
    padding-bottom: 15px;
    padding-right: 20px;
    padding-left: 15px;
    line-height: 1.2;
    font-weight: 600;
}

.histories .vertical-history p:nth-of-type(2),
.histories .horizontal-history p:nth-of-type(2) {
    font-size: 14px;
    line-height: 1.35;
    text-align: justify;
    min-height: 180px;
    font-weight: 400;
}

.histories .horizontal-history p:nth-of-type(2) {
    min-height: 100px;
    padding-bottom: 15px;
}

.horizontal-history p.st-name {
    margin-bottom: 25px;
}

.horizontal-history img {
    width: 100%;
}

@media only screen and (max-width: 768px) {
    .histories .vertical-history p:nth-of-type(2),
    .histories .horizontal-history p:nth-of-type(2) {
        min-height: 0;
        padding-bottom: 30px;
    }
    .histories .vertical-history {
        margin-bottom: 8vh;
    }

    .histories .vertical-history:last-of-type {
        margin-bottom: 0;
    }

    .marketing-movie {
        margin-top: 0;
    }

    .promo-movie,
    .new-video-desc {
        width: 100%;
    }

    #site-content .marketing-movie > * {
        margin-left: auto;
        margin-right: auto;
    }

    .new-video-desc {
        max-width: 570px;
        margin-top: 3vh;
    }

    .horizontals > .horizontal-history {
        flex-wrap: wrap;
    }

    .horizontal-history > * {
        margin-left: auto !important;
        margin-right: auto !important;
        margin-top: 10px;
    }

    .histories .graduates .histories-slider {
        padding: 15px 0;
    }
}

.histories-slider .slick-prev {
    left: 0px;
}

.histories-slider .slick-next {
    right: 0px;
}

.horizontal-history > a {
    margin: 30px 20px;
}

@media only screen and (max-width: 985px) {
    #graduate-stories .lp-newTopZbiorczy.lp-newTopStatic {
        background: url("/media/climb.jpg") no-repeat #ffffff;
        background-position: 0;
        background-size: cover;
    }
}
@media only screen and (max-width: 840px) {
    #graduate-stories .lp-newTopZbiorczy.lp-newTopStatic {
        background: url("/media/climb_mobile2.jpg") no-repeat #ffffff !important;
        background-position-x: 0%;
        background-position-y: 0%;
        background-size: auto;
        background-position: 0 !important;
        background-size: cover !important;
    }
}

@media only screen and (max-width: 720px) {
    #graduate-stories .lp-newTopZbiorczy.lp-newTopStatic h2 {
        width: 62%;
    }
    .histories .graduates {
        padding: 10px 0;
    }

    .horizontal-history img {
        width: 100%;
        max-width: 570px;
    }
}

@media only screen and (max-width: 530px) {
    #graduate-stories .lp-newTopZbiorczy.lp-newTopStatic h1 {
        font-size: 26px;
    }
    #graduate-stories .lp-newTopZbiorczy.lp-newTopStatic h1 {
        width: 80%;
    }
    #graduate-stories .lp-newTopZbiorczy.lp-newTopStatic h1 {
        font-size: 23px;
        line-height: 1.3em;
        width: 90%;
    }

    #graduate-stories .lp-newTopZbiorczy.lp-newTopStatic h2 {
        font-size: 16px;
        line-height: 1.3em;
        width: 90%;
    }
    #graduate-stories .lp-newTopZbiorczy.lp-newTopStatic {
        padding-left: 20px;
    }
}
@media only screen and (max-width: 450px) {
    #graduate-stories #site-content .lp-newTopZbiorczy.lp-newTopStatic {
        background-position: -30px 0px !important;
    }
}
@media only screen and (max-width: 375px) {
    .lp-newTopZbiorczy.lp-newTopStatic h2 {
        width: 72%;
    }
    .lp-newTopZbiorczy.lp-newTopStatic {
        background-position: -95px 10px !important;
    }
}


.histories .horizontal-history p:nth-of-type(2) {
    min-height: 100px;
    padding-bottom: 15px;
}

.horizontal-history p.st-name {
    margin-bottom: 25px;
}

.graduates .the_name {
    width: 100%;
    text-align: center;
}

.histories .vertical-history p:nth-of-type(2) {
    min-height: 180px;
}

.the_name span {
    background-color: white;
    margin-top: -3px;
    margin-bottom: -3px;
    padding: 5px 6px;
    display: inline-block;
}

.lp-btn-blue:hover {
    background: #3288e8;
}

p.st-name:hover {
    border: 2px solid #3288e8;
}

.histories-slider a {
    margin: 0px;
}

#graduate-stories a img:hover {
    opacity: 0.85;
}

.important {
    margin: 30px auto;
}

@media only screen and (max-width: 839px) {
    #graduate-stories .lp-newTop.lp-newTopZbiorczy {
        padding-top: 200px;
    }
}

@media only screen and (max-width: 768px) {
    .histories .vertical-history p:nth-of-type(2) {
        min-height: 0px;
    }

    #graduate-stories .lp-newTop.lp-newTopZbiorczy {
        padding-top: 150px;
    }

    .quote {
        padding: 8px 30px;
    }
}

@media only screen and (max-width: 530px) {
    #graduate-stories .lp-newTopZbiorczy.lp-newTopStatic {
        box-sizing: border-box;
    }
}

@media only screen and (max-width: 420px) {
    #graduate-stories .lp-newTopZbiorczy.lp-newTopStatic {
        background: url("/media/climb_mobile2.jpg") no-repeat #ffffff !important;
        background-size: auto;
        background-position: -155px 0 !important;
        background-size: auto 100% !important;
    }
}

@media only screen and (max-width: 350px) {
    #graduate-stories .lp-newTopZbiorczy.lp-newTopStatic {
        background-position: -240px 0 !important;
    }
}
