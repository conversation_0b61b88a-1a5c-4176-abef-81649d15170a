h2.contact-title{
    font-size: 20px;
    @media all and (min-width: 768px) {
        font-size: 24px;
    }
}
.lp-contactbox {
    position: relative;
    width: 100%;
    padding: 8px;
    margin: 20px 0 50px 0;
    box-sizing: border-box;
    border: 1px solid #eaeaea;
    &:after {
        content: '';
        background: url('https://alx.training/media/contact_cloud.png') no-repeat;
        width: 349px;
        height: 238px;
        max-width: 90vw;
        background-size: contain;
        display: block;
        position: absolute;
        top: 0;
        left: 10px;
    }
    @media all and (min-width: 768px) {
        padding: 16px 16px 16px 66px;
        display: flex;
    }
    .lp-contactbox-left {
        z-index: 7;
        width: 100%;
        position: relative;
        @media all and (min-width: 768px) {
            width: 60%;
        }
        h4 {
            font-size: 33px;
            font-weight: 300;
            letter-spacing: -1px;
            margin: 0;
            &:after {
                content: " ";
                display: block;
                width: 36px;
                height: 2px;
                margin: 15px 0 0 0;
                background: #4682b4;
            }
            span {
                font-size: 22px;
            }
        }
        p {
            margin: 15px 0 0 0;
            font-size: 12px;
            line-height: 15px;
            font-weight: 400;
            color: #444444;
        }
    }
    .lp-contactbox-right {
        z-index: 7;
        width: 100%;
        position: relative;
        @media all and (min-width: 768px) {
            width: 40%;
        }
        p {
            margin: 0 0 20px 0;
            line-height: 1.3em;
            font-size: 14px;
            font-weight: 400;
            &:last-child {
                margin: 0;
            }
            a {
                display: block;
                text-decoration: none;
                font-weight: 600;
                &:hover {
                    text-decoration: underline;
                }
            }
        }
    }
}

.lp-contactlocation {
    font-size: 13px;
    line-height: 18px;
    @media all and (min-width: 768px) {
        display: flex;
    }
    .lp-contactlocation-single {
        box-sizing: border-box;
        margin: 0 0 40px 0;
        @media all and (min-width: 768px) {
            padding: 0 30px;
            border-left: 1px solid #f0f0f0;
        }
        &.ireland .lp-phone {
            margin: 15px 0 0;
        }
        a {
            display: block;
            margin: 16px 0 0 0;
            font-size: 12px;
            color: #076a8f;
            text-transform: uppercase;
            font-weight: 700;
            letter-spacing: -0.5px;
            text-decoration: none;
            &:before {
                content: " ";
                display: block;
                width: 20px;
                height: 2px;
                margin: 0 0 16px 0;
                background: #4682b4;
            }
            &:hover {
                text-decoration: underline;
            }
        }
        h5, h4 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
        }
        p {
            margin: 0;
            letter-spacing: -0.4px;
            b {
                font-weight: 600;
            }
        }
        .lp-address {
            margin: 15px 0 0;
        }
        .lp-phone {
            margin: 10px 0 0;
        }
    }
}

.lp-contactmap {
    width: 100%;
    text-align: center;
    img {
        margin: 0 0 65px 0;
    }
}

.lp-otherinformation-p {
    p {
        font-size: 13px;
        line-height: 18px;
        margin: 10px 0 0 0;
    }
    b {
        font-weight: 600;
    }
    .lp-otherinformation-wrap {
        .lp-otherinformation-p {
            /* float: left;
            width: 50%; */
            margin: 10px 0 70px 0;
        }
    }
}
