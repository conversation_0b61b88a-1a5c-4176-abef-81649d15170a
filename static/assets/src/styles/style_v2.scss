.courses-data,
.filtr_najblizsze.form {
	min-width: 100%;
	max-width: 100%;
}

@media only screen and (max-width: 800px) {
	.filtr_najblizsze.form {
		margin: 0;
	}
}

@media only screen and (max-width: 650px) {
	.filtr_najblizsze.form .courses-data {
		display: block;
	}

	.filtr_najblizsze.form .courses-data tbody {
		display: flex;
		flex-wrap: wrap;
		width: 100%;
	}

	.filtr_najblizsze.form table.courses-data tr {
		width: 50%;
		border-bottom: 2px white solid;
	}
}

@media only screen and (max-width: 450px) {
	.filtr_najblizsze.form table.courses-data tr {
		width: 100%;
		border-bottom: none;
	}

	.filtr_najblizsze.form .field select {
		width: calc(100% - 10px);
		min-width: 120px;
	}

	.filtr_najblizsze.form .field-option span.label {
		width: 100%;
	}

	.filtr_najblizsze.form .field-option {
		width: calc(50% - 4px);
		display: inline-block;
	}
}

#content-header .nav a:focus {
	color: #000;
	border: none;
}

#content-header .current a {
	color: #b12222 !important;
	border-bottom: 2px solid #b12222 !important;
}

@media only screen and (max-width: 768px) {
	#content-header .nav a:hover {
		color: #000;
		border: none;
	}
}


@media only screen and (max-width: 1020px) {


	.current a:hover:link {
		color: #000;
		border: none;
	}
}

@media only screen and (min-width: 530px) {
	.course_suggest {
		padding-left: 65px;
	}
}

@media only screen and (min-width: 580px) {
	.course_suggest {
		padding-left: 40px;
	}
}


@media only screen and (max-width: 580px) {

	.fieldset ul li {
		position: relative;
	}

	input#id_akceptuje_regulamin,
	input#id_chce_zapisac_sie_na_newsletter {
		top: 8px;
		left: 10px;
	}

	input#id_chce_zapisac_sie_na_newsletter {
		top: 29px;
	}

	label[for="id_chce_zapisac_sie_na_newsletter"]+small {
		padding-left: 40px !important;
		box-sizing: border-box;
		display: block;
		width: 90% !important;
	}


	#simplemodal-container {
		position: fixed;
		z-index: 1002;
		width: 100% !important;
		left: 0 !important;
		top: 0 !important;
		bottom: 0 !important;
		min-height: 100% !important;
		height: 100% !important;
		overflow: hidden;
		padding-bottom: 25px;
	}

	#simplemodal-container .simplemodal-wrap {
		overflow-y: scroll !important;
		padding-bottom: 25px;
		margin-bottom: 20px !important;
	}
}

.js_youtubepromo_phpPiotr_play svg {
	max-width: 90px !important;
}

.histories-slider .slick-prev {
	left: -8px;
}

.histories-slider .slick-next {
	right: -8px;
}


@media screen and (min-width: 576px) and (max-width: 839px) {
	.lp-newTop.lp-courseTop {
		padding-top: 100px !important;
		overflow-x: hidden;
	}
}

.text-main {
	margin-top: 4vh;
}

.lp-newTop-coach.slick-initialized ul.slick-dots {
	display: flex;
	justify-content: center;
	margin-left: 0px;
	text-align: center;
}

.lp-newTop-coach.slick-initialized .slick-dots li {
	position: relative;
	display: inline-block;
	width: 15px;
	height: 15px;
	margin: 0px 1px;
	padding: 0;
	cursor: pointer;
}

.lp-newTop-coach.slick-initialized .slick-dots li button {
	font-size: 0;
	line-height: 0;
	display: block;
	width: 20px;
	height: 20px;
	padding: 5px;
	cursor: pointer;
	color: transparent;
	border: 0;
	outline: none;
	background: transparent;
}

.lp-newTop-coach.slick-initialized .slick-dots li button:before {
	content: '•';
	font-size: 30px;
	line-height: 30px;
	position: absolute;
	top: -12px;
	left: 0;
	width: 15px;
	height: 15px;
	text-align: center;
	/*opacity: .25;*/
	color: #858484;
}

.lp-newTop-coach.slick-initialized .slick-dots li.slick-active button:before {
	color: #dad9d9;
}


.clearfix:after {
	visibility: hidden;
	display: block;
	font-size: 0;
	content: " ";
	clear: both;
	height: 0;
}

/* Prawa kolumna */
.lp-rightSection-wrap {
	width: 100%;
	margin-bottom: 20px;
	border: 1px solid #e9e9e9;
	color: #394448;
	background: #f7f7f7;

	img {
		width: 100%;
	}
}

.lp-rightSection-header {
	width: 100%;
	padding: 15px;
	border-bottom: 1px solid #e9e9e9;
	background: #f0f0f0;
	box-sizing: border-box;
}

.lp-rightSection-header h3 {
	display: inline-block;
	font-size: 15px;
	font-weight: 600;
	line-height: 1em;
	color: #434343;
	letter-spacing: -0.5px;
}

.lp-rightSection-header a {
	float: right;
	font-family: Arial, sans-serif;
	font-size: 11px;
	color: #727272;
	text-decoration: none;
}

.lp-rightSection-header a:hover {
	text-decoration: underline;
}

.lp-rightSection-content {
	padding: 18px 15px;
	font-size: 13px;
	line-height: 16px;
	box-sizing: border-box;
}

.lp-rightSection-contact {
	background: url("../img/contact-bg.png") 115px -20px no-repeat;
}

.lp-rightSection-contact p {
	margin: 0 0 10px 0;
	font-weight: 600;
	line-height: 14px;
}

.lp-rightSection-contact p:last-child {
	margin: 0;
}

.lp-rightSection-contact p small {
	display: block;
	font-size: 11px;
	font-family: Arial, sans-serif;
	font-weight: normal;
	color: #727272;
}

.lp-rightSection-contact p a {
	color: #394448;
	text-decoration: none;
}

.lp-rightSection-contact p a:hover {
	text-decoration: underline;
}

.lp-rightSection-location {
	padding: 0;
}

.lp-rightSection-location ul {
	margin: 0;
	padding: 0;
	list-style: none;
}

.lp-rightSection-location li {
	position: relative;
	padding: 0;
	font-family: Arial, sans-serif;
	color: #676767;
	border-bottom: 1px solid #e5e5e5;
}

.lp-rightSection-location li:last-child {
	border-bottom: 0;
}

.lp-rightSection-location a {
	display: block;
	padding: 15px;
	text-decoration: none;
	color: #676767;
}

.lp-rightSection-location a:hover {
	background: #f2f2f2;
}

.lp-rightSection-location h4 {
	display: block;
	margin: 0 0 2px 0;
	font-family: Open sans, Arial, sans-serif;
	font-size: 13px;
	font-weight: 600;
	color: #394448;
	letter-spacing: -0.4px;
}

.lp-rightSection-location p {
	width: 85%;
	margin: 0;
	padding: 0;
}

.lp-location-icon {
	position: absolute;
	top: 50%;
	right: 15px;
	display: block;
	width: 14px;
	margin-top: -10px;
	height: 19px;
	overflow: hidden;
	text-indent: 2000px;
	background: url("../img/mini-sprite.png") -40px 0 no-repeat;
}

.lp-rightSection-gallery {
	width: 100%;
	height: 200px;
	overflow: hidden;
	padding: 0;
	line-height: 1em;
}

.lp-rightSection-gallery img {
	margin: 0;
	padding: 0;
	line-height: 1em;
}

.lp-rightSection-clients {
	text-align: center;
}

.lp-rightSection-coach {
	padding: 0;
}

.lp-rightSection-coach ul {
	display: table;
	width: 100%;
	margin: 0;
	padding: 0;
	list-style: none;
}

.lp-rightSection-coach li {
	border-bottom: 1px solid #e5e5e5;
}

.lp-rightSection-coach li:last-child {
	border: 0;
}

.lp-rightSection-coach li a {
	display: table;
	padding: 15px;
	height: 100%;
}

.lp-coachImageWrap {
	display: table-cell;
	height: 100%;
	vertical-align: middle;
}

.lp-coachImage {
	width: 66px;
	height: 66px;
	float: left;
	overflow: hidden;
	border-radius: 50%;
	text-align: center;
}

.lp-coachImage img {
	width: 100%;
}

.lp-coachName {
	float: left;
	margin: 0 0 0 13px;
	padding: 0;
	width: 111px;
	height: 66px;
	display: table;
}

.lp-coachName p {
	margin: 0;
	padding: 0;
	display: table-cell;
	vertical-align: middle;
}

.lp-coachName b {
	display: block;
	margin: 0 0 2px 0;
	font-family: Open sans, Arial, sans-serif;
	font-size: 13px;
	font-weight: 600;
	color: #394448;
	letter-spacing: -0.4px;
}

.lp-coachName small {
	display: block;
	line-height: 1em;
	font-family: Arial, sans-serif;
	color: #676767;
}

.lp-setNotification {
	width: 169px;
	margin: -15px auto 35px;
	font-family: Arial, sans-serif;
	font-size: 12px;
	line-height: 1.3em;
	box-sizing: border-box;
}

.lp-setNotification a {
	display: block;
	color: #717171;
}

.lp-setNotification a:before {
	float: left;
	content: " ";
	display: block;
	margin: 3px 8px 0 0;
	width: 33px;
	height: 27px;
	background: url("../img/mini-sprite.png") 0 0 no-repeat;
}

/* Nawigacja */
#meta-content:before {
	display: none;
}



.lp-left-stickyHeader {
	display: none;
	width: 30%;
	position: absolute;
	top: 0;
	left: 0;
	margin: 13px 0 0 0;
}

.lp-left-stickyHeader h4 {
	float: left;
	display: block;
	width: 40%;
	margin: 0 0 0 15px;
	padding: 0 0 0 15px;
	font-size: 17px;
	letter-spacing: -0.6px;
	font-weight: 300;
	color: #394448;
	border-left: 1px solid #b4b4b4;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.lp-stickyHeader-logo {
	float: left;
	display: block;
	width: 62px;
	height: 21px;
	margin-left: 20px;
	text-indent: -3000px;
	overflow: hidden;
	background: url("../img/logo-alx-black.png") no-repeat;
	background-size: 100%;
	position: relative;
	top: 4px;
}

.fixed-header {
	@media all and (min-width: 1024px) {
		position: fixed;
		top: 0;
		left: 0;
		z-index: 100;
		width: 100%;
		box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.24);

	}
}

.fixed-header .lp-left-stickyHeader,
.fixed-header .lp-question-btn {
	display: block;
	animation: lp-stickyElement 1s 1;
}

.fixed-header #content-header {
	margin-top: 0;
}

.fixed-header #content-header .nav {
	border: 0;
	padding: 0 10px;
}

.fixed-header #content-header .nav a {
	padding: 11px 0 17px 0;
}

.fixed-header #content-header .nav a:focus,
.fixed-header #content-header .nav a:hover {
	position: relative;
	top: 0;
	padding: 11px 0 15px 0;
}

.fixed-header #content-header .current a:focus,
.fixed-header #content-header .current a:hover,
.fixed-header #content-header .current a {
	position: relative;
	top: 0;
	padding: 11px 0 15px 0;
}

@keyframes lp-stickyElement {
	0% {
		opacity: 0;
	}

	100% {
		opacity: 1;
	}
}

@-webkit-keyframes lp-stickyElement {
	0% {
		opacity: 0;
	}

	100% {
		opacity: 1;
	}
}

@media only screen and (min-width: 1980px) {
	.lp-left-stickyHeader h4 {
		max-width: 350px;
		width: 55%;
	}
}

@media only screen and (max-width: 1650px) {
	.lp-left-stickyHeader h4 {
		width: 35%;
	}
}

@media only screen and (max-width: 1580px) {
	.lp-left-stickyHeader h4 {
		width: 30%;
	}
}

@media only screen and (max-width: 1530px) {
	.lp-left-stickyHeader h4 {
		display: none;
	}
}

@media only screen and (max-width: 1425px) {
	.fixed-header .lp-question-btn {
		display: none;
	}
}

@media only screen and (max-width: 1210px) {
	.fixed-header .lp-left-stickyHeader {
		display: none;
	}
}

/* Article bottom */
.lp-articleBottom {
	display: block;
	width: 710px;
	margin: 50px -20px;
}

.lp-articleBottom h3 {
	margin: 0;
	font-size: 24px;
	font-weight: 600;
	line-height: 1.3em;
	letter-spacing: -1px;
}

.lp-articleBottom h4 {
	margin: 0;
	font-size: 15px;
	font-weight: 400;
	line-height: 1.3em;
	letter-spacing: -0.2px;
}

.lp-articleBottom-left,
.lp-articleBottom-right {
	width: 49%;
	min-height: 271px;
	padding: 18px;
	box-sizing: border-box;
}

.lp-articleBottom-left {
	float: left;
	margin-right: 2%;
	color: #3c729e;
	background: #e4f1f7;
	border: 1px solid #c3dbe5;
}

.lp-articleBottom-right {
	float: left;
	color: #4d4d49;
	background: url("../img/bottom-bg.png") no-repeat #f7f7f7;
	background-position: bottom -70px left -80px;
	border: 1px solid #e4e4e4;
}

.lp-sendEmail {
	width: 100%;
	display: block;
}

.lp-sendEmail label {
	margin: 35px 0 6px 0;
	font-size: 14px;
	font-weight: 600;
	letter-spacing: -0.4px;
	line-height: 1em;
}

.lp-sendEmail input {
	width: 100%;
	display: block;
	margin: 0;
	padding: 11px 10px;
	color: #838383;
	font-size: 14px;
	border: 1px solid #e6e6e6;
	box-sizing: border-box;
	-webkit-box-shadow: none;
	-moz-box-shadow: none;
	box-shadow: none;
}

.lp-sendEmail button {
	display: inline-block;
	margin: 8px 0 0;
	padding: 16px 30px;
	color: #fff;
	font-size: 14px;
	font-weight: 600;
	line-height: 1em;
	letter-spacing: -0.4px;
	text-transform: uppercase;
	text-align: center;
	text-decoration: none;
	background: #b12222;
	border: 0;
}

.lp-sendEmail button:hover {
	background: #9d1616;
}

.lp-cityContainer {
	width: 100%;
	display: block;
	margin: 20px 0 0;
}

.lp-cityRow {
	width: 100%;
	clear: both;
	display: block;
	margin: 0 0 10px 0;
}

.lp-cityRow h5,
.lp-cityRow p.h5 {
	float: left;
	display: block;
	width: 70px;
	margin: 0;
	padding: 0;
	font-size: 12px;
	font-weight: 600;
	text-align: right;
	letter-spacing: -0.4px;
}

.lp-cityRow ul {
	float: left;
	display: block;
	width: 220px;
	margin: 0 0 0 10px;
	padding: 0;
	font-size: 12px;
	font-family: Arial, sans-serif;
	list-style: none;
}

.lp-cityRow li {
	display: inline-block;
	margin: 0 10px 2px 0;
	text-decoration: underline;
}

.lp-cityRow li:hover {
	text-decoration: none;
}

.lp-cityRow li a {
	color: #3c729e;
}

/* terminy */
.lp-courseDates-cointainer {
	display: block;
	clear: both;
	width: 100%;
	margin: 0;
}

.lp-courseDates-cointainer table {
	width: 710px;
	margin: 0 0 0 -20px;
	background: #fff;
	border: 1px solid #e3e3e3;
	border-radius: 0;
	box-shadow: none;
}

.lp-courseDates-cointainer thead {
	font-family: Arial, sans-serif;
	font-size: 12px;
	font-weight: bold;
	line-height: 1em;
	letter-spacing: 0;
	color: #596063;
	text-align: left;
	background: #f0f0f0;
}

.lp-courseDates-cointainer tbody {
	font-family: Arial, sans-serif;
	font-size: 12px;
}

.lp-courseDates-cointainer th {
	padding: 15px 10px;
	border: 1px solid #e3e3e3;
}

.lp-courseDates-cointainer tr:first-child {
	border: 1px solid #e3e3e3;
}

.lp-courseDates-cointainer tr {
	padding: 0;
	border: 0;
}

.lp-courseDates-cointainer td {
	padding: 12px 9px;
	border: 0;
	box-sizing: border-box;
}

.lp-courseDates-cointainer td.lp-courseCategory {
	padding: 18px 0;
	font-family: Open Sans, Arial, sans-serif;
	font-weight: 600;
	letter-spacing: -0.4px;
	text-align: center;
	line-height: 1em;
	color: #333;
	font-size: 16px;
}

.lp-courseDates-cointainer .lp-courseDates {
	width: 262px;
}

.lp-courseDates-cointainer .lp-courseTime {
	width: 40px;
	text-align: center;
}

.lp-courseDates-cointainer .lp-coursePrice {
	width: 56px;
	text-align: center;
}

.lp-courseDates-cointainer td.lp-coursePrice {
	font-family: Open sans, Arial, sans-serif;
}

.lp-courseDates-cointainer td.lp-coursePrice strong {
	font-size: 14px;
	font-weight: 600;
}

.lp-courseDates-cointainer .lp-cityContainer,
.lp-courseDates-cointainer .lp-coursePrice p {
	margin: 0;
}

.lp-courseDates-cointainer .lp-cityContainer {
	margin-top: -3px;
	margin-bottom: 10px;
}

.lp-courseDates-cointainer .lp-cityRow h5 {
	font-size: 12px;
}

.lp-individualLessons {
	margin: 0;
	padding: 0 0 0 19px;
}

html .lp-courseName {
	line-height: 1.5em;
}

.lp-courseName a {
	margin: 0;
	font-family: Open sans, Arial, sans-serif;
	font-size: 14px;
	font-weight: 600;
	line-height: 1em;
	text-decoration: none;
	color: #316b97;
}

.lp-courseName a:hover {
	text-decoration: underline;
}

.lp-courseName p {
	margin: 7px 0 0;
	padding: 0;
	line-height: 1.2em;
}

.lp-courseName p span {
	display: block;
}

/* gray color */
.lp-courseRow td {
	color: #8b8b8b;
	border-right: 1px solid #e3e3e3;
}

.lp-courseRow .lp-cityContainer a {
	color: #8b8b8b;
}

.lp-courseRow td.lp-coursePrice strong {
	color: #8b8b8b;
}

.lp-courseRow td:last-child {
	border: 0;
}

/* blue color */
.lp-courseDates-cointainer tr:nth-child(even) {
	background: #e4f1f7;
	border-bottom: 1px solid #c3dbe6;
	border-top: 1px solid #c3dbe6;
}

.lp-courseDates-cointainer tr:nth-child(even).lp-courseRow td {
	color: #316b97;
	border-right: 1px solid #c3dbe6;
}

.lp-courseDates-cointainer tr:nth-child(even).lp-courseRow .lp-cityContainer a {
	color: #316b97;
}

.lp-courseDates-cointainer tr:nth-child(even).lp-courseRow .lp-coursePrice strong {
	color: #316b97;
}

.lp-courseDates-cointainer tr:nth-child(even).lp-courseRow td:last-child {
	border: 0;
}

.lp-courseDates-cointainer .lp-borderBottomBlue,
.lp-courseDates-cointainer .lp-borderBottomBlue:first-child {
	border-bottom: 1px solid #c3dbe6;
}

/* tooltips */
.lp-courseDates-cointainer th.lp-coursePrice p {
	position: relative;
	left: -8px;
	width: 100%;
	margin: 0;
}

.lp-courseDates-cointainer [data-legend] {
	border: 0;
}

.lp-courseDates-cointainer [data-legend]:after {
	position: absolute;
	right: -5px;
	top: -1px;
	content: " ";
	width: 14px;
	height: 14px;
	margin-left: 0px;
	text-shadow: none;
	border: 0;
	background: none;
	background: url("../img/mini-sprite.png") -61px -20px no-repeat;
}

.lp-courseDates-cointainer tr:nth-child(even) [data-legend]:after {
	background: url("../img/mini-sprite.png") -41px -20px no-repeat;
}

.lp-individualLessons[data-legend]:after {
	left: 0;
	top: 3px;
}

.lp-courseDates-cointainer [data-legend]:hover:before,
[data-legend]:active:before {
	right: -80px;
	width: 260px;
	margin: 0 0 10px 0;
	text-shadow: none;
	padding: 20px;
	box-shadow: none;
	background: #fff;
	color: #646464;
	font-size: 12px;
	font-family: Arial, sans-serif;
	line-height: 17px;
	text-align: left;
	border: 0;
	box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, 0.2);
}

.article .lp-individualLessons {
	margin: 0;
}

.lp-tooltipTriangle {
	display: none;
	width: 19px;
	height: 7px;
	background: url("../img/mini-sprite.png") -81px -20px no-repeat;
	overflow: hidden;
	text-indent: 2000px;
}

.lp-courseDates-cointainer [data-legend]:hover .lp-tooltipTriangle {
	display: block;
	position: absolute;
	top: -10px;
	left: 53px;
	z-index: 100;
}

/* Promotion Price */
.lp-promotionPrice {
	line-height: 1.4em;
}

.lp-promotionPrice .lp-coursePriceNormal {
	text-decoration: line-through;
}

.lp-coursePricePromotion,
.lp-courseDates-cointainer tr:nth-child(odd) td.lp-coursePrice .lp-coursePricePromotion strong,
.lp-courseDates-cointainer tr:nth-child(even) td.lp-coursePrice .lp-coursePricePromotion strong,
.lp-courseRow td.lp-coursePrice .promotional-price strong,
.lp-courseDates-cointainer tr:nth-child(even) td.lp-coursePrice .promotional-price strong {
	color: #ce1e1e;
}

/* Zaproponuj wlasny termin */
.lp-ownTerm {
	clear: both;
	display: block;
	width: 100%;
	margin: 0 0 15px -519px;
	font-family: Open sans, Arial, sans-serif;
	font-size: 13px;
	color: #414141;
}

.lp-ownTerm p {
	display: inline-block;
	margin: 0 0 0 7px;
	font-weight: 700;
	letter-spacing: -0.3px;
}

.lp-ownTerm a {
	position: relative;
	z-index: 100;
	padding: 10px 0 10px 51px;
	font-weight: 400;
	color: #414141;
	text-decoration: none;
}

.lp-ownTerm strong {
	font-weight: 700;
}

.lp-ownTerm span {
	text-decoration: underline;
}

.lp-ownTerm a:hover span {
	text-decoration: none;
}

.lp-ownTerm:before {
	position: relative;
	top: 12px;
	left: 50px;
	z-index: 10;
	content: " ";
	display: inline-block;
	width: 37px;
	height: 33px;
	background: url("../img/mini-sprite.png") -140px 0px no-repeat;
}

/* Masz pytanie */
.lp-rightSlidePanel {
	position: fixed;
	top: 50%;
	right: -305px;
	margin: -150px 0 0 0;
	z-index: 100;
	display: block;
	-webkit-transition: all 1000ms cubic-bezier(0.075, 0.82, 0.165, 1);
	transition: all 1000ms cubic-bezier(0.075, 0.82, 0.165, 1);
}

.lp-rightSlidePanel:hover,
.lp-rightSlidePanel:focus,
.lp-rightSlidePanel:active {
	right: 0px;
}

.lp-rightSlidePanel-left {
	display: block;
	float: left;
	width: 47px;
	height: 160px;
	overflow: hidden;
	text-indent: -9000px;
	background: url("../img/mini-sprite.png") -200px 0px no-repeat;
}

.lp-rightSlidePanel-right {
	float: left;
	display: block;
	position: relative;
	width: 305px;
	min-height: 160px;
	padding: 21px;
	font-size: 14px;
	font-weight: 400;
	line-height: 19px;
	color: #fff;
	letter-spacing: -0.2px;
	box-sizing: border-box;
	background: url("../img/bg-question.png") right bottom #276ca1 no-repeat;
}

.lp-rightSlidePanel-right form {
	width: 100%;
	margin: 0;
	padding: 0;
}

.lp-rightSlidePanel-right input,
.lp-rightSlidePanel-right textarea {
	width: 100%;
	padding: 12px;
	font-size: 13px;
	font-weight: 400;
	color: #6d6d6d;
	line-height: 16px;
	letter-spacing: -0.2px;
	box-sizing: border-box;
	background: #fff;
	border: 0;
	border-radius: 0;
}

.lp-rightSlidePanel-right input {
	margin: 17px 0 0 0;
}

.lp-rightSlidePanel-right textarea {
	margin: 7px 0 0 0;
	height: 120px;
}

.lp-rightSlidePanel-right button {
	display: block;
	padding: 15px 26px;
	margin: 7px 0 0 0;
	color: #fff;
	text-decoration: none;
	font-size: 14px;
	font-weight: 600;
	line-height: 1em;
	font-family: Open sans;
	letter-spacing: -0.4px;
	text-transform: uppercase;
	background: #b12222;
	border: 0;
}

.lp-rightSlidePanel-right button:hover {
	background: #9d1616;
}

.lp-rightSlidePanel-right .userCode {
	position: absolute;
	left: -5000px;
}

.lp-rightSlidePanel-second {
	width: 100%;
}

.lp-rightSlidePanel-second h3 {
	margin: 25px 0 3px 0;
	font-size: 20px;
	font-weight: 600;
	line-height: 1.1em;
	letter-spacing: -0.5px;
}

.lp-rightSlidePanel-second h3:first-child {
	margin-top: 0;
}

.lp-rightSlidePanel-second form {
	display: block;
	width: 100%;
}

.lp-newsletterRow {
	display: block;
	width: 100%;
	margin: 18px 0 0;
}

.lp-newsletterRow input {
	width: 15px;
	margin: 0 3px 0;
	display: inline-block;
}

.lp-newsletterRow label {
	width: 150px;
	margin: 1px 0 0;
	padding: 0;
	display: inline-block;
	font-weight: 600;
	font-size: 13px;
	letter-spacing: -0.4px;
	line-height: 1em;
}

/* Placeholder */
.lp-rightSlidePanel-right ::-webkit-input-placeholder {
	position: relative;
	top: 1px;
	font-size: 13px;
	letter-spacing: -0.2px;
	color: #6d6d6d;
}

.lp-rightSlidePanel-right ::-moz-placeholder {
	position: relative;
	top: 1px;
	font-size: 13px;
	letter-spacing: -0.2px;
	color: #6d6d6d;
}

.lp-rightSlidePanel-right ::-moz-placeholder {
	position: relative;
	top: 1px;
	font-size: 13px;
	letter-spacing: -0.2px;
	color: #6d6d6d;
}

.lp-rightSlidePanel-right ::-ms-input-placeholder {
	position: relative;
	top: 1px;
	font-size: 13px;
	letter-spacing: -0.2px;
	color: #6d6d6d;
}

/* Error */
.lp-rightSlidePanel-right .error .error-message {
	display: block;
}

.lp-rightSlidePanel-right .error input,
.lp-rightSlidePanel-right .error textarea {
	border: 2px solid #dd2a2a;
	background: #ffe4e4;
}

.lp-rightSlidePanel-right .error-message {
	display: none;
	margin: 3px 0 10px;
	font-size: 12px;
	color: #fff;
	font-weight: 600;
	text-transform: uppercase;
}

.lp-rightSlidePanel-right .error ::-webkit-input-placeholder {
	color: #dd2a2a;
}

.lp-rightSlidePanel-right .error ::-moz-placeholder {
	color: #dd2a2a;
}

.lp-rightSlidePanel-right .error ::-moz-placeholder {
	color: #dd2a2a;
}

.lp-rightSlidePanel-right .error ::-ms-input-placeholder {
	color: #dd2a2a;
}


/* Nagłówek H2 przy tabelach */
.article .lp-tableHeaders h2 {
	margin: 30px 0 15px -20px;
	font-size: 20px;
	letter-spacing: -0.3px;
}

/* Dodatkowe style */
.zadaj-szybkie-pytanie .lp-rightSlidePanel-right input[type="text"] {
	width: 100% !important;
}

.lp-rightSlidePanel.en .lp-rightSlidePanel-left {
	background: url("../img/mini-sprite.png") -259px 0px no-repeat;
}

.morph-content ul.errorlist {
	display: block;
	clear: both;
	font-size: 12px;
	letter-spacing: 0px;
}

.morph-content ul.errorlist {
	position: relative;
	top: -22px;
}

.morph-content .your-email ul.errorlist {
	position: relative;
	top: 3px;
	padding-left: 115px;
}

.fieldset ul label[for="id_akceptuje_regulamin"] {
	font-weight: 600;
}

/* New Top */
.lp-newTop {
	clear: both;
	width: 100%;
	padding: 22px 0 35px;
	color: #fff;
	background: #2a3642;
	background-position: center bottom;
	background-repeat: no-repeat;
}

.lp-newTop-background001 {
	background-image: url("../img/top-image.jpg");
}

.lp-newTop-content {
	width: 980px;
	margin: 0 auto;
	padding: 0 20px 0 0;
	box-sizing: border-box;
}

.lp-newTop-left {
	float: left;
	position: relative;
	width: 735px;
}

.lp-newTop-right {
	float: right;
	width: 220px;
	margin: 7px 0 0 0;
}

.lp-newTop-header {
	float: left;
	width: 575px;
	margin: -5px 0 0 0;
	position: relative;
}

.lp-newTop-header h1 {
	margin: 0;
	font-size: 27px;
	/* wersja podstawowa 35px */
	font-weight: 400;
	line-height: 30px;
	letter-spacing: -0.5px;
}

.lp-newTop-header p {
	margin: 15px 0 0 0;
	font-size: 17px;
	font-weight: 300;
	line-height: 22px;
	letter-spacing: -0.3px;
}

.lp-newTop-rating {
	float: right;
	width: 130px;
	margin: 8px 20px 0 0;
}

/* ocena kursu*/
.lp-newTop-rating .rate {
	float: left;
	font-weight: 400;
	letter-spacing: -0.5px;
	font-size: 22px;
}

.lp-newTop-rating .rate span {
	font-size: 16px;
}

.lp-newTop-rating .stars-cointaner {
	float: left;
	display: block;
	width: 77px;
	height: 12px;
	margin: 7px 0px 0 8px;
}

.lp-newTop-rating .stars {
	display: block;
	width: 100%;
	height: 100%;
	text-indent: -6000px;
	overflow: hidden;
	background: url("../img/sprite.png") no-repeat;
	/* do podmiany URL */
}

/* stars */
.stars.stars-0,
.stars.stars-0-0 {
	background-position: -240px 0;
}

.stars.stars-0-5 {
	background-position: -240px -20px;
}

.stars.stars-1,
.stars.stars-1-0 {
	background-position: -240px -40px;
}

.stars.stars-1-5 {
	background-position: -240px -60px;
}

.stars.stars-2,
.stars.stars-2-0 {
	background-position: -240px -80px;
}

.stars.stars-2-5 {
	background-position: -240px -100px;
}

.stars.stars-3,
.stars.stars-3-0 {
	background-position: -240px -120px;
}

.stars.stars-3-1,
.stars.stars-3-2 {
	background-position: -39px -180px;
}

.stars.stars-3-3,
.stars.stars-3-4 {
	background-position: -39px -200px;
}

.stars.stars-3-5 {
	background-position: -240px -140px;
}

.stars.stars-3-6,
.stars.stars-3-7 {
	background-position: -39px -220px;
}

.stars.stars-3-8,
.stars.stars-3-9 {
	background-position: -39px -240px;
}

.stars.stars-4,
.stars.stars-4-0 {
	background-position: -240px -160px;
}

.stars.stars-4-1,
.stars.stars-4-2 {
	background-position: -139px -180px;
}

.stars.stars-4-3,
.stars.stars-4-4 {
	background-position: -139px -200px;
}

.stars.stars-4-5 {
	background-position: -240px -180px;
}

.stars.stars-4-6,
.stars.stars-4-7 {
	background-position: -139px -220px;
}

.stars.stars-4-8,
.stars.stars-4-9 {
	background-position: -139px -240px;
}

.stars.stars-5,
.stars.stars-5-0 {
	background-position: -240px -200px;
}

.lp-clr {
	clear: both;
	display: block;
	width: 100%;
}

.lp-newTop-promotedWrap {
	display: block;
	clear: both;
	margin: 40px 0 0 0;
	background: url("../img/box-top.png") no-repeat;
}

.lp-newTop-promotedSingle {
	width: 33%;
	padding: 17px 17px 0 17px;
	float: left;
	box-sizing: border-box;
	background: url("../img/box-left.png") no-repeat;
}

.lp-newTop-promotedSingle h2 {
	margin: 0;
	font-size: 15px;
	font-weight: 600;
	line-height: 16px;
	letter-spacing: -0.4px;
	color: #fff;
}

.lp-newTop-promotedSingle h3 {
	margin: 4px 0 0;
	font-size: 11px;
	font-weight: 600;
	text-transform: uppercase;
	line-height: 1em;
	color: #fcaf12;
}

.lp-newTop-promotedSingle ul {
	margin: 17px 0 10px 17px;
	font-size: 12px;
	font-family: Arial, sans-serif;
	line-height: 14px;
	color: #fcaf12;
}

.lp-newTop-promotedSingle ul li:nth-child(n + 1) {
	margin-bottom: 7px;
}

.lp-newTop-promotedSingle ul li:last-child {
	margin-bottom: 0px;
}

.lp-newTop-promotedSingle ul span {
	color: #fff;
}

.lp-newTop-promotedSingle a {
	font-size: 11px;
	color: #30a2f1;
	text-decoration: none;
}

.lp-newTop-promotedSingle a:hover {
	text-decoration: underline;
}

.lp-newTop-certifications {
	display: inline-block;
	align-items: center;
	margin: 0 20px 0 0;
	padding: 0;
	list-style-type: none;
	text-align: center;
	min-height: 65px;
}

.forbes-diamonds {
	width: 150px;
	position: absolute;
	display: inline-block;
}

.forbes-diamonds.hide {
	display: none;
}

.lp-newTop-certifications li {
	width: 80px;
	margin: 15px auto 0;
	display: inline-block;
	vertical-align: middle;
}

.lp-newTop-certifications li:only-of-type {
	width: 100px;
	margin: 30px auto 0;
}

.lp-newTop-certifications li:nth-child(2) {
	margin: 3px auto 0;
}

.lp-newTop-certifications li img {
	width: 100%;
}

/* Trener */
.lp-newTop-coachWrap {
	position: relative;
	padding: 0;
	margin: 0 0 30px 0;
	box-sizing: border-box;
	border-left: 1px solid #fff;
	border-right: 1px solid #fff;
	border-bottom: 1px solid #fff;
}

.lp-newTop-coachWrap h3 {
	width: 150px;
	margin: 0 30px;
	font-size: 20px;
	font-weight: 300;
	line-height: 22px;
	letter-spacing: -0.6px;
}

.lp-newTop-coachWrap h3 span {
	position: relative;
	top: -12px;
}

.lp-newTop-coachWrap h3 strong {
	font-weight: 600;
	color: #fff;
}

.lp-newTop-coachWrap h3:before {
	content: " ";
	float: left;
	position: absolute;
	left: 0px;
	width: 20px;
	height: 1px;
	background: #fff;
}

.lp-newTop-coachWrap h3:after {
	content: " ";
	float: right;
	position: absolute;
	right: 0px;
	top: 0px;
	width: 45px;
	height: 1px;
	background: #fff;
}

.lp-newTop-coach {
	margin: -22px 0 0;
}

.lp-newTop-singleCoach {
	padding: 30px 0 0;
	text-align: center;
}

.lp-newTop-singleCoach:focus {
	outline: none;
}

.lp-newTop-singleCoach p {
	margin: 18px 0 25px;
	font-size: 12px;
	font-family: Arial, sans-serif;
	line-height: 18px;
	color: #9ba1a6;
}

.lp-newTop-singleCoach p span {
	font-size: 1em;
	display: inline-block;
}

.lp-newTop-singleCoach p strong {
	color: inherit;
}

.lp-newTop-singleCoach p strong:first-of-type {
	display: block;
	margin: 0;
	font-size: 14px;
	font-weight: 600;
	font-family: Open sans, Arial, sans-serif;
	color: #fff;
	letter-spacing: 0px;
}

.lp-newTop-coachImg {
	display: block;
	width: 117px;
	height: 117px;
	margin: 0 auto;
	overflow: hidden;
	border-radius: 50%;
	box-shadow: 0px 0px 75px 0px rgba(0, 0, 0, 0.33);
}

.lp-newTop-coachImg img {
	width: 100%;
	//margin: -10px 0 0;
}

/* Slider */
.slick-slider {
	position: relative;
	display: block;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	-webkit-touch-callout: none;
	-khtml-user-select: none;
	-ms-touch-action: pan-y;
	touch-action: pan-y;
	-webkit-tap-highlight-color: transparent;
	width: 100%;
}

.slick-list {
	position: relative;
	display: block;
	overflow: hidden;
	margin: 0;
	padding: 0;
}

.slick-list:focus {
	outline: none;
}

.slick-list.dragging {
	cursor: pointer;
	outline: none;
}

.slick-slider .slick-track,
.slick-slider .slick-list {
	transform: translate3d(0, 0, 0);
}

.slick-track {
	position: relative;
	top: 0;
	left: 0;
	display: block;
}

.slick-track:before,
.slick-track:after {
	display: table;
	content: "";
}

.slick-track:after {
	clear: both;
}

.slick-loading .slick-track {
	visibility: hidden;
}

.slick-slide {
	display: none;
	float: left;
	height: 100%;
	min-height: 1px;
}

[dir="rtl"] .slick-slide {
	float: right;
}

.slick-slide img {
	display: block;
}

.slick-slide.slick-loading img {
	display: none;
}

.slick-slide.dragging img {
	pointer-events: none;
}

.slick-initialized .slick-slide {
	display: block;
}

.slick-loading .slick-slide {
	visibility: hidden;
}

.slick-vertical .slick-slide {
	display: block;
	height: auto;
	border: 1px solid transparent;
}

.slick-arrow.slick-hidden {
	display: none;
}

.slick-arrow {
	position: absolute;
	top: 77px;
	display: block;
	width: 17px;
	height: 25px;
	text-indent: 6000px;
	border: 0;
	background: url("../img/mini-sprite.png") no-repeat;

	@media only screen and (max-width : 1024px) and (-webkit-device-pixel-ratio : 2) {
		width: 11px;
	}

	@media only screen and (max-width : 1024px) and (-webkit-device-pixel-ratio : 3) {
		width: 11px;
	}
}

.slick-prev {
	left: 18px;
	background-position: 0 -40px;
	z-index: 1;
}

.slick-next {
	right: 18px;
	background-position: -20px -40px;
}

.slick-prev:hover {
	background-position: -40px -40px;
}

.slick-next:hover {
	background-position: -60px -40px;
}

.slick-prev:focus,
.slick-next:focus {
	outline: none;
}

/* Wyszkolone osoby */
.lp-newTop-People {
	clear: both;
	width: 170px;
	margin: -3px auto 0;
	font-size: 13px;
	line-height: 15px;
	letter-spacing: -0.3px;
}

.lp-newTop-People:before {
	display: block;
	content: " ";
	float: left;
	width: 50px;
	height: 38px;
	margin: 0 10px 0 0;
	display: block;
	background: url("../img/mini-sprite.png") -80px -40px no-repeat;
	/* do podmiany URL */
}

.lp-newTop-People p {
	float: left;
	width: 105px;
	margin: 5px 0 0;
}

.lp-newTop-People strong {
	font-weight: 600;
	color: #fff;
}

/* Terminy - dodatkowe style */
.lp-courseDates-cointainer .lp-cityRow li {
	float: left;
}

tr.lp-courseRow:nth-child(odd) {
	background: #f6f9fb;
}

tr.lp-borderBottomBlue {
	background: #fff !important;
}

.lp-courseDates-cointainer tr.lp-borderBottomBlue:nth-child(even) {
	border-top: 1px solid #e3e3e3;
	border-bottom: 1px solid #e3e3e3;
}

h2 span {
	margin-left: -20px;
	font-size: 22px;
	letter-spacing: -0.4px;
}

.lp-courseDates-cointainer .lp-cityRow ul {
	width: 193px;
}

.lp-courseDates-cointainer .lp-cityRow {
	margin: 0 0 3px 0;
}

.lp-courseDates-cointainer .lp-cityRow li {
	margin: 0 8px 2px 0;
}

.lp-coursePriceNormal small {
	font-size: 11px;
}

/* Certyfikaty */
.lp-newTop-background002 {
	padding: 35px 0 95px;
	background-image: url("../img/top-image.jpg");
}



/* Wykladowcy - podstrona zbiorcza */
.lp-coachAll p b {
	font-weight: 600;
}

.lp-coachAll p b:first-of-type {
	display: block;
	margin-top: 20px;
}

.lp-coachAll ul li {
	margin-bottom: 10px;
}

.article h2.lp-specialH2 {
	display: block;
	margin-top: 30px;
	letter-spacing: -0.3px;
}

.lp-coachAll-single {
	display: block;
	width: 100%;
}

.lp-coachSingleWrap {
	display: block;
	width: 100%;
	margin: 36px 0 0;
}

.lp-coachSingleWrap:after {
	content: " ";
	display: block;
	width: 100%;
	height: 7px;
	margin: 40px 0 0;
	background: url("../img/coachBottomShadow.jpg") center center no-repeat;
}

.lp-coachSingleWrap:last-child:after {
	display: none;
}

.lp-coachSingleContainer {
	display: table-row;
	width: 100%;
}

.lp-singleDescription {
	display: table-cell;
	width: 510px;
	padding: 0 30px 0 0;
	vertical-align: middle;
	box-sizing: border-box;
}

.lp-singleDescription h3 {
	margin: 0;
	font-size: 18px;
	font-weight: 400;
	letter-spacing: -0.2px;
	color: #2e2e2b;
}

.lp-singleDescription h4 {
	margin: 0;
	font-size: 14px;
	font-style: italic;
	font-weight: 300;
	line-height: 17px;
	letter-spacing: -0.2px;
	color: #7d7d7d;
}

.lp-singleDescription p {
	margin: 15px 0 15px 0;
}

.lp-singleDescription button,
.lp-coachSingleMore button {
	margin: 0;
	padding: 0;
	color: #076a8f;
	font-family: Open sans, Arial, sans-serif;
	font-size: 13px;
	font-weight: 600;
	letter-spacing: -0.2px;
	text-transform: uppercase;
	text-decoration: none;
	background: transparent;
	border: 0;
	transition: opacity 0.2s linear;
}

.lp-singleDescription button:after,
.lp-coachSingleMore button:after {
	content: " ";
	display: inline-block;
	width: 7px;
	height: 10px;
	margin: 2px 0 0 6px;
	background: url("../img/mini-sprite.png") no-repeat;
	background-position: 0 -80px;
}

.lp-singleDescription button:focus,
.lp-coachSingleMore button:focus {
	border: 0;
	outline: 0;
}

.lp-coachSingleMore button {
	margin: 30px 0 0;
}

.lp-coachSingleImageWrap {
	display: table-cell;
	width: 160px;
	vertical-align: middle;
}

.lp-coachSingleImage {
	overflow: hidden;
	width: 160px;
	height: 160px;
	border-radius: 50%;
}

.lp-coachSingleImage img {
	width: 100%;
}

.lp-hidden {
	opacity: 0;
	visibility: hidden;
}

.lp-coachSingleMore {
	display: none;
	width: 100%;
	padding: 20px 0 0;
}

.lp-coachSingleMore h3 {
	margin: 30px 0 10px;
	font-size: 14px;
	font-weight: 600;
	letter-spacing: -0.1px;
}

#article .lp-coachCertificate {
	margin: 0;
	padding: 0;
	list-style-type: none;
}

#article .lp-coachCertificate li {
	margin: 0 0 6px 0;
}

.lp-coachCertificate li:before {
	content: "#";
	margin: 0 5px 0 0;
	display: inline-block;
	font-weight: 700;
	color: #da3e3e;
}



/* Modal */
#simplemodal-container.lp-modal {
	display: none;
	background-color: #fff;
}

.lp-modalHeader {
	display: block;
	width: 100%;
	padding: 30px;
	box-sizing: border-box;
	background: #f7f7f7;
}

.lp-linkHeader {
	letter-spacing: 0;
	font-size: 14px;
	font-family: "Open Sans", Arial, sans-serif;
	font-weight: 600;
	line-height: 1.4em;
}

#simplemodal-container .lp-linkCertificate {
	color: #076a8f;
	display: block;
	margin: 10px 0 0 0;
	padding-bottom: 0;
	position: relative;
	top: 20px;
}

#simplemodal-container .lp-linkCertificate a {
	color: #076a8f;
}

/* Sticky TOP */
.fixed-header #content-header {
	background: #f3f3f3;
}

.fixed-header #content-header .current a {
	background: transparent !important;
}

/* Article bottom - additional function */
.lp-articleBottom .lp-cityRow li {
	margin: 0 7px 2px 0;
}

.lp-articleBottom .lp-cityRow li:nth-child(4n) {
	margin: 0 0 2px 0;
}

.lp-articleBottom .lp-cityRow ul {
	width: 235px;
}

.lp-articleBottom .lp-articleBottom-left {
	padding-right: 10px;
}

.lp-articleBottom [data-legend] {
	border: 0;
}

.lp-articleBottom [data-legend]:after {
	display: none;
}

.lp-articleBottom [data-legend]:hover:before,
.lp-articleBottom [data-legend]:active:before,
.lp-articleBottom [data-legend]:focus:before {
	left: -86px;
	width: 180px;
	margin: 0 0 10px 0;
	text-shadow: none;
	padding: 12px;
	box-shadow: none;
	background: #fff;
	color: #646464;
	font-size: 12px;
	font-family: Arial, sans-serif;
	line-height: 17px;
	text-align: center;
	border: 0;
	box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, 0.2);
}

.lp-tooltipTriangle {
	display: none;
	width: 19px;
	height: 7px;
	background: url("../img/mini-sprite.png") -81px -20px no-repeat;
	overflow: hidden;
	text-indent: 2000px;
}

.lp-articleBottom [data-legend]:hover .lp-tooltipTriangle,
.lp-articleBottom [data-legend]:focus .lp-tooltipTriangle {
	display: block;
	position: absolute;
	top: -10px;
	left: 0px;
	z-index: 100;
}

.lp-courseDates-cointainer .lp-cityRow [data-legend]:after,
.lp-courseDates-cointainer .lp-cityRow tr:nth-child(even) [data-legend]:after {
	display: none;
}

.lp-courseDates-cointainer .lp-cityRow [data-legend]:hover:before,
.lp-courseDates-cointainer .lp-cityRow [data-legend]:active:before,
.lp-courseDates-cointainer .lp-cityRow [data-legend]:focus:before {
	width: 140px;
	text-align: center;
}

.lp-courseDates-cointainer .lp-cityRow [data-legend]:hover .lp-tooltipTriangle,
.lp-courseDates-cointainer .lp-cityRow [data-legend]:focus .lp-tooltipTriangle {
	left: 0;
}

/* Top Kursy i szkolenia */
.lp-newTop.lp-courseTop {
	padding-bottom: 25px;
}

.lp-courseTop .lp-newTop-content {
	position: relative;
}

.lp-courseTop {
	font-family: Open sans, sans-serif;
}

.lp-courseTop .lp-newTop-header {
	float: none;
	display: block;
}

.lp-courseTop .lp-newTop-rating {
	width: 187px;
	margin: 0 0 30px 0;
}

.lp-courseTop .lp-newTop-rating .rate {
	width: 100px;
	text-align: right;
}

.lp-courseTop .lp-newTop-right {
	margin: 0;
}

.lp-topCoursePrice {
	position: relative;
	z-index: 9;
	width: 100%;
	clear: both;
	display: block;
}

.lp-courseTop .lp-newTop-left {
	width: 678px;
	/*width: 650px;*/
}

.lp-courseTop .lp-newTop-right {
	width: 280px;
	/*width: 305px; */
}

.lp-topCoursePriceLeft {
	float: right;
	padding: 0 20px 0 0;
	line-height: 1em;
	border-right: 1px solid #68717a;
}

.lp-topCoursePriceRight {
	float: right;
	width: 33px;
	height: 15px;
	display: block;
}

.lp-topCoursePricePromotion {
	display: none;
}

.lp-coursePromotion .lp-topCoursePricePromotion {
	display: block;
}

.lp-topCoursePriceStandard,
.lp-topCoursePricePromotion {
	font-size: 18px;
	font-weight: 300;
	color: #fcaf12;
}

.lp-topCoursePriceStandard strong,
.lp-topCoursePricePromotion strong {
	font-weight: 600;
	color: #fcaf12;
}

.lp-coursePromotion .lp-topCoursePriceStandard {
	margin: 0 0 5px 0;
}

.lp-coursePromotion .lp-topCoursePriceStandard,


.lp-topCoursePriceLeft .lp-topCourseInstallment:first-of-type .fa {
	margin-right: 17px;
}

.lp-priceStandard {
	display: none;
}

.lp-coursePromotion .lp-priceStandard {
	display: inline-block;
}

.lp-coursePromotion .lp-priceNormal {
	display: none;
}

.lp-courseTop .lp-info-icon {
	position: absolute;
	top: 50%;
	display: block;
	width: 15px;
	height: 15px;
	margin: -9px 0 0 15px;
	padding: 0;
	background: url("../img/mini-sprite.png") no-repeat;
	background-position: -100px -20px;
}

.lp-courseTop .lp-info-icon:hover {
	cursor: pointer;
}

.lp-courseTop .lp-info-icon:hover .lp-tooltip {
	display: block;
}

.lp-courseTop .lp-tooltip {
	display: none;
	position: relative;
	top: -90px;
	left: -241px;
	z-index: 10;
	width: 252px;
	text-shadow: none;
	padding: 15px;
	box-shadow: none;
	background: #fff;
	color: #646464;
	font-size: 12px;
	font-family: Arial, sans-serif;
	line-height: 17px;
	text-align: left;
	box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, 0.2);
}

.lp-courseTop .lp-tooltip span {
	display: block;
	position: absolute;
	bottom: -8px;
	right: 22px;
	width: 19px;
	height: 8px;
	background: url("../img/mini-sprite.png") no-repeat;
	background-position: -80px -20px;
}

.lp-courseTop .lp-courseDescription {
	margin: 0;
	font-size: 13px;
	font-weight: 400;
}

.lp-courseTop .lp-newTop-header span.lp-courseTitle {
	margin-right: 8px;
}

.lp-courseID {
	display: inline-block;
	position: relative;
	top: -4px;
	margin: 0 3px 0 0;
	padding: 3px 5px;
	font-size: 12px;
	font-family: Arial, sans-serif;
	letter-spacing: 0;
	line-height: 1em;
	border: 1px solid #fff;
	border-radius: 2px;
}

.lp-courseCalendar {
	clear: both;
	display: block;
	width: 100%;
	padding: 23px 0 17px;
	/* poprzednio -bottom 21px*/
}

.lp-courseTop .lp-cityContainer {
	margin: 0;
}

.lp-courseTop .lp-cityRow {
	width: 100%;
	margin: 0 0 2px 0;
}

.lp-courseTop .lp-cityRow ul {
	width: 540px;
}

.lp-courseTop .lp-cityRow h5,
.lp-courseTop .lp-cityRow p.h5 {
	width: 80px;
	color: #fcaf12;
	font-size: 12px;
	letter-spacing: -0.1px;
}

.lp-courseTop .lp-cityRow li {
	text-decoration: none;
}

.lp-courseTop .lp-cityRow li a {
	color: #fff;
}

.lp-londyn {
	position: relative;
	top: -2px;
}

.lp-londyn:after {
	position: relative;
	top: 5px;
	content: " ";
	display: inline-block;
	width: 27px;
	height: 18px;
	margin: 0 0 0 6px;
	background: url("../img/mini-sprite.png") no-repeat;
	background-position: -20px -80px;
}

.lp-courseInformationWrap {
	display: block;
	width: 100%;
	margin: 0 0 17px 0;
	clear: both;
}

.lp-courseLegend {
	margin: 6px 25px 0 1px;
	float: left;
	font-family: Arial, sans-serif;
	font-size: 12px;
	color: #fff;
}

.lp-courseLegend strong {
	color: #fff !important;
}

.lp-proposeTerm {
	width: 300px;
	float: left;
	font-weight: 400;
	font-size: 13px;
	line-height: 1.2em;
}

.lp-proposeTerm:before {
	position: relative;
	top: -2px;
	content: " ";
	float: left;
	width: 37px;
	height: 34px;
	margin: 0 11px 0 0;
	background: url("../img/mini-sprite.png") no-repeat;
	background-position: -140px -40px;
}

.lp-proposeTerm strong {
	display: block;
	font-weight: 600;
	color: #fcaf12 !important;
}

.lp-proposeTerm a {
	color: #fff;
}

.lp-newTop-header p.lp-noTerm {
	margin-top: 25px;
	margin-bottom: 25px;
}

.lp-displayNone {
	display: none;
}

/* kursy */
.lp-trainingCalendar {
	width: auto;
	clear: both;
	margin: 20px 0 5px;
	/* poprzednio: 20px 0*/
	border: 0;
	border-radius: 0;
	box-shadow: none;
	background: transparent;
}

.lp-trainingCalendar tbody {
	border-bottom: 1px solid #4b555f;
}

.lp-trainingCalendar tbody:last-child {
	border-bottom: 0;
}

.lp-trainingCalendar tbody tr:first-child td {
	padding-top: 7px;
	/* poprzednio 13px */
}

.lp-trainingCalendar tbody tr:last-child td {
	padding-bottom: 6px;
	/* poprzednio 13px */
}

.lp-trainingCalendar td,
.lp-trainingCalendar th {
	border: 0;
	margin: 0;
	padding: 0;
}

.lp-trainingCalendar tr:nth-child(even) {
	background: transparent;
}

.lp-trainingCalendar th {
	text-align: left;
	font-size: 11px;
	font-weight: 400;
	color: #fcaf12;
	line-height: 1.4em;
	box-sizing: border-box;
	padding-bottom: 2px;
}

.lp-trainingCalendar th strong {
	font-size: 12px;
	display: block;
	color: #fcaf12;
	text-transform: uppercase;
}

.lp-trainingCalendar .lp-trainingCity {
	width: 80px;
	padding-right: 20px;
	text-align: right;
}

.lp-trainingCalendar .lp-trainingCity h3 {
	margin: -1px 0 0 0;
	/* poprzednio 0*/
	padding: 0;
	color: #fcaf12;
	font-size: 12px;
	font-weight: 600;
	letter-spacing: -0.1px;
}

.lp-trainingCalendar tr .lp-trainingCity h3 {
	display: none;
}

.lp-trainingCalendar tr:first-of-type .lp-trainingCity h3 {
	display: block;
}

.lp-trainingDaily {
	box-sizing: border-box;
	padding-right: 30px;
}

.lp-trainingDaily,
.lp-trainingWeekly {
	font-family: Arial, sans-serif;
	font-size: 12px;
	color: #fff;
}

.lp-trainingDaily a,
.lp-trainingWeekly a {
	color: #fff;
	text-decoration: underline;
}

.lp-trainingDaily a:hover,
.lp-trainingWeekly a:hover {
	text-decoration: none;
}

.lp-trainingDaily,
.lp-trainingWeekly {
	width: 260px;
}

.lp-trainingLast,
.lp-trainingProgress,
.lp-trainingDescription {
	position: relative;
	top: -5px;
	display: block;
	padding-left: 11px;
	font-size: 11px;
	line-height: 15px;
	letter-spacing: 0px;
}

.lp-trainingLast:after,
.lp-trainingProgress:after,
.lp-trainingDescription:after {
	position: absolute;
	top: -3px;
	left: 0;
	content: " ";
	display: block;
	float: left;
	width: 0;
	height: 0;
	margin: 7px 3px 0 0;
	border-style: solid;
	border-width: 0 4px 6px 4px;
	border-color: transparent transparent #f93636 transparent;
}

.lp-trainingLast {
	color: #f93636;
}

.lp-trainingLast:after {
	border-color: transparent transparent #f93636 transparent;
}

.lp-trainingProgress {
	color: #51b659;
}

.lp-trainingProgress:after {
	border-color: transparent transparent #51b659 transparent;
}

.lp-trainingDescription {
	color: #fff;
	/* yellow #fcaf12 */
}

.lp-trainingDescription:after {
	border-color: transparent transparent #fff transparent;
}

.lp-akredytacja {
	position: absolute;
	bottom: -25px;
	/*bottom: -130px;*/
	right: 0;
	display: block;
	width: 298px;
	height: 183px;
	/*height: 300px;*/
	overflow: hidden;
	text-indent: 6000px;
	background: url("../img/akredytacja-logotyp.png") no-repeat;
}

/* mało terminów */
.lp-cityContainerLess {
	display: block;
	width: 100%;
	margin: 30px 0;
}

.lp-cityContainerLess .lp-cityRow {
	margin: 0 0 15px;
}

.lp-cityContainerLess .lp-cityRow:last-child {
	margin: 0;
}

.lp-cityContainerLess .lp-cityRow h5,
.lp-cityContainerLess .lp-cityRow p.h5{
	margin: -1px 0 0 0;
	padding: 0;
	color: #fcaf12;
	font-size: 12px;
	font-weight: 600;
	letter-spacing: -0.1px;
}

.lp-cityContainerLess .lp-cityRow li {
	display: block;
	text-decoration: none;
}

.lp-cityContainerLess .lp-cityRow li a,
.lp-cityContainerLess .lp-cityRow li strong {
	color: #fcaf12;
	font-weight: bold;
}

/* TOP Zbiorczy */
.lp-newTop-coach {
	overflow: hidden;
}

.lp-newTopZbiorczy .lp-newTop-coachWrap h3:after {
	width: 103px;
}

.lp-newTopZbiorczy h1 {
	margin-bottom: 30px;
}

.lp-optionHeader,
.lp-newTop-header p.lp-descriptionCourse {
	width: 710px;
}

.lp-optionHeader {
	margin: 0 0 15px 0;
	padding: 0;
	font-size: 16px;
	font-weight: 600;
	line-height: 19px;
	color: #fff;
}

.lp-newTop-header p.lp-descriptionCourse {
	margin: 0 0 15px 0;
	padding: 0;
	font-size: 13px;
	font-weight: 400;
	line-height: 19px;
	letter-spacing: 0;
}

.lp-newTop-header p.lp-descriptionCourse:last-child {
	margin-bottom: 0;
}

.lp-newTop-header p.lp-descriptionCourse a {
	font-weight: 600;
	color: #fff;
	text-decoration: underline;
}

.lp-newTop-header p.lp-descriptionCourse a:hover {
	text-decoration: none;
}

.lp-newTop-header p.lp-descriptionCourse strong {
	color: #fff;
	font-weight: 600;
}

.lp-newTopZbiorczy .lp-newTop-certifications {
	margin: 10px 0 0;
}

.lp-newTopZbiorczy .lp-newTop-certifications li {
	margin: 0 20px 0 0;
}

.lp-newTopZbiorczy .lp-newTop-certifications li img {
	width: auto;
}

.lp-noCoach .lp-newTop-content {
	position: relative;
}

.lp-noCoach .lp-newTop-rating {
	position: absolute;
	top: 0;
	right: 0;
}

.lp-noCoach .lp-newTop-People {
	margin: 50px 0 0 59px;
}

.lp-newTop-People strong {
	display: block;
}

/* Top FIX */
.lp-courseTop .lp-newTop-header {
	width: 650px;
}

.lp-akredytacja.lp-small {
	width: 150px;
	height: 92px;
	/*height: 151px;*/
	bottom: -26px;
	/*bottom: -97px;*/
	right: 55px;
	background-size: 100%;
}

td.lp-trainingDaily {
	padding-right: 10px;
	box-sizing: border-box;
}

.lp-newTopZbiorczy .lp-newTop-header h2 {
	margin: 0 0 0 0;
	/* wcześniej margin: 0 0 15px 0;*/
	font-size: 27px;
	font-weight: 400;
	line-height: 30px;
	letter-spacing: -0.5px;
}

.lp-newTopZbiorczy .lp-newTop-header h2.lp-optionHeader {
	width: 100%;
}

.lp-newTopZbiorczy .lp-newTop-header p {
	margin: 0 0 15px 0;
	padding: 0;
	font-size: 13px;
	font-weight: 400;
	line-height: 19px;
	letter-spacing: 0;
}

.lp-newTopZbiorczy .lp-newTop-header p:last-child {
	margin-bottom: 0;
}

.lp-newTopZbiorczy .lp-newTop-header p a,
ul.lp-text-list a {
	font-weight: 600;
	color: #fff;
	text-decoration: underline;
}

.lp-newTopZbiorczy .lp-newTop-header p a:hover,
ul.lp-text-list a:hover {
	text-decoration: none;
}

.lp-newTopZbiorczy .lp-newTop-header p strong {
	color: #fff;
	font-weight: 600;
}

.lp-newTopZbiorczy .lp-newTop-coachImg img {
	margin-top: 0;
}

.lp-newTopZbiorczy .lp-newTop-singleCoach p {
	width: 90%;
	margin: 18px auto 25px;
	line-height: 15px;
}

.lp-newTopZbiorczy .lp-newTop-singleCoach p strong {
	margin-bottom: 7px;
}

.lp-newTopZbiorczy .slick-arrow {
	z-index: 9;
}

/* Box z tekstami */
.lp-textBox {
	display: block;
	width: 100%;
}

.lp-textBox h2 {
	width: 100%;
	margin: 0;
	padding: 30px 0 20px 0;
	color: #2e2e2b;
	font-size: 25px;
	font-weight: 300;
	line-height: 1em;
	letter-spacing: -0.3px;
	border-bottom: 1px solid #e0e0e0;
}

.lp-textBox h2 strong {
	font-weight: 600;
}

.lp-seeMore-Wrap ul {
	float: left;
	width: 34%;
	margin: 30px 0 0 0;
	padding: 0;
	text-align: right;
	list-style: none;
	box-sizing: border-box;
}

.lp-textBox ul li {
	margin: 0 0 8px 0;
	padding: 0 22px 0 0;
	box-sizing: border-box;
	border-right: 2px solid #fff;
}

.lp-textBox ul li:last-child {
	margin: 0;
}

.lp-textBox ul li a {
	color: #333;
	text-decoration: none;
}

.lp-textBox ul li a:focus,
.lp-textBox ul li:focus,
.lp-textBox ul li.ui-tabs-active:focus {
	outline: none;
}

.lp-textBox ul li.ui-tabs-active {
	letter-spacing: -0.1px;
	border-right: 2px solid #316b97;
}

.lp-textBox ul li.ui-tabs-active a {
	color: #316b97;
	font-weight: 600;
}

.lp-textBox .ui-tabs-panel {
	float: left;
	width: 66%;
	font-size: 14px;
	padding: 30px 0 0 30px;
	box-sizing: border-box;
	border-left: 1px solid #e0e0e0;
	background: url("../img/textBoxBg.jpg") top left no-repeat;
}

.lp-textBox .ui-tabs-panel h3 {
	margin: 0;
	font-size: 16px;
	font-weight: 600;
	text-transform: uppercase;
	letter-spacing: -0.1px;
}

.lp-tabsMoreLink {
	text-decoration: none;
	text-transform: uppercase;
	font-size: 12px;
	font-weight: 700;
}

.lp-tabsMoreLink:hover {
	text-decoration: underline;
}

/* Question bottom */
.lp-rightSlidePanel {
	top: auto;
	right: 40px;
}

.lp-rightSlidePanel:hover,
.lp-rightSlidePanel:focus,
.lp-rightSlidePanel:active {
	right: 40px;
	bottom: 0 !important;
}

.lp-rightSlidePanel-left {
	display: block;
	float: right;
	width: 160px;
	height: 47px;
	overflow: hidden;
	text-indent: -9000px;
	background: url("../img/mini-sprite.png") -199px -59px no-repeat;
}

.lp-rightSlidePanel.en .lp-rightSlidePanel-left {
	width: 130px;
	background-position: -198px 0px;
}

.lp-rightSlidePanel-right {
	float: none;
	clear: both;
}

/* Topy zbiorcze - dodatkowe */
.lp-zbiorczyMax .lp-newTop-coachWrap {
	clear: both;
}

.lp-zbiorczyMax .lp-newTop-header {
	width: 96%;
}

.lp-zbiorczyMax .lp-newTop-right {
	margin-top: 0;
	display: none;

	@media all and (min-width: 1024px) {
		display: block;
	}
}

.lp-zbiorczyMax .lp-newTop-rating {
	margin: 0 0 19px 0;
}

.lp-zbiorczyMax .lp-newTop-coachWrap {
	margin: 0 0 19px 0;
}

.lp-newTopZbiorczy.lp-zbiorczyMax .lp-newTop-singleCoach p {
	margin: 18px auto 10px;
}

.lp-zbiorczyMax .lp-newTop-certifications {
	margin-top: 0;
}

.lp-noCoach .lp-newTop-header {
	width: 96%;
}

.lp-noCoach .lp-newTop-People {
	margin: 40px 0 0 59px;
}

.lp-noCoach .lp-newTop-right {
	margin: 0;
}

.lp-noCoach .lp-newTop-rating {
	margin-top: 0;
}

.lp-zbiorczyMedium .lp-newTop-header {
	width: 96%;
}

.lp-zbiorczyMedium .lp-newTop-rating {
	float: left;
	margin: 20px 20px 0 0;
}

.lp-zbiorczyMedium .lp-newTop-People {
	float: left;
	clear: none;
	margin: 12px 0 0;
}

.lp-zbiorczyMedium .lp-newTop-coachWrap {
	margin: 0;
}

.lp-zbiorczyPrawaImg {
	width: 100%;
	clear: both;
	display: block;
	text-align: center;
}

.lp-zbiorczyPrawaImg img {
	max-width: 100%;
}

/* Top statyczny */
.lp-newTopStatic .lp-newTop-left,
.lp-newTopStatic .lp-newTop-header {
	width: 100%;
}

.lp-newTopZbiorczy.lp-newTopStatic h1 {
	margin-bottom: 0;
}

/* END Topy zbiorcze - dodatkowe */

/* HomePage */
.lp-homePageTop {
	background-image: url("../img/bgtopbanner-homepage.jpg");
	padding-top: 30px !important;
}

.lp-homePageTop .lp-newTop-left,
.lp-homePageTop .lp-newTop-header {
	width: 100%;
	float: none;
}

.lp-homePageTop .lp-newTop-content {
	width: 940px;
	position: relative;
}

v .lp-homePageTop .lp-akredytacja {
	bottom: -35px;
	right: -60px;
	height: 222px;
}

.lp-homeSecHeader {
	margin: 5px 0 0;
	font-weight: 300;
	font-size: 18px;
	line-height: 20px;
	letter-spacing: -0.3px;
	color: #fff;
}

.lp-homePageTop .lp-textBox {
	position: relative;
	z-index: 2;
	clear: both;
	margin: 30px 0 0;
	border-top: 1px solid rgba(255, 255, 255, 0.15);
}

.lp-homePageTop .lp-textBox ul {
	width: 26%;
}

.lp-homePageTop .lp-textBox ul li {
	margin: 0 0 10px 0;
	border-right: 2px solid rgba(255, 255, 255, 0);
}

.lp-homePageTop .lp-textBox ul li a {
	color: #fff;
	text-decoration: none;
}

.lp-homePageTop .lp-textBox ul li.ui-tabs-active {
	border-right: 2px solid #fcaf12;
}

.lp-homePageTop .lp-textBox ul li.ui-tabs-active a {
	color: #fcaf12;
}

.lp-homePageTop .lp-textBox .ui-tabs-panel {
	width: 73%;
	min-height: 205px;
	padding-bottom: 20px;
	border-left: 1px solid rgba(255, 255, 255, 0.15);
	background: none;
}

.lp-homePageTop .lp-textBox .ui-tabs-panel h3 {
	margin: 0 0 10px 0;
}

.lp-homePageTop .lp-textBox .ui-tabs-panel p {
	margin: 0 0 5px 0;
}

.lp-homePageTop .lp-textBox .ui-tabs-panel a {
	color: #fff;
}

.lp-homePageTop .lp-textBox .ui-tabs-panel ul {
	width: 100%;
	margin-top: 0;
	margin-left: 19px;
	text-align: left;
	list-style-type: square;
}

.lp-homePageTop .lp-textBox .ui-tabs-panel ul li a {
	text-decoration: underline;
}

/* Home Page Content */
.lp-homePageContent {
	width: 940px;
	margin: 20px auto;
}

.lp-homeTagWrap {
	position: relative;
	width: 100%;
}

.lp-homeTagWrap .slick-arrow {
	z-index: 5;
	top: 0px;
	right: -32px;
	width: 28px;
	height: 28px;
	overflow: hidden;
	background: transparent;
	border-radius: 3px;
	background: url("../img/homepage-sprite.png") #4682b4 no-repeat;
	background-position: 10px -39px;
}

.lp-homeTagWrap .slick-arrow:hover {
	background-color: #296495;
}

.lp-homeTagWrap .slick-prev {
	display: none !important;
}

.lp-homeTagWrap h2 {
	display: block;
	width: 220px;
	margin: 3px 0 0;
	float: left;
	font-size: 14px;
	font-weight: 600;
	color: #111111;
}

.lp-homeTags {
	width: 688px;
	float: left;
}

.lp-singleTag a {
	display: block;
	margin: 0 5px 0 0;
	padding: 7px 8px 7px 9px;
	line-height: 1em;
	text-decoration: none;
	color: #fff;
	background: #4682b4;
	border-radius: 3px;
}

.lp-singleTag a:hover {
	background: #296495;
}

.lp-homepageBox {
	padding: 40px 0;
}

.lp-homepageSingleBox {
	position: relative;
	float: left;
	width: 300px;
	height: 368px;
	padding: 40px 25px 0 25px;
	border: 1px solid #e3e3e3;
	box-sizing: border-box;
	text-align: center;
	background-position: center;
}

.lp-homepageSingleBox:nth-child(2) {
	margin: 0 20px;
}

.lp-homepageSB1 {
	background-image: url("../img/homepage-mapa.jpg");
}

.lp-homepageSB2 {
	background-image: url("../img/homepage-kursy.jpg");
}

.lp-homepageSB3 {
	background-image: url("../img/homepage-specjalisci.jpg");
}

/* Style boxów */
.lp-homepageH2Header {
	margin: 0 0 10px;
	color: #111111;
	font-size: 18px;
	font-weight: 600;
	line-height: 21px;
	letter-spacing: -0.3px;
}

.lp-homepageParagrafStyle,
.lp-homepageListStyle {
	color: #111111;
	font-size: 13px;
	font-weight: 400;
	line-height: 16px;
}

.lp-homepageBtn {
	display: inline-block;
	padding: 12px 20px;
	font-size: 13px;
	font-weight: 600;
	text-transform: uppercase;
	letter-spacing: -0.3px;
	line-height: 1em;
	text-decoration: none;
	color: #1266ad;
	border: 1px solid #1266ad;
	border-radius: 3px;
	transition-property: color, background-color, border-color;
	transition-duration: 0.3s;
}

.lp-homepageBtn:hover,
.lp-homepageBtn:active,
.lp-homepageBtn:focus {
	color: #fff;
	border-color: #4682b4;
	background: #4682b4;
}

.lp-homepageSingleBox select {
	display: inline-block;
	margin: 0;
	width: 190px;
	padding: 8px 0 9px 8px;
	font-size: 13px;
	font-weight: 600;
	text-transform: uppercase;
	letter-spacing: -0.3px;
	line-height: 1em;
	text-decoration: none;
	text-align: left;
	color: #1266ad;
	background: none;
	border: 1px solid #1266ad;
	border-radius: 3px;
	box-shadow: none;
	transition-property: color, background-color, border-color;
	transition-duration: 0.3s;
}

.lp-homepageSingleBox select:hover,
.lp-homepageSingleBox select:active,
.lp-homepageSingleBox select:focus {
	cursor: pointer;
	color: #fff;
	border-color: #4682b4;
	background: #4682b4;
	outline: none;
}

.lp-homepageSingleBox option {
	padding: 0;
	min-height: 0;
}

.lp-homepageSB1 .lp-homepageBtn,
.lp-homepageSB2 .lp-homepageBtn,
.lp-homepageSingleBox select {
	margin-top: 36px;
}

.lp-homepageSB3 .lp-homepageBtn {
	margin-top: 20px;
}

.lp-contactLineWrap {
	width: 100%;
	height: 90px;
	background: #4682b4;
}

.lp-contactLineContent {
	width: 940px;
	margin: 0 auto;
	color: #fff;
	font-size: 12px;
	display: flex;
	flex-wrap: wrap;
	justify-content: space-around;
}

.lp-contactLineContent strong,
.lp-contactLineContent a {
	display: block;
	color: #fff;
	font-size: 22px;
	font-weight: 600;
	text-decoration: none;
}

.lp-contactLineContent a:hover {
	text-decoration: underline;
}

.lp-contactLineContent span {
	position: relative;
	top: -3px;
	opacity: 0.8;
}

.lp-contactSingle {
	position: relative;
	float: left;
	width: 33.333%;
	margin: 26px 0 0;
}

.lp-contactSingle.contactThree {
	margin-top: 32px;
}

.contactOne:before,
.contactTwo:before,
.contactThree:before {
	position: absolute;
	left: 0;
	display: block;
	content: " ";
	background: url("../img/homepage-sprite.png") no-repeat;
}

.contactOne,
.contactTwo,
.contactThree {
	box-sizing: border-box;
}

.contactOne {
	padding-left: 60px;
}

.contactTwo {
	left: 15px;
	padding-left: 47px;
}

.contactThree {
	left: 30px;
	padding-left: 69px;
}

.contactOne:before {
	top: -3px;
	width: 47px;
	height: 47px;
	background-position: 0 0;
}

.contactTwo:before {
	top: -5px;
	left: 7px;
	width: 28px;
	height: 50px;
	background-position: -50px 0;
}

.contactThree:before {
	top: -1px;
	left: 16px;
	width: 42px;
	height: 29px;
	background-position: -100px 0;
}

.lp-homepageCoachWrap {
	width: 100%;
	clear: both;
	margin: 40px 0 0;
}

.lp-homepageCoachIntro {
	width: 70%;
	margin: 0 0 40px 0;
}

.lp-homepageCoachIntro h2,
.lp-homepageRefSingle h2 {
	font-size: 27px;
	font-weight: 400;
	letter-spacing: -1px;
	color: #202020;
}

.lp-homepageCoachIntro p {
	font-size: 18px;
	font-weight: 300;
	line-height: 22px;
	color: #202020;
}

.lp-homepageDoubleBox {
	width: 100%;
	margin: 20px 0 50px;
}

.lp-homepageDoubleBoxOne {
	height: 260px;
	position: relative;
	float: left;
	padding: 30px 120px 30px 30px;
	width: calc(50% - 10px);
	border: 1px solid #e3e3e3;
	box-sizing: border-box;
}

.lp-homepageDoubleBoxOne:first-child {
	margin-right: 20px;
}

.lp-homepageDoubleBoxOne .lp-homepageH2Header,
.lp-homepageDoubleBoxOne .lp-homepageParagrafStyle,
.lp-homepageDoubleBoxOne .lp-homepageListStyle,
.lp-homepageDoubleBoxOne .lp-homepageBtn {
	color: #fff;
}

.lp-homepageListStyle {
	margin-top: 10px;
}

.lp-homepageListStyle li {
	margin-bottom: 6px;
}

.lp-homepageListStyle li:last-child {
	margin: 0;
}

.lp-homepageDoubleBoxOne .lp-homepageBtn {
	position: absolute;
	bottom: 30px;
	border-color: #fff;
	transition-property: color, background-color;
	transition-duration: 0.3s;
}

.lp-homepageDoubleBoxOne .lp-homepageBtn:hover,
.lp-homepageDoubleBoxOne .lp-homepageBtn:active,
.lp-homepageDoubleBoxOne .lp-homepageBtn:focus {
	color: #333;
	background: #fff;
}

.lp-homepageDB2 .lp-homepageH2Header {
	width: 70%;
}

.lp-homepageDB1 {
	background: url("../img/homepagebanners-specjalisci.jpg") no-repeat;
}

.lp-homepageDB2 {
	background: url("../img/homepagebanners-administracja.jpg") no-repeat;
}

.lp-homepageRefWrap {
	width: 100%;
	margin: 0 0 50px 0;
}

.lp-homepageRefSingle {
	float: left;
	width: 50%;
	box-sizing: border-box;
}

.lp-refOne img {
	margin-top: 25px;
}

.lp-refTwo {
	padding-left: 9px;
	box-sizing: border-box;
}

.lp-homepagePeopleRef {
	position: relative;
	width: 100%;
	padding-left: 40px;
	margin-top: 50px;
	box-sizing: border-box;
}

.lp-homepagePeopleRef p {
	color: #202020;
	font-size: 14px;
	font-style: italic;
	font-weight: 400;
	line-height: 18px;
}

.lp-homepagePeopleRef p strong {
	display: block;
	margin: 10px 0 0;
	font-size: 13px;
	font-weight: 700;
	font-style: normal;
}

.lp-homepagePeopleRef:before {
	position: absolute;
	top: -10px;
	left: 0;
	content: " ";
	display: block;
	width: 120px;
	height: 100px;
	background: url("../img/homepage-sprite.png") -150px 0 no-repeat;
}

/* Style trenerzy */
.lp-homepageCoachWrap ul {
	display: block;
	width: 100%;
	margin: 0;
	clear: both;
}

.lp-homepageCoachWrap ul li {
	display: block;
	width: 20%;
	margin: 0 0 20px;
	float: left;
	list-style-type: none;
}

.lp-homepageCoachWrap ul li a {
	display: block;
	text-decoration: none;
}

.lp-homepageCoachWrap .lp-coachImageWrap {
	float: none;
	display: block;
	width: 100%;
}

.lp-homepageCoachWrap .lp-coachImage {
	float: none;
	display: block;
	width: 117px;
	height: 117px;
	margin: 0 auto;
}

.lp-homepageCoachWrap .lp-coachName {
	float: none;
	width: 100%;
	height: 70px;
	margin: 10px auto 0;
	display: block;
	text-align: center;
}

.lp-homepageCoachWrap .lp-coachName p {
	width: 100%;
	display: block;
	margin: 0;
	font-size: 13px;
}

.lp-homepageCoachWrap .lp-coachName p small {
	font-size: 13px;
	font-family: "Open sans", sans-serif;
	font-weight: 300;
	line-height: 1.3em;
}

/* End HomePage */

/* FIX Topy zbiorcze */
.lp-newTop-coachImg a,
.lp-newTop-coachImg a:hover,
.lp-newTop-coachImg a:focus {
	outline: 0;
}

ul.lp-text-list {
	margin-top: 15px;
	margin-bottom: 15px;
	padding: 0;
	font-size: 14px;
	font-weight: 400;
	line-height: 19px;
	letter-spacing: 0;
}

.lp-newTopZbiorczy .lp-tabsMoreLink {
	margin-top: 0px;
	display: inline-block;
	padding: 3px 6px;
	border: 1px solid #fff;
	text-decoration: none !important;
	border-radius: 3px;
}

.lp-newTopZbiorczy .lp-newTop-header p {
	font-size: 14px;
}

.lp-newTopZbiorczy h2.lp-optionHeader.lp-optionHeaderSmall {
	margin-bottom: 10px;
	margin-top: 25px;
	font-size: 19px;
	line-height: 20px;
}

.lp-newTop.lp-newTopZbiorczy {
	padding: 22px 0;
}

.lp-newTopZbiorczy .lp-newTop-header p:first-of-type {
	margin-top: 12px;
}

/* FIX Tooltip */
.lp-courseTop .lp-topCoursePrice .lp-tooltip {
	position: relative;
	top: 27px;
}

.lp-courseTop .lp-topCoursePrice .lp-tooltip span {
	display: block;
	position: absolute;
	top: -7px;
	bottom: auto;
	right: 23px;
	width: 0;
	height: 0;
	border-style: solid;
	border-width: 0 9.5px 7px 9.5px;
	border-color: transparent transparent #ffffff transparent;
	background: none;
}

/* FIX Higglights */
#meta-content>h1,
#highlights {
	z-index: 0;
}

/* FIX Underline */
.lp-cityRow li {
	text-decoration: none;
}

.lp-cityRow li a {
	text-decoration: underline;
}

.lp-cityRow li a:hover {
	text-decoration: none;
}

/* FIX */

/*********************************************
	* input email - formularz zgłoszeniowy		 *
	* https://www.alx.pl/zgloszenie/www-grafika/ *
	**********************************************/
.formularz_zgloszeniowy #id_email,
.formularz_zgloszeniowy #id_email_ksiegowosc,
.formularz_zgloszeniowy #id_wiek_uczestnika {
	width: 180px;
	box-sizing: content-box;
	padding: 3px 3px 3px 5px;
	font: 1em/1.5em "Open Sans", sans-serif;
	vertical-align: middle;
	border: solid 1px;
	border-color: #d5d5d5 #cdcdcd #b9b9b9;
	border-radius: 2px;
	box-shadow: 0 2px 3px rgba(0, 0, 0, 0.075);
}

/************************************
	* formularz zgloszeniowy - naglowki *
	************************************/

h2 span.subtitle {
	position: relative;
	top: -3px;
	margin-left: 0;
}

/* ZSP - pytanie przysłaniające btn Zapisz */
.zadaj-szybkie-pytanie {
	width: 305px;
	height: 47px;
	top: auto;
	left: auto;
	right: 40px;
	bottom: 0;
}



/* END Flat Menu */

/*************/
/**** RWD ****/
/*************/


/* Nav */
/* Flexnav Base Styles */
.flexnav {
	transition: none;
	overflow: hidden;
	margin: 0 auto;
	width: 100%;
	max-height: 0;
}

.flexnav.opacity {
	opacity: 0;
}

.flexnav.flexnav-show {
	max-height: 2000px;
	opacity: 1;
	-webkit-transition: all 0.5s ease-in-out;
	-moz-transition: all 0.5s ease-in-out;
	-ms-transition: all 0.5s ease-in-out;
	transition: all 0.5s ease-in-out;
}

.flexnav.one-page {
	position: fixed;
	top: 50px;
	right: 5%;
	max-width: 200px;
}

.flexnav li {
	font-size: 100%;
	position: relative;
	overflow: hidden;
	display: block;
}

.flexnav li a {
	position: relative;
	display: block;
	padding: 8px 20px;
	z-index: 2;
	overflow: hidden;
	color: #000;
	background: rgba(70, 130, 180, 1);
	border-bottom: 1px solid rgba(255, 255, 255, 0.06);
	text-transform: uppercase;
	text-decoration: none;
	font-weight: 600;
	font-size: 13px;
	letter-spacing: -0.2px;
	box-sizing: border-box;
}

.flexnav li ul {
	width: 100%;
	margin: 0;
}

.flexnav li ul li {
	font-size: 100%;
	position: relative;
	overflow: hidden;
}

.flexnav li ul.flexnav-show li {
	overflow: visible;
}

.flexnav li ul li a {
	text-transform: none;
	font-weight: 400;
	display: block;
	background: #256499;
}

.flexnav ul li ul li a {
	background: #115085;
}

.flexnav ul li ul li ul li a {
	background: #cbcbc9;
}

li.lp-flexnav-header {
	background: #256499;
	box-sizing: border-box;
	padding: 15px 20px 5px;
	font-weight: 600;
	text-transform: uppercase;
	font-size: 12px !important;
	letter-spacing: 0;
	color: #559dda;
}

.flexnav .touch-button {
	position: absolute;
	z-index: 999;
	top: 0;
	right: 0;
	width: 50px;
	height: 50px;
	display: inline-block;
	background: transparent;
	text-align: center;
}

.flexnav .touch-button:hover {
	cursor: pointer;
}

.flexnav .touch-button .navicon {
	position: relative;
	top: 7px;
	font-size: 11px;
	color: #fff;
	opacity: 0.3;
}

/* flaga */
.flexnav li.lp-eng a {
	padding: 8px 20px 8px 65px;
}

.flexnav li.lp-eng:after {
	position: absolute;
	top: 12px;
	left: 21px;
	z-index: 10;
	display: block;
	content: " ";
	width: 46px;
	height: 24px;
	background: url("../img/menu-uk-flag.png") no-repeat;
	background-size: 75%;
}



@media all and (min-width: 990px) {
	body.one-page {
		padding-top: 70px;
	}

	.flexnav {
		overflow: visible;
	}

	.flexnav.opacity {
		opacity: 1;
	}

	.flexnav.one-page {
		top: 0;
		right: auto;
		max-width: 1080px;
	}

	.flexnav li {
		position: relative;
		list-style: none;
		float: left;
		display: block;
		background-color: #a6a6a2;
		overflow: visible;
		width: 20%;
	}

	.flexnav li a {
		border-left: 1px solid #acaca1;
		border-bottom: none;
	}

	.flexnav li>ul {
		position: absolute;
		top: auto;
		left: 0;
	}

	.flexnav li>ul li {
		width: 100%;
	}

	.flexnav li ul li>ul {
		margin-left: 100%;
		top: 0;
	}

	.flexnav li ul li a {
		border-bottom: none;
	}

	.flexnav li ul.open {
		display: block;
		opacity: 1;
		visibility: visible;
		z-index: 1;
	}

	.flexnav li ul.open li {
		overflow: visible;
		max-height: 100px;
	}

	.flexnav li ul.open ul.open {
		margin-left: 100%;
		top: 0;
	}
}

.oldie body.one-page {
	padding-top: 70px;
}

.oldie .flexnav {
	overflow: visible;
}

.oldie .flexnav.one-page {
	top: 0;
	right: auto;
	max-width: 1080px;
}

.oldie .flexnav li {
	position: relative;
	list-style: none;
	float: left;
	display: block;
	background-color: #a6a6a2;
	width: 20%;
	min-height: 50px;
	overflow: visible;
}

.oldie .flexnav li:hover>ul {
	display: block;
	width: 100%;
	overflow: visible;
}

.oldie .flexnav li:hover>ul li {
	width: 100%;
	float: none;
}

.oldie .flexnav li a {
	border-left: 1px solid #acaca1;
	border-bottom: none;
	overflow: visible;
}

.oldie .flexnav li>ul {
	background: #acaca1;
	position: absolute;
	top: auto;
	left: 0;
	display: none;
	z-index: 1;
	overflow: visible;
}

.oldie .flexnav li ul li ul {
	top: 0;
}

.oldie .flexnav li ul li a {
	border-bottom: none;
}

.oldie .flexnav li ul.open {
	display: block;
	width: 100%;
	overflow: visible;
}

.oldie .flexnav li ul.open li {
	width: 100%;
}

.oldie .flexnav li ul.open ul.open {
	margin-left: 100%;
	top: 0;
	display: block;
	width: 100%;
	overflow: visible;
}

.oldie .flexnav ul li:hover ul {
	margin-left: 100%;
	top: 0;
}

.oldie.ie7 .flexnav li {
	width: 19.9%;
}

/* Small Screens */
@media only screen and (max-width: 1080px) {
	body {
		min-width: 100%;
		overflow-x: hidden;
	}
}

/* Tablets */
@media only screen and (max-width: 1025px) {

	/* galeria strzalki */
	.lb-nav a.lb-next,
	.lb-nav a.lb-prev {
		background: transparent !important;
	}
}

@media only screen and (max-width: 1020px) {
	#aside {
		display: none;
	}

	#article {
		position: relative;
		width: calc(100% - 90px);
		margin: 0 auto;
		padding: 0;
		left: 0;
		float: none;
	}

	#content-header {
		width: calc(100% - 50px);
		margin: 10px auto 0;
		padding: 0;
		z-index: 9;
	}



	#content-header .nav {
		position: relative;
		left: 0px;
		float: left;
		width: 650px;
		margin: 0;
		overflow-x: scroll;
		height: 48px;
		overflow-y: hidden;
	}

	#content-header .nav li {
		display: inline-block;
		float: none;
	}

	#site-content {
		width: 100%;
		padding: 30px 0 0 0;
	}

	.lp-articleBottom {
		width: calc(100% + 40px);
	}

	.lp-newTop-content {
		width: 100%;
		padding: 80px 20px 0 20px;
		box-sizing: border-box;
	}

	.lp-courseTop .lp-newTop-left {
		width: 100%;
		padding: 0;
	}

	.lp-newTop-header h1 {
		width: 100%;
	}

	.lp-courseTop .lp-newTop-header {
		width: calc(100% - 260px);
	}

	.lp-courseTop .lp-newTop-right {
		width: 100%;
		margin: 0 auto;
		display: block;
	}

	.lp-courseTop .lp-newTop-rating {
		position: absolute;
		top: 2px;
		right: 20px;
	}

	.lp-topCoursePriceLeft {
		float: left;
		margin: 30px 0 0;
		text-align: left;
		border-right: none;
	}

	.lp-topCoursePriceRight {
		float: left;
		position: absolute;
		top: 35px;
		left: 180px;
	}

	.lp-akredytacja {
		background-position-x: 30px;
	}

	.lp-gallery {
		width: 100%;
	}

	#article .lp-gallery img {
		width: 32.5% !important;
		margin: 0 !important;
	}

	.lp-articleBottom .lp-cityRow ul {
		width: calc(100% - 70px);
	}

	.lp-singleDescription {
		width: calc(100% - 190px) !important;
	}

	/* Podstrona zbiorcza */

	.lp-newTop-left {
		width: 100%;
	}

	.lp-courseDates-cointainer table {
		width: calc(100% + 40px);
	}

	.lp-textBox {
		margin-bottom: 40px;
	}
}

/* nakładki scroll */
@media only screen and (max-width: 1020px) {


	#content-header .nav {
		overflow: hidden !important;
	}
}

@media only screen and (max-width: 810px) {


	#content-header .nav {
		overflow-x: scroll;
		overflow-y: hidden;
	}
}

/* END nakładki scroll */

@media only screen and (max-width: 991px) {

	/* Global styles */
	/* WIDTH */
	.lp-optionHeader,
	.lp-homeSecHeader,
	.lp-homepageBox,
	.lp-homePageContent {
		width: 100%;
	}

	/* BOX SIZING */
	.lp-homepageBox,
	.lp-homepageSingleBox,
	.lp-contactLineContent,
	.lp-homepageCoachWrap ul,
	.lp-homepageDoubleBox,
	.lp-homepageRefWrap {
		box-sizing: border-box;
	}

	/* PADDINGS LEFT & RIGHT */
	.lp-homepageDoubleBox,
	.lp-homepageRefWrap {
		padding-left: 20px;
		padding-right: 20px;
	}

	.lp-homePageTop {
		overflow: hidden;
		padding-top: 20px !important;
	}

	.lp-homePageTop .lp-newTop-content {
		width: 100%;
		padding: 0 20px;
		box-sizing: border-box;
	}

	#site-header {
		width: 100%;
		min-width: 100%;
	}

	#site-header .top-nav .nav {
		width: 100%;
		margin: 0;
		padding-right: 20px;
		left: 0;
		box-sizing: border-box;
	}

	.searchbox {
		margin: 0;
		float: left;
		left: 20px;
	}

	.lp-homeTagWrap {
		padding: 0 20px;
		margin: 0 auto;
		overflow: hidden;
		box-sizing: border-box;
	}

	.lp-homepageBox {
		padding: 40px 20px;
	}

	.lp-homepageSingleBox {
		width: 32%;
	}

	.lp-homepageSingleBox:nth-child(2) {
		margin: 0 2%;
	}

	.lp-homepageSingleBox select {
		width: 80%;
		margin-left: auto;
		margin-right: auto;
	}

	.lp-homeTagWrap h2 {
		margin-bottom: 5px;
	}

	.lp-contactLineContent {
		width: 100%;
		padding-left: 20px;
		padding-right: 20px;
	}

	.lp-contactSingle {
		width: 33%;
	}

	.contactTwo {
		margin-top: 17px;
	}

	.contactTwo span {
		top: 1px;
		display: inline-block;
		line-height: 15px !important;
	}

	.contactTwo:before {
		top: 2px;
	}

	.lp-contactLineContent strong,
	.lp-contactLineContent a {
		font-size: 16px;
	}

	.lp-homepageCoachIntro {
		margin: 0 0 40px 20px;
	}

	.lp-homepageCoachWrap ul {
		padding: 0 20px;
	}

	.lp-homepageCoachWrap .lp-coachName {
		height: 75px;
	}

	.lp-coachName b {
		display: inline-block;
		line-height: 15px !important;
	}

	.lp-homepageDoubleBoxOne {
		padding: 20px 50px 20px 20px;
	}

	.lp-homepageDB1 {
		background: url("../img/homepagebanners-specjalisci.jpg") center right -50px no-repeat;
	}

	.lp-refOne img {
		width: 90%;
	}

	/* footer */
	#site-footer {
		padding: 0 20px;
		min-width: 100%;
		box-sizing: border-box;
	}

	.site-map {
		width: 100%;
		padding-top: 20px;
	}

	.site-map>li {
		width: 17%;
		margin: 0;
	}

	.additional-footer {
		padding: 0 20px;
		width: calc(100% + 40px);
		margin-left: -20px;
		box-sizing: border-box;
	}

	.additional-footer p {
		left: 0;
		margin: 15px 0 0;
	}

	.additional-footer p:first-child+p {
		left: 0;
		margin: 0;
		width: 50%;
		display: inline-block;
		height: 30px;
		white-space: normal;
		line-height: 15px;
		padding: 14px 0;
	}

	/* nav mobile */


	/* podstrona */
	.lp-newTopZbiorczy .lp-tabsMoreLink {
		display: block;
		width: 90px;
		text-align: center;
		margin: 10px 0 0 !important;
	}

	/* Portfolio */
	.portfolio {
		width: 100% !important;
		margin: 0 auto !important;
	}

	.portfolio-box {
		width: 49% !important;
		box-sizing: border-box !important;
	}
}

/* Small Tablets & Phones */
@media only screen and (max-width: 769px) {
	.lp-homeTagWrap .slick-arrow {
		display: none !important;
	}

	.lp-homeTags {
		width: 100%;
	}

	.lp-homepageSB3 {
		background-position: center right;
	}

	.site-map>li {
		width: 25%;
		margin-bottom: 15px;
		margin-top: 15px;
	}

	.lp-contactSingle {
		width: 30%;
	}

	.lp-contactLineContent span {
		display: inline-block;
		line-height: 15px !important;
	}

	.contactTwo {
		margin-left: 1%;
		margin-right: 1%;
	}

	.lp-homepageCoachWrap ul li {
		width: 33%;
		padding: 0 20px;
		box-sizing: border-box;
	}

	.lp-homepageSB1 {
		background-repeat: no-repeat;
		background-position: center right;
		background-color: #e0e4f0;
	}
}

@media only screen and (max-width: 769px) {
	.lp-homepageSingleBox {
		width: 49%;
	}

	.lp-homepageSB3 {
		width: 100%;
		margin-top: 15px;
		background-repeat: no-repeat;
	}

	.lp-homepageSingleBox:nth-child(2) {
		margin-right: 0;
	}

	.lp-homepageCoachWrap ul li {
		width: 50%;
	}

	.lp-homepageDoubleBoxOne {
		width: 100%;
	}

	.lp-homepageDoubleBoxOne:first-child {
		margin-right: 0;
		margin-bottom: 20px;
	}

	.lp-contactSingle.contactOne {
		display: none;
	}

	.lp-contactSingle {
		width: 43%;
	}

	.lp-homepageRefSingle {
		width: 100%;
		float: none;
	}

	.lp-refOne {
		text-align: center;
	}

	.lp-refOne h2 {
		text-align: left;
	}

	.lp-refOne img {
		width: 100%;
		max-width: 420px;
		margin-bottom: 40px;
	}

	.site-map {
		text-align: left;
	}

	.site-map h3 {
		margin-top: 16px;
	}

	.site-map:before,
	.site-map:after {
		display: block;
		content: " ";
		width: 100%;
		clear: both;
		margin: 0;
		padding: 0;
		height: 0;
	}

	.site-map>li~li {
		margin: 0;
	}

	.site-map>li {
		display: inline-block;
		width: 49%;
		margin: 10px 0 20px;
		padding: 0;
		float: none;
	}

	.lp-homepageDB2 {
		background-color: #1a274a;
		background-position: center right;
		background-image: url("../img/mobile-homepagebanners-administracja.jpg");
	}

	.lp-homepageSB3 {
		background-color: #edf1f8;
		background-image: url("../img/mobile-specjalisci.jpg");
	}

	.lp-homepageSB2 {
		background-color: #97cbfb;
		background-image: url("../img/mobile-homepage-kursy.jpg");
	}

	.lp-homepageDB1 {
		padding-right: 200px;
		background-color: #7e7d7e;
		background-image: url("../img/mobile-homepagebanners-specjalisci.jpg");
	}

	.lp-homepageSB3 p {
		width: 85%;
		margin-left: auto;
		margin-right: auto;
	}
}

@media only screen and (max-width: 760px) {
	.lp-newTop.lp-courseTop {
		overflow-y: hidden;
	}

	.lp-articleBottom-left,
	.lp-articleBottom-right {
		width: 100%;
	}

	.lp-articleBottom-right {
		margin-top: 20px;
	}

	/* Podstrony kursow */
	.lp-courseTop .lp-cityRow h5,
	.lp-courseTop .lp-cityRow p.h5 {
		float: none;
		display: block;
		width: 100%;
		text-align: left;
	}

	.lp-cityRow ul {
		width: 100%;
		float: none;
		display: block;
		margin: 0;
	}

	.lp-courseLegend {
		margin-bottom: 20px;
	}

	/* Podstrony kursów TABLET */
	/* Sekcje Globalne */
	.lp-oneIconSection p,
	.lp-oneIconSection h3,
	.lp-oneIconSection h4,
	.lp-subHeader {
		width: 100% !important;
		margin-left: 0 !important;
	}

	/* Wyszkoliliśmy ponad 7 000 osób */
	.lp-howMany {
		margin-top: 90px !important;
	}

	.lp-howMany:before {
		top: -59px !important;
		left: 50% !important;
		margin-left: -39px !important;
	}

	.lp-oneIconSection.lp-howMany p {
		width: 100% !important;
		margin-left: 0 !important;
	}

	/* Interesuje Cię bardziej back-end */
	.lp-courseProgramPHP a:before {
		top: 50% !important;
		margin-top: -34px !important;
	}

	/* Box z krzeslem */
	.lp-boxRaisePhoto {
		width: 100% !important;
		padding: 20px !important;
	}

	.lp-boxRaisePhoto:after {
		display: none !important;
	}

	.lp-boxRaisePhoto p {
		width: 100% !important;
	}

	.lp-boxRaisePhoto p:before {
		bottom: -11px !important;
		left: -11px !important;
	}

	/* Listy z ikonkami */
	.lp-oneIconSection {
		width: 100%;
	}

	.lp-oneIconSection h4,
	.lp-oneIconSection p {
		width: calc(100% - 120px) !important;
		margin-left: 120px !important;
	}

	/* ludzie - referencje */
	.lp-recoManLeft {
		width: calc(100% - 180px) !important;
	}

	/* firmy - referencje */
	.lp-courseRefRight {
		width: calc(100% - 200px) !important;
	}

	/* inne */
	.lp-possible .lp-subHeader {
		width: calc(100% - 140px) !important;
		margin: 0 !important;
		padding: 0 0 0 120px;
	}

	.lp-bgLeft:before {
		display: none !important;
	}

	/* WWW - GRAFIKA */
	.lp-phpshadowTop,
	.lp-phpshadowBottom {
		display: none !important;
	}

	.lp-bgLeft {
		margin: 0 !important;
		width: 100% !important;
		background: transparent !important;
	}

	.lp-bgLeft .lp-subHeader {
		width: 100%;
		margin: 0 !important;
	}

	.lp-bgLeft p {
		width: 100% !important;
		margin: 20px 0 0 !important;
	}
}

/* background's */
@media only screen and (max-width: 680px) {

	.lp-courseDates-cointainer .lp-coursePrice {
		min-width: 56px !important;
	}

	.lp-courseDates-cointainer .lp-courseDates {
		min-width: 262px;
	}

	.portfolio-box {
		position: relative !important;
		height: 350px !important;
	}

	.portfolio-box img {
		position: absolute !important;
		bottom: -50px !important;
	}

	.portfolio a:nth-child(3) img,
	.portfolio a:nth-child(5) img,
	.portfolio a:nth-child(6) img,
	.portfolio a:nth-child(7) img {
		bottom: -5px !important;
	}

	.lp-newTop-header p:last-child {
		margin-bottom: 40px !important;
	}
}

/* Phones */
@media only screen and (max-width: 580px) {
	#article table {
		width: 100%;
	}

	.lp-newTop.lp-zbiorczyMax .lp-newTop-rating {
		top: 86px !important;
	}

	.lp-courseDates-cointainer .lp-cityRow h5,
	.lp-courseDates-cointainer .lp-cityRow p.h5 {
		padding-right: 8px;
	}

	.article p[align="right"] {
		text-align: left !important;
	}

	.article p img[align="left"] {
		display: block;
		float: none;
		margin-bottom: 20px;
		text-align: center;
	}

	table.kontakt {
		width: 100%;
		margin: 0;
	}

	.tel>strong,
	.tel>em {
		display: block;
	}

	.article .addresses {
		margin: 0;
	}

	.addresses>li {
		width: 100%;
		margin: 10px auto;
		display: block;
		box-sizing: border-box;
	}

	.form {
		width: 100%;
		margin: 0;
	}

	.form textarea {
		width: 90%;
	}

	.lp-newTop .lp-trainingCity h3,
	.lp-newTop .lp-trainingCalendar th,
	.lp-newTop .lp-trainingCalendar th strong {
		font-size: 11px;
	}

	.lp-newTop .lp-trainingCalendar th {
		padding-right: 5px;
	}

	.date-format-full {
		display: none;
	}

	.date-format-short {
		display: block !important;
		font-size: 11px;
		letter-spacing: -0.5px;
	}

	.lp-homepageDB1 {
		padding-right: 70px;
	}

	.site-map>li {
		width: 100%;
	}

	.lp-homepageCoachWrap ul li {
		width: 100%;
	}

	.lp-homepageSingleBox {
		width: 100%;
		margin: 0;
	}

	.lp-homepageSingleBox:nth-child(2) {
		margin: 20px 0;
	}

	.additional-footer p,
	.additional-footer p:first-child+p {
		display: block;
		width: 100%;
		text-align: left;
		float: none;
	}

	.additional-footer p:first-child+p {
		margin-bottom: 20px;
	}

	.lp-contactSingle {
		display: block;
		float: none;
		width: 100%;
	}

	.lp-contactLineWrap {
		height: auto;
	}

	.lp-contactLineContent {
		padding-top: 30px;
		padding-bottom: 30px;
	}

	.contactTwo,
	.contactThree {
		margin: 0;
		left: 0;
		padding-left: 60px;
	}

	.contactThree:before {
		left: 2px;
	}

	/* HomeHaeder*/
	.lp-homePageTop .lp-textBox {
		width: 100%;
		border-top: 0;
		overflow: hidden;
	}

	.lp-homePageTop .lp-textBox ul {
		width: 100%;
		text-align: left;
	}

	.lp-homePageTop .lp-textBox ul.ui-tabs-nav {
		margin-top: 0;
		overflow-y: hidden;
		overflow-x: scroll;
		white-space: nowrap;
		height: 39px;
	}

	.lp-homePageTop .ui-tabs-nav li {
		display: inline-block;
		padding: 0;
		margin: 0 10px 0 0 !important;
	}

	.lp-homePageTop .lp-textBox ul li.ui-tabs-active {
		margin: 0;
		border-right: 0;
		border-bottom: 2px solid #fcaf12;
	}

	.lp-homePageTop .lp-akredytacja {
		right: 160px;
	}

	.lp-homePageTop .lp-textBox .ui-tabs-panel {
		width: 100%;
		min-height: auto;
		border-left: 0;
		margin: 0 !important;
		padding-left: 0;
		padding-bottom: 0;
	}

	.lp-homePageTop .ui-tabs-nav li:first-child,
	.lp-homePageTop .lp-textBox ul li.ui-tabs-active:first-child {
		margin-left: 0px;
	}

	.lp-newTop {
		padding-top: 30px !important;
	}

	.lp-newTop.lp-homePageTop {
		padding-top: 90px !important;
	}

	.lp-courseTop .lp-newTop-rating {
		position: absolute;
		top: -32px;
		right: auto;
		left: 10px;
	}

	.lp-courseTop .lp-newTop-header {
		width: 100%;
	}

	.lp-articleBottom-right {
		height: 272px !important;
	}

	/* Podstrona */
	.lp-zbiorczyMedium .lp-newTop-rating {
		position: absolute;
		top: 80px;
	}

	.lp-newTop-left,
	.lp-zbiorczyMedium .lp-newTop-header {
		width: 100%;
	}

	.lp-zbiorczyMedium .lp-newTop-People {
		position: absolute;
		top: 82px;
		right: 0;
	}

	.lp-newTop-right {
		width: 100%;
		margin: 40px 0 0;
	}

	.lp-homePageTop .lp-textBox .ui-tabs-panel ul {
		margin-left: 0;
		margin-bottom: 0;
		padding-bottom: 0;
		border-bottom: 0;
	}

	.lp-homePageTop .lp-textBox ul li {
		border: 0;
	}

	.lp-homepageSingleBox {
		height: 330px;
	}

	.lp-homepageDoubleBoxOne {
		padding: 15px 20px 20px 15px;
	}

	.lp-homepageDoubleBoxOne .lp-homepageBtn {
		bottom: 15px;
	}

	.lp-homepageDB2 .lp-homepageH2Header {
		width: 90%;
	}

	.lp-homepageDB1 {
		background-position: right -75px center;
	}

	.lp-homepageDB2 {
		background-position: right -70px center;
	}

	.lp-homepagePeopleRef {
		padding-left: 0;
		margin-top: 30px;
	}

	.site-map {
		padding-top: 0;
	}

	.site-map>li {
		margin: 0;
	}

	.site-map>li>ul {
		margin-bottom: 0;
	}

	.site-map>li>ul:first-child {
		margin-top: 0;
	}

	.lp-homePageTop .lp-newTop-content,
	.lp-homeTagWrap {
		padding: 0 10px;
	}

	.lp-homepageBox,
	.lp-homepageDoubleBox,
	.lp-homepageRefWrap {
		padding-left: 10px;
		padding-right: 10px;
	}

	.lp-homepageCoachIntro {
		width: 90%;
		margin: 0 10px 40px 10px;
	}

	.lp-refTwo {
		padding-left: 0;
	}

	.lp-newTop.lp-courseTop {
		padding-top: 65px !important;
		overflow-x: hidden;
	}

	.lp-newTop-content {
		padding: 0 10px;
	}

	#article {
		width: 100%;
		box-sizing: border-box;
		padding: 12px;
	}

	.lp-newTop.lp-newTopZbiorczy {
		padding-top: 70px !important;
	}

	.lp-newTop.lp-newTopZbiorczy.lp-zbiorczyMedium .lp-newTop-rating {
		top: 63px;
	}

	.lp-newTop.lp-newTopZbiorczy.lp-zbiorczyMedium .lp-newTop-People {
		top: 55px;
	}

	.lp-newTop.lp-newTopZbiorczy .lp-newTop-rating {
		position: absolute;
		top: 93px;
	}

	h2 span {
		margin-left: 0;
	}

	.lp-courseDates-cointainer {
		width: 100% !important;
	}

	.lp-courseDates-cointainer table {
		margin: 0;
	}

	.lp-newTop-People:before {
		margin: 0 4px 0 0;
	}

	.lp-newTop-People {
		width: 164px;
	}

	.lp-newTop-rating .stars-cointaner {
		margin-left: 5px;
	}



	#content-header {
		width: 100%;
	}



	#content-header .nav {
		top: 0;
	}

	.zadaj-szybkie-pytanie {
		width: 300px;
		right: 10px;
		height: 35px;
	}

	.lp-rightSlidePanel {
		right: 10px;
	}

	.lp-rightSlidePanel:hover,
	.lp-rightSlidePanel:focus,
	.lp-rightSlidePanel:active {
		right: 10px;
	}

	.lp-rightSlidePanel-right {
		width: 300px;
		right: 0px;
	}

	.lp-courseTop .lp-newTop-rating .rate {
		width: auto;
		text-align: left !important;
	}

	/* Podstrony kursów MOBILE */

	/* lista z ikonkami */
	.lp-oneIconSection h4,
	.lp-oneIconSection p {
		width: 100% !important;
		margin-left: 0 !important;
	}

	.lp-oneIconSection:before {
		position: relative !important;
		top: 0 !important;
		left: 0 !important;
		margin: 50px auto 20px !important;
	}

	.lp-recoManRight {
		float: left !important;
		right: auto !important;
		left: 50% !important;
		top: 0 !important;
		margin: 0 0 0 -75px !important;
	}

	.lp-recoManLeft {
		width: 100% !important;
		float: left !important;
		position: relative !important;
		margin: 170px auto 0px;
	}

	.lp-recoTriangle,
	.lp-recoManLeftImg .lp-recoTriangle {
		top: -19px !important;
		right: auto !important;
		left: 50% !important;
		margin: 0 0 0 -7.5px !important;
		transform: rotate(270deg) !important;
	}

	/* Info - szare tlo */
	.lp-possible {
		padding: 5px 20px 20px !important;
		box-sizing: border-box;
	}

	.lp-possible .lp-subHeader {
		width: 100% !important;
		padding: 0 !important;
	}

	.lp-possible:before {
		top: -15px !important;
	}

	/* referencje klientow */
	.lp-courseRefLeft {
		float: none !important;
		margin: 40px auto 20px !important;
	}

	.lp-courseRefRight {
		float: none !important;
		width: 100% !important;
	}

	.lp-courseRefLeft.ref1 img,
	.lp-courseRefLeft.ref2 img,
	.lp-courseRefLeft.ref3 img,
	.lp-courseRefLeft.ref4 img {
		top: 0 !important;
	}

	/* wygaszenie homepage dark */
	.lp-homePageTop .lp-textBox:after {
		opacity: 0.6;
		position: absolute;
		top: 0;
		right: 0;
		width: 55px;
		height: 50px;
		display: block;
		content: " ";
		background: linear-gradient(to right,
				rgba(255, 255, 255, 0) 0%,
				rgba(42, 54, 66, 0.1) 10%,
				rgba(42, 54, 66, 1) 100%);
	}

	/* wygaszenie homepage white */
	.lp-homeTagWrap .slick-next.slick-arrow {
		display: block !important;
		position: absolute;
		right: 0;
		background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 1) 100%);
	}

	.lp-homePageTop .lp-akredytacja {
		right: 10px;
		width: 150px;
		bottom: -200px;
		background-size: contain;
	}

	/* zadaj pytanie - resize */
	.lp-rightSlidePanel-left {
		width: 120px;
		height: 35px;
		background: url("../img/masz-pytanie-sprite.png") no-repeat;
		background-position: 0 -45px;
	}

	.zadaj-szybkie-pytanie:focus .lp-rightSlidePanel,
	.zadaj-szybkie-pytanie:active .lp-rightSlidePanel {
		bottom: 0 !important;
	}

	/* stickyTop */


	.tabcontenttworzymy-oprogramowanie img {
		width: 100%;
		margin: 0 auto;
	}

	.lp-newTop.lp-newTopZbiorczy.lp-noCoach .lp-newTop-rating {
		position: absolute;
		top: -36px;
		left: 10px;
	}

	.lp-newTop.lp-newTopZbiorczy.lp-noCoach .lp-newTop-People {
		position: absolute;
		top: -50px;
		right: 10px;
		margin: 0;
	}

	.lp-articleBottom .lp-cityRow ul {
		width: 100%;
	}

	.lp-cityRow h5,
	.lp-cityRow p.h5 {
		float: none;
		text-align: left;
	}
}

@media only screen and (max-width: 530px) {

	.additional-styles-special ul {
		width: 100% !important;
	}

	.lp-phptop-content {
		width: calc(100% - 100px) !important;
	}

	.additional-styles-special .lp-platnoscRatalna {
		width: 100% !important;
		margin: 0 !important;
		left: 0 !important;
	}

	.additional-styles-special {
		padding: 10px !important;
	}

	.lp-articleBottom {
		width: 100% !important;
		margin: 50px 0;
	}

	.lp-articleBottom-left,
	.lp-articleBottom-right {
		width: 100%;
		margin-left: auto !important;
		margin-right: auto !important;
	}

	h2 span {
		font-size: 18px;
	}

	.lp-specialSection-paragraf {
		width: 95%;
	}

	.lp-homePageTop .lp-textBox ul.ui-tabs-nav {
		border-bottom: 0;
		margin-bottom: 15px;
	}

	.portfolio-box {
		float: none !important;
		width: 100% !important;
		height: auto !important;
	}

	.portfolio-box img {
		width: 100% !important;
		position: relative !important;
	}

	.portfolio a:nth-child(3) img,
	.portfolio a:nth-child(5) img,
	.portfolio a:nth-child(6) img,
	.portfolio a:nth-child(7) img {
		bottom: auto !important;
	}

	.portfolio a:nth-child(even) .portfolio-box {
		margin-left: auto !important;
	}
}

@media only screen and (max-width: 490px) {
	.lp-textBox {
		overflow: hidden;
	}

	.lp-textBox h2 {
		border-bottom: 0;
	}

	.lp-textBox ul {
		width: 100%;
		float: none;
		margin: 0 0 25px;
		padding: 0 0 15px 0;
		text-align: left;
		border-bottom: 1px solid #e0e0e0;
	}

	.lp-textBox ul li {
		display: inline-block;
		margin: 0 12px 5px 0;
		padding: 0 0 2px 0;
		border-bottom: 2px solid #fff;
	}

	.lp-textBox .ui-tabs-panel {
		width: 100%;
		float: none;
		padding: 0;
		border: 0;
		background: transparent;
	}

	.lp-textBox ul li.ui-tabs-active {
		border-right: 0;
		border-bottom: 2px solid #316b97;
	}

	.lp-phptop-label {
		width: 100% !important;
		float: none !important;
		text-align: left !important;
	}

	.lp-phptop-content {
		width: 100% !important;
		float: none !important;
		padding: 0 !important;
	}

	.lp-coachSingleContainer {
		display: block !important;
		position: relative;
	}

	.lp-singleDescription {
		display: block !important;
		width: 100% !important;
	}

	.lp-singleDescription {
		padding-right: 0 !important;
	}

	.lp-singleDescription h3,
	.lp-singleDescription h4 {
		width: calc(100% - 95px);
		padding-right: 95px;
	}

	.lp-coachSingleImageWrap {
		display: block !important;
		width: 90px !important;
		position: absolute;
		top: -18px;
		right: 0;
	}

	.lp-coachSingleImage {
		width: 85px !important;
		height: 85px !important;
	}

	.lp-singleDescription p {
		margin-top: 35px !important;
	}

	.lp-coachSingleMore {
		padding-top: 0 !important;
	}

	#article .lp-gallery img {
		width: 32% !important;
	}

	.additional-styles-special .lp-header-01 {
		font-size: 24px !important;
		line-height: 1em !important;
	}

	.additional-styles-special .lp-header-02 {
		width: 100% !important;
		font-size: 24px !important;
		line-height: 1em !important;
	}
}

@media only screen and (max-width: 390px) {

	.lp-zbiorczyMedium .lp-newTop-People {
		top: 109px;
	}

	.lp-zbiorczyMedium .lp-newTop-rating {
		top: 119px;
	}

	.lp-newTop.lp-newTopZbiorczy .lp-newTop-rating {
		position: absolute;
		top: 120px;
	}
}

@media only screen and (max-width: 350px) {
	.lp-articleBottom-right {
		height: 319px !important;
	}
}

/* flexnav height */
.flexnav.flexnav-show {
	max-height: 4350px !important;
}

/* Tabela Zbiorcza - wersja II */
@media only screen and (max-width: 630px) {
	.lp-courseDates-cointainer {
		width: calc(100% + 40px);
	}

	/* BLOCK */
	.lp-courseDates-cointainer table,
	.lp-courseDates-cointainer table tbody,
	.lp-courseDates-cointainer tr.lp-courseRow,
	.lp-courseDates-cointainer tr.lp-courseRow td {
		display: block;
		width: 100%;
		max-width: 100%;
		min-width: auto;
	}

	.lp-courseDates-cointainer thead {
		display: none;
	}

	.lp-courseDates-cointainer tr.lp-courseRow {
		position: relative;
	}

	.lp-courseDates-cointainer table td.lp-courseName {
		width: calc(100% - 100px) !important;
		border: 0;
	}

	.lp-courseDates-cointainer tr:nth-child(even).lp-courseRow td {
		border: 0;
	}

	.lp-courseDates-cointainer table td.lp-courseTime,
	.lp-courseDates-cointainer table td.lp-coursePrice {
		text-align: right;
		width: 100px;
	}

	.lp-courseDates-cointainer table td.lp-courseTime {
		position: absolute;
		top: 0;
		right: 0;
		border: 0;
		width: 100px;
		text-align: right;
		display: none;
	}

	.lp-courseDates-cointainer table td.lp-coursePrice {
		position: absolute;
		top: 0;
		right: 0;
	}

	.lp-borderBottomBlue,
	.lp-courseDates-cointainer td.lp-courseCategory {
		width: 100%;
		display: block;
	}

	.lp-courseDates-cointainer tr:first-child {
		border: 0;
	}

	.lp-courseDates-cointainer .lp-borderBottomBlue,
	.lp-courseDates-cointainer .lp-borderBottomBlue:first-child {
		border: 0;
	}

}

/* formularz zgloszeniowy */
@media only screen and (max-width: 760px) {
	.form.formularz_zgloszeniowy textarea {
		width: 97%;
	}
}

@media only screen and (max-width: 715px) {
	#id_captcha {
		margin-bottom: 10px;
	}

	input[name="captcha"]+img {
		margin-left: 0;
	}
}

@media only screen and (max-width: 580px) {

	.form.formularz_zgloszeniowy table.fieldset,
	.form.formularz_zgloszeniowy table.fieldset tbody,
	.form.formularz_zgloszeniowy table.fieldset tr {
		position: relative;
		width: 100%;
		display: block;
	}

	.form.formularz_zgloszeniowy table.fieldset td {
		width: 95%;
		margin: 0 auto;
		display: block;
		padding-right: 0px !important;
		padding-left: 0px !important;
	}

	.form.formularz_zgloszeniowy table.fieldset .field.control {
		width: 100%;
		box-sizing: border-box;
	}

	label[for="id_bylem_wczesniej"],
	label[for="id_chce_zapisac_sie_na_newsletter"],
	label[for="id_akceptuje_regulamin"],
	label[for="id_akceptuje_regulamin"]+small {
		padding-left: 40px !important;
		box-sizing: border-box;
		display: block;
		width: 90% !important;
	}

	input#id_bylem_wczesniej {
		position: absolute;
		top: 36px;
		left: 20px;
	}

	input#id_chce_zapisac_sie_na_newsletter,
	input#id_akceptuje_regulamin {
		position: absolute;
		top: 60px;
		left: 20px;
	}

	td.input+input#id_bylem_wczesniej {
		background: red;
	}

	input[name="captcha"]+img {
		display: block;
	}
}

.lp-k-android-long {
	background-color: #ffd302 !important;
}

/* Klasy BG */
@media only screen and (max-width: 760px) {
	.lp-bg-c-prog-intro {
		background-image: url("../img/mobile-bg-C-PROG-INTRO.jpg") !important;
		background-position: bottom right !important;
	}

	.lp-k-admin {
		background-image: url("../img/K-ADMIN-mobile.jpg") !important;
	}

	.lp-k-admin-s {
		background-image: url("../img/mobile-bg-K-ADMIN-S.jpg") !important;
	}

	.lp-k-www {
		background-image: url("../img/www-courseHeader_bg_2itKjxU.jpg") !important;
	}

	.lp-k-php {
		background-image: url("../img/mobile-bgPHPnew.jpg") !important;
		background-position: bottom right !important;
	}

	.lp-k-php-s {
		background-image: url("../img/mobile-bg-K-PHP-S.jpg") !important;
		background-position: bottom right !important;
	}

	.lp-k-prog-intro-j {
		background-image: url("../img/mobile-bg-K-JAVA2.jpg") !important;
	}

	.lp-k-java {
		background-image: url("../img/mobile-k-java-bg-new.jpg") !important;
	}

	.lp-k-android-long {
		background-image: url("../img/mobile-bg-android-developer-2.jpg") !important;
		background-size: cover !important;
	}

	.lp-k-analiza {
		background-image: url("../img/mobile-analiza-danych-courseHeader_bg.jpg") !important;
	}

	.lp-k-excel {
		background-image: url("../img/mobile-excelTopBg.jpg") !important;
		background-position: bottom right !important;
	}
}

@media only screen and (max-width: 760px) {
	.lp-proLink .lp-subHeader {
		margin-top: 80px !important;
	}

	.lp-proLink span {
		top: -55px !important;
		left: 36px !important;
	}

	.lp-specialReco.lp-recoManRight {
		position: relative !important;
		float: none !important;
		margin: 0 auto !important;
		left: 0 !important;
		right: 0 !important;
	}

	.lp-specialReco.lp-recoManRight img {
		position: absolute;
		top: 0 !important;
		left: 0 !important;
		margin-top: 0 !important;
	}

	.lp-obrazekRight {
		background: none !important;
	}

	.additional-styles h2 {
		text-align: left;
	}

	.lp-paragraf-image-right p {
		float: none !important;
		width: 100% !important;
	}

	.lp-paragraf-image-right img {
		display: none !important;
	}

	.lp-numberList li:nth-child(1):before {
		display: none !important;
	}

	.lp-courseProgramJavaIntro a:before {
		top: 9px !important;
		left: 0 !important;
	}

	.lp-courseProgramJavaIntro a {
		padding-left: 72px;
	}

	.lp-seeMore-Wrap {
		position: relative;
		width: 100%;
		margin-bottom: 20px;
		overflow-x: scroll;
		overflow-y: hidden;
	}

	.lp-seeMore-Wrap ul {
		width: 1000px;
		margin: 0;
		padding: 0;
	}

	.lp-seeMore-Wrap:after {
		position: absolute;
		top: 0;
		right: 0;
		z-index: 10;
		display: block;
		content: " ";
		width: 60px;
		height: 46px;
		background: linear-gradient(to right,
				rgba(255, 255, 255, 0) 0%,
				rgba(255, 255, 255, 1) 100%);
	}

	.lp-seeMore-Wrap ul li {
		margin-bottom: 0;
		padding-bottom: 0px;
	}

	#lp-movie iframe {
		width: 100% !important;
		height: 280px !important;
	}

	/* Bootcamp mobile */
	.lp-AndroidLogo:before {
		display: none !important;
	}

	.lp-AndroidLogo h2.lp-header-01 {
		width: 100% !important;
	}

	.lp-AndroidLogo h2.lp-header-01,
	.lp-AndroidLogo h1.lp-header-02 {
		padding: 0 20px !important;
		box-sizing: border-box;
	}

	.lp-k-android-long {
		padding-top: 20px !important;
		padding-bottom: 20px !important;
	}

	.lp-proLink.lp-androidMobile {
		top: 0 !important;
		margin-bottom: 70px !important;
	}

	.lp-proLink.lp-androidMobile span {
		top: 20px !important;
	}

	.lp-android-pro {
		width: 92% !important;
		margin-left: auto !important;
		margin-right: auto !important;
		background-color: #211f22 !important;
		background-image: url("../img/mobile-android-pro.jpg") !important;
	}

	.lp-obrazekRight p {
		width: 100% !important;
	}

	.lp-android-schemat {
		width: 100%;
	}

	.lp-our-apps {
		display: none;
	}

	.lp-android-p-width img {
		display: none;
	}

	.lp-android-p-width p:first-child,
	.lp-android-p-width ul {
		width: 100% !important;
	}

	.lp-android-data li {
		width: 48% !important;
	}

	.lp-android-korzysci {
		width: 100% !important;
	}
}

@media only screen and (max-width: 580px) {
	.lp-frontEnd a:before {
		top: 10px !important;
	}

	.lp-proLink.K-PHP span {
		top: -65px !important;
		width: 255px !important;
	}

	.lp-proLink.K-PHP span:before {
		right: 267px !important;
	}

	.lp-recoMan.K-PHP-reco .lp-recoManRight {
		position: relative !important;
		float: none !important;
		right: 0 !important;
		left: 0 !important;
		top: 0 !important;
		margin: 0 auto !important;
	}

	.lp-recoMan.K-PHP-reco .lp-recoManRight img {
		top: 0 !important;
		left: 0 !important;
		margin: 0 !important;
	}

	.lp-newTop.lp-courseTop .lp-cityRow ul {
		width: 100% !important;
	}

	.lp-newTop.lp-courseTop .lp-cityRow ul li {
		font-size: 11px;
		letter-spacing: -0.2px;
	}

	.lp-newTop.lp-courseTop .lp-cityRow ul li strong {
		font-size: 12px;
	}

	.lp-imgLeftSide001:before {
		top: 72px !important;
	}

	.lp-linkSection.lp-courseProgramJavaIntro a {
		padding-left: 67px !important;
	}

	/* Bootcamp mobile */
	.lp-AndroidLogo h2.lp-header-01,
	.lp-AndroidLogo h1.lp-header-02 {
		padding: 0px !important;
	}

	.lp-k-android-long .lp-platnoscRatalna.lp-ratyPro:before {
		height: 66px !important;
		top: 23px !important;
		margin-right: 14px !important;
	}

	.lp-android-numbers {
		height: auto !important;
	}

	.lp-android-numbers ul li {
		width: 100% !important;
		float: none !important;
		margin: 10px auto 50px !important;
	}

	.lp-android-numbers ul li p {
		margin-top: 4px;
	}

	.lp-proLink.lp-androidMobile {
		top: 0 !important;
		margin-bottom: 90px !important;
	}

	.lp-proLink.lp-androidMobile span {
		width: 225px !important;
	}

	.lp-proLink.lp-androidMobile span:before {
		right: 235px;
	}

	.lp-disableDiv {
		display: none;
	}

	.lp-android-pro {
		width: 100%;
		box-sizing: border-box;
	}

	.lp-android-pro-content {
		width: 100% !important;
	}

	.lp-android-data li {
		width: 100% !important;
		float: none !important;
		margin: 20px auto 50px !important;
	}
}

/* Klasy ogólne mobilne */
@media only screen and (max-width: 760px) {
	#site-footer {
		margin-top: 50px;
	}

	.lp-topCoursePriceRight {
		display: none;
	}

	.lp-mobileHide {
		display: none !important;
	}

	.lp-mobileImg {
		width: 100%;
	}

	.lp-mobileTable {
		width: 100%;
		font-size: 11px;
		line-height: 11px;
	}

	.lp-mobileTable td {
		padding: 4px !important;
	}
}

@media only screen and (max-width: 590px) {
	.lp-imgMobile {
		max-width: 100%;
		display: block;
		margin: 10px auto !important;
		float: none;
	}
}

/* Search */
#lp-search-btn {
	display: none;
}

@media only screen and (max-width: 580px) {
	#lp-search-btn {
		position: relative;
		top: 5px;
		left: 5px;
		z-index: 10000;
		display: block;
		float: left;
		width: 35px;
		height: 35px;
		overflow: hidden;
		text-indent: -600px;
		border: 0;
		background: url("../img/search-2x.png") center no-repeat;
		background-size: 18px 18px;
	}

	.top-nav.searchbox-dynamic-height {
		height: 50px !important;
	}

	.top-nav.searchbox-dynamic-height .searchbox {
		display: block;
		top: 12px;
	}

	.top-nav.searchbox-dynamic-height form {
		z-index: 0;
	}

	/* animacje */
	.top-nav {
		transition: height 0.45s cubic-bezier(0.51, 0.01, 0.46, 0.92);
	}

	.fieldset ul li {
		position: relative;
	}

	input#id_akceptuje_regulamin,
	input#id_chce_zapisac_sie_na_newsletter {
		top: 8px;
		left: 10px;
	}

	input#id_chce_zapisac_sie_na_newsletter {
		top: 29px;
	}

	label[for="id_chce_zapisac_sie_na_newsletter"]+small {
		padding-left: 40px !important;
		box-sizing: border-box;
		display: block;
		width: 90% !important;
	}
}

/* dodatkowe podstrony  */
@media only screen and (max-width: 1020px) {

	#tab-spis ul.kursy li,
	#tab-spis ul.szkolenia li {
		width: 48%;
		box-sizing: border-box;
		padding: 0;
		margin: 0 0 10px 0;
	}

	/* ukrycie zadan pytanie */
	.zadaj-szybkie-pytanie {
		display: none !important;
	}
}

@media only screen and (max-width: 800px) {

	.filtr_najblizsze form,
	table.courses-data {
		width: 100%;
		min-width: auto;
		margin: 0 0 10px 0;
	}
}

@media only screen and (max-width: 700px) {
	.sciezka_container {
		width: 100% !important;
		box-sizing: border-box;
	}

	#scrollpane-front-end,
	#scrollpane-senior-php,
	#scrollpane-operations,
	#scrollpane-enterprise,
	#scrollpane-mobile {
		width: 100% !important;
		overflow-x: scroll;
	}

	#innerscrollpane-front-end,
	#innerscrollpane-senior-php,
	#innerscrollpane-operations,
	#innerscrollpane-enterprise,
	#innerscrollpane-mobile {
		width: 100% !important;
	}

	#innerscrollpane-front-end embed,
	#innerscrollpane-senior-php embed,
	#innerscrollpane-operations embed,
	#innerscrollpane-enterprise embed,
	#innerscrollpane-mobile embed {
		width: 1300px !important;
	}
}

@media only screen and (max-width: 650px) {
	table.courses-data thead {
		display: none;
	}

	table.courses-data tr,
	table.courses-data td {
		display: block;
		width: 100%;
		display: block;
		border: 0;
		box-sizing: border-box;
	}
}

@media only screen and (max-width: 530px) {

	#tab-spis ul.kursy li,
	#tab-spis ul.szkolenia li {
		width: 100%;
	}
}

/* Floating TOP*/
@media only screen and (max-width: 580px) {

	#site-header {
		position: fixed;
		z-index: 10;
		top: 0px;
		width: 100%;
	}

	#site-header .top-nav {
		position: relative;
	}
}

/* FIX IE */
@media screen and (min-width: 0) and (min-resolution: +72dpi) {
	.flexnav li {
		line-height: 0;
	}

	.flexnav li a {
		padding: 30px 20px;
	}

	.flexnav li ul {
		background: #256499;
	}

	.flexnav .touch-button .navicon {
		top: 27px;
	}

	.flexnav li.lp-eng a {
		padding: 29px 20px 29px 65px;
	}
}

/* Wstazka */
a[name="trener-40"] {
	display: block;
	content: " ";
	width: 47px;
	height: 47px;
	background: url("../img/lp-wstazka.png") no-repeat;
	position: relative;
	top: 200px;
	left: 76%;
}

@media only screen and (max-width: 740px) {
	a[name="trener-40"] {
		left: 74%;
		top: 210px;
	}
}

@media only screen and (max-width: 657px) {
	a[name="trener-40"] {
		left: 71%;
		top: 210px;
	}
}

@media only screen and (max-width: 530px) {
	a[name="trener-40"] {
		left: 69%;
		top: 215px;
	}
}

@media only screen and (max-width: 490px) {
	a[name="trener-40"] {
		background-size: 70%;
		left: calc(100% - 100px);
		top: 120px;
		z-index: 10;
	}
}

/* Trenerzy */
.lp-specialTrainer {
	margin-top: 0;
}

/* Podstrona zbiorcza kursów */
.lp-courseAll-sprite,
.lp-btn-arrowDown:after,
.lp-btn-arrowLeft:after,
.lp-courseSingleTime:after,
.lp-courseSteps li:after,
.lp-courseMap-left ul li:after {
	background: url("../img/kursy_zbiorcze/kursy_zbiorcze_sprite.png") no-repeat;
}

.lp-newTop.lp-allCourses {
	padding-top: 70px;
	padding-bottom: 70px;
	padding-left: 40px;
	background: url("../img/kursy_zbiorcze/imgBg_zbiorcza.jpg") no-repeat #f0f0f0;
	background-position: center;
	box-sizing: border-box;
}

.lp-allCourses h1 {
	width: 55%;
	margin-bottom: 15px;
	font-family: "Open sans", sans-serif;
	font-size: 28px;
	line-height: 30px;
	font-weight: 600;
	color: #1c262f;
}

.lp-allCourses h2 {
	width: 65%;
	min-height: 40px;
	margin-bottom: 40px;
	font-family: "Open sans", sans-serif;
	font-size: 22px;
	line-height: 29px;
	font-weight: 300;
	color: #2a3642;
}

.lp-btn-blue {
	display: inline-block;
	position: relative;
	padding: 10px 30px 10px 12px;
	font-weight: 600;
	font-size: 12px;
	text-transform: uppercase;
	text-decoration: none;
	color: #fff;
	background: #256ebf;
	border-radius: 4px;
}

.lp-btn-arrowDown:after {
	position: absolute;
	top: 50%;
	right: 13px;
	margin-top: -4px;
	content: " ";
	display: block;
	width: 9px;
	height: 5px;
	background-position: -150px -200px;
}

.lp-btn-arrowLeft:after {
	position: absolute;
	top: 50%;
	right: 14px;
	margin-top: -5px;
	content: " ";
	display: block;
	width: 5px;
	height: 9px;
	background-position: -100px -200px;
}

.lp-btn-outline {
	background: transparent;
	border: 1px solid #256ebf;
	color: #256ebf;
	font-weight: 700;
}

.lp-btn-outline.lp-btn-arrowLeft:after {
	background-position: 0px -200px;
	width: 6px;
	height: 10px;
	margin-top: -6px;
}

.lp-allCoursesHeader h2 {
	margin-bottom: 10px;
	font-size: 28px;
	font-weight: 600;
}

.lp-allCoursesHeader p {
	font-size: 22px;
	line-height: 29px;
	font-weight: 300;
}

.lp-courseAll {
	width: 100%;
	display: block;
	margin-bottom: 50px;
}

.lp-courseSingle {
	display: inline-block;
	width: calc(25% - 20px);
	padding: 20px;
	margin: 0 20px 20px 0;
	border: 3px solid #e9e9e9;
	box-sizing: border-box;
	font-size: 13px;
	line-height: 18px;
}

.lp-courseSingle:nth-child(4n) {
	margin-right: 0;
}

.lp-courseSingle h2 {
	min-height: 42px;
	font-size: 16px;
	line-height: 20px;
	font-weight: 600;
	letter-spacing: -1px;
	color: #2a3642;
}

.lp-courseSingle p {
	min-height: 100px;
	margin-bottom: 20px;
}

.lp-courseSingleTime {
	position: relative;
	box-sizing: border-box;
	padding-left: 35px;
	margin-bottom: 20px;
}

.lp-courseSingleTime b {
	display: block;
}

.lp-courseSingleTime:after {
	position: absolute;
	top: 5px;
	left: 0;
	width: 27px;
	height: 26px;
	content: " ";
	display: block;
	background-position: -50px -200px;
}

.lp-courseSingleImg {
	width: calc(100% + 40px);
	height: 120px;
	margin: 20px 0;
	margin-left: -20px;
	overflow: hidden;
	text-indent: -2000px;
}

.lp-courseImg1 {
	background: url("../img/kursy_zbiorcze/imgCourse_7.jpg") no-repeat;
}

.lp-courseImg2 {
	background: url("../img/kursy_zbiorcze/imgCourse_8.jpg") no-repeat;
}

.lp-courseImg3 {
	background: url("../img/kursy_zbiorcze/imgCourse_4.jpg") no-repeat;
}

.lp-courseImg4 {
	background: url("../img/kursy_zbiorcze/imgCourse_6.jpg") no-repeat;
}

.lp-courseImg5 {
	background: url("../img/kursy_zbiorcze/imgCourse_5.jpg") no-repeat;
}

.lp-courseImg6 {
	background: url("../img/kursy_zbiorcze/imgCourse_3.jpg") no-repeat;
}

.lp-courseImg7 {
	background: url("../img/kursy_zbiorcze/imgCourse_1.jpg") no-repeat;
}

.lp-courseImg8 {
	background: url("../img/kursy_zbiorcze/imgCourse_2.jpg") no-repeat;
}

.lp-courseSteps {
	display: block;
	width: 100%;
	list-style-type: none;
	margin: 0;
	padding: 0;
}

.lp-courseSteps li {
	position: relative;
	padding-top: 100px;
	padding-bottom: 100px;
	padding-left: 42%;
	box-sizing: border-box;
}

.lp-courseSteps li h3,
.lp-courseSteps li p {
	width: 100%;
}

.lp-courseSteps li h3 {
	margin-bottom: 10px;
}

.lp-courseSteps li p {
	margin-bottom: 20px;
}

.lp-courseSteps li:nth-child(1):after {
	position: absolute;
	top: 319px;
	left: 195px;
	z-index: 10;
	content: " ";
	display: block;
	width: 5px;
	height: 101px;
	background-position: -794px 0;
}

.lp-courseSteps li:nth-child(2):after {
	position: absolute;
	top: 329px;
	left: 195px;
	z-index: 10;
	content: " ";
	display: block;
	width: 5px;
	height: 101px;
	background-position: -794px 0;
}

.lp-courseSteps li:nth-child(1) {
	background: url("../img/kursy_zbiorcze/courseStepsImg_1.jpg") no-repeat;
	background-position: -75px -25px;
}

.lp-courseSteps li:nth-child(2) {
	background: url("../img/kursy_zbiorcze/courseStepsImg_2.jpg") no-repeat;
	background-position: -75px -25px;
}

.lp-courseSteps li:nth-child(3) {
	background: url("../img/kursy_zbiorcze/courseStepsImg_3.jpg") no-repeat;
	background-position: -75px -10px;
}

.lp-courseWork {
	position: relative;
	width: 100%;
	padding: 47px;
	margin-bottom: 50px;
	border: 3px solid #e9e9e9;
	box-sizing: border-box;
}

.lp-courseWork h3,
.lp-courseWork p {
	width: 50%;
	position: relative;
	z-index: 10;
}

.lp-courseWork h3 {
	margin-bottom: 10px;
	font-size: 18px;
	font-weight: 600;
}

.lp-courseWork h3 strong {
	color: #266ca0;
	font-weight: 600;
}

.lp-courseWork:after {
	position: absolute;
	right: 0;
	bottom: 0;
	content: " ";
	display: block;
	width: 775px;
	height: 250px;
	background: url("../img/kursy_zbiorcze/courseFotel.png") no-repeat;
}

.lp-courseNumbers {
	width: 100%;
	height: 600px;
	margin: 50px 0;
	background: #b3d4e0;
	text-align: center;
	background: url("../img/kursy_zbiorcze/courseNumbers.jpg") bottom center no-repeat;
}

.lp-courseNumbers h2 {
	font-size: 80px;
	line-height: 1em;
	color: #a1c4d0;
	font-weight: 300;
	letter-spacing: -2px;
	padding: 70px 0 0;
}

.lp-courseNumbers h3 {
	font-size: 28px;
	font-weight: 600;
	line-height: 1em;
	letter-spacing: -1px;
	color: #3b5b67;
	position: relative;
	z-index: 10;
	top: -10px;
}

.lp-courseNumbers ul {
	width: 100%;
	max-width: 940px;
	list-style-type: none;
	margin: 60px auto 0;
	padding: 0;
}

.lp-courseNumbers ul li {
	display: inline-block;
	width: 23%;
	margin: 0;
	padding: 0;
	font-size: 14px;
	font-weight: 400;
	color: #607d87;
	text-transform: uppercase;
	letter-spacing: -0.5px;
}

.lp-courseNumbers ul li strong {
	font-weight: 600;
	font-size: 40px;
	line-height: 1em;
	color: #181818;
	display: block;
	letter-spacing: -1px;
}

.lp-courseGallery {
	position: relative;
	z-index: 10;
	width: 100%;
	box-sizing: border-box;
	padding: 31px;
	box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, 0.2);
	background: url("../img/kursy_zbiorcze/courseGallery.jpg") right bottom no-repeat;
}

.lp-courseGallery-left {
	width: 315px;
	float: left;
}

.lp-courseGallery-right {
	width: calc(100% - 400px);
	float: right;
	box-sizing: border-box;
	padding-right: 30px;
}

.lp-courseGallery-right h3 {
	font-size: 18px;
	font-weight: 600;
	margin-bottom: 15px;
}

.lp-courseGallery-right p {
	margin-bottom: 25px;
}

.lp-courseMap {
	display: block;
	width: 100%;
	background: url("../img/kursy_zbiorcze/courseMap.jpg") top -50px center no-repeat;
}

.lp-courseMap-left {
	float: left;
	width: 50%;
	padding: 40px;
	box-sizing: border-box;
}

.lp-courseMap-left ul {
	list-style: none;
	margin: 0;
	padding: 0;
}

.lp-courseMap-left ul li {
	margin: 0 0 6px 14px;
	position: relative;
}

.lp-courseMap-left ul li:after {
	position: absolute;
	top: 8px;
	left: -14px;
	content: " ";
	display: block;
	width: 6px;
	height: 10px;
	background-position: 0px -200px;
}

.lp-courseMap-right {
	float: right;
	width: 50%;
	padding: 40px;
	box-sizing: border-box;
}

.lp-courseMap h3 {
	font-size: 19px;
	font-weight: 600;
	letter-spacing: -0.4px;
	color: #000;
	line-height: 21px;
	margin-bottom: 15px;
}

.lp-courseMap p {
	margin-bottom: 20px;
}

.od-juniora {
	position: relative;
	display: block;
	width: 100%;
	padding: 45px 30px;
	margin-top: 50px;
	background: #e0e4ef;
	box-sizing: border-box;
}

.od-juniora:after {
	content: " ";
	width: 100%;
	height: 344px;
	position: absolute;
	bottom: 0;
	left: 0;
	background: url("../img/kursy_zbiorcze/courseMan.png") no-repeat;
}

.od-juniora h3 {
	font-size: 19px;
	font-weight: 600;
	letter-spacing: -0.4px;
	color: #000;
	line-height: 21px;
	padding-left: 50%;
	margin-bottom: 15px;
	position: relative;
	z-index: 10;
}

.od-juniora p {
	color: #000;
	margin-bottom: 0 !important;
	padding-left: 50%;
	position: relative;
	z-index: 10;
}

.lp-courseAll-Hardware {
	width: 100%;
	padding: 40px;
	display: block;
	background: url("../img/kursy_zbiorcze/courseSprzet.jpg") center no-repeat;
	box-sizing: border-box;
}

.lp-courseAll-Hardware h3 {
	font-size: 19px;
	font-weight: 600;
	letter-spacing: -0.4px;
	color: #fff;
	line-height: 21px;
	padding-left: 55%;
}

.lp-courseAll-Hardware p {
	color: #fff;
	margin-bottom: 0 !important;
	padding-left: 55%;
}

/* referencje twarze */
.lp-courseAll-ref {
	width: 100%;
	display: block;
}

.lp-courseAll-ref .lp-recoManRight.lp-recoImgA {
	position: absolute;
	top: 50%;
	right: 0;
	margin-top: -73px;
	width: 150px;
	height: 146px;
	display: block;
	text-indent: 200px;
	overflow: hidden;
	background: url(../img/kursy_zbiorcze/kursy_zbiorcze_sprite.png) 0 0 no-repeat;
}

.lp-courseAll-ref .lp-recoManRight.lp-recoImgB {
	position: absolute;
	top: 50%;
	right: 0;
	margin-top: -73px;
	width: 150px;
	height: 146px;
	display: block;
	text-indent: 200px;
	overflow: hidden;
	background: url(../img/kursy_zbiorcze/kursy_zbiorcze_sprite.png) -200px 0 no-repeat;
}

.lp-courseAll-ref .lp-recoManRight.lp-recoImgC {
	position: absolute;
	top: 50%;
	right: 0;
	margin-top: -73px;
	width: 150px;
	height: 146px;
	display: block;
	text-indent: 200px;
	overflow: hidden;
	background: url(../img/kursy_zbiorcze/kursy_zbiorcze_sprite.png) -400px 0 no-repeat;
}

.lp-courseAll-ref .lp-recoManRight.lp-recoImgD {
	position: absolute;
	top: 50%;
	right: 0;
	margin-top: -73px;
	width: 150px;
	height: 146px;
	display: block;
	text-indent: 200px;
	overflow: hidden;
	background: url(../img/kursy_zbiorcze/kursy_zbiorcze_sprite.png) -600px 0 no-repeat;
}

.lp-courseAll-ref .lp-recoMan {
	position: relative;
	display: block;
	width: 100%;
	margin: 40px 0;
}

.lp-courseAll-ref .lp-recoManLeft {
	position: relative;
	float: left;
	width: calc(100% - 180px);
	padding: 30px;
	box-sizing: border-box;
	border: 1px solid #e5e5e5;
	box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.1);
}

.lp-courseAll-ref .lp-recoManLeft h3 {
	margin: 0 0 20px;
	font-size: 14px;
	font-weight: 600;
}

.lp-courseAll-ref .lp-recoManLeft p {
	margin: 0;
	font-size: 14px;
	font-weight: 300;
	font-style: italic;
	color: #7d7d7d;
}

.lp-courseAll-ref .lp-recoManLeft p.lp-recoManPodpis {
	margin: 20px 0 0;
	font-weight: 600;
	font-size: 12px;
	font-weight: 600;
	color: #2e2e2b;
}

.lp-courseAll-ref .lp-recoTriangle {
	position: absolute;
	top: 50%;
	right: -13px;
	margin-top: -13px;
	display: block;
	width: 15px;
	height: 26px;
	background: url('../img/recoTriangle.png') no-repeat;
}

.lp-courseAll-ref .lp-recoManRight {
	display: block;
	float: right;
	width: 160px;
	height: 100%;
}

.lp-courseAll-ref .lp-recoManRight img {
	position: absolute;
	top: 50%;
	margin-top: -83px;
}

.lp-courseAll-ref .lp-recoManLeftImg .lp-recoManLeft {
	float: right;
}

.lp-courseAll-ref .lp-recoManLeftImg .lp-recoManRight {
	float: left;
}

.lp-courseAll-ref .lp-recoManLeftImg .lp-recoTriangle {
	left: -13px;
	transform: rotate(180deg);
}

.lp-courseAll-ref .lp-recoManLeftImg .lp-recoManRight.lp-recoImgB,
.lp-courseAll-ref .lp-recoManLeftImg .lp-recoManRight.lp-recoImgD {
	left: 0;
}

.lp-courseAllRef {
	width: 100%;
	display: block;
}

.lp-courseAllRef .lp-courseRef {
	width: 100%;
	clear: both;
	margin: 0 0 30px 0;
}

.lp-courseAllRef .lp-courseRef:last-child {
	margin-bottom: 100px;
}

.lp-courseAllRef .lp-courseRefLeft {
	display: block;
	width: 170px;
	height: 43px;
	float: left;
	height: 100%;
	position: relative;
}

.lp-courseAll-ref p {
	margin-bottom: 40px;
	padding-top: 10px;
}

.lp-courseAll-ref h2 {
	font-size: 28px;
	font-weight: 600;
	margin-bottom: 10px;
	color: #202020;
}

.lp-courseAllRef .lp-courseRefLeft.ref1 img {
	position: relative;
	top: 10px;
}

.lp-courseAllRef .lp-courseRefLeft.ref2 img {
	position: relative;
	top: 10px;
}

.lp-courseAllRef .lp-courseRefLeft.ref3 img {
	position: relative;
	top: 10px;
}

.lp-courseAllRef .lp-courseRefLeft.ref4 img {
	position: relative;
	top: 10px;
}

.lp-courseAllRef .lp-courseRefRight {
	width: calc(100% - 220px);
	float: right;
	padding: 0 0 0 20px;
	font-size: 14px;
	font-style: italic;
	background: url("../img/kursy_zbiorcze/referencjeIkona.jpg") 0px 0px no-repeat;
	box-sizing: border-box;
}

@media only screen and (max-width: 985px) {
	#site-content .lp-homePageContent {
		width: calc(100% - 60px);
		padding-left: 30px;
		padding-right: 30px;
	}

	.lp-courseSingle {
		width: calc(50% - 15px);
	}

	.lp-courseSingle:nth-child(2n) {
		margin-right: 0;
	}

	.lp-newTop.lp-allCourses {
		background: url("../img/kursy_zbiorcze/imgBg_zbiorcza_mobile.jpg") no-repeat #f0f0f0;
		background-position: right -400px bottom;
	}

	.lp-courseSteps li:nth-child(1),
	.lp-courseSteps li:nth-child(2),
	.lp-courseSteps li:nth-child(3) {
		background-position: -145px -25px;
	}

	.lp-courseSteps li:nth-child(2) {
		background-image: url("../img/kursy_zbiorcze/courseStepsImg_2.jpg");
	}

	.lp-courseSteps li {
		padding-left: 0;
	}

	.lp-courseSteps li h3,
	.lp-courseSteps li p {
		width: calc(100% - 300px);
		padding-left: 300px;
	}

	.lp-courseSteps li:nth-child(1):after,
	.lp-courseSteps li:nth-child(2):after {
		left: 125px;
	}

	.lp-courseImg1 {
		background: url("../img/kursy_zbiorcze/imgCourse_7_mobile.jpg") no-repeat;
	}

	.lp-courseImg2 {
		background: url("../img/kursy_zbiorcze/imgCourse_8_mobile.jpg") no-repeat;
	}

	.lp-courseImg3 {
		background: url("../img/kursy_zbiorcze/imgCourse_4_mobile.jpg") no-repeat;
	}

	.lp-courseImg4 {
		background: url("../img/kursy_zbiorcze/imgCourse_6_mobile.jpg") no-repeat;
	}

	.lp-courseImg5 {
		background: url("../img/kursy_zbiorcze/imgCourse_5_mobile.jpg") no-repeat;
	}

	.lp-courseImg6 {
		background: url("../img/kursy_zbiorcze/imgCourse_3_mobile.jpg") no-repeat;
	}

	.lp-courseImg7 {
		background: url("../img/kursy_zbiorcze/imgCourse_1_mobile.jpg") no-repeat;
	}

	.lp-courseImg8 {
		background: url("../img/kursy_zbiorcze/imgCourse_2_mobile.jpg") no-repeat;
	}

	.lp-courseSingleImg {
		background-position: center center;
	}

	.lp-courseWork:after {
		width: 100%;
		background-position: right bottom;
	}
}

@media only screen and (max-width: 865px) {

	.lp-courseSteps li:nth-child(1):after,
	.lp-courseSteps li:nth-child(2):after {
		display: none;
	}

	.lp-courseGallery-left {
		width: 50%;
	}

	.lp-courseGallery-left img {
		width: 90%;
	}

	.lp-courseGallery-right {
		width: 50%;
	}

	.lp-courseMap {
		background-size: 120%;
	}

	.od-juniora:after {
		background-position: left -130px bottom;
	}

	.lp-courseNumbers ul li strong {
		font-size: 30px;
	}

	.lp-courseNumbers ul li {
		font-size: 12px;
	}

	.lp-courseGallery {
		z-index: 9;
	}

	.lp-courseNumbers h2 {
		font-size: 50px;
	}
}

@media only screen and (max-width: 530px) {
	.lp-newTop.lp-allCourses {
		background-position: right -640px bottom;
	}

	.lp-courseSingle {
		width: 100%;
	}

	.lp-courseSteps li h3,
	.lp-courseSteps li p {
		width: 100%;
		padding-left: 0;
	}

	.lp-courseSteps li {
		padding: 0;
	}

	.lp-homepageCoachIntro {
		margin-left: 0;
		margin-right: 0;
	}

	.lp-allCourses h1 {
		font-size: 23px;
		line-height: 1.3em;
		width: 90%;
	}

	.lp-allCourses h2 {
		font-size: 16px;
		line-height: 1.3em;
		width: 90%;
	}

	.lp-newTop.lp-allCourses {
		padding-left: 20px;
		width: 100%;
	}

	#site-content .lp-homePageContent {
		width: calc(100% - 40px);
		padding-left: 20px;
		padding-right: 20px;
	}

	.lp-courseSteps li:nth-child(1),
	.lp-courseSteps li:nth-child(2),
	.lp-courseSteps li:nth-child(3) {
		background-position: 50% -60px;
		background-size: 400px;
	}

	.lp-courseAll-Hardware h3 {
		padding-left: 0;
	}

	.lp-courseAll-ref p {
		padding-left: 0;
	}

	.od-juniora h3,
	.od-juniora p {
		padding-left: 0;
	}

	.od-juniora:after {
		display: none;
	}

	.lp-courseSingle p {
		min-height: auto;
	}

	.lp-courseMap-left,
	.lp-courseMap-right {
		float: none;
		width: 100%;
	}

	.lp-courseGallery-left,
	.lp-courseGallery-right {
		float: none;
		width: 100%;
	}

	.lp-courseGallery-right {
		padding-right: 0;
	}

	.lp-courseGallery-left img {
		width: 100%;
		margin-bottom: 20px;
	}

	.lp-courseWork {
		padding: 30px;
	}

	.lp-courseWork:after {
		display: none;
	}

	.lp-courseWork h3,
	.lp-courseWork p {
		width: 100%;
	}

	.lp-courseNumbers h2 {
		font-size: 33px;
	}

	.lp-courseNumbers h3 {
		font-size: 23px;
		width: 80%;
		margin-left: auto;
		margin-right: auto;
	}

	.lp-courseNumbers ul {
		margin-top: 30px;
	}

	.lp-courseNumbers ul li {
		width: 42%;
		margin-bottom: 50px;
	}

	.lp-courseSteps li:nth-child(1),
	.lp-courseSteps li:nth-child(2),
	.lp-courseSteps li:nth-child(3) {
		padding-top: 200px;
	}
}

/* END Podstrona zbiorcza kursów */

.lp_advert_banner_dlaczego_big {
	display: none;
}

.lp_advert_banner_dlaczego_small {
	border: none;
}

.lp_advert_banner_dlaczego_small img {
	display: block;
	width: 99%;
	margin: 0 auto;
}

@media only screen and (max-width: 1020px) {
	.lp_advert_banner_dlaczego_big {
		width: 95%;
		margin: 10px auto;
		display: block;
	}

	.lp_advert_banner_dlaczego_big img {
		width: 100%;
	}
}

.lp-platnoscRatalna {
	margin-bottom: 3px !important;
}

.lp-platnoscRatalna .fa {
	color: inherit;
	margin-right: 5px;
	width: 10px;
	font-weight: bold;
}

/* Spis szkolen */

.spis-szkolen>li>a {
	font-size: 20px;
	text-decoration: none;
}

.spis-szkolen>li>ul>li {
	margin: 0;
	padding: 0;
	display: block;
	float: left;
	line-height: 1.4em;
}

.spis-szkolen>li>ul>li a {
	font-size: 14px;
	text-decoration: none;
}

.spis-szkolen>li>ul>li>ul {
	margin-bottom: 40px;
	padding-right: 60px;
	box-sizing: border-box;
}

#tab-spis.tabcontent> :first-child {
	margin: 0;
	color: #ccc;
	text-transform: uppercase;
	font-size: 13px;
	font-weight: bold;
	letter-spacing: 30px;
}

.spis-szkolen>li~li {
	border: 0;
}

ul.kursy {
	margin: 0;
	padding: 0 0 20px 0;
}

ul.kursy:after {
	visibility: hidden;
	display: block;
	font-size: 0;
	content: " ";
	clear: both;
	height: 0;
}

/* End Spis szkolen */

div.search-block {
	padding: 10px;
	margin-bottom: 10px;
	margin-top: 15px;
	border: 1px solid #c0c0c0;
	display: block;
}

h3.search-title {
	margin: 0;
}

span.search-link {
	color: #008000;
}

p.search-lead {
	margin-top: 5px;
}

select#id_termin {
	font-size: 90%;
}

.tos.error span.text {
	position: relative;
}

.tos.error span.text:after {
	content: "Pole wymagane!";
	position: absolute;
	top: -32px;
	left: -16px;
	color: red;
	background-color: white;
	border-radius: 2px;
	z-index: 11;
	padding: 3px 5px;
	width: 80px;
	border: 2px solid red;
	box-shadow: 1px 1px 1px grey;
}

.g-recaptcha {
	margin-top: 8px;
	transform: scale(0.87);
	transform-origin: 0 0;
}

.tos {
	margin-top: 8px;
	text-align: left;
	font-size: 11px;
}

.tos input {
	width: auto;
	padding: 10px;
	margin: 0;
}

.tos.error span.text {
	color: #ffaaaa;
}

.lp-newsletterRow p {
	font-size: 11px;
	margin-left: 25px;
}

.red-text {
	color: red;
}

/*new menu styles*/
.flexnav .touch-button {
	width: 100%;
	position: absolute;
}

#site-header .top-nav .nav {
	position: static;
	margin-right: 0;
	float: right;
	margin-right: calc(50% - 470px);
}

#site-header .top-nav .nav a {
	font-size: 12px;
}

.site-map h3 {
	font-size: 0.97em;
}

.flexnav .touch-button .navicon {
	position: absolute;
	right: 10px;
}

.dla-konsultantow {
	border-left: 1px solid rgba(255, 255, 255, 0.28);
}

#bootcamps-mobile {
	display: flex;
	width: 100%;
	flex-wrap: wrap;
}

.showAll {
	background-color: white;
	color: #111111;
	padding: 2px 20px;
	width: 100%;
	cursor: pointer;
}

.showBasic,
.showAdvanced {
	width: 50%;
	padding: 2px;
	text-align: center;
	box-sizing: border-box;
	color: #aaaaaa;
	background-color: #eeeeee;
	border-bottom: 1px solid #dedede;
	cursor: pointer;
}

.activeTab {
	color: #636363;
	background-color: #dcdcdc;
	border-bottom: 1px solid #9b9b9b;
}

.flexnav ul [data-level="2"] {
	display: none;
}

.flexnav ul [data-level="2"],
.flexnav ul [data-level="1"] {
	position: relative;
}

.flexnav ul [data-level="2"] span,
.flexnav ul [data-level="1"] span,
.young-crs ul li span {
	float: right;
	padding: 6px;
	font-size: 12px;
	color: white;
	min-width: 72px;
	margin: 9px 0;
	line-height: 12px;
	text-align: center;
}

.badge-hit,
.badge-cplusjava {
	background-color: #bd10e0;
}

.badge-bestseller,
.badge-linux {
	background-color: #7ed321;
}

.badge-oracle {
	background-color: #ffb701;
}

.badge-wordpress {
	background-color: #24aceb;
}

@keyframes openClose {
	from {
		transform: rotate(0deg);
	}

	to {
		transform: rotate(180deg);
	}
}

.navicon {
	transform: rotate(0deg);
	transition: transform 0.3s;
}

.active .navicon {
	transform: rotate(180deg);
	transition: transform 0.3s;
}

@media only screen and (min-width: 840px) {
	.flexnav.opacity {
		display: none;
	}

	.nav {
		display: inline-block;
	}
}

@media only screen and (max-width: 839px) {
	.nav {
		position: static;
		float: right;
		margin-right: 0 !important;
		width: auto !important;
	}

	.flexnav.flexnav-show {
		max-height: 0 !important;
	}

	.flexnav li.lp-eng a {
		padding: 8px 20px 8px 65px;
	}

	.flexnav.flexnav-show {
		min-height: calc(100vh - 50px) !important;
		overflow-y: scroll;
		background-color: #9b9b9b;
	}

	.flexnav-show {
		background-color: white;
	}

	.flexnav .touch-button .navicon {
		opacity: 1;
		width: 15px;
		font-size: 0;
		background: url("/media/arrow-down.svg") no-repeat;
		background-position: center;
		top: 2px;
	}

	.bodySticked {
		overflow: hidden;
	}

	li.lp-flexnav-header {
		background: #efeeee;
	}
}

#site-content .black {
	color: black !important;
}

.lp-outlineBorder {
	padding: 13px 20px;
	border: 1px solid #cacaca;
	border-left: 0;
	border-top-right-radius: 3px;
	border-bottom-right-radius: 3px;
}

.lp-linkSection a:hover .lp-outlineBorder {
	border-color: #da3e3e;
}

.lp-courseProgramAnalisys a::before {
	content: " ";
	position: absolute;
	left: 8px;
	z-index: -1;
	background: url("/media/cogs.png") no-repeat;
	background-position: 0 !important;
	background-size: cover;
	width: 40px;
	height: 62px;
	top: calc(50% - 31px);
}

.lp-courseProgramAnalisys {
	margin: 30px 0;
	width: 100%;
	text-align: center;
}

.lp-courseProgramAnalisys a {
	position: relative;
	display: inline-block;
	font-weight: 600;
	color: #da3e3e;
	text-decoration: none;
	z-index: 2;
	padding-left: 45px;
}

#site-content .lp-outlineBorder::after {
	width: 40px;
	height: 2px;
	background-color: #e13e3e;
	display: block;
	content: "";
	margin-top: 10px;
}

#site-content .black {
	color: black !important;
}

#site-content .lp-courseProgramAnalisys {
	margin: 15px 0;
}

.lp-courseProgramAnalisys a {
	font-weight: 600;
	color: #da3e3e;
	font-size: 13px;
}

.info-text2 span {
	margin-left: 15px;
	display: block;
}

@media only screen and (max-width: 580px) {


	#site-content .lp-courseProgramAnalisys {
		margin: 15px 0;
	}

	#site-content .lp-courseProgramAnalisys a::before {
		background: url("/media/cogs.png") no-repeat;
		background-position: 0 !important;
		background-size: cover;
		width: 20px;
		height: 31px;
		top: calc(50% - 15px);
	}

	#site-content .lp-courseProgramAnalisys a {
		padding-left: 13px;
	}

	#site-content .attention .lp-courseProgramAnalisys a::before {
		background-position: 0 !important;
		width: 25px;
		height: 25px;
		background: url("/media/warning.png") no-repeat;
		background-position-x: 0%;
		background-position-y: 0%;
		background-size: auto auto;
		background-size: cover;
		left: 0px;
		top: calc(50% - 12.5px);
	}

	#site-content .attention .lp-courseProgramAnalisys a {
		padding-left: 10px;
	}
}

.attention .lp-courseProgramAnalisys a::before {
	background-position: 0 !important;
	width: 68px;
	height: 68px;
	background: url("/media/warning.png") no-repeat;
	background-position-x: 0%;
	background-position-y: 0%;
	background-size: auto auto;
	background-size: cover;
	left: 0px;
}

.attention .lp-courseProgramAnalisys a {
	padding-left: 55px;
}

.new-suggest-bar .lp-linkSection {
	text-align: left;
}

.new-suggest-bar .lp-courseProgramAnalisys a {
	padding-left: 38.5px;
}

#site-content .multi-suggests .suggest-separator {
	height: 1px;
	width: 100%;
	background: #f7f7f7;
	background: linear-gradient(to right,
			#f7f7f7 0%,
			#7f7e7e 50%,
			#f7f7f7 100%);
	content: "";
	display: block;
	opacity: 0.2;
}

#site-content .multi-suggests .lp-courseProgramAnalisys {
	margin: 0px 0 0 0;
}

@media screen and (max-width: 991px) and (min-width: 581px) {
	.lp-contactSingle {
		width: 31%;
	}

	.lp-homePageTop .lp-newTop-content {
		padding: 60px 20px 20px;
	}
}

.lp-homePageTop .lp-akredytacja {
	right: 0px;
}


.course_suggest {
	width: calc(100% - 40px);
	padding: 8px 0 0 0;
	margin: 5px 0px 25px 25px;
	box-sizing: border-box;
	font-size: 13px;
	color: #7d7d7d;
	padding-left: 0;
}

.course_suggest * {
	box-sizing: border-box;
}

.course_suggest p {
	margin: 1px 0;
	font-weight: 400;
	float: left;
}

.course_suggest p:first-of-type {
	display: block;
	width: 100%;
	float: none;
	font-weight: 900;
}

.course_suggest span {
	width: calc(100% - 110px);
	float: right;
	margin: 1px 0;
}

.course_suggest a {
	color: inherit;
	font-weight: 400;
	text-decoration: underline;
}

@media only screen and (max-width: 580px) {
	.course_suggest {
		width: 100%;
		margin: 0;
		padding: 20px 0px 10px 0px;
	}
}

@media only screen and (max-width: 320px) {
	.course_suggest p:nth-of-type(2) a {
		width: 100%;
		padding-left: 10px;
		display: block;
		margin: 10px 0;
	}

	.course_suggest span {
		width: 100%;
		float: none;
		margin: 5px 0;
		padding-left: 15px;
		display: block;
	}

	.course_suggest p {
		display: block;
		float: none;
		padding-left: 5px;
	}

	.course_suggest p:first-of-type {
		padding-left: 0;
	}
}

/* new */

.mw-content .lp-newTop-rating {
	float: none;
	margin: 8px 20px 0 0;
	display: inline-block;
}

.mw-content .lp-newTop-rating .rate {
	text-align: left;
}

.mw-new-price-tag {
	text-align: left;
}

.mw-new-price-tag strong {
	font-size: 25px;
}

.mw-content .lp-newTop-right {
	width: calc(100% - 740px);
}

.mw-content .lp-newTop-left {
	width: 718px;
}

.mw-short-description {
	padding-bottom: 15px;
}

.mw-right-panel p {
	color: black;
}

/* miasta */
.lokalizacje-szkolen {
	font-family: Open sans, Arial, sans-serif;
}

.lokalizacje-szkolen h3 {
	margin-top: 20px;
}

ul.miasta {
	list-style-type: none;
	display: flex;
	padding-left: 0;
	margin-left: 0;
	padding-bottom: 115px;
	margin-top: 10px;
}

ul.miasta li {
	width: 90px;
	border: 2px #6f97fa solid;
	text-align: center;
	font-size: 12px;
	color: #6f97fa;
	padding: 5px;
	margin: 0 5px;
	cursor: pointer;
	position: relative;
}

ul.miasta li.miasto-aktywne {
	color: black;
	border: 2px black solid;
}

ul.miasta li p {
	margin: 0;
}

.mw-terminy {
	list-style-type: none;
	padding-left: 0;
	margin-left: 0;
	position: absolute;
	top: 100%;
	width: 180px;
	color: black;
	text-align: left;
	margin-top: 15px;
	margin-bottom: 5px;
}

.mw-terminy p:nth-of-type(1) {
	text-transform: uppercase;
	font-weight: 700;
	font-size: 12px;
	margin: 2px 0 5px 0;
}

.mw-terminy p:nth-of-type(2) {
	color: #40b840;
	font-size: 15px;
	font-weight: 700;
	margin: 1px 0;
}

.mw-terminy p:nth-of-type(3) {
	font-size: 12px;
	margin: 5px 0 14px 0;
	font-weight: 600;
	line-height: 1.25;
}

.na-zamowienie {
	color: #b1b1b1;
	font-size: 12px;
}

.lp-courseDates-cointainer .lp-cityRow li {
	margin: 0 5px 2px 0;
	width: calc(33.3% - 5px);
}

.lp-courseDates-cointainer .lp-courseDates {
	width: 272px;
}

.lp-courseDates-cointainer .lp-courseTime {
	width: 30px;
}

.lp-courseDates-cointainer .lp-cityRow ul {
	width: 193px;
}

.lp-courseDates-cointainer [data-legend]:hover::before,
[data-legend]:active::before {
	display: block;
	white-space: pre-line;
	word-break: keep-all;
}

.grecaptcha-badge {
	visibility: hidden;
}

.raty-5-info {
	padding-left: 13px;
}

/*nowe style do tabelki z filrowaniem kursów*/

.courses-data,
.filtr_najblizsze.form {
	min-width: 100%;
	max-width: 100%;
}

@media only screen and (max-width: 800px) {
	.filtr_najblizsze.form {
		margin: 0;
	}
}

@media only screen and (max-width: 650px) {
	.filtr_najblizsze.form .courses-data {
		display: block;
	}

	.filtr_najblizsze.form .courses-data tbody {
		display: flex;
		flex-wrap: wrap;
		width: 100%;
	}

	.filtr_najblizsze.form table.courses-data tr {
		width: 50%;
		border-bottom: 2px white solid;
	}
}

@media only screen and (max-width: 450px) {
	.filtr_najblizsze.form table.courses-data tr {
		width: 100%;
		border-bottom: none;
	}

	.filtr_najblizsze.form .field select {
		width: calc(100% - 10px);
		min-width: 120px;
	}

	.filtr_najblizsze.form .field-option span.label {
		width: 100%;
	}

	.filtr_najblizsze.form .field-option {
		width: calc(50% - 4px);
		display: inline-block;
	}
}

/*=======*/
/* 2 warianty kursu */
@media only screen and (max-width: 540px) {
	.lp-newTop-rating {
		display: none;
	}

	.pakiet-2-opis {
		display: block;
	}

	.lp-topCoursePrice.warianty {
		margin-top: 0 !important;
		margin-left: 0 !important;
		margin-right: 0 !important;
		padding-top: 0 !important;
		width: 100% !important;
		max-width: 100%;
	}

	.trener-sm img {
		max-width: 100% !important;
		box-shadow: none;
	}

	.tenerzy-sm .trener-sm>* {
		vertical-align: middle !important;
	}

	.zalety-sm {
		padding-bottom: 25px !important;
		width: 100% !important;
		margin-left: 0 !important;
		margin-right: 0 !important;
		padding-left: calc(50% - 105px) !important;
		box-sizing: border-box !important;
	}

	.trener-sm .name {
		margin-bottom: 0;
		margin-top: 0.75em;
	}

	.trener-sm .coach-tech {
		margin-top: 0;
		margin-bottom: 0;
	}

	.tenerzy-sm .slick-next {
		right: -20px !important;
	}

	.additional-styles-special .lp-header-01,
	.additional-styles-special .lp-header-02 {
		font-size: 18px !important;
		color: #434343 !important;
	}

	.additional-styles-special .lp-header-02 {
		line-height: 1.3 !important;
	}

	.additional-styles-special .lp-header-01 {
		margin-top: 15px !important;
	}

	.pakiet-1>span,
	.pakiet-2>span {
		text-align: center;
		font-size: 13px;
		line-height: 1.3;
	}

	.wiele-rat-part {
		display: inline-block;
		width: 100%;
	}

	.wiele-rat-part a {
		color: #434343;
		text-decoration: none;
	}

	.pakiet-2 .dlugosc-pakietu,
	.pakiety-sm .dlugosc-pakietu {
		margin-top: 25px;
	}

	.pakiet-1>p:nth-child(5),
	.pakiet-2>p:nth-child(5) {
		margin-top: -10px;
	}

	.wiele-rat-part {
		margin-top: -4px;
	}

	.trener-sm img {
		margin-bottom: 0 !important;
	}
}

ul.tryby li {
	list-style-type: none;
	margin: 0 0 0 0;
	padding: 0 0 0 0;
}

.tryby-lista {
	background: url("/media/trybyIcon_1x.png") no-repeat;
	display: inline-block;
	width: 20px;
	height: 16px;
}

.tryby-lista.weekendowy {
	background-position: -80px 4px;
}

.tryby-lista.dzienny {
	background-position: -40px 1px;
}

.tryby-lista.wieczorowy {
	background-position: 0 1px;
}

.lp-courseProgramAnalisys a::before {
	content: " ";
	position: absolute;
	top: calc(50% - 34px);
	left: 8px;
	z-index: -1;
	background-position: 0 -150px !important;
	width: 44px;
	height: 68px;
	background: url("/media/www-sprite.png") no-repeat;
}

.lp-courseProgramAnalisys {
	margin: 30px 0;
	width: 100%;
	text-align: center;
}

.lp-courseProgramAnalisys a {
	position: relative;
	display: inline-block;
	font-weight: 600;
	color: #da3e3e;
	text-decoration: none;
	z-index: 2;
	padding-left: 45px;
}

.additional-styles-special .metryczka-v2 {
	width: 90%;
	max-width: 550px;
	color: #434343;
}

.additional-styles-special .metryczka-v2 li {
	margin-bottom: 20px;
}

.course-length,
.course-prices {
	width: calc(100% - 100px);
	display: flex;
	justify-content: space-between;
}

.course-length fieldset {
	border: 1px #d4d4d4 solid;
	padding: 10px 15px 20px 15px;
	width: calc(50% - 23px);
	max-width: 150px;
}

.course-length fieldset legend {
	margin-right: 3%;
	margin-left: auto;
	font-weight: 600;
	padding: 0 10px;
}

.course-length fieldset div {
	font-weight: 600;
	font-size: 16px;
	margin: 0px 0 10px 0;
}

.course-length>p.divide-lenghts {
	border: 1px #d4d4d4 solid;
	padding: 10px 11px;
	border-radius: 100%;
	display: inline-block;
	font-style: italic;
	font-size: 12px;
	height: 20px;
	width: 18px;
	margin: auto;
}

.course-price-2 {
	position: relative;
	margin: auto;
	padding: 0 2px;
}

.course-price-2 strong {
	font-size: 16px;
}

.course-price-2 p {
	font-size: 12px;
	margin: 0;
}

.lp-phptop-content.course-prices .lp-price-info {
	top: -2px;
	right: -20px;
}

.mode-title {
	font-weight: 600;
	width: 100px;
	display: inline-block;
	vertical-align: top;
}

.metryczka-v2 ul.tryby li p {
	width: calc(100% - 130px);
	display: inline-block;
	vertical-align: top;
	font-size: 12px;
	padding: 0;
	margin: 0;
	line-height: 1.2;
}

.metryczka-v2 ul.tryby li {
	margin-bottom: 10px;
}

.metryczka-v2 .lp-platnoscRatalna {
	width: 215px;
	margin: auto;
}

.metryczka-v2 .course_suggest {
	margin: 0;
}

.pakiet-1,
.pakiet-2 {
	text-align: center;
	padding-top: 15px;
}

.pakiet-1 p:first-of-type,
.pakiet-2 p:first-of-type {
	font-size: 17px;
	font-weight: 600;
}

.article p.pakiet-2-opis {
	font-size: 12px;
	line-height: 1.3;
	text-align: left;
	width: 80%;
	height: 100px;
	margin: 0.75em 10%;
}

.pakiet-1>span:first-of-type,
.pakiet-2>span:first-of-type {
	font-size: 11px;
}

.lp-topCoursePrice.warianty {
	background-color: white;
	color: #2e2d2b;
	padding: 25px 25px 10px 25px;
	box-sizing: border-box;
	border-top-left-radius: 12px;
	border-top-right-radius: 12px;
}

.lp-topCoursePrice.warianty .lp-topCoursePriceStandard,
.lp-topCoursePrice.warianty .lp-topCoursePricePromotion {
	font-weight: 600;
	font-size: 18px;
}

.lp-topCoursePrice.warianty .lp-topCoursePriceStandard {
	color: #2e2d2b;
}

.zalety-sm {
	background-color: white;
	color: #2e2d2b;
	padding: 15px 25px 10px 25px;
	box-sizing: border-box;
	margin-bottom: -35px;
}

.zalety-sm .lp-platnoscRatalna {
	position: static;
	width: 100%;
	color: #2e2d2b;
	text-decoration: underline;
}

.zalety-sm .lp-platnoscRatalna svg {
	fill: #fcb00f;
	margin-right: 5px !important;
}

.dlugosc-pakietu {
	background-color: #fcb00f;
	padding: 2px;
	border-radius: 10px;
	width: 85px;
	box-sizing: border-box;
	margin: 5px auto;
	font-weight: 600;
	font-size: 12px;
	text-align: center;
	color: #52565b;
}

.trener-sm .name {
	font-weight: 600;
	font-size: 12.5px;
	line-height: 1.35;
}

.trener-sm .coach-tech {
	font-size: 10.5px;
	font-style: italic;
	line-height: 1.3;
}

.trener-sm>div p:not(.name):not(.coach-tech) {
	margin-bottom: 5px;
}

.trener-sm img {
	width: 100%;
	height: auto;
	border-radius: 100%;
}

.tenerzy-sm,
.tenerzy-sm .slick-list {
	width: 100%;
}

.tenerzy-sm .trener-sm>* {
	display: inline-block;
	vertical-align: top;
}

.trener-sm a {
	width: 30%;
	margin: 5px 8px 0 12px;
}

.trener-sm>div {
	width: calc(70% - 34px);
	margin: 0 8px 0 2px;
}

.tenerzy-sm .slick-dots {
	margin: 5px auto;
	text-align: center;
}

.tenerzy-sm .slick-dots li {
	display: inline-block;
}

.tenerzy-sm .slick-dots li {
	position: relative;
	display: inline-block;
	width: 15px;
	height: 15px;
	margin: 0 5px;
	padding: 0;
	cursor: pointer;
}

.tenerzy-sm .slick-dots li button {
	font-size: 0;
	line-height: 0;
	display: block;
	width: 20px;
	height: 20px;
	padding: 5px;
	cursor: pointer;
	color: transparent;
	border: 0;
	outline: none;
	background: transparent;
}

.tenerzy-sm .slick-dots li button:before {
	content: "•";
	font-size: 22px;
	line-height: 20px;
	position: absolute;
	top: 0;
	left: 0;
	width: 15px;
	height: 15px;
	text-align: center;
	color: #dad9d9;
}

.tenerzy-sm .slick-dots li.slick-active button:before {
	color: #858484;
}

.tenerzy-sm button {
	font-size: 0;
}

.tenerzy-sm .slick-prev {
	background-position: 0;
	left: -8px;
	top: 45px;
	background: url("/media/gr-arrow-left.png") no-repeat;
}

.tenerzy-sm .slick-next {
	background-position: 0;
	right: -8px;
	top: 45px;
	background: url("/media/gr-arrow-right.png") no-repeat;
}

.tenerzy-sm .slick-prev:hover {
	background-position: auto;
	opacity: 0.75;
}

.tenerzy-sm .slick-next:hover {
	background-position: 100%;
	opacity: 0.75;
}

.warianty,
.zalety-sm {
	margin-left: 30px;
	width: 100%;
}

.warianty .lp-info-icon {
	display: none;
}

.send-entire-department {
	position: relative;

	@media only screen and (max-width: 700px) {
		background: #454347;
	}

	.box-content {
		max-width: 430px;
		color: map-get($color-stack, 'white');
		padding: 5px 20px;

		h2 {
			color: map-get($color-stack, 'white');
			text-transform: uppercase;
			margin-bottom: 40px;
		}
	}

	img {
		position: absolute;
		top: 0;
		left: 0;
		z-index: -1;

		@media only screen and (max-width: 700px) {
			display: none;
		}
	}
}

.loader {
	border: 4px solid #f3f3f3;
	border-radius: 50%;
	border-top: 4px solid #fcaf12;
	box-sizing: border-box;
	width: 40px;
	height: 40px;
	margin: 20px auto;
	-webkit-animation: spin 2s linear infinite;
	/* Safari */
	animation: spin 2s linear infinite;
}

/* Safari */
@-webkit-keyframes spin {
	0% {
		-webkit-transform: rotate(0deg);
	}

	100% {
		-webkit-transform: rotate(360deg);
	}
}

@keyframes spin {
	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(360deg);
	}
}

@media only screen and (min-width: 1021px) {
	.lp-trainingCalendar .lp-trainingDaily {
		width: 45%;
	}

	.lp-trainingCalendar .lp-trainingWeekly,
	.lp-trainingCalendar th:nth-of-type(3) {
		padding-left: 25px;
		width: auto;
	}

	.lp-trainingCalendar tr,
	.lp-trainingCalendar tbody,
	.lp-trainingCalendar,
	.lp-trainingCalendar thead {
		max-width: 570px;
	}

	.lp-trainingCalendar th:nth-of-type(3) {
		width: 45%;
		padding-right: 5px;
	}

	.lp-trainingCalendar .lp-trainingCity {
		width: 16%;
	}

	.lp-trainingCalendar {
		/* display: block; */
		max-width: 570px;
		margin-top: 40px;
	}

	.lp-newTop-header .small-tags {
		margin-top: 10px;
	}

	.lp-topCoursePrice.warianty {
		margin-top: 77px;
	}
}

/* rwd */

@media only screen and (max-width: 1024px) {
	#site-header {
		z-index: 999;
		position: fixed;
		width: 100%;
	}

	.lp-topCoursePrice.warianty {
		margin: 25px 0 0 0;
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.tenerzy-sm {
		max-width: 50%;
		display: inline-block;
		vertical-align: middle;
	}

	.trener-sm .name {
		font-size: 16px;
	}

	.trener-sm .coach-tech {
		font-size: 13px;
	}

	.trener-sm>div p:not(.name):not(.coach-tech) {
		font-size: 16px;
	}

	.pakiety-sm {
		width: 20%;
		display: inline-block;
		vertical-align: middle;
		padding: 0 15px;
	}

	.pakiet-1 {
		padding-top: 0;
	}

	.pakiet-2 {
		width: 20%;
		display: inline-block;
		vertical-align: middle;
		border-left: 2px solid #adadad;
		padding: 0 15px;
	}

	.lp-topCoursePrice.warianty .lp-topCoursePriceStandard {
		display: inline-block;
		width: 100%;
	}

	.warianty,
	.zalety-sm {
		margin-left: 0;
	}

	.zalety-sm .lp-platnoscRatalna {
		position: static;
		width: 210px;
		color: #2e2d2b;
		text-decoration: underline;
		margin-left: calc(50% - 105px);
	}
}

@media only screen and (max-width: 920px) {
	.pakiety-sm {
		width: 30%;
	}

	.pakiet-2 {
		width: 30%;
	}

	.tenerzy-sm {
		width: 35%;
		margin-left: 2.5%;
		margin-right: 2.5%;
	}

	.trener-sm .name {
		font-size: 16px;
	}

	.trener-sm .coach-tech {
		font-size: 12px;
	}
}

@media only screen and (max-width: 768px) {
	.pakiety-sm {
		width: calc(50% - 4px);
		box-sizing: border-box;
	}

	.pakiet-2 {
		width: calc(50% - 4px);
		box-sizing: border-box;
		border-left: 1px solid #adadad;
	}

	.tenerzy-sm {
		width: 80%;
		max-width: 80%;
		margin-left: 10%;
		margin-right: 10%;
		margin-top: 5vh;
		margin-bottom: 5vh;
	}

	.lp-topCoursePrice.warianty {
		flex-wrap: wrap;
	}

	.trener-sm .coach-tech {
		font-size: 14px;
	}

	.tenerzy-sm .slick-prev,
	.tenerzy-sm .slick-next {
		top: calc(50% - 12.5px);
	}

	.zalety-sm {
		padding: 5vh 2vw;
	}

	.metryczka-v2 .not-on-mobilex {
		display: none !important;
	}
}

@media only screen and (max-width: 520px) {

	.lp-topCoursePrice.warianty,
	.zalety-sm {
		margin-left: -10px;
		margin-right: -10px;
		border-top-left-radius: 0px;
		border-top-right-radius: 0px;
		width: calc(100% + 20px);
		padding: 25px 0 0 0;
	}

	.tenerzy-sm {
		margin-top: 2vh;
		margin-bottom: 3vh;
	}

	.trener-sm .coach-tech {
		font-size: 11px;
	}

	.pakiety-sm,
	.pakiet-2 {
		display: block;
		width: 50%;
		padding: 0 5px;
	}

	.lp-topCoursePrice.warianty,
	.zalety-sm {
		margin-bottom: -30px;
		margin-top: 30px;
	}

	.zalety-sm {
		padding: 30px 0 15px 0;
	}

	.metryczka-v2 ul.tryby li p {
		width: 100%;
	}

	.tenerzy-sm .slick-prev {
		left: -18px;
	}

	.tenerzy-sm .slick-nex {
		right: -18px;
	}
}