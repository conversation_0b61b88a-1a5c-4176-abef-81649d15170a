#highlights {
	text-shadow: 0 2px 2px rgba(0, 0, 0, 0.15);
	font-size: 0;
	word-spacing: -0.25em;
	white-space: nowrap;
	color: #27261f;
}

#highlights:before {
	content: "";
	position: absolute;
	top: 0;
	bottom: 0;
	left: -180px;
	width: 3px;
	background: #a5ccd7;
	background: rgba(1, 120, 159, 0.3);
}

#highlights .nav {
	display: inline-block;
	width: 280px;
	margin-left: -490px;
	padding: 0 20px;
	font-size: 16px;
	line-height: 1.5em;
	vertical-align: middle;
	white-space: normal;
	word-spacing: 0;
}

#highlights .nav a {
	display: block;
	padding: 4px 0;
	color: #27261f;
}

#highlights .nav .current a {
	padding: 3px 10px 3px 19px;
	margin: 0 -10px 0 -20px;
	text-shadow: 0 0 5px #fff;
	background: url("../img/bg-9.png") 0 0 repeat-y;
	background: linear-gradient(0deg, rgba(1, 120, 159, 0.15) 25%, rgba(1, 120, 159, 0.025));
	border: #a5ccd7 solid;
	border-width: 1px 0 1px 1px;
	border-color: rgba(1, 120, 159, 0.3);
}

#highlights .nav .current a:focus {
	outline: none;
}

#highlights .nav .current:after,
#highlights .nav .current:before {
	content: none;
}

.highlight {
	display: inline-block;
	visibility: hidden;
	position: static;
	width: 620px;
	padding-left: 10px;
	margin: 0 -630px 0 0;
	font-size: 14px;
	line-height: 1.625em;
	vertical-align: middle;
	white-space: normal;
	word-spacing: 0;
}

.highlight:after,
.highlight:before {
	content: "";
	position: absolute;
	top: 0;
	left: -177px;
	width: 317px;
	height: 1px;
	background: url("../img/bg-10.png") 0 0 repeat-y;
	background: linear-gradient(0deg, rgba(1, 120, 159, 0.3) 90px, rgba(1, 120, 159, 0));
}

.highlight:after {
	top: auto;
	bottom: 0;
}

.highlight.active {
	visibility: visible;
}

.highlight .title {
	max-width: 80%;
	margin: 0.25em 0;
	font-size: 18px;
	font-weight: 700;
	line-height: 1.25em;
}

.highlight p,
.highlight ul {
	margin-top: 10px;
	margin-bottom: 10px;
}

.call-to-action {
	font-weight: 700;
	text-decoration: none;
	color: #f77f00;
}

#highlights .nav a:after,
.call-to-action:after {
	content: "\2023";
	display: inline-block;
	margin-left: 0.25em;
	height: 100%;
	font-size: 2ex;
	line-height: 100%;
	vertical-align: baseline;
}

#highlights .nav a:after {
	color: #c0c0c0;
	color: rgba(0, 0, 0, 0.2);
}
