const { watch, series, src, dest, parallel } = require('gulp');
const sass = require('gulp-dart-sass');
const postcss = require('gulp-postcss');
const autoprefixer = require('autoprefixer');
const cssnano = require('cssnano');
const imagemin = require("gulp-imagemin");
const uglify = require('gulp-uglify');
const rename = require('gulp-rename');
const plumber = require("gulp-plumber");
const babelify = require('babelify');
const browserify = require('browserify');
const source = require('vinyl-source-stream');
const buffer = require('vinyl-buffer');
const minimist = require('minimist');

const options = minimist(process.argv.slice(2), {
    boolean: 'js',
    default: { js: false }
});

function images() {
  return src('./src/img/**/*')
    .pipe(
      imagemin([
        imagemin.gifsicle({ interlaced: true }),
        imagemin.mozjpeg({ progressive: true }),
        imagemin.optipng({ optimizationLevel: 5 }),
        imagemin.svgo({
          plugins: [
            {
              removeViewBox: false,
              collapseGroups: true
            }
          ]
        })
      ])
    )
    .pipe(dest("./public/img"));
}

function javascript() {
  var result = browserify({
      entries    : './src/js/main.js',
      debug      : true
  });
  return result
    .transform(babelify, { presets: ["@babel/preset-env"] })
    .bundle()
    .pipe(source('main.js'))
    .pipe(buffer())
    .pipe(uglify())
    .pipe(dest('./public/js'))
    .pipe(uglify())
    .pipe(rename({ extname: '.min.js' }))
    .pipe(dest('./public/js'));
}

function css() {
  return src('./src/styles/main.scss')
    .pipe(plumber())
    .pipe(sass())
    .pipe(postcss([autoprefixer(), cssnano()]))
    .pipe(dest('./public/css'));
}

function watchFilesTask() {
  watch('./src/styles/**/*.scss', css);
  watch('./src/js/**/*.js', series(javascript));
}

const build = options.js
  ? series(javascript) // Build only JavaScript if the --js flag is provided
  : series(parallel(css, images, javascript));

const watchFiles = parallel(watchFilesTask);

exports.build = build;
exports.watchFiles = watchFiles;
