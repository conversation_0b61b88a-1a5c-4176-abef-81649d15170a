/**
 * CSS Guide for Mambo 4.5 templates 
 * <AUTHOR>
 * @url
 * @email 
 */

body,table {
	font-family: verdana, sans-serif;
	font-size: 12px;
	text-align: left;
}

div { text-align: left; }
div.linuxplus {text-align: center; margin: 2px; }

h2 { font-size: 18px; } 

body {
	margin-left: auto;
	margin-right: auto;
	margin-top: 0px;
	background-color: #f5f5dc;
	font-family: verdana, sans-serif;
	/* text-align: justify;  */
	font-size: 12px;
/*	background-image: url('/test/templates/akademia_jeden/images/akademiahead.jpg');
	background-repeat: no-repeat;
	background-position: top center;
	padding-top: 160px;*/
	width: 798px;
}
/* p, li {
	text-align: justify;
} */

div.footer {
	font-size: x-small;
	color: #909068;
	text-align: right;
	margin-bottom: 20px;
}

span.footer_copyright {
	float: left;
}

span.footer_copyright a {
	color: #909068;
}

div.facebook_share {
	margin-top: 20px;
	margin-bottom: 20px;
	text-align: right;
}

div.facebook_find_us {
	margin-top: 12px;
	margin-bottom: 8px;
	text-align: center;
}

div.facebook_find_us img {
	margin: 8px;
}

a:link, a:visited, a:active {
	text-decoration:none;
	color: #903030;
}

p a:link, p a:visited, p a:active {
	text-decoration: underline;
}

a:hover {
/*	text-decoration: underline;*/
}

td.tytul {
	text-align: center;
}

td.ramka {
/*	border: solid 1px #d0d0d0;
	background-color: #f8f8f8;*/
}

td.tableborderforum
{
	border-bottom: solid 1px #dcdca0;
	border-top: solid 1px #dcdca0;
}

*:first-child+html td.ramka {
	padding-right: 6px;
}

td {
	padding: 0px;
}

span.tytul {
	font-size: xx-large;
	font-variant: small-caps;
}

span.cena_red {
	color: #ff0000;
}

span.czas {
	color: #800000;
}
span.nieaktualne {
	text-decoration: line-through;
}

div.naglowek_inf_prasowej {
	font-style: italic;
	text-align: right;
}



table.splasz {
	margin-top: 0px;
	margin-bottom: 0px;
	margin-left: 7px;
	margin-right: 0px;
	padding: 10px;
	/* width: 100%; */
	border-width: 1px;
	border-color: gray;
	border-style: solid;
	background-color: #FFDAB9;
}

div.splasz {
	margin-top: 0px;
	margin-bottom: 0px;
	margin-left: 7px;
	margin-right: 0px;
	padding: 10px;
	width: 100%;
	border-width: 1px;
	border-color: gray;
	border-style: solid;
	background-color: #FFDAB9;
}

div.formularz {
	margin: 0px;
	padding: 10px 10px 10px 50px;
	width: 100%;
	border-width: 1px;
	border-color: gray;
	border-style: solid;
	background-color: #CCCCCC;
}

div.form_intra_table {
        margin-top: 2px;
		margin-bottom: 2px;
		margin-left: 7px;
        padding: 8px;
        border-width: 1px;
        border-color: gray;
        border-style: solid;
        background-color: #DDD;
}

table.moduletable_menu {
	border-collapse: collapse;
	width: 100%;
	border: solid 1px #d0d0d0;
	background-color: #f0f0c1;
	font-size: 12px;
}
table.moduletable_menu td {
	padding-left: 0px;
	padding-top: 0px;
	padding-bottom: 0px;
	padding-right: 0px;
}

table.moduletable_menu .indentlevel0 {
	display: block;
	padding-left: 0.5em;
}

table.moduletable, table.moduletable_newsflash {
	border-collapse: collapse;
}

table.moduletable_newsflash td {
	font-size: 10px;
	padding-left: 0.3em;
	padding-right: 0.3em;
}
table.moduletable_newsflash table.moduletable_newsflash {
	border: none;
}
table.moduletable_newsflash table.moduletable_newsflash td {
	vertical-align: top;
}
table.moduletable_newsflash .contentheading {
	background: none;
}

table.moduletable_banner {
	padding: 0.5em;
	text-align: center;
	width: 100%;
}

td.lista_szkolen_td_x5, th.grey {
	background-color: #DDD;
}

textarea, input {
	border: solid 1px #d0d0d0;
}

li p {
	margin-top: 0px;
}

table.szkielet {
	border-spacing: 0px;
	vertical-align: middle;
}


table.terminy {
	border-spacing: 0px;
	border-collapse: collapse;
}
table.terminy td, table.terminy th {
	border: solid 1px #000;
	padding: 0.2em;
	padding-left:0.3em;
	padding-right:0.3em;
}
table.terminy td.cena {
	text-align: right;
}

table.terminy span.nazwa_kursu {
	font-size: 16px;
}

table.formularz_zgloszeniowy {
	border-spacing: 0px;
	border-collapse: collapse;
	background-color: #e0e0e0;
}

table.formularz_zgloszeniowy ul {
	list-style-type: none
}

table.formularz_zgloszeniowy ul.errorlist {
	color: red;
}

table.formularz_zgloszeniowy td {
	border: solid 1px black;
	padding: 0.3em;
}

table.formularz_zgloszeniowy td.label div.label {
	padding: 0.3em;
	font-weight: bold;
}

table.formularz_zgloszeniowy .input input[type=text], table.formularz_zgloszeniowy .input input[type=submit], table.formularz_zgloszeniowy .input select {
    width: 100%;
}

table.formularz_zgloszeniowy .input textarea {
    width: 99%;
}

td small{
    font-size: 12px;
}

table.terminy td small{
    font-size: 10px;
}

.input ul {
    padding-left: 0;
}

table.formularz_zgloszeniowy .input_text textarea, .input textarea {
	height: 60px;
}

tr.required td:first-child .label label:before {
    content: "* ";
    color: #900;
}

/**
 * General Mambo Styles
 * --------------------
 * The following are styles that are often used
 * site wide by Mambo to provide better control
 * of content's appearences.
 */

hr {}
hr.separator {}
.pagenavbar {} /* not used in to page nav bar but used for nicknames in forum posts by Simpleboard */
.pagenav {} /* as the name implies, this is for formatting texts for those "<< Start < Previous 1 Next > End >>" links */
.small {}


/**
 * Mambo Form Styles
 * -----------------
 * Here are 2 styles that Mambo uses to let users control
 * how their forms and buttons may look.
 */
 
.button {}
.inputbox {}


/**
 * Mambo Tabbed Frontend Admin Interface
 * -------------------------------------
 * The CSS styles here defines how the frontend admin interface
 * will look like when editing and adding news through frontend.
 */
 
.ontab {}/* For styling of the "Tab" buttons when editing contents through the frontend as admin. 
		This .ontab is the styling for the tab when it is active or after its "clicked" */
.offtab {} /* Same as above, used for styling of the "Tab" buttons when editing contents through the frontend. 
		This the styling for the tab when it is NOT active or when it is NOT "clicked" */
.tabpadding {} /* this style is used set the size of the tab in the above */ 
.tabheading {} /* Not too sure what this is used for. Couldn't find anything related to it yet at the moment */
.pagetext {} /* this style is used to style the content of the editing form contents (where HTMLArea sits and all its forms + contents) in
		the frontend Administration interface */




/**
 * Mambo Menu Styling
 * ------------
 * You can control the way menu behave and look
 * by using the CSS settings below
 */

a.indentlevel0, span.indentlevel0 {
	padding-top: 3px;
	padding-bottom: 0px;
	padding-right: 4px;
} /* this styling is for the MAIN items in the menu */

a.indentlevel1, span.indentlevel1 {
	display: block;
	padding-left: 16px; /* 1em; */
	padding-top: 1px;
	padding-bottom: 0px;
	padding-right: 4px;
} /* this styling is for menu items that HAS A PARENT */

a.indentlevel2, span.indentlevel2 {
	display: block;
	padding-left: 32px; /* 1em; */
	padding-top: 1px;
	padding-bottom: 0px;
	padding-right: 4px;
}

a#active_menu {
	color: #80805d;
}

/**
 * Mambo General styling for Sections/Categories/Contents
 * ------------------------------------------------------
 * These stylings are either sometimes or often used across
 * Sections, categories and contents. They, therefore, deserves
 * to be mentioned and separated from others.
 */
 
.createdate {} /* For styling the date the content/articles are created under contents title */
.modifydate {} /* need me to say? :) Well it styles the "Last updated on" text at the end of articles/contents */
.readon, .readon_front{
	text-align: right;
	float: right;
	margin-top: 0; /*-1em;*/
	margin-bottom: 1em;
} /* For formatting the "Read on..." link for blogs and on frontpage. */




/** 
 * Mambo Styling for Contents
 * --------------------------
 * All the styling for contents are listed
 * below.
 */
 
.contentpane {
} /* This is used mainly for the table that holds the SECTIONS such as News.
	   This is when you create a link to a Section and when clicked on it, it will display
	   "News" then some description (with image if you selected one) and then a list of 
	   Category of News. Yes! That entire thing is contained in a table with this style */
.contentpaneopen, .contentpaneopen_front {
	width: 100%;
} /* Found this being used by Phil in his Shambo2. Don't know where else its used
		in Mambo */
.contentheading, .contentheading_front {
	font-size: 20px;
	font-variant: small-caps;
	font-weight: bold;
	display:block;
	border-bottom: solid 1px #000;
	background: url("/static/images/bullet.gif") right center no-repeat;
}

.blog .contentheading {
	padding-top: 0.4em;

}

.blog p {
}

.contentpagetitle {} /* Couldn't find where is this yet. Will update this part when I
		get to it. Sorry. If you know, please email me the infos */
.contentdescription {} /* When you create a link to a Section in the main menu and when clicked on it, it will display
	   "News" then some description (with image if you selected one) and then a list of 
	   Category of News. This style is used for formating the "DESCRIPTION" part of that page */
table.contenttoc {} /* This is used to format the table of the Tables of Contents or "Jump to" 
	   box when it is enabled in a multiple paged content or article */
table.contenttoc td {} /* the same as above, but this is used to format the td or
	   table cells */
.content_rating {} /* used for styling the texts used for displaying 
	   those "stars" for rating in an article */
.content_vote {} /* used for those voting texts (the one with voting RADIO buttons) */



/**
 * Mambo Sections Styles
 * ---------------------
 * Styling of sections tables 
 */

.sectiontableheader {
	padding: 0.1em;
	background-color: #dcdca0;
} /* This is for styling the section table headers on a SECTION's page.
	   An example would be those articles lists when you click on "News" or something?
	   With a table header of "Date", "Item Title", "Author" and "Hits" ? That's the
	   header that you will be controlling through this style */
.sectiontableentry1 td {
	padding: 0.1em;
	background-color: #f0f0c1;
} /* this is used when there's a whole list of data to provide and
	   you need to create alternate colors for each row of data. This is
	   the first color */	
.sectiontableentry2 td {
	padding: 0.1em;
} /* this is the second color for the row. So, the table generator,
	   will alternate its style through sectionableentry1 and sectiontableentry2
	   as it cycles through and outputs each row of data. Similar to forum's post
	   listings */


/**
 * Mambo Styles for Categories
 * ---------------------------
 * The following are the styling for "Category" items
 * generated from the Sections area
 */

.category {} /* this is to set the category Titles ( found on a SECTION's page
	   where the Category lists are) styles */
a.category:link, a.category:visited {} /* same as above, but to set the link format */
a.category:hover {} /* same as above, but for links with mouse pointer over it */



/**
 * Mambo Blog styling
 * ------------------
 * Not too sure where this is used yet. I tried the blog section
 * but it doesn't seem to use it. If you have info on this,
 * email me. I'll update this part.
 */
 
.blogsection{} /* to be updated */




/**
 * Mambo Components Styles
 * -----------------------
 * These stylings are to format the way components title
 * is displayed
 */


.componentheading, .componentheading_front {
	font-size: x-large;
	font-variant: small-caps;
	font-weight: bold;
} /* This is used for formatting the component's title
	   when it is displayed on its own page on the frontend */

.componentheading_front {
	padding-bottom: 0.5em;
}

/**
 * Mambo Modules formatting
 * ------------------------
 * These stylings are to format the way modules are
 * displayed. It mainly deals with the format of its
 * table. I think this doesn't need too much explaining
 */

table.moduletable, table.moduletable_newsflash, table.moduletable_menu {
	border: solid 1px #c0c0c0;
	width: 100%;
	margin-top: 0.5em;
} /* styling the module table */
table.moduletable th, table.moduletable_menu th, table.moduletable_newsflash th {
	text-align: center;
	font-variant: small-caps;
	font-size: 14px;
	border: solid 1px #c0c0c0;
	background-color: #dcdca0;
} /* styling the module header, and the module titles */
table.moduletable td, table.moduletable_newsflash {
	background-color: #f5f5dc;
	padding: 0.3em;
}	/* well.. for formatting the table cells of the module table */
	


/**
 * Mambo's Built-in Component's Syling
 * -----------------------------------
 * These stylings are used for Mambo's built-in components
 * such as newsfeeds, weblinks, contact, search and polls.
 * A commented subtitle will guide you which is for which
 */
 
 
 /*  Polls  */
 
.poll {} /* polls texts */
.pollstableborder {} /* set the border properties of the polls voting table */


/*  Weblinks */
.weblinks{} /* well.. to format the link's titles under the "Weblinks" 
	   section on the frontend */
a.weblinks:hover {} /* same as above, but for link with mouseover */


/*  Newsfeeds */
.newsfeedheading {} /* The newsfeed title. NOTE: This will not affect the newsfeed's news title! */
.newsfeeddate {} /* yeah.. the date on the newsfeed */
.fase4rdf {} /* this is the body text of the newsfeed */


/* Search page */
table.searchintro {} /* This is for formatting the box with "Search Keyword: test returned 4 matches" box
	   that appears after you have entered a search value. It appears on the mainbody
	   with the search results  */


/*  Contact's table settings  */
table.contact {} /* for formatting the entire "Contact" table which includes
	   the name, address icons and form. Note this table DOES NOT 
	   enclose the dropdown list for selecting the Department/person
	   to contact */
table.contact td.icons {} /* the name gave it away. This formats the <td> cells
	   where the little icons sits next to the details (those addresses,
	   phone number etc) */
table.contact td.details {} /* this is for formatting the <td> cells where all the details sits.
	   The details where addresses, phone number and additional info are */


/* fdreger */
p.zapiszsieteraz {padding: 15px; margin: 2% 20% 2% 20%; border-style: solid; text-align: center; font-size: 130%; border-width:1px}

table.kontakt_table_kontakt_form td { text-align: left }

table.pollstableborder { font-size: 11px; }

ul.referencje a { font-weight: bold; }

div.form_intra_table ul {
	padding: 0;
	margin: 0;
	list-style-type: none;
}

div.form_intra_table li { margin-left: 5px; }

div.form_intra_table li li { margin-left: 10px; }

h2 span.small, h3 span.small { font-size: 12px; font-weight: normal; }

div.seealso { 
	width: 220px;
	float: right; 
	margin-right: 0px;
	margin-left: 3px;
	margin-top: 5px;
	padding: 2px;
    border: 1px solid gray;
    background: #ddd; 
	font-size: 10px;
	clear: right;
}
div.seealso ul { margin-top: 5px; margin-left: 0px; padding-left: 0px; list-style-type: none; }
div.seealso ul#terminy { margin-top: 5px; margin-bottom: 0px; margin-left: 14px; list-style-type: none; }
div.seealso li { margin-bottom: 2px; }
div.seealso ul#terminy li { font-size: 12px; }

div.zapiszsie { text-align: right; padding-right: 65px; font-size: 12px; margin-bottom: 15px; margin-top: 0px; }

div#szczegoly { padding-bottom: 10px; }
div#szczegoly h3 { margin: 7px; font-size: 13px; }
div#szczegoly p { margin-left: 14px; font-size: 12px; }

ul.rozstrzelona li { margin-bottom: 5px; }

div.zgloszenie_online { font-weight: bold; font-size: 15px; }

a.with_separator_above, span.with_separator_above { display: block; border-top: 1px solid #C0C0C0; margin-top: 5px;  }

.hint { position: relative; overflow: hidden; display: inline-block;
	height: 1.25em; line-height: 1.25em; padding: 0 15px 0 0;
	text-decoration: none;
	background: url('/static/images/hint.png') no-repeat 100% 0.2em;
	vertical-align: middle; }

.hint span { display: block;
	white-space: normal; }

.hint > span { position: absolute; top: 0; right: 100%; overflow: hidden;
	width: 1px; height: 1px; margin: 0 10px 0 0; }
.hint:hover { overflow: visible; }
.hint:hover > span { width: 250px; height: auto;
	padding: 10px;
	background: #fff; border: #aaa solid 1px; 
	overflow: visible; }

.hint:link, .hint:visited { color: #000; }

table.filtr_najblizsze select { width: 100% }
table.filtr_najblizsze td { padding : 5px; text-align: right }

table.opcjonalnie {
	border-collapse: collapse;
	border-style: solid none none none;
	border-width: 1px;
	border-color: #c0c0c0;
	font-size: 10px;
	width: 100%;
	margin-top: 4px;
}

table.opcjonalnie td {
	padding: 1px;
	border-style: none;
}

p.sylwetka {
	font-size: 10px;
}

p.sylwetka img {
	padding: 0px 4px 4px 4px;
}

div.sylwetka_big {
	clear: both;
}

div.sylwetka_big img {
	padding: 0px 10px 20px 10px;
}

div.sylwetka_big p.imie_nazwisko {
	font-size: 20px;
	font-weight: bold;
}

div.sylwetka_big p.sylwetka {
	font-size: 12px;
}

li.zobacz_takze_hidden {
	display: none;
}

p.errors_in_form {
	color: #ff0000;
}

.sciezka_container {
  width: 602px;
  margin-top: 40px;
}

.sciezka_scrollpane {
  overflow: hidden;
}

.sciezka_innerscrollpane {
  transition: all 0.6s ease; transform-origin: 0px 0px;
  -webkit-transition: all 0.6s ease; -webkit-transform-origin: 0px 0px;
  -moz-transition: all 0.6s ease; -moz-transform-origin: 0px 0px;
  -o-transition: all 0.6s ease; -o-transform-origin: 0px 0px;
  -ms-transition: all 0.6s ease; -ms-transform-origin: 0px 0px;
}

div.translations {
  text-align: right;
  position: absolute;
  width: 798px;
}

div.translations span {
  background-color: #e0e0e0;
  padding: 4px;
}



