/**
 * Default theme
 *
 */
/* Slight reset to make the preview have ample padding. */
.cse .gsc-control-cse,
.gsc-control-cse {
  padding: 1em;
  width: auto;
}
.cse .gsc-control-wrapper-cse,
.gsc-control-wrapper-cse {
  width: 100%;
}
.cse .gsc-branding,
.gsc-branding {
  display: none;
}
.cse .gsc-control-cse div,
.gsc-control-cse div {
  position: normal;
}
/* Selector for entire element. */
.cse .gsc-control-cse,
.gsc-control-cse {
  background-color: #f5f5dc;
  border: 1px solid #f5f5dc;
}
.cse .gsc-control-cse:after,
.gsc-control-cse:after {
  content:".";
  display:block;
  height:0;
  clear:both;
  visibility:hidden;
}
.cse .gsc-resultsHeader,
.gsc-resultsHeader {
  border: block;
}
/* Search button */
.cse input.gsc-search-button,
input.gsc-search-button {
  font-family: inherit;
  color: #000000;
  text-shadow: 0 1px 2px #FFFFFF;
  background-color: #CECECE;
  border: 1px outset;
  border-color: #666666;
  border-radius: 2px;
  -moz-border-radius: 2px;
  -webkit-border-radius: 2px;
}
/* Inactive tab */
.cse .gsc-tabHeader.gsc-tabhInactive,
.gsc-tabHeader.gsc-tabhInactive {
  border-bottom: none;
  color: #666666;
  background-color: #e9e9e9;
  border: 1px solid;
  border-color: #e9e9e9;
  border-bottom: none;
}
/* Active tab */
.cse .gsc-tabHeader.gsc-tabhActive,
.gsc-tabHeader.gsc-tabhActive {
  background-color: #FFFFFF;
  border: 1px solid;
  border-top: 2px solid;
  border-color: #e9e9e9;
  border-top-color: #FF9900;
  border-bottom: none;
}
/* This is the tab bar bottom border. */
.cse .gsc-tabsArea,
.gsc-tabsArea {
  margin-top: 1em;
  border-bottom: 1px solid #e9e9e9;
}
/* Inner wrapper for a result */
.cse .gsc-webResult.gsc-result,
.gsc-webResult.gsc-result,
.gsc-imageResult-column,
.gsc-imageResult-classic {
  padding: .25em;
  border: 1px solid;
  border-color: #ffffff;
  margin-bottom: 1em;
}
/* Result hover event styling */
.cse .gsc-webResult.gsc-result:hover,
.gsc-webResult.gsc-result:hover,
.gsc-webResult.gsc-result.gsc-promotion:hover,
.gsc-results .gsc-imageResult-classic:hover,
.gsc-results .gsc-imageResult-column:hover {
  border: 1px solid;
  border-color: #FFFFFF;
}
/*Promotion Settings*/
/* The entire promo */
.gsc-webResult.gsc-result.gsc-promotion {
  background-color: #FFFFFF;
  border-color: #336699;
}
/* Promotion links */
.cse .gs-promotion a.gs-title:link,
.gs-promotion a.gs-title:link,
.cse .gs-promotion a.gs-title:link *,
.gs-promotion a.gs-title:link *,
.cse .gs-promotion .gs-snippet a:link,
.gs-promotion .gs-snippet a:link {
  color: #0000CC;
}
.cse .gs-promotion a.gs-title:visited,
.gs-promotion a.gs-title:visited,
.cse .gs-promotion a.gs-title:visited *,
.gs-promotion a.gs-title:visited *,
.cse .gs-promotion .gs-snippet a:visited,
.gs-promotion .gs-snippet a:visited {
  color: #0000CC;
}
.cse .gs-promotion a.gs-title:hover,
.gs-promotion a.gs-title:hover,
.cse .gs-promotion a.gs-title:hover *,
.gs-promotion a.gs-title:hover *,
.cse .gs-promotion .gs-snippet a:hover,
.gs-promotion .gs-snippet a:hover {
  color: #0000CC;
}
.cse .gs-promotion a.gs-title:active,
.gs-promotion a.gs-title:active,
.cse .gs-promotion a.gs-title:active *,
.gs-promotion a.gs-title:active *,
.cse .gs-promotion .gs-snippet a:active,
.gs-promotion .gs-snippet a:active {
  color: #0000CC;
}
/* Promotion snippet */
.cse .gs-promotion .gs-snippet,
.gs-promotion .gs-snippet,
.cse .gs-promotion .gs-title .gs-promotion-title-right,
.gs-promotion .gs-title .gs-promotion-title-right,
.cse .gs-promotion .gs-title .gs-promotion-title-right *,
.gs-promotion .gs-title .gs-promotion-title-right * {
  color: #000000;
}
/* Promotion url */
.cse .gs-promotion .gs-visibleUrl,
.gs-promotion .gs-visibleUrl {
  color: #008000;
}
/* Style for auto-completion table
 * .gsc-completion-selected : styling for a suggested query which the user has moused-over
 * .gsc-completion-container : styling for the table which contains the completions
 */
.gsc-completion-selected {
  background: #D5E2FF;
}
.gsc-completion-container {
  font-family: Arial, sans-serif;
  font-size: 13px;
  position: absolute;
  background: white;
  border: 1px solid #666666;
  margin-left: 0;
  margin-right: 0;
  /* The top, left, and width are set in JavaScript. */
}

/* Full URL */
.gs-webResult div.gs-visibleUrl-short,
.gs-promotion div.gs-visibleUrl-short {
  display: none;
}
.gs-webResult div.gs-visibleUrl-long,
.gs-promotion div.gs-visibleUrl-long {
  display: block;
}
