$(function () {

    // --------------------------------------------------
    // inserts button for dateReminders popup
    // --------------------------------------------------
    function dateReminders() {
        this.options = {
            container: '#meta-content .course-calendar td.zapisz-sie',
            buttonClass: 'basic-button button-big button-green popupMessage-opener',
            buttonText: 'Powiadomienie o terminach',
            popupHtmlRows: [
                '<form action="do-stuff.php" class="popup-form" method="post" validate enctype="multipart/form-data">',
                '<h2>Powiadomienie o terminach</h2>',
                '<p>Zaznacz interesujące Cię miasta. Jeżeli w którymś z nich zostanie zaplanowany nowy termin szkolenia, otr<PERSON><PERSON>z od nas wiadomość na podany adres email.</p>',
                '<div class="form-row form-row-half"><input id="form-city-0" type="checkbox" name="form-city" value="Warszawa"><label for="form-city-0">Warszawa</label></div>',
                '<div class="form-row form-row-half"><input id="form-city-1" type="checkbox" name="form-city" value="Kraków"><label for="form-city-1">Kraków</label></div>',
                '<div class="form-row form-row-half"><input id="form-city-2" type="checkbox" name="form-city" value="Poznań"><label for="form-city-2">Poznań</label></div>',
                '<div class="form-row form-row-half"><input id="form-city-3" type="checkbox" name="form-city" value="Katowice"><label for="form-city-3">Katowice</label></div>',
                '<div class="form-row form-row-half"><input id="form-city-4" type="checkbox" name="form-city" value="Wrocław"><label for="form-city-4">Wrocław</label></div>',
                '<div class="form-row form-row-half"><input id="form-city-5" type="checkbox" name="form-city" value="Łódź"><label for="form-city-5">Łódź</label></div>',
                '<div class="form-row form-row-half"><input id="form-city-6" type="checkbox" name="form-city" value="Londyn (UK)"><label for="form-city-6">Londyn (UK)</label></div>',
                '<div class="form-row"><input id="form-email" placeholder="Twój adres email" type="email" name="email" required><button type="submit" class="basic-button button-green">Wyślij</button></div>',
                '<p><small>Na podany adres wyślemy tylko i wyłacznie krótką informację o nowych terminach, nie wyślemy ofert oraz mail będzie zawierał link do wyłaczenia powiadomień.</small></p>',
                '</form>'
            ],
        };
        this.run = function () {
            // set this
            var that = this;
            // loop through html elements to merge all data
            var popupData = '';
            for (a = 0; a < that.options.popupHtmlRows.length; a++) {
                popupData += String(that.options.popupHtmlRows[a]);
            }
            // add html to the defined container
            $(that.options.container).append('<p><a class="' + that.options.buttonClass + '" data-popupContent=\'' + popupData + '\'>' + that.options.buttonText + '</a></p>');
            return true;
        };
    }
    var reminders = new dateReminders();
    reminders.run();

    // --------------------------------------------------
    // html-data-driven popup message system
    // --------------------------------------------------
    function popupSystem() {
        this.options = {
            container: '.popupMessage',
            htmlBegin: '<div class="popupMessage popupMessage-hidden"><div class="popupMessage-content">',
            htmlCloser: '<a class="popupMessage-closer basic-button">&times;</a>',
            htmlEnd: '</div></div>',
            itemDataAttribute: 'data-popupContent',
            hidingClass: 'popupMessage-hidden',
            opener: '.popupMessage-opener',
            closer: '.popupMessage-closer',
            time: 400,
        };
        this.popupMessage = {
            create: function (item, options) {
                // set this
                var popupMessage = this;
                // get data from item
                // and set the default value of existence timeout
                var itemData = item.target.getAttribute(options.itemDataAttribute);
                var existenceTimeout = 0;
                // check if container exists
                if($(options.container).length > 0) {
                    // enlarge timeout
                    existenceTimeout = options.time;
                }
                // postpone creation and everything by existenceTimeout
                setTimeout(function () {
                    // again check if container still exists
                    // to avoid creating duplicates
                    if($(options.container).length > 0) {
                        // enlarge timeout
                        console.warn('Trying to create a duplicate popup!');
                    } else {
                        // create html
                        $('body').append(options.htmlBegin + options.htmlCloser + itemData + options.htmlEnd);
                        // wait for 1ms for container html to show up
                        // and remove hiding class
                        setTimeout(function() {
                            $(options.container).removeClass(options.hidingClass);
                        }, 1);
                        // add closing event for closer
                        // that triggers popup message destroyer
                        $(options.closer).on('click', function () {
                            popupMessage.destroy(options);
                        });
                    }
                }, existenceTimeout);
                return true;
            },
            destroy: function (options) {
                // add hiding class to the container
                $(options.container).addClass(options.hidingClass);
                // wait for some time for css animation to end
                // and completely remove popup message html
                setTimeout(function() {
                    $(options.container).remove();
                }, options.time);
                return true;
            },
        };
        this.run = function () {
            // set this
            var that = this;
            // add click event for all openers
            // that triggers popup message creator
            $(that.options.opener).on('click', function (item) {
                that.popupMessage.create(item, that.options);
            });
            return true;
        };
    }
    var popups = new popupSystem();
    popups.run();

});