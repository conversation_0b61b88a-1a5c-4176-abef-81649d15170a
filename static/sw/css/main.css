/*/general*/
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Open Sans', sans-serif;
}

.bg-container {
    max-width: 1920px;
    margin: 0 auto;
    height: 100%;
}

.home-container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 56px;
}

.sticky-advertisement {
    padding: 8px 0;
    background-color: #72B2FF;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 16px;
    position: -webkit-sticky;
    /* Safari */
    position: sticky;
    top: 0;
    z-index: 50;
}

.sticky-advertisement > p {
    font-size: 16px;
    color: #121930;
    font-weight: 700;
    line-height: 21px;

}

.sticky-advertisement > a {
    font-size: 17px;
    text-transform: uppercase;
    color: #121930;
    font-weight: 700;
    line-height: 21px;
}

@media screen and (max-width: 1279px) {
    .sticky-advertisement {
        top: 50px;
    }
}

.home-title {
    font-size: 40px;
    font-weight: 700;
    color: #FFF;
    position: relative;
    margin-bottom: 40px;
    line-height: normal;
    margin-top: 0;
}

.home-title::before {
    position: absolute;
    content: '';
    left: 0;
    bottom: -8px;
    width: 180px;
    height: 4px;
    background-color: #FCB00F;
}

@media screen and (max-width: 1279px) {
    .home-container {
        padding-left: 40px;
        padding-right: 40px;
    }
}

@media screen and (max-width: 767px) {
    .home-container {
        padding-left: 16px;
        padding-right: 16px;
    }
}

/*/entry-info*/
.entryInfo {
    background-color: #010302;
    color: #FFF;
}

.entryInfo .bg-container {
    background-image: url('../assets/images/entry-info/bg.jpg');
    background-position: center bottom;
    background-repeat: no-repeat;
    background-size: cover;
}

.entryInfo .bg-container > .home-container {
    height: 100%;
    padding-top: 64px;
}

.entryInfo--article {
    padding-bottom: 390px;
}

.entryInfo--article-title {
    display: flex;
    align-items: flex-start;
    margin-bottom: 32px;
}

.entryInfo--article-titleWrapper > h1 {
    font-size: 48px;
    font-weight: 700;
    line-height: normal;
    margin: 0;
}

.entryInfo--article-titleWrapper > h3 {
    font-size: 32px;
    font-weight: 700;
    line-height: normal;
}

.entryInfo--article-title > span {
    padding: 4px 8px;
    border: 1px solid #FFF;
    border-radius: 4px;
    margin: 16px;
    font-size: 12px;
    line-height: normal;
}

.entryInfo--article-subtitles {
    max-width: 468px;
}

.entryInfo--article-subtitles > p:first-child {
    font-size: 16px;
    color: #FCB00F;
    margin-bottom: 32px;
    font-weight: 700;
    line-height: 21px;
}

.entryInfo--article-subtitles > p:last-child {
    font-size: 18px;
    color: #FFF;
    font-weight: 400;
    margin-bottom: 32px;
    line-height: 26px;
}

.entryinfo--article-socialMediaMarks {
    margin-bottom: 8px;
}

.entryinfo--article-socialMediaMarks > h6 {
    color: #FFF;
    font-size: 16px;
    font-weight: 700;
    line-height: 21px;
}

.entryinfo--article-socialMediaLinks {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 32px;
}

.entryinfo--article-socialMediaLinks > a > img {
    width: 32px;
    height: 32px;
}

.entryinfo--article-socialMediaButtons {
    display: flex;
    flex-direction: column;
    gap: 16px;
    max-width: 236px;
}

.entryinfo--article-socialMediaButtons > a {
    padding: 12px 0;
    font-size: 18px;
    font-weight: 600;
    color: #FFF;
    background: none;
    outline: none;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    text-align: center;
}

.entryinfo--article-socialMediaButtons > a:first-child {
    background-color: #5395E3;
    line-height: 21px;
}

.entryinfo--article-socialMediaButtons > a:first-child:hover {
    background-color: #2266B4;
}

.entryinfo--article-socialMediaButtons > a:last-child {
    border: 2px solid #5395E3;
    line-height: 21px;
}

.entryinfo--article-socialMediaButtons > a:last-child:hover {
    border-color: #2266B4;
}

.entryInfo--cards {
    margin-top: -202px;
    padding-bottom: 64px;
}

.entryInfo--cards-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 32px;
}

.entryInfo--card {
    background-color: #FFF;
    border-radius: 10px;
    padding: 32px;
    box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.20);
    display: flex;
    flex-direction: column;
    gap: 16px;
    justify-content: space-between;
    align-items: center;
}

.entryInfo--card > img {
    width: 56px;
    height: 56px;
}

.entryInfo--card > p {
    text-align: center;
    font-size: 16px;
    font-weight: 700;
    color: #121930;
    line-height: 21px;
}

@media screen and (max-width: 1279px) {
    .entryInfo .bg-container {
        background-color: #121930;
        background-image: none;
    }

    .entryInfo .bg-container > .home-container {
        padding-top: 32px;
    }

    .entryInfo--article {
        padding-bottom: 333px;
    }

    .entryInfo--article-subtitles {
        max-width: 100%;
    }

    .entryInfo--article-subtitles > p:first-child > br {
        display: none;
    }

    .entryInfo--cards {
        margin-top: -222px;
        padding-bottom: 32px;
    }

    .entryInfo--cards-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media screen and (max-width: 767px) {
    .entryInfo--article {
        padding-top: 50px;
        padding-bottom: 198px;
    }

    .entryInfo--article-title {
        flex-direction: column;
    }

    .entryInfo--article-title > span {
        margin-left: 0;
        margin-bottom: 0;
    }

    .entryInfo--article-subtitles > p:first-child,
    .entryInfo--article-subtitles > p:last-child {
        margin-bottom: 16px;
    }

    .entryinfo--article-socialMediaButtons {
        max-width: 100%;
    }

    .entryInfo--cards {
        margin-top: -129px;
    }

    .entryInfo--cards-grid {
        grid-template-columns: repeat(1, 1fr);
    }
}

/*/learning*/
.learning--section-header {
    background-color: #121930;
}

.learning--section-header > .home-container {
    padding: 64px 200px 64px 100px;
}

.learning--section-header > .home-container > h2 {
    color: #fff;
    font-size: 32px;
    font-weight: 700;
    line-height: normal;
}

.learning--section-header > .home-container > h2 > span {
    color: #FCB00F;
    margin-left: 0;
    font-size: inherit;
    font-weight: 700;
    line-height: normal;
    letter-spacing: normal;
}

.learning--section-gridV1,
.learning--section-gridV2 {
    display: grid;
    gap: 32px;
    padding: 64px 0;
}

.learning--section-gridV1 {
    grid-template-columns: 7fr 5fr;
}

.learning--section-gridV2 {
    grid-template-columns: 5fr 7fr;
}

.learning--gridV1-text > h4,
.learning--gridV2-text > h4 {
    color: #121930;
    margin-top: 0;
    line-height: normal;
}

.learning--gridV1-text > ul {
    font-size: 18px;
    line-height: 26px;
    font-weight: 400;
    color: #3F4452;
    padding-left: 20px;
}

.learning--gridV1-text > ul > li {
    line-height: 26px;
}

.learning--gridV2-collapsedText > h6 {
    color: #121930;
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 16px;
}

.learning--gridV2-collapsedText > p {
    color: #3F4452;
    font-size: 18px;
    font-weight: 400;
    margin-bottom: 16px;
    line-height: 26px;
}

.learning--gridV2-collapsedText {
    max-height: 330px;
    overflow: hidden;
    transition: max-height .3s ease-out;
}

.learning--gridV2-collapsedText--active {
    max-height: 1646px;
    transition: max-height 0.3s ease-in;
}

.learning--gridV2-text > div:last-child {
    padding-top: 24px;
    display: flex;
    justify-content: center;
}

.learning-collapse-button {
    background-color: transparent;
    border: none;
    outline: none;
    color: #2266B4;
    font-size: 16px;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 16px;
    cursor: pointer;
}

.learning-collapse-icon {
    transition: transform 0.3s;
}

.learning-collapse-icon--active {
    transform: rotate(180deg);
}

.learning--gridV1-photo > img,
.learning--gridV2-photo > img {
    width: 100%;
    height: auto;
}

.learning--reasons-buttonWrapper {
    display: flex;
    flex-direction: row;
    gap: 16px;
    align-items: center;
    justify-content: center;
}

.learning--reasons-buttonWrapper > a {
    background-color: transparent;
    border: none;
    outline: none;
    padding: 12px 16px;
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    width: 190px;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    text-align: center;
    line-height: 21px;
}

.learning--reasons-buttonWrapper > a:first-child {
    color: #fff;
    background-color: #5395E3;
    line-height: 21px;
}

.learning--reasons-buttonWrapper > a:first-child:hover {
    background-color: #2266B4;
}

.learning--reasons-buttonWrapper > a:last-child {
    border: 1px solid #5395E3;
    color: #2266B4;
}

.learning--reasons-buttonWrapper > a:last-child:hover {
    border-color: #2266B4;
}

@media screen and (max-width: 1279px) {
    .learning--section-header > .home-container {
        padding: 64px 40px;
    }
}

@media screen and (max-width: 767px) {
    .learning--section-header > .home-container {
        padding: 64px 16px;
    }

    .learning--section-header > .home-container > h2 > br {
        display: none;
    }

    .learning--section-gridV1,
    .learning--section-gridV2 {
        grid-template-columns: 1fr;
    }

    .learning--gridV1-text {
        order: 2;
    }

    .learning--gridV1-photo {
        order: 1;
    }

    .learning--reasons-buttonWrapper {
        flex-direction: column;
    }

    .learning--reasons-buttonWrapper > a {
        width: 100%;
    }
}

/*/learning-reason*/
.learning--reasons-header {
    background-color: #121930;
    padding: 64px 0;
}

.learning--reasons-header-wrapper {
    max-width: 768px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    gap: 32px;
}

.learning--reasons-header-wrapper > h1 {
    text-align: center;
    font-size: 48px;
    color: #FFF;
    font-weight: 700;
    line-height: normal;
    margin: 0;
}

.learning--reasons-header-wrapper > p {
    text-align: center;
    font-size: 16px;
    color: #FFF;
    font-weight: 700;
    line-height: 21px;
}

.learning--reasons-header-wrapper > p > span {
    color: #FCB00F;
}

.learning--reasons-header-additionalInfo {
    display: grid;
    grid-template-columns: 1fr 1fr;
}

.learning--reasons-header-additionalInfo > div:first-child {
    border-right: 1px solid #DDD;
}

.learning--reasons-header-additionalInfo > div {
    display: flex;
    text-align: center;
    justify-content: center;
    align-items: center;
    gap: 16px;
    flex-grow: 1;
}

.learning--reasons-header-additionalInfo h6 {
    font-size: 32px;
    font-weight: 700;
    color: #FCB00F;
    font-style: normal;
    line-height: normal;
}

.learning--reasons-header-additionalInfo p {
    font-size: 16px;
    font-weight: 700;
    color: #FFF;
    font-style: normal;
    line-height: 21px
}

.learning--reasons-article {
    padding: 64px 0;
    max-width: 963px;
    margin: 0 auto;
}

.learning--reasons-article-block {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
}

.learning--reasons-article-block:not(:last-child) {
    border-bottom: 1px solid #DDD;
    padding-bottom: 32px;
    margin-bottom: 32px;
}

.learning--reasonNumber > h2 {
    width: 150px;
    margin-right: 32px;
    color: rgba(83, 149, 227, 0.25);
    font-size: 128px;
    line-height: 128px;
    font-weight: 600;
}

.learning--reasons-article-block > div:last-child {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.learning--reasonDescription > h3 {
    color: #0D1F2D;
    font-size: 32px;
    font-weight: 700;
    line-height: normal;
}

.learning--reasonDescription > h5 {
    color: #121930;
    font-size: 16px;
    font-weight: 700;
    line-height: 21px;
}

.learning--reasonDescription > p {
    color: #3F4452;
    font-size: 18px;
    font-weight: 400;
    line-height: 26px;
}

.learning--reasonDescription > ul {
    list-style: none;
    margin-left: 8px;
}

.learning--reasonDescription > ul > li {
    color: #3F4452;
    font-size: 18px;
    font-weight: 400;
    padding-left: 32px;
    position: relative;
    line-height: 26px;
}

.learning--reasonDescription > ul > li::before {
    position: absolute;
    content: '';
    width: 24px;
    height: 24px;
    background-image: url('../assets/images/learning-reason/check-circle.svg');
    top: 2px;
    left: 0;
}

.learning--reasonDescription > ul > li:not(:last-child) {
    margin-bottom: 16px;
}

@media screen and (max-width: 1279px) {
    .learning--reasons-article {
        padding: 64px 40px;
    }

    .learning--reasons-header-wrapper {
        padding: 0 16px;
    }
}

@media screen and (max-width: 767px) {
    .learning--reasons-header-wrapper {
        width: 100%;
    }

    .learning--reasons-header-additionalInfo {
        grid-template-columns: 1fr;
    }

    .learning--reasons-header-additionalInfo > div:first-child {
        border-right: none;
        border-bottom: 1px solid #DDD;
        padding-bottom: 16px;
        margin-bottom: 16px;
    }

    .learning--reasonNumber {
        display: none;
    }

    .learning--reasons-article {
        padding: 64px 16px;
    }
}

/*/social-rating*/
.social--rating {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-top: 8px;
}

.social--rating-marks {
    color: #FFF;
    font-size: 16px;
    font-weight: 700;
    margin-right: 8px;
}

.rating {
    font-size: 16px;
    display: flex;
    align-items: center;
    transform: perspective(1px) translateZ(0);
}

.rating--body {
    position: relative;
}

.rating--body::before {
    content: '★★★★★';
    font-size: 16px;
    display: block;
    color: #FFF;
}

.rating--active {
    position: absolute;
    width: 0;
    height: 100%;
    top: 0;
    left: 0;
    overflow: hidden;
}

.rating--active::before {
    content: '★★★★★';
    font-size: 16px;
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    color: #FCB00F;
}

.rating--items {
    display: flex;
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
}

.rating--item {
    margin: 0;
    flex: 0 0 20%;
    height: 100%;
    opacity: 0;
}

/*/client-opinions*/
.clientOpinions {
    background-color: #010302;
    color: #FFF;
}

.clientOpinions .bg-container {
    background-image: url('../assets/images/client-opinions/bg.jpg');
    padding: 64px 0;
    background-size: cover;
    background-position: center;
}

.clientOpinions .bg-container > .home-container {
    height: 100%;
    display: flex;
    gap: 32px;
}

.clientOpinions--title {
    width: 33%;
}

.clientOpinions--title-headerWrapper {
    display: flex;
    flex-direction: column;
}

.clientOpinions--title-headerWrapper > h1 {
    font-size: 48px;
    font-weight: 700;
    margin: 0 0 32px 0;
}

.clientOpinions--subTitle {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
}

.clientOpinions--subTitle > p {
    font-size: 16px;
    font-weight: 700;
    display: flex;
    justify-content: center;
    line-height: 21px;
}

.clientOpinions--subTitle-socialMedias {
    display: flex;
    gap: 16px;
}

.clientOpinions--subTitle-socialMedias > button {
    background: transparent;
    border: none;
    cursor: pointer;
}

.clientOpinions--subTitle-socialMedias > img {
    width: 32px;
    height: 32px;
}

.clientOpinions--opinions-wrapper {
    width: 67%;
    display: flex;
    gap: 32px;
}

.clientOpinions--opinions-wrapper::-webkit-scrollbar {
    display: none;
}

.clientOpinions--opinions-column {
    display: flex;
    flex-direction: column;
    gap: 32px;
    width: 50%;
}

.clientOpinions--opinions-column > article {
    background-color: white;
    padding: 32px;
    display: flex;
    gap: 14px;
    flex-direction: column;
    border-radius: 10px;
    box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.20);
    backdrop-filter: blur(2px);
}

.clientOpinions--opinions-column > article > img {
    height: 19px;
    width: 93px;
}

.clientOpinions--opinions-column > article > p {
    font-weight: 700;
    font-size: 16px;
    line-height: 21px;
    color: black;
}

@media screen and (max-width: 1279px) {
    .clientOpinions .bg-container > .home-container {
        flex-direction: column;
    }

    .clientOpinions--title {
        width: 100%;
    }

    .clientOpinions--opinions-wrapper {
        width: 100%;
        flex-direction: column;
    }

    .clientOpinions--opinions-column {
        width: 100%;
    }
}

@media screen and (max-width: 767px) {
    .clientOpinions .bg-container {
        background-image: none;
        background-color: #121930;
    }
}

/*/lessons-form*/
.lessonsForm {
    background-color: #FFF;
    color: #000;
}

.lessonsForm > .home-container {
    display: flex;
    flex-direction: column;
}

.lessonsForm--row {
    display: flex;
    gap: 32px;
    padding-top: 64px;
    padding-bottom: 64px;
}

.lessonsForm--row-columnSmall {
    width: 40%;
}

.lessonsForm--row-columnSmall > img {
    max-width: 100%;
}

.lessonsForm--row-columnLarge {
    width: 60%;
}

.lessonsForm--title {
    display: flex;
    flex-direction: column;
}

.lessonsForm--title > h1 {
    font-size: 40px;
    color: #121930;
    font-weight: 700;
}


.lessonsForm--content {
    display: flex;
    flex-direction: column;
    gap: 32px;
    margin-bottom: 32px;
    overflow: hidden;
    color: #3F4452;
    font-size: 18px;
    height: 225px;
    transition: height .3s ease-out;
}

.lessonsForm--content .lessonsForm--content-paragraph > p {
    line-height: 26px
}

.lessonsForm--content > div > p {
    line-height: 23px;
}

.lessonsForm--content > div > ul {
    padding-inline-start: 20px;
    padding-top: 16px;
    line-height: 26px;
    margin-left: 8px;
}

.lessonsForm--expandButton-wrapper {
    display: flex;
    justify-content: center;
}

.lessonsForm--row-reverse {
    flex-direction: row-reverse;
}

.lessonsForm--expandButton-wrapper > button {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 16px;
    color: #2266B4;
    background-color: white;
    border: none;
    font-weight: 700;
    width: 100px;
    height: 20px;
    font-size: 16px;
    cursor: pointer;
}

.lessonsForm--expandButton-arrow {
    transform: rotate(0);
    transition: transform 0.3s;
}

.lessonsForm--expandButton-arrow--active {
    transform: rotate(180deg);
}

.lessonsForm--textWrapper--active {
    height: calc(100% - 140px);
    transition: height 0.3s ease-in;
}

@media screen and (max-width: 1279px) {
    .lessonsForm--row-columnLarge {
        height: auto;
        height: auto;
    }

    .lessonsForm--expandButton-wrapper {
        display: flex;
        justify-content: center;
        gap: 8px;
    }
}

@media screen and (max-width: 767px) {
    .lessonsForm--row {
        width: 100%;
        display: grid;
        padding-bottom: 64px;
        padding-top: 32px;
    }


    .lessonsForm--row-columnLarge {
        width: 100%;
    }

    .lessonsForm--row-columnSmall {
        width: 100%;
        justify-content: center;
        display: flex;
    }

    .lessonsForm--textWrapper {
        height: 200px;
    }

    .lessonsForm--expandButton--active {
        max-height: 1000px;
        transition: max-height 0.3s ease-in;
    }
}

/*/program*/
.program {
    background-color: #121930;
    color: white;
}

.program {
    padding: 64px 0;
}

.program > .home-container {
    display: flex;
    height: 100%;
    gap: 32px;
}

.program--title {
    width: 33%;
}

.program--title-headerWrapper {
    display: flex;
    flex-direction: column;
}

.program--title-headerWrapper > h1 {
    font-size: 48px;
    font-weight: 700;
}

.program--subTitle {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 32px;
}

.program--subTitle > p {
    font-size: 16px;
    font-weight: 700;
    display: flex;
    justify-content: center;
    line-height: 21px;
}

.program--subTitle > a {
    display: flex;
    align-items: center;
    flex-direction: column;
    border-radius: 4px;
    background-color: #5395E3;
    padding: 12px 16px;
    justify-content: center;
    color: white;
    border: none;
    font-weight: 600;
    cursor: pointer;
    text-decoration: none;
    line-height: 21px;
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 21px;
}

.program--programPoints {
    display: flex;
    flex-direction: column;
    gap: 32px;
    width: 67%;
}

.program--programPoints > details > summary {
    list-style-type: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 16px;
    cursor: pointer;
}

.program--programPoints > details > summary::-webkit-details-marker {
    display: none;
}

.program--programPoints > details[open] .program--point-title img {
    transform: rotate(180deg);
}

.program--programPoints > details {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.program--point {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 16px;
    width: 100%;
}

.program--point-index {
    height: 48px;
    min-width: 48px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    border: 1px solid #5395E3;
    position: relative;
    font-size: 20px;
    font-style: normal;
    font-weight: 700;
    line-height: 23px;
}

.program--point-title {
    display: flex;
    justify-content: space-between;
    width: 100%;
    align-items: center;
    border-bottom: 1px solid #5395E3;
    height: auto;
    padding: 8px 0;
    font-weight: 700;
    font-size: 16px;
    font-style: normal;
    line-height: 21px;
}

.program--point-description {
    margin: 16px 0 0 56px;
    line-height: normal;
}

.program--point-subtitle {
    display: flex;
    padding: 4px 16px;
    align-items: flex-start;
    gap: 10px;
    border-radius: 4px;
    border: 1px solid #FCB00F;
    width: fit-content;
    margin: 16px 0 0 56px;
    font-weight: 700;
}

.program--point-caseStudy {
    background: #FCB00F;
    color: black;
}

.program--point-list {
    margin: 16px 0 0 72px;
}

.program--point-info {
    position: absolute;
    top: -4px;
    right: -4px;
}

@media screen and (max-width: 767px) {
    .program > .home-container {
        width: 100%;
        padding-top: 0px;
        flex-direction: column;
    }

    .program--title {
        width: 100%;
    }

    .program--programPoints {
        width: 100%;
    }

    .program--subTitle > a {
        width: 100%;
    }

    .program--point-subtitle {

    }
}

/*/coaches*/
.coaches h1 {
    color: #0D1F2D;
    font-size: 40px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
}

.coaches__line {
    height: 2px;
    width: 180px;
    background-color: #FCB00F;
    margin-bottom: 32px;
}

.coaches__article {
    padding-top: 64px;
    padding-bottom: 64px;
    border-bottom: 1px solid #DDDDDD;
}

.coaches__article img {
    width: 100%;
    border-radius: 10px;
}

.coaches__article-white {
    background-color: white
}

.coaches__article-grey {
    background-color: #FAFAFA;
}

.coaches__box {
    gap: 32px;
    display: flex;
}

.coaches__article-photo {
    max-height: 368px;
    max-width: 368px;
    flex-shrink: 0
}

.coaches__article-profile {
    display: flex;
    flex-direction: column;
}

.coaches__article-profile p {
    margin-top: 16px;
    color: #3F4452;
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    line-height: 26px;
}

.coaches__article-profile h5 {
    font-size: 20px;
    font-style: normal;
    font-weight: 700;
    line-height: 23px;
    color: #0D1F2D;
    margin-top: 32px;
    margin-bottom: 16px;
}

.coaches__article-profile ul {
    padding: 0 0 0 24px;
    margin: 0;
}

.coaches__article-profile li {
    color: #3F4452;
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    line-height: 26px;
}

.coaches__article-profile h3 {
    color: #0D1F2D;
    font-size: 32px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
}

.coaches__article h4 {
    margin-top: 16px;
    color: #2266B4;
    font-size: 20px;
    font-style: normal;
    font-weight: 700;
    line-height: 23px;
}

@media screen and (max-width: 1279px) {
    .coaches__box {
        flex-direction: column;
    }

    .coaches__article img {
        margin-bottom: 32px;
    }

    .coaches__box-reverse {
        flex-direction: column-reverse;
    }
}

@media screen and (max-width: 767px) {
    .coaches__article img {
        margin-bottom: 0;
    }
}

/*/join-us*/
.join {
    background-color: #010302;
    color: #FFF;
    padding: 64px 0;
}

.join__main {
    display: flex;
    gap: 32px
}

.join__line {
    height: 2px;
    width: 180px;
    background-color: #FCB00F;
    margin-bottom: 32px;
}

.join__main-left h1 {
    font-size: 40px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
}

.join__paragraphs {
    display: flex;
    flex-direction: column;
    gap: 32px
}

.join__paragraph {
    display: flex;
    gap: 16px;
}

.join__paragraph-icon {
    width: 40px;
    height: 40px;
}

.join__paragraph-text h3 {
    font-size: 20px;
    font-style: normal;
    font-weight: 700;
    line-height: 23px;
}

.join__paragraph-text p {
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    line-height: 26px;
    margin-top: 16px;
}

.join__button {
    cursor: pointer;
    padding: 12px 16px;
    margin-top: 16px;
    border-radius: 4px;
    border: 2px solid #5395E3;
    background-color: transparent;
    color: #FFF;
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 21px;
    text-decoration: none;
    display: inline-block;
}

.join__button:hover {
    color: #DDD;
    border: 2px solid #2266B4;
}

.join__main-left {
    width: 60%;
}

.join__main-right {
    display: flex;
    width: 40%;
    flex-direction: column;
    gap: 32px;
}

.join__box {
    padding: 32px;
    border-radius: 10px;
    background: #FFF;
    box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
    backdrop-filter: blur(5px);
}

.join__box-header {
    justify-content: space-between;
    display: flex;
}

.join__box h3 {
    color: #121930;
    font-size: 20px;
    font-style: normal;
    font-weight: 700;
    line-height: 23px;
    margin-bottom: 16px;
}

.join__box-header img {
    width: 80px;
}

.join__price {
    font-size: 32px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    padding: 8px 16px;
    color: #121930;
    display: inline-flex;
    gap: 8px;
    border-radius: 4px;
    background-color: rgba(83, 149, 227, 0.25);
}

.join__box-part {
    border-bottom: 1px solid #DDD;
    margin-bottom: 16px;
    padding-bottom: 16px;
}

.join__box-part ul {
    margin: 0;
    padding: 16px 0 0 24px;
}

.join__box-part li {
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 21px;
    color: #3F4452;
}

.join__box h4 {
    color: #121930;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 21px;
}

.join__box p {
    color: #3F4452;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 21px;
    margin-top: 8px;
}

.join__box-part > a {
    color: #2266B4;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 21px;
    text-decoration-line: underline;
}

.join__box-button {
    width: 100%;
    cursor: pointer;
    padding: 12px 16px;
    text-align: center;
    background-color: #5395E3;
    color: #FFF;
    border-radius: 4px;
    border: none;
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 21px;
    text-decoration: none;
    display: block;
}

.join__box-button:hover {
    background-color: #2266B4;
}

.join__box-footer {
    display: flex;
    gap: 16px;
    margin-top: 32px;
}

.join__form-group {
    padding-top: 16px;
    color: #121930;
}

.join__form-group input[type="text"] {
    padding: 12px 16px;
    border-radius: 4px;
    width: 100%;
    border: 2px solid #3F4452;
}

.join__form-group input[type="checkbox"] {
    margin-right: 9px;
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    line-height: 26px;
}

.join__form-group p {
    margin-top: 0;
    padding-bottom: 16px;
    border-bottom: 1px solid #DDD;
}

.join__form-group a {
    border-radius: 4px;
    cursor: pointer;
    background: transparent;
    border: 2px solid #5395E3;
    padding: 12px 16px;
    text-align: center;
    color: #2266B4;
    width: 100%;
    text-decoration: none;
    display: inline-block;
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 21px;
}

.join__form-group a:hover {
    border: 2px solid #2266B4;
}

@media screen and (max-width: 1279px) {
    .join__main {
        flex-direction: column;
    }

    .join__main-left,
    .join__main-right {
        width: 100%;
    }
}

/*/other*/
.other {
    background-color: rgba(83, 149, 227, 0.25);
    color: #FFF;
    padding: 64px 0;
}

.other__content {
    padding: 0 100px;
}

.other h2 {
    color: #121930;
    font-size: 40px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    margin-bottom: 16px;
}

.other p {
    color: #121930;
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    line-height: 26px;
}

.other__section {
    padding-top: 32px;
}

.other__section h3 {
    color: #121930;
    font-size: 20px;
    font-style: normal;
    font-weight: 700;
    line-height: 23px;
    margin-bottom: 16px;
}

.other__section a {
    cursor: pointer;
    padding: 12px 16px;
    border-radius: 4px;
    border: 2px solid #5395E3;
    color: #2266B4;
    text-align: center;
    background-color: transparent;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 21px;
    text-decoration: none;
    display: inline-block;
}

.other__section a:hover {
    border: 2px solid #2266B4;
}

.other__section-buttons {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
}

@media screen and (max-width: 1279px) {
    .other__content {
        padding: 0
    }
}

