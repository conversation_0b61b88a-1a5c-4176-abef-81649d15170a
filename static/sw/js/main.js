const collapseButton = document.querySelector('.learning-collapse-button');
const collapseButtonSpan = document.querySelector('.learning-collapse-button > span');
const collapseIcon = document.querySelector('.learning-collapse-icon');
const collapseContent = document.querySelector('.learning--gridV2-collapsedText');

const scheduleButton = document.querySelector('#schedule-button');
const scheduleButtonSpan = document.querySelector('#schedule-button > span');
const scheduleIcon = document.querySelector('#schedule-button img');
const scheduleContent = document.querySelector('#schedule-content');

const lessonsFormButton = document.querySelector('#lessons-button');
const lessonsFormButtonSpan = document.querySelector('#lessons-button > span');
const lessonsFormIcon = document.querySelector('#lessons-button img');
const lessonsFormContent = document.querySelector('#lessons-content');

let timeoutId;

function extendBox([content, contentClass], [icon, iconClass], buttonSpan) {
  content.classList.toggle(contentClass);
  icon.classList.toggle(iconClass);

  if (timeoutId) {
    clearTimeout(timeoutId);
  }

  timeoutId = setTimeout(() => {
    const textButton = buttonSpan.textContent;
    buttonSpan.textContent = textButton === 'Rozwiń' ? 'Zwiń' : 'Rozwiń';
    timeoutId = null;
  }, 300);
}

collapseButton.addEventListener('click', () => {
  extendBox(
    [collapseContent, 'learning--gridV2-collapsedText--active'],
    [collapseIcon, 'learning-collapse-icon--active'],
    collapseButtonSpan)
});


scheduleButton.addEventListener('click', () => {
  extendBox(
    [scheduleContent, 'lessonsForm--textWrapper--active'],
    [scheduleIcon, 'lessonsForm--expandButton-arrow--active'],
    scheduleButtonSpan)
});

lessonsFormButton.addEventListener('click', () => {
  extendBox(
    [lessonsFormContent, 'lessonsForm--textWrapper--active'],
    [lessonsFormIcon, 'lessonsForm--expandButton-arrow--active'],
    lessonsFormButtonSpan)
});

class Accordion {
  constructor(el) {
    this.el = el;
    this.summary = el.querySelector('summary');
    this.content = el.querySelector('.program--point-content');

    this.animation = null;
    this.isClosing = false;
    this.isExpanding = false;
    this.summary.addEventListener('click', (e) => this.onClick(e));
  }

  onClick(e) {
    e.preventDefault();
    this.el.style.overflow = 'hidden';
    if (this.isClosing || !this.el.open) {
      this.open();
    } else if (this.isExpanding || this.el.open) {
      this.shrink();
    }
  }

  shrink() {
    this.isClosing = true;

    const startHeight = `${this.el.offsetHeight}px`;
    const endHeight = `${this.summary.offsetHeight}px`;

    if (this.animation) {
      this.animation.cancel();
    }

    this.animation = this.el.animate({
      height: [startHeight, endHeight]
    }, {
      duration: 400,
      easing: 'ease-out'
    });

    this.animation.onfinish = () => this.onAnimationFinish(false);
    this.animation.oncancel = () => this.isClosing = false;
  }

  open() {
    this.el.style.height = `${this.el.offsetHeight}px`;
    this.el.open = true;
    window.requestAnimationFrame(() => this.expand());
  }

  expand() {
    this.isExpanding = true;
    const startHeight = `${this.el.offsetHeight}px`;
    const endHeight = `${this.summary.offsetHeight + this.content.offsetHeight}px`;

    if (this.animation) {
      this.animation.cancel();
    }

    this.animation = this.el.animate({
      height: [startHeight, endHeight]
    }, {
      duration: 400,
      easing: 'ease-out'
    });
    this.animation.onfinish = () => this.onAnimationFinish(true);
    this.animation.oncancel = () => this.isExpanding = false;
  }

  onAnimationFinish(open) {
    this.el.open = open;
    this.animation = null;
    this.isClosing = false;
    this.isExpanding = false;
    this.el.style.height = this.el.style.overflow = '';
  }
}

document.querySelectorAll('details').forEach((el) => {
  new Accordion(el);
});