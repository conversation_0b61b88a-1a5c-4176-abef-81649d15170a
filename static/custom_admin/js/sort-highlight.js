// Na podstawie http://djangosnippets.org/snippets/1053/

// Jak mam dwa różne rodzaje rzeczy sortowalnych na stronie, to traktowane są one razem i numerki im wychodzą wspólne. Ale ponieważ to w sumie nie przeszkadza, to na razie niech se tak będzie.

$(function() {
    $('div.inline-group').sortable({
        items: 'div.inline-related',
        handle: 'h3:first',
        update: uporzadkuj
    });
    $('div.inline-related h3').css('cursor', 'move');
    $('div.inline-related').find('input[id$=ordering]').parent('div').hide();

    $('form').each(function() {
	    $(this).change(uporzadkuj);
    });
});

function uporzadkuj() {
    $('body').find('div.inline-related').each(function(i) {
        if ($(this).find('select[id$=highlight]').val() || $(this).find('input[id$=title]').val()) { // AAARGH
            $(this).find('input[id$=ordering]').val(i+1);
        }
    });
}
