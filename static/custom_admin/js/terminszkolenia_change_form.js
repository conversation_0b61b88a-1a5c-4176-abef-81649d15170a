$(function(){
    var form_helper = function() {
        var form = $('#terminszkolenia_form');
        var termin_lookup = $('#lookup_id_termin_nadrzedny');
        var szkolenie_lookup = $('#lookup_id_szkolenie');

        $.ajax({
            dataType: "json",
            type: form.attr('method'),
            url: "/admin/www/terminszkolenia/termin-ajax-form-helper/",
            data: form.serialize(),
            success: function (data) {
                autoryzacja(data['szkolenie_autoryzacja']);

                if (data['termin_nadrzedny_url']) {
                    termin_lookup.show();
                    termin_lookup.attr("href", data['termin_nadrzedny_url']);
                }
                else {
                    termin_lookup.hide();
                }

                szkolenie_lookup.attr("href", data['szkolenie_url']);
            },
            error: function(data) {
                // failed to finish response
            }
        });
        return false;
    };

    var autoryzacja = function(autoryzacja_option) {
        var autoryzacja_aktywna_label = $('label[for="id_autoryzacja_aktywna"]');
        var text_help = '<p class="help help-autoryzacja">UWAGA: To szkolenie nie ma opcji autoryzacji.</p>';

        function check_inlines(set_as_disabled) {
          $("label[for^=id_uczestnik_set-][for$=-chce_autoryzacje]").each(function (index) {
              if (set_as_disabled) {
                  $(this).css({opacity: 0.5});
                  $(text_help).insertAfter($(this));
              }
              else {
                  $(this).css({opacity: 1.0});
              }
          });
        }

        $('p.help-autoryzacja').remove();

        if (autoryzacja_option === undefined || autoryzacja_option === null) {
            autoryzacja_aktywna_label.css({opacity: 1.0});
            check_inlines(false);
        }
        else {
          if (autoryzacja_option) {
              autoryzacja_aktywna_label.css({opacity: 1.0});
              check_inlines(false);
          } else {
              autoryzacja_aktywna_label.css({opacity: 0.5});
              $(text_help).insertAfter(autoryzacja_aktywna_label);
              check_inlines(true);
          }
        }
    };

    var szkolenie = $('#id_szkolenie');
    $('#id_lokalizacja').on('change', form_helper);
    $('#id_termin').on('change keyup focusin', form_helper);
    szkolenie.on('change keyup', form_helper);
    szkolenie.change();

    // potwierdzenie dodania obiektu jako nowy
   $("input[type='submit'][name='_saveasnew']").click(function(e) {
        if (!confirm("Czy jesteś pewien, że chcesz utworzyć nowy termin szkolenia? PS. Uczestnicy nie zostaną skopiowani.")) {
            e.preventDefault();
            return false;
        }
        SelectBox.select_all('is_sprzet_to');
        // if(c)
        // {
        //     $('<input>').attr({
        //         type: 'hidden',
        //         id: '_saveasnew',
        //         name: '_saveasnew',
        //         value: '1'
        //     }).appendTo('form#terminszkolenia_form');
        //     $('form#terminszkolenia_form').submit();
        // }
    });
});
