$(function() {
	function money_fix(x) {
		return (Math.round(Math.round(x * 10000) / 100) / 100).toFixed(2);
	}

	function brutto_z_netto(field) {
		var stawka = parseFloat($('#id_stawka_vat')[0].value);

		$('#'+field.id+'_brutto')[0].value = (field.value === '') ? '' : money_fix((1+stawka) * field.value);
	}

	function netto_z_brutto(field) {
		var stawka = parseFloat($('#id_stawka_vat')[0].value);

		field.value = ($('#'+field.id+'_brutto')[0].value === '') ? '' : money_fix($('#'+field.id+'_brutto')[0].value / (1+stawka));
	}

	$('.cena_netto').each(function() {
		brutto_z_netto(this);
		var that = this;
		$(this).change(function(e) {
			that.value = money_fix(that.value);
			brutto_z_netto(that);
		});
		$('#'+this.id+'_brutto').change(function(e) {
			netto_z_brutto(that);
			brutto_z_netto(that);
		});
	});

	$('#id_stawka_vat').change(function(e) {
		$('.cena_netto').each(function() {
			brutto_z_netto(this);
		});
	});
});
