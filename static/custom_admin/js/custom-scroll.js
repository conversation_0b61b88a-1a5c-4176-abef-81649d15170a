const maximum = 10;
let form = null;
let results = null;

function handleChange(event) {
  const results = document.querySelector('.results')
  if (!results) return null;

  const newPosition = event.target.value * 100
  const offsetY = form.offsetTop
  const offsetX = form.offsetLeft += newPosition

  results.scrollTo(offsetX, offsetY)
}

function buildScroll() {
  const container = document.querySelector('#content-main')
  const tableWidth = document.querySelector('#result_list').offsetWidth;
  const resultsWidth = document.querySelector('div.results').offsetWidth;

  // Dodajemy div.results tylko gdy szerokość tabeli jest większa niż szerokość div.results
  if (tableWidth > resultsWidth) {
    const div = document.createElement('div');
    div.classList.add('list-scroll')

    const input = document.createElement('input')
    input.setAttribute('type', 'range');
    input.setAttribute('min', 0)
    input.setAttribute('max', maximum)
    input.setAttribute('value', 0)
    input.addEventListener('input', handleChange)

    div.appendChild(input)
    container.appendChild(div);
  }
}

document.addEventListener('DOMContentLoaded', () => {

    form = document.querySelector('#changelist-form');
    buildScroll();

})
