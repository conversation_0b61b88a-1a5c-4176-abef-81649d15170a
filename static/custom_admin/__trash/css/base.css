/*
    DJANGO Admin styles
*/

body {
    margin: 0;
    padding: 0;
    font-size: 12px;
    font-family: "Lucida Grande","DejaVu Sans","Bitstream Vera Sans",Verdana,Arial,sans-serif;
    color: #333;
    background: #fff;
}

/* LINKS */

a:link, a:visited {
    color: #5b80b2;
    text-decoration: none;
}

a:hover {
    color: #036;
}

a img {
    border: none;
}

a.section:link, a.section:visited {
    color: white;
    text-decoration: none;
}

/* GLOBAL DEFAULTS */

p, ol, ul, dl {
    margin: .2em 0 .8em 0;
}

p {
    padding: 0;
    line-height: 140%;
}

h1,h2,h3,h4,h5 {
    font-weight: bold;
}

h1 {
    font-size: 18px;
    color: #666;
    padding: 0 6px 0 0;
    margin: 0 0 .2em 0;
}

h2 {
    font-size: 16px;
    margin: 1em 0 .5em 0;
}

h2.subhead {
    font-weight: normal;
    margin-top: 0;
}

h3 {
    font-size: 14px;
    margin: .8em 0 .3em 0;
    color: #666;
    font-weight: bold;
}

h4 {
    font-size: 12px;
    margin: 1em 0 .8em 0;
    padding-bottom: 3px;
}

h5 {
    font-size: 10px;
    margin: 1.5em 0 .5em 0;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 1px;
}

ul li {
    list-style-type: square;
    padding: 1px 0;
}

ul.plainlist {
    margin-left: 0 !important;
}

ul.plainlist li {
    list-style-type: none;
}

li ul {
    margin-bottom: 0;
}

li, dt, dd {
    font-size: 11px;
    line-height: 14px;
}

dt {
    font-weight: bold;
    margin-top: 4px;
}

dd {
    margin-left: 0;
}

form {
    margin: 0;
    padding: 0;
}

fieldset {
    margin: 0;
    padding: 0;
}

blockquote {
    font-size: 11px;
    color: #777;
    margin-left: 2px;
    padding-left: 10px;
    border-left: 5px solid #ddd;
}

code, pre {
    font-family: "Bitstream Vera Sans Mono", Monaco, "Courier New", Courier, monospace;
    background: inherit;
    color: #666;
    font-size: 11px;
}

pre.literal-block {
    margin: 10px;
    background: #eee;
    padding: 6px 8px;
}

code strong {
    color: #930;
}

hr {
    clear: both;
    color: #eee;
    background-color: #eee;
    height: 1px;
    border: none;
    margin: 0;
    padding: 0;
    font-size: 1px;
    line-height: 1px;
}

/* TEXT STYLES & MODIFIERS */

.small {
    font-size: 11px;
}

.tiny {
    font-size: 10px;
}

p.tiny {
    margin-top: -2px;
}

.mini {
    font-size: 9px;
}

p.mini {
    margin-top: -3px;
}

.help, p.help {
    font-size: 10px !important;
    color: #999;
}

img.help-tooltip {
    cursor: help;
}

p img, h1 img, h2 img, h3 img, h4 img, td img {
    vertical-align: middle;
}

.quiet, a.quiet:link, a.quiet:visited {
    color: #999 !important;
    font-weight: normal !important;
}

.quiet strong {
    font-weight: bold !important;
}

.float-right {
    float: right;
}

.float-left {
    float: left;
}

.clear {
    clear: both;
}

.align-left {
    text-align: left;
}

.align-right {
    text-align: right;
}

.example {
    margin: 10px 0;
    padding: 5px 10px;
    background: #efefef;
}

.nowrap {
    white-space: nowrap;
}

/* TABLES */

table {
    border-collapse: collapse;
    border-color: #ccc;
}

td, th {
    font-size: 11px;
    line-height: 13px;
    border-bottom: 1px solid #eee;
    vertical-align: top;
    padding: 5px;
    font-family: "Lucida Grande", Verdana, Arial, sans-serif;
}

th {
    text-align: left;
    font-size: 12px;
    font-weight: bold;
}

thead th,
tfoot td {
    color: #666;
    padding: 2px 5px;
    font-size: 11px;
    background: #e1e1e1 url(../img/nav-bg.gif) top left repeat-x;
    border-left: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
}

tfoot td {
    border-bottom: none;
    border-top: 1px solid #ddd;
}

thead th:first-child,
tfoot td:first-child {
    border-left: none !important;
}

thead th.optional {
    font-weight: normal !important;
}

fieldset table {
    border-right: 1px solid #eee;
}

tr.row-label td {
    font-size: 9px;
    padding-top: 2px;
    padding-bottom: 0;
    border-bottom: none;
    color: #666;
    margin-top: -1px;
}

tr.alt {
    background: #f6f6f6;
}

.row1 {
    background: #EDF3FE;
}

.row2 {
    background: white;
}

/* SORTABLE TABLES */

thead th {
    padding: 2px 5px;
    line-height: normal;
}

thead th a:link, thead th a:visited {
    color: #666;
}

thead th.sorted {
    background: #c5c5c5 url(../img/nav-bg-selected.gif) top left repeat-x;
}

thead th.sorted .text {
  padding-right: 42px;
}

table thead th .text span {
    padding: 2px 5px;
    display:block;
}

table thead th .text a {
    display: block;
    cursor: pointer;
    padding: 2px 5px;
}

table thead th.sortable:hover {
    background: white url(../img/nav-bg-reverse.gif) 0 -5px repeat-x;
}

thead th.sorted a.sortremove {
    visibility: hidden;
}

table thead th.sorted:hover a.sortremove {
    visibility: visible;
}

table thead th.sorted .sortoptions {
    display: block;
    padding: 4px 5px 0 5px;
    float: right;
    text-align: right;
}

table thead th.sorted .sortpriority {
    font-size: .8em;
    min-width: 12px;
    text-align: center;
    vertical-align: top;
}

table thead th.sorted .sortoptions a {
    width: 14px;
    height: 12px;
    display: inline-block;
}

table thead th.sorted .sortoptions a.sortremove {
    background: url(../img/sorting-icons.gif) -4px -5px no-repeat;
}

table thead th.sorted .sortoptions a.sortremove:hover {
    background: url(../img/sorting-icons.gif) -4px -27px no-repeat;
}

table thead th.sorted .sortoptions a.ascending {
    background: url(../img/sorting-icons.gif) -5px -50px no-repeat;
}

table thead th.sorted .sortoptions a.ascending:hover {
    background: url(../img/sorting-icons.gif) -5px -72px no-repeat;
}

table thead th.sorted .sortoptions a.descending {
    background: url(../img/sorting-icons.gif) -5px -94px no-repeat;
}

table thead th.sorted .sortoptions a.descending:hover {
    background: url(../img/sorting-icons.gif) -5px -115px no-repeat;
}

/* ORDERABLE TABLES */

table.orderable tbody tr td:hover {
    cursor: move;
}

table.orderable tbody tr td:first-child {
    padding-left: 14px;
    background-image: url(../img/nav-bg-grabber.gif);
    background-repeat: repeat-y;
}

table.orderable-initalized .order-cell, body>tr>td.order-cell {
    display: none;
}

/* FORM DEFAULTS */

input, textarea, select, .form-row p {
    margin: 2px 0;
    padding: 2px 3px;
    vertical-align: middle;
    font-family: "Lucida Grande", Verdana, Arial, sans-serif;
    font-weight: normal;
    font-size: 11px;
}

textarea {
    vertical-align: top !important;
}

input[type=text], input[type=password], textarea, select, .vTextField {
    border: 1px solid #ccc;
}

/* FORM BUTTONS */

.button, input[type=submit], input[type=button], .submit-row input {
    background: white url(../img/nav-bg.gif) bottom repeat-x;
    padding: 3px 5px;
    color: black;
    border: 1px solid #bbb;
    border-color: #ddd #aaa #aaa #ddd;
}

.button:active, input[type=submit]:active, input[type=button]:active {
    background-image: url(../img/nav-bg-reverse.gif);
    background-position: top;
}

.button[disabled], input[type=submit][disabled], input[type=button][disabled] {
  background-image: url(../img/nav-bg.gif);
  background-position: bottom;
  opacity: 0.4;
}

.button.default, input[type=submit].default, .submit-row input.default {
    border: 2px solid #5b80b2;
    background: #7CA0C7 url(../img/default-bg.gif) bottom repeat-x;
    font-weight: bold;
    color: white;
    float: right;
}

.button.default:active, input[type=submit].default:active {
    background-image: url(../img/default-bg-reverse.gif);
    background-position: top;
}

.button[disabled].default, input[type=submit][disabled].default, input[type=button][disabled].default {
  background-image: url(../img/default-bg.gif);
  background-position: bottom;
  opacity: 0.4;
}


/* MODULES */

.module {
    border: 1px solid #ccc;
    margin-bottom: 5px;
    background: white;
}

.module p, .module ul, .module h3, .module h4, .module dl, .module pre {
    padding-left: 10px;
    padding-right: 10px;
}

.module blockquote {
    margin-left: 12px;
}

.module ul, .module ol {
    margin-left: 1.5em;
}

.module h3 {
    margin-top: .6em;
}

.module h2, .module caption, .inline-group h2 {
    margin: 0;
    padding: 2px 5px 3px 5px;
    font-size: 11px;
    text-align: left;
    font-weight: bold;
    background: #7CA0C7 url(../img/default-bg.gif) top left repeat-x;
    color: white;
}

.module table {
    border-collapse: collapse;
}

/* MESSAGES & ERRORS */

ul.messagelist {
    padding: 0 0 5px 0;
    margin: 0;
}

ul.messagelist li {
    font-size: 12px;
    display: block;
    padding: 4px 5px 4px 25px;
    margin: 0 0 3px 0;
    border-bottom: 1px solid #ddd;
    color: #666;
    background: #ffc url(../img/icon_success.gif) 5px .3em no-repeat;
}

ul.messagelist li.warning{
    background-image: url(../img/icon_alert.gif);
}

ul.messagelist li.error{
    background-image: url(../img/icon_error.gif);
}

.errornote {
    font-size: 12px !important;
    display: block;
    padding: 4px 5px 4px 25px;
    margin: 0 0 3px 0;
    border: 1px solid red;
    color: red;
    background: #ffc url(../img/icon_error.gif) 5px .3em no-repeat;
}

ul.errorlist {
    margin: 0 !important;
    padding: 0 !important;
}

.errorlist li {
    font-size: 12px !important;
    display: block;
    padding: 4px 5px 4px 25px;
    margin: 0 0 3px 0;
    border: 1px solid red;
    color: white;
    background: red url(../img/icon_alert.gif) 5px .3em no-repeat;
}

.errorlist li a {
  color: white;
    text-decoration: underline;
}

td ul.errorlist {
    margin: 0 !important;
    padding: 0 !important;
}

td ul.errorlist li {
    margin: 0 !important;
}

.errors {
    background: #ffc;
}

.errors input, .errors select, .errors textarea {
    border: 1px solid red;
}

div.system-message {
    background: #ffc;
    margin: 10px;
    padding: 6px 8px;
    font-size: .8em;
}

div.system-message p.system-message-title {
    padding: 4px 5px 4px 25px;
    margin: 0;
    color: red;
    background: #ffc url(../img/icon_error.gif) 5px .3em no-repeat;
}

.description {
    font-size: 12px;
    padding: 5px 0 0 12px;
}

/* BREADCRUMBS */

div.breadcrumbs {
    background: white url(../img/nav-bg-reverse.gif) 0 -10px repeat-x;
    padding: 2px 8px 3px 8px;
    font-size: 11px;
    color: #999;
    border-top: 1px solid white;
    border-bottom: 1px solid #ccc;
    text-align: left;
}

/* ACTION ICONS */

.addlink {
    padding-left: 12px;
    background: url(../img/icon_addlink.gif) 0 .2em no-repeat;
}

.changelink {
    padding-left: 12px;
    background: url(../img/icon_changelink.gif) 0 .2em no-repeat;
}

.deletelink {
    padding-left: 12px;
    background: url(../img/icon_deletelink.gif) 0 .25em no-repeat;
}

a.deletelink:link, a.deletelink:visited {
    color: #CC3434;
}

a.deletelink:hover {
    color: #993333;
}

/* OBJECT TOOLS */

.object-tools {
    font-size: 10px;
    font-weight: bold;
    font-family: Arial,Helvetica,sans-serif;
    padding-left: 0;
    float: right;
    position: relative;
    margin-top: -2.4em;
    margin-bottom: -2em;
}

.form-row .object-tools {
    margin-top: 5px;
    margin-bottom: 5px;
    float: none;
    height: 2em;
    padding-left: 3.5em;
}

.object-tools li {
    display: block;
    float: left;
    background: url(../img/tool-left.gif) 0 0 no-repeat;
    padding: 0 0 0 8px;
    margin-left: 2px;
    height: 16px;
}

.object-tools li:hover {
    background: url(../img/tool-left_over.gif) 0 0 no-repeat;
}

.object-tools a:link, .object-tools a:visited {
    display: block;
    float: left;
    color: white;
    padding: .1em 14px .1em 8px;
    height: 14px;
    background: #999 url(../img/tool-right.gif) 100% 0 no-repeat;
}

.object-tools a:hover, .object-tools li:hover a {
    background: #5b80b2 url(../img/tool-right_over.gif) 100% 0 no-repeat;
}

.object-tools a.viewsitelink, .object-tools a.golink {
    background: #999 url(../img/tooltag-arrowright.gif) top right no-repeat;
    padding-right: 28px;
}

.object-tools a.viewsitelink:hover, .object-tools a.golink:hover {
    background: #5b80b2 url(../img/tooltag-arrowright_over.gif) top right no-repeat;
}

.object-tools a.addlink {
    background: #999 url(../img/tooltag-add.gif) top right no-repeat;
    padding-right: 28px;
}

.object-tools a.addlink:hover {
    background: #5b80b2 url(../img/tooltag-add_over.gif) top right no-repeat;
}

/* OBJECT HISTORY */

table#change-history {
    width: 100%;
}

table#change-history tbody th {
    width: 16em;
}

/* PAGE STRUCTURE */

#container {
    position: relative;
    width: 100%;
    min-width: 760px;
    padding: 0;
}

#content {
    margin: 10px 15px;
}

#header {
    width: 100%;
}

#content-main {
    float: left;
    width: 100%;
}

#content-related {
    float: right;
    width: 18em;
    position: relative;
    margin-right: -19em;
}

#footer {
    clear: both;
    padding: 10px;
}

/* COLUMN TYPES */

.colMS {
    margin-right: 20em !important;
}

.colSM {
    margin-left: 20em !important;
}

.colSM #content-related {
    float: left;
    margin-right: 0;
    margin-left: -19em;
}

.colSM #content-main {
    float: right;
}

.popup .colM {
    width: 95%;
}

.subcol {
    float: left;
    width: 46%;
    margin-right: 15px;
}

.dashboard #content {
    width: 500px;
}

/* HEADER */

#header {
    background: #417690;
    color: #ffc;
    overflow: hidden;
}

#header a:link, #header a:visited {
    color: white;
}

#header a:hover {
    text-decoration: underline;
}

#branding h1 {
    padding: 0 10px;
    font-size: 18px;
    margin: 8px 0;
    font-weight: normal;
    color: #f4f379;
}

#branding h2 {
    padding: 0 10px;
    font-size: 14px;
    margin: -8px 0 8px 0;
    font-weight: normal;
    color: #ffc;
}

#user-tools {
    position: absolute;
    top: 0;
    right: 0;
    padding: 1.2em 10px;
    font-size: 11px;
    text-align: right;
}

/* SIDEBAR */

#content-related h3 {
    font-size: 12px;
    color: #666;
    margin-bottom: 3px;
}

#content-related h4 {
    font-size: 11px;
}

#content-related .module h2 {
    background: #eee url(../img/nav-bg.gif) bottom left repeat-x;
    color: #666;
}

tr.termin-status-4 td, tr.termin-status-4 td a {
    color: #d7cac3;
}