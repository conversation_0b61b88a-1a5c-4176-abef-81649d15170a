/* SELECTOR (FILTER INTERFACE) */

.selector {
    width: 580px;
    float: left;
}

.selector select {
    width: 270px;
    height: 17.2em;
}

.selector-available, .selector-chosen {
    float: left;
    width: 270px;
    text-align: center;
    margin-bottom: 5px;
}

.selector-chosen select {
    border-top: none;
}

.selector-available h2, .selector-chosen h2 {
    border: 1px solid #ccc;
}

.selector .selector-available h2 {
    background: white url(../img/nav-bg.gif) bottom left repeat-x;
    color: #666;
}

.selector .selector-filter {
    background: white;
    border: 1px solid #ccc;
    border-width: 0 1px;
    padding: 3px;
    color: #999;
    font-size: 10px;
    margin: 0;
    text-align: left;
}

.selector .selector-filter label,
.inline-group .aligned .selector .selector-filter label {
    width: 16px;
    padding: 2px;
}

.selector .selector-available input {
    width: 230px;
}

.selector ul.selector-chooser {
    float: left;
    width: 22px;
    height: 50px;
    background: url(../img/chooser-bg.gif) top center no-repeat;
    margin: 10em 5px 0 5px;
    padding: 0;
}

.selector-chooser li {
    margin: 0;
    padding: 3px;
    list-style-type: none;
}

.selector select {
    margin-bottom: 10px;
    margin-top: 0;
}

.selector-add, .selector-remove {
    width: 16px;
    height: 16px;
    display: block;
    text-indent: -3000px;
    overflow: hidden;
}

.selector-add {
    background: url(../img/selector-icons.gif) 0 -161px no-repeat;
    cursor: default;
    margin-bottom: 2px;
}

.active.selector-add {
    background: url(../img/selector-icons.gif) 0 -187px no-repeat;
    cursor: pointer;
}

.selector-remove {
    background: url(../img/selector-icons.gif) 0 -109px no-repeat;
    cursor: default;
}

.active.selector-remove {
    background: url(../img/selector-icons.gif) 0 -135px no-repeat;
    cursor: pointer;
}

a.selector-chooseall, a.selector-clearall {
    display: inline-block;
    text-align: left;
    margin-left: auto;
    margin-right: auto;
    font-weight: bold;
    color: #666;
}

a.selector-chooseall {
    padding: 3px 18px 3px 0;
}

a.selector-clearall {
    padding: 3px 0 3px 18px;
}

a.active.selector-chooseall:hover, a.active.selector-clearall:hover {
    color: #036;
}

a.selector-chooseall {
    background: url(../img/selector-icons.gif) right -263px no-repeat;
    cursor: default;
}

a.active.selector-chooseall {
    background: url(../img/selector-icons.gif) right -289px no-repeat;
    cursor: pointer;
}

a.selector-clearall {
    background: url(../img/selector-icons.gif) left -211px no-repeat;
    cursor: default;
}

a.active.selector-clearall {
    background: url(../img/selector-icons.gif) left -237px no-repeat;
    cursor: pointer;
}

/* STACKED SELECTORS */

.stacked {
    float: left;
    width: 500px;
}

.stacked select {
    width: 480px;
    height: 10.1em;
}

.stacked .selector-available, .stacked .selector-chosen {
    width: 480px;
}

.stacked .selector-available {
    margin-bottom: 0;
}

.stacked .selector-available input {
    width: 442px;
}

.stacked ul.selector-chooser {
    height: 22px;
    width: 50px;
    margin: 0 0 3px 40%;
    background: url(../img/chooser_stacked-bg.gif) top center no-repeat;
}

.stacked .selector-chooser li {
    float: left;
    padding: 3px 3px 3px 5px;
}

.stacked .selector-chooseall, .stacked .selector-clearall {
    display: none;
}

.stacked .selector-add {
    background: url(../img/selector-icons.gif) 0 -57px no-repeat;
    cursor: default;
}

.stacked .active.selector-add {
    background: url(../img/selector-icons.gif) 0 -83px no-repeat;
    cursor: pointer;
}

.stacked .selector-remove {
    background: url(../img/selector-icons.gif) 0 -5px no-repeat;
    cursor: default;
}

.stacked .active.selector-remove {
    background: url(../img/selector-icons.gif) 0 -31px no-repeat;
    cursor: pointer;
}

/* DATE AND TIME */

p.datetime {
    line-height: 20px;
    margin: 0;
    padding: 0;
    color: #666;
    font-size: 11px;
    font-weight: bold;
}

.datetime span {
    font-size: 11px;
    color: #ccc;
    font-weight: normal;
    white-space: nowrap;
}

table p.datetime {
    font-size: 10px;
    margin-left: 0;
    padding-left: 0;
}

/* URL */

p.url {
    line-height: 20px;
    margin: 0;
    padding: 0;
    color: #666;
    font-size: 11px;
    font-weight: bold;
}

.url a {
    font-weight: normal;
}

/* FILE UPLOADS */

p.file-upload {
    line-height: 20px;
    margin: 0;
    padding: 0;
    color: #666;
    font-size: 11px;
    font-weight: bold;
}

.file-upload a {
    font-weight: normal;
}

.file-upload .deletelink {
    margin-left: 5px;
}

span.clearable-file-input label {
    color: #333;
    font-size: 11px;
    display: inline;
    float: none;
}

/* CALENDARS & CLOCKS */

.calendarbox, .clockbox {
    margin: 5px auto;
    font-size: 11px;
    width: 16em;
    text-align: center;
    background: white;
    position: relative;
}

.clockbox {
    width: auto;
}

.calendar {
    margin: 0;
    padding: 0;
}

.calendar table {
    margin: 0;
    padding: 0;
    border-collapse: collapse;
    background: white;
    width: 100%;
}

.calendar caption, .calendarbox h2 {
    margin: 0;
    font-size: 11px;
    text-align: center;
    border-top: none;
}

.calendar th {
    font-size: 10px;
    color: #666;
    padding: 2px 3px;
    text-align: center;
    background: #e1e1e1 url(../img/nav-bg.gif) 0 50% repeat-x;
    border-bottom: 1px solid #ddd;
}

.calendar td {
    font-size: 11px;
    text-align: center;
    padding: 0;
    border-top: 1px solid #eee;
    border-bottom: none;
}

.calendar td.selected a {
    background: #C9DBED;
}

.calendar td.nonday {
    background: #efefef;
}

.calendar td.today a {
    background: #ffc;
}

.calendar td a, .timelist a {
    display: block;
    font-weight: bold;
    padding: 4px;
    text-decoration: none;
    color: #444;
}

.calendar td a:hover, .timelist a:hover {
    background: #5b80b2;
    color: white;
}

.calendar td a:active, .timelist a:active {
    background: #036;
    color: white;
}

.calendarnav {
    font-size: 10px;
    text-align: center;
    color: #ccc;
    margin: 0;
    padding: 1px 3px;
}

.calendarnav a:link, #calendarnav a:visited, #calendarnav a:hover {
    color: #999;
}

.calendar-shortcuts {
    background: white;
    font-size: 10px;
    line-height: 11px;
    border-top: 1px solid #eee;
    padding: 3px 0 4px;
    color: #ccc;
}

.calendarbox .calendarnav-previous, .calendarbox .calendarnav-next {
    display: block;
    position: absolute;
    font-weight: bold;
    font-size: 12px;
    background: #C9DBED url(../img/default-bg.gif) bottom left repeat-x;
    padding: 1px 4px 2px 4px;
    color: white;
}

.calendarnav-previous:hover, .calendarnav-next:hover {
    background: #036;
}

.calendarnav-previous {
    top: 0;
    left: 0;
}

.calendarnav-next {
    top: 0;
    right: 0;
}

.calendar-cancel {
    margin: 0 !important;
    padding: 0 !important;
    font-size: 10px;
    background: #e1e1e1 url(../img/nav-bg.gif) 0 50% repeat-x;
    border-top: 1px solid #ddd;
}

.calendar-cancel:hover {
    background: #e1e1e1 url(../img/nav-bg-reverse.gif) 0 50% repeat-x;
}

.calendar-cancel a {
    color: black;
    display: block;
}

ul.timelist, .timelist li {
    list-style-type: none;
    margin: 0;
    padding: 0;
}

.timelist a {
    padding: 2px;
}

/* INLINE ORDERER */

ul.orderer {
    position: relative;
    padding: 0 !important;
    margin: 0 !important;
    list-style-type: none;
}

ul.orderer li {
    list-style-type: none;
    display: block;
    padding: 0;
    margin: 0;
    border: 1px solid #bbb;
    border-width: 0 1px 1px 0;
    white-space: nowrap;
    overflow: hidden;
    background: #e2e2e2 url(../img/nav-bg-grabber.gif) repeat-y;
}

ul.orderer li:hover {
    cursor: move;
    background-color: #ddd;
}

ul.orderer li a.selector {
    margin-left: 12px;
    overflow: hidden;
    width: 83%;
    font-size: 10px !important;
    padding: 0.6em 0;
}

ul.orderer li a:link, ul.orderer li a:visited {
    color: #333;
}

ul.orderer li .inline-deletelink {
    position: absolute;
    right: 4px;
    margin-top: 0.6em;
}

ul.orderer li.selected {
    background-color: #f8f8f8;
    border-right-color: #f8f8f8;
}

ul.orderer li.deleted {
    background: #bbb url(../img/deleted-overlay.gif);
}

ul.orderer li.deleted a:link, ul.orderer li.deleted a:visited {
    color: #888;
}

ul.orderer li.deleted .inline-deletelink {
    background-image: url(../img/inline-restore.png);
}

ul.orderer li.deleted:hover, ul.orderer li.deleted a.selector:hover {
    cursor: default;
}

/* EDIT INLINE */

.inline-deletelink {
    float: right;
    text-indent: -9999px;
    background: transparent url(../img/inline-delete.png) no-repeat;
    width: 15px;
    height: 15px;
    border: 0px none;
    outline: 0; /* Remove dotted border around link */
}

.inline-deletelink:hover {
    background-position: -15px 0;
    cursor: pointer;
}

.editinline button.addlink {
    border: 0px none;
    color: #5b80b2;
    font-size: 100%;
    cursor: pointer;
}

.editinline button.addlink:hover {
    color: #036;
    cursor: pointer;
}

.editinline table .help {
    text-align: right;
    float: right;
    padding-left: 2em;
}

.editinline tfoot .addlink {
    white-space: nowrap;
}

.editinline table thead th:last-child {
    border-left: none;
}

.editinline tr.deleted {
    background: #ddd url(../img/deleted-overlay.gif);
}

.editinline tr.deleted .inline-deletelink {
    background-image: url(../img/inline-restore.png);
}

.editinline tr.deleted td:hover {
    cursor: default;
}

.editinline tr.deleted td:first-child {
    background-image: none !important;
}

/* EDIT INLINE - STACKED */

.editinline-stacked {
    min-width: 758px;
}

.editinline-stacked .inline-object {
    margin-left: 210px;
    background: white;
}

.editinline-stacked .inline-source {
    float: left;
    width: 200px;
    background: #f8f8f8;
}

.editinline-stacked .inline-splitter {
    float: left;
    width: 9px;
    background: #f8f8f8 url(../img/inline-splitter-bg.gif) 50% 50% no-repeat;
    border-right: 1px solid #ccc;
}

.editinline-stacked .controls {
    clear: both;
    background: #e1e1e1 url(../img/nav-bg.gif) top left repeat-x;
    padding: 3px 4px;
    font-size: 11px;
    border-top: 1px solid #ddd;
}

