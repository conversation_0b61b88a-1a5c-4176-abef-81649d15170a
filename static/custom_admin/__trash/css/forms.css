@import url('widgets.css');

/* FORM ROWS */

.form-row {
    overflow: hidden;
    padding: 8px 12px;
    font-size: 11px;
    border-bottom: 1px solid #eee;
}

.form-row img, .form-row input {
    vertical-align: middle;
}

form .form-row p {
    padding-left: 0;
    font-size: 11px;
}

/* FORM LABELS */

form h4 {
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
}

label {
    font-weight: normal !important;
    color: #666;
    font-size: 12px;
}

.required label, label.required {
    font-weight: bold !important;
    color: #333 !important;
}

/* RADIO BUTTONS */

form ul.radiolist li {
    list-style-type: none;
}

form ul.radiolist label {
    float: none;
    display: inline;
}

form ul.inline {
    margin-left: 0;
    padding: 0;
}

form ul.inline li {
    float: left;
    padding-right: 7px;
}

/* ALIGNED FIELDSETS */

.aligned label {
    display: block;
    padding: 3px 10px 0 0;
    float: left;
    width: 8em;
}

.aligned ul label {
    display: inline;
    float: none;
    width: auto;
}

.colMS .aligned .vLargeTextField, .colMS .aligned .vXMLLargeTextField {
    width: 350px;
}

form .aligned p, form .aligned ul {
    margin-left: 7em;
    padding-left: 30px;
}

form .aligned table p {
    margin-left: 0;
    padding-left: 0;
}

form .aligned p.help {
    padding-left: 38px;
}

.aligned .vCheckboxLabel {
    float: none !important;
    display: inline;
    padding-left: 4px;
}

.colM .aligned .vLargeTextField, .colM .aligned .vXMLLargeTextField {
    width: 610px;
}

.checkbox-row p.help {
    margin-left: 0;
    padding-left: 0 !important;
}

fieldset .field-box {
    float: left;
    margin-right: 20px;
}

/* WIDE FIELDSETS */

.wide label {
    width: 15em !important;
}

form .wide p {
    margin-left: 15em;
}

form .wide p.help {
    padding-left: 38px;
}

.colM fieldset.wide .vLargeTextField, .colM fieldset.wide .vXMLLargeTextField {
    width: 450px;
}

/* COLLAPSED FIELDSETS */

fieldset.collapsed * {
    display: none;
}

fieldset.collapsed h2, fieldset.collapsed {
    display: block !important;
}

fieldset.collapsed h2 {
    background-image: url(../img/nav-bg.gif);
    background-position: bottom left;
    color: #999;
}

fieldset.collapsed .collapse-toggle {
    background: transparent;
    display: inline !important;
}

/* MONOSPACE TEXTAREAS */

fieldset.monospace textarea {
    font-family: "Bitstream Vera Sans Mono",Monaco,"Courier New",Courier,monospace;
}

/* SUBMIT ROW */

.submit-row {
    padding: 5px 7px;
    text-align: right;
    background: white url(../img/nav-bg.gif) 0 100% repeat-x;
    border: 1px solid #ccc;
    margin: 5px 0;
    overflow: hidden;
}

body.popup .submit-row {
    overflow: auto;
}

.submit-row input {
    margin: 0 0 0 5px;
}

.submit-row p {
    margin: 0.3em;
}

.submit-row p.deletelink-box {
    float: left;
}

.submit-row .deletelink {
    background: url(../img/icon_deletelink.gif) 0 50% no-repeat;
    padding-left: 14px;
}

/* CUSTOM FORM FIELDS */

.vSelectMultipleField {
    vertical-align: top !important;
}

.vCheckboxField {
    border: none;
}

.vDateField, .vTimeField {
    margin-right: 2px;
}

.vURLField {
    width: 30em;
}

.vLargeTextField, .vXMLLargeTextField {
    width: 48em;
}

.flatpages-flatpage #id_content {
    height: 40.2em;
}

.module table .vPositiveSmallIntegerField {
    width: 2.2em;
}

.vTextField {
    width: 20em;
}

.vIntegerField {
    width: 5em;
}

.vBigIntegerField {
    width: 10em;
}

.vForeignKeyRawIdAdminField {
    width: 5em;
}

/* INLINES */

.inline-group {
    padding: 0;
    border: 1px solid #ccc;
    margin: 10px 0;
}

.inline-group .aligned label {
    width: 8em;
}

.inline-related {
    position: relative;
}

.inline-related h3 {
    margin: 0;
    color: #666;
    padding: 3px 5px;
    font-size: 11px;
    background: #e1e1e1 url(../img/nav-bg.gif) top left repeat-x;
    border-bottom: 1px solid #ddd;
}

.inline-related h3 span.delete {
    float: right;
}

.inline-related h3 span.delete label {
    margin-left: 2px;
    font-size: 11px;
}

.inline-related fieldset {
    margin: 0;
    background: #fff;
    border: none;
    width: 100%;
}

.inline-related fieldset.module h3 {
    margin: 0;
    padding: 2px 5px 3px 5px;
    font-size: 11px;
    text-align: left;
    font-weight: bold;
    background: #bcd;
    color: #fff;
}

.inline-group .tabular fieldset.module {
    border: none;
    border-bottom: 1px solid #ddd;
}

.inline-related.tabular fieldset.module table {
    width: 100%;
}

.last-related fieldset {
    border: none;
}

.inline-group .tabular tr.has_original td {
    padding-top: 2em;
}

.inline-group .tabular tr td.original {
    padding: 2px 0 0 0;
    width: 0;
    _position: relative;
}

.inline-group .tabular th.original {
    width: 0px;
    padding: 0;
}

.inline-group .tabular td.original p {
    position: absolute;
    left: 0;
    height: 1.1em;
    padding: 2px 7px;
    overflow: hidden;
    font-size: 9px;
    font-weight: bold;
    color: #666;
    _width: 700px;
}

.inline-group ul.tools {
    padding: 0;
    margin: 0;
    list-style: none;
}

.inline-group ul.tools li {
    display: inline;
    padding: 0 5px;
}

.inline-group div.add-row,
.inline-group .tabular tr.add-row td {
    color: #666;
    padding: 3px 5px;
    border-bottom: 1px solid #ddd;
    background: #e1e1e1 url(../img/nav-bg.gif) top left repeat-x;
}

.inline-group .tabular tr.add-row td {
    padding: 4px 5px 3px;
    border-bottom: none;
}

.inline-group ul.tools a.add,
.inline-group div.add-row a,
.inline-group .tabular tr.add-row td a {
    background: url(../img/icon_addlink.gif) 0 50% no-repeat;
    padding-left: 14px;
    font-size: 11px;
    outline: 0; /* Remove dotted border around link */
}

.empty-form {
    display: none;
}
