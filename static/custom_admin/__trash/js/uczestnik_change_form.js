(function($) {
    $(document).ready(function($) {
        var termin = $('select#id_termin');

        termin.change(function () {
          var chce_autoryzacje_label = $('label[for="id_chce_autoryzacje"]');
          var autoryzacja = $("option:selected", this).data("autoryzacja");
          var text_help = '<p class="help help-autoryzacja">UWAGA: To szkolenie nie ma opcji autoryzacji.</p>';

          $('p.help-autoryzacja').remove();

          if (typeof autoryzacja != 'undefined') {
              if (parseInt(autoryzacja)) {
                  chce_autoryzacje_label.css({opacity: 1.0});
              } else {
                  chce_autoryzacje_label.css({opacity: 0.5});
                  $(text_help).insertAfter(chce_autoryzacje_label);
              }
          } else {
              chce_autoryzacje_label.css({opacity: 1.0});
          }
        });

        termin.change();
    });
})(django.jQuery);
