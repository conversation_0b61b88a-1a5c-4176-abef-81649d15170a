/* Dodatkowe */
.simplemodal-wrap {
	font-weight: 600;
    font-family: Open Sans, Arial, sans-serif;
	letter-spacing: -0.5px;
	}
	
#simplemodal-container {
	padding: 0px !important;
	border: 0 !important;
	}

#simplemodal-container .simplemodal-data {
	padding: 0px !important;
	}


#simplemodal-container a.modalCloseImg {
    position: absolute;
    top: 42px !important;
    right: 19px !important;
	}
	
	
	

/* Podstawowe */
.header-bg {
	width: 100%;
	padding: 30px; 
	box-sizing: border-box;
	background: #f2f2f2;
}

.morph-content h3 {
	margin: 0;
	padding: 0;
	font-family: 'Open Sans', sans-serif;
	font-size: 21px;
	font-weight: 600;
	line-height: 21px;
	color: #1f2528;
	letter-spacing: -1px;
}

.modal-content {
	width: 100%;
	height: auto;
	padding: 30px;
	box-sizing: border-box;
	font-family: Arial, sans-serif;
	font-size: 13px;
	color: #5c5c5c;
	line-height: 17px;
}

.modal-content p {
	margin: 0;
	padding: 0;
}

.modal-content form {
	width: 100%;
	margin: 35px 0 0 0;
}

.modal-content .city  {
	float: left;
	width: 115px;
	margin-bottom: 20px;
}

.modal-content .city:nth-child(5), .modal-content .city:nth-child(6), .modal-content .city:nth-child(7), .modal-content .city:nth-child(8){
	margin-bottom: 30px;
}

.modal-content .city input {
	display: inline-block;
	position: relative;
	top: 2px;
}

.modal-content .city label {
	display: inline-block;
	width: 84px !important; 
	color: #5c5c5c;
	font-weight: normal;
}

.modal-content .your-email {
	margin-bottom: 25px;
}

.modal-content .your-email input {
	width: 230px;
	margin: 0 0 0 5px;
	font-family: Arial, sans-serif;
	font-size: 13px;
	color: #818181;
	padding: 10px 8px;
	border: 1px solid #bfbfbf;
}

.modal-content .your-email input:focus {
	outline: none;
}

.modal-content .your-email, .modal-content .buttons {
	display: block;
	clear: both; 
}

.buttons p {
    margin-top: 25px;
	font-size: 11px;
    line-height: 1.3em;
	}

 .modal-content .buttons .btn:first-child {
	margin-right: 4px;
}

.btn {
	display: inline-block;
	margin: 0;
	padding: 16px 39px;
	font-family: 'Open Sans', sans-serif;
	font-size: 14px;
	font-weight: 600;
	line-height: 14px;
	letter-spacing: -0.5px;
	text-transform: uppercase;
	text-decoration: none;
	border: 0;
}

.btn-red {
	background: #b12222;
	color: #fff;
}

.btn-red:hover {
	background: #a21212;
}


.btn-gray {
	padding: 15px 38px;
	background: #d5d5d5;
	color: #666 !important;
	border: 1px solid #cbcbcb;
}

.btn-gray:hover {
	background: #b8b8b8;
}

input.btn-red[type='submit'] {
	display: inline-block;
	margin: 0 !important;
	padding: 16px 39px !important;
	font-family: 'Open Sans', sans-serif !important;
	font-size: 14px !important;
	font-weight: 600 !important;
	line-height: 14px !important;
	letter-spacing: -0.5px !important;
	text-transform: uppercase !important;
	text-decoration: none !important;
	border: 0 !important;
	background: #b12222 !important;
	color: #fff !important;
	-webkit-border-radius: 0px !important;
    -moz-border-radius: 0px !important;
    border-radius: 0px !important;
	}


/* Style for modal */
.content-style-form {
	position: relative;
	text-align: left;
}

.content-style-form .icon-close {
	position: absolute;
	top: 32px;
	right: 30px;
	z-index: 3000;
	display: none;
	width: 14px;
	height: 14px;
	text-indent: -3000px;
	background: url(../img/close.png) no-repeat; 
}

.content-style-form .icon-close:hover{
	cursor: pointer;
}

.js .content-style-form-1 h3,
.js .content-style-form-1 p,
.js .modal-content form,
.js .content-style-form-1 .icon-close {
	opacity: 0;
	-webkit-transition: opacity 0.2s 0.35s, -webkit-transform 0.2s 0.35s;
	transition: opacity 0.2s 0.35s, transform 0.2s 0.35s;
	-webkit-transform: scale(0.85);
	transform: scale(0.85);
}

.morph-button.open .content-style-form-1 h3,
.morph-button.open .content-style-form-1 p,
.morph-button.open .modal-content form,
.morph-button.open .content-style-form-1 .icon-close {
	opacity: 1;
	-webkit-transform: scale(1);
	transform: scale(1);
}

/* Morph Button: Default Styles */
.morph-button {
	position: relative;
	display: block;
}

.morph-button > button {
	position: relative;
	width: 214px;
	padding: 17px 0;
	font-family: 'Open Sans', sans-serif;
	font-size: 14px;
	letter-spacing: -0.1px;
	font-weight: 600;
	border: none;
	overflow: hidden;
}

/*
.morph-button.open > button {
	pointer-events: none;
}

.morph-content {
	pointer-events: none;
}

.morph-button.open .morph-content {
	pointer-events: auto;
}
*/

/* Common styles for overlay and modal type (fixed morph) */
.morph-button-fixed,
.morph-button-fixed .morph-content {
	width: 214px;
	height: 53px;
}

.morph-button-fixed > button {
	z-index: 1000;
	-webkit-transition: opacity 0.1s 0.5s;
	transition: opacity 0.1s 0.5s;
}

.morph-button-fixed.open > button {
	opacity: 0;
	-webkit-transition: opacity 0.1s;
	transition: opacity 0.1s;
}

.morph-button-fixed .morph-content {
	position: fixed;
	z-index: 900;
	opacity: 0;
	-webkit-transition: opacity 0.3s 0.5s, width 0.4s 0.1s, height 0.4s 0.1s, top 0.4s 0.1s, left 0.4s 0.1s, margin 0.4s 0.1s;
	transition: opacity 0.3s 0.5s, width 0.4s 0.1s, height 0.4s 0.1s, top 0.4s 0.1s, left 0.4s 0.1s, margin 0.4s 0.1s;
}

.morph-button-fixed.open .morph-content {
	opacity: 1;
}

.morph-button-fixed .morph-content > div {
	visibility: hidden;
	height: 0;
	opacity: 0;
	-webkit-transition: opacity 0.1s, visibility 0s 0.1s, height 0s 0.1s;
	transition: opacity 0.1s, visibility 0s 0.1s, height 0s 0.1s;
}

.morph-button-fixed.open .morph-content > div {
	visibility: visible;
	height: auto;
	opacity: 1;
	-webkit-transition: opacity 0.3s 0.5s;
	transition: opacity 0.3s 0.5s;
}

.morph-button-fixed.active > button {
	z-index: 2000;
}

.morph-button-fixed.active .morph-content {
	z-index: 1900;
}

/* Morph Button Style: Modal */
.morph-button-modal::before {
	position: fixed;
	top: 0;
	left: 0;
	z-index: 800;
	width: 100%;
	height: 100%;
	background: rgba(0,0,0,0.9);
	content: '';
	opacity: 0;
	-webkit-transition: opacity 0.5s;
	transition: opacity 0.5s;
	pointer-events: none;
}

.morph-button-modal.open::before {
	opacity: 1;
	pointer-events: auto;
}

.morph-button-modal.active::before {
	z-index: 1800;
}

.morph-button-modal .morph-content {
	overflow: hidden;
	-webkit-transition: opacity 0.3s 0.5s, width 0.4s 0.1s, height 0.4s 0.1s, top 0.4s 0.1s, left 0.4s 0.1s, margin 0.4s 0.1s;
	transition: opacity 0.3s 0.5s, width 0.4s 0.1s, height 0.4s 0.1s, top 0.4s 0.1s, left 0.4s 0.1s, margin 0.4s 0.1s;
}

.morph-button-modal.open .morph-content {
	top: 50% !important;
	left: 50% !important;
	margin: -220px 0 0 -283px;
	width: 566px;
	height: 439px;
	-webkit-transition: width 0.4s 0.1s, height 0.4s 0.1s, top 0.4s 0.1s, left 0.4s 0.1s, margin 0.4s 0.1s;
	transition: width 0.4s 0.1s, height 0.4s 0.1s, top 0.4s 0.1s, left 0.4s 0.1s, margin 0.4s 0.1s;
}

/* Colors and sizes for individual modals*/
.morph-button-modal-2 > button {
	background-color: #d5d5d5;
	color: #666;
}

.morph-button-modal-2 .morph-content {
	background-color: #fff;
	color: #5c5c5c;
}

.morph-button-modal-2.open .morph-content {
	margin: -220px 0 0 -283px;
	width: 566px;
	height: 439px;
}

.morph-button-modal-2.open .morph-content > div {
 	-webkit-transition: opacity 0.3s 0.3s;
	transition: opacity 0.3s 0.3s;
}

/* Easing */
.morph-button .morph-content,
.morph-button.open .morph-content {
	-webkit-transition-timing-function: cubic-bezier(0.7,0,0.3,1);
	transition-timing-function: cubic-bezier(0.7,0,0.3,1);
}

/* No JS */
.no-js .morph-button > button {
	display: none;
}

.no-js .morph-button {
	float: none;
}

.no-js .morph-button,
.no-js .morph-button .morph-content,
.no-js .morph-button .morph-content > div {
	position: relative;
	width: auto;
	height: auto;
	opacity: 1;
	visibility: visible;
	top: auto;
	left: auto;
	-webkit-transform: none;
	transform: none;
	pointer-events: auto;
}

.no-js .morph-button .morph-content .icon-close {
	display: none;
}

.no-transition {
	-webkit-transition: none !important;
	transition: none !important;
}

/* Media Queries */
@media screen and (max-width: 600px) {
	.morph-button-modal.open .morph-content {
		top: 0% !important;
		left: 0% !important;
		margin: 0;
		width: 100%;
		height: 100%;
		overflow-y: scroll;
		-webkit-transition: width 0.4s 0.1s, height 0.4s 0.1s, top 0.4s 0.1s, left 0.4s 0.1s;
		transition: width 0.4s 0.1s, height 0.4s 0.1s, top 0.4s 0.1s, left 0.4s 0.1s;
	}
}

@media screen and (max-width: 400px) {
	.morph-button-fixed,
	.morph-button-fixed .morph-content {
		width: 200px;
		height: 80px;
	}

	.morph-button-fixed > button {
		font-size: 75%;
	}

	.morph-button-sidebar > button {
		font-size: 1.6em;
	}

	.morph-button-inflow .morph-content .morph-clone {
		font-size: 0.9em;
	}

	.morph-button-modal-4,
	.morph-button-modal-4 .morph-content {
		width: 220px;
		height: 120px;
	}

	.morph-button-modal-4 > button {
		font-size: 100%;
		line-height: 50px;
	}

	.morph-button-modal-4 > button span {
		display: block;
	}

	.morph-button-modal-4 .morph-clone {
		right: 83px;
		bottom: 26px;
	}

	.morph-button-sidebar,
	.morph-button-sidebar .morph-content {
		width: 100% !important;
		height: 60px !important;
	}

	.morph-button-sidebar {
		bottom: 0px;
		left: 0px;
	}

	.morph-button-sidebar.open .morph-content {
		height: 100% !important;
	}
}











