import copy
import datetime
import io
import json
import logging
import mimetypes
import os
import re
from collections import OrderedDict
from urllib.parse import urlencode

import Lev<PERSON>htein
from dateutil import parser
from django.conf import settings
from django.contrib import messages
from django.contrib.auth.decorators import permission_required, user_passes_test
from django.core import signing
from django.core.cache import cache
from django.core.exceptions import ValidationError
from django.core.mail import EmailMessage, send_mail
from django.db.models import Count, Prefetch, Q
from django.forms.models import modelformset_factory
from django.http import (
    Http404,
    HttpResponse,
    HttpResponsePermanentRedirect,
    HttpResponseRedirect,
    JsonResponse,
)
from django.shortcuts import get_object_or_404, redirect, render
from django.template.loader import render_to_string
from django.urls import reverse
from django.utils import translation
from django.utils.formats import number_format
from django.utils.html import strip_tags
from django.utils.text import Truncator
from django.utils.translation import ugettext as _
from django.views.decorators.cache import never_cache
from django.views.decorators.http import etag, require_http_methods
from ipware import get_client_ip

import www.tasks
from common.gchart import gchart
from i18n.models import WersjaJezykowa
from i18n.utils import pretty_number
from newsletter.models import Odbiorca
from newsletter.utils import random_string
from www.forms import (
    CaptchaContactForm,
    EmailCertificate,
    EmailForm,
    InvisibleCaptchaContactForm,
    PropozycjaTerminuForm,
    RegistrationLinkForm,
    SubscriptionForm,
    UczestnikPlikForm,
    UserNotificationPersonalDataForm,
    ZamknieteZgloszenieForm,
    ZgloszeniePolskieForm,
    ZgloszeniePolskieFormNoValidation,
    ZgloszenieZagraniczneForm,
    get_manage_subscription_form,
)
from www.models import (
    ZAPLACE_CHOICES,
    Certyfikat,
    ContentGroup,
    ContinuationUnsubscribed,
    FakturaWysylka,
    Graduate,
    GraduateStory,
    Highlight,
    Lokalizacja,
    MyFlatPage,
    PotencjalnyChetny,
    Prowadzacy,
    Referencja,
    Sciezka,
    Szkolenie,
    SzkolenieWariant,
    TagDlugosc,
    TagTechnologia,
    TagZawod,
    TerminSzkolenia,
    Testimonial,
    Uczestnik,
    UczestnikPlik,
    UserCoursesNotification,
    UserNotification,
    Zgloszenie,
)
from www.utils import file_to_response, get_email

logger = logging.getLogger(__name__)


def custom_404(request, exception=None):
    if re.match("^/en/", request.get_full_path()) or re.search(
            "\.training(:\d+)?$", request.get_host()
    ):
        language = "en"
    else:
        language = "pl"
    translation.activate(language)

    response = render(request, "404.html", {})
    response.status_code = 404
    try:
        return response
    finally:
        if language != settings.DEFAULT_LANGUAGE:
            translation.activate(settings.DEFAULT_LANGUAGE)


def activate_translation(view_function):
    def decorated_view_function(request, *args, **kwargs):
        # Mamy dwa języki obsługiwane na jednej instancji serwera, więc po
        # zakończeniu zapytania chcemy przywrócić polski.
        language = kwargs.get("language", settings.DEFAULT_LANGUAGE)
        translation.activate(language)

        if hasattr(request, "session"):
            request.session["language"] = language

        try:
            return view_function(request, *args, **kwargs)
        finally:
            if language != settings.DEFAULT_LANGUAGE:
                translation.activate(settings.DEFAULT_LANGUAGE)

    decorated_view_function.__name__ = view_function.__name__
    return decorated_view_function


def get_highlights(obj):
    highlights = obj.highlights.all()
    if highlights and all([not x.initially_selected for x in highlights]):
        highlights[0].initially_selected = True
    return highlights


def get_najblizsze(language=None):
    najblizsze = TerminSzkolenia.objects.filter(
        termin__gte=datetime.date.today(),
        zamkniete=False,
        szkolenie__aktywne=True,
        lokalizacja__panstwo__in=WersjaJezykowa.biezaca().wyswietlane_panstwa.all(),
    ).order_by("termin")
    if language:
        najblizsze = najblizsze.filter(szkolenie__language=language)
    return najblizsze


@activate_translation
def frontpage(request, language=settings.DEFAULT_LANGUAGE):
    if not any([x[0] == language for x in settings.LANGUAGES]):
        raise Http404
    if language == settings.DEFAULT_LANGUAGE and request.path != "/":
        return HttpResponseRedirect("/")

    tech = TagTechnologia.objects.filter(
        domena__url=request.get_host(), language=language
    )
    if tech:
        return index_by_technologia(request, tech[0].slug, root=True)

    najblizsze = get_najblizsze(language)[:6]
    lokalizacje = Lokalizacja.objects.filter(reklamowana=True)
    tlumaczenia = [x for x in [("pl", "/"), ("en", "/en/")] if x[0] != language]
    page = MyFlatPage.objects.get(slug="frontpage", language=language)
    host = request.get_host() or ""

    if host == "frontpage-akademia.linuksa.pl":
        template = "www/frontpage-akademia.linuksa.pl.html"
    else:
        template = "www/frontpage.html"

    schema = {
        "@context": "https://schema.org",
        "@type": "Organization",
        "@id": "https://www.alx.pl/#org",
        "logo": "https://www.alx.pl/media/logo-alx.png",
        "name": "ALX",
        "legalName": "ALX Academy Sp. z o.o.",
        "sameAs": [
            "http://www.facebook.com/alxeu",
            "https://www.instagram.com/alx_szkola_it/",
            "https://www.linkedin.com/school/alx-academy/",
            "https://www.youtube.com/channel/UCBAXV7fbddsB5jsvqNxyFkw"
        ],
        "url": "https://www.alx.pl/",
        "telephone": "+48226364164",
        "email": "<EMAIL>",
        "faxNumber": "+48222660695",
        "address": {
            "@type": "PostalAddress",
            "addressCountry": "PL",
            "streetAddress": "Jasna 14/16A",
            "addressLocality": "Warszawa",
            "postalCode": "00-041"
        }
    }

    schema_json = json.dumps(schema, indent=4)

    return render(
        request,
        template,
        {
            "najblizsze": najblizsze if language == "en" else None,
            "lokalizacje": lokalizacje if language == "en" else None,
            "tlumaczenia": tlumaczenia,
            "page": page,
            "highlights": get_highlights(page) if language == "en" else None,
            "frontpage": True,
            "years_delta": datetime.date.today().year - 2002,
            "schema_json": schema_json,
        },
    )


@never_cache
@activate_translation
def index(request, language=settings.DEFAULT_LANGUAGE):
    # Attempt to get cached lista_szkolen
    lista_szkolen = cache.get(f"lista_szkolen_{language}")
    tagi = TagTechnologia.objects.filter(widoczny_publicznie=True).order_by("ordering")

    szkolenia_grouped = {}
    for tag in tagi:
        szkolenia_grouped[tag.nazwa] = []

    lista_szkolen = Szkolenie.objects.filter(
        aktywne=True, tag_dlugosc__slug="szkolenie", language=language
    ).prefetch_related("tagi_technologia")

    for szkolenie in lista_szkolen:
        for tag in szkolenie.tagi_technologia.all():
            if tag.widoczny_publicznie:
                szkolenia_grouped[tag.nazwa].append(szkolenie)

    szkolenia_grouped = {k: v for k, v in szkolenia_grouped.items() if v}

    # Fetch kursy_zawodowe with a single database query
    kursy_zawodowe = Szkolenie.objects.filter(
        aktywne=True, tag_dlugosc__slug="kurs-zawodowy", language=language
    )

    return render(
        request,
        "www/index.html",
        {
            "lista_szkolen": dict(szkolenia_grouped),
            "kursy_zawodowe": kursy_zawodowe,
            "taby": {},
        },
    )


def detail_etag(
        request,
        slug,
        pdf=False,
        custom_oferta=False,
        tab_slug=None,
        language=settings.DEFAULT_LANGUAGE,
):
    return Szkolenie.objects.get_etag(slug=slug, language=language)


@etag(detail_etag)
@activate_translation
def detail(
        request,
        slug,
        pdf=False,
        custom_oferta=False,
        tab_slug=False,
        language=settings.DEFAULT_LANGUAGE,
):
    if slug:
        wybrany_wpis = get_object_or_404(
            Szkolenie, slug=slug, aktywne=True, language=language
        )
    else:
        raise Http404

    taby_pl = ["opis", "cennik", "terminy", "na-zamowienie", "konsultacje"]
    taby_en = ["overview", "prices-and-discounts", "schedules", "on-demand", "consulting"]

    # pozwalamy w adresie tylko na zdefiniowane taby
    if tab_slug and ((language == "pl" and tab_slug not in taby_pl) or (language == "en" and tab_slug not in taby_en)):
        raise Http404

    if (
            wybrany_wpis.strona_z_opisem_slug
            and not (pdf or custom_oferta)
    ):
        return HttpResponsePermanentRedirect(wybrany_wpis.get_absolute_url())

    request.content_group = wybrany_wpis.content_group

    tagi_technologia = wybrany_wpis.tagi_technologia.all()
    terminy = (
        wybrany_wpis.terminy()
        .select_related("szkolenie__tag_dlugosc")
        .annotate(liczba_terminow_podrzednych=Count("nadrzedne"))
    )
    terminy_z_harmonogramami = [x for x in terminy if x.harmonogram]

    taby = {
        "pl": [
            {"id": "opis", "slug": "opis", "title": "Opis szkolenia", "selected": True},
            {"id": "cennik", "slug": "cennik", "title": "Ceny i rabaty"},
            {"id": "terminy-lokalizacje", "slug": "terminy", "title": "Terminy"},
            {"id": "na-zamowienie", "slug": "na-zamowienie", "title": "Na zamówienie"},
            {"id": "konsultacje", "slug": "konsultacje", "title": "Konsultacje"},
        ],
        "en": [
            {"id": "opis", "slug": "overview", "title": "Overview", "selected": True},
            {
                "id": "cennik",
                "slug": "prices-and-discounts",
                "title": "Prices and discounts",
            },
            {"id": "terminy-lokalizacje", "slug": "schedules", "title": "Schedules"},
            {"id": "na-zamowienie", "slug": "on-demand", "title": "On demand"},
            {"id": "konsultacje", "slug": "consulting", "title": "Consulting"},
        ],
    }[language]

    pole_informacyjne = MyFlatPage.objects.filter(
        slug="pole-informacyjne-pod-terminami-{}-{}".format(
            wybrany_wpis.tag_dlugosc.slug, wybrany_wpis.language
        )
    ).first()
    pole_informacyjne = pole_informacyjne.content if pole_informacyjne else None

    active_tab = taby[0]

    if tab_slug:
        for tab in taby:
            if tab_slug == tab["slug"]:
                active_tab = tab

    kwargs = {
        "context": {
            "szkolenie": wybrany_wpis,
            "tlumaczenia": [
                (x.language, x.get_absolute_url())
                for x in wybrany_wpis.tlumaczenia()
                if x.aktywne
            ],
            "tagi_technologia": tagi_technologia,
            "terminy": terminy,
            "terminy_z_harmonogramami": terminy_z_harmonogramami,
            "logo": wybrany_wpis.logo.plik if wybrany_wpis.logo else None,
            "taby": taby,
            "pokazuj_notyfikacje": True,
            "pokazuj_box_z_terminami": True,
            "pokazuj_probki_materialow": False,
            "pokazuj_baner_o_bootcamp": False,
            "pokazuj_video_o_szkoleniach": False,
            "pole_informacyjne": pole_informacyjne,
            "tab_as_hyperlink": True,
            "active_tab": active_tab,
            "is_a_first_tab": active_tab["id"] == "opis",
            "base_tab_url": wybrany_wpis.get_absolute_url(),
            "non_indexable_tab": active_tab["id"] != "opis",
        },
    }

    schema_json = get_schema(request, wybrany_wpis)
    kwargs["context"]["schema_json"] = schema_json

    if not pdf and not custom_oferta:
        return render(request, "www/detail.html", **kwargs)
    if custom_oferta:
        kwargs["content_type"] = "application/msword"
        return render(request, "www/detail_custom_oferta.html", **kwargs)
    if pdf:
        from reportlab.lib import enums
        from reportlab.lib.styles import getSampleStyleSheet
        from reportlab.lib.units import cm
        from reportlab.pdfbase import pdfmetrics
        from reportlab.pdfbase.ttfonts import TTFont
        from reportlab.platypus import (
            Image as PlatypusImage,
            Paragraph,
            SimpleDocTemplate,
        )
        from reportlab.rl_config import defaultPageSize

        PAGE_HEIGHT = defaultPageSize[1]
        PAGE_WIDTH = defaultPageSize[0]

        styles = getSampleStyleSheet()
        para_style = copy.copy(styles["Normal"])
        para_style.fontSize = 11
        para_style.fontName = "DejaVuSerif"
        para_style.alignment = enums.TA_JUSTIFY
        para_style.autoLeading = "max"
        para_style.spaceBefore = 2

        def my_page(canvas, doc):
            canvas.saveState()
            head_width = PAGE_WIDTH - 2.5 * cm
            head_image = os.path.join(settings.PDF_HELPERS_PATH, "alx_head.png")
            if language != settings.DEFAULT_LANGUAGE:
                translated_head_image = os.path.join(
                    settings.PDF_HELPERS_PATH, "alx_head_{0}.png".format(language)
                )
                if os.path.exists(translated_head_image):
                    head_image = translated_head_image
            head = PlatypusImage(head_image, head_width, head_width / (1751.0 / 180.0))
            head.drawOn(canvas, 1.1 * cm, PAGE_HEIGHT - 3 * cm)
            canvas.setFont("DejaVuSans", 8)
            canvas.drawCentredString(
                PAGE_WIDTH / 2.0, 36, _("ul. Jasna 14/16A, 00-041 Warszawa")
            )
            canvas.drawCentredString(
                PAGE_WIDTH / 2.0, 26, _("tel. 22 63 64 164, fax 22 266 06 95")
            )
            canvas.drawCentredString(
                PAGE_WIDTH / 2.0,
                16,
                _("<EMAIL>") + ", " + _("http://www.alx.pl"),
            )

            rightpane = PAGE_WIDTH - 4.8 * cm

            canvas.setFont("DejaVuSansBold", 10)
            canvas.drawString(rightpane, PAGE_HEIGHT - 5 * cm, _("Zapytaj o szczegóły"))
            canvas.setFont("DejaVuSans", 9)
            canvas.drawString(
                rightpane, PAGE_HEIGHT - 5 * cm - 14, _("tel. 22 63 64 164")
            )
            canvas.drawString(
                rightpane, PAGE_HEIGHT - 5 * cm - 26, _("<EMAIL>")
            )
            if terminy:
                canvas.setFont("DejaVuSansBold", 10)
                canvas.drawString(
                    rightpane, PAGE_HEIGHT - 9 * cm, _("Najbliższe terminy")
                )
                canvas.setFont("DejaVuSans", 9)
                for i, t in zip(list(range(len(terminy))), terminy):
                    canvas.drawString(
                        rightpane,
                        PAGE_HEIGHT - 9 * cm - 14 - 12 * i,
                        "%s (%s)"
                        % (t.termin.strftime("%Y-%m-%d"), t.lokalizacja.shortname),
                    )
            canvas.restoreState()

        pdfmetrics.registerFont(
            TTFont(
                "DejaVuSerif", os.path.join(settings.PDF_HELPERS_PATH, "georgia.ttf")
            )
        )
        pdfmetrics.registerFont(
            TTFont(
                "DejaVuSerifItalic",
                os.path.join(settings.PDF_HELPERS_PATH, "georgiai.ttf"),
            )
        )
        pdfmetrics.registerFont(
            TTFont(
                "DejaVuSerifBold",
                os.path.join(settings.PDF_HELPERS_PATH, "georgiab.ttf"),
            )
        )
        pdfmetrics.registerFont(
            TTFont(
                "DejaVuSerifBoldItalic",
                os.path.join(settings.PDF_HELPERS_PATH, "georgiaz.ttf"),
            )
        )
        pdfmetrics.registerFont(
            TTFont("DejaVuSans", os.path.join(settings.PDF_HELPERS_PATH, "verdana.ttf"))
        )
        pdfmetrics.registerFont(
            TTFont(
                "DejaVuSansBold",
                os.path.join(settings.PDF_HELPERS_PATH, "verdanab.ttf"),
            )
        )

        pdfmetrics.registerFontFamily(
            "DejaVuSeri",
            normal="DejaVuSerif",
            bold="DejaVuSerifBold",
            italic="DejaVuSerifItalic",
            boldItalic="DejaVuSerifBoldItalic",
        )

        paras = []
        output = io.BytesIO()
        doc = SimpleDocTemplate(
            output,
            showBoundary=False,
            leftMargin=1 * cm,
            rightMargin=5.5 * cm,
            topMargin=3 * cm,
            bottomMargin=3 * cm,
        )

        # templejtów od pdf-ów jeszcze nie zmerge'owałem i teraz nie mam pory
        html = render_to_string(
            (
                "www/detail_pdf.en.platypus"
                if language == "en"
                else "www/detail_pdf.platypus"
            ),
            **kwargs,
        )
        html = re.sub(
            r"(?m)^!\s*(.*?)$",
            r'<font size="12" name="DejaVuSansBold">\1</font><br/>',
            html,
        )
        html = re.sub(
            r"(?m)^\*\s*",
            r'</para><para spaceBefore="0" leftIndent="16"><bullet>—</bullet>',
            html,
        )
        html = re.sub(
            r"(?m)^#\*\*\s*",
            r'</para><para spaceBefore="0" leftIndent="48" bulletIndent="32"><bullet>—</bullet>',
            html,
        )
        html = re.sub(
            r"(?m)^#\*\s*",
            r'</para><para spaceBefore="0" leftIndent="32" bulletIndent="16"><bullet>—</bullet>',
            html,
        )
        html = re.sub(
            r"(?m)^#\s*",
            r'</para><para spaceBefore="0" leftIndent="16"><bullet><seq />.</bullet>',
            html,
        )
        html = re.sub(r"<\/?\s*br\s*\/?>", "\n", html)
        html = re.sub(
            r"(?m)(\r?\n){2,}", r'</para><para spaceBefore="0.2cm" ><seqreset />', html
        )
        html = re.sub(r"(?:\s|\n)+(</para>)", r"\1", html)
        html = re.sub(r"(<para[^>]*>)(?:\s|\n)+", r"\1", html)
        # Platypus ma problem z obrazkami, więc pozbywamy się ich
        html = re.sub(r"<img[^>]*>", "", html)
        # html = re.sub(r'(>)(?:\s)*(?:\r|\n)+(?:\s)*', r'\1', html)
        # html = re.sub(r'(?:\s|\r|\n)*(<)', r'\1', html)
        html = re.sub(r"(?i)<\s*a [^>]*>", "", html)
        html = re.sub(r"(?i)<\s*/a\s*>", "", html)
        for p in re.findall(r"(?s)(<para[^>]*>.*?</para>)", html):
            paras.append(Paragraph(p, para_style, caseSensitive=1))

        doc.build(paras, onFirstPage=my_page, onLaterPages=my_page)
        response = HttpResponse(output.getvalue(), content_type="application/pdf")
        response["Content-Disposition"] = "inline; filename=%s.pdf" % wybrany_wpis.slug
        if wybrany_wpis.tag_dlugosc.slug == "szkolenie":
            response["Link"] = '<https://%s%s>; rel="canonical"' % (
                WersjaJezykowa.biezaca().domain,
                wybrany_wpis.get_absolute_url(),
            )
        return response


@never_cache
@activate_translation
def detail_pdf(request, slug, language=settings.DEFAULT_LANGUAGE):
    return detail(request, slug, pdf=True, language=language)


@never_cache
@permission_required("www.change_terminszkolenia", raise_exception=True)
def detail_custom_oferta(request, slug):
    return detail(request, slug, custom_oferta=True)


def index_by_technologia_etag(
        request,
        technologia_slug,
        root=False,
        tab_slug=None,
        language=settings.DEFAULT_LANGUAGE,
):
    return TagTechnologia.objects.get_etag(
        slug=technologia_slug,
        language=language,
    )


@etag(index_by_technologia_etag)
@activate_translation
def index_by_technologia(
        request,
        technologia_slug,
        root=False,
        tab_slug=None,
        language=settings.DEFAULT_LANGUAGE,
):
    taby_pl = ["lista", "zamkniete", "konsultacje", "rabaty"]
    taby_en = ["courses", "on-demand", "consulting", "discounts"]

    if tab_slug and ((language == "pl" and tab_slug not in taby_pl) or (language == "en" and tab_slug not in taby_en)):
        raise Http404

    tech = get_object_or_404(
        TagTechnologia.objects.select_related(),
        slug=technologia_slug,
        language=language,
        widoczny_publicznie=True,
    )
    request.content_group = tech.content_group

    if not root:
        if tech.domena and tech.domena_redirect:
            return HttpResponsePermanentRedirect("http://%s/" % tech.domena.url)

    if tech.static_page:
        return HttpResponsePermanentRedirect(tech.static_page.get_absolute_url())

    lista_szkolen = (
        Szkolenie.objects.filter(
            tagi_technologia=tech,
            aktywne=True,
            tag_dlugosc__slug="szkolenie",
            language=language,
        )
        .select_related("tag_zawod")
        .order_by("tag_zawod__ordering", "ordering")
    )
    kursy_zawodowe = Szkolenie.objects.filter(
        tagi_technologia=tech,
        aktywne=True,
        tag_dlugosc__slug="kurs-zawodowy",
        language=language,
    ).prefetch_related(
        Prefetch(
            "warianty_szkolenia",
            queryset=SzkolenieWariant.objects.select_related("wariant"),
        )
    )

    tag_zawod_ordering = []
    if tech.tag_zawod_ordering:
        try:
            for pk in tech.tag_zawod_ordering.split(","):
                tag_zawod_ordering.append(int(pk.strip()))
        except:
            pass

    zawod_dict = OrderedDict()

    if tag_zawod_ordering:
        for tag_zawod_id in tag_zawod_ordering:
            for s in lista_szkolen:
                if s.tag_zawod_id == tag_zawod_id:
                    if s.tag_zawod.nazwa in zawod_dict:
                        zawod_dict[s.tag_zawod.nazwa].append(s)
                    else:
                        zawod_dict[s.tag_zawod.nazwa] = [s]
        # pozostale
        for s in lista_szkolen:
            if s.tag_zawod.nazwa not in zawod_dict:
                zawod_dict[s.tag_zawod.nazwa] = []

                for _s in lista_szkolen:
                    if _s.tag_zawod.nazwa == s.tag_zawod.nazwa:
                        zawod_dict[s.tag_zawod.nazwa].append(s)
    else:
        for s in lista_szkolen:
            if s.tag_zawod.nazwa in zawod_dict:
                zawod_dict[s.tag_zawod.nazwa].append(s)
            else:
                zawod_dict[s.tag_zawod.nazwa] = [s]

    zawod_list = []
    for key, values in zawod_dict.items():
        zawod_list.append(
            {
                "grouper": key,
                "list": values,
            }
        )

    taby = {
        "pl": [
            {
                "id": "lista",
                "slug": "lista",
                "title": "Lista szkoleń",
                "selected": True
            },
            {"id": "zamkniete", "slug": "zamkniete", "title": "Na zamówienie"},
            {
                "id": "konsultacje",
                "slug": "konsultacje",
                "title": "Konsultacje indywidualne"
            },
            {"id": "rabaty", "slug": "rabaty", "title": "Upusty i rabaty"},
        ],
        "en": [
            {"id": "lista", "slug": "courses", "title": "Courses", "selected": True},
            {"id": "zamkniete", "slug": "on-demand", "title": "On demand"},
            {"id": "konsultacje", "slug": "consulting", "title": "Consulting"},
            {"id": "rabaty", "slug": "discounts", "title": "Discounts"},
        ],
    }[language]

    tech_trenerzy_obj = tech.tagtechnologiaprowadzacy_set.filter(
        prowadzacy__pokazywac=True
    ).select_related("prowadzacy")

    tech_trenerzy = [obj.prowadzacy for obj in tech_trenerzy_obj]

    highlights = get_highlights(tech)
    top_highlight = None

    for h in highlights:
        if h.initially_selected:
            top_highlight = h
            break

    active_tab = taby[0]

    if tab_slug:
        for tab in taby:
            if tab_slug == tab["slug"]:
                active_tab = tab

    # Build schema.org JSON-LD for tech listing page
    try:
        if tech.kursy_po_szkoleniach:
            combined_items = list(lista_szkolen) + list(kursy_zawodowe)
        else:
            combined_items = list(kursy_zawodowe) + list(lista_szkolen)
    except Exception:
        combined_items = list(lista_szkolen)
    schema_json = get_tech_schema(request, tech, combined_items, language=language)

    return render(
        request,
        "www/index_by_technologia.html",
        {
            "lista_szkolen": lista_szkolen,
            "kursy_zawodowe": kursy_zawodowe,
            "tech": tech,
            "highlights": highlights,
            "tlumaczenia": [
                (x.language, x.get_absolute_url())
                for x in tech.tlumaczenia()
                if x.widoczny_publicznie
            ],
            "logo": tech.logo.plik if tech.logo else None,
            "taby": taby,
            "top_highlight": top_highlight,
            "tech_trenerzy": tech_trenerzy,
            "zawod_list": zawod_list,
            "tab_as_hyperlink": True,
            "active_tab": active_tab,
            "is_a_first_tab": active_tab["id"] == "lista",
            "base_tab_url": tech.get_absolute_url(),
            "non_indexable_tab": active_tab["id"]
                                 in ["zamkniete", "konsultacje", "rabaty"],
            "schema_json": schema_json,
        },
    )


def my_flat_page_etag(request, slug, tab_slug=None, language=settings.DEFAULT_LANGUAGE):
    return MyFlatPage.objects.get_etag(slug=slug, language=language)


def get_active_tab(tab_slug, taby):
    # returns tab, is_a_first_tab, non_indexable_tab

    tab = None
    is_a_first_tab = False
    non_indexable_tab = False

    taby = taby or []

    for tab in taby:
        if tab_slug == tab.slug or tab_slug is None:
            # są tu taby,
            # znalazlem pasujacy tab
            return (
                tab,
                tab == taby[0],
                tab != taby[0]
            )

    if taby:
        # tab_slug to żaden z tagów, ale ustawiam w takim razie
        # is_a_first_tab na True - żeby
        # wyświetlały się rzeczy z pierwszego taba
        # ustawiam tab jako pierwszy
        # nie powinniśmy jednak indeksowac takiego adresu
        # np: /pl/kurs-ai-machine-learning/zly_tag/
        tab = taby[0]
        is_a_first_tab = True
        non_indexable_tab = True

    elif tab_slug and not taby:
        # tabów nie ma, ale podany jest tab slug
        # /pl/kurs-ai-machine-learning/zly_tag/
        tab = None
        is_a_first_tab = False
        non_indexable_tab = True

    return tab, is_a_first_tab, non_indexable_tab


def get_schema(request, szkolenie=None):
    if not szkolenie:
        return ""
    uri = request.build_absolute_uri()
    tag_technologia = szkolenie.tagi_technologia.first().slug if szkolenie.tagi_technologia.first() else ""
    nazwa = szkolenie.nazwa or ""
    opis = szkolenie.header_descr or szkolenie.opis or ""

    schema = {
        "@context": "https://schema.org",
        "@graph": [
            {
                "@type": "Organization",
                "@id": "https://www.alx.pl/#org",
                "name": "ALX",
                "url": "https://www.alx.pl/"
            },
            {
                "@type": "WebPage",
                "@id": f"{uri}#webpage",
                "url": uri,
                "name": nazwa,
                "inLanguage": "pl-PL",
                "publisher": {"@id": "https://www.alx.pl/#org"},
                "mainEntity": {"@id": f"{uri}#course"}
            },
            {
                "@type": "BreadcrumbList",
                "@id": f"{uri}#breadcrumbs",
                "itemListElement": [
                    {"@type": "ListItem", "position": 1, "name": "ALX", "item": "https://www.alx.pl/"},
                    {"@type": "ListItem", "position": 2, "name": tag_technologia,
                     "item": f"https://www.alx.pl/tech/{tag_technologia}/"},
                    {"@type": "ListItem", "position": 3, "name": nazwa, "item": uri}
                ]
            },
            {
                "@type": "Course",
                "@id": f"{uri}#course",
                "name": nazwa,
                "description": opis,
                "inLanguage": "pl-PL",
                "provider": {"@id": "https://www.alx.pl/#org"}
            }
        ]
    }
    schema_json = json.dumps(schema, indent=4)
    return schema_json


def get_tech_schema(request, tech, items, language=settings.DEFAULT_LANGUAGE):
    try:
        uri = request.build_absolute_uri()
    except Exception:
        uri = tech.get_absolute_url()
    # Map language code to BCP-47
    lang_map = {"pl": "pl-PL", "en": "en"}
    in_lang = lang_map.get(language, language)

    # WebPage name
    if language == "pl":
        webpage_name = tech.meta_title or f"Szkolenia i kursy {tech.nazwa}"
        itemlist_name = f"Szkolenia i kursy {tech.nazwa}"
    else:
        webpage_name = tech.meta_title or f"Courses and trainings {tech.nazwa}"
        itemlist_name = f"Courses and trainings {tech.nazwa}"

    def course_dict(szk):
        name = getattr(szk, "nazwa", "") or ""
        opis = getattr(szk, "header_descr", None) or getattr(szk, "opis", "") or ""
        try:
            url = request.build_absolute_uri(szk.get_absolute_url())
        except Exception:
            url = szk.get_absolute_url()
        return {
            "@type": "Course",
            "name": name,
            "description": opis,
            "url": url,
            "inLanguage": in_lang,
            "provider": {"@id": "https://www.alx.pl/#org"},
        }

    item_list_elements = []
    position = 1
    for szk in items:
        item_list_elements.append({
            "@type": "ListItem",
            "position": position,
            "item": course_dict(szk)
        })
        position += 1

    schema = {
        "@context": "https://schema.org",
        "@graph": [
            {
                "@type": "Organization",
                "@id": "https://www.alx.pl/#org",
                "name": "ALX",
                "url": "https://www.alx.pl/"
            },
            {
                "@type": "WebPage",
                "@id": f"{uri}#webpage",
                "url": uri,
                "name": webpage_name,
                "inLanguage": in_lang,
                "publisher": {"@id": "https://www.alx.pl/#org"},
                "mainEntity": {"@id": f"{uri}#itemlist"}
            },
            {
                "@type": "BreadcrumbList",
                "@id": f"{uri}#breadcrumbs",
                "itemListElement": [
                    {"@type": "ListItem", "position": 1, "name": "ALX", "item": "https://www.alx.pl/"},
                    {"@type": "ListItem", "position": 2, "name": tech.nazwa, "item": uri}
                ]
            },
            {
                "@type": "ItemList",
                "@id": f"{uri}#itemlist",
                "name": itemlist_name,
                "itemListElement": item_list_elements
            }
        ]
    }
    return json.dumps(schema, indent=4)



@etag(my_flat_page_etag)
@activate_translation
def my_flat_page(request, slug, tab_slug=None, language=settings.DEFAULT_LANGUAGE):
    page = get_object_or_404(MyFlatPage, slug=slug, language=language, enabled=True)
    request.content_group = page.content_group

    szkolenie = page.szkolenie
    warianty_szkolenia = szkolenie.warianty() if szkolenie else []

    pole_informacyjne = (
        MyFlatPage.objects.filter(
            slug="pole-informacyjne-pod-terminami-{}-{}".format(
                szkolenie.tag_dlugosc.slug, szkolenie.language
            )
        ).first()
        if szkolenie
        else None
    )
    pole_informacyjne = pole_informacyjne.content if pole_informacyjne else None

    taby = page.get_taby()

    if tab_slug and tab_slug not in (tab.slug for tab in taby):
        raise Http404

    active_tab, is_a_first_tab, non_indexable_tab = get_active_tab(tab_slug, taby)
    program = page.get_program_tab()

    schema_json = get_schema(request, szkolenie)

    return render(
        request,
        "www/my_flat_page.html",
        {
            "page": page,
            "highlights": get_highlights(page),
            "tlumaczenia": [
                (x.language, x.get_absolute_url())
                for x in page.tlumaczenia()
                if x.enabled
            ],
            "logo": page.logo.plik if page.logo else None,
            "taby": taby,
            "pokazuj_notyfikacje": bool(szkolenie),
            "pokazuj_baner_o_bootcamp": bool(szkolenie) and language == "pl",
            "pokazuj_video_o_szkoleniach": bool(szkolenie) and language == "pl",
            "pokazuj_box_z_terminami": bool(szkolenie),
            "pokazuj_probki_materialow": False,
            "szkolenie": szkolenie,
            "wariant_szkolenia": warianty_szkolenia[0] if warianty_szkolenia else None,
            "warianty_szkolenia": warianty_szkolenia,
            "pole_informacyjne": pole_informacyjne,
            "tab_as_hyperlink": bool(szkolenie),
            "active_tab": active_tab,
            "is_a_first_tab": bool(szkolenie) and is_a_first_tab,
            "base_tab_url": page.get_absolute_url(),
            "non_indexable_tab": non_indexable_tab,
            "is_not_searchable": not page.is_searchable,
            "program": program,
            "schema_json": schema_json,
        },
    )


@never_cache
@activate_translation
@require_http_methods(["POST"])
def send_registration_link(request, language, slug):
    szkolenie = get_object_or_404(Szkolenie, slug=slug, language=language, aktywne=True)

    form = RegistrationLinkForm(request.POST)
    if not form.is_valid():
        return HttpResponse(status=400)

    www.tasks.autoresponder.delay(
        email=form.cleaned_data["email"],
        topic="link_rejestracyjny",
        language=language,
        email_data={
            "link_do_szkolenia": szkolenie.url_with_domain(),
            "link_rejestracji": szkolenie.url_registration(),
            "szkolenie_nazwa": szkolenie.nazwa,
        },
        msg_subject=_("ALX - link rejestracyjny na szkolenie: %(nazwa)s")
                    % {"nazwa": szkolenie.nazwa},
    )
    return HttpResponse(status=200)


@never_cache
@activate_translation
def post_contact_form(request, language=settings.DEFAULT_LANGUAGE):
    if request.method != "POST" or not request.POST:
        return HttpResponseRedirect("/")

    username = request.POST.get("username", "")
    usercode = request.POST.get("usercode", "")

    if username or usercode:
        return HttpResponseRedirect("/")

    if request.POST.get("version", "") == "3":
        form_class = InvisibleCaptchaContactForm
    else:
        form_class = CaptchaContactForm

    form = form_class(request.POST)
    if not form.is_valid():
        raise Http404

    mesg = ""
    cleaned_data = {}
    for key in request.POST:

        if key in ["email_telefon", "tresc"]:
            key = strip_tags(key)
            value = strip_tags(request.POST.get(key, ""))

            if value:
                cleaned_data[key] = strip_tags(request.POST.get(key, ""))
                mesg += "%s => %s\n" % (key, cleaned_data[key])

    mesg += "\nip => %s\n" % get_client_ip(request)[0]

    if "rodzaj" in request.POST and request.POST["rodzaj"] == "zgloszenie":
        if language == "en":
            redirect_to = "thank-you-for-sending-application"
        else:
            redirect_to = "dziekujemy-za-wyslanie-zgloszenia"
        mail_to = settings.MAIL_ZGLOSZENIE_TO_ADDRESS
    else:
        if language == "en":
            redirect_to = "thank-you-for-sending-form"
        else:
            redirect_to = "dziekujemy-za-wyslanie-formularza"
        refererstring = "%s" % request.META.get("HTTP_REFERER")
        refererclean = re.sub(r"\?.*", "", refererstring)
        mesg += "ze strony => %s\n" % refererclean
        mail_to = settings.MAIL_CONTACT_FORM_TO_ADDRESS

    if request.POST.get("trap", "") == "":

        sendto = request.POST.get("sendto", "").lower()

        if sendto in settings.ALLOWED_MAIL_CONTACT_FORM_TO_ADDRESSES:
            mail_to = sendto

        send_mail(
            "ALX formularz kontaktowy",
            mesg,
            settings.MAIL_FROM_ADDRESS_ZAPYTANIE,
            [mail_to],
        )

        email = get_email(cleaned_data.get("email_telefon"))
        if email:
            www.tasks.autoresponder.delay(
                email=email,
                topic="formularz_kontaktowy",
                language=language,
                email_data={
                    "imie_nazwisko": request.POST.get("imie_nazwisko", ""),
                    "tresc": cleaned_data.get("tresc", ""),
                },
            )

    return HttpResponseRedirect(
        reverse("my_flat_page", kwargs={"language": language, "slug": redirect_to})
    )


@never_cache
@activate_translation
def dziekujemy_za_zgloszenie(
        request, token, conversion, language=settings.DEFAULT_LANGUAGE
):
    zgloszenie = get_object_or_404(Zgloszenie, token=token)
    now = datetime.datetime.now()

    if zgloszenie.time < now - datetime.timedelta(days=90):
        raise Http404

    uczestnik = Uczestnik.objects.select_related("termin").get(
        zgloszenie_form=zgloszenie
    )

    if uczestnik.termin.termin < now.date() - datetime.timedelta(days=14):
        raise Http404

    viewed = zgloszenie.viewed

    if not viewed:
        zgloszenie.viewed = True
        zgloszenie.save(update_fields=["viewed"])

    language = zgloszenie.termin.szkolenie.language

    # Uzytkownik moze wgrac dokumenty, gdy:
    #  - jest to kurs
    #  - nie jest to firma
    #  - wybral platnosci ratalne
    upload_allowed = (
            uczestnik.termin.is_kurs()
            and uczestnik.prywatny
            and uczestnik.is_raty()
            and False
    )

    if upload_allowed and request.method == "POST" and request.is_ajax():
        form = UczestnikPlikForm(
            request.POST,
            request.FILES,
            remote_addr=get_client_ip(request)[0],
            uczestnik=uczestnik,
        )

        if form.is_valid():
            fobj = form.save()
            www.tasks.participant_file_alert.delay(file_id=fobj.pk)
            return HttpResponse(json.dumps(""), content_type="application/json")
        else:
            return HttpResponse(
                json.dumps(
                    str(dict(form.errors).get("file", [_("Błąd zapisu pliku.")])[0]),
                    ensure_ascii=False,
                ),
                content_type="application/json",
                status=400,
            )

    if zgloszenie.termin.prywatna_rejestracja:
        konwersja_cena = zgloszenie.cena
    else:
        konwersja_cena = min([zgloszenie.cena, zgloszenie.termin.szkolenie.cena * 15])
    return render(
        request,
        "www/dziekujemy_za_zgloszenie.html",
        {
            "first_visit": not viewed,
            "zgloszenie": zgloszenie,
            "konwersja_cena": konwersja_cena,
            "uczestnik": uczestnik,
            "conversion": conversion,
            "zamkniete": uczestnik.termin.prywatna_rejestracja if uczestnik else None,
            "cena_papierowej_faktury": number_format(
                settings.CENA_NETTO_PAPIEROWEJ_FAKTURY[language], decimal_pos=2
            ),
            "cena_drukowanego_certyfikatu": number_format(
                settings.CENA_NETTO_DRUKOWANEGO_CERTYFIKATU[language], decimal_pos=2
            ),
            "files_exist": UczestnikPlik.objects.filter(participant=uczestnik).exists(),
            "upload_allowed": upload_allowed,
        },
    )


def make_uczestnik_from_zgloszenie(z):
    u = Uczestnik()
    u.status = -3
    u.papier = 1
    u.czas_dodania = z.time
    uczestnik_fields = [
        "termin",
        "imie_nazwisko",
        "osoba_do_kontaktu",
        "firma",
        "email",
        "email_ksiegowosc",
        "wiek_uczestnika",
        "odpowiedz_na_dodatkowe_pytanie",
        "telefon",
        "adres",
        "miejscowosc_kod",
        "chce_fakture",
        "drukowany_certyfikat",
        "faktura_firma",
        "faktura_adres",
        "faktura_miejscowosc_kod",
        "faktura_nip",
        "faktura_kraj",
        "za_kurs_zaplace",
        "bylem_wczesniej",
        "bylem_na",
        "uwagi_klienta",
        "uczestnik_wieloosobowy_ilosc_osob",
        "podmiot_publiczny",
        "stawka_vat",
        "prywatny",
        "chce_obiady",
        "ile_obiadow_wegetarianskich",
        "cena_obiadow",
        "chce_autoryzacje",
        "waluta",
        "raty_nazwa_dokumentu",
        "raty_numer_dokumentu",
        "raty_pesel",
        "raty_panstwo",
        "discount_code",
    ]
    for a in uczestnik_fields:
        setattr(u, a, getattr(z, a))
    u.kwota_do_zaplaty = z.cena
    u.zgloszenie_form = z
    u.save()
    return u


def podobni_chetni(u, szkolenie):
    u_in = Uczestnik.normalizuj_dla_levenshtein(u.imie_nazwisko)
    u_f = Uczestnik.normalizuj_dla_levenshtein(u.faktura_firma)
    matches = []
    for p in PotencjalnyChetny.objects.filter(
            szkolenie=szkolenie, stan_sprawy="aktualne"
    ):
        p_in = Uczestnik.normalizuj_dla_levenshtein(p.imie_nazwisko)
        p_f = Uczestnik.normalizuj_dla_levenshtein(p.firma)
        jaro = Levenshtein.jaro(p_in, u_in)
        if p_in and u.prywatny:
            if p_in in u_in:
                jaro = 1
        if not p_in:
            jaro = Levenshtein.jaro(p_f, u_f)
        if jaro > 0.95 or not p_in and jaro > 0.8:
            p.url_jego = reverse("admin:www_potencjalnychetny_change", args=(u.id,))
            matches.append([jaro, p])
    return [x[1] for x in sorted(matches, key=lambda a: a[0], reverse=True)]


def potencjalnie_poprzednie(u1, imie_nazwisko=None, nazwa_firmy=None):
    if u1:
        indywiduum = u1.prywatny
        ff1 = u1.nazwa()
    else:
        if nazwa_firmy:
            ff1 = nazwa_firmy
            indywiduum = False
        else:
            ff1 = imie_nazwisko
            indywiduum = True
    ff1 = Uczestnik.normalizuj_dla_levenshtein(ff1)
    matches = []
    for u2 in Uczestnik.objects.all():
        if u1 == u2:
            continue
        ff2 = Uczestnik.normalizuj_dla_levenshtein(u2.nazwa())
        jaro = Levenshtein.jaro(ff1, ff2)
        if jaro > 0.95 or not indywiduum and jaro > 0.8:
            matches.append([jaro, u2])
    return [x[1] for x in sorted(matches, key=lambda a: a[0], reverse=True)]


FUNKCJONALNE = ["com", "org", "net", "edu", "gov", "mil", "ac", "co"]

REGIONALNE = [
    "augustow",
    "babia-gora",
    "bedzin",
    "beskidy",
    "bialowieza",
    "bialystok",
    "bielawa",
    "bieszczady",
    "boleslawiec",
    "bydgoszcz",
    "bytom",
    "cieszyn",
    "czeladz",
    "czest",
    "dlugoleka",
    "elblag",
    "elk",
    "glogow",
    "gniezno",
    "gorlice",
    "grajewo",
    "ilawa",
    "jaworzno",
    "jelenia-gora",
    "jgora",
    "kalisz",
    "kazimierz-dolny",
    "karpacz",
    "kartuzy",
    "kaszuby",
    "katowice",
    "kepno",
    "ketrzyn",
    "klodzko",
    "kobierzyce",
    "kolobrzeg",
    "konin",
    "konskowola",
    "kutno",
    "lapy",
    "lebork",
    "legnica",
    "lezajsk",
    "limanowa",
    "lomza",
    "lowicz",
    "lubin",
    "lukow",
    "malbork",
    "malopolska",
    "mazowsze",
    "mazury",
    "mielec",
    "mielno",
    "mragowo",
    "naklo",
    "nowaruda",
    "nysa",
    "olawa",
    "olecko",
    "olkusz",
    "olsztyn",
    "opoczno",
    "opole",
    "ostroda",
    "ostroleka",
    "ostrowiec",
    "ostrowwlkp",
    "pila",
    "pisz",
    "podhale",
    "podlasie",
    "polkowice",
    "pomorze",
    "pomorskie",
    "prochowice",
    "pruszkow",
    "przeworsk",
    "pulawy",
    "radom",
    "rawa-maz",
    "rybnik",
    "rzeszow",
    "sanok",
    "sejny",
    "slask",
    "slupsk",
    "sosnowiec",
    "stalowa-wola",
    "skoczow",
    "starachowice",
    "stargard",
    "suwalki",
    "swidnica",
    "swiebodzin",
    "swinoujscie",
    "szczecin",
    "szczytno",
    "tarnobrzeg",
    "tgory",
    "turek",
    "tychy",
    "ustka",
    "walbrzych",
    "warmia",
    "warszawa",
    "waw",
    "wegrow",
    "wielun",
    "wlocl",
    "wloclawek",
    "wodzislaw",
    "wolomin",
    "wroclaw",
    "zachpomor",
    "zagan",
    "zarow",
    "zgora",
    "zgorzelec",
]

FREE_EMAIL_HOSTS = [
    "gmail.com",
    "o2.pl",
    "op.pl",
    "onet.eu",
    "onet.pl",
    "tlen.pl",
    "wp.pl",
    "yahoo.com",
    "yahoo.co.uk",
    "poczta.fm",
    "interia.pl",
    "gazeta.pl",
]


def url_from_email(email):
    m = re.search(r"@(.*?)\s*$", email.lower())
    if m and m.group(1) in FREE_EMAIL_HOSTS:
        return ""
    m = re.search(r"(?:([a-z0-9-]+)\.)?([a-z0-9-]+)\.([a-z0-9-]+)\s*$", email.lower())
    if m is None:
        return ""
    if m.group(1) is None and (m.group(3) == "pl" and m.group(2) in REGIONALNE):
        return ""
    if m.group(1) is not None and (
            m.group(2) in FUNKCJONALNE or (m.group(3) == "pl" and m.group(2) in REGIONALNE)
    ):
        return "http://%s/" % ".".join(("www",) + m.groups())
    return "http://%s/" % ".".join(["www", m.group(2), m.group(3)])


@never_cache
@activate_translation
def zamkniete_zgloszenie(request, slug, secret_key, language=settings.DEFAULT_LANGUAGE):
    termin = get_object_or_404(
        TerminSzkolenia.objects.select_related("szkolenie").exclude(odbylo_sie=False),
        szkolenie__slug=slug,
        szkolenie__language=language,
        # szkolenie__aktywne=True,
        secret_key=secret_key,
        zamkniete=True,
        prywatna_rejestracja=True,
        termin__gte=datetime.date.today(),
    )
    szkolenie = termin.szkolenie

    klasa_formularza = ZamknieteZgloszenieForm

    form_extra = {"szkolenie": szkolenie, "termin": termin}

    if szkolenie.tag_dlugosc.slug == "szkolenie":
        zaplace_label = _("Za szkolenie zapłacę")
    else:  # kurs
        zaplace_label = _("Za kurs zapłacę")

    zaplace_choices = [x for x in ZAPLACE_CHOICES if x[0] in [5, 7]]

    if request.method == "POST":
        form = klasa_formularza(request.POST, **form_extra)
    else:
        form = klasa_formularza(**form_extra)

    form.fields["za_kurs_zaplace"].choices = zaplace_choices
    form.fields["za_kurs_zaplace"].label = zaplace_label

    if request.method == "POST":
        if form.is_valid():
            d = form.cleaned_data

            zgloszenie = form.save(commit=False)
            zgloszenie.ip = get_client_ip(request)[0]
            zgloszenie.token = random_string(16)
            zgloszenie.set_cena(szkolenie_zamkniete=True)

            # Ustaw kraj na podstawie języka strony:
            #   - EN - Wielka Brytania
            #   - PL i każdy inny - Polska
            if language == "en":
                zgloszenie.faktura_kraj = "GB"
            else:
                zgloszenie.faktura_kraj = "PL"

            zgloszenie.save()

            u = make_uczestnik_from_zgloszenie(zgloszenie)

            if d.get("chce_zapisac_sie_na_newsletter"):
                refererclean, lang = Odbiorca.podaj_refererlang(
                    request.build_absolute_uri(),
                    szkolenie.language,
                )
                Odbiorca.dodaj_potwierdzonego(
                    u.email,
                    telefon=u.telefon,
                    firma=u.faktura_firma,
                    uczestnik=u,
                    zrodlo_kontaktu="zgloszenie",
                    referer=refererclean,
                    lang=lang,
                )

            www.tasks.zapis_na_termin.delay(uczestnik_id=u.pk, language=language)

            return HttpResponseRedirect(
                reverse(
                    "dziekujemy_za_zgloszenie",
                    kwargs={"token": zgloszenie.token, "language": language},
                )
            )

    return render(
        request,
        "www/zamkniete_zgloszenie.html",
        {
            "szkolenie": termin.szkolenie,
            "termin": termin,
            "form": form,
        },
    )


@never_cache
@activate_translation
def zgloszenie(request, slug, language=settings.DEFAULT_LANGUAGE):
    wersja_jezykowa = WersjaJezykowa.biezaca()

    szkolenie = get_object_or_404(
        Szkolenie,
        slug=slug,
        language=language,
    )

    jest_wariantem = szkolenie.jest_wariantem()

    if not szkolenie.aktywne and not jest_wariantem:
        raise Http404

    klasa_formularza = {"pl": ZgloszeniePolskieForm}.get(
        language, ZgloszenieZagraniczneForm
    )

    form_extra = {"szkolenie": szkolenie}

    terminy_interesujace = szkolenie.terminy().filter(szkolenie__language=language)

    if szkolenie.tag_dlugosc.slug == "szkolenie":
        zaplace_choices = [x for x in ZAPLACE_CHOICES if x[0] in [5, 6, 7]]
        zaplace_label = _("Za szkolenie zapłacę")
        terminy = [
            (
                x.id,
                "%s (%s, %s)"
                % (x.termin.__str__(), x.lokalizacja.shortname, x.get_tryb_display()),
            )
            for x in terminy_interesujace
        ]
    else:  # kurs
        if szkolenie.wiele_rat:
            zaplace_choices = [x for x in ZAPLACE_CHOICES if x[0] in [1, 2, 3, 4, 10]]
        else:
            zaplace_choices = [x for x in ZAPLACE_CHOICES if x[0] in [1, 2, 3, 4]]
        # Z formularza angielskiego wywalamy płatność w pięciu ratach.
        if language == "en":
            zaplace_choices = [
                (value, label) for value, label in zaplace_choices if not value == 10
            ]
        zaplace_label = _("Za kurs zapłacę")
        terminy = [
            (
                x.id,
                "%s (%s, %s%s)"
                % (
                    x.termin.__str__(),
                    x.lokalizacja.shortname,
                    x.get_tryb_display(),
                    (
                        ", %s" % Truncator(x.dodatkowe_uwagi).chars(37)
                        if x.dodatkowe_uwagi
                        else ""
                    ),
                ),
            )
            for x in terminy_interesujace
        ]

    # terminy_obiady zawierają też info o autoryzacjach
    terminy_obiady = dict(
        [
            (
                x.id,
                {
                    "stawka_vat": bool(wersja_jezykowa.stawka_vat),
                    "obiady": x.obiady,
                    "cena_obiadu": str(
                        pretty_number(x.cena_obiadu)
                        if x.cena_obiadu is not None
                        else None
                    ),
                    "obiady_data_id": x.obiady_data_id_do_formularza_zgloszeniowego(),
                    "ma_autoryzacje": bool(x.autoryzacja()),
                },
            )
            for x in terminy_interesujace
        ]
    )
    if request.method == "POST":
        form = klasa_formularza(request.POST, **form_extra)
    else:
        # Sprawdzamy, czy w URL jest termin, ktory mamy domyslnie zaznaczyc.
        termin_from_url = request.GET.get("t", "")

        if (
                termin_from_url
                and termin_from_url.isnumeric()
                and int(termin_from_url) in dict(terminy)
        ):
            termin_initial = int(termin_from_url)
        else:
            termin_initial = None

        initial = {}
        if termin_initial:
            initial["termin"] = termin_initial

        form = klasa_formularza(initial=initial, **form_extra)

    form.fields["termin"].choices = terminy
    form.fields["za_kurs_zaplace"].choices = zaplace_choices
    form.fields["za_kurs_zaplace"].label = zaplace_label
    if szkolenie.autoryzacja:
        form.fields["chce_autoryzacje"].help_text = _(
            "{short_description}<br/>Koszt {price} {waluta} {netto} za osobę."
        ).format(
            short_description=szkolenie.autoryzacja.opis_krotki,
            price=szkolenie.cena_autoryzacji,
            waluta=szkolenie.waluta.symbol,
            netto=(_("netto") if wersja_jezykowa.stawka_vat is not None else ""),
        )
    else:
        # form.fields["chce_autoryzacje"].widget = forms.HiddenInput()
        # form.fields["chce_autoryzacje"].initial = False
        del form.fields["chce_autoryzacje"]

    if request.is_ajax():
        response_data = {}
        try:
            form = ZgloszeniePolskieFormNoValidation(request.POST)
            form.fields["termin"].choices = terminy
            zgloszenie = form.save(commit=False)
            zgloszenie.set_uczestnik_wieloosobowy_ilosc_osob()
            zgloszenie.set_dane_obiadow(form["obiad_wegetarianski"])
            zgloszenie.set_cena()
            response_data["cena_netto"] = str(zgloszenie.cena)

            if zgloszenie.is_doplata_do_faktury():
                response_data["doplata"] = "1"
            else:
                response_data["doplata"] = "0"
        except:
            response_data["doplata"] = "0"
        return HttpResponse(json.dumps(response_data), content_type="application/json")

    if request.method == "POST":
        if form.is_valid():
            d = form.cleaned_data

            zgloszenie = form.save(commit=False)
            zgloszenie.ip = get_client_ip(request)[0]
            zgloszenie.token = random_string(16)

            zgloszenie.set_uczestnik_wieloosobowy_ilosc_osob()
            zgloszenie.set_dane_obiadow(d["obiad_wegetarianski"])
            zgloszenie.set_cena()

            # Ustaw kraj na podstawie języka strony:
            #   - EN - Wielka Brytania
            #   - PL i każdy inny - Polska
            if language == "en":
                zgloszenie.faktura_kraj = "GB"
            else:
                zgloszenie.faktura_kraj = "PL"

            if zgloszenie.chce_autoryzacje == "":
                zgloszenie.chce_autoryzacje = False

            zgloszenie.save()

            u = make_uczestnik_from_zgloszenie(zgloszenie)

            if d.get("chce_zapisac_sie_na_newsletter"):
                refererclean, lang = Odbiorca.podaj_refererlang(
                    request.build_absolute_uri(),
                    szkolenie.language,
                )
                Odbiorca.dodaj_potwierdzonego(
                    u.email,
                    telefon=u.telefon,
                    firma=u.faktura_firma,
                    uczestnik=u,
                    zrodlo_kontaktu="zgloszenie",
                    referer=refererclean,
                    lang=lang,
                )

            www.tasks.zapis_na_termin.delay(uczestnik_id=u.pk, language=language)

            return HttpResponseRedirect(
                reverse(
                    "dziekujemy_za_zgloszenie",
                    kwargs={"token": zgloszenie.token, "language": language},
                )
            )

    if not jest_wariantem:
        warianty_szkolenia = [
            e.wariant for e in szkolenie.warianty_szkolenia.select_related("wariant")
        ]

    else:
        warianty_szkolenia = [jest_wariantem] + [
            e.wariant
            for e in jest_wariantem.warianty_szkolenia.select_related(
                "wariant"
            ).exclude(wariant__pk=szkolenie.pk)
        ]

    language = szkolenie.language

    return render(
        request,
        "www/zgloszenie.html",
        {
            "szkolenie": szkolenie,
            "terminy": terminy,
            "terminy_obiady": json.dumps(terminy_obiady),
            "terminy_obiady_dict": terminy_obiady,
            "form": form,
            "wariant_szkolenia": warianty_szkolenia[0] if warianty_szkolenia else None,
            "warianty_szkolenia": warianty_szkolenia,
            "cena_papierowej_faktury": number_format(
                settings.CENA_NETTO_PAPIEROWEJ_FAKTURY[language], decimal_pos=2
            ),
            "cena_drukowanego_certyfikatu": number_format(
                settings.CENA_NETTO_DRUKOWANEGO_CERTYFIKATU[language], decimal_pos=2
            ),
            "tlumaczenia": [
                (
                    tlumaczenie.language,
                    reverse(
                        "zgloszenie",
                        kwargs={
                            "slug": tlumaczenie.slug,
                            "language": tlumaczenie.language,
                        },
                    ),
                )
                for tlumaczenie in szkolenie.tlumaczenia()
            ],
        },
    )


@never_cache
def feed(request, tech=None):
    # 11.06.2018 - wyłączamy RSSa.
    return HttpResponsePermanentRedirect("/")


@never_cache
@activate_translation
def najblizsze_szkolenia(request, language=settings.DEFAULT_LANGUAGE):
    # tutaj jest trochę dziwnie, bo w wersji angielskiej filtrujemy po
    # polskich tagach itd., ale użytkownikowi pokazujemy angielskie
    # odpowiedniki wszystkiego (jeśli szkolenie nie ma odpowiednika
    # angielskiego, to termin się nie pokazuje po angielsku)
    najblizsze = get_najblizsze().select_related()
    data = ""
    # data_opcje = [ '%d-%02d' % (x.year, x.month) for x in najblizsze.dates('termin', 'month') ]
    data_opcje = najblizsze.dates("termin", "month")
    try:
        m = re.match("(\d+)-(\d+)", strip_tags(request.GET.get("data", "")))
        if m:
            data_parts = (int(m.group(1)), int(m.group(2)))
            data = "%d-%02d" % data_parts
            najblizsze = najblizsze.filter(
                termin__year=data_parts[0], termin__month=data_parts[1]
            )
    except ValueError:
        pass
    lokalizacja = None
    lokalizacje = Lokalizacja.objects.filter(reklamowana=True)
    try:
        lokalizacja_id = int(strip_tags(request.GET.get("lokalizacja", "")))
        lokalizacja = Lokalizacja.objects.get(id=lokalizacja_id)
        najblizsze = najblizsze.filter(lokalizacja=lokalizacja)
    except (ValueError, Lokalizacja.DoesNotExist):
        pass
    tag_dlugosc = None
    tag_dlugosc_opcje = TagDlugosc.objects.all()
    try:
        tag_dlugosc = TagDlugosc.objects.get(
            slug=strip_tags(request.GET.get("tag_dlugosc", ""))
        )
        najblizsze = najblizsze.filter(szkolenie__tag_dlugosc=tag_dlugosc)
    except TagDlugosc.DoesNotExist:
        pass
    tag_technologia = None
    tag_technologia_opcje = TagTechnologia.objects.filter(
        widoczny_publicznie=True, language=settings.DEFAULT_LANGUAGE
    )
    try:
        tag_technologia = tag_technologia_opcje.get(
            slug=strip_tags(request.GET.get("tag_technologia", ""))
        )
        najblizsze = najblizsze.filter(
            Q(szkolenie__tagi_technologia=tag_technologia)
            | Q(szkolenie__tagi_technologia__base_translation=tag_technologia)
        )
    except TagTechnologia.DoesNotExist:
        pass
    if language == "en":
        tag_technologia_opcje = [x for x in tag_technologia_opcje if x.en()]
    tag_zawod = None
    tag_zawod_opcje = TagZawod.objects.filter(
        widoczny_publicznie=True, language=settings.DEFAULT_LANGUAGE
    )
    try:
        tag_zawod = tag_zawod_opcje.get(
            slug=strip_tags(request.GET.get("tag_zawod", ""))
        )
        najblizsze = najblizsze.filter(
            Q(szkolenie__tag_zawod=tag_zawod)
            | Q(szkolenie__tag_zawod__base_translation=tag_zawod)
        )
    except TagZawod.DoesNotExist:
        pass

    filtering_language = request.GET.get("language", None) or None
    if filtering_language and filtering_language in ["pl", "en"]:
        najblizsze = najblizsze.filter(szkolenie__language=filtering_language)
    else:
        filtering_language = None

    filtering_tryb = request.GET.get("tryb", None) or None
    if filtering_tryb and filtering_tryb in ["1", "2", "3"]:
        najblizsze = najblizsze.filter(tryb=int(filtering_tryb))
    else:
        filtering_tryb = None

    if language == "en":
        tag_zawod_opcje = [x for x in tag_zawod_opcje if x.en()]
        najblizsze = [
            x for x in najblizsze if x.szkolenie.en() or x.szkolenie.language == "en"
        ]

    tryb_opcje = [
        ["1", _("dzienny")],
        ["2", _("wieczorowy")],
        ["3", _("weekendowy (zaoczny)")],
    ]
    language_opcje = sorted(
        [shortcut for shortcut in WersjaJezykowa.biezaca().jezyki_szkolen()]
    )
    tlumaczenia = [
        x for x in [("pl", "/najblizsze/"), ("en", "/en/dates/")] if x[0] != language
    ]

    return render(
        request,
        "www/najblizsze.html",
        {
            "lokalizacja": lokalizacja,
            "lokalizacje": lokalizacje,
            "najblizsze": najblizsze,
            "tag_dlugosc": tag_dlugosc,
            "tag_dlugosc_opcje": tag_dlugosc_opcje,
            "tag_technologia": tag_technologia,
            "tag_technologia_opcje": tag_technologia_opcje,
            "tag_zawod": tag_zawod,
            "tag_zawod_opcje": tag_zawod_opcje,
            "filtering_language": filtering_language,
            "language_opcje": language_opcje,
            "data": data,
            "data_opcje": data_opcje,
            "tlumaczenia": tlumaczenia,
            "tryb_opcje": tryb_opcje,
            "filtering_tryb": filtering_tryb,
        },
    )


@never_cache
@activate_translation
def last_minute(request, target=None, language=settings.DEFAULT_LANGUAGE):
    """
    Pobieramy szkolenia, które rozpoczynają się w przeciągu miesiąca
    (od jutra) i sa na nie jeszcze wolne miejsca.
    """

    # today = datetime.date.today()
    # tomorrow = today + datetime.timedelta(days=1)
    # plus_one_month = tomorrow + datetime.timedelta(days=30)
    #
    # qs = TerminSzkolenia.objects.filter(
    #     termin__gte=tomorrow,
    #     termin__lte=plus_one_month,
    #     zamkniete=False,
    #     odbylo_sie=True,
    #     szkolenie__aktywne=True,
    #     # szkolenie__tag_dlugosc__slug='kurs-zawodowy',
    #     szkolenie__language='pl'
    # ).order_by('termin').select_related()
    #
    # if target == 'kursy':
    #     qs = qs.filter(szkolenie__tag_dlugosc__slug='kurs-zawodowy')
    # elif target == 'szkolenia':
    #     qs = qs.filter(szkolenie__tag_dlugosc__slug='szkolenie')
    #
    # # if language == 'en':
    # #     qs = qs.filter(szkolenie__language='en')
    #
    # # Filtrujemy na ilość uczestnikow (muszą być wolne miejsca)
    # qs = [
    #     e for e in qs if
    #     e.szkolenie.maksymalna_liczba_osob_dla_grup_otwartych() -
    #     e.ilosc_uczestnikow_potwierdzonych() > 0
    # ]
    # set_session_language(request, language)
    # return render( request,
    #         'www/last_minute.html',
    #         {   'last_minute': qs,
    #         }
    #     )
    return redirect("/", permanent=True)


@never_cache
@activate_translation
def zaproponuj_termin(request, slug, language=settings.DEFAULT_LANGUAGE):
    szkolenie = get_object_or_404(Szkolenie, slug=slug, language=language)

    # Check if the training is active or has variants
    if not szkolenie.aktywne and not szkolenie.jest_wariantem():
        raise Http404

    if request.method == "POST":
        form = PropozycjaTerminuForm(language, request.POST)
        if form.is_valid():
            cleaned_data = form.cleaned_data

            # Create message content for the email
            mesg = f"Szkolenie: {szkolenie.nazwa} ({szkolenie.kod})\n"
            mesg += "\n".join(
                f"{field} => {cleaned_data[field]}" for field in form.fields
            )
            mesg += f"jezyk => {language}\n"

            # Send the email with the proposal
            send_mail(
                "Propozycja terminu szkolenia",
                mesg,
                settings.MAIL_FROM_ADDRESS_ZAPYTANIE,
                [settings.MAIL_PROPOZYCJA_TERMINU_TO_ADDRESS],
            )

            # Trigger autoresponder if an email is provided
            email = get_email(cleaned_data.get("email"))
            if email:
                www.tasks.autoresponder.delay(
                    email=email,
                    topic="propozycja_terminu",
                    language=language,
                    email_data={
                        "szkolenie": szkolenie.nazwa,
                        "imie_nazwisko": cleaned_data["imie_nazwisko"],
                    },
                )

            # Build redirect URL with query parameters
            base_url = reverse(
                "dziekujemy_za_propozycje_terminu", kwargs={"language": language}
            )
            query_params = urlencode(
                {
                    "slug": slug,
                    "email": email or "",
                    "phone": cleaned_data.get("kontakt", ""),
                    "n": cleaned_data.get("liczba_osob", 1),
                }
            )
            full_url = f"{base_url}?{query_params}"

            return HttpResponseRedirect(redirect_to=full_url)
    else:
        form = PropozycjaTerminuForm(language)

    # Render the form page
    return render(
        request,
        "www/zaproponuj_termin.html",
        {
            "szkolenie": szkolenie,
            "language": language,
            "form": form,
        },
    )


@never_cache
@activate_translation
def dziekujemy_za_propozycje_terminu(request, language):
    slug = request.GET.get("slug")

    szkolenie = get_object_or_404(Szkolenie, slug=slug, language=language)

    email = request.GET.get("email")
    phone = request.GET.get("phone")

    # Get 'n' with default value and cast it to int
    n = request.GET.get("n")

    if n is None or n == "None":
        n = 1
    else:
        n = int(n)
    # Calculate price, with a cap at 15 times the base price
    cena = min(szkolenie.cena * n, 15 * szkolenie.cena)

    # Render the response with the required context
    return render(
        request,
        "www/dziekujemy_za_propozycje_terminu.html",
        {
            "language": language,
            "kod": szkolenie.kod,
            "email": email,
            "phone": phone,
            "cena": cena,
        },
    )


@never_cache
def zgloszenie_barcode(request, id, token):
    zgloszenie = get_object_or_404(Zgloszenie, id=id, token=token)
    from . import code128

    s = "%s-%s" % (zgloszenie.id, zgloszenie.token)
    img = code128.barcodeImg(code128.codeBCFromString(s), s)
    f = io.BytesIO()
    img.save(f, "PNG")
    return HttpResponse(f.getvalue(), content_type="image/png")


@never_cache
@permission_required("www.change_terminszkolenia", raise_exception=True)
def windykacja(request):
    uczestnicy = Uczestnik.objects.all().order_by("termin")
    zalegajacy = [x for x in uczestnicy if x.zaplacone3() is False]
    for z in zalegajacy:
        z.powod = z.verbose_zaplacone3()[1]
    return render(
        request,
        "www/windykacja.html",
        {
            "zalegajacy": zalegajacy,
            "root_path": "/admin/",
        },
    )


@never_cache
@permission_required("www.change_terminszkolenia", raise_exception=True)
def niespojnosc_rat(request):
    zalegajacy = []

    for uczestnik in Uczestnik.objects.all().order_by("-termin"):
        try:
            uczestnik.sprawdz_spojnosc_rat()
        except ValidationError as e:
            uczestnik.powod = e.messages[0]
            zalegajacy.append(uczestnik)

    return render(
        request,
        "www/niespojnosc_rat.html",
        {
            "zalegajacy": zalegajacy,
            "root_path": "/admin/",
        },
    )


@never_cache
def wykladowcy(request):
    wykladowcy = Prowadzacy.objects.filter(pokazywac=True).order_by(
        "ordering", "nazwisko", "imie"
    )
    try:
        request.content_group = ContentGroup.objects.get(slug="wykladowcy")
    except ContentGroup.DoesNotExist:
        pass
    return render(
        request,
        "www/wykladowcy.html",
        {
            "wykladowcy": wykladowcy,
        },
    )


def make_uploads_serve(cls, file_field_name="plik"):
    def uploads_serve(request, path):
        objs = cls.objects.filter(**{file_field_name: path})
        if objs:
            obj = objs[0]
            mimetype, encoding = mimetypes.guess_type(
                getattr(obj, file_field_name).path
            )
            mimetype = mimetype or "application/octet-stream"
            response = HttpResponse(
                open(getattr(obj, file_field_name).path, "rb").read(),
                content_type=mimetype,
            )
            if encoding:
                response["Content-Encoding"] = encoding
            return response
        else:
            raise Http404

    return uploads_serve


uploads_referencje = user_passes_test(lambda u: u.is_staff)(
    make_uploads_serve(Referencja)
)
uploads_certyfikaty = user_passes_test(lambda u: u.is_staff)(
    make_uploads_serve(Certyfikat)
)
# TODO: Te highlighty i tak nie są zabezpieczane, można by je serwować w normalny sposób.
uploads_highlights = make_uploads_serve(Highlight, "obrazek")
uploads_testimoniale = make_uploads_serve(Testimonial, "obrazek")


@never_cache
@user_passes_test(lambda u: u.is_staff)
def reset_nginx_cache(request):
    from subprocess import call

    result = call("sudo /usr/bin/find /var/spool/nginx -type f -delete", shell=True)

    if not result:
        messages.success(request, "Cache został wyczyszczony.")
    else:
        messages.error(request, "Nie można wyczyścić cache.")

    return redirect("admin:index")


@never_cache
@permission_required("www.change_terminszkolenia", raise_exception=True)
def stats(request):
    try:
        typ_wykresu = int(request.GET.get("wykres", 5))
        assert typ_wykresu in range(1, 7)

        # 1 - szkolenia
        # 2 - uczestnicy
        # 3 - grupy
        # 4 - osobo-dni
        # 5 - notyfikacje
        # 6 - newsletter

    except Exception:
        typ_wykresu = 5

    tagi_dlugosc = dict(
        (tag_dlugosc.slug, tag_dlugosc) for tag_dlugosc in TagDlugosc.objects.all()
    )
    dlugosci = list(tagi_dlugosc.keys()) + ["wszystko"]
    podzial_na_dlugosc = lambda: dict([(dlugosc, {}) for dlugosc in dlugosci])

    years = podzial_na_dlugosc()
    months = podzial_na_dlugosc()
    weeks = podzial_na_dlugosc()
    days = podzial_na_dlugosc()
    weekdays = podzial_na_dlugosc()
    poczatek_swiata = datetime.date(2009, 1, 5)
    today = datetime.date.today()

    schemat_dlugosci = [
        (str(tagi_dlugosc.get(dlugosc, "Wszystko")), "number") for dlugosc in dlugosci
    ]
    years_available = list(range(poczatek_swiata.year, today.year + 1))
    schemat_lat = [(str(year), "number") for year in years_available]  # NOQA
    min_height = 600
    ColumnChart = gchart.corechart.ColumnChartFactory(height=min_height)
    LineChart = gchart.corechart.LineChartFactory(height=min_height)
    charts = []

    if typ_wykresu == 1:
        terminy = (
            TerminSzkolenia.objects.filter(odbylo_sie=True)
            .extra(
                where=[
                    "(select count(*) from www_uczestnik where "
                    "www_uczestnik.termin_id = www_terminszkolenia.id and "
                    "www_uczestnik.status IN (1,3)) > 0"
                ]
            )
            .select_related()
        )

        for t in terminy:
            for d in t.daty():
                day = "%d-%02d-%02d" % (d.year, d.month, d.day)
                start_of_week = d - datetime.timedelta(days=d.weekday())
                week = "%d-%02d-%02d" % (
                    start_of_week.year,
                    start_of_week.month,
                    start_of_week.day,
                )
                month = "%d-%02d" % (d.year, d.month)
                year = "%d" % (d.year,)
                weekday = d.weekday()
                for dlugosc in (t.szkolenie.tag_dlugosc.slug, "wszystko"):
                    days[dlugosc][day] = days[dlugosc].get(day, 0) + 1
                    weeks[dlugosc][week] = weeks[dlugosc].get(week, 0) + 1
                    months[dlugosc][month] = months[dlugosc].get(month, 0) + 1
                    years[dlugosc][year] = years[dlugosc].get(year, 0) + 1
                    weekdays[dlugosc][weekday] = weekdays[dlugosc].get(weekday, 0) + 1

        schema = [("Rok", "string")] + schemat_dlugosci
        data = [
            [year] + [years[dlugosc].get(str(year), 0) for dlugosc in dlugosci]
            for year in years_available
        ]
        charts.append(ColumnChart(schema, data, title="Latami"))

        schema = [("Miesiąc", "string")] + schemat_dlugosci
        data = []
        d = poczatek_swiata
        while d < today + datetime.timedelta(days=60):
            key = "%d-%02d" % (d.year, d.month)
            data.append([key] + [months[dlugosc].get(key, 0) for dlugosc in dlugosci])
            d = (d + datetime.timedelta(days=31)).replace(day=1)
        charts.append(LineChart(schema, data, title="Miesiącami"))

        for i, dlugosc in enumerate(dlugosci):
            schema = [("Miesiąc", "string")] + schemat_lat
            data = [
                [month]
                + [
                    months[dlugosc].get("%d-%02d" % (year, month), 0)
                    for year in years_available
                ]
                for month in range(1, 13)
            ]  # NOQA
            charts.append(
                LineChart(
                    schema,
                    data,
                    title="Miesiącami porównanie lat — {0}".format(
                        schemat_dlugosci[i][0]
                    ),
                )
            )

        # Tylko szkolenia
        schema = [("Dzień", "string"), ("Dni", "number")]
        data = []
        d = poczatek_swiata
        while d < today + datetime.timedelta(days=30):
            key = "%d-%02d-%02d" % (d.year, d.month, d.day)
            data.append([key, weeks["szkolenie"].get(key, 0)])
            d += datetime.timedelta(days=7)
        charts.append(ColumnChart(schema, data, title="Tygodniami — szkolenia"))

        for i, dlugosc in enumerate(dlugosci):
            schema = [("Dzień", "string")] + schemat_lat
            data = []
            for da in range(365):
                row = [da]
                for ye in years_available:
                    day = datetime.date(ye, 1, 1) + datetime.timedelta(days=da)
                    sow = day - datetime.timedelta(days=day.weekday())
                    row.append(
                        weeks[dlugosc].get(
                            "%d-%02d-%02d" % (sow.year, sow.month, sow.day), 0
                        )
                    )
                data.append(row)
            charts.append(
                LineChart(
                    schema,
                    data,
                    title="Tygodniami porównanie lat — {0}".format(
                        schemat_dlugosci[i][0]
                    ),
                )
            )

        for dlugosc, tag_dlugosc in list(tagi_dlugosc.items()):
            schema = [("Dzień", "string"), ("Dni", "number")]
            weekday_labels = {
                0: "Poniedziałek",
                1: "Wtorek",
                2: "Środa",
                3: "Czwartek",
                4: "Piątek",
                5: "Sobota",
                6: "Niedziela",
            }
            data = [
                [weekday_label, weekdays[dlugosc].get(weekday_number, 0)]
                for weekday_number, weekday_label in list(weekday_labels.items())
            ]
            charts.append(
                ColumnChart(
                    schema, data, title="Dniami tygodnia — {0}".format(tag_dlugosc)
                )
            )

        for i, dlugosc in enumerate(dlugosci):
            schema = [("Dzień", "string")] + schemat_lat
            data = []
            for da in range(365):
                row = [da]
                for ye in years_available:
                    day = datetime.date(ye, 1, 1) + datetime.timedelta(days=da)
                    row.append(
                        days[dlugosc].get(
                            "%d-%02d-%02d" % (day.year, day.month, day.day), 0
                        )
                    )
                data.append(row)
            charts.append(
                LineChart(
                    schema,
                    data,
                    title="Dniami porównanie lat — {0}".format(schemat_dlugosci[i][0]),
                )
            )
    elif typ_wykresu == 2:

        # liczba przeszkolonych w roku z podziałem na Kurs i Szkolenie.

        uczestnicy = Uczestnik.objects.filter(
            status__in=[1, 3], termin__odbylo_sie=True
        ).select_related(
            "termin", "termin__szkolenie", "termin__szkolenie__tag_dlugosc"
        )

        if uczestnicy.count():
            uczestnicy_data = {}

            years_available_plus_future = years_available[:]

            for uczestnik in uczestnicy:
                s = uczestnik.termin.szkolenie
                termin_year = str(uczestnik.termin.termin.year)

                if termin_year not in uczestnicy_data:
                    uczestnicy_data[termin_year] = dict(
                        [(dlugosc, 0) for dlugosc in dlugosci]
                    )

                if int(termin_year) not in years_available_plus_future:
                    years_available_plus_future.append(int(termin_year))

                uczestnicy_data[termin_year][
                    s.tag_dlugosc.slug
                ] += uczestnik.liczba_osob()
                uczestnicy_data[termin_year]["wszystko"] += uczestnik.liczba_osob()

            schema = [("Rok", "string")] + schemat_dlugosci
            data = [
                [year]
                + [
                    uczestnicy_data.get(str(year), {}).get(dlugosc, 0)
                    for dlugosc in dlugosci
                ]
                for year in years_available_plus_future
            ]
            charts.append(ColumnChart(schema, data, title="Liczba przeszkolonych"))

        # liczba osób na kursy w każdym z miesięcy

        uczestnicy = Uczestnik.objects.filter(
            status__in=[3, 1],
            termin__odbylo_sie=True,
            termin__szkolenie__tag_dlugosc__slug="kurs-zawodowy",
        ).select_related("termin")

        if uczestnicy.count():
            uczestnicy_data = {}
            dates_available = []

            for uczestnik in uczestnicy:
                termin_date = "%d-%02d" % (
                    uczestnik.termin.termin.year,
                    uczestnik.termin.termin.month,
                )

                if termin_date not in uczestnicy_data:
                    uczestnicy_data[termin_date] = {
                        "1": 0,
                        "3": 0,
                        "wszystko": 0,
                    }
                    dates_available.append(termin_date)

                uczestnicy_data[termin_date][
                    str(uczestnik.status)
                ] += uczestnik.liczba_osob()
                uczestnicy_data[termin_date]["wszystko"] += uczestnik.liczba_osob()

            schema = [
                ("Miesiąc", "string"),
                ("Przeszkolony", "number"),
                ("Uczestnik wszystko wie", "number"),
                ("Wszystko", "number"),
            ]
            data = [
                [date]
                + [
                    uczestnicy_data.get(date, {}).get(status, 0)
                    for status in ["3", "1", "wszystko"]
                ]
                for date in reversed(dates_available)
            ]
            charts.append(
                ColumnChart(schema, data, title="Uczestnicy kursów zawodowych w mc")
            )
    elif typ_wykresu == 3:
        # liczba grup kursowych w każdym z miesięcy

        terminy = (
            TerminSzkolenia.objects.filter(
                odbylo_sie=True, szkolenie__tag_dlugosc__slug="kurs-zawodowy"
            )
            .filter(
                Q(termin_nadrzedny__isnull=True) | Q(termin_nadrzedny__odbylo_sie=False)
            )
            .extra(
                where=[
                    "(select count(*) from www_uczestnik where "
                    "www_uczestnik.termin_id = www_terminszkolenia.id and "
                    "www_uczestnik.status IN (1,3)) > 0"
                ]
            )
            .select_related()
        )

        if terminy.count():
            terminy_dates = {}
            terminy_quarters = {}

            dates_available = []
            quarter_available = []

            quarters_map = {
                1: "IV",
                2: "I",
                3: "I",
                4: "I",
                5: "II",
                6: "II",
                7: "II",
                8: "III",
                9: "III",
                10: "III",
                11: "IV",
                12: "IV",
            }

            for termin in terminy:
                termin_date = "%d-%02d" % (termin.termin.year, termin.termin.month)
                termin_quarter = "%d-%s" % (
                    (
                        termin.termin.year - 1
                        if termin.termin.month == 1
                        else termin.termin.year
                    ),
                    quarters_map[termin.termin.month],
                )

                if termin_date not in terminy_dates:
                    terminy_dates[termin_date] = 0
                    dates_available.append(termin_date)

                if termin_quarter not in terminy_quarters:
                    terminy_quarters[termin_quarter] = 0
                    quarter_available.append(termin_quarter)

                terminy_dates[termin_date] += 1
                terminy_quarters[termin_quarter] += 1

            schema = [("Miesiąc", "string"), ("Liczba grup", "number")]
            data = [[date, terminy_dates.get(date, 0)] for date in dates_available]
            charts.append(
                ColumnChart(
                    schema, data, title="Liczba grup kursowych w każdym z miesięcy"
                )
            )

            schema = [("Kwartał", "string"), ("Liczba grup", "number")]
            data = [
                [quarter, terminy_quarters.get(quarter, 0)]
                for quarter in quarter_available
            ]
            charts.append(
                ColumnChart(
                    schema, data, title="Liczba grup kursowych w każdym z kwartałów"
                )
            )
    elif typ_wykresu == 4:
        # liczba osobo-dni na Jasnej w danym roku

        uczestnicy = Uczestnik.objects.filter(
            status__in=[3, 1],
            termin__odbylo_sie=True,
            termin__lokalizacja__ulica_i_numer__icontains="jasna",
            termin__lokalizacja__shortname__icontains="warszawa",
        ).select_related("termin")

        uczestnicy_data = {}

        years_available_plus_future = years_available[:]

        for uczestnik in uczestnicy:
            termin_year = str(uczestnik.termin.termin.year)

            if termin_year not in uczestnicy_data:
                uczestnicy_data[termin_year] = 0

            if int(termin_year) not in years_available_plus_future:
                years_available_plus_future.append(int(termin_year))

            uczestnicy_data[
                str(uczestnik.termin.termin.year)
            ] += uczestnik.liczba_osob() * len(list(uczestnik.termin.daty()))

        schema = [("Rok", "string"), ("Liczba osobo-dni", "number")]
        data = [
            [year, uczestnicy_data.get(str(year), 0)]
            for year in years_available_plus_future
        ]
        charts.append(
            ColumnChart(schema, data, title="Liczba osobo-dni na Jasnej w danym roku")
        )
    elif typ_wykresu == 5:
        # Statystyki dla Powiadomień

        locations = Lokalizacja.objects.filter(reklamowana=True)

        notifications = list(
            UserCoursesNotification.objects.stats_query(
                locations,
                user__status=1,
                user__created_at__gte=datetime.datetime(2015, 1, 1),
            )
        )

        if notifications:
            rows = {}
            for row in notifications:
                rows[row["month"]] = row

            schema = [("Miesiąc", "string")] + [
                (l.fullname, "number") for l in locations
            ]
            data = []

            d = notifications[0]["month"]
            ends_at = datetime.datetime(today.year, today.month, 1)

            while d <= ends_at:
                key = "%d-%02d" % (d.year, d.month)

                row = rows.get(d)

                if row:
                    data.append([key] + [row.get(str(l.pk), 0) for l in locations])
                else:
                    data.append([key] + [0 for __ in locations])

                d = (d + datetime.timedelta(days=31)).replace(day=1)
            charts.append(LineChart(schema, data, title="Notyfikacje - Miesiącami"))
    elif typ_wykresu == 6:
        # Statystyki - Newsletter
        recipients = Odbiorca.objects.stats_query(
            status="potwierdzony", czas_zgloszenia__gte=datetime.datetime(2012, 9, 1)
        )

        if recipients:
            rows = {}
            for row in recipients:
                rows[row["month"]] = row

            sources = OrderedDict(Odbiorca.ZRODLA_KONTAKTU)

            schema = [("Miesiąc", "string")] + [
                (v, "number") for k, v in list(sources.items())
            ]
            data = []

            d = recipients[0]["month"]
            ends_at = datetime.datetime(today.year, today.month, 1)

            while d <= ends_at:
                key = "%d-%02d" % (d.year, d.month)

                row = rows.get(d)

                if row:
                    data.append([key] + [row.get(source, 0) for source in sources])
                else:
                    data.append([key] + [0 for __ in sources])

                d = (d + datetime.timedelta(days=31)).replace(day=1)
            charts.append(LineChart(schema, data, title="Newsletter - Miesiącami"))
    return render(
        request, "www/stats.html", {"charts": charts, "typ_wykresu": typ_wykresu}
    )


@never_cache
def sciezki(request, language=settings.DEFAULT_LANGUAGE):
    sciezki = Sciezka.objects.filter(language=language)
    return render(
        request,
        "www/sciezki.html",
        {
            "sciezki": sciezki,
        },
    )


@never_cache
@activate_translation
def sciezki_detail(request, slug, language=settings.DEFAULT_LANGUAGE):
    sciezka = get_object_or_404(Sciezka, language=language, slug=slug)
    return render(
        request,
        "www/sciezki_detail.html",
        {
            "sciezka": sciezka,
            "highlights": get_highlights(sciezka),
        },
    )


def polska_data(d, z_rokiem=True):
    # pomyślałby kto, że locale da coś takiego
    months = [
        "stycznia",
        "lutego",
        "marca",
        "kwietnia",
        "maja",
        "czerwca",
        "lipca",
        "sierpnia",
        "września",
        "października",
        "listopada",
        "grudnia",
    ]
    if z_rokiem:
        return "%d %s %d" % (d.day, months[d.month - 1], d.year)
    else:
        return "%d %s" % (d.day, months[d.month - 1])


@never_cache
@permission_required("www.change_terminszkolenia", raise_exception=True)
def lista_obecnosci(request, termin_id, z_obiadami=False):
    # Zakładamy, że jak wyświetlamy obiady, to chcemy wyświetlić też
    # autoryzacje i certyfikaty
    z_autoryzacjami = z_obiadami
    z_certyfikatami = z_obiadami
    import zipfile

    termin = get_object_or_404(TerminSzkolenia, id=int(termin_id))
    ndni = None
    termin_rozpoczecia = termin.termin
    termin_zakonczenia = None
    if termin.szkolenie.tag_dlugosc.slug == "szkolenie":
        if termin.ile_dni:
            ndni = termin.ile_dni
        elif termin.termin_zakonczenia:
            ndni = (termin.termin_zakonczenia - termin.termin).days + 1
        elif termin.szkolenie.czas_dni:
            ndni = termin.szkolenie.czas_dni
        if ndni:
            if ndni > 5:
                ndni = 5
            termin_zakonczenia = termin.termin + datetime.timedelta(days=ndni - 1)
            dni = [
                polska_data(termin.termin + datetime.timedelta(days=x), z_rokiem=False)
                for x in range(ndni)
            ]
        else:
            dni = [""]
    else:
        dni = ["", ""]
    do_str = ""
    if termin_zakonczenia:
        do_str = " – %s" % polska_data(termin_zakonczenia)
    w_dniach = "%s%s" % (polska_data(termin_rozpoczecia), do_str)
    ludzie = termin.uczestnicy_niezrezygnowani_indywidualni()
    jedyna_firma = None
    uczestnicy_n = list(
        termin.uczestnicy_niezrezygnowani(not_statuses=[-3, -2, -1, 0, 3, 5, 4])
    )
    if (
            len(uczestnicy_n) == 1
            and uczestnicy_n[0].uczestnik_wieloosobowy_ilosc_osob
            and uczestnicy_n[0].uczestnik_wieloosobowy_ilosc_osob > 0
    ):
        jedyna_firma = uczestnicy_n[0].faktura_firma
    # mnoże przez dwa, bo jednostką jest połowa punkta
    wielkosc_liter_w_tabeli = 2 * (14 if len(dni) < 4 else 12)
    szerokosc = {
        "razem": 14318,
        "imie_nazwisko": 2919,
        "obiady": 625,
        "autoryzacje": 625,
        "id": 992,
    }
    if not z_obiadami:
        szerokosc["obiady"] = 0
    if not z_autoryzacjami:
        szerokosc["autoryzacje"] = 0
    if len(dni) > 4:
        szerokosc["id"] = int(szerokosc["id"] * 0.75)
    szerokosc["data"] = (
                                szerokosc["razem"]
                                - (
                                        szerokosc["imie_nazwisko"]
                                        + szerokosc["obiady"]
                                        + szerokosc["autoryzacje"]
                                        + szerokosc["id"] * len(dni)
                                )
                        ) / len(dni)
    doc = render_to_string(
        "www/lista_obecnosci.xml",
        {
            "ludzie": ludzie,
            "dni": dni,
            "termin": termin,
            "sala": str(termin.sala),
            "w_dniach": w_dniach,
            "jedyna_firma": jedyna_firma,
            "szerokosc": szerokosc,
            "wielkosc_liter_w_tabeli": wielkosc_liter_w_tabeli,
            "z_obiadami": z_obiadami,
            "z_autoryzacjami": z_autoryzacjami,
            "z_certyfikatami": z_certyfikatami,
        },
        request,
    )
    with open(settings.LISTA_OBECNOSCI_TEMPLATE, "rb") as f:
        fakefile = io.BytesIO(f.read())
    zipf = zipfile.ZipFile(fakefile, mode="a")
    zipf.writestr("word/document.xml", doc)
    zipf.close()
    response = HttpResponse(
        fakefile.getvalue(),
        content_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    )
    response["Content-Disposition"] = "attachment; filename=%s.docx" % termin.id
    return response


@never_cache
@permission_required("www.change_terminszkolenia", raise_exception=True)
def wykladowca_zestawienie(request, prowadzacy_id):
    def policz_dni(qs):
        return sum([x if x is not None else 0 for x in [y.get_ile_dni() for y in qs]])

    prowadzacy = get_object_or_404(Prowadzacy, id=int(prowadzacy_id))
    terminy = (
        TerminSzkolenia.objects.distinct()
        .filter(
            prowadzacy=prowadzacy,
            szkolenie__tag_dlugosc__slug="szkolenie",
            odbylo_sie=True,
        )
        .exclude(szkolenie__kod__in=["SnZ", "KI"])
    )
    dni_total = policz_dni(terminy)
    office_tags = [
        "office",
        "excel",
        "access",
        "powerpoint",
        "word",
        "outlook",
        "microsoft-autoryzowane",
        "project",
        "vba",
    ]
    office = terminy.filter(szkolenie__tagi_technologia__slug__in=office_tags)
    dni_office = policz_dni(office)
    project = terminy.filter(szkolenie__tagi_technologia__slug="project")
    dni_project = policz_dni(project)
    excel = terminy.filter(szkolenie__tagi_technologia__slug="excel")
    dni_excel = policz_dni(excel)
    excel_szkolenia = {}
    for t in excel:
        dni = t.get_ile_dni()
        if dni:
            excel_szkolenia[t.szkolenie.kod] = (
                    excel_szkolenia.get(t.szkolenie.kod, 0) + dni
            )
    excel_szczegolowo = []
    for k in sorted(excel_szkolenia.keys()):
        excel_szczegolowo.append([k, excel_szkolenia[k]])

    return render(
        request,
        "www/wykladowca_zestawienie.html",
        {
            "prowadzacy": prowadzacy,
            "dni_total": dni_total,
            "dni_office": dni_office,
            "dni_project": dni_project,
            "dni_excel": dni_excel,
            "excel_szczegolowo": excel_szczegolowo,
            "office_tags": office_tags,
        },
    )


##################
# Powiadomienia
##################


@never_cache
@activate_translation
def subscription_form(request, training_id, language=settings.DEFAULT_LANGUAGE):
    """
    Widok serwujący formularz zapisu na subskrypcję. Dostępny jedynie via AJAX.
    """

    if not request.is_ajax():
        raise Http404

    mode = request.GET.get("mode", "")
    training = get_object_or_404(
        Szkolenie, pk=training_id, aktywne=True, language=language
    )

    if language == "en":
        # Dla wersji EN pobieramy lokalizacje tylko w Wielkiej Brytanii
        # (obecnie to tylko Londyn)
        locations = Lokalizacja.objects.filter(
            reklamowana=True, panstwo__nazwa__iexact="Wielka Brytania"
        )
    else:
        if mode == "online_only":
            # wybieramy tylko zdalnie
            locations = Lokalizacja.objects.filter(shortname="Zdalnie")
        else:
            locations = Lokalizacja.objects.filter(reklamowana=True).order_by(
                "panstwo", "numer"
            )

    grouped_locations = [
        locations.filter(numer__lt=100),
        locations.filter(numer__gte=100, numer__lt=200),
        locations.filter(numer__gte=200, numer__lt=300),
        locations.filter(numer__gte=300),
    ]

    form_kwargs = {
        "language": language,
        "grouped_locations": grouped_locations,
    }

    if request.method == "POST":
        form = SubscriptionForm(request.POST, **form_kwargs)

        if form.is_valid():
            user, course, send_activation_email = form.save(training=training)

            # Uzpełniamy informacje o adresie IP i przeglądarce - jeśli nie
            # istnieją.
            save_instance = not (user.remote_addr and user.user_agent)

            if not user.remote_addr:
                user.remote_addr = get_client_ip(request)[0]
            if not user.user_agent:
                user.user_agent = request.META["HTTP_USER_AGENT"]

            if save_instance:
                user.save()

            email_data = {
                "training": training,
                "locations": course.locations.all(),
                "user": user,
                "domain": settings.DOMENY_DLA_JEZYKOW[language],
                "protocol": settings.FORCE_SSL and "https" or "http",
            }

            if send_activation_email:
                # Użytkownik niepotwierdzony wysyłamy maila aktywacyjnego.
                subject = _(
                    "[Wymagana reakcja] Potwierdź chęć otrzymywania "
                    "powiadomień z ALX (www.alx.pl)"
                )
                msg_content = render_to_string(
                    "www/notifications/activation_email_{0}.html".format(language),
                    email_data,
                )
            else:
                # Wysyłamy tylko informacyjnego maila do użytkownika.
                subject = _("Nowa subskrypcja na ALX (www.alx.pl)")
                msg_content = render_to_string(
                    "www/notifications/new_subscription_email_{0}.html".format(
                        language
                    ),
                    email_data,
                )

            msg = EmailMessage(
                subject,
                msg_content,
                (
                    settings.MAIL_FROM_ADDRESS
                    if language == "pl"
                    else settings.MAIL_FROM_ADDRESS_EN
                ),
                [user.email],
            )
            msg.content_subtype = "html"
            msg.send()

            return render(
                request,
                "www/notifications/ajax_subscription_done.html",
                {
                    "activation_email": user.email,
                    "send_activation_email": send_activation_email,
                    "mode": mode,
                    "training": training,
                },
            )
    else:
        form = SubscriptionForm(
            initial={"email": request.GET.get("email", "")}, **form_kwargs
        )

    return render(
        request,
        "www/notifications/ajax_subscription_form.html",
        {
            "training": training,
            "language": language,
            "form": form,
        },
    )


@never_cache
@activate_translation
def postivo_callback(request, key, language=settings.DEFAULT_LANGUAGE):
    """
    Widok pozwlający na zmianę statusu przesyłki.
    """

    obj = get_object_or_404(FakturaWysylka, key=key, dispatched_at__isnull=False)

    dispatch_id = strip_tags(request.GET.get("dispatch_id", ""))
    status_code = strip_tags(request.GET.get("status_code", ""))

    try:
        date = parser.parse(strip_tags(request.GET.get("date")))
    except:
        date = None

    if dispatch_id and status_code and date and obj.dispatch_id == dispatch_id:
        obj.status = status_code
        obj.status_date = date
        obj.save(update_fields=["status", "status_date", "updated_at"])
        return HttpResponse()

    logger.error("[postivo callback] No required data.")
    raise Http404


def continuation_alert_mail(continuation):
    msg_content = render_to_string(
        "www/notifications/user_deleted_continuation_alert.html",
        {
            "obj": continuation,
            "domain": settings.DOMENY_DLA_JEZYKOW["pl"],
            "protocol": settings.FORCE_SSL and "https" or "http",
        },
    )

    msg = EmailMessage(
        "[Kontynuacje Wypis] - {0}".format(continuation.email.lower()),
        msg_content,
        settings.MAIL_FROM_ADDRESS,
        [settings.MAIL_TO_NOTIFICATION_ALERT],
        [settings.MAIL_ZGLOSZENIE_TO_ADDRESS],
    )
    msg.content_subtype = "html"
    msg.send()


@never_cache
@activate_translation
def cancel_continuation(request, token, language=settings.DEFAULT_LANGUAGE):
    """
    Widok deaktywujący subskrypcję użytkownika. Oznaczamy go jako usunięty.
    """

    continuation = None

    try:
        email = (
            signing.loads(token, salt="signing.continuation_training")["email"]
            .lower()
            .strip()
        )
    except:
        logger.exception("Proba wypisu z kontynuacji z blednym tokenem.")
    else:
        continuation = ContinuationUnsubscribed.objects.filter(
            email__iexact=email
        ).first()
        if continuation is None:
            continuation, created = ContinuationUnsubscribed.objects.get_or_create(
                email=email
            )
            if created:
                continuation_alert_mail(continuation)

    return render(
        request,
        "www/notifications/cancel_continuation.html",
        {
            "language": language,
            "continuation_unsubscribed": continuation,
        },
    )


@never_cache
@activate_translation
def manage_subscriptions(request, key, language=settings.DEFAULT_LANGUAGE):
    """
    Widok pozwlający na zarządzanie swoimi subskrypcjami.
    """

    obj = get_object_or_404(UserNotification, key=key, status=1)

    # Pobieramy wszystkie notyfikacje użytkownika
    courses = (
        UserCoursesNotification.objects.filter(user=obj)
        .select_related("training", "user")
        .prefetch_related("locations")
        .order_by("-created_at")
    )
    old_query_list = www.tasks.notification_qs_to_dict(courses)

    if language == "en":
        # Dla wersji EN pobieramy lokalizacje tylko w Wielkiej Brytanii
        # (obecnie to tylko Londyn)
        locations = Lokalizacja.objects.filter(
            reklamowana=True, panstwo__nazwa__iexact="Wielka Brytania"
        )
    else:
        locations = Lokalizacja.objects.filter(reklamowana=True).order_by(
            "panstwo", "shortname"
        )

    SubscriptionFormSet = modelformset_factory(
        UserCoursesNotification,
        can_order=False,
        can_delete=True,
        extra=0,
        form=get_manage_subscription_form(locations),
    )

    formset_kwargs = {
        "queryset": courses,
    }

    form_kwargs = {
        "instance": obj,
    }

    # def update_rodo(user):
    #     if not user.tos_agreement:
    #         user.tos_agreement = datetime.datetime.now()
    #         user.save()

    # Formset do zarządzania sybskrypcją.
    if request.method == "POST" and "manage_subscription_form" in request.POST:
        formset = SubscriptionFormSet(request.POST, **formset_kwargs)

        if formset.is_valid():
            has_changed = formset.has_changed()
            formset.save()
            messages.success(request, _("Zmiany zostały zapisane."))

            if has_changed:
                # Wyślij podsumowanie zmian do Biura
                www.tasks.user_notifications_alert.delay(
                    obj.id, old_query_list=old_query_list
                )

            return redirect(request.path)
    else:
        formset = SubscriptionFormSet(**formset_kwargs)

    # Formularz do opcjonalnych danych osobowych.
    if request.method == "POST" and "personal_data_form" in request.POST:
        form = UserNotificationPersonalDataForm(request.POST, **form_kwargs)

        if form.is_valid():
            form.save()
            messages.success(request, _("Zmiany zostały zapisane."))

            return redirect(request.path)
    else:
        form = UserNotificationPersonalDataForm(**form_kwargs)

    return render(
        request,
        "www/notifications/manage_subscriptions.html",
        {
            "language": language,
            "formset": formset,
            "user_notification": obj,
            "courses_count": courses.count(),
            "personal_data_form": form,
        },
    )


def notification_alert_mail(user_notification):
    msg_content = render_to_string(
        "www/notifications/user_deleted_notifications_alert.html",
        {
            "user": user_notification,
            "domain": settings.DOMENY_DLA_JEZYKOW["pl"],
            "protocol": settings.FORCE_SSL and "https" or "http",
        },
    )

    msg = EmailMessage(
        "[Powiadomienie Wypis] - {0}".format(user_notification.email.lower()),
        msg_content,
        settings.MAIL_FROM_ADDRESS,
        [settings.MAIL_TO_NOTIFICATION_ALERT],
        [settings.MAIL_ZGLOSZENIE_TO_ADDRESS],
    )
    msg.content_subtype = "html"
    msg.send()


@never_cache
@activate_translation
def cancel_subscription(request, key, language=settings.DEFAULT_LANGUAGE):
    """
    Widok deaktywujący subskrypcję użytkownika. Oznaczamy go jako usunięty.
    """

    user_notification = get_object_or_404(UserNotification, key=key)

    # Deaktywuj tylko aktywną subskrypcję, dla reszty pokaż sam komunikat
    # (zamiast 404).
    if user_notification.status == 1:
        user_notification.set_as_cancelled()
        notification_alert_mail(user_notification)

    return render(
        request,
        "www/notifications/cancel_subscription.html",
        {
            "language": language,
            "user_notification": user_notification,
        },
    )


@never_cache
@activate_translation
def activate_subscription(request, key, language=settings.DEFAULT_LANGUAGE):
    """
    Widok aktywujący subskrypcję użytkownika.
    """

    obj = get_object_or_404(UserNotification, key=key)

    # Flaga mówiąca o tym, czy na stronie wyświetlamy kody trackujące dla
    # GoogleAnalitics. Wyświetlamy je tylko raz podczas aktywacji konta.
    ga_tracking = False

    # Aktywuj tylko taką subskrypcję, która tego wymaga, a dla reszty pokaż
    # sam komunikat (zamiast 404).
    if obj.status in [-1, 0, 2]:
        obj.set_as_activated()

        ga_tracking = True

        # Użytkownik się aktywuje. Zakładamy, więc, że jego wszystkie
        # notyfikacje są nowe i informujemy o tym fakcie Biuro.
        www.tasks.user_notifications_alert.delay(
            obj.id,
            new_query_list=www.tasks.notification_qs_to_dict(
                obj.preferences.all().prefetch_related("locations")
            ),
        )

    form_kwargs = {
        "instance": obj,
    }

    # Formularz do opcjonalnych danych osobowych.
    if request.method == "POST" and "personal_data_form" in request.POST:
        form = UserNotificationPersonalDataForm(request.POST, **form_kwargs)

        if form.is_valid():
            form.save()
            messages.success(request, _("Zmiany zostały zapisane."))
            return redirect(request.path)
    else:
        form = UserNotificationPersonalDataForm(**form_kwargs)

    return render(
        request,
        "www/notifications/activate_subscription.html",
        {
            "language": language,
            "user_notification": obj,
            "ga_tracking": ga_tracking,
            "personal_data_form": form,
        },
    )


#########################
# Historie absolwentów
########################


@never_cache
@activate_translation
def graduates(request, language=settings.DEFAULT_LANGUAGE):
    graduates = list(GraduateStory.objects.filter(is_active=True))
    other_graduates = graduates[3:]
    empty_graduates = 4 - len(other_graduates)

    return render(
        request,
        "www/graduates.html",
        {
            "graduates": graduates,
            "other_graduates": other_graduates,
            "empty_graduates": (
                list(range(empty_graduates)) if empty_graduates > 0 else []
            ),
            "recaptcha_sitekey": settings.NORECAPTCHA_INVISIBLE_SITE_KEY,
        },
    )


@never_cache
@activate_translation
def graduate_story(request, slug, language=settings.DEFAULT_LANGUAGE):
    obj = get_object_or_404(GraduateStory, is_active=True, slug=slug)
    graduates = GraduateStory.objects.filter(is_active=True).exclude(pk=obj.pk)
    return render(
        request,
        "www/graduate_story.html",
        {
            "person": obj,
            "graduates": graduates,
            "recaptcha_sitekey": settings.NORECAPTCHA_INVISIBLE_SITE_KEY,
        },
    )


##################
# E-certyfikaty
##################


@never_cache
@activate_translation
def certificate(request, slug, key, language=settings.DEFAULT_LANGUAGE):
    """
    Widok serwujący uczestnikowi certyfikat w formie HTML oraz możliwość jego
    pobrania w PDF.
    """

    obj = get_object_or_404(
        Graduate.objects.select_related(), key=key, slug=slug, is_active=True
    )

    # Formularz wysyłania certyfikatu via email
    if request.method == "POST":
        form = EmailCertificate(request.POST)

        if form.is_valid():
            msg_content = render_to_string(
                "www/certificates/share_via_email_{0}.html".format(language),
                {
                    "certificate": obj,
                    "language": language,
                    "domain": settings.DOMENY_DLA_JEZYKOW[language],
                    "protocol": settings.FORCE_SSL and "https" or "http",
                },
            )

            msg = EmailMessage(
                _("Zobacz mój certyfikat"),
                msg_content,
                obj.email,
                [form.cleaned_data["email"]],
            )
            msg.content_subtype = "html"
            msg.send()

            messages.success(request, _("Wiadomość została wysłana."))
            return redirect(request.path)
    else:
        form = EmailCertificate()

    term = obj.term

    return render(
        request,
        "www/certificates/certificate.html",
        {
            "form": form,
            "certificate": obj,
            "term": term,
            "pokazuj_notyfikacje": True,
            "term_date": term.date_range(),
            "term_days": len(list(term.daty())),
            "language": language,
            "domain": settings.DOMENY_DLA_JEZYKOW[language],
            "protocol": settings.FORCE_SSL and "https" or "http",
            "facebook_app_id": settings.SHARING_FACEBOOK_APP_ID,
            "linkedin_certificate_url": settings.LINKEDIN_CERTIFICATE_URL,
            "years_delta": datetime.date.today().year - 2002,
        },
    )


@never_cache
@activate_translation
def public_certificate(request, slug, public_key, language=settings.DEFAULT_LANGUAGE):
    """
    Widok serwujący certyfikat do publicznego sherowania.
    """

    obj = get_object_or_404(
        Graduate.objects.select_related(),
        public_key=public_key,
        slug=slug,
        is_active=True,
    )
    term = obj.term

    return render(
        request,
        "www/certificates/public_certificate.html",
        {
            "certificate": obj,
            "term": term,
            "term_date": term.date_range(),
            "term_days": len(list(term.daty())),
            "language": language,
            "domain": settings.DOMENY_DLA_JEZYKOW[language],
            "protocol": settings.FORCE_SSL and "https" or "http",
            "facebook_app_id": settings.SHARING_FACEBOOK_APP_ID,
            "years_delta": datetime.date.today().year - 2002,
        },
    )


@never_cache
def get_certificate_pdf(request, slug, key):
    """
    Widok serwujący uczestnikowi certyfikat w formie PDF.
    """

    obj = get_object_or_404(
        Graduate.objects.select_related(), key=key, slug=slug, is_active=True
    )

    if not obj.pdf:
        raise Http404

    return file_to_response(obj.slug, obj.pdf)


#######
# RODO
#######


@never_cache
def rodo(request, activation_type):
    """
    Aktywacja zgód dla RODO
    """

    key = request.GET.get("key", "")

    if not key:
        raise Http404

    try:
        email = signing.loads(key)
    except:
        pass
    else:
        if activation_type in ("notification-and-newsletter", "newsletter"):
            # Szukamy w newsletterze (tam może być kilka takich samych kont)
            try:
                newsletter = Odbiorca.objects.filter(
                    email__iexact=email,
                    tos_agreement__isnull=True,
                    status="potwierdzony",
                    lang="pl",
                )[0]
            except IndexError:
                pass
            else:
                newsletter.tos_agreement = datetime.datetime.now()
                newsletter.save()

        if activation_type in ("notification-and-newsletter", "notification"):
            # Szukamy w powiadomieniach
            try:
                notification = UserNotification.objects.filter(
                    email__iexact=email,
                    tos_agreement__isnull=True,
                    status=1,
                    source="www",
                    language="pl",
                )[0]
            except IndexError:
                pass
            else:
                notification.tos_agreement = datetime.datetime.now()
                notification.save()

    return render(
        request,
        "www/rodo.html",
        {
            "activation_type": activation_type,
        },
    )


#######
# Inne
#######


@never_cache
@activate_translation
def cancel_all_subscriptions(request, language=settings.DEFAULT_LANGUAGE):
    """
    Widok pozwlający na rozpoczęcie procesu wypisana ze wszystkich subskrypcji.
    """

    cache_key = "cancel_all_subscriptions:{0}".format(get_client_ip(request)[0])
    action_allowed = cache.get(cache_key) is None

    if request.method == "POST":
        form = EmailForm(request.POST, action_allowed=action_allowed)

        if form.is_valid():

            cache.set(cache_key, "1", 30 * 60)

            email = form.cleaned_data["email"].lower()

            notification = (
                UserNotification.objects.get_active()
                .filter(email__iexact=email)
                .exists()
            )
            continuation = (
                    Uczestnik.objects.filter(email__iexact=email).exists()
                    and not ContinuationUnsubscribed.objects.filter(
                email__iexact=email
            ).exists()
            )
            newsletter = Odbiorca.objects.filter(
                email__iexact=email, status="potwierdzony"
            ).exists()

            if notification or continuation or newsletter:
                msg_content = render_to_string(
                    "www/emails/cancel_all_subscriptions_{0}.html".format(language),
                    {
                        "key": signing.dumps(
                            {"email": email}, salt="signing.cancel_all_subscriptions"
                        ),
                        "language": language,
                        "domain": settings.DOMENY_DLA_JEZYKOW[language],
                        "protocol": settings.FORCE_SSL and "https" or "http",
                    },
                )

                msg = EmailMessage(
                    _("Rezygnacja z powiadomień"),
                    msg_content,
                    settings.MAIL_FROM_ADDRESS_NOTIFICATION,
                    [email],
                )
                msg.content_subtype = "html"
                msg.send()

            messages.success(
                request,
                _(
                    "Rozpoczęlismy sprawdzanie, czy twój adres email znajduje się na "
                    "naszych listach. Jeśli tak wyślemy do ciebie maila z linkiem "
                    "potwierdzającym wypisanie z naszych list mailingowych."
                ),
            )

            return redirect(request.path)
    else:
        form = EmailForm(action_allowed=action_allowed)

    return render(
        request,
        "www/notifications/cancel_all_subscriptions.html",
        {
            "language": language,
            "form": form,
        },
    )


def newsletter_alert_mail(newsletter):
    msg_content = render_to_string(
        "www/notifications/user_deleted_newsletter_alert.html",
        {
            "user": newsletter,
            "domain": settings.DOMENY_DLA_JEZYKOW["pl"],
            "protocol": settings.FORCE_SSL and "https" or "http",
        },
    )

    msg = EmailMessage(
        "[Newsletter Wypis] - {0}".format(newsletter.email.lower()),
        msg_content,
        settings.MAIL_FROM_ADDRESS,
        [settings.MAIL_TO_NOTIFICATION_ALERT],
        [settings.MAIL_ZGLOSZENIE_TO_ADDRESS],
    )
    msg.content_subtype = "html"
    msg.send()


@never_cache
@activate_translation
def do_cancel_all_subscriptions(request, token, language=settings.DEFAULT_LANGUAGE):
    """
    Widok pozwlający na wypisane się ze wszystkich subskrypcji.
    """

    try:
        email = signing.loads(token, salt="signing.cancel_all_subscriptions")[
            "email"
        ].lower()
    except:
        logger.exception("Proba wypisu z wszystkich powiadomien z blednym tokenem.")
        email = None
    else:
        ###############
        # continuation
        ###############
        if Uczestnik.objects.filter(email__iexact=email).exists():
            if not ContinuationUnsubscribed.objects.filter(
                    email__iexact=email
            ).exists():
                continuation = ContinuationUnsubscribed.objects.create(email=email)
                continuation_alert_mail(continuation)

        ###############
        # notification
        ###############
        user_notification = (
            UserNotification.objects.get_active().filter(email__iexact=email).first()
        )
        if user_notification:
            user_notification.set_as_cancelled()

            notification_alert_mail(user_notification)

        ###############
        # newsletter
        ###############
        newsletter = Odbiorca.objects.filter(
            email__iexact=email, status="potwierdzony"
        ).first()
        if newsletter:
            newsletter.status = "opt-out"
            newsletter.save()

            newsletter_alert_mail(newsletter)

    return render(
        request,
        "www/notifications/do_cancel_all_subscriptions.html",
        {
            "language": language,
            "email": email,
        },
    )


def prowadzacy_autocomplete(request):
    if request.is_ajax():
        term = request.GET.get("q", "")  # `q` to parametr wyszukiwania w Select2
        prowadzący = (
                Prowadzacy.objects.filter(imie__icontains=term)
                | Prowadzacy.objects.filter(nazwisko__icontains=term)[:10]
        )  # Maksymalnie 10 wyników

        results = []
        for prow in prowadzący:
            prow_data = {
                "id": prow.id,
                "text": prow.imie_naziwsko(),  # Używamy metody imie_naziwsko
            }
            results.append(prow_data)
        return JsonResponse({"results": results})



def tech_root_404(request, *args, **kwargs):
    """Return a 404 for bare /tech/ and /pl/tech/ to avoid redirect loops."""
    raise Http404
