import datetime
import os
import re
from decimal import Decimal

from django import forms
from django.conf import settings
from django.core.validators import EMPTY_VALUES, validate_email
from django.db.models import ForeignKey, ManyToManyField, OneToOneField, Q
from django.forms import ValidationError, widgets
from django.forms.fields import MultiValueField
from django.utils import six
from django.utils.encoding import force_text
from django.utils.html import conditional_escape, strip_tags
from django.utils.safestring import mark_safe
from django.utils.translation import ugettext_lazy as _
from localflavor.pl.forms import PLNIPField, PLPESELField, PLPostalCodeField
from nocaptcha_recaptcha.fields import NoReCaptchaField

import captcha.forms
import www.tasks
from i18n.models import WersjaJezykowa
from .api import update_invoice_status
from .models import (
    ZAPLACE_CHOICES,
    DiscountCode,
    DzienSzkolenia,
    FakturaKorekta,
    FakturaWysylka,
    Leave,
    Lokalizacja,
    MappedTranslatedModel,
    MyFlatPage,
    PotencjalnyChetny,
    Prowadzacy,
    Szkolenie,
    Tab,
    TerminSzkolenia,
    TerminSzkoleniaMail,
    TranslatedModel,
    Uczestnik,
    UczestnikPlik,
    UserCoursesNotification,
    UserNotification,
    Zgloszenie,
    parse_miejscowosc_kod,
)
from .utils import check_invoice_data, generate_invoice_note_data, is_html
from .widgets import (
    CustomAceWidget,
    LargeTextarea,
    MultipleDatePicker,
    NettoBruttoInput,
    SelectWithDataAttr,
    SmallTextarea,
)


class CustomPLNIPField(PLNIPField):
    default_error_messages = {
        "invalid": "Wprowadź numer identyfikacji podatkowej (NIP) w jednym "
        "z trzech formatów: 'XXX-XXX-XX-XX', 'XXX-XX-XX-XXX' lub "
        "'XXXXXXXXXX'.",
        "checksum": "Błędna suma kontrolna numeru NIP.",
    }

    def clean(self, value):
        super().clean(value)
        if value in EMPTY_VALUES:
            return ""
        re_value = re.sub("[-]", "", value)
        if not self.has_valid_checksum(re_value):
            raise ValidationError(self.error_messages["checksum"])
        return "%s" % value


class ModelFormI18n(forms.ModelForm):
    """Klasa filtrująca queryset w polach relacyjnych formularza na podstawie
    języka obiektu."""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        language = self.initial.get("language", None) or self.data.get("language", None)
        if language is not None:
            model = self._meta.model
            model_fields = model._meta.fields + model._meta.many_to_many
            i18n_model_fields = [
                field
                for field in model_fields
                if isinstance(field, (ForeignKey, ManyToManyField, OneToOneField))
                and issubclass(field.remote_field.model, TranslatedModel)
                and field.name != "base_translation"
                and field.editable
            ]
            for model_field in i18n_model_fields:
                if model_field in self.fields:
                    self.fields[model_field.name].queryset = self.fields[
                        model_field.name
                    ].queryset.filter(language=language)


# po to, że chcę, żeby pole się wyświelało jako required, ale walidowało się warunkowo
# zależnie od innych


class ChoiceFieldIgnoreRequired(forms.ChoiceField):
    def validate(self, value):
        pass


class HorizontalRadioSelect(forms.RadioSelect):
    """this overrides widget method to put radio buttons horizontally instead of
    vertically."""

    template_name = "widgets/horizontal_select.html"


class InvisibleCaptchaContactForm(forms.Form):
    captcha = NoReCaptchaField(
        site_key=settings.NORECAPTCHA_INVISIBLE_SITE_KEY,
        secret_key=settings.NORECAPTCHA_INVISIBLE_SECRET_KEY,
    )


class CaptchaContactForm(forms.Form):
    captcha = NoReCaptchaField(
        gtag_attrs={
            "data-size": "normal",
        },
        label="",
    )


class RegistrationLinkForm(InvisibleCaptchaContactForm):
    email = forms.EmailField(required=True, max_length=200)


class CityPostalCodeField(MultiValueField):
    def __init__(self, *args, **kwargs):
        error_messages = {
            "required": "Pole wymagane",
            "incomplete": "Wprowadź poprawny kod pocztowy",
        }

        fields = (
            # forms.CharField(max_length=6, widget=forms.TextInput() ),
            PLPostalCodeField(),
            forms.CharField(max_length=92, widget=forms.TextInput()),
        )

        super().__init__(
            error_messages=error_messages,
            fields=fields,
            require_all_fields=True,
            *args,
            **kwargs,
        )

    def compress(self, data_list):
        return ", ".join(data_list)


class ZgloszenieForm(forms.ModelForm):
    """
    Bazowa klasa formularza zgłoszenia (nie należy używać jej bezpośrednio).

    Podklasy powinny ustawić parametr “fields”.
    """

    discount_coupon = forms.CharField(
        max_length=50, required=False, label=_("Kod rabatowy")
    )

    class Meta:
        model = Zgloszenie
        fields = "__all__"

    def __init__(self, *args, **kwargs):
        self.szkolenie = kwargs.pop("szkolenie", None)

        super().__init__(*args, **kwargs)
        self.label_suffix = ""
        self.discount_code = None

        if self.szkolenie:
            if not self.szkolenie.mozliwa_rejestracja_jako_firma:
                self.fields["prywatny"].choices = (("1", _("osoba prywatna")),)

            if self.szkolenie.dodatkowe_pytanie_o_wiek == "hidden":
                self.fields["wiek_uczestnika"].widget = forms.HiddenInput()
            else:
                self.fields["wiek_uczestnika"].required = (
                    self.szkolenie.dodatkowe_pytanie_o_wiek == "required"
                )

            if (
                self.szkolenie.dodatkowe_pytanie_do_uczestnika
                and self.szkolenie.dodatkowe_pytanie_do_uczestnika_widocznosc
                != "hidden"
            ):
                self.fields[
                    "odpowiedz_na_dodatkowe_pytanie"
                ].label = self.szkolenie.dodatkowe_pytanie_do_uczestnika
                self.fields["odpowiedz_na_dodatkowe_pytanie"].required = (
                    self.szkolenie.dodatkowe_pytanie_do_uczestnika_widocznosc
                    == "required"
                )
            else:
                self.fields[
                    "odpowiedz_na_dodatkowe_pytanie"
                ].widget = forms.HiddenInput()

        try:
            self.za_kurs_zaplace = int(self.data.get("za_kurs_zaplace", 0))
        except:
            self.za_kurs_zaplace = 0

    # faktura_nip = PLNIPField(label="NIP")

    raty_nazwa_dokumentu = forms.CharField(
        label=_("Nazwa dokumentu"), required=False, max_length=200
    )
    raty_numer_dokumentu = forms.CharField(
        label=_("Numer dokumentu"), required=False, max_length=100
    )
    raty_pesel = PLPESELField(label=_("PESEL"), required=False)
    raty_panstwo = forms.ChoiceField(
        label=_("Kraj"),
        choices=(
            ("", "----"),
            ("polska", _("Polska")),
            ("inny", _("inny")),
        ),
        required=False,
    )

    # Label jest nadpisywany w widoku
    za_kurs_zaplace = forms.ChoiceField(
        label=_("Za kurs zapłacę"), choices=ZAPLACE_CHOICES, widget=forms.RadioSelect
    )

    captcha = captcha.forms.CaptchaField(
        label=_("Wpisz tekst z obrazka"),
        help_text=_("(wielkość liter nie ma znaczenia)"),
    )

    obiad_wegetarianski = forms.BooleanField(
        label=_("Obiady wegetariańskie"), required=False
    )
    chce_obiady = ChoiceFieldIgnoreRequired(
        label=_("Zamawiam obiady"),
        choices=(("1", _("TAK")), ("0", _("NIE"))),
        widget=forms.RadioSelect,
        required=True,
    )

    chce_fakture = ChoiceFieldIgnoreRequired(
        label=_("Faktura"),
        choices=(("0", _("elektroniczna")), ("1", _("papierowa"))),
        widget=forms.RadioSelect,
        required=True,
        initial="0",
        help_text=_(
            "Wybór faktury elektronicznej oznacza rezygnację z faktury papierowej i wyrażenie zgody na przesłanie faktury e-mailem. Zmiana formy wysyłki faktury po wystawieniu faktury końcowej wiąże się z dopłatą 50 zł netto."
        ),
    )

    drukowany_certyfikat = ChoiceFieldIgnoreRequired(
        label=_("Certyfikat"),
        choices=(("0", _("elektroniczny")), ("1", _("elektroniczny i drukowany"))),
        widget=forms.RadioSelect,
        required=True,
        initial="0",
        help_text=_(
            "Zmiana formy wystawienia certyfikatu po wystawieniu faktury końcowej wiąże się z dopłatą 50 zł netto / osobę."
        ),
    )

    chce_autoryzacje = ChoiceFieldIgnoreRequired(
        label=_("Zamawiam autoryzację"),
        choices=(("1", _("TAK")), ("0", _("NIE"))),
        widget=forms.RadioSelect,
        required=True,
    )

    uczestnik_wieloosobowy_ilosc_osob = forms.IntegerField(
        label=_("Liczność grupy"),
        min_value=2,
        required=False,
        help_text=_("Wpisać łączną liczbę dla potwierdzenia"),
    )

    required_css_class = "required"

    prywatny = ChoiceFieldIgnoreRequired(
        label=_("Zgłaszam się jako"),
        choices=(("0", _("firma")), ("1", _("osoba prywatna"))),
        widget=HorizontalRadioSelect(),
        required=True,
        initial="1",
    )

    czy_grupa = forms.ChoiceField(
        label=_("Liczba zgłaszanych osób"),
        choices=(("1", _("pojedyncza osoba")), ("0", _("grupa"))),
        widget=HorizontalRadioSelect(),
        required=True,
        initial="1",
    )

    # miejscowosc_kod = CityPostalCodeField(label='miasto, kot pocztowy', widget=CityPostalCodeWidget() )

    def _html_output(
        self, normal_row, error_row, row_ender, help_text_html, errors_on_separate_row
    ):
        "Helper function for outputting HTML. Used by as_table(), as_ul(), as_p()."
        top_errors = (
            self.non_field_errors()
        )  # Errors that should be displayed above all fields.
        output, hidden_fields = [], []

        for name, field in list(self.fields.items()):

            # te pola stylujemy osobno
            if name in ["chce_zapisac_sie_na_newsletter", "akceptuje_regulamin"]:
                continue

            html_class_attr = ""
            bf = self[name]
            # Escape and cache in local variable.
            bf_errors = self.error_class(
                [conditional_escape(error) for error in bf.errors]
            )
            if bf.is_hidden:
                if bf_errors:
                    top_errors.extend(
                        [
                            "(Hidden field %(name)s) %(error)s"
                            % {"name": name, "error": force_text(e)}
                            for e in bf_errors
                        ]
                    )
                hidden_fields.append(six.text_type(bf))
            else:
                # Create a 'class="..."' attribute if the row should have any
                # CSS classes applied.
                css_classes = bf.css_classes()
                if css_classes:
                    html_class_attr = ' class="%s"' % css_classes

                if errors_on_separate_row and bf_errors:
                    output.append(error_row % force_text(bf_errors))

                if bf.label:
                    label = conditional_escape(force_text(bf.label))
                    label = bf.label_tag(label) or ""
                else:
                    label = ""

                if field.help_text:
                    help_text = help_text_html % force_text(field.help_text)
                else:
                    help_text = ""

                output.append(
                    normal_row
                    % {
                        "errors": force_text(bf_errors),
                        "label": force_text(label),
                        "field": six.text_type(bf),
                        "help_text": help_text,
                        "html_class_attr": html_class_attr,
                        "field_name": bf.html_name,
                    }
                )

        if top_errors:
            output.insert(0, error_row % force_text(top_errors))

        if hidden_fields:  # Insert any hidden fields in the last row.
            str_hidden = "".join(hidden_fields)
            if output:
                last_row = output[-1]
                # Chop off the trailing row_ender (e.g. '</td></tr>') and
                # insert the hidden fields.
                if not last_row.endswith(row_ender):
                    # This can happen in the as_p() case (and possibly others
                    # that users write): if there are only top errors, we may
                    # not be able to conscript the last row for our purposes,
                    # so insert a new, empty row.
                    last_row = normal_row % {
                        "errors": "",
                        "label": "",
                        "field": "",
                        "help_text": "",
                        "html_class_attr": html_class_attr,
                    }
                    output.append(last_row)
                output[-1] = last_row[: -len(row_ender)] + str_hidden + row_ender
            else:
                # If there aren't any rows in the output, just append the
                # hidden fields.
                output.append(str_hidden)
        return mark_safe("\n".join(output))

    def as_table(self):
        return self._html_output(
            normal_row='<tr%(html_class_attr)s><td class="label">%(label)s%(help_text)s</td><td class="input">%(errors)s%(field)s</td></tr>',
            error_row='<tr><td colspan="2">%s</td></tr>',
            row_ender="</td></tr>",
            help_text_html="<small>%s</small>",
            errors_on_separate_row=False,
        )

    def clean_discount_coupon(self):
        discount_coupon = self.cleaned_data.get("discount_coupon")

        if discount_coupon:
            if self.za_kurs_zaplace not in [1, 2, 5]:
                raise forms.ValidationError(
                    _("Kod rabatowy nie jest dostępny dla tej opcji płatności.")
                )

            discount_code = DiscountCode.objects.get_active(code=discount_coupon)

            if discount_code:
                self.discount_code = discount_code
            else:
                raise forms.ValidationError(
                    _("Kod rabatowy jest nieważny lub został wykorzystany.")
                )
        return discount_coupon

    def clean_prywatny(self):
        if self.cleaned_data["prywatny"] == "":
            raise forms.ValidationError(_("To pole jest wymagane."))
        elif (
            self.cleaned_data["prywatny"] == "0"
            and self.szkolenie
            and not self.szkolenie.mozliwa_rejestracja_jako_firma
        ):
            raise forms.ValidationError(_("Wybierz poprawną wartość."))
        return self.cleaned_data["prywatny"]

    def clean_wiek_uczestnika(self):
        if self.szkolenie and self.szkolenie.dodatkowe_pytanie_o_wiek == "hidden":
            return None
        return self.cleaned_data["wiek_uczestnika"]

    def clean_odpowiedz_na_dodatkowe_pytanie(self):
        if self.szkolenie and (
            not self.szkolenie.dodatkowe_pytanie_do_uczestnika
            or self.szkolenie.dodatkowe_pytanie_do_uczestnika_widocznosc == "hidden"
        ):
            return ""
        return self.cleaned_data["odpowiedz_na_dodatkowe_pytanie"]

    def _faktura_helper(self, field):
        if "prywatny" in self.cleaned_data:
            if self.cleaned_data["prywatny"] == "0" and not self.cleaned_data[field]:
                raise forms.ValidationError(
                    _("To pole jest wymagane dla klientów firmowych.")
                )
        return self.cleaned_data[field]

    def clean_termin(self):
        termin = self.cleaned_data.get("termin")
        if termin:
            if termin.pk not in dict(self.fields["termin"].choices):
                raise forms.ValidationError(_("Wybierz poprawną wartość."))
        return termin

    def clean_faktura_firma(self):
        return self._faktura_helper("faktura_firma")

    def clean_faktura_adres(self):
        return self._faktura_helper("faktura_adres")

    def clean_faktura_miejscowosc_kod(self):
        return self._faktura_helper("faktura_miejscowosc_kod")

    def clean_faktura_nip(self):
        if WersjaJezykowa.biezaca().kod_jezyka == "pl":
            return self._faktura_helper("faktura_nip")
        else:
            return self.cleaned_data["faktura_nip"]

    def clean_faktura_vat_id(self):
        if WersjaJezykowa.biezaca().kod_jezyka != "pl":
            return self._faktura_helper("faktura_vat_id")
        else:
            return self.cleaned_data["faktura_vat_id"]

    def clean_bylem_na(self):
        if self.cleaned_data["bylem_wczesniej"] and not self.cleaned_data["bylem_na"]:
            raise forms.ValidationError(_("To pole jest wymagane."))
        return self.cleaned_data["bylem_na"]

    def clean_chce_obiady(self):
        if (
            "termin" in self.cleaned_data
            and self.cleaned_data["termin"].obiady == "obiady-opcjonalne"
            and self.cleaned_data["chce_obiady"] == ""
        ):
            raise forms.ValidationError(_("To pole jest wymagane."))
        return self.cleaned_data["chce_obiady"]

    def clean_chce_autoryzacje(self):
        if (
            self.cleaned_data["chce_autoryzacje"]
            and not self.cleaned_data["termin"].autoryzacja()
        ):
            # To nie powinno się zdarzyć, bo ptaszki od autoryzacji są obsługiwane przez JavaScript, ale
            # lepiej być zapobiegliwym.
            raise forms.ValidationError(
                "Zaznaczono pole „chcę autoryzację”, ale dla wybranego terminu"
                "szkolenia autoryzacja nie jest dostępna."
            )
        return self.cleaned_data["chce_autoryzacje"]

    def clean_ile_obiadow_wegetarianskich(self):
        if "termin" in self.cleaned_data and (
            self.cleaned_data["termin"].obiady == "nie-ma-obiadow"
            or (
                self.cleaned_data["termin"].obiady == "obiady-opcjonalne"
                and self.cleaned_data.get("chce_obiady", "0") == "0"
            )
            or self.cleaned_data.get("prywatny", "0") == "1"
            or not ("uczestnik_wieloosobowy_ilosc_osob" in self.cleaned_data)
            or self.cleaned_data["uczestnik_wieloosobowy_ilosc_osob"] is None
            or self.cleaned_data["uczestnik_wieloosobowy_ilosc_osob"] == 1
            or self.cleaned_data["ile_obiadow_wegetarianskich"] is None
        ):
            return self.cleaned_data["ile_obiadow_wegetarianskich"]
        if (self.cleaned_data["ile_obiadow_wegetarianskich"] or 0) > (
            self.cleaned_data["uczestnik_wieloosobowy_ilosc_osob"] or 0
        ):
            raise forms.ValidationError(
                _(
                    "Liczba obiadów wegetariańskich nie może być większa niż liczba uczestników."
                )
            )
        return self.cleaned_data["ile_obiadow_wegetarianskich"]

    def clean_uwagi_klienta(self):
        if (
            "za_kurs_zaplace" in self.cleaned_data
            and int(self.cleaned_data["za_kurs_zaplace"]) in [4, 7]
            and not self.cleaned_data["uwagi_klienta"]
        ):
            raise forms.ValidationError(
                _("Proszę wpisać dokonane ustalenia odnośnie trybu płatności")
            )
        return self.cleaned_data["uwagi_klienta"]

    def clean_uczestnik_wieloosobowy_ilosc_osob(self):
        if (
            int(self.cleaned_data["czy_grupa"]) == 0
            and not self.cleaned_data["uczestnik_wieloosobowy_ilosc_osob"]
        ):
            raise forms.ValidationError(_("Proszę wpisać liczność grupy."))
        return self.cleaned_data["uczestnik_wieloosobowy_ilosc_osob"]

    def _clean_fields(self):
        super()._clean_fields()

        if self.cleaned_data.get("prywatny", "0") == "1" and (
            (self.cleaned_data.get("uczestnik_wieloosobowy_ilosc_osob") or 0) > 1
            or self.cleaned_data["czy_grupa"] == "0"
        ):
            self._errors["prywatny"] = self.errors.get("prywatny", [])
            text_bledu = "{0}. {1}, {2}.".format(
                _("Osoba prywatna nie może zgłosić grupy"),
                _("Jeżeli chcesz zapisać na szkolenie kilka prywatnych osób"),
                _("każda z nich musi musi przysłać osobny formularz"),
            )
            self._errors["prywatny"].append(text_bledu)

    def clean(self):
        data = super().clean()

        za_kurs_zaplace = int(data.get("za_kurs_zaplace", 0))
        prywatny = data.get("prywatny", "0") == "1"
        raty = za_kurs_zaplace in [3, 10]

        if not prywatny and raty:
            self._errors["za_kurs_zaplace"] = self.error_class(
                [_("Raty są dostępny tylko dla osób prywatnych.")]
            )

        if prywatny and za_kurs_zaplace == 6:
            self._errors["za_kurs_zaplace"] = self.error_class(
                [_("Opcja niedostępna dla osób prywatnych.")]
            )

        if prywatny and raty:
            raty_panstwo = data.get("raty_panstwo")

            if not raty_panstwo:
                self._errors["raty_panstwo"] = self.error_class(
                    [_("To pole jest wymagane.")]
                )
            else:
                if not data.get("raty_numer_dokumentu"):
                    self._errors["raty_numer_dokumentu"] = self.error_class(
                        [_("To pole jest wymagane.")]
                    )

                if raty_panstwo == "polska":
                    if not data.get("raty_pesel") and not self._errors.get(
                        "raty_pesel"
                    ):
                        self._errors["raty_pesel"] = self.error_class(
                            [_("To pole jest wymagane.")]
                        )

                    regex = re.compile(r"^[A-Za-z]{3}[0-9]{6}$")
                    if not regex.search(force_text(data.get("raty_numer_dokumentu"))):
                        self._errors["raty_numer_dokumentu"] = self.error_class(
                            [_("Wprowadź numer dowodu w formacie XXXYYYYYY.")]
                        )
                else:
                    if not data.get("raty_nazwa_dokumentu"):
                        self._errors["raty_nazwa_dokumentu"] = self.error_class(
                            [_("To pole jest wymagane.")]
                        )
                    if not data.get("raty_numer_dokumentu"):
                        self._errors["raty_numer_dokumentu"] = self.error_class(
                            [_("To pole jest wymagane.")]
                        )
        return data

    def save(self, commit=True):
        zgloszenie = super().save(commit=False)

        # Jesli to kurs i ktos zapisal sie na 30 dni przed to dajemy mu rabat 3%
        if (
            self.szkolenie
            and self.za_kurs_zaplace in [1, 2, 5]
            and not self.discount_code
            and self.szkolenie.tag_dlugosc.slug != "szkolenie"
            and zgloszenie.termin.termin
            >= (datetime.date.today() + datetime.timedelta(days=30))
        ):

            self.discount_code = DiscountCode.objects.create(
                discount=Decimal("3"),
                source="30days",
                limit=1,
                comment="Dodany automatycznie w trakcie zapisu dla: {}.".format(
                    zgloszenie.imie_nazwisko
                ),
            )

        zgloszenie.discount_code = self.discount_code
        zgloszenie.waluta = zgloszenie.termin.szkolenie.waluta
        zgloszenie.stawka_vat = zgloszenie.przypuszczalna_stawka_vat()

        if commit:
            zgloszenie.save()
        return zgloszenie


class ZgloszeniePolskieForm(ZgloszenieForm):
    """
    Polski formularz zgłoszeniowy.
    """

    class Meta:
        model = Zgloszenie
        fields = (
            "termin",
            "chce_autoryzacje",
            "prywatny",
            "czy_grupa",
            "imie_nazwisko",
            "wiek_uczestnika",
            "uczestnik_wieloosobowy_ilosc_osob",
            "osoba_do_kontaktu",
            "email",
            "email_ksiegowosc",
            "telefon",
            "adres",
            "miejscowosc_kod",
            "chce_fakture",
            "drukowany_certyfikat",
            "faktura_firma",
            "faktura_adres",
            "faktura_miejscowosc_kod",
            "faktura_nip",
            "podmiot_publiczny",
            "uczestnik_wieloosobowy_ilosc_osob",
            "chce_obiady",
            "obiad_wegetarianski",
            "ile_obiadow_wegetarianskich",
            "za_kurs_zaplace",
            "discount_coupon",
            "raty_panstwo",
            "raty_nazwa_dokumentu",
            "raty_numer_dokumentu",
            "raty_pesel",
            "bylem_wczesniej",
            "bylem_na",
            "odpowiedz_na_dodatkowe_pytanie",
            "uwagi_klienta",
        )

    faktura_nip = CustomPLNIPField(required=False, label="NIP")

    chce_zapisac_sie_na_newsletter = forms.BooleanField(
        label=mark_safe(_("Chcę zapisać się na newsletter")),
        initial=False,
        required=False,
        help_text=_(
            "Wyrażam zgodę na otrzymywanie informacji handlowych wysyłanych przez ALX Academy sp. z o.o. na wyżej podany adres e-mail zgodnie z ustawą o świadczeniu usług drogą elektroniczną (Dz.U. z 2002 r. Nr 144, poz. 1204)."
        ),
    )
    akceptuje_regulamin = forms.BooleanField(
        label=_("Akceptuję regulamin"),
        help_text=_(
            'Oświadczam, że zapoznałam/em się z <a href="/pl/regulamin/" target="_blank">regulaminem</a> i akceptuję jego treść oraz warunki.'
        ),
    )


class ZamknieteZgloszenieForm(forms.ModelForm):
    faktura_nip = CustomPLNIPField(required=True, label="NIP")
    chce_fakture = forms.ChoiceField(
        label=_("Faktura"),
        choices=(("0", _("elektroniczna")), ("1", _("papierowa"))),
        widget=forms.RadioSelect,
        required=True,
        initial="0",
        help_text=_(
            "Wybór faktury elektronicznej oznacza rezygnację z faktury papierowej i wyrażenie zgody na przesłanie faktury e-mailem. Zmiana formy wysyłki faktury po wystawieniu faktury końcowej wiąże się z dopłatą 50 zł netto."
        ),
    )
    za_kurs_zaplace = forms.ChoiceField(
        label=_("Za kurs zapłacę"), choices=ZAPLACE_CHOICES, widget=forms.RadioSelect
    )
    captcha = captcha.forms.CaptchaField(
        label=_("Wpisz tekst z obrazka"),
        help_text=_("(wielkość liter nie ma znaczenia)"),
    )
    uczestnik_wieloosobowy_ilosc_osob = forms.IntegerField(
        label=_("Liczność grupy"),
        min_value=1,
        required=True,
        help_text=_("Wpisać łączną liczbę dla potwierdzenia"),
    )
    chce_zapisac_sie_na_newsletter = forms.BooleanField(
        label=mark_safe(_("Chcę zapisać się na newsletter")),
        initial=False,
        required=False,
        help_text=_(
            "Wyrażam zgodę na otrzymywanie informacji handlowych wysyłanych przez ALX Academy sp. z o.o. na wyżej podany adres e-mail zgodnie z ustawą o świadczeniu usług drogą elektroniczną (Dz.U. z 2002 r. Nr 144, poz. 1204)."
        ),
    )
    required_css_class = "required"

    class Meta:
        model = Zgloszenie
        fields = (
            "uczestnik_wieloosobowy_ilosc_osob",
            "imie_nazwisko",
            "osoba_do_kontaktu",
            "email_ksiegowosc",
            "telefon",
            "adres",
            "miejscowosc_kod",
            "chce_fakture",
            "faktura_firma",
            "faktura_adres",
            "faktura_miejscowosc_kod",
            "faktura_nip",
            "podmiot_publiczny",
            "faktura_vat_id",
            "za_kurs_zaplace",
            "bylem_wczesniej",
            "bylem_na",
            "uwagi_klienta",
        )

    def __init__(self, *args, **kwargs):
        self.szkolenie = kwargs.pop("szkolenie")
        self.termin = kwargs.pop("termin")

        super().__init__(*args, **kwargs)

        self.fields["faktura_firma"].required = True
        if self.szkolenie.language == "pl":
            self.fields["faktura_adres"].help_text = (
                "Oficjalny/rejestrowy adres " "firmy - do faktury."
            )
        self.fields["faktura_adres"].required = True
        self.fields["faktura_miejscowosc_kod"].required = True
        self.fields["faktura_vat_id"].required = True
        self.fields["email_ksiegowosc"].required = True
        self.fields["email_ksiegowosc"].help_text = ""
        self.fields["osoba_do_kontaktu"].required = True
        self.fields["osoba_do_kontaktu"].help_text = ""
        self.fields["telefon"].required = True
        self.fields["adres"].required = False
        if self.szkolenie.language == "pl":
            self.fields["adres"].help_text = (
                "Do wysyłki faktury, materiałów "
                "szkoleniowych itd. - wypełnić tylko jeśli "
                "inny niż oficjalny/rejestrowy do faktury "
                "poniżej."
            )
        self.fields["miejscowosc_kod"].required = False
        self.fields["imie_nazwisko"].required = False
        self.fields["imie_nazwisko"].label = _("Lista osób")
        self.fields["imie_nazwisko"].help_text = _("Może zostać uzupełnione później.")

        if self.szkolenie.language != "pl":
            del self.fields["faktura_nip"]
        else:
            del self.fields["faktura_vat_id"]

    def _html_output(
        self, normal_row, error_row, row_ender, help_text_html, errors_on_separate_row
    ):
        "Helper function for outputting HTML. Used by as_table(), as_ul(), as_p()."
        top_errors = (
            self.non_field_errors()
        )  # Errors that should be displayed above all fields.
        output, hidden_fields = [], []

        for name, field in list(self.fields.items()):

            # te pola stylujemy osobno
            if name in ["chce_zapisac_sie_na_newsletter", "akceptuje_regulamin"]:
                continue

            html_class_attr = ""
            bf = self[name]
            # Escape and cache in local variable.
            bf_errors = self.error_class(
                [conditional_escape(error) for error in bf.errors]
            )
            if bf.is_hidden:
                if bf_errors:
                    top_errors.extend(
                        [
                            "(Hidden field %(name)s) %(error)s"
                            % {"name": name, "error": force_text(e)}
                            for e in bf_errors
                        ]
                    )
                hidden_fields.append(six.text_type(bf))
            else:
                # Create a 'class="..."' attribute if the row should have any
                # CSS classes applied.
                css_classes = bf.css_classes()
                if css_classes:
                    html_class_attr = ' class="%s"' % css_classes

                if errors_on_separate_row and bf_errors:
                    output.append(error_row % force_text(bf_errors))

                if bf.label:
                    label = conditional_escape(force_text(bf.label))
                    label = bf.label_tag(label) or ""
                else:
                    label = ""

                if field.help_text:
                    help_text = help_text_html % force_text(field.help_text)
                else:
                    help_text = ""

                output.append(
                    normal_row
                    % {
                        "errors": force_text(bf_errors),
                        "label": force_text(label),
                        "field": six.text_type(bf),
                        "help_text": help_text,
                        "html_class_attr": html_class_attr,
                        "field_name": bf.html_name,
                    }
                )

        if top_errors:
            output.insert(0, error_row % force_text(top_errors))

        if hidden_fields:  # Insert any hidden fields in the last row.
            str_hidden = "".join(hidden_fields)
            if output:
                last_row = output[-1]
                # Chop off the trailing row_ender (e.g. '</td></tr>') and
                # insert the hidden fields.
                if not last_row.endswith(row_ender):
                    # This can happen in the as_p() case (and possibly others
                    # that users write): if there are only top errors, we may
                    # not be able to conscript the last row for our purposes,
                    # so insert a new, empty row.
                    last_row = normal_row % {
                        "errors": "",
                        "label": "",
                        "field": "",
                        "help_text": "",
                        "html_class_attr": html_class_attr,
                    }
                    output.append(last_row)
                output[-1] = last_row[: -len(row_ender)] + str_hidden + row_ender
            else:
                # If there aren't any rows in the output, just append the
                # hidden fields.
                output.append(str_hidden)
        return mark_safe("\n".join(output))

    def as_table(self):
        return self._html_output(
            normal_row='<tr%(html_class_attr)s><td class="label">%(label)s%(help_text)s</td><td class="input">%(errors)s%(field)s</td></tr>',
            error_row='<tr><td colspan="2">%s</td></tr>',
            row_ender="</td></tr>",
            help_text_html="<small>%s</small>",
            errors_on_separate_row=False,
        )

    def clean_bylem_na(self):
        if self.cleaned_data["bylem_wczesniej"] and not self.cleaned_data["bylem_na"]:
            raise forms.ValidationError(_("To pole jest wymagane."))
        return self.cleaned_data["bylem_na"]

    def clean_uwagi_klienta(self):
        if (
            "za_kurs_zaplace" in self.cleaned_data
            and int(self.cleaned_data["za_kurs_zaplace"]) in [4, 7]
            and not self.cleaned_data["uwagi_klienta"]
        ):
            raise forms.ValidationError(
                _("Proszę wpisać dokonane ustalenia odnośnie trybu płatności")
            )
        return self.cleaned_data["uwagi_klienta"]

    def save(self, commit=True):
        zgloszenie = super().save(commit=False)
        zgloszenie.chce_autoryzacje = self.termin.autoryzacja_w_cenie
        zgloszenie.drukowany_certyfikat = self.termin.drukowany_certyfikat_w_cenie
        zgloszenie.cena_obiadow = Decimal(0)
        zgloszenie.chce_obiady = self.termin.obiady == "obiady-wliczone"
        zgloszenie.termin = self.termin

        if not zgloszenie.imie_nazwisko:
            zgloszenie.imie_nazwisko = "Osób w grupie: {0}".format(
                zgloszenie.uczestnik_wieloosobowy_ilosc_osob
            )

        zgloszenie.waluta = zgloszenie.termin.szkolenie.waluta
        zgloszenie.stawka_vat = zgloszenie.przypuszczalna_stawka_vat()
        zgloszenie.email = zgloszenie.email_ksiegowosc

        if commit:
            zgloszenie.save()
        return zgloszenie


class ZgloszeniePolskieFormNoValidation(ZgloszenieForm):
    class Meta:
        model = Zgloszenie
        fields = (
            "termin",
            "chce_autoryzacje",
            "czy_grupa",
            "uczestnik_wieloosobowy_ilosc_osob",
            "prywatny",
            "chce_fakture",
            "obiad_wegetarianski",
            "ile_obiadow_wegetarianskich",
        )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        for field in self.fields:
            try:
                self.fields[field].required = False
            except AttributeError:
                pass

    def clean(self):
        cleaned_data = self.cleaned_data
        del self._errors["captcha"]
        if "discount_coupon" in list(self._errors.keys()):
            del self._errors["discount_coupon"]
        if "chce_obiady" in list(self._errors.keys()):
            del self._errors["chce_obiady"]
        if "prywatny" in list(self._errors.keys()):
            del self._errors["prywatny"]
        if "uczestnik_wieloosobowy_ilosc_osob" in list(self._errors.keys()):
            del self._errors["uczestnik_wieloosobowy_ilosc_osob"]
            self.cleaned_data["uczestnik_wieloosobowy_ilosc_osob"] = 1
        return cleaned_data

    def save(self, commit=False):
        zgloszenie = super().save(commit=False)
        zgloszenie.discount_code = self.discount_code
        zgloszenie.waluta = zgloszenie.termin.szkolenie.waluta
        zgloszenie.stawka_vat = zgloszenie.przypuszczalna_stawka_vat()
        return zgloszenie


class ZgloszenieZagraniczneForm(ZgloszenieForm):

    """
    Zagraniczny formularz zgłoszeniowy.
    """

    class Meta:
        model = Zgloszenie
        fields = (
            "termin",
            "chce_autoryzacje",
            "prywatny",
            "czy_grupa",
            "imie_nazwisko",
            "wiek_uczestnika",
            "uczestnik_wieloosobowy_ilosc_osob",
            "osoba_do_kontaktu",
            "email",
            "email_ksiegowosc",
            "telefon",
            "adres",
            "miejscowosc_kod",
            "chce_fakture",
            "drukowany_certyfikat",
            "faktura_firma",
            "faktura_adres",
            "faktura_miejscowosc_kod",
            "faktura_vat_id",
            "uczestnik_wieloosobowy_ilosc_osob",
            "chce_obiady",
            "obiad_wegetarianski",
            "ile_obiadow_wegetarianskich",
            "za_kurs_zaplace",
            "discount_coupon",
            "raty_panstwo",
            "raty_nazwa_dokumentu",
            "raty_numer_dokumentu",
            "raty_pesel",
            "bylem_wczesniej",
            "bylem_na",
            "odpowiedz_na_dodatkowe_pytanie",
            "uwagi_klienta",
        )

    akceptuje_regulamin = forms.BooleanField(
        label="I accept the terms and conditions",
        help_text=_(
            'I declare that I have read the <a href="/en/terms/" target="_blank">terms and conditions</a>, I fully understand them and accept them.'
        ),
    )    


class DiscountCodeAdminForm(forms.ModelForm):
    class Meta:
        model = DiscountCode
        fields = "__all__"

    def clean_code(self):
        code = self.cleaned_data.get("code")
        if (
            code
            and DiscountCode.objects.filter(code__iexact=code)
            .exclude(pk=self.instance.pk)
            .exists()
        ):
            raise ValidationError("Kod już istnieje w Kod rabatowy.")
        return code


class MyFlatPageAdminForm(ModelFormI18n):
    content = forms.CharField(
        widget=CustomAceWidget(
            mode="html",
            wordwrap=True,
            showprintmargin=False,
            width="1000px",
            height="700px",
        ),
        required=False,
    )

    class Meta:
        model = MyFlatPage
        fields = "__all__"


class TabAdminForm(forms.ModelForm):
    content = forms.CharField(
        widget=CustomAceWidget(
            mode="html",
            wordwrap=True,
            showprintmargin=False,
            width="1000px",
            height="700px",
        ),
        required=False,
    )

    class Meta:
        model = Tab
        fields = "__all__"


class SzkolenieAdminForm(ModelFormI18n):
    # program = forms.CharField(widget=LargeTextarea, help_text="XXX")

    class Meta:
        model = Szkolenie
        fields = "__all__"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        self.old_file_path = None

        if self.instance.pk and self.instance.materialy_plik:
            self.old_file_path = self.instance.materialy_plik.path

        # nie wiem która metoda nadpisywania widgeta jest lepsza
        self.fields["program"].widget = LargeTextarea()
        self.fields["cel"].label = mark_safe("Opis szkolenia<br /> - na jego stronę")
        self.fields["opis"].label = mark_safe("Opis skrótowy<br /> (do tabel)")
        self.fields["tag_zawod"].label_from_instance = lambda obj: "{0} ({1})".format(
            obj.nazwa, obj.language.upper()
        )

        if not self.instance.pk or self.instance.tag_zawod.slug != "kurs-zawodowy":
            self.fields["tryb_zaoczny_opis"].initial = "(soboty i niedziele)"
            self.fields["tryb_dzienny_opis"].initial = "(zajęcia w dni powszednie)"

        # Dodajemy tylko szkolenia, ktore moga byc nadrzedne
        self.fields["szkolenie_nadrzedne"].queryset = Szkolenie.objects.exclude(
            szkolenie_nadrzedne_id__isnull=False
        ).exclude(pk=self.instance.pk)

    def get_initial_for_field(self, field, field_name):
        # Dla nowych instancji, które nie zostały zapisane
        if not self.instance.pk:
            if field_name == "tryb_zaoczny_opis":
                return "(soboty i niedziele)"
            elif field_name == "tryb_dzienny_opis":
                return "(zajęcia w dni powszednie)"

        # Dla istniejących instancji, które mają puste wartości w określonych polach
        else:
            value = getattr(self.instance, field_name, "")
            if not value:  # Jeśli pole jest puste
                if field_name == "tryb_zaoczny_opis":
                    return "(soboty i niedziele)"
                elif field_name == "tryb_dzienny_opis":
                    return "(zajęcia w dni powszednie)"

        # Domyślne zachowanie dla innych przypadków
        return super().get_initial_for_field(field, field_name)
    def clean_szkolenie_nadrzedne(self):
        szkolenie_nadrzedne = self.cleaned_data.get("szkolenie_nadrzedne")
        if szkolenie_nadrzedne and self.instance.pk:
            if Szkolenie.objects.filter(szkolenie_nadrzedne=self.instance).exists():
                raise ValidationError(
                    "To szkolenie jest już nadrzędne dla "
                    "innych i nie możesz wybrać żadnego "
                    "szkolenia nadrzędnego."
                )
        return szkolenie_nadrzedne

    def clean_program_szkolenia(self):
        program_szkolenia = self.cleaned_data.get("program_szkolenia")
        if program_szkolenia and not is_html(program_szkolenia):
            raise ValidationError("Program szkolenia musi zawierać tagi HTML.")
        return program_szkolenia

    def clean(self):
        cleaned_data = super().clean()
        base_translation = cleaned_data.get("base_translation")
        tag_zawod = cleaned_data.get("tag_zawod")
        tag_dlugosc = cleaned_data.get("tag_dlugosc")
        tagi_technologia = cleaned_data.get("tagi_technologia", [])
        language = cleaned_data.get("language")
        szkolenie_nadrzedne = cleaned_data.get("szkolenie_nadrzedne")
        podzbior_dni = cleaned_data.get("podzbior_dni")
        wiele_rat = cleaned_data.get("wiele_rat")
        maile_o_kontynuacji = self.cleaned_data.get("maile_o_kontynuacji", False)
        kontynuacja = self.cleaned_data.get("kontynuacja", False)
        tryb_opis = (
            cleaned_data.get("tryb_dzienny_opis")
            or cleaned_data.get("tryb_wieczorowy_opis")
            or cleaned_data.get("tryb_zaoczny_opis")
        )

        if maile_o_kontynuacji and not kontynuacja:
            self._errors["maile_o_kontynuacji"] = self.error_class(
                ["Szkolenie musi mieć wskazane szkolenie, które jest jego kontynuacją."]
            )

        if wiele_rat and tag_dlugosc and tag_dlugosc.slug != "kurs-zawodowy":
            self._errors["wiele_rat"] = self.error_class(
                ["Ta wartość może być zaznaczona tylko dla Kursów Zawodowych."]
            )
            return cleaned_data

        if tag_dlugosc and tag_dlugosc.slug == "kurs-zawodowy" and not tryb_opis:
            raise forms.ValidationError(
                "Kurs zawodowy musi mieć min. jeden z trzech opisów trybu "
                "(dzienny, wieczorowy, zaoczny)"
            )

        if szkolenie_nadrzedne and not podzbior_dni:
            self._errors["podzbior_dni"] = self.error_class(
                ["Jeśli wybrano szkolenie nadrzędne musisz podać podzbiór dni."]
            )
            return cleaned_data

        if base_translation:
            for field in self.instance.FIELDS_SYNCED_BETWEEN_TRANSLATIONS:
                field_value = cleaned_data.get(field)
                if isinstance(field_value, MappedTranslatedModel):
                    # Dla zmapowanych modeli chcemy porównać obiekty bazowe.
                    field_value = field_value.base_translation
                if field_value not in [None, ""] and field_value != getattr(
                    base_translation, field
                ):
                    raise forms.ValidationError(
                        "Podano inną wartość pola „{0}” niż w obiekcie bazowym. Podaj tę samą wartość lub pozostaw pole puste, aby dokonać automatycznej synchronizacji.".format(
                            self.instance._meta.get_field_by_name(field)[0].verbose_name
                        )
                    )

        if language:
            if tag_zawod and tag_zawod.language != language:
                self._errors["tag_zawod"] = self.error_class(
                    ["Język taga musi być zgodny z językiem szkolenia."]
                )
                return cleaned_data
            for tag_technologia in tagi_technologia:
                if tag_technologia.language != language:
                    self._errors["tagi_technologia"] = self.error_class(
                        [
                            "Język wszystkich tagów musi być zgodny z "
                            "językiem szkolenia."
                        ]
                    )
                    return cleaned_data
        return cleaned_data

    def save(self, commit=True):
        # Sprawdzamy, czy jest robiony nowy upload pliku, lub usuwany obecny
        # plik. W obu przypadkach stary plik powinien zostać usunięty.
        if self.old_file_path and (
            self.files.get("materialy_plik") or self.data.get("materialy_plik-clear")
        ):
            # Usuń starą wersję pliku (jeśli istnieje)
            if os.path.exists(self.old_file_path):
                os.remove(self.old_file_path)

        return super().save(commit=commit)


class PropozycjaTerminuForm(forms.Form):
    termin = forms.DateField(label=_("Proponowany termin (RRRR-MM-DD)"), required=False)
    liczba_osob = forms.IntegerField(
        label=_("Orientacyjna liczba osób kierowanych na szkolenie"),
        required=False,
        min_value=1,
    )
    # Choices dla lokalizacji jest nadpisany w konstruktorze.
    lokalizacja = forms.ChoiceField(choices=[], label=_("Lokalizacja"), required=False)
    imie_nazwisko = forms.CharField(label=_("Imię i nazwisko"), max_length=200)
    nazwa_firmy = forms.CharField(
        label=_("Nazwa firmy"), required=False, max_length=200
    )
    email = forms.EmailField(label=_("Email"))
    kontakt = forms.CharField(
        label=_("Telefon kontaktowy"), required=False, max_length=200
    )
    uwagi = forms.CharField(
        label=_("Uwagi"), widget=forms.Textarea, required=False, max_length=800
    )
    captcha = captcha.forms.CaptchaField(label=_("Wpisz tekst z obrazka"))

    def __init__(self, language, *args, **kwargs):
        super().__init__(*args, **kwargs)
        dostepne_lokalizacje = Lokalizacja.objects.filter(
            panstwo__in=WersjaJezykowa.biezaca().wyswietlane_panstwa.all(),
            reklamowana=True,
        )
        self.fields["lokalizacja"].choices = [("", "-----")]
        self.fields["lokalizacja"].choices += [
            (x.shortname, x.fullname) for x in dostepne_lokalizacje
        ]
        self.fields["lokalizacja"].choices.append(
            (_("inna lokalizacja"), _("inna lokalizacja"))
        )

    def clean_termin(self):
        termin = self.cleaned_data.get("termin")
        if termin and termin <= datetime.date.today():
            raise ValidationError("Data musi być w przyszłości.")
        return termin


class UczestnikAdminInlineFormset(forms.models.BaseInlineFormSet):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Musimy przekazać do każdego formularza należącego do formsetu, aby
        # przeprowadził walidację odnośnie faktury pro forma. Jeśli nadrzędny
        # obiekt szkolenia ma ustawiona opcję `wysylaj_powiadomienia_proformy`
        # na False to wtedy nie można uczestnikowi wystawić proformy.
        if (
            self.instance.pk
            and self.instance.szkolenie.wysylaj_powiadomienia_proformy is False
        ):

            for form in self.forms:
                form._do_not_send_proforma = True

                # Dodatkowo dla nowych pól ustaw wartość checkboxa na False.
                form.fields["wystaw_proforme_automatycznie"].initial = False


class UczestnikAdminInlineForm(forms.ModelForm):
    """
    Formularz dla `UczestnikAdminInline` pozwalający na sprawdzenie
    poprawności danych teleadresowych poszczególnych uczestników terminu
    szkolenia.
    """

    # Zmienna ustawiona z poziomu formsetu - `UczestnikAdminInlineFormset` dla
    # każdego obiektu formularza.
    _do_not_send_proforma = False

    class Meta:
        model = Uczestnik
        fields = "__all__"

    # def clean_nr_proformy(self):
    #     """
    #     Nie pozwalamy na wyczyszczenie pola z numerem proforma,
    #     gdy wczesniej juz istaniało.
    #     """
    #
    #     nr_proformy = self.cleaned_data.get('nr_proformy')
    #
    #     if self.instance.pk and not nr_proformy:
    #         return self.instance.nr_proformy
    #     return nr_proformy

    def clean_wystaw_proforme_automatycznie(self):
        wystaw_proforme_automatycznie = self.cleaned_data.get(
            "wystaw_proforme_automatycznie", False
        )

        if wystaw_proforme_automatycznie and self._do_not_send_proforma:
            raise forms.ValidationError(
                "Uczestnicy tego szkolenia nie mogą otrzymać faktury ProForma."
            )
        return wystaw_proforme_automatycznie

    def clean(self):
        data = super().clean()

        if any(self.errors):
            return data

        for r in ["rata4", "rata3", "rata2", "rata1", "zaliczka"]:
            if (data.get(r + "_zaplacone") or data.get(r + "_termin")) and data.get(
                r + "_kwota"
            ) is None:
                self._errors[r + "_kwota"] = self.error_class(
                    ["Należy podać kwotę (lub 0.00)"]
                )

        term = data.get("termin")
        prywatny = data.get("prywatny")

        if data.get("prywatny"):
            miejscowosc_kod = data.get("miejscowosc_kod")
        else:
            miejscowosc_kod = data.get("faktura_miejscowosc_kod")

        faktura_nip = data.get("faktura_nip")
        faktura_kraj = data.get("faktura_kraj")

        # Osoba prywatna nie może mieć NIPu, niezależnie od ustawień terminu.
        if faktura_nip and prywatny:
            self._errors["faktura_nip"] = self.error_class(
                ["Osoba prywatna nie może mieć numeru NIP."]
            )
            return data

        # Sprawdzamy tylko te obiekty uczestników, które mają zaznaczoną
        # opcję `wystaw_proforme_automatycznie` oraz termin jest ustawiony
        # jako `odbylo_sie` == True.
        if term and term.odbylo_sie and data.get("wystaw_proforme_automatycznie"):

            if not self.instance.has_required_data_for_invoice(data):
                # Teraz sprawdzamy, czy do zaznaczenia zostało tylko pole
                # `imie_nazwisko_zostalo_sprawdzone`, jeśli tak wyświetl błąd
                # tylko na tym polem - w przeciwnym razie także nad
                # `wystaw_proforme_automatycznie`.
                if not data.get("imie_nazwisko_zostalo_sprawdzone"):
                    self._errors["imie_nazwisko_zostalo_sprawdzone"] = self.error_class(
                        [
                            "Musisz potwierdzić, że dane do "
                            "faktury zawierają poprawne dane."
                        ]
                    )

                fake_data = data.copy()
                fake_data["imie_nazwisko_zostalo_sprawdzone"] = True

                if not self.instance.has_required_data_for_invoice(fake_data):
                    self._errors["wystaw_proforme_automatycznie"] = self.error_class(
                        ["Uzupełnij dane do faktury."]
                    )
            else:
                # Sprawdź poprwanośc pola `miejscowosc_kod`.
                # Pola te sprawdzamy dopiero jak uczestnik ma uzupełnione
                # wszystkie wymagane dane do faktury.

                if miejscowosc_kod:
                    miasto_kod = parse_miejscowosc_kod(miejscowosc_kod)

                    if not miasto_kod["kod"] or not miasto_kod["miejscowosc"]:
                        key = (
                            "miejscowosc_kod"
                            if data.get("prywatny")
                            else "faktura_miejscowosc_kod"
                        )
                        self._errors[key] = self.error_class(
                            [
                                "Pole ma niepoprawny format. Powinno być: "
                                '"XX-XXX Miejscowość"'
                            ]
                        )

        # Walidacja numeru NIP. Przypadki:
        #   - dla firmy PL - NIP musi być podany i poprawny
        #   - dla firmy nie PL - NIP może być podany, ale nie
        #                        sprawdzamy jego poprawności.
        if faktura_nip and faktura_kraj in ("PL",):
            # Uwaga, czasami NIP może być poprzedzony skrótem PL,
            # w takim wypadku przed walidacją wyrzucamy ten
            # przedrostek.

            if faktura_nip.upper().startswith("PL"):
                faktura_nip = faktura_nip[2:]

            try:
                CustomPLNIPField().clean(faktura_nip)
            except forms.ValidationError as err:
                self._errors["faktura_nip"] = self.error_class(err.messages)

        return data


class UczestnikAdminForm(forms.ModelForm):
    kwota_do_zaplaty = forms.DecimalField(required=False, widget=NettoBruttoInput)
    zaliczka_kwota = forms.DecimalField(required=False, widget=NettoBruttoInput)
    rata1_kwota = forms.DecimalField(required=False, widget=NettoBruttoInput)
    rata2_kwota = forms.DecimalField(required=False, widget=NettoBruttoInput)
    rata3_kwota = forms.DecimalField(required=False, widget=NettoBruttoInput)
    rata4_kwota = forms.DecimalField(required=False, widget=NettoBruttoInput)
    cena_obiadow = forms.DecimalField(required=False, widget=NettoBruttoInput)

    class Meta:
        model = Uczestnik
        fields = "__all__"
        widgets = {
            "termin": SelectWithDataAttr(),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        obj = kwargs.get("instance", None)
        today = datetime.date.today()

        if obj is not None:
            currently_chosen = [
                (
                    obj.termin.id,
                    {
                        "label": "%s: %s, %s, tryb %s, %s, %s"
                        % (
                            obj.termin.szkolenie,
                            obj.termin.termin,
                            obj.termin.lokalizacja,
                            obj.termin.get_tryb_display(),
                            "zamknięte"
                            if obj.termin.prywatna_rejestracja
                            else "otwarte",
                            str(obj.termin.pk),
                        ),
                        "autoryzacja": int(
                            bool(
                                obj.termin.autoryzacja_aktywna
                                and obj.termin.szkolenie.autoryzacja_id
                            )
                        ),
                    },
                )
            ]
            currently_chosen_id = obj.termin.id
            wysylaj_proformy = obj.termin.szkolenie.wysylaj_powiadomienia_proformy
            terminy = (
                TerminSzkolenia.objects.filter(
                    Q(termin__gte=today)
                    | Q(
                        termin_zakonczenia__isnull=False,
                        termin_zakonczenia__gte=today,
                        odbylo_sie=True,
                    )
                )
                .exclude(pk=currently_chosen_id)
                .select_related()
                .order_by(
                    "-szkolenie__tag_dlugosc_id",
                    "szkolenie__kod",
                    "lokalizacja_id",
                    "termin",
                )
            )
            self.fields["termin"].choices = currently_chosen + [
                (
                    y.id,
                    {
                        "label": "%s%s: %s, %s, tryb %s, %s, %s"
                        % (
                            "[ROZPOCZĘTY] " if y.termin <= today else "",
                            y.szkolenie,
                            y.termin,
                            y.lokalizacja.shortname,
                            y.get_tryb_display(),
                            "zamknięte" if y.prywatna_rejestracja else "otwarte",
                            str(y.pk),
                        ),
                        "autoryzacja": int(
                            bool(y.autoryzacja_aktywna and y.szkolenie.autoryzacja_id)
                        ),
                    },
                )
                for y in terminy
            ]
            self._aktualny_termin = obj.termin
        else:
            currently_chosen = [("", "wybierz")]
            currently_chosen_id = None
            wysylaj_proformy = None
            self.fields["termin"].choices = currently_chosen + [
                (
                    str(x),
                    [
                        (
                            y.id,
                            {
                                "label": "%s, %s, tryb %s, %s, %s"
                                % (
                                    y.termin,
                                    y.lokalizacja.shortname,
                                    y.get_tryb_display(),
                                    "zamknięte"
                                    if y.prywatna_rejestracja
                                    else "otwarte",
                                    str(y.pk),
                                ),
                                "autoryzacja": int(
                                    bool(
                                        y.autoryzacja_aktywna
                                        and y.szkolenie.autoryzacja_id
                                    )
                                ),
                            },
                        )
                        for y in x.terminy_all().exclude(id=currently_chosen_id)
                    ],
                )
                for x in Szkolenie.objects.all()
                if x.terminy_all().exclude(id=currently_chosen_id)
            ]
            self._aktualny_termin = None

        self.fields["termin"].wysylaj_proformy = wysylaj_proformy
        self.fields["faktura_firma"].help_text = "Dostępne tylko dla firmy."
        self.fields["faktura_adres"].help_text = "Dostępne tylko dla firmy."
        self.fields["faktura_miejscowosc_kod"].help_text = "Dostępne tylko dla firmy."
        self.fields["faktura_nip"].help_text = "Dostępne tylko dla firmy."
        self.fields["faktura_vat_id"].help_text += ". Dostępne tylko dla firmy."

        if self.instance.pk:
            self.old_faktura_status = self.instance.faktura_status
        else:
            self.old_faktura_status = None

    # def clean_nr_proformy(self):
    #     """
    #     Nie pozwalamy na wyczyszczenie pola z numerem proforma,
    #     gdy wczesniej juz istaniało.
    #     """
    #
    #     nr_proformy = self.cleaned_data.get('nr_proformy')
    #
    #     if self.instance.pk and not nr_proformy:
    #         return self.instance.nr_proformy
    #     return nr_proformy

    def clean_miejscowosc_kod(self):
        miejscowosc_kod = self.cleaned_data.get("miejscowosc_kod")

        if miejscowosc_kod:
            miasto_kod = parse_miejscowosc_kod(miejscowosc_kod)

            if not miasto_kod["kod"] or not miasto_kod["miejscowosc"]:
                raise ValidationError(
                    "Pole ma niepoprawny format. "
                    'Powinno być: "XX-XXX Miejscowość". '
                    "Sprawdź, czy nie ma spacji w kodzie "
                    "pocztowym."
                )
        return miejscowosc_kod

    def clean_faktura_miejscowosc_kod(self):
        faktura_miejscowosc_kod = self.cleaned_data.get("faktura_miejscowosc_kod")

        if faktura_miejscowosc_kod:
            miasto_kod = parse_miejscowosc_kod(faktura_miejscowosc_kod)

            if not miasto_kod["kod"] or not miasto_kod["miejscowosc"]:
                raise ValidationError(
                    "Pole ma niepoprawny format. "
                    'Powinno być: "XX-XXX Miejscowość". '
                    "Sprawdź, czy nie ma spacji w kodzie "
                    "pocztowym."
                )
        return faktura_miejscowosc_kod

    def clean(self):
        podmiot_publiczny = self.cleaned_data.get("podmiot_publiczny")
        stawka_vat = self.cleaned_data.get("stawka_vat")
        if podmiot_publiczny and (stawka_vat or 0) > 0:
            raise forms.ValidationError(
                "Podmiot publiczny musi mieć zerową stawkę VAT."
            )

        if (
            self.cleaned_data["status"] == 3
            and not self.cleaned_data["termin"].odbylo_sie
        ):
            raise forms.ValidationError(
                "Aby ustawić status „przeszkolony”, uczestnik musi być przypisany do terminu szkolenia, który się odbył."
            )

        if (
            not self.cleaned_data.get("prywatny", "0") == 1
            and not self.cleaned_data["faktura_firma"]
        ):
            raise forms.ValidationError(
                "Uczestnik firmowy musi mieć przypisaną nazwę firmy."
            )

        faktura_status = self.cleaned_data.get("faktura_status", "")
        zaplacone = self.cleaned_data.get("zaplacone")
        zaliczka_zaplacone = self.cleaned_data.get("zaliczka_zaplacone")
        zliczacz_faktura_no = (
            True if (self.instance.pk and self.instance.zliczacz_faktura_no) else False
        )

        for r in ["rata4", "rata3", "rata2", "rata1", "zaliczka"]:
            if (
                self.cleaned_data.get(r + "_zaplacone")
                or self.cleaned_data.get(r + "_termin")
            ) and self.cleaned_data.get(r + "_kwota") is None:
                self._errors[r + "_kwota"] = self.error_class(
                    ["Należy podać kwotę (lub 0.00)"]
                )

        if faktura_status and not zliczacz_faktura_no:
            self._errors["faktura_status"] = self.error_class(
                [
                    "Nie możesz wybrać statusu, gdyż faktura nie została "
                    "wygenerowana w Zliczacz."
                ]
            )
        elif zliczacz_faktura_no and not zaplacone and faktura_status == "paid":
            self._errors["faktura_status"] = self.error_class(
                [
                    "Nie możesz wybrać tego statusu, gdyż nie została wprowadzona "
                    "data opłacenia (pole 'Zapłacone')."
                ]
            )
        elif zliczacz_faktura_no and (
            (zaplacone or zaliczka_zaplacone)
            and faktura_status not in ["paid", "zaliczka_paid"]
        ):
            self._errors["faktura_status"] = self.error_class(
                [
                    "Gdy faktura jest opłacona, nie można wybrać innego statusu "
                    "jak 'zapłacona'."
                ]
            )
        elif (
            zliczacz_faktura_no
            and not zaliczka_zaplacone
            and faktura_status == "zaliczka_paid"
        ):
            self._errors["faktura_status"] = self.error_class(
                [
                    "Nie możesz wybrać tego statusu, gdyż nie została wprowadzona "
                    "data opłacenia (pole 'Zaliczka zapłacone')."
                ]
            )

        if self.instance.pk and self.instance.termin != self.cleaned_data.get("termin"):
            self._zmiana_terminu = True

        return super().clean()

    def save(self, commit=True):
        update = self.instance.pk
        new_faktura_status = self.cleaned_data.get("faktura_status", "")
        obj = super().save(commit=commit)

        if (
            settings.GENERATE_INVOICE_THROUGH_API_ZLICZACZ
            and update
            and new_faktura_status
            and new_faktura_status != self.old_faktura_status
        ):

            if obj.faktura_status in ["paid", "zaliczka_paid"]:
                data_oplacenia = (
                    obj.zaplacone
                    if obj.faktura_status == "paid"
                    else obj.zaliczka_zaplacone
                )
            else:
                data_oplacenia = None

            update_invoice_status(
                obj.zliczacz_faktura_no,
                obj.faktura_status,
                data_oplacenia.isoformat() if data_oplacenia else None,
            )

        if self._aktualny_termin and self._aktualny_termin != obj.termin:
            obj._zmiana_terminu = self._aktualny_termin

        return obj


class ProwadzacyAdminForm(forms.ModelForm):
    class Meta:
        model = Prowadzacy
        fields = "__all__"

    # def clean(self):
    #     pokazywac = self.cleaned_data.get("pokazywac")
    #     pracowal_do = self.cleaned_data.get("pracowal_do")
    #
    #     if pokazywac and pracowal_do:
    #         self._errors['pracowal_do'] = self.error_class([
    #             "Nie możesz podać tej wartości gdy opcja \"Pokazywać\" "
    #             "jest zaznaczona."
    #         ])
    #     return super(ProwadzacyAdminForm, self).clean()


class DzienSzkoleniaAdminInlineFormset(forms.models.BaseInlineFormSet):
    # def clean(self):
    #     super(DzienSzkoleniaAdminInlineFormset, self).clean()
    #     total = 0
    #
    #     prowadzacy = getattr(self.instance, "prowadzacy", None)
    #     sala = getattr(self.instance, "sala", None)
    #     sprzet = getattr(self.instance, "sprzet", [])
    #     daty_szczegolowo = len(
    #         getattr(self.instance, "daty_szczegolowo", "").split(","))
    #
    #     prowadzacy_lista = []
    #     sala_lista = []
    #     sprzet_lista = []
    #
    #     for form in self.forms:
    #         if not form.is_valid():
    #             return
    #         if form.cleaned_data and not form.cleaned_data.get('DELETE'):
    #             total += 1
    #             prowadzacy_lista.append(form.cleaned_data["prowadzacy"])
    #             sala_lista.append(form.cleaned_data["sala"])
    #             sprzet_lista.append([i for i in form.cleaned_data["sprzet"]])
    #
    #     if total and daty_szczegolowo and total >= daty_szczegolowo:
    #         # Wychodzi na to, że przeciążyliśmy wszystkie daty, więc
    #         # sprawdźmy, czy istanieą w przeciążeniach: główna sala,
    #         # prowadzący, sprzęt
    #         if prowadzacy and prowadzacy not in prowadzacy_lista:
    #             raise ValidationError(
    #                 "Przeciążone zostały wszystkie dni, a główny prowadzący "
    #                 "({}) w nich nie występuje.".format(
    #                     prowadzacy.__str__()))
    #         if sala and sala not in sala_lista:
    #             raise ValidationError(
    #                 "Przeciążone zostały wszystkie dni, a główna sala "
    #                 "({}) w nich nie występuje.".format(
    #                     sala.__str__()))

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Musimy przekazać do każdego formularza należącego do formsetu, aby
        # przeprowadził walidację odnośnie daty, i sali.
        for form in self.forms:
            form.termin_instance = self.instance


class DzienSzkoleniaAdminInlineForm(forms.ModelForm):
    class Meta:
        model = DzienSzkolenia
        fields = "__all__"
        exclude = ()

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Pobieramy prowadzących. Prowądzacy może już nie współpracować z ALX
        # dlatego ich filtrujemy:
        #
        # 1. Jeśli termin szkolenia istanieje pobieramy Prowadzących
        #    pracujących min. do daty terminu.
        # 2. Jeśli dodajemy nowy termin pobieramy tylko aktualnie
        #    współpracujących.

        if self.instance.pk:
            prowadzacy_qs = Prowadzacy.objects.active(
                date=self.instance.terminszkolenia.termin
            )
        else:
            prowadzacy_qs = Prowadzacy.objects.active()
        self.fields["prowadzacy"].queryset = prowadzacy_qs

    def clean_sala(self):
        sala = self.cleaned_data.get("sala")

        if (
            sala
            and hasattr(self.termin_instance, "lokalizacja")
            and sala.lokalizacja != self.termin_instance.lokalizacja
        ):
            raise forms.ValidationError(
                "Lokalizacja sali nie pokrywa się z lokalizacją szkolenia."
            )
        return sala

    def clean_data(self):
        data = self.cleaned_data.get("data")

        if data and data.isoformat() not in self.termin_instance.daty_szczegolowo:
            raise forms.ValidationError(
                'Data nie występuje w liście dat "Daty szczegolowo".'
            )
        return data


class TerminSzkoleniaAdminForm(forms.ModelForm):
    daty_szczegolowo = forms.CharField(
        widget=MultipleDatePicker,
        required=False,
        help_text="wpisywać dla kursów i dla szkoleń jeśli są niestandardowe "
        "(normalne sobie zgadnę)",
    )
    wylacz_podrzedne = forms.BooleanField(
        required=False,
        label="Wyłącz publikację szkoleń podrzędnych.",
        help_text="funkcjonalność działa jedynie dla już opublikowanych terminów, nie działa przy publikacji nowych terminów"
    )

    class Meta:
        model = TerminSzkolenia
        fields = "__all__"
        exclude = ()
        widgets = {
            "czy_reklamowac": widgets.Select(
                choices=((True, "Tak"), (False, "Nie")),
            ),
            "gwarantowany": widgets.Select(
                choices=((True, "Tak"), (False, "Nie")),
            )
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        obj = kwargs.get("instance", None)

        if obj is not None:
            wysylaj_proformy = obj.szkolenie.wysylaj_powiadomienia_proformy
        else:
            wysylaj_proformy = None

        self.fields["szkolenie"].wysylaj_proformy = wysylaj_proformy

        # Pobieramy prowadzących. Prowądzacy może już nie współpracować z ALX
        # dlatego ich filtrujemy:
        #
        # 1. Jeśli termin szkolenia istanieje pobieramy Prowadzących
        #    pracujących min. do daty terminu.
        # 2. Jeśli dodajemy nowy termin pobieramy tylko aktualnie
        #    współpracujących.

        if self.instance.pk:
            prowadzacy_qs = Prowadzacy.objects.active(date=self.instance.termin)
        else:
            prowadzacy_qs = Prowadzacy.objects.active()

        self.fields["prowadzacy"].queryset = prowadzacy_qs
        # self.fields['dodatkowi_prowadzacy'].queryset = prowadzacy_qs

    def clean_program_szkolenia(self):
        program_szkolenia = self.cleaned_data.get("program_szkolenia")
        if program_szkolenia and not is_html(program_szkolenia):
            raise ValidationError("Program szkolenia musi zawierać tagi HTML.")
        return program_szkolenia

    def clean(self):
        termin_nadrzedny = self.cleaned_data.get("termin_nadrzedny")
        szkolenie = self.cleaned_data.get("szkolenie")
        daty_szczegolowo = self.cleaned_data.get("daty_szczegolowo")
        odbylo_sie = self.cleaned_data.get("odbylo_sie")
        sprzet = self.cleaned_data.get("sprzet")
        sala = self.cleaned_data.get("sala")
        prowadzacy = self.cleaned_data.get("prowadzacy")
        lokalizacja = self.cleaned_data.get("lokalizacja")
        termin_zakonczenia = self.cleaned_data.get("termin_zakonczenia")
        termin = self.cleaned_data.get("termin")
        obiady = self.cleaned_data.get("obiady")
        tryb = self.cleaned_data.get("tryb", 0)
        termin_zdalny = self.cleaned_data.get("termin_zdalny")

        if termin_zdalny and szkolenie and lokalizacja and termin:
            if lokalizacja.zdalna:
                self._errors["termin_zdalny"] = self.error_class(
                    ["Nie możesz ustawić tego pola dla terminu w Zdalnej lokalizacji."]
                )
            elif termin_zdalny.szkolenie != szkolenie:
                self._errors["termin_zdalny"] = self.error_class(
                    ["Wskazany termin musi być tym samym szkoleniem."]
                )
            elif not termin_zdalny.lokalizacja.zdalna:
                self._errors["termin_zdalny"] = self.error_class(
                    ["Wskazany termin nie jest w Zdalnej lokalizacji."]
                )
            elif termin_zdalny.termin != termin:
                self._errors["termin_zdalny"] = self.error_class(
                    ["Terminy są w różnych datach."]
                )
            elif termin_zdalny.tryb != tryb:
                self._errors["termin_zdalny"] = self.error_class(
                    ["Terminy są w różnych trybach."]
                )
            else:
                qs = TerminSzkolenia.objects.filter(termin_zdalny=termin_zdalny)
                if self.instance.id:
                    qs = qs.exclude(id=self.instance.id)
                if qs.exists():
                    self._errors["termin_zdalny"] = self.error_class(
                        ["Taki termin jest już przypisany do innego szkolenia."]
                    )                

        if termin and tryb:
            if termin.weekday() in [5, 6] and tryb != 3:
                self._errors["tryb"] = self.error_class(
                    [
                        "Szkolenie rozpoczyna się w weekend, więc tryb musi być "
                        "zaoczny."
                    ]
                )
            elif termin.weekday() not in [5, 6] and tryb == 3:
                self._errors["tryb"] = self.error_class(
                    ["Szkolenie zaoczne musi rozpoczynać się w weekend."]
                )

        if odbylo_sie and not sala:
            raise forms.ValidationError(
                "Jeśli szkolenie się odbywa, to musi być ustawiona sala."
            )

        if odbylo_sie and not prowadzacy:
            raise forms.ValidationError(
                "Jeśli szkolenie się odbywa, to musi być ustalony prowadzący."
            )

        if odbylo_sie and not sprzet:
            raise forms.ValidationError(
                "Jeśli szkolenie się odbywa, to musi być ustalony zestaw "
                "komputerowy."
            )

        if sala is not None and sala.lokalizacja != lokalizacja:
            raise forms.ValidationError(
                "Lokalizacja nie pokrywa się z lokalizacją sali."
            )

        if termin_zakonczenia is not None and termin_zakonczenia < termin:
            raise forms.ValidationError(
                "Termin zakończenia nie może być wcześniejszy niż termin "
                "rozpoczęcia."
            )

        if (
            obiady
            and obiady not in ("nie-ma-obiadow", "nie-ma-obiadow-weekend")
            and tryb == 2
        ):
            self._errors["obiady"] = self.error_class(
                ["Obiady nie mogą być przypisane do trybu wieczorowego."]
            )

        if termin_nadrzedny and szkolenie and lokalizacja:
            # Walidujemy nadrzednosc terminow
            if szkolenie.szkolenie_nadrzedne_id != termin_nadrzedny.szkolenie.pk:
                self._errors["termin_nadrzedny"] = self.error_class(
                    ["Brak reguły nadrzędności w Szkoleniach."]
                )
                return self.cleaned_data

            if lokalizacja != termin_nadrzedny.lokalizacja:
                self._errors["termin_nadrzedny"] = self.error_class(
                    ["Ten termin jest w innej lokalizacji."]
                )
                return self.cleaned_data

            # Sprawdzamy, czy poprawne dni
            podzbior_dni = szkolenie.podzbior_dni.split(",")
            daty = list(
                self.instance.daty(
                    daty_szczegolowo, termin_zakonczenia, szkolenie, termin
                )
            )

            if len(podzbior_dni) != len(daty):
                self._errors["termin_nadrzedny"] = self.error_class(
                    ["Podzbiór dni jest inny niż liczba dni tego terminu."]
                )
                return self.cleaned_data

            if sala and termin_nadrzedny.sala and sala != termin_nadrzedny.sala:
                self._errors["termin_nadrzedny"] = self.error_class(
                    ["Sala terminu nadrzędnego jest inna niż wybrana."]
                )
                return self.cleaned_data

            termin_nadrzedny_daty = list(termin_nadrzedny.daty())

            for no, date in enumerate(daty):
                day = int(podzbior_dni[no]) - 1

                try:
                    termin_nadrzedny_date = termin_nadrzedny_daty[day]
                except IndexError:
                    self._errors["termin_nadrzedny"] = self.error_class(
                        [
                            "W terminie nadrzędnym nie "
                            "istnieje dzień {0} szkolenia.".format(podzbior_dni[no])
                        ]
                    )
                else:
                    if date != termin_nadrzedny_date:
                        self._errors["termin_nadrzedny"] = self.error_class(
                            [
                                "Podzbiór dat się nie zgadza. Data tego terminu: "
                                "{0}, dzień podzbioru: {1}, data terminu "
                                "nadrzędnego: {2}.".format(
                                    date, podzbior_dni[no], termin_nadrzedny_daty[day]
                                )
                            ]
                        )
                        return self.cleaned_data
        return self.cleaned_data


class TerminSzkoleniaInlineAdminForm(TerminSzkoleniaAdminForm):
    dodatkowe_uwagi = forms.CharField(
        label="Uwagi www", widget=SmallTextarea, required=False
    )
    uwagi = forms.CharField(
        label="Uwagi internal", widget=SmallTextarea, required=False
    )


class PotencjalnyChetnyAdminForm(forms.ModelForm):
    class Meta:
        model = PotencjalnyChetny
        fields = "__all__"

    def clean(self):
        email = self.cleaned_data.get("email", "")
        telefon = self.cleaned_data.get("telefon", "")
        if not (email or telefon):
            raise forms.ValidationError("Wymagany email lub telefon.")
        return self.cleaned_data


##################
# Powiadomienia
##################


class UserNotificationReportAdminForm(forms.Form):
    date_from = forms.DateField(required=False)
    date_to = forms.DateField(required=False)

    def clean(self):
        date_from = self.cleaned_data.get("date_from")
        date_to = self.cleaned_data.get("date_to")

        if date_from and date_to and (date_from > date_to):
            self._errors["date_from"] = self.error_class(
                ["Data OD nie może być wieksz od daty DO."]
            )

        return self.cleaned_data

    def get_params(self):
        params = {}

        date_from = self.cleaned_data.get("date_from")
        date_to = self.cleaned_data.get("date_to")

        if date_from:
            params["user__created_at__gte"] = date_from
        if date_to:
            params["user__created_at__lte"] = date_to

        return params


class UserNotificationPersonalDataForm(forms.ModelForm):
    class Meta:
        model = UserNotification
        fields = ("full_name", "phone_number", "company_name")

    def clean(self):
        data = super().clean()

        # Usuwamy potencjalnie niebezpieczne znaki z pól formularza
        for field in self.fields:
            if data.get(field):
                data[field] = strip_tags(data[field])

                if field == "phone_number" and data[field]:
                    # Zostawiamy tylko cyfry
                    data[field] = re.sub(r"[^\d]+", "", data[field])
        return data


def get_manage_subscription_form(locations):
    class ManageSubscriptionForm(forms.ModelForm):
        class Meta:
            model = UserCoursesNotification
            fields = ("locations",)
            widgets = {"locations": forms.CheckboxSelectMultiple()}

        def __init__(self, *args, **kwargs):
            super().__init__(*args, **kwargs)
            self.fields["locations"].queryset = locations

        def save(self, commit=True):
            # Zmieniamy źródło rejestracji - przypadek, gdy zostaliśmy
            # zapisani przez automat, ale coś zmieniliśmy, wtedy ustawiamy
            # źródło na "użytkownika".
            self.instance.source = "www"
            return super().save(commit=True)

    return ManageSubscriptionForm


class SubscriptionForm(forms.Form):
    email = forms.EmailField(required=True, max_length=150)
    tos = forms.BooleanField()

    def __init__(self, *args, **kwargs):
        self.grouped_locations = kwargs.pop("grouped_locations", None)
        self.language = kwargs.pop("language")
        super().__init__(*args, **kwargs)

        # # Jeśli jesteśmy na wersji EN nie pokazujemy miejscowości
        # # (bo to tylko Londyn).
        # if self.language != 'en':

        all_locations = self.grouped_locations[0] | self.grouped_locations[1] | self.grouped_locations[2] | self.grouped_locations[3]

        self.fields["locations"] = forms.ModelMultipleChoiceField(
            queryset=all_locations, widget=forms.CheckboxSelectMultiple, required=False
        )


    def save(self, training):
        """
        Metoda dodaje nowego użytkownika powiadomień lub aktualizuje obecnego.
        Zwraca ona obiekt notyfikacji oraz wartość logiczną determinującą
        wysłanie maila aktywacyjnego.
        """

        email = self.cleaned_data["email"].lower()
        locations = self.cleaned_data["locations"]

        user, created = UserNotification.objects.get_or_create(email=email)

        send_activation_email = False

        if created or user.status == 2:
            # Tutaj sprawa jest prosta. Zapisujemy użytkownika jako nowego,
            # ustawiamy mu domyślny język.
            #
            #  lub
            #
            # Użytkownik był już wcześniej zapisany, ale się wypisał
            # (status == 2). W takim wypadku konto przechodzi ponownie w stan
            # "oczekuje na aktywacje".

            user.language = self.language
            user.activation_email_sent_at = datetime.datetime.now()
            user.resend_activation_email_counter = 0

            # Dotyczy (status == 2) - musimy wyczyscić stare wartości.
            user.status = 0
            user.last_email_sent_at = None
            user.activation_at = None

            if not user.tos_agreement:
                user.tos_agreement = datetime.datetime.now()

            user.save()

            send_activation_email = True
        elif user.status in (0, -1):
            # Przypadek, gdy użytkownik istnieje już w bazie, ale nie
            # jest aktywowany. W takim wypadku, wysyłamy kolejnego maila
            # aktywacyjnego, gdy od wysłania poprzedniego minęła 1 godzina.
            #
            # lub
            #
            # Użytkownik został oznaczony przez system jako
            # "nigdy niezweryfikowany" (status == -1) - w takim przypadku
            # należy zresetować te wartości.

            # Dotyczy (status == -1)
            user.activation_email_sent_at = datetime.datetime.now()
            user.resend_activation_email_counter = 0
            user.status = 0

            if not user.tos_agreement:
                user.tos_agreement = datetime.datetime.now()

            user.save()

            # Na razie wyłaczamy opcję godzinnego interwału pomiędzy wysyłkami

            # delta = datetime.datetime.now() - datetime.timedelta(hours=1)
            #
            # if (not user.activation_email_sent_at or
            #    user.activation_email_sent_at < delta):
            #     user.activation_email_sent_at = datetime.datetime.now()
            #     user.save()

            send_activation_email = True

        # Dla wszystkich przypadków, zrób aktualizację ustawień.
        course, course_created = UserCoursesNotification.objects.get_or_create(
            user=user, training=training
        )
        course.locations.clear()
        course.locations.set(locations)
        course.save()

        if course_created and user.status == 1:
            # Gdy zostało dodane nowe powiadomienie i użytkownik jest aktywny
            # poinformuj o tym Biuro.
            www.tasks.user_notifications_alert.delay(
                user.id, new_query_list=[{"id": course.id, "locations": []}]
            )

        return user, course, send_activation_email


class UserCoursesNotificationAdminForm(forms.ModelForm):
    class Meta:
        model = UserCoursesNotification
        fields = "__all__"

    def save(self, commit=True):
        if not self.instance.pk:
            # Jeśli dodajemy obiekt przez panel ustawmy źródło na "staff".
            self.instance.source = "staff"
        return super().save(commit=commit)


class UserNotificationAdminForm(forms.ModelForm):
    class Meta:
        model = UserNotification
        fields = "__all__"

    def __init__(self, *args, **kwargs):
        # Gdy dodajemy przez panel nową subskrypcję ustawmy dwa pola w innej
        # wartości niż są domyślnie.
        if not kwargs.get("instance"):
            if "initial" not in kwargs:
                kwargs["initial"] = {}
            kwargs["initial"].update(
                {
                    "status": 1,
                }
            )
        super().__init__(*args, **kwargs)


class EmailCertificate(forms.Form):
    email = forms.EmailField()


class CommaSeparatedEmailField(forms.Field):
    description = "Adresy email"

    def __init__(self, *args, **kwargs):
        self.token = kwargs.pop("token", ",")
        super().__init__(*args, **kwargs)
        self.widget.attrs["placeholder"] = "Adresy oddzielone przecinkami ..."

    def to_python(self, value):
        if value in EMPTY_VALUES:
            return []

        value = [item.strip() for item in value.split(self.token) if item.strip()]
        return list(set(value))

    def clean(self, value):
        """
        Check that the field contains one or more 'comma-separated' emails
        and normalizes the data to a list of the email strings.
        """
        value = self.to_python(value)

        if value in EMPTY_VALUES and self.required:
            raise forms.ValidationError("To pole jest wymagane.")

        for email in value:
            try:
                validate_email(email)
            except forms.ValidationError:
                raise forms.ValidationError("'%s' jest błędnym adresem email." % email)
        return value


class TerminSzkoleniaMailForm(forms.ModelForm):
    subject = forms.CharField(max_length=200, required=True, min_length=5)

    class Meta:
        model = TerminSzkoleniaMail
        fields = ("subject", "message", "participant_status", "attachment")

    def __init__(self, *args, **kwargs):
        self.term = kwargs.pop("term")
        self.author = kwargs.pop("author")
        super().__init__(*args, **kwargs)

    def clean_participant_status(self):
        participant_status = self.cleaned_data.get("participant_status")
        if participant_status is not None:
            if participant_status == 99:
                cnt = len(self.term.uczestnicy_niezrezygnowani())
            else:
                cnt = self.term.uczestnik_set.filter(status=participant_status).count()

            if not cnt:
                raise ValidationError("Brak Uczestników o podanym statusie.")
        return participant_status

    def save(self, commit=True):
        obj = super().save(commit=False)
        obj.term = self.term
        obj.author = self.author

        if commit:
            obj.save()
        return obj


class EmailInvoiceAdminForm(forms.Form):
    to_emails = CommaSeparatedEmailField(required=False)
    cc_emails = CommaSeparatedEmailField(required=False)
    subject = forms.CharField(max_length=200, required=True)
    content = forms.CharField(widget=forms.Textarea, required=True)


class EmailAgreementAdminForm(forms.Form):
    to_emails = CommaSeparatedEmailField(required=False)
    cc_emails = CommaSeparatedEmailField(required=False)
    subject = forms.CharField(max_length=200, required=True)
    content = forms.CharField(widget=forms.Textarea, required=True)


class FakturaKorektaAddAdminForm(forms.ModelForm):
    zliczacz_id = forms.CharField(
        label="Numer lub ID faktury z systemu " "zliczacz", required=True
    )
    make_defaults = forms.BooleanField(
        label="Automatycznie twórz pozycje " "korekty", initial=True, required=False
    )

    class Meta:
        model = FakturaKorekta
        fields = ()

    def clean_zliczacz_id(self):
        zliczacz_id = self.cleaned_data.get("zliczacz_id")
        if not zliczacz_id:
            raise ValidationError("To pole jest wymagane.")

        api_data, errors = generate_invoice_note_data(zliczacz_id)

        if errors:
            raise forms.ValidationError(
                "Błąd przy pobieraniu " "faktury: {0}".format(errors)
            )
        self.api_data = api_data
        return zliczacz_id

    def save_m2m(self, *args, **kwargs):
        pass

    def save(self, commit=True):
        return FakturaKorekta.objects.create_from_zliczacz(
            api_data=self.api_data, make_defaults=self.cleaned_data["make_defaults"]
        )


class FakturaWysylkaAddAdminForm(forms.ModelForm):
    zliczacz_id = forms.CharField(
        label="Numer lub ID faktury z systemu " "zliczacz", required=True
    )

    class Meta:
        model = FakturaWysylka
        fields = ()

    def clean_zliczacz_id(self):
        zliczacz_id = self.cleaned_data.get("zliczacz_id")
        if not zliczacz_id:
            raise ValidationError("To pole jest wymagane.")

        api_data, errors = check_invoice_data(zliczacz_id)

        if errors:
            raise forms.ValidationError(
                "Błąd przy pobieraniu faktury: {0}".format(errors)
            )
        self.api_data = api_data

        if FakturaWysylka.objects.filter(faktura_id=api_data["faktura_id"]).exists():
            raise ValidationError("Ta faktura została już dodana.")
        return zliczacz_id

    def save_m2m(self, *args, **kwargs):
        pass

    def save(self, commit=True):
        return FakturaWysylka.objects.create_from_zliczacz(api_data=self.api_data)


class LeaveAdminForm(forms.ModelForm):
    dates = forms.CharField(
        widget=MultipleDatePicker, required=True, label="Dni urlopu"
    )
    year = forms.ChoiceField(label="Za rok", required=True)

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        today = datetime.date.today()
        year_start = today.year

        if today.month > 10:
            year_start += 1

        self.fields["year"].choices = tuple(
            [(y, str(y)) for y in range(year_start, 2017, -1)]
        )

    class Meta:
        model = Leave
        fields = "__all__"


##################
# E-certyfikaty
##################

# class CertificateForm(forms.ModelForm):
#     class Meta:
#         model = Certificate
#         fields = ('email',)
#
#     def save(self, *args, **kwargs):
#         super(CertificateForm, self).save(*args, **kwargs)
#
#         www.tasks.update_certificate.delay(
#             self.instance.pk,
#             force_create_pdf=True
#         )


class EmailForm(forms.Form):
    email = forms.EmailField(required=True)
    captcha = NoReCaptchaField(
        site_key=settings.NORECAPTCHA_INVISIBLE_SITE_KEY,
        secret_key=settings.NORECAPTCHA_INVISIBLE_SECRET_KEY,
    )

    def __init__(self, *args, **kwargs):
        self.action_allowed = kwargs.pop("action_allowed")
        super().__init__(*args, **kwargs)

    def clean(self):
        data = super().clean()

        if not self.action_allowed:
            self._errors["email"] = self.error_class(
                [_("Musisz odczekać 30 minut pomiędzy kolejnymi próbami.")]
            )
        return data


def file_size_validator(fobj):
    filesize = fobj.size
    mb_limit = 15
    if filesize > mb_limit * 1024 * 1024.0:
        raise ValidationError(
            _("Maksymalny rozmiar pliku to %(size)sMB") % {"size": str(mb_limit)}
        )


class UczestnikPlikForm(forms.ModelForm):
    file = forms.ImageField(required=True, validators=[file_size_validator])

    class Meta:
        model = UczestnikPlik
        fields = ("file",)

    def __init__(self, *args, **kwargs):
        self.uczestnik = kwargs.pop("uczestnik")
        self.remote_addr = kwargs.pop("remote_addr")
        super().__init__(*args, **kwargs)

    def clean(self):
        data = super().clean()

        delta = datetime.datetime.now() - datetime.timedelta(hours=48)

        if (
            UczestnikPlik.objects.filter(
                participant=self.uczestnik, created_at__gte=delta
            ).count()
            >= 4
        ):
            self._errors["file"] = self.error_class(
                [_("Limit dodanych plików został osiągnięty.")]
            )
        return data

    def save(self, commit=True):
        obj = super().save(commit=False)
        obj.participant = self.uczestnik
        obj.remote_addr = self.remote_addr

        if commit:
            obj.save()
        return obj
