import datetime
import logging

from django.conf import settings
from django.core.mail import EmailMessage
from django.core.management.base import BaseCommand
from django.db.models import Q
from django.template.loader import render_to_string

from www.models import Uczestnik, UczestnikNotification

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    """
    Polecenie wysyła powiadomienia do Uczestników 1 dzień przed niezapłaconą ratą.
    """

    def handle(self, *args, **options):
        day_after_tomorrow = datetime.date.today() + datetime.timedelta(days=2)

        raty_2 = (
            Uczestnik.objects.filter(
                status__in=[1, 3],
                za_kurs_zaplace=3,
                #termin__szkolenie__language="pl",
                termin__zamkniete=False,
                termin__odbylo_sie=True,
            )
            .filter(
                Q(rata1_zaplacone__isnull=True, rata1_termin=day_after_tomorrow)
                | Q(rata2_zaplacone__isnull=True, rata2_termin=day_after_tomorrow)
            )
            .exclude(Q(email="") | Q(email__isnull=True))
            .select_related()
        )

        raty_5 = (
            Uczestnik.objects.filter(
                status__in=[1, 3],
                za_kurs_zaplace=10,
                #termin__szkolenie__language="pl",
                termin__zamkniete=False,
                termin__odbylo_sie=True,
            )
            .filter(
                Q(zaliczka_zaplacone__isnull=True, zaliczka_termin=day_after_tomorrow)
                | Q(rata1_zaplacone__isnull=True, rata1_termin=day_after_tomorrow)
                | Q(rata2_zaplacone__isnull=True, rata2_termin=day_after_tomorrow)
                | Q(rata3_zaplacone__isnull=True, rata3_termin=day_after_tomorrow)
                | Q(rata4_zaplacone__isnull=True, rata4_termin=day_after_tomorrow)
            )
            .exclude(Q(email="") | Q(email__isnull=True))
            .select_related()
        )

        participants = list(raty_2) + list(raty_5)

        for participant in participants:

            notification_type = None
            price = None

            if participant.za_kurs_zaplace == 3:
                if (
                    participant.rata1_zaplacone is None
                    and participant.rata1_termin == day_after_tomorrow
                ):
                    notification_type = "rata_1_niezaplacona"
                    price = participant.rata1_kwota
                elif (
                    participant.rata2_zaplacone is None
                    and participant.rata2_termin == day_after_tomorrow
                ):
                    notification_type = "rata_2_niezaplacona"
                    price = participant.rata2_kwota
            elif participant.za_kurs_zaplace == 10:
                if (
                    participant.zaliczka_zaplacone is None
                    and participant.zaliczka_termin == day_after_tomorrow
                ):
                    notification_type = "rata_1_niezaplacona"
                    price = participant.zaliczka_kwota
                elif (
                    participant.rata1_zaplacone is None
                    and participant.rata1_termin == day_after_tomorrow
                ):
                    notification_type = "rata_2_niezaplacona"
                    price = participant.rata1_kwota
                elif (
                    participant.rata2_zaplacone is None
                    and participant.rata2_termin == day_after_tomorrow
                ):
                    notification_type = "rata_3_niezaplacona"
                    price = participant.rata2_kwota
                elif (
                    participant.rata3_zaplacone is None
                    and participant.rata3_termin == day_after_tomorrow
                ):
                    notification_type = "rata_4_niezaplacona"
                    price = participant.rata3_kwota
                elif (
                    participant.rata4_zaplacone is None
                    and participant.rata4_termin == day_after_tomorrow
                ):
                    notification_type = "rata_5_niezaplacona"
                    price = participant.rata4_kwota

            if notification_type is None or price is None:
                logger.error(
                    "Nie można ustalić płatności raty dla "
                    "uczestnika {0}.".format(participant.pk)
                )
                continue

            try:
                UczestnikNotification.objects.get(
                    participant=participant, notification_type=notification_type
                )
            except UczestnikNotification.DoesNotExist:
                language = participant.termin.szkolenie.language

                message = render_to_string(
                    "www/notifications/installment_notification_{}.html".format(language),
                    {
                        "user": participant,
                        "price": str(participant.cena_brutto_rata(price)),
                        "training": participant.termin.szkolenie,
                        "installment_no": notification_type.replace(
                            "rata_", ""
                        ).replace("_niezaplacona", ""),
                        "domain": settings.DOMENY_DLA_JEZYKOW[language],
                        "protocol": settings.FORCE_SSL and "https" or "http",
                    },
                )

                UczestnikNotification.objects.create(
                    participant=participant,
                    notification_type=notification_type,
                    email=participant.email,
                    message=message,
                )
            else:
                continue

            try:
                if language == "en":
                    mail_from = settings.MAIL_FROM_ADDRESS_EN
                else:
                    mail_from = settings.MAIL_FROM_ADDRESS

                msg = EmailMessage(
                    "Przypomnienie o terminie płatności" if language == "pl" else "Installment payment reminder",
                    message,
                    mail_from,
                    to=[participant.get_email_ksiegowosc()],
                    bcc=[settings.MAIL_ARCHIVE_MONITOR],
                )
                msg.content_subtype = "html"
                msg.send()
            except:
                logger.exception(
                    "Błąd przy wysyłce maila 'przypomnienie o racie' dla "
                    "użytkownika ID: {0} ({1})".format(
                        participant.pk, participant.email
                    )
                )
