import datetime
import logging

from django.conf import settings
from django.core.mail import EmailMessage
from django.core.management.base import BaseCommand
from django.db.models import Q
from django.template.loader import render_to_string
from django.utils.translation import activate, ugettext_lazy as _

from optout.utils import email_optout
from www.models import TerminSzkolenia, Uczestnik, UserNotification, UserNotificationLog, Lokalizacja

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    """
    Polecenie wysyła powiadomienia o dodanych i uruchomionych terminach szkoleń
    do zapisanych użytkowników w `UserNotification`.
    """

    def handle(self, *args, **options):
        now = datetime.datetime.now()
        today = now.date()

        # Filtruje użytkowników pod względem ostatniej aktywności
        # (otrzymania ostatnigo powiadomienia) - domyślnie 2 dni temu.
        sending_interval = settings.USER_NOTIFICATION_SENDING_INTERVAL_HOURS

        # Warunkuje, że termin w powiadomieniu musiał zostać odadny min.
        # 2 godziny temu.
        two_hours_earlier = now - datetime.timedelta(hours=2)

        # Służy do uwzględniania szkoleń, zakończonych mniej jak 7 dni temu.
        seven_days_earlier = today - datetime.timedelta(days=7)

        # Warunkuje, że początek startu szkolenie musi być min. następnego
        # dnia.
        tommorow = (now + datetime.timedelta(hours=24)).date()

        for user in UserNotification.objects.ready_for_notifications(sending_interval):

            if email_optout(user.email):
                continue

            # Ostatnia data akcji, uwzględniamy albo datę ostatniej wysyłki,
            # albo rejestracji.
            last_action_date = user.last_email_sent_at or user.created_at

            # Pobieramy teraz preferencje użytkownika (szkolenia i lokalizacje
            # na jakie się zapisał).
            preferences = user.preferences.prefetch_related("locations")

            # `email_terms` przechowuje listę wszystkich szkoleń jakie
            # powinniśmy wysłać do użytkownika:
            #   :created - nowo utworzone
            #   :started - rozpoczęte
            email_terms = {
                "created": [],
                "started": [],
                "before_start": [],
            }

            # Kolejka dla logów
            logs = []

            for preference in preferences:
                # Nowy warunek od 25-02-2015. Osoby, które zapisały się
                # na termin danego szkolenia (czyli są jego Uczestnikami),
                # w momencie, gdy dostają o nim powiadomienie, nie do końca je
                # rozumieją i myślą, że nastąpiła jakaś zmiana lub mają
                # coś potwierdzać. Dlatego sprawdzamy, czy osoba nie jest
                # czasem przyszłym uczestnikiem dowolnego terminu danego
                # szkolenia. Jeśli tak omijamy to szkolenie.
                #
                # Uwaga: Z racji tego, że BOK nie od razu zmienia statusy
                # Uczestników i Szkoleń, uwzględniamy też szkolenia zakończone
                # mniej jak 7 dni temu.
                # Zmiany 10.06.2020: https://app.asana.com/0/4723313594922/1179609181373273
                already_in = (
                    Uczestnik.objects.filter(
                        email__iexact=user.email,
                        termin__szkolenie_id=preference.training_id,
                    )
                    .filter(
                        Q(
                            ~Q(termin__odbylo_sie=False),
                            termin__termin__gte=seven_days_earlier,
                            status__in=[-3, -2, -1, 0, 1, 3],
                        )
                        | Q(
                            termin__termin_zakonczenia__isnull=False,
                            termin__termin_zakonczenia__gte=seven_days_earlier,
                            termin__odbylo_sie=True,
                            status__in=[1, 3],
                        )
                    )
                    .count()
                )

                if already_in:
                    continue

                # Zmiana: do wszystkich wysyłamy terminy lokalizacji zdalnej, ale tylko do terminow (is_started)
                # https://app.asana.com/0/4723313594922/1202739204833928
                locations_ids = []
                has_sala_zdalna = False

                for location in preference.locations.all():
                    locations_ids.append(location.id)
                    if location.zdalna:
                        has_sala_zdalna = True

                if not has_sala_zdalna:
                    sala_zdalna = Lokalizacja.get_zdalna(user.language or settings.DEFAULT_LANGUAGE)
                    if sala_zdalna:
                        locations_ids.append(sala_zdalna.id)

                # Znajdź terminy szkoleń, które:
                #   - przypisane są do szkolenia z preferencji
                #   - przypisane są do jednej z lokalizacji z preferencji
                #   - zostały dodane min. 2 godziny temu
                #   - nie są zamknięte
                #   - data rozpoczęcia szkolenia jest w przyszłości (min. jutro)
                #   - data utworzenia lub staru terminu jest późniejsza niż
                #     data ostatniej akcji użytkownika.
                terms = TerminSzkolenia.objects.get_for_notifications(
                    training_id=preference.training_id,
                    locations_ids=locations_ids,
                    created_at=two_hours_earlier,
                    term_date=tommorow,
                    last_action_date=last_action_date,
                )

                for term in terms:
                    # Ustalamy priorytet - jeśli termin jest zarówno dodany
                    # jak i uruchomiony informujemy tylko o tym, że termin
                    # jest uruchomiony (nawet jeśli został uruchomiony mniej
                    # jak 2 godziny temu - ważne, że został dodany więcej jak
                    # 2 godziny temu).

                    is_started = bool(term.jobs_state)

                    # Teraz sprawdzamy, czy termin posiada wymaganą liczbę
                    # Uczestników (lub "min -1" dla Warszawy).

                    before_start = False

                    if not is_started:
                        participants_count = (
                            Uczestnik.objects.filter(termin=term, nierentowny=False)
                            .exclude(status=4)
                            .count()
                        )

                        min_grupa = term.szkolenie.min_grupa

                        if (
                            min_grupa
                            and term.lokalizacja.fullname.lower() == "warszawa"
                        ):
                            min_grupa -= 1

                        if participants_count >= min_grupa or term.czy_reklamowac:
                            before_start = True

                    # Teraz dodatkowe zabezpieczenie - sprawdzamy w logach,
                    # czy takie powiadomienie czasami już nie wyszło do
                    # tego użytkownika.

                    already_sent = UserNotificationLog.objects.filter(
                        user=user,
                        term_id=term.pk,
                        location_id=term.lokalizacja_id,
                        notification_type=UserNotificationLog.get_status(
                            is_started, before_start
                        ),
                    ).count()

                    if not already_sent:
                        # Nie było takiego powiadomienia, możemy dodać je
                        # do kolejki.
                        if is_started:
                            email_terms["started"].append(term)
                            logs.append((term, is_started, before_start))
                        elif before_start:
                            if not has_sala_zdalna and term.lokalizacja.zdalna:
                                continue                            
                            email_terms["before_start"].append(term)
                            logs.append((term, is_started, before_start))
                        elif term.created_at >= user.created_at:
                            if not has_sala_zdalna and term.lokalizacja.zdalna:
                                continue                            
                            email_terms["created"].append(term)
                            logs.append((term, is_started, before_start))

            if (
                email_terms["created"]
                or email_terms["started"]
                or email_terms["before_start"]
            ):
                try:
                    lang = user.language

                    if lang == "en":
                        mail_from = settings.MAIL_FROM_ADDRESS_NOTIFICATION_EN
                    else:
                        mail_from = settings.MAIL_FROM_ADDRESS_NOTIFICATION

                    activate(lang)

                    # Sprawdzamy warunki umiejscowienia w tekście informacji o
                    # możliwości zaproponowania terminu.
                    # Jeśli w terminach utworzonych (`email_terms['created']`)
                    # jest jeden typ szkolenia (nie będący kursem) to dodajemy
                    # informację wraz z linkiem do formularza powiązanego z
                    # tym szkoleniem. Jeśli natomiast jest więcej jak jedno
                    # szkolenie (bedące kursem) to też umieszczamy tekst, ale
                    # bez linku.
                    trainings_in_terms = any(
                        [not e.is_kurs() for e in email_terms["created"]]
                    )

                    unique_created_trainings = set(
                        [e.szkolenie.pk for e in email_terms["created"]]
                    )

                    if trainings_in_terms and len(list(unique_created_trainings)) == 1:
                        only_one_training = email_terms["created"][0]
                    else:
                        only_one_training = False

                    # Robimy jeszcze jedną rzecz, mianowicie, jeśli w liście
                    # szkoleń (wszystkich) jest kurs zawodowy musimy dodać
                    # dodatkową informację w mailu (rozróżniając, czy oprócz
                    # kursu są inne szkolenia lub kursy).
                    all_terms = (
                        email_terms["created"]
                        + email_terms["started"]
                        + email_terms["before_start"]
                    )
                    courses_in_terms = any([e.is_kurs() for e in all_terms])

                    unique_all_trainings = set([e.szkolenie.pk for e in all_terms])

                    if courses_in_terms and len(list(unique_all_trainings)) == 1:
                        only_one_course = all_terms[0]
                    else:
                        only_one_course = False

                    def has_sala_zdalna(terms):
                        return any([t.lokalizacja.zdalna for t in terms])

                    msg_content = render_to_string(
                        "www/notifications/user_notifications_email_"
                        "{0}.html".format(lang),
                        {
                            "user": user,
                            "terms_created": email_terms["created"],
                            "terms_started": email_terms["started"],
                            "terms_before_start": email_terms["before_start"],
                            "courses_in_terms": courses_in_terms,
                            "trainings_in_terms": trainings_in_terms,
                            "only_one_course": only_one_course,
                            "only_one_training": only_one_training,
                            "domain": settings.DOMENY_DLA_JEZYKOW[lang],
                            "protocol": settings.FORCE_SSL and "https" or "http",
                            "has_sala_zdalna": has_sala_zdalna(email_terms["created"] + email_terms["started"] + email_terms["before_start"]),
                        },
                    )

                    msg = EmailMessage(
                        _("Powiadomienie z firmy ALX (www.alx.pl)"),
                        msg_content,
                        mail_from,
                        [user.email],
                    )
                    msg.content_subtype = "html"
                    msg.send()
                except:
                    logger.exception(
                        "Błąd przy wysyłce powiadomienia dla użytkownika "
                        "ID: {0} ({1})".format(user.pk, user.email)
                    )
                else:
                    # Bradzo ważne! Zrób aktualizację daty wysyłki i dodaj
                    # logi.
                    user.last_email_sent_at = now
                    user.save()

                    # Tutaj nie robimy bulk_create, gdyż przy błędzie jednego
                    # zapisu, stracimy wszystko. Wykorzystanie `get_or_create`
                    # ma sens, gdyż jest tam już cała logika transakcji i
                    # race condition.
                    #
                    # Obiekt `log` to:
                    #   - log[0] - obiekt terminu szkolenia
                    #   - log[1] - wartość logiczna mówiąca o tym, czy
                    #              termin jest rozpoczęty
                    #   - log[2] - wartość logiczna mówiąca o tym, czy
                    #              termin ma odpowienią ilość uczestników
                    for log in logs:
                        try:
                            UserNotificationLog.objects.get_or_create(
                                user=user,
                                term=log[0],
                                location_id=log[0].lokalizacja_id,
                                notification_type=UserNotificationLog.get_status(
                                    log[1], log[2]
                                ),
                            )
                        except:
                            # Ewentualne błędy możemy pominąć, gdyż logi pełnią
                            # rolę dodatkoweo zabezpieczenia, nie głównego.
                            logger.warning(
                                "Błąd przy zapisie logu dla Terminu ID: {0} "
                                "dla użytkownika ID: {1} ({2})".format(
                                    log[0].pk, user.pk, user.email
                                )
                            )
