import datetime
import logging

from django.conf import settings
from django.core.exceptions import ValidationError
from django.core.management.base import BaseCommand
from django.core.validators import validate_email
from django.db import models, transaction

from optout.utils import email_optout
from www.models import (
    TerminSzkolenia,
    Uczestnik,
    UserCoursesNotification,
    UserNotification,
)

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    """
    Polecenie "poluje" na nieprzeszkolonych uczestników szkoleń i dodaje ich
    do powiadomień mailowych.
    """

    def handle(self, *args, **options):
        today = datetime.date.today()

        # Filtruje terminy szkoleń - nie starsze niż (domyślnie 180 dni)
        max_training_age = today - datetime.timedelta(
            days=settings.UNTRAINED_USERS_MAX_TRAINING_AGE
        )

        # Pobierz wszystkie terminy, które się nie odbyły i nie są starsze
        # niż `max_training_age`.
        terms = TerminSzkolenia.objects.get_for_untrained(max_training_age)

        # Kolejka `preferences` przechowuje wszystkich użytkowników gotowych
        # do zapisania na powiadomienia wraz ze szkoleniami i lokalizacjami.
        # Struktura:
        #
        # preferences = {
        #     '<email>': {
        #          'trainings': {
        #               '<training_pk>': [<location_pk>, ...]
        #          },
        #          'language': <language>,
        #     }
        # }
        preferences = {}

        for term in terms:
            training = term.szkolenie
            location = term.lokalizacja

            # Znajdź wszystkich użytkowników terminu `term`, który:
            #   - ma status różny od 4
            #   - ma ustawiony adres email
            participants = term.uczestnik_set.exclude(
                status=4,
            ).exclude(models.Q(email="") | models.Q(email__isnull=True))

            for participant in participants:
                # Sprawdź, czy email użytkownika jest poprawny - jeśli nie
                # nie ma sensu dalej nic robić.
                email = participant.email.lower()

                try:
                    validate_email(email)
                except ValidationError:
                    continue

                if email_optout(email):
                    continue

                # Teraz bardzo ważne, musimy sprawdzić, czy istnieje taki
                # użytkownik jak `participant`, który był uczestnikiem
                # terminu szkolenia takim, że:
                #   - jest ukończone (`odbylo_sie=True`)
                #   - jest przypisane do szkolenia `term.szkolenie`
                #   - jest młodsze lub równe terminowo niż `term`
                #   - zostało zakończone
                #
                # Uwaga: lokalizacja nas nie interesuje; jeśli termin nie
                #        odbył się w Krakowie, ale potem był w Warszawie i
                #        uczestniczył w nim nasz użytkownik to jest OK.
                exist = (
                    Uczestnik.objects.filter(
                        email__iexact=email,
                        termin__odbylo_sie=True,
                        termin__szkolenie=training,
                        termin__termin__gte=term.termin,
                        termin__termin__lt=today,
                    )
                    .exclude(termin__pk=term.pk)
                    .distinct()
                    .count()
                )

                if not exist:
                    # Wszystko wskazuje na to, że ten użytkownik nie został
                    # przeszkolony. Sprawdźmy zatem, czy nie istnieje już w
                    # Powiadomieniach. Nie jest ważny status użytkownika w
                    # powiadomieniach, ani rodzaj szkoleń na jakie się
                    # zapisał. Jeśli tam jest to zostawiamy go w spokoju.

                    check_if_exist = UserNotification.objects.filter(
                        email__iexact=email
                    ).count()

                    if not check_if_exist:
                        # Możemy zatem dodać użytkownika do kolejki
                        # `preferences`.

                        # Sprawdź, czy taki uzytkownik już jest, jeśli nie
                        # stwórz go.

                        if email not in preferences:
                            preferences[email] = {
                                "trainings": {},
                                "language": term.language,
                                "participant": participant,
                            }

                        # Sprawdź, czy takie szkolenie już jest w `preferences`
                        # - jeśli nie dodaj je. W przeciwnym razie, sprawdź,
                        # czy taka lokalizacja już jest - jeśli nie dodaj ją.

                        if training.pk in preferences[email]["trainings"]:
                            # Dodaj lokalizację - ie przejmujmy się
                            # powtórzeniami, przed zapisem robimy `set`
                            # na kolekcji.
                            preferences[email]["trainings"][training.pk].append(
                                location.pk
                            )
                        else:
                            preferences[email]["trainings"] = {
                                training.pk: [location.pk]
                            }

                        # Ostatnia kwestia to język powiadomień - ustalamy,
                        # że priorytet ma język polski, jeśli został zatem
                        # już wcześniej ustawiony to go nie zmieniamy -
                        # wszystkie inne modyfikujemy.

                        if preferences[email]["language"] != "pl":
                            preferences[email]["language"] = term.language

        # Jeśli "upolowaliśmy" kogoś do powiadomień - zapiszmy go.
        if preferences:

            def add_to_notification(email, courses, language, participant):
                """
                Funkcja tworzy obiekt powiadomień i preferencji w transakcji,
                w przypadku błędu, nie zapisujemy nic - niekompletne dane
                są nieprzydatne.
                """

                try:
                    with transaction.atomic():
                        user = UserNotification.objects.create(
                            email=email,
                            status=1,
                            source="system",
                            language=language,
                            full_name=participant.get_nazwa_oneline(),
                            phone_number=participant.telefon,
                            company_name=participant.faktura_firma,
                        )
                        for training_id, locations in list(courses.items()):
                            e = UserCoursesNotification.objects.create(
                                user=user, training_id=training_id, source="system"
                            )
                            e.save()
                            e.locations.set(list(set(locations)))
                            e.save()
                except:
                    logger.exception(
                        "Could not add user '{0}' to " "notification".format(email)
                    )
                    return False
                return True

            # Zapisujemy po jednym użytkowniku
            for email, preference in list(preferences.items()):
                add_to_notification(
                    email,
                    preference["trainings"],
                    preference["language"],
                    preference["participant"],
                )
