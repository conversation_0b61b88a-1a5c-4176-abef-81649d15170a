import datetime
import logging
from decimal import Decimal

from django.conf import settings
from django.core import signing
from django.core.mail import EmailMessage
from django.core.management.base import BaseCommand
from django.template.loader import render_to_string
from django.utils.translation import activate, ugettext as _

from www.models import (
    ContinuationLog,
    DiscountCode,
    TerminSzkolenia,
    TerminSzkoleniaLog,
)

logger = logging.getLogger(__name__)

SPAM_DATES = (
    "15.02",
    "15.05",
    "17.08",
    "15.11",
)


class Command(BaseCommand):
    """
    Polecenie wysyła powiadomienia o kontynuacji szkoleń.

    FLOW:

        1. D<PERSON>ń przed ostatnim zjazdem wszystkich uczestników kursów i szkoleń,
           które mają ustawioną flagę o rozsyłce kontynuacji, wys<PERSON><PERSON><PERSON> maila, że jest
           kontynuacja, pod warunkiem że:

        <PERSON>. Uczestnik ma status równy 1 (potwierdzone, ...) lub 3 (prz<PERSON><PERSON><PERSON>lony)

        B. Uczestnik nie uczestniczył nigdy w<PERSON>j,
           ani nie zapisał sie już na taką kontynuacją (w przyszłości), niezależnie
           od statusu uczestnictwa

        C. Uczestnik nie zrezygnował z notyfikacji o kontynuacji

        D. Uczestnik nie dostał wcześniej pierwszego lub drugiego powiadomienia o tej
           kontynuacji

                        ***

        2. W dniach: 15.02, 15.05, 17.08, 15.11 wysyłamy takiego samego maila drugi raz
           (lub pierwszy), pod warunkiem że:

        A. Kurs/szkolenie dla uczestników którego wysyłamy maile rozpoczął się nie
           wcześniej niż 01.01.2018 i zakończył dzień przed uruchomieniem tej wysyłki

        B. Uczestnik ma status równy 1 (potwierdzone, ...) lub 3 (przeszkolony)

        C. Minęło min. 6 tyg od pierwszego powiadomienia lub w ogóle nie było pierwszego
           powiadomienia

        D. Uczestnik nie zrezygnował z notyfikacji o kontynuacji

        E. Uczestnik nie uczestniczył nigdy wcześniej,
           ani nie zapisał sie już na taką kontynuacją (w przyszłości), niezależnie
           od statusu uczestnictwa
    """

    def send_email(
        self,
        uczestnik,
        szkolenie_kontynuacja,
        terminy_kontynuacja=None,
        kod_rabatowy=None,
    ):
        lang = szkolenie_kontynuacja.language

        msg_content = render_to_string(
            "www/emails/kontynuacja_{0}.html".format(lang),
            {
                "uczestnik": uczestnik,
                "szkolenie_kontynuacja": szkolenie_kontynuacja,
                "terminy_kontynuacja": terminy_kontynuacja,
                "kod_rabatowy": kod_rabatowy,
                "email_token": signing.dumps(
                    {"email": uczestnik.email.lower()},
                    salt="signing.continuation_training",
                ),
                "domain": settings.DOMENY_DLA_JEZYKOW[lang],
                "protocol": settings.FORCE_SSL and "https" or "http",
            },
        )

        try:
            msg = EmailMessage(
                _("Chcesz kontynuować naukę? Masz rabat na kolejny kurs."),
                msg_content,
                settings.MAIL_FROM_ADDRESS_EN
                if lang == "en"
                else settings.MAIL_FROM_ADDRESS,
                [uczestnik.email],
                bcc=[settings.MAIL_ARCHIVE_MONITOR],
            )
            msg.content_subtype = "html"
            msg.send()
        except:
            logger.exception(
                "Błąd przy wysyłce maila o kontynuacji dla Uczestnika "
                "ID: {0} ({1})".format(uczestnik.pk, uczestnik.email)
            )
            return False
        return True

    def send_email_to_trener(self, termin):
        lang = termin.szkolenie.language

        msg_content = render_to_string(
            "www/emails/kontynuacja_dla_trenera.html",
            {
                "termin": termin,
                "domain": settings.DOMENY_DLA_JEZYKOW[lang],
                "protocol": settings.FORCE_SSL and "https" or "http",
            },
        )

        try:
            prowadzacy_email = termin.prowadzacy.user.email
            assert prowadzacy_email

            msg = EmailMessage(
                "Wspomnij ludziom na sali o kontynuacji Twojego szkolenia.",
                msg_content,
                settings.MAIL_FROM_ADDRESS_EN
                if lang == "en"
                else settings.MAIL_FROM_ADDRESS,
                [prowadzacy_email],
                bcc=[settings.MAIL_ARCHIVE_MONITOR],
            )
            msg.content_subtype = "html"
            msg.send()
        except:
            logger.warning(
                "[Mail kontynuacja] Trener ({0}) nie ma przypisanego użytkownika".format(
                    termin.prowadzacy
                )
            )
            return False
        return True

    def send_first_notification(self):
        tomorrow = datetime.date.today() + datetime.timedelta(days=1)

        for termin in TerminSzkolenia.objects.get_for_continuation(tomorrow):

            activate(termin.language)

            uczestnicy = termin.uczestnik_set.filter(status__in=[1, 3]).select_related(
                "termin__szkolenie__kontynuacja"
            )

            if uczestnicy:
                szkolenie_kontynuacja = termin.szkolenie.kontynuacja
                terminy_kontynuacja = szkolenie_kontynuacja.terminy().order_by(
                    "lokalizacja_id", "termin"
                )

                for uczestnik in uczestnicy:
                    if uczestnik.can_send_first_continuation_email():

                        kod_rabatowy = DiscountCode.objects.create(
                            source="continuation_training", discount=Decimal(7)
                        )

                        result = self.send_email(
                            uczestnik,
                            szkolenie_kontynuacja,
                            terminy_kontynuacja,
                            kod_rabatowy,
                        )

                        if result:
                            ContinuationLog.objects.create(
                                email=uczestnik.email.lower(),
                                term=termin,
                                training=szkolenie_kontynuacja,
                                notification_type="first_notification",
                                discount_code=kod_rabatowy,
                                participant=uczestnik,
                            )

            if not TerminSzkoleniaLog.objects.filter(
                log_type="kontynuacja_do_trenera", term=termin
            ).exists():
                result = self.send_email_to_trener(termin)

                if result:
                    TerminSzkoleniaLog.objects.create(
                        log_type="kontynuacja_do_trenera", term=termin
                    )

    def send_second_notification(self):
        for termin in TerminSzkolenia.objects.get_for_continuation():

            activate(termin.language)

            uczestnicy = termin.uczestnik_set.filter(status__in=[1, 3]).select_related(
                "termin__szkolenie__kontynuacja"
            )

            if uczestnicy:
                szkolenie_kontynuacja = termin.szkolenie.kontynuacja
                terminy_kontynuacja = szkolenie_kontynuacja.terminy().order_by(
                    "lokalizacja_id", "termin"
                )

                for uczestnik in uczestnicy:
                    if uczestnik.can_send_second_continuation_email():

                        kod_rabatowy = DiscountCode.objects.create(
                            source="continuation_training", discount=Decimal(7)
                        )

                        result = self.send_email(
                            uczestnik,
                            szkolenie_kontynuacja,
                            terminy_kontynuacja,
                            kod_rabatowy,
                        )

                        if result:
                            ContinuationLog.objects.create(
                                email=uczestnik.email.lower(),
                                term=termin,
                                training=szkolenie_kontynuacja,
                                notification_type="second_notification",
                                discount_code=kod_rabatowy,
                                participant=uczestnik,
                            )

    def handle(self, *args, **options):
        self.send_first_notification()

        if datetime.date.today().strftime("%d.%m") in SPAM_DATES:
            self.send_second_notification()
