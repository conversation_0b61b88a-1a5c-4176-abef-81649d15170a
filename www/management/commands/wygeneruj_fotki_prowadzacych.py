import os
import urllib.error
import urllib.parse
import urllib.request

from django.conf import settings
from django.core.management.base import BaseCommand

from www.models import Prowadzacy


class Command(BaseCommand):
    def handle(self, *args, **options):
        if settings.DEBUG:
            sylwetka = urllib.request.urlopen(
                urllib.request.Request("http://placekitten.com/200/300")
            ).read()
            for prowadzacy in Prowadzacy.objects.exclude(fotka=""):
                if not os.path.exists(prowadzacy.fotka.path):
                    katalog_fotki = os.path.dirname(prowadzacy.fotka.path)
                    if not os.path.exists(katalog_fotki):
                        os.makedirs(katalog_fotki)
                    with open(prowadzacy.fotka.path, "wb") as plik_z_sylwetka:
                        plik_z_sylwetka.write(sylwetka)
