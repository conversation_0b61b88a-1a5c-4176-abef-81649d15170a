import sys
from subprocess import call

from django.conf import settings
from django.core import management
from django.core.management.commands.test import Command as TestCommand


class Command(TestCommand):
    def add_arguments(self, parser):
        parser.add_argument(
            "--nopep",
            action="store_true",
            dest="nopep",
            default=False,
            help="don't run flake8",
        )
        parser.add_argument(
            "--nolocale",
            action="store_true",
            dest="nolocale",
            default=False,
            help="don't make locale messages",
        )
        parser.add_argument(
            "--noblack",
            action="store_true",
            dest="noblack",
            default=False,
            help="don't run black",
        )
        parser.add_argument(
            "--noisort",
            action="store_true",
            dest="noisort",
            default=False,
            help="don't run isort",
        )
        super().add_arguments(parser)

    def handle(self, *args, **options):
        nopep = options.pop("nopep")
        nolocale = options.pop("nolocale")
        noblack = options.pop("noblack")
        noisort = options.pop("noisort")

        if not noisort:
            if call(
                [
                    "isort",
                    "--multi-line=3",
                    "--trailing-comma",
                    "--combine-as",
                    "--line-width=88",
                    "--force-grid-wrap=0",
                    "-py=38",
                    ".",
                ]
            ):
                sys.exit(1)

        if not noblack:
            if call(["black", "--target-version=py38", "-l 88", "."]):
                sys.exit(1)

        if not nolocale:
            languages = [
                lang
                for lang, name in settings.LANGUAGES
                if lang != settings.LANGUAGE_CODE
            ]
            management.call_command("makemessages", locale=languages)
            management.call_command("compilemessages", locale=languages)

        management.call_command("collectstatic", interactive=False)

        # if not nopep:
        #     if call(
        #         [
        #             "flake8",
        #             "--max-line-length=88",
        #             "--ignore=W503,E203,E731,F405",
        #             "--exclude=*/migrations/*,media/*,static_collected/*,uploads/*,"
        #             "assets/*",
        #         ]
        #     ):
        #         sys.exit(1)

        super().handle(*args, **options)
