import datetime

from django.conf import settings
from django.core.mail import EmailMessage
from django.core.management.base import BaseCommand
from django.template.loader import render_to_string

from www.models import Uczestnik, UserNotification


class Command(BaseCommand):
    """
    Polecenie wypisuje z powiadomień użytkowników, którzy:

      - zostali zapisani w dowolny sposób
      - zostali zapisani na dane szkolenie/szkolenia w dowolny sposób
      - zostali przeszkoleni
    """

    def handle(self, *args, **options):
        today = datetime.date.today()

        # Pobierz wszystkie zapisy na szkolenia.
        users = UserNotification.objects.get_active()

        # Tablica przechowująca informacje o usuniętych powiadomieniach w
        # celu wysłania raportu do Biura.
        deleted = {}

        for user in users:
            # Iterujemy po każdym szkoleniu na które zapisał się użytkownik
            # sprawdzaj<PERSON>c, czy został już z niego przeszkolony.

            for course in user.preferences.all():
                # Sprawdzamy, czy istnieje taki użytkownik jak `user`,
                # który był uczestnikiem terminu szkolenia takim, że:
                #   - jest ukończone (`odbylo_sie=True`)
                #   - jest przypisane do szkolenia `course.training_id`
                #   - jest późnijesze niż data dodania notyfikacji
                #   - zostało zakończone
                #
                # Uwaga: lokalizacja nas nie interesuje; jeśli uzytkownik
                #        zapisał się na powiadomienia w Krakowie, ale został
                #        przeszkolony w Warszawie to jest OK.
                already_trained = Uczestnik.objects.filter(
                    status=3,  # "Przeszkolony"
                    email__iexact=user.email,
                    termin__odbylo_sie=True,
                    termin__szkolenie_id=course.training_id,
                    termin__termin__gte=course.created_at.date(),
                    termin__termin__lt=today,
                ).distinct()

                if already_trained.count():
                    if user.email not in deleted:
                        deleted[user.email] = []

                    deleted[user.email].append([course.training.nazwa, already_trained])

                    # Użytkownik przeszkolony, usuwamy powiadomienie.
                    course.delete()

        # Wyślij raport
        if deleted:
            msg_content = render_to_string(
                "www/notifications/report_unsubscribe_trained_users.html",
                {
                    "data": deleted,
                },
            )

            msg = EmailMessage(
                "Raport z automatycznego wypisania uczestników szkoleń/kursów "
                "z powiadomień",
                msg_content,
                settings.MAIL_FROM_ADDRESS,
                [settings.MAIL_ARCHIVE_MONITOR],
            )
            msg.content_subtype = "html"
            msg.send()
