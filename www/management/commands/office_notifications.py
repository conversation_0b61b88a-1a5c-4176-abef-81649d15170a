import datetime
import logging

from django.conf import settings
from django.core.mail import EmailMessage
from django.core.management.base import BaseCommand
from django.template.loader import render_to_string

from www.models import TerminSzkolenia
from www.utils import two_working_days_before_start

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    """
    Alerty mailowe

    [1] Gdy:
        - Jest T-2
        - grupa jest potwierdzona
    """

    def handle(self, *args, **options):
        dates = two_working_days_before_start(datetime.date.today())

        terms = TerminSzkolenia.objects.filter(termin__in=dates, odbylo_sie=True)

        if terms:
            msg_content = render_to_string(
                "www/emails/grupa_rusza_za_2_dni.html",
                {
                    "terms": terms,
                    "domain": settings.DOMENY_DLA_JEZYKOW["pl"],
                    "protocol": settings.FORCE_SSL and "https" or "http",
                },
            )

            msg = EmailMessage(
                "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> się potwierdzone terminy szkoleń",
                msg_content,
                settings.MAIL_FROM_ADDRESS,
                [settings.MAIL_ZGLOSZENIE_TO_ADDRESS],
            )
            msg.content_subtype = "html"
            msg.send()
