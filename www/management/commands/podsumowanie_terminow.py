import datetime
import logging

from django.conf import settings
from django.core.mail import EmailMessage
from django.core.management.base import BaseCommand
from django.template.loader import render_to_string
from django.utils.translation import activate

from www.models import TerminSzkoleniaLog

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    """
    Alert mailowy

        Raporcik miałby dwie sekcje. Byłby on mailowy, cod<PERSON><PERSON>ie rano, tak samo
        (i do tego samego grona może być) co nasz tzw. "plan dnia".

        Sekcja 1:

        Terminy wczoraj potwierdzone:
        - K-PYTHON-A (2020-03-21 Warszawa zaoczny) [6/6R] <URL do TerminuSzkolenia>
        ...

        Terminy wczoraj zapromowane:
        - K-PYTHON-A (2020-03-21 Warszawa zaoczny) [6/6R] <URL do TerminuSzkolenia>

        <PERSON><PERSON><PERSON> coś byłoby przypadkiem jednego dnia i zapromowane, i potwierdzone -
        to wypisuj taki termin tylko w sekcji "potwierdzone".

        Jeśli dana sekcja nie ma nic, to nie wypisywać jej można wcale.

        Jakby nic nie było kiedyś w ogóle, ani potwierdzone ani zapromowane -
        to proponuję wtedy ogóle nie wysyłać tego maila.

    https://app.asana.com/0/4723313594922/1164115417340673
    """

    def handle(self, *args, **options):
        activate("pl")

        yesterday = datetime.date.today() - datetime.timedelta(days=1)

        odbylo_sie_terms = TerminSzkoleniaLog.objects.filter(
            created_at__day=yesterday.day,
            created_at__month=yesterday.month,
            created_at__year=yesterday.year,
            log_type="odbylo_sie",
        ).select_related("term__szkolenie", "user")

        czy_reklamowac_terms = TerminSzkoleniaLog.objects.filter(
            created_at__day=yesterday.day,
            created_at__month=yesterday.month,
            created_at__year=yesterday.year,
            log_type="czy_reklamowac",
        ).select_related("term__szkolenie", "user")

        # Jak już coś jest w uruchomionych, to nie musi byc w reklamowanych
        if odbylo_sie_terms:
            czy_reklamowac_terms = czy_reklamowac_terms.exclude(
                term_id__in=[e.term_id for e in odbylo_sie_terms]
            )

        if czy_reklamowac_terms or odbylo_sie_terms:
            msg_content = render_to_string(
                "www/emails/terminy_uruchomione_i_reklamowane.html",
                {
                    "yesterday": yesterday,
                    "czy_reklamowac_terms": czy_reklamowac_terms,
                    "odbylo_sie_terms": odbylo_sie_terms,
                    "domain": settings.DOMENY_DLA_JEZYKOW["pl"],
                    "protocol": settings.FORCE_SSL and "https" or "http",
                },
            )

            msg = EmailMessage(
                "Terminy wczoraj potwierdzone i zapromowane",
                msg_content,
                settings.MAIL_FROM_ADDRESS,
                [settings.MAIL_TERMINY],
            )
            msg.content_subtype = "html"
            msg.send()
