import datetime

from django.conf import settings
from django.core.mail import EmailMessage
from django.core.management.base import BaseCommand
from django.template.loader import render_to_string

from www.models import UserCoursesNotification, UserNotification


class Command(BaseCommand):
    """
    Polecenie wypisuje z powiadomień użytkowników, którzy:

      - zostali zapisani przez automat
      - zostali zapisani na dane szkolenie/szkolenia przez automat
      - w między czasie nic samodzilenie nie zmieniali/dodawali
      - czas od zapisu jest większy niż `EXPIRED_NOTIFICATIONS_MAX_AGE`
    """

    def handle(self, *args, **options):
        today = datetime.date.today()

        # Filtruje zapisy na szkolenia - nie młodsze niż (domyślnie 180 dni)
        expired_time = today - datetime.timedelta(
            days=settings.EXPIRED_NOTIFICATIONS_MAX_AGE
        )

        # Tablica przechowująca informacje o usuniętych powiadomieniach w
        # celu wysłania raportu do Biura.
        deleted = {}

        # Pobierz wszystkie zapisy na szkolenia, które od początku do końca
        # były tworzone przez automat.
        system_users = UserNotification.objects.get_created_by_system()

        for user in system_users:
            # Wypisujemy użytkownika tylko ze szkoleń, które spełniają
            # kryteria usunięcia (zostały utworzone wcześniej niż
            # `expired_time`).

            courses = UserCoursesNotification.objects.filter(
                user=user,
                created_at__lte=expired_time,
                source="system",
            )

            for course in courses:
                if user.email not in deleted:
                    deleted[user.email] = []

                deleted[user.email].append(course.training.nazwa)

                course.delete()

        # Wyślij raport
        if deleted:
            msg_content = render_to_string(
                "www/notifications/report_unsubscribe_system_users.html",
                {
                    "data": deleted,
                    "EXPIRED_NOTIFICATIONS_MAX_AGE": settings.EXPIRED_NOTIFICATIONS_MAX_AGE,
                },
            )

            msg = EmailMessage(
                "Raport z automatycznego wypisania uczestników szkoleń/kursów "
                "z powiadomień dodanych przez automat",
                msg_content,
                settings.MAIL_FROM_ADDRESS,
                [settings.MAIL_ARCHIVE_MONITOR],
            )
            msg.content_subtype = "html"
            msg.send()
