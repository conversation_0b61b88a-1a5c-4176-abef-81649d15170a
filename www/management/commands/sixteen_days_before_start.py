import datetime
import logging

from django.conf import settings
from django.core.mail import EmailMessage
from django.core.management.base import BaseCommand
from django.db.models import Q
from django.template.loader import render_to_string
from django.utils.translation import activate, ugettext as _

from www.models import TerminSzkolenia, TerminSzkoleniaLog

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    """
    Polecenie wysyła powiadomienia do Uczestników 16 dni przed rozpoczęciem
    szkolenia.
    """

    def handle(self, *args, **options):
        today = datetime.date.today()

        starts_at = today + datetime.timedelta(days=16)

        # Znajdź terminy szkoleń, które:
        #   - rozpoczynają się dokładnie za 16 dni
        #   - nie są potwierdzony (`odbylo_sie` != None)
        #   - są otwarte (`zamkniete` = False)

        terms = TerminSzkolenia.objects.filter(
            termin=starts_at, 
            #szkolenie__language="pl", 
            zamkniete=False, 
            odbylo_sie=None
        )

        for term in terms:
            # Sprawdzamy, czy czasami nie wysłaliśmy już powiadomienia do
            # tego terminu.

            log, created = TerminSzkoleniaLog.objects.get_or_create(
                term=term, log_type="16_dni_przed_startem"
            )

            if not created:
                continue

            # Znajdź wszystkich użytkowników terminu `term`, który:
            #   - mają status różny od 4
            #   - mają ustawiony adres email
            participants = term.uczestnik_set.exclude(
                status=4,
            ).exclude(Q(email="") | Q(email__isnull=True))

            # Dane do maila
            training = term.szkolenie
            lang = training.language

            if lang == "en":
                mail_from = settings.MAIL_FROM_ADDRESS_NOTIFICATION_EN
            else:
                mail_from = settings.MAIL_FROM_ADDRESS_NOTIFICATION

            activate(lang)
            is_kurs = term.is_kurs()

            for participant in participants:
                msg_content = render_to_string(
                    "www/notifications/sixteen_days_before_start_"
                    "{0}.html".format(lang),
                    {
                        "user": participant,
                        "term": term,
                        "training": training,
                        "szkolenia_kursu": {"pl": "kursu", "en": "course"}[lang] if is_kurs else {"pl": "szkolenia", "en": "course"}[lang],
                        "szkolenie_kurs": {"pl": "kurs", "en": "course"}[lang] if is_kurs else {"pl": "szkolenie", "en": "course"}[lang],
                        "szkoleniu_kursie": {"pl": "kursie", "en": "course"}[lang] if is_kurs else {"pl": "szkoleniu", "en": "course"}[lang],
                        "is_kurs": is_kurs,
                        "domain": settings.DOMENY_DLA_JEZYKOW[lang],
                        "protocol": settings.FORCE_SSL and "https" or "http",
                    },
                )

                subject = "{} {} ALX {} {}".format(
                    _("Przypomnienie o"),
                    _("kursie") if is_kurs else _("szkoleniu"),
                    _("dla"),
                    participant.get_nazwa()
                    .rstrip()
                    .replace("\r", "")
                    .replace("\n", ""),
                )

                try:
                    msg = EmailMessage(
                        subject, msg_content, mail_from, to=[participant.email]
                    )
                    msg.content_subtype = "html"
                    msg.send()
                except Exception:
                    logger.exception(
                        "Błąd przy wysyłce maila '16 dni przed startem' dla "
                        "użytkownika ID: {0} ({1})".format(
                            participant.pk, participant.email
                        )
                    )
