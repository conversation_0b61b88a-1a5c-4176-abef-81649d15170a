import datetime
import logging

from django.conf import settings
from django.core.mail import EmailMessage
from django.core.management.base import BaseCommand
from django.db import transaction
from django.template.loader import render_to_string

from www.models import TerminSz<PERSON>lenia, TerminSzkoleniaLog

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    """
    Dwa nowe alerty mailowe

    [1] Gdy:
        - grupa w przyszłości osiąga N-1 zapisanych rentownych osób
          (gdzie N to "min_grupa"),
        - grupa nie jest jeszcze potwierdzona

    Mail wysyła się w dowolnym terminie do T-7.
    Ten raport ma sens dla N >= 3.

    [2.] Gdy:
        - Jest T-14, i grupa czegokolwiek osiąga N+1 RENTOWNYCH zapisów

    https://zliczacz.alx.pl/sprawy/37328/
    """

    def handle(self, *args, **options):
        today = datetime.date.today()

        def send_mail_and_add_to_log(log_type, term, template, subject):
            """
            Funkcja tworzy obiekt logu i wysyła maila w transakcji,
            w przypadku błędu, nie zapisujemy nic - niekompletne dane
            są nieprzydatne.
            """

            try:
                with transaction.atomic():
                    TerminSzkoleniaLog.objects.create(term=term, log_type=log_type)

                    msg_content = render_to_string(
                        "www/emails/{0}".format(template),
                        {
                            "term": term,
                            "domain": settings.DOMENY_DLA_JEZYKOW["pl"],
                            "protocol": settings.FORCE_SSL and "https" or "http",
                        },
                    )

                    msg = EmailMessage(
                        subject,
                        msg_content,
                        settings.MAIL_FROM_ADDRESS,
                        [settings.MAIL_TERMINY],
                    )
                    msg.content_subtype = "html"
                    msg.send()
            except:
                logger.exception(
                    "Blad podczas tworzenia logow 'termin_rusza' i/lub "
                    "wysylania maila z powiadomieniem"
                )
                return False
            return True

        ###################
        # Mail nr 1.
        ###################

        # Znajdz wszystkie szkolenia w przyszłości, które:
        #   - nie są jeszcze potwierdzone
        #   - czas startu jest większy/równy T-7
        #   - min. grupa jest większa równa 3

        # Filtruje terminy szkoleń
        max_training_age = today + datetime.timedelta(days=7)

        terms = TerminSzkolenia.objects.get_for_grupa_rusza(
            max_training_age, min_grupa=3
        )

        data = []

        for term in terms:
            # Na początku sprawdzamy, czy powiadomienie dla tego szkolenia
            # czasami nie zostało wysłane.
            try:
                TerminSzkoleniaLog.objects.get(
                    term=term, log_type="grupa_rusza_prawie_zebrana"
                )
            except TerminSzkoleniaLog.DoesNotExist:
                # Nie było powiadomienia idziemy dalej
                pass
            else:
                continue

            # Sprawdzanie wielkości grupy robimy tylko na rentownych
            # użytkownikach.
            participants = term.ilosc_uczestnikow(nierentowny=False, hybrydowe=True)

            if participants == term.szkolenie.min_grupa - 1:
                # Możemy wysłać powiadomienie do Biura
                data.append(term)

        for term in data:
            send_mail_and_add_to_log(
                "grupa_rusza_prawie_zebrana",
                term,
                "grupa_rusza_prawie_zebrana.html",
                "Grupa prawie zebrana ({0} - {1})".format(
                    term.szkolenie.kod, term.termin
                ),
            )

        ###################
        # Mail nr 2.
        ###################

        # Znajdz wszystkie szkolenia w przyszłości, które:
        #   - nie są jeszcze potwierdzone
        #   - czas startu jest większy/równy T-7 i mniejszy/równy jak T-14
        # Uwaga: to polecenie działa od PN do PT. Dlatego jeśli jest piątek,

        if datetime.date.today().isoweekday() == 5:
            min_days = 16
        else:
            min_days = 14

        # Filtruje terminy szkoleń
        max_training_age = today + datetime.timedelta(days=7)
        min_training_age = today + datetime.timedelta(days=min_days)

        terms = TerminSzkolenia.objects.get_for_grupa_rusza(
            max_training_age, min_training_age
        )

        data = []

        for term in terms:
            # Na początku sprawdzamy, czy powiadomienie dla tego szkolenia
            # czasami nie zostało wysłane.
            try:
                TerminSzkoleniaLog.objects.get(
                    term=term, log_type="grupa_rusza_szybkie_potwierdzenie"
                )
            except TerminSzkoleniaLog.DoesNotExist:
                # Nie było powiadomienia idziemy dalej
                pass
            else:
                continue

            # Sprawdzanie wielkości grupy robimy tylko na rentownych
            # użytkownikach.
            participants = term.ilosc_uczestnikow(nierentowny=False, hybrydowe=True)

            if participants >= term.szkolenie.min_grupa + 1:
                # Możemy wysłać powiadomienie do Biura
                data.append(term)

        for term in data:
            send_mail_and_add_to_log(
                "grupa_rusza_szybkie_potwierdzenie",
                term,
                "grupa_rusza_szybkie_potwierdzenie.html",
                "Grupa zebrana przed terminem - szybkie potwierdzenie? "
                "({0} - {1})".format(term.szkolenie.kod, term.termin),
            )
