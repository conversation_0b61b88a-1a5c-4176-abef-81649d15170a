import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("www", "0064_discountcode_is_active"),
    ]

    operations = [
        migrations.CreateModel(
            name="ContinuationLog",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                (
                    "email",
                    models.EmailField(
                        max_length=75, verbose_name="email", db_index=True
                    ),
                ),
                (
                    "notification_type",
                    models.CharField(
                        max_length=50,
                        verbose_name="typ powiadomienia",
                        choices=[
                            ("first_notification", "pierwsze powiadomienie"),
                            ("second_notification", "drugie powiadomienie"),
                        ],
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="utworzono"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="aktualizowano"),
                ),
                (
                    "term",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        verbose_name="termin szkolenia",
                        to="www.TerminSzkolenia",
                    ),
                ),
                (
                    "training",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        verbose_name="szkolenie (kontynuacja)",
                        to="www.Szkolenie",
                    ),
                ),
            ],
            options={
                "verbose_name": "log kontynuacji",
                "verbose_name_plural": "logi kontynuacji",
            },
            bases=(models.Model,),
        ),
        migrations.AlterUniqueTogether(
            name="continuationlog",
            unique_together=set([("training", "email", "notification_type")]),
        ),
        migrations.RemoveField(
            model_name="terminszkolenia",
            name="maile_o_kontynuacji",
        ),
        migrations.AddField(
            model_name="szkolenie",
            name="maile_o_kontynuacji",
            field=models.BooleanField(
                default=False, verbose_name="wysyłać maile o kontynuacji?"
            ),
            preserve_default=True,
        ),
    ]
