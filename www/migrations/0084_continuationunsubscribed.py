from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("www", "0083_uczestnik_mail_o_potwierdzeniu_terminu"),
    ]

    operations = [
        migrations.CreateModel(
            name="ContinuationUnsubscribed",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                (
                    "email",
                    models.EmailField(unique=True, max_length=75, verbose_name="email"),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="utworzono"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="aktualizowano"),
                ),
            ],
            options={
                "ordering": ("-created_at",),
                "verbose_name": "anulowanie kontynuacji",
                "verbose_name_plural": "anulowanie kontynuacji",
            },
            bases=(models.Model,),
        ),
    ]
