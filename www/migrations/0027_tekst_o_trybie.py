from django.conf import settings
from django.db import migrations, models


def set_tekst_o_trybie(apps, schema_editor):
    mapa_szkolen = {
        "K-ADMIN": {
            1: "(dwa 4-dniowe bloki i jeden 2-dniowy, co 2 tyg.)",
            3: "(sob-niedz., średnio co 2 tygodnie)",
        },
        "K-ADMIN-S": {
            1: "(4 dni zaję<PERSON>, tyd<PERSON><PERSON> przerwy, 4 dni zajęć)",
            3: "(sob-niedz., średnio co 2 tygodnie)",
        },
        "K-ANALIZA": {
            1: "(4 dni zajęć, tyd<PERSON><PERSON> przerwy, 4 dni zajęć)",
            3: "(sob-niedz., średnio co 2 tygodnie)",
        },
        "K-ANDROID-COMPLETE": {
            1: "(4 dni zajęć, tyd<PERSON><PERSON> przerwy, 3 dni zajęć)",
            3: "(sob-niedz., średnio co 2 tygodnie)",
        },
        "K-EXCEL": {
            1: "(3 dni zaj<PERSON>ć, tyd<PERSON><PERSON> przerwy, 3 dni zajęć)",
            3: "(sob-niedz., średnio co 2 tygodnie)",
        },
        "K-HTML-CSS-GFX": {
            3: "(sob-niedz., średnio co 2 tygodnie)",
        },
        "K-JAVA": {
            1: "(4 dni zajęć, tydzień przerwy, 4 dni zajęć)",
            3: "(sob-niedz., średnio co 2 tygodnie)",
        },
        "K-PHP": {
            1: "(5 dni zajęć, tydzień przerwy, 4 dni zajęć)",
            3: "(sob-niedz., średnio co 2 tygodnie)",
        },
        "K-PHP-S": {
            1: "(3 dni zajęć, tydzień przerwy, 3 dni zajęć)",
            3: "(sob-niedz., średnio co 2 tygodnie)",
        },
        "K-PROG-INTRO-J": {
            1: "(4 dni zajęć, tydzień przerwy, 3 dni zajęć)",
            3: "(sob-niedz., średnio co 2 tygodnie)",
        },
        "K-WWW": {
            1: "(4 dni zajęć, tydzień przerwy, 2 dni zajęć)",
            3: "(sob-niedz., średnio co 2 tygodnie)",
        },
        "LC-ADMIN": {
            1: "(4-days classes twice and 2-days once, biweekly)",
        },
        "LC-ANALYSIS": {
            1: "(4-days classes, week break, 4-days classes)",
        },
        "LC-ANDROID-COMPLETE": {
            1: "(3-days classes, week break, 4-days classes)",
        },
        "LC-JAVA": {
            1: "(4-days classes, week break, 4-days classes)",
        },
        "LC-WWW": {
            1: "(4 training days, week break, 2 training days)",
        },
    }

    Szkolenie = apps.get_model("www", "Szkolenie")

    for k, v in list(mapa_szkolen.items()):
        try:
            obj = Szkolenie.objects.get(kod=k)
        except Szkolenie.DoesNotExist:
            pass
        else:
            if not obj.tryb_wieczorowy_opis:
                obj.tryb_wieczorowy_opis = v.get(2, "")
            if not obj.tryb_zaoczny_opis:
                obj.tryb_zaoczny_opis = v.get(3, "")
            if not obj.tryb_dzienny_opis:
                obj.tryb_dzienny_opis = v.get(1, "")
            obj.save()


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("www", "0026_auto_20160510_1903"),
    ]

    operations = [
        migrations.RunPython(set_tekst_o_trybie),
    ]
