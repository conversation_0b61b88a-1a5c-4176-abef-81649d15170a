# Generated by Django 1.11.25 on 2019-11-03 09:28


import uuid

import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("www", "0085_auto_20190921_1618"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="autoryzacja",
            options={
                "verbose_name": "autoryzacja",
                "verbose_name_plural": "autoryzacje",
            },
        ),
        migrations.AlterField(
            model_name="autoryzacja",
            name="nazwa",
            field=models.CharField(max_length=100, verbose_name="nazwa"),
        ),
        migrations.AlterField(
            model_name="autoryzacja",
            name="opis_dlugi",
            field=models.TextField(
                help_text="Można używać znaczników HTML.", verbose_name="opis długi"
            ),
        ),
        migrations.AlterField(
            model_name="autoryzacja",
            name="opis_krotki",
            field=models.TextField(verbose_name="opis krótki"),
        ),
        migrations.AlterField(
            model_name="certificategroup",
            name="email",
            field=models.EmailField(max_length=254, verbose_name="adres email"),
        ),
        migrations.AlterField(
            model_name="certificategroup",
            name="key",
            field=models.UUIDField(default=uuid.uuid4, editable=False),
        ),
        migrations.AlterField(
            model_name="continuationlog",
            name="email",
            field=models.EmailField(
                db_index=True, max_length=254, verbose_name="email"
            ),
        ),
        migrations.AlterField(
            model_name="continuationunsubscribed",
            name="email",
            field=models.EmailField(max_length=254, unique=True, verbose_name="email"),
        ),
        migrations.AlterField(
            model_name="fakturakorekta",
            name="uczestnik",
            field=models.OneToOneField(
                blank=True,
                default=None,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="www.Uczestnik",
                verbose_name="Uczestnik",
            ),
        ),
        migrations.AlterField(
            model_name="fakturawysylka",
            name="key",
            field=models.UUIDField(default=uuid.uuid4, editable=False),
        ),
        migrations.AlterField(
            model_name="graduate",
            name="discount_code",
            field=models.OneToOneField(
                blank=True,
                default=None,
                help_text="Zostanie automatycznie wygenerowany.",
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="www.DiscountCode",
                verbose_name="kod rabatowy",
            ),
        ),
        migrations.AlterField(
            model_name="graduate",
            name="key",
            field=models.UUIDField(default=uuid.uuid4, editable=False),
        ),
        migrations.AlterField(
            model_name="graduate",
            name="public_key",
            field=models.UUIDField(default=uuid.uuid4, editable=False),
        ),
        migrations.AlterField(
            model_name="graduate",
            name="remote_addr",
            field=models.GenericIPAddressField(
                blank=True, editable=False, null=True, verbose_name="adres IP"
            ),
        ),
        migrations.AlterField(
            model_name="highlight",
            name="obrazek",
            field=models.ImageField(
                blank=True,
                help_text="Gdy tagtech nie ma trenerów - obrazek pójdzie na prawą stronę. Gdy trenerzy są - pójdzie pod tekst topu, niezależnie od tego jaka się wybierze wersja renderowania topu. Sugerowany rozmiar obrazka: ok. 140\xd7106px.",
                upload_to="highlights/",
                verbose_name="obrazek",
            ),
        ),
        migrations.AlterField(
            model_name="leave",
            name="year",
            field=models.IntegerField(verbose_name="za rok"),
        ),
        migrations.AlterField(
            model_name="potencjalnychetny",
            name="email",
            field=models.EmailField(blank=True, max_length=254),
        ),
        migrations.AlterField(
            model_name="szkolenie",
            name="autoryzacja",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="www.Autoryzacja",
                verbose_name="autoryzacja",
            ),
        ),
        migrations.AlterField(
            model_name="szkolenie",
            name="cena_autoryzacji_bazowa",
            field=models.IntegerField(
                blank=True,
                help_text="W przypadku jej braku wyświetlana cena zostanie obliczona na podstawie ceny szkolenia bazowego (dotyczy tłumaczeń).",
                null=True,
                verbose_name="cena autoryzacji",
            ),
        ),
        migrations.AlterField(
            model_name="szkolenie",
            name="cena_bazowa",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                help_text="W przypadku jej braku wyświetlana cena zostanie obliczona na podstawie ceny szkolenia bazowego (dotyczy tłumaczeń).",
                max_digits=30,
                null=True,
                verbose_name="cena",
            ),
        ),
        migrations.AlterField(
            model_name="szkolenie",
            name="kod_autoryzacji",
            field=models.CharField(
                blank=True, max_length=50, verbose_name="kod autoryzacji"
            ),
        ),
        migrations.AlterField(
            model_name="szkolenie",
            name="nazwa_autoryzacji",
            field=models.CharField(
                blank=True, max_length=200, verbose_name="nazwa autoryzacji"
            ),
        ),
        migrations.AlterField(
            model_name="szkolenie",
            name="opis_egzaminu",
            field=models.TextField(
                blank=True,
                help_text="Wyświetlany na stronie szkolenia po opisie autoryzacji. Można używać znaczników HTML.",
                verbose_name="opis egzaminu",
            ),
        ),
        migrations.AlterField(
            model_name="tagtechnologia",
            name="domena",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="www.Domena",
            ),
        ),
        migrations.AlterField(
            model_name="terminszkolenia",
            name="secret_key",
            field=models.UUIDField(default=uuid.uuid4),
        ),
        migrations.AlterField(
            model_name="terminszkolenia",
            name="tryb",
            field=models.IntegerField(
                choices=[(1, "dzienny"), (2, "wieczorowy"), (3, "zaoczny")], default=1
            ),
        ),
        migrations.AlterField(
            model_name="testimonial",
            name="content",
            field=models.TextField(
                help_text="Można używać znaczników HTML.", verbose_name="Treść"
            ),
        ),
        migrations.AlterField(
            model_name="testimonial",
            name="id_opinii_zrodlowej",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="ankiety.Komentarz",
                verbose_name="ID opinii źródłowej",
            ),
        ),
        migrations.AlterField(
            model_name="testimonial",
            name="imie_nazwisko",
            field=models.CharField(
                blank=True, max_length=100, verbose_name="imię i nazwisko"
            ),
        ),
        migrations.AlterField(
            model_name="uczestnik",
            name="bylem_na",
            field=models.TextField(
                blank=True,
                verbose_name="Jeśli tak, proszę podać nazwę szkolenia i orientacyjną datę jego rozpoczęcia",
            ),
        ),
        migrations.AlterField(
            model_name="uczestnik",
            name="bylem_wczesniej",
            field=models.BooleanField(
                default=False,
                verbose_name="byłem wcześniej u was na szkoleniu (dotyczy także innych pracowników firmy)",
            ),
        ),
        migrations.AlterField(
            model_name="uczestnik",
            name="chce_autoryzacje",
            field=models.BooleanField(
                default=False, verbose_name="zamawiam autoryzację"
            ),
        ),
        migrations.AlterField(
            model_name="uczestnik",
            name="chce_egzamin",
            field=models.BooleanField(default=False, verbose_name="zamawiam egzamin"),
        ),
        migrations.AlterField(
            model_name="uczestnik",
            name="chce_fakture",
            field=models.BooleanField(
                default=False, verbose_name="proszę o wystawienie papierowej faktury"
            ),
        ),
        migrations.AlterField(
            model_name="uczestnik",
            name="email",
            field=models.EmailField(
                blank=True,
                help_text="W przypadku grup adres osoby kontaktowej.",
                max_length=254,
                verbose_name="email kursanta",
            ),
        ),
        migrations.AlterField(
            model_name="uczestnik",
            name="email_ksiegowosc",
            field=models.EmailField(
                blank=True, max_length=254, verbose_name="email do spraw księgowych"
            ),
        ),
        migrations.AlterField(
            model_name="uczestnik",
            name="faktura_adres",
            field=models.CharField(blank=True, max_length=100, verbose_name="adres"),
        ),
        migrations.AlterField(
            model_name="uczestnik",
            name="faktura_firma",
            field=models.CharField(blank=True, max_length=100, verbose_name="firma"),
        ),
        migrations.AlterField(
            model_name="uczestnik",
            name="faktura_kraj",
            field=models.CharField(
                choices=[
                    ("PL", "Polska"),
                    ("GB", "Wielka Brytania"),
                    ("DE", "Niemcy"),
                    ("AF", "Afganistan"),
                    ("AL", "Albania"),
                    ("DZ", "Algeria"),
                    ("AD", "Andora"),
                    ("AO", "Angola"),
                    ("AI", "Anguilla"),
                    ("AQ", "Antarktyda"),
                    ("AG", "Antigua i Barbuda"),
                    ("SA", "Arabia Saudyjska"),
                    ("AR", "Argentyna"),
                    ("AM", "Armenia"),
                    ("AW", "Aruba"),
                    ("AU", "Australia"),
                    ("AT", "Austria"),
                    ("AZ", "Azerbejdżan"),
                    ("BS", "Bahamy"),
                    ("BH", "Bahrajn"),
                    ("BD", "Bangladesz"),
                    ("BB", "Barbados"),
                    ("BE", "Belgia"),
                    ("BZ", "Belize"),
                    ("BJ", "Benin"),
                    ("BM", "Bermudy"),
                    ("BT", "Bhutan"),
                    ("BY", "Białoruś"),
                    ("BO", "Boliwia"),
                    ("BQ", "Bonaire, Sint Eustatius and Saba"),
                    ("BA", "Bośnia i Hercegowina"),
                    ("BW", "Botswana"),
                    ("BR", "Brazylia"),
                    ("BN", "Brunei"),
                    ("IO", "Brytyjskie Terytorium Oceanu Indyjskiego"),
                    ("VG", "Brytyjskie Wyspy Dziewicze"),
                    ("VI", "Brytyjskie Wyspy Dziewicze"),
                    ("BG", "Bułgaria"),
                    ("BF", "Burkina Faso"),
                    ("BI", "Burundi"),
                    ("CV", "Cabo Verde"),
                    ("CL", "Chile"),
                    ("CN", "Chiny"),
                    ("HR", "Chorwacja"),
                    ("CW", "Curaçao"),
                    ("CY", "Cyprs"),
                    ("TD", "Czad"),
                    ("ME", "Czarnogóra"),
                    ("CZ", "Czechy"),
                    ("UM", "Dalekie Wyspy Mniejsze Stanów Zjednoczonych"),
                    ("DK", "Dania"),
                    ("DM", "Dominika"),
                    ("DO", "Dominikana"),
                    ("DJ", "Dżibuti"),
                    ("EG", "Egipt"),
                    ("EC", "Ekwador"),
                    ("ER", "Erytrea"),
                    ("EE", "Estonia"),
                    ("ET", "Etiopia"),
                    ("FK", "Falklandy"),
                    ("FJ", "Fidżi"),
                    ("PH", "Filipiny"),
                    ("FI", "Finlandia"),
                    ("FR", "Francja"),
                    ("TF", "Francuskie Terytoria Południowe i Antarktyczne"),
                    ("GA", "Gabon"),
                    ("GM", "Gambia"),
                    ("GS", "Georgia Południowa i Sandwich Południowy"),
                    ("GH", "Ghana"),
                    ("GI", "Gibraltar"),
                    ("GR", "Grecja"),
                    ("GD", "Grenada"),
                    ("GL", "Grenlandia"),
                    ("GE", "Gruzja"),
                    ("GU", "Guam"),
                    ("GG", "Guernsey"),
                    ("GY", "Gujana"),
                    ("GF", "Gujana Francuska"),
                    ("GP", "Gwadelupa"),
                    ("GT", "Gwatemala"),
                    ("GN", "Gwinea"),
                    ("GW", "Gwinea Bissau"),
                    ("GQ", "Gwinea Równikowa"),
                    ("HT", "Haiti"),
                    ("ES", "Hiszpania"),
                    ("NL", "Holandia"),
                    ("HN", "Honduras"),
                    ("HK", "Hong Kong"),
                    ("IN", "Indie"),
                    ("ID", "Indonezja"),
                    ("IQ", "Irak"),
                    ("IR", "Iran"),
                    ("IE", "Irlandia"),
                    ("IS", "Islandia"),
                    ("IL", "Izrael"),
                    ("JM", "Jamajka"),
                    ("JP", "Japonia"),
                    ("YE", "Jemen"),
                    ("JE", "Jersey"),
                    ("JO", "Jordania"),
                    ("KY", "Kajmany"),
                    ("KH", "Kambodża"),
                    ("CM", "Kamerun"),
                    ("CA", "Kanada"),
                    ("QA", "Katar"),
                    ("KZ", "Kazachstan"),
                    ("KE", "Kenia"),
                    ("KG", "Kirgistan"),
                    ("KI", "Kiribati"),
                    ("CO", "Kolumbia"),
                    ("KM", "Komory"),
                    ("CG", "Kongo"),
                    ("KP", "Korea Północna"),
                    ("KR", "Korea Południowa"),
                    ("CR", "Kostaryka"),
                    ("CU", "Kuba"),
                    ("KW", "Kuwejt"),
                    ("LA", "Laos"),
                    ("LS", "Lesotho"),
                    ("LB", "Liban"),
                    ("LR", "Liberia"),
                    ("LY", "Libia"),
                    ("LI", "Liechtenstein"),
                    ("LT", "Litwa"),
                    ("LU", "Luksembourg"),
                    ("MK", "Macedonia"),
                    ("MG", "Madagaskar"),
                    ("YT", "Majotta"),
                    ("MO", "Makau"),
                    ("MW", "Malawi"),
                    ("MV", "Malediwy"),
                    ("MY", "Malezja"),
                    ("ML", "Mali"),
                    ("MT", "Malta"),
                    ("MP", "Mariany Północne"),
                    ("MA", "Maroko"),
                    ("MQ", "Martynika"),
                    ("MR", "Mauritania"),
                    ("MU", "Mauritius"),
                    ("MX", "Meksyk"),
                    ("FM", "Mikronezja"),
                    ("MM", "Mjanma"),
                    ("MD", "Mołdawia"),
                    ("MC", "Monako"),
                    ("MN", "Mongolia"),
                    ("MS", "Montserrat"),
                    ("MZ", "Mozambik"),
                    ("NA", "Nambia"),
                    ("NR", "Nauru"),
                    ("NP", "Nepal"),
                    ("NE", "Niger"),
                    ("NG", "Nigeria"),
                    ("NI", "Nikaragua"),
                    ("NU", "Niue"),
                    ("NF", "Norfolk"),
                    ("NO", "Norwegia"),
                    ("NC", "Nowa Kaledonia"),
                    ("NZ", "Nowa Zelandia"),
                    ("OM", "Oman"),
                    ("PK", "Pakistan"),
                    ("PW", "Palau"),
                    ("PS", "Palestine, State of"),
                    ("PA", "Palestyna"),
                    ("PG", "Papua-Nowa Gwinea"),
                    ("PY", "Paragwaj"),
                    ("PE", "Peru"),
                    ("PN", "Pitcairn"),
                    ("PF", "Polinezja Francuska"),
                    ("PR", "Portoryko"),
                    ("PT", "Portugalia"),
                    ("CD", "Republika Kongo"),
                    ("ZA", "Republika Południowej Afryki"),
                    ("CF", "Republika Środkowoafrykańska"),
                    ("RE", "Reunion"),
                    ("RU", "Rosja"),
                    ("RO", "Rumunia"),
                    ("RW", "Rwanda"),
                    ("EH", "Sahara Zachodnia"),
                    ("BL", "Saint Barthélemy"),
                    ("KN", "Saint Kitts i Nevis"),
                    ("LC", "Saint Lucia"),
                    ("PM", "Saint Pierre i Miquelon"),
                    ("VC", "Saint Vincent i Grenadyny"),
                    ("MF", "Saint-Martin"),
                    ("SV", "Salwador"),
                    ("WS", "Samoa"),
                    ("AS", "Samoa Amerykańskie"),
                    ("SM", "San Marino"),
                    ("SN", "Senegal"),
                    ("RS", "Serbia"),
                    ("SC", "Seszele"),
                    ("SL", "Sierra Leone"),
                    ("SG", "Singapur"),
                    ("SX", "Sint Maarten"),
                    ("SO", "Somalia"),
                    ("SK", "Słowacja"),
                    ("SI", "Słowenia"),
                    ("LK", "Sri Lanka"),
                    ("US", "Stany Zjednoczone"),
                    ("SZ", "Suazi"),
                    ("SD", "Sudan"),
                    ("SS", "Sudan Południowy"),
                    ("SR", "Suriname"),
                    ("SJ", "Svalbard i Jan Mayen"),
                    ("SY", "Syria"),
                    ("CH", "Szwajcaria"),
                    ("SE", "Szwecja"),
                    ("TJ", "Tadżykistan"),
                    ("TH", "Tajlandia"),
                    ("TW", "Tajwan"),
                    ("TZ", "Tanzania"),
                    ("TL", "Timor Wschodni"),
                    ("TG", "Togo"),
                    ("TK", "Tokelau"),
                    ("TO", "Tonga"),
                    ("TT", "Trynidad i Tobago"),
                    ("TN", "Tunezja"),
                    ("TR", "Turkcja"),
                    ("TM", "Turkmenistan"),
                    ("TC", "Turks i Caicos"),
                    ("TV", "Tuvalu"),
                    ("UG", "Uganda"),
                    ("UA", "Ukraina"),
                    ("UY", "Urugwaj"),
                    ("UZ", "Uzbekistan"),
                    ("VU", "Vanuatu"),
                    ("WF", "Wallis i Futuna"),
                    ("VA", "Watykan"),
                    ("HU", "Węgry"),
                    ("VE", "Wenezuela"),
                    ("VN", "Wietnam"),
                    ("IT", "Włochy"),
                    ("CI", "Wybrzeże Kości Słoniowej"),
                    ("BV", "Wyspa Bouveta"),
                    ("CX", "Wyspa Bożego Narodzenia"),
                    ("IM", "Wyspa Man"),
                    (
                        "SH",
                        "Wyspa Świętej Heleny, Wyspa Wniebowstąpienia i Tristan da Cunha",
                    ),
                    ("AX", "Wyspy Alandzkie"),
                    ("CK", "Wyspy Cooka"),
                    ("HM", "Wyspy Heard i McDonalda"),
                    ("CC", "Wyspy Kokosowe"),
                    ("MH", "Wyspy Marshalla"),
                    ("FO", "Wyspy Owcze"),
                    ("SB", "Wyspy Salomona"),
                    ("ST", "Wyspy Świętego Tomasza i Książęca"),
                    ("ZM", "Zambia"),
                    ("ZW", "Zimbabwe"),
                    ("AE", "Zjednoczone Emiraty Arabskie"),
                    ("LV", "Łotwa"),
                ],
                default="PL",
                max_length=2,
                verbose_name="kraj",
            ),
        ),
        migrations.AlterField(
            model_name="uczestnik",
            name="faktura_miejscowosc_kod",
            field=models.CharField(
                blank=True, max_length=100, verbose_name="miejscowość, kod pocztowy"
            ),
        ),
        migrations.AlterField(
            model_name="uczestnik",
            name="faktura_nip",
            field=models.CharField(blank=True, max_length=40, verbose_name="NIP"),
        ),
        migrations.AlterField(
            model_name="uczestnik",
            name="faktura_vat_id",
            field=models.CharField(
                blank=True,
                help_text="lub ID firmy",
                max_length=40,
                verbose_name="VAT ID",
            ),
        ),
        migrations.AlterField(
            model_name="uczestnik",
            name="ile_obiadow_wegetarianskich",
            field=models.IntegerField(
                blank=True, null=True, verbose_name="dla ilu osób obiady wegetariańskie"
            ),
        ),
        migrations.AlterField(
            model_name="uczestnik",
            name="imie_nazwisko",
            field=models.TextField(
                help_text="lub lista osób, jeżeli zgłoszenie wieloosobowe",
                verbose_name="imię i nazwisko",
            ),
        ),
        migrations.AlterField(
            model_name="uczestnik",
            name="imie_nazwisko_zostalo_sprawdzone",
            field=models.BooleanField(
                default=False,
                help_text="W jednej linii powinno być tylko jedno imię i nazwisko, nic poza tym.",
                verbose_name="Potwierdzam, że dane są poprawne i nadają się umieszczenia na fakturze",
            ),
        ),
        migrations.AlterField(
            model_name="uczestnik",
            name="odpowiedz_na_dodatkowe_pytanie",
            field=models.TextField(
                blank=True, verbose_name="Odpowiedź na dodatkowe pytanie"
            ),
        ),
        migrations.AlterField(
            model_name="uczestnik",
            name="osoba_do_kontaktu",
            field=models.CharField(
                blank=True,
                help_text="Jeśli inna niż uczestnik, np. pracownik organizujący szkolenie lub rodzic w przypadku niepełnoletnich uczestników.",
                max_length=100,
                verbose_name="osoba do kontaktu i rozliczeń",
            ),
        ),
        migrations.AlterField(
            model_name="uczestnik",
            name="prywatny",
            field=models.BooleanField(
                default=False, verbose_name="jestem osobą prywatną"
            ),
        ),
        migrations.AlterField(
            model_name="uczestnik",
            name="raty_panstwo",
            field=models.CharField(
                blank=True,
                choices=[("polska", "Polska"), ("inny", "inny")],
                max_length=200,
                verbose_name="kraj",
            ),
        ),
        migrations.AlterField(
            model_name="uczestnik",
            name="wiek_uczestnika",
            field=models.PositiveSmallIntegerField(
                blank=True,
                default=None,
                null=True,
                validators=[
                    django.core.validators.MaxValueValidator(99),
                    django.core.validators.MinValueValidator(13),
                ],
                verbose_name="wiek uczestnika",
            ),
        ),
        migrations.AlterField(
            model_name="uczestnik",
            name="za_kurs_zaplace",
            field=models.IntegerField(
                choices=[
                    (1, "od razu pełną kwotę"),
                    (
                        2,
                        "wpłacając teraz zaliczkę 500 PLN i pozostałą kwotę do siedmiu dni przed rozpoczęciem kursu",
                    ),
                    (
                        3,
                        "(tylko klient indywidualny) wpłacając teraz zaliczkę 500 PLN i pozostałą kwotę w dwóch równych ratach, zgodnie z <a href='/pl/raty/'>zasadami płatności ratalnej</a>",
                    ),
                    (
                        10,
                        "(tylko klient indywidualny) wpłacając kwotę w pięciu ratach, zgodnie z <a href='/pl/raty/'>zasadami płatności ratalnej</a>",
                    ),
                    (
                        4,
                        "inny tryb płatności lub umowa, ustalone indywidualnie (proszę wpisać dokonane ustalenia w polu uwagi dodatkowe)",
                    ),
                    (
                        5,
                        "standard - płatność w oparciu o fakturę pro forma na 7 dni przed rozpoczęciem szkolenia",
                    ),
                    (6, "płatność po szkoleniu, 14 dni po zakończeniu, dopłata +5%"),
                    (
                        7,
                        "inny tryb płatności lub umowa, ustalone indywidualnie (proszę wpisać dokonane ustalenia w polu uwagi dodatkowe)",
                    ),
                ],
                default=1,
            ),
        ),
        migrations.AlterField(
            model_name="uczestniknotification",
            name="email",
            field=models.EmailField(max_length=254, verbose_name="email uczestnika"),
        ),
        migrations.AlterField(
            model_name="usernotification",
            name="key",
            field=models.UUIDField(
                default=uuid.uuid4, editable=False, verbose_name="klucz dostępu"
            ),
        ),
        migrations.AlterField(
            model_name="usernotification",
            name="remote_addr",
            field=models.GenericIPAddressField(
                blank=True, null=True, verbose_name="adres IP"
            ),
        ),
        migrations.AlterField(
            model_name="zgloszenie",
            name="adres",
            field=models.CharField(
                max_length=100, verbose_name="adres korespondencyjny"
            ),
        ),
        migrations.AlterField(
            model_name="zgloszenie",
            name="bylem_na",
            field=models.TextField(
                blank=True,
                verbose_name="Jeśli tak, proszę podać nazwę szkolenia i orientacyjną datę jego rozpoczęcia",
            ),
        ),
        migrations.AlterField(
            model_name="zgloszenie",
            name="bylem_wczesniej",
            field=models.BooleanField(
                default=False,
                verbose_name="byłem wcześniej u was na szkoleniu (dotyczy także innych pracowników firmy)",
            ),
        ),
        migrations.AlterField(
            model_name="zgloszenie",
            name="chce_autoryzacje",
            field=models.BooleanField(
                default=False, verbose_name="zamawiam autoryzację"
            ),
        ),
        migrations.AlterField(
            model_name="zgloszenie",
            name="chce_egzamin",
            field=models.BooleanField(default=False, verbose_name="zamawiam egzamin"),
        ),
        migrations.AlterField(
            model_name="zgloszenie",
            name="chce_fakture",
            field=models.BooleanField(
                default=False, verbose_name="proszę o wystawienie papierowej faktury"
            ),
        ),
        migrations.AlterField(
            model_name="zgloszenie",
            name="chce_obiady",
            field=models.BooleanField(default=False, verbose_name="zamawiam obiady"),
        ),
        migrations.AlterField(
            model_name="zgloszenie",
            name="email",
            field=models.EmailField(
                help_text="W przypadku grup adres osoby kontaktowej.",
                max_length=254,
                verbose_name="email uczestnika",
            ),
        ),
        migrations.AlterField(
            model_name="zgloszenie",
            name="email_ksiegowosc",
            field=models.EmailField(
                blank=True,
                help_text="Jeśli inny niż powyższy",
                max_length=254,
                verbose_name="email osoby do rozliczeń",
            ),
        ),
        migrations.AlterField(
            model_name="zgloszenie",
            name="faktura_adres",
            field=models.CharField(blank=True, max_length=100, verbose_name="adres"),
        ),
        migrations.AlterField(
            model_name="zgloszenie",
            name="faktura_firma",
            field=models.CharField(blank=True, max_length=100, verbose_name="firma"),
        ),
        migrations.AlterField(
            model_name="zgloszenie",
            name="faktura_kraj",
            field=models.CharField(
                choices=[
                    ("PL", "Polska"),
                    ("GB", "Wielka Brytania"),
                    ("DE", "Niemcy"),
                    ("AF", "Afganistan"),
                    ("AL", "Albania"),
                    ("DZ", "Algeria"),
                    ("AD", "Andora"),
                    ("AO", "Angola"),
                    ("AI", "Anguilla"),
                    ("AQ", "Antarktyda"),
                    ("AG", "Antigua i Barbuda"),
                    ("SA", "Arabia Saudyjska"),
                    ("AR", "Argentyna"),
                    ("AM", "Armenia"),
                    ("AW", "Aruba"),
                    ("AU", "Australia"),
                    ("AT", "Austria"),
                    ("AZ", "Azerbejdżan"),
                    ("BS", "Bahamy"),
                    ("BH", "Bahrajn"),
                    ("BD", "Bangladesz"),
                    ("BB", "Barbados"),
                    ("BE", "Belgia"),
                    ("BZ", "Belize"),
                    ("BJ", "Benin"),
                    ("BM", "Bermudy"),
                    ("BT", "Bhutan"),
                    ("BY", "Białoruś"),
                    ("BO", "Boliwia"),
                    ("BQ", "Bonaire, Sint Eustatius and Saba"),
                    ("BA", "Bośnia i Hercegowina"),
                    ("BW", "Botswana"),
                    ("BR", "Brazylia"),
                    ("BN", "Brunei"),
                    ("IO", "Brytyjskie Terytorium Oceanu Indyjskiego"),
                    ("VG", "Brytyjskie Wyspy Dziewicze"),
                    ("VI", "Brytyjskie Wyspy Dziewicze"),
                    ("BG", "Bułgaria"),
                    ("BF", "Burkina Faso"),
                    ("BI", "Burundi"),
                    ("CV", "Cabo Verde"),
                    ("CL", "Chile"),
                    ("CN", "Chiny"),
                    ("HR", "Chorwacja"),
                    ("CW", "Curaçao"),
                    ("CY", "Cyprs"),
                    ("TD", "Czad"),
                    ("ME", "Czarnogóra"),
                    ("CZ", "Czechy"),
                    ("UM", "Dalekie Wyspy Mniejsze Stanów Zjednoczonych"),
                    ("DK", "Dania"),
                    ("DM", "Dominika"),
                    ("DO", "Dominikana"),
                    ("DJ", "Dżibuti"),
                    ("EG", "Egipt"),
                    ("EC", "Ekwador"),
                    ("ER", "Erytrea"),
                    ("EE", "Estonia"),
                    ("ET", "Etiopia"),
                    ("FK", "Falklandy"),
                    ("FJ", "Fidżi"),
                    ("PH", "Filipiny"),
                    ("FI", "Finlandia"),
                    ("FR", "Francja"),
                    ("TF", "Francuskie Terytoria Południowe i Antarktyczne"),
                    ("GA", "Gabon"),
                    ("GM", "Gambia"),
                    ("GS", "Georgia Południowa i Sandwich Południowy"),
                    ("GH", "Ghana"),
                    ("GI", "Gibraltar"),
                    ("GR", "Grecja"),
                    ("GD", "Grenada"),
                    ("GL", "Grenlandia"),
                    ("GE", "Gruzja"),
                    ("GU", "Guam"),
                    ("GG", "Guernsey"),
                    ("GY", "Gujana"),
                    ("GF", "Gujana Francuska"),
                    ("GP", "Gwadelupa"),
                    ("GT", "Gwatemala"),
                    ("GN", "Gwinea"),
                    ("GW", "Gwinea Bissau"),
                    ("GQ", "Gwinea Równikowa"),
                    ("HT", "Haiti"),
                    ("ES", "Hiszpania"),
                    ("NL", "Holandia"),
                    ("HN", "Honduras"),
                    ("HK", "Hong Kong"),
                    ("IN", "Indie"),
                    ("ID", "Indonezja"),
                    ("IQ", "Irak"),
                    ("IR", "Iran"),
                    ("IE", "Irlandia"),
                    ("IS", "Islandia"),
                    ("IL", "Izrael"),
                    ("JM", "Jamajka"),
                    ("JP", "Japonia"),
                    ("YE", "Jemen"),
                    ("JE", "Jersey"),
                    ("JO", "Jordania"),
                    ("KY", "Kajmany"),
                    ("KH", "Kambodża"),
                    ("CM", "Kamerun"),
                    ("CA", "Kanada"),
                    ("QA", "Katar"),
                    ("KZ", "Kazachstan"),
                    ("KE", "Kenia"),
                    ("KG", "Kirgistan"),
                    ("KI", "Kiribati"),
                    ("CO", "Kolumbia"),
                    ("KM", "Komory"),
                    ("CG", "Kongo"),
                    ("KP", "Korea Północna"),
                    ("KR", "Korea Południowa"),
                    ("CR", "Kostaryka"),
                    ("CU", "Kuba"),
                    ("KW", "Kuwejt"),
                    ("LA", "Laos"),
                    ("LS", "Lesotho"),
                    ("LB", "Liban"),
                    ("LR", "Liberia"),
                    ("LY", "Libia"),
                    ("LI", "Liechtenstein"),
                    ("LT", "Litwa"),
                    ("LU", "Luksembourg"),
                    ("MK", "Macedonia"),
                    ("MG", "Madagaskar"),
                    ("YT", "Majotta"),
                    ("MO", "Makau"),
                    ("MW", "Malawi"),
                    ("MV", "Malediwy"),
                    ("MY", "Malezja"),
                    ("ML", "Mali"),
                    ("MT", "Malta"),
                    ("MP", "Mariany Północne"),
                    ("MA", "Maroko"),
                    ("MQ", "Martynika"),
                    ("MR", "Mauritania"),
                    ("MU", "Mauritius"),
                    ("MX", "Meksyk"),
                    ("FM", "Mikronezja"),
                    ("MM", "Mjanma"),
                    ("MD", "Mołdawia"),
                    ("MC", "Monako"),
                    ("MN", "Mongolia"),
                    ("MS", "Montserrat"),
                    ("MZ", "Mozambik"),
                    ("NA", "Nambia"),
                    ("NR", "Nauru"),
                    ("NP", "Nepal"),
                    ("NE", "Niger"),
                    ("NG", "Nigeria"),
                    ("NI", "Nikaragua"),
                    ("NU", "Niue"),
                    ("NF", "Norfolk"),
                    ("NO", "Norwegia"),
                    ("NC", "Nowa Kaledonia"),
                    ("NZ", "Nowa Zelandia"),
                    ("OM", "Oman"),
                    ("PK", "Pakistan"),
                    ("PW", "Palau"),
                    ("PS", "Palestine, State of"),
                    ("PA", "Palestyna"),
                    ("PG", "Papua-Nowa Gwinea"),
                    ("PY", "Paragwaj"),
                    ("PE", "Peru"),
                    ("PN", "Pitcairn"),
                    ("PF", "Polinezja Francuska"),
                    ("PR", "Portoryko"),
                    ("PT", "Portugalia"),
                    ("CD", "Republika Kongo"),
                    ("ZA", "Republika Południowej Afryki"),
                    ("CF", "Republika Środkowoafrykańska"),
                    ("RE", "Reunion"),
                    ("RU", "Rosja"),
                    ("RO", "Rumunia"),
                    ("RW", "Rwanda"),
                    ("EH", "Sahara Zachodnia"),
                    ("BL", "Saint Barthélemy"),
                    ("KN", "Saint Kitts i Nevis"),
                    ("LC", "Saint Lucia"),
                    ("PM", "Saint Pierre i Miquelon"),
                    ("VC", "Saint Vincent i Grenadyny"),
                    ("MF", "Saint-Martin"),
                    ("SV", "Salwador"),
                    ("WS", "Samoa"),
                    ("AS", "Samoa Amerykańskie"),
                    ("SM", "San Marino"),
                    ("SN", "Senegal"),
                    ("RS", "Serbia"),
                    ("SC", "Seszele"),
                    ("SL", "Sierra Leone"),
                    ("SG", "Singapur"),
                    ("SX", "Sint Maarten"),
                    ("SO", "Somalia"),
                    ("SK", "Słowacja"),
                    ("SI", "Słowenia"),
                    ("LK", "Sri Lanka"),
                    ("US", "Stany Zjednoczone"),
                    ("SZ", "Suazi"),
                    ("SD", "Sudan"),
                    ("SS", "Sudan Południowy"),
                    ("SR", "Suriname"),
                    ("SJ", "Svalbard i Jan Mayen"),
                    ("SY", "Syria"),
                    ("CH", "Szwajcaria"),
                    ("SE", "Szwecja"),
                    ("TJ", "Tadżykistan"),
                    ("TH", "Tajlandia"),
                    ("TW", "Tajwan"),
                    ("TZ", "Tanzania"),
                    ("TL", "Timor Wschodni"),
                    ("TG", "Togo"),
                    ("TK", "Tokelau"),
                    ("TO", "Tonga"),
                    ("TT", "Trynidad i Tobago"),
                    ("TN", "Tunezja"),
                    ("TR", "Turkcja"),
                    ("TM", "Turkmenistan"),
                    ("TC", "Turks i Caicos"),
                    ("TV", "Tuvalu"),
                    ("UG", "Uganda"),
                    ("UA", "Ukraina"),
                    ("UY", "Urugwaj"),
                    ("UZ", "Uzbekistan"),
                    ("VU", "Vanuatu"),
                    ("WF", "Wallis i Futuna"),
                    ("VA", "Watykan"),
                    ("HU", "Węgry"),
                    ("VE", "Wenezuela"),
                    ("VN", "Wietnam"),
                    ("IT", "Włochy"),
                    ("CI", "Wybrzeże Kości Słoniowej"),
                    ("BV", "Wyspa Bouveta"),
                    ("CX", "Wyspa Bożego Narodzenia"),
                    ("IM", "Wyspa Man"),
                    (
                        "SH",
                        "Wyspa Świętej Heleny, Wyspa Wniebowstąpienia i Tristan da Cunha",
                    ),
                    ("AX", "Wyspy Alandzkie"),
                    ("CK", "Wyspy Cooka"),
                    ("HM", "Wyspy Heard i McDonalda"),
                    ("CC", "Wyspy Kokosowe"),
                    ("MH", "Wyspy Marshalla"),
                    ("FO", "Wyspy Owcze"),
                    ("SB", "Wyspy Salomona"),
                    ("ST", "Wyspy Świętego Tomasza i Książęca"),
                    ("ZM", "Zambia"),
                    ("ZW", "Zimbabwe"),
                    ("AE", "Zjednoczone Emiraty Arabskie"),
                    ("LV", "Łotwa"),
                ],
                default="PL",
                max_length=2,
                verbose_name="kraj",
            ),
        ),
        migrations.AlterField(
            model_name="zgloszenie",
            name="faktura_miejscowosc_kod",
            field=models.CharField(
                blank=True, max_length=100, verbose_name="miejscowość, kod pocztowy"
            ),
        ),
        migrations.AlterField(
            model_name="zgloszenie",
            name="faktura_nip",
            field=models.CharField(blank=True, max_length=40, verbose_name="NIP"),
        ),
        migrations.AlterField(
            model_name="zgloszenie",
            name="faktura_vat_id",
            field=models.CharField(
                blank=True,
                help_text="lub ID firmy",
                max_length=40,
                verbose_name="VAT ID",
            ),
        ),
        migrations.AlterField(
            model_name="zgloszenie",
            name="ile_obiadow_wegetarianskich",
            field=models.IntegerField(
                blank=True, null=True, verbose_name="dla ilu osób obiady wegetariańskie"
            ),
        ),
        migrations.AlterField(
            model_name="zgloszenie",
            name="imie_nazwisko",
            field=models.TextField(
                help_text="lub lista osób, jeżeli zgłoszenie wieloosobowe",
                verbose_name="imię i nazwisko",
            ),
        ),
        migrations.AlterField(
            model_name="zgloszenie",
            name="ip",
            field=models.GenericIPAddressField(blank=True, editable=False, null=True),
        ),
        migrations.AlterField(
            model_name="zgloszenie",
            name="miejscowosc_kod",
            field=models.CharField(
                max_length=100, verbose_name="miejscowość, kod pocztowy"
            ),
        ),
        migrations.AlterField(
            model_name="zgloszenie",
            name="odpowiedz_na_dodatkowe_pytanie",
            field=models.TextField(
                blank=True, verbose_name="Odpowiedź na dodatkowe pytanie"
            ),
        ),
        migrations.AlterField(
            model_name="zgloszenie",
            name="osoba_do_kontaktu",
            field=models.CharField(
                blank=True,
                help_text="Jeśli inna niż uczestnik, np. pracownik organizujący szkolenie lub rodzic w przypadku niepełnoletnich uczestników.",
                max_length=100,
                verbose_name="osoba do kontaktu i rozliczeń",
            ),
        ),
        migrations.AlterField(
            model_name="zgloszenie",
            name="podmiot_publiczny",
            field=models.BooleanField(
                default=False,
                help_text="Zaznaczenie opcji upoważnia do zwolnienia z VAT. W razie pytań lub wątpliwości prosimy o kontakt.",
                verbose_name="podmiot publiczny lub finansowanie ze środków publicznych",
            ),
        ),
        migrations.AlterField(
            model_name="zgloszenie",
            name="prywatny",
            field=models.BooleanField(
                default=False, verbose_name="jestem osobą prywatną"
            ),
        ),
        migrations.AlterField(
            model_name="zgloszenie",
            name="raty_panstwo",
            field=models.CharField(
                blank=True,
                choices=[("polska", "Polska"), ("inny", "inny")],
                max_length=200,
                verbose_name="kraj",
            ),
        ),
        migrations.AlterField(
            model_name="zgloszenie",
            name="telefon",
            field=models.CharField(max_length=50, verbose_name="telefon do kontaktu"),
        ),
        migrations.AlterField(
            model_name="zgloszenie",
            name="termin",
            field=models.ForeignKey(
                help_text="oraz miasto i tryb zajęć",
                on_delete=django.db.models.deletion.PROTECT,
                to="www.TerminSzkolenia",
                verbose_name="termin",
            ),
        ),
        migrations.AlterField(
            model_name="zgloszenie",
            name="uczestnik_wieloosobowy_ilosc_osob",
            field=models.IntegerField(
                blank=True,
                help_text="w przypadku zgłaszania grupy proszę wpisać liczbę osób",
                null=True,
                verbose_name="liczność grupy",
            ),
        ),
        migrations.AlterField(
            model_name="zgloszenie",
            name="uwagi_klienta",
            field=models.TextField(blank=True, verbose_name="Uwagi dodatkowe"),
        ),
        migrations.AlterField(
            model_name="zgloszenie",
            name="wiek_uczestnika",
            field=models.PositiveSmallIntegerField(
                blank=True,
                default=None,
                null=True,
                validators=[
                    django.core.validators.MaxValueValidator(99),
                    django.core.validators.MinValueValidator(13),
                ],
                verbose_name="wiek uczestnika",
            ),
        ),
        migrations.AlterField(
            model_name="zgloszenie",
            name="za_kurs_zaplace",
            field=models.IntegerField(
                choices=[
                    (1, "od razu pełną kwotę"),
                    (
                        2,
                        "wpłacając teraz zaliczkę 500 PLN i pozostałą kwotę do siedmiu dni przed rozpoczęciem kursu",
                    ),
                    (
                        3,
                        "(tylko klient indywidualny) wpłacając teraz zaliczkę 500 PLN i pozostałą kwotę w dwóch równych ratach, zgodnie z <a href='/pl/raty/'>zasadami płatności ratalnej</a>",
                    ),
                    (
                        10,
                        "(tylko klient indywidualny) wpłacając kwotę w pięciu ratach, zgodnie z <a href='/pl/raty/'>zasadami płatności ratalnej</a>",
                    ),
                    (
                        4,
                        "inny tryb płatności lub umowa, ustalone indywidualnie (proszę wpisać dokonane ustalenia w polu uwagi dodatkowe)",
                    ),
                    (
                        5,
                        "standard - płatność w oparciu o fakturę pro forma na 7 dni przed rozpoczęciem szkolenia",
                    ),
                    (6, "płatność po szkoleniu, 14 dni po zakończeniu, dopłata +5%"),
                    (
                        7,
                        "inny tryb płatności lub umowa, ustalone indywidualnie (proszę wpisać dokonane ustalenia w polu uwagi dodatkowe)",
                    ),
                ],
                default=1,
            ),
        ),
    ]
