import random

from django.db import migrations, models


def set_numbers(apps, schema_editor):
    # Ustawiamy cokolwiek. Przed ta migracja nie bylo i tak zadnych
    # absolwentow (tylko testowi).
    objs = apps.get_model("www", "Graduate").objects.all()
    for obj in objs:
        obj.number = random.randint(1000000000, 9999999999)
        obj.save()


class Migration(migrations.Migration):

    dependencies = [
        ("www", "0013_auto_20160301_1746"),
    ]

    operations = [
        migrations.RunPython(set_numbers),
    ]
