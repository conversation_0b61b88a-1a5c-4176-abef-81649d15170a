import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("www", "0055_auto_20180728_1323"),
    ]

    operations = [
        migrations.AddField(
            model_name="szkolenie",
            name="dodatkowe_pytanie_do_uczestnika",
            field=models.TextField(
                verbose_name="dodatkowe pytanie podczas zgłoszenia", blank=True
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="szkolenie",
            name="dodatkowe_pytanie_do_uczestnika_widocznosc",
            field=models.CharField(
                default="hidden",
                max_length=20,
                verbose_name="widoczność dodatkowego pytania",
                choices=[
                    ("hidden", "ukryte"),
                    ("optional", "opcjonalne"),
                    ("required", "wymagane"),
                ],
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="szkolenie",
            name="dodatkowe_pytanie_o_wiek",
            field=models.CharField(
                default="hidden",
                help_text="Je<PERSON><PERSON> widoczne, w formularzu zgłoszeniowym pokaże się pole z miejscem na wiek uczestnika.",
                max_length=20,
                verbose_name="dodatkowe pytanie o wiek",
                choices=[
                    ("hidden", "ukryte"),
                    ("optional", "opcjonalne"),
                    ("required", "wymagane"),
                ],
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="uczestnik",
            name="odpowiedz_na_dodatkowe_pytanie",
            field=models.TextField(
                verbose_name="Odpowiedź na dodatkowe pytanie", blank=True
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="uczestnik",
            name="wiek_uczestnika",
            field=models.PositiveSmallIntegerField(
                default=None,
                null=True,
                verbose_name="wiek uczestnika",
                blank=True,
                validators=[
                    django.core.validators.MaxValueValidator(99),
                    django.core.validators.MinValueValidator(13),
                ],
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="zgloszenie",
            name="odpowiedz_na_dodatkowe_pytanie",
            field=models.TextField(
                verbose_name="Odpowiedź na dodatkowe pytanie", blank=True
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="zgloszenie",
            name="wiek_uczestnika",
            field=models.PositiveSmallIntegerField(
                default=None,
                null=True,
                verbose_name="wiek uczestnika",
                blank=True,
                validators=[
                    django.core.validators.MaxValueValidator(99),
                    django.core.validators.MinValueValidator(13),
                ],
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="zgloszenie",
            name="email",
            field=models.EmailField(
                help_text="In the case of groups, an email address of the contact person.",
                max_length=75,
                verbose_name="participant's email",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="zgloszenie",
            name="email_ksiegowosc",
            field=models.EmailField(
                help_text="Email address to the organizing person or parent in the case of underage participants.",
                max_length=75,
                verbose_name="email for accounting purposes",
                blank=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="myflatpage",
            name="slug",
            field=models.SlugField(blank=True),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="uczestnik",
            name="odpowiedz_na_dodatkowe_pytanie",
            field=models.TextField(
                verbose_name="The answer to an additional question", blank=True
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="uczestnik",
            name="wiek_uczestnika",
            field=models.PositiveSmallIntegerField(
                default=None,
                null=True,
                verbose_name="participant's age",
                blank=True,
                validators=[
                    django.core.validators.MaxValueValidator(99),
                    django.core.validators.MinValueValidator(13),
                ],
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="zgloszenie",
            name="odpowiedz_na_dodatkowe_pytanie",
            field=models.TextField(
                verbose_name="The answer to an additional question", blank=True
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="zgloszenie",
            name="wiek_uczestnika",
            field=models.PositiveSmallIntegerField(
                default=None,
                null=True,
                verbose_name="participant's age",
                blank=True,
                validators=[
                    django.core.validators.MaxValueValidator(99),
                    django.core.validators.MinValueValidator(13),
                ],
            ),
            preserve_default=True,
        ),
    ]
