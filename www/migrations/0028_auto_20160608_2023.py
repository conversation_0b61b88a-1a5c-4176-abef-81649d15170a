from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("www", "0027_tekst_o_trybie"),
    ]

    operations = [
        migrations.AddField(
            model_name="tagtechnologia",
            name="frontpage_ordering",
            field=models.PositiveSmallIntegerField(
                default=1, verbose_name="sortowanie na stronie głównej"
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="tagtechnologia",
            name="widoczny_na_frontpage",
            field=models.BooleanField(
                default=True,
                help_text="Aby tag był widoczny na stronie głównej to pole musi być zaznaczone jak też 'widoczny publicznie'.",
                verbose_name="widoczny na stronie głównej",
            ),
            preserve_default=True,
        ),
    ]
