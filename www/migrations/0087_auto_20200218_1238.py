# Generated by Django 1.11.25 on 2020-02-18 12:38


import re

import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("www", "0086_auto_20191103_0928"),
    ]

    operations = [
        migrations.AlterField(
            model_name="graduate",
            name="discount_code",
            field=models.OneToOneField(
                blank=True,
                default=None,
                help_text="Zostanie automatycznie wygenerowany.",
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="graduate",
                to="www.DiscountCode",
                verbose_name="kod rabatowy",
            ),
        ),
        migrations.AlterField(
            model_name="szkolenie",
            name="podzbior_dni",
            field=models.CharField(
                blank=True,
                help_text="Lista dni oddzielona przecinkami, np. 1,2,3",
                max_length=100,
                validators=[
                    django.core.validators.RegexValidator(
                        re.compile("^\\d+(?:,\\d+)*\\Z", 32),
                        code="invalid",
                        message="Enter only digits separated by commas.",
                    )
                ],
                verbose_name="podzbiór dni",
            ),
        ),
    ]
