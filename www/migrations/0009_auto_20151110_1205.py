import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("www", "0008_piec_rat"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="usernotificationlog",
            options={
                "verbose_name": "log powiadomień",
                "verbose_name_plural": "logi powiadomień",
            },
        ),
        migrations.AddField(
            model_name="sitemodule",
            name="pokazuj_tytul",
            field=models.BooleanField(
                default=True,
                help_text="Jeśli odznaczone cały niebieski nagłówek wraz z tytułem nie będzie wyświetlany.",
                verbose_name="pokazuj tytuł",
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="uczestnik",
            name="raty_nazwa_dokumentu",
            field=models.CharField(
                max_length=200, verbose_name="nazwa dokumentu", blank=True
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="uczestnik",
            name="raty_numer_dokumentu",
            field=models.CharField(
                max_length=200, verbose_name="numer dokumentu", blank=True
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="uczestnik",
            name="raty_panstwo",
            field=models.CharField(
                blank=True,
                max_length=200,
                verbose_name="kraj",
                choices=[("polska", "Poland"), ("inny", "other")],
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="uczestnik",
            name="raty_pesel",
            field=models.CharField(
                help_text="Dotyczy tylko narodowości polskiej.",
                max_length=11,
                verbose_name="PESEL",
                blank=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="zgloszenie",
            name="raty_nazwa_dokumentu",
            field=models.CharField(
                max_length=200, verbose_name="nazwa dokumentu", blank=True
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="zgloszenie",
            name="raty_numer_dokumentu",
            field=models.CharField(
                max_length=200, verbose_name="numer dokumentu", blank=True
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="zgloszenie",
            name="raty_panstwo",
            field=models.CharField(
                blank=True,
                max_length=200,
                verbose_name="kraj",
                choices=[("polska", "Poland"), ("inny", "other")],
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="zgloszenie",
            name="raty_pesel",
            field=models.CharField(
                help_text="Dotyczy tylko narodowości polskiej.",
                max_length=11,
                verbose_name="PESEL",
                blank=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="szkolenie",
            name="podzbior_dni",
            field=models.CommaSeparatedIntegerField(
                help_text="Lista dni oddzielona przecinkami, np. 1,2,3",
                max_length=100,
                verbose_name="podzbiór dni",
                blank=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="terminszkolenia",
            name="termin_nadrzedny",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="nadrzedne",
                blank=True,
                to="www.TerminSzkolenia",
                help_text="Proszę podać, jeśli edytowany Termin Szkolenia jest częścią innego, większego Terminu Szkolenia",
                null=True,
                verbose_name="termin nadrzędny",
            ),
            preserve_default=True,
        ),
    ]
