import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("www", "0084_continuationunsubscribed"),
    ]

    operations = [
        migrations.CreateModel(
            name="SzkolenieWariant",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                (
                    "szkolenie",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="warianty_szkolenia",
                        to="www.Szkolenie",
                    ),
                ),
                (
                    "wariant",
                    models.OneToOneField(
                        to="www.Szkolenie",
                        on_delete=django.db.models.deletion.CASCADE,
                        limit_choices_to={
                            "aktywne": False,
                            "tag_dlugosc__slug": "kurs-zawodowy",
                        },
                    ),
                ),
            ],
            options={
                "ordering": ["-pk"],
                "verbose_name": "Wariant szkolenia",
                "verbose_name_plural": "Warianty szkoleń",
            },
            bases=(models.Model,),
        ),
        migrations.AlterUniqueTogether(
            name="szkoleniewariant",
            unique_together=set([("szkolenie", "wariant")]),
        ),
    ]
