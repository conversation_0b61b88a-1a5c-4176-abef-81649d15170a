from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("www", "0033_uczestnik_faktura_status"),
    ]

    operations = [
        migrations.CreateModel(
            name="AutoresponderLog",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                (
                    "receiver_email",
                    models.EmailField(max_length=250, verbose_name="email"),
                ),
                (
                    "email_sent_at",
                    models.DateTimeField(
                        auto_now_add=True,
                        verbose_name="data wysłania emaila",
                        db_index=True,
                    ),
                ),
                (
                    "email_type",
                    models.CharField(
                        max_length=50,
                        verbose_name="typ autoodpowiedzi",
                        choices=[
                            ("formularz_kontaktowy", "Formularz kontaktowy"),
                            ("masz_pytanie", "Masz pytanie"),
                            ("formularz_rejestracji", "Formularz rejestracji"),
                            ("propozycja_terminu", "Propozycja terminu"),
                            ("link_rejestracyjny", "Link rejestracyjny do szkolenia"),
                        ],
                    ),
                ),
            ],
            options={
                "verbose_name": "log autoodpowiedzi",
                "verbose_name_plural": "logi autoodpowiedzi",
            },
            bases=(models.Model,),
        ),
        migrations.AddField(
            model_name="terminszkolenia",
            name="potencjalnie_zainteresowani_mail",
            field=models.TextField(
                verbose_name="treść maila dla potencjalnie zainteresowanych z innych miast",
                blank=True,
            ),
            preserve_default=True,
        ),
    ]
