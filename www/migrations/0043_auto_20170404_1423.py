from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("www", "0042_myflatpage_is_landingpage"),
    ]

    operations = [
        migrations.AddField(
            model_name="szkolenie",
            name="czas_dni_kurs",
            field=models.IntegerField(
                default=None,
                help_text="domyślnie należy wpisywać dni trwania kursu - pole używane wyłacznie do walidacji w terminach szkolenia.",
                null=True,
                verbose_name="czas dni (kurs)",
                blank=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="terminszkolenia",
            name="czas_dni_kurs",
            field=models.IntegerField(
                default=None,
                help_text="domyślnie należy wpisywać dni trwania kursu - pole używane wyłacznie do walidacji. Jesli nie podane, zostanie pobrana wartoś<PERSON> z obiektu Szkolenia.",
                null=True,
                verbose_name="czas dni (kurs)",
                blank=True,
            ),
            preserve_default=True,
        ),
    ]
