import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("www", "0079_graduatestory"),
    ]

    operations = [
        migrations.CreateModel(
            name="UczestnikNotification",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                (
                    "email",
                    models.EmailField(max_length=75, verbose_name="email uczestnika"),
                ),
                (
                    "notification_type",
                    models.CharField(
                        max_length=50,
                        verbose_name="typ powiadomienia",
                        choices=[
                            ("rata_1_niezaplacona", "rata I niezapłacona"),
                            ("rata_2_niezaplacona", "rata II niezapłacona"),
                            ("rata_3_niezaplacona", "rata III niezapłacona"),
                            ("rata_4_niezaplacona", "rata IV niezapłacona"),
                            ("rata_5_niezaplacona", "rata <PERSON> niezapłacona"),
                        ],
                    ),
                ),
                ("message", models.TextField(verbose_name="wiadomoś<PERSON>")),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="utworzono"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="aktualizowano"),
                ),
                (
                    "participant",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        verbose_name="uczestnik",
                        to="www.Uczestnik",
                    ),
                ),
            ],
            options={
                "ordering": ("-created_at",),
                "verbose_name": "notyfikacja uczestnika",
                "verbose_name_plural": "notyfikacje uczestników",
            },
            bases=(models.Model,),
        ),
        migrations.AlterUniqueTogether(
            name="uczestniknotification",
            unique_together=set([("participant", "notification_type")]),
        ),
    ]
