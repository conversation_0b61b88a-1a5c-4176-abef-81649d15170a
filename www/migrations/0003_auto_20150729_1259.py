from django.db import migrations, models


class FakeAlterField(migrations.AlterField):
    """
    Ta migracja to *beznajdziejny* przypadek. Pole M2M Szkolenie.prowadzacy
    korzysta z tej samej tabeli co M2M Prowadzacy.szkolenia - jest to nie do
    przelkniecia przez migracje Django, dlatego nalezy zaws<PERSON> robic fake
    tej migracji.
    """

    def database_forwards(self, app_label, schema_editor, from_state, to_state):
        pass

    def database_backwards(self, app_label, schema_editor, from_state, to_state):
        pass


class Migration(migrations.Migration):

    dependencies = [
        ("www", "0002_testimonial_id_opinii_zrodlowej"),
    ]

    operations = [
        FakeAlterField(
            model_name="szkolenie",
            name="prowadzacy",
            field=models.ManyToManyField(
                related_name="szkolenie_set",
                db_table="www_prowadzacy_szkolenia",
                to="www.Prowadzacy",
                blank=True,
            ),
            preserve_default=True,
        ),
    ]
