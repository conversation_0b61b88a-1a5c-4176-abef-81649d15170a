from django.conf import settings
from django.db import migrations, models


def add_default_author(apps, schema_editor):
    User = apps.get_model("auth", "User")
    Asset = apps.get_model("www", "Asset")

    try:
        user = User.objects.get(username="m<PERSON><PERSON><PERSON><PERSON><PERSON>")
    except User.DoesNotExist:
        pass
    else:
        for asset in Asset.objects.all():
            if not asset.user:
                asset.user = user
                asset.save()


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("www", "0004_auto_20150804_1318"),
    ]

    operations = [
        migrations.RunPython(add_default_author),
    ]
