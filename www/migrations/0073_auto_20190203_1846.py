import uuid

import django.core.validators
from django.db import migrations, models


def set_secret_key(apps, schema_editor):
    TerminSzkolenia = apps.get_model("www", "TerminSzkolenia")

    for termin in TerminSzkolenia.objects.all():
        termin.secret_key = uuid.uuid4()
        termin.save(update_fields=["secret_key"])


class Migration(migrations.Migration):

    dependencies = [
        ("www", "0072_myflatpage_is_bootcamp"),
    ]

    operations = [
        migrations.AddField(
            model_name="terminszkolenia",
            name="cena_bazowa_do_n_osob",
            field=models.PositiveSmallIntegerField(
                default=None,
                validators=[django.core.validators.MinValueValidator(1)],
                blank=True,
                help_text="Jeśli nie podano, obowiązuje cena bazowa dla wszystkich grup.",
                null=True,
                verbose_name="od ilu osób N (włącznie) obowiązuje nowa cena bazowa szkolenia",
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="terminszkolenia",
            name="cena_bazowa_powyzej_n_osob",
            field=models.DecimalField(
                decimal_places=2,
                validators=[django.core.validators.MinValueValidator(1)],
                max_digits=30,
                blank=True,
                help_text="Jeśli nie podano, obowiązuje cena bazowa dla wszystkich grup.",
                null=True,
                verbose_name="nowa cena bazowa powyżej N osób",
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="terminszkolenia",
            name="secret_key",
            field=models.UUIDField(
                verbose_name="secret key",
                unique=True,
                max_length=32,
                editable=False,
                blank=True,
                default=None,
                null=True,
            ),
            preserve_default=False,
        ),
        migrations.RunPython(set_secret_key),
    ]
