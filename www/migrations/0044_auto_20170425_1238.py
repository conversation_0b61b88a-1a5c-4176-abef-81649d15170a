import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("www", "0043_auto_20170404_1423"),
    ]

    operations = [
        migrations.CreateModel(
            name="DzienSzkolenia",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("data", models.DateField(verbose_name="data")),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="utworzono", null=True
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        auto_now=True, verbose_name="aktualizowano", null=True
                    ),
                ),
                (
                    "prowadzacy",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        verbose_name="prowadzący",
                        to="www.Prowadzacy",
                    ),
                ),
                (
                    "sala",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        verbose_name="sala",
                        to="www.Sala",
                    ),
                ),
            ],
            options={
                "ordering": ("data",),
                "verbose_name": "dzień szkolenia",
                "verbose_name_plural": "dni szkolenia",
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="Sprzet",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                (
                    "nazwa",
                    models.CharField(max_length=250, verbose_name="nazwa zestawu"),
                ),
                (
                    "liczba_urzadzen",
                    models.PositiveIntegerField(
                        default=0, verbose_name="liczba urządzeń"
                    ),
                ),
                (
                    "sprawdzaj_konflikty",
                    models.BooleanField(
                        default=True, verbose_name="sprawdzaj konflikty"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True,
                        null=True,
                        verbose_name="utworzono",
                        db_index=True,
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        auto_now=True, verbose_name="aktualizowano", null=True
                    ),
                ),
            ],
            options={
                "verbose_name": "zestaw komputerowy",
                "verbose_name_plural": "zestawy komputerowe",
            },
            bases=(models.Model,),
        ),
        migrations.AddField(
            model_name="dzienszkolenia",
            name="sprzet",
            field=models.ManyToManyField(to="www.Sprzet", verbose_name="sprzęt"),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="dzienszkolenia",
            name="terminszkolenia",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                verbose_name="termin szkolenia",
                related_name="dni_szkolenia",
                to="www.TerminSzkolenia",
            ),
            preserve_default=True,
        ),
        migrations.AlterUniqueTogether(
            name="dzienszkolenia",
            unique_together=set([("data", "terminszkolenia")]),
        ),
        migrations.AddField(
            model_name="terminszkolenia",
            name="sprzet",
            field=models.ManyToManyField(
                to="www.Sprzet", verbose_name="zestawy komputerowe", blank=True
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="calendarupdate",
            name="daty_szczegolowo",
            field=models.TextField(blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="terminszkolenia",
            name="kalendarze_dane_flat",
            field=models.TextField(editable=False, blank=True),
            preserve_default=True,
        ),
    ]
