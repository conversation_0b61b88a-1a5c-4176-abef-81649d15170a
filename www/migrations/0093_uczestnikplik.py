# -*- coding: utf-8 -*-
# Generated by Django 1.11.29 on 2020-10-06 11:32
from __future__ import unicode_literals

import django.db.models.deletion
from django.db import migrations, models

import www.models
import www.utils


class Migration(migrations.Migration):

    dependencies = [
        ("www", "0092_tagtechnologia_static_page"),
    ]

    operations = [
        migrations.CreateModel(
            name="UczestnikPlik",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "file",
                    models.FileField(
                        max_length=250,
                        storage=www.models.ParticipantFileSystemStorage(),
                        upload_to=www.utils.get_upload_path_for_participant_files,
                        verbose_name="plik",
                    ),
                ),
                (
                    "remote_addr",
                    models.GenericIPAddressField(
                        blank=True, editable=False, null=True, verbose_name="adres IP"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True,
                        db_index=True,
                        null=True,
                        verbose_name="utworzono",
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        auto_now=True, null=True, verbose_name="aktualizowano"
                    ),
                ),
                (
                    "participant",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="www.Uczestnik",
                        verbose_name="uczestnik",
                    ),
                ),
            ],
            options={
                "verbose_name": "plik",
                "verbose_name_plural": "pliki",
            },
        ),
    ]
