from django.conf import settings
from django.db import migrations, models


def copy_tagline(apps, schema_editor):
    Prowadzacy = apps.get_model("www", "Prowadzacy")

    for prowadzacy in Prowadzacy.objects.all():
        prowadzacy.tagline_en = prowadzacy.tagline
        prowadzacy.save()


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("www", "0029_prowadzacy_tagline_en"),
    ]

    operations = [
        migrations.RunPython(copy_tagline),
    ]
