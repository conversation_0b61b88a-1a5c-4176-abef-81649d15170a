from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("www", "0051_uczestnik_zgloszenie_form"),
    ]

    operations = [
        migrations.AddField(
            model_name="uczestnik",
            name="faktura_raty_wyslana",
            field=models.DateTimeField(null=True, editable=False, blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="uczestnik",
            name="nr_faktury_raty",
            field=models.CharField(
                max_length=80, verbose_name="nr faktury z ratami", blank=True
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="uczestnik",
            name="zliczacz_faktura_raty_no",
            field=models.CharField(
                help_text="Je<PERSON><PERSON> ch<PERSON>z usunąć omyłkowo wystawioną fakturę, to nale<PERSON>y usunąć niniejsze pole (id faktury z ratami w zliczacz) ORAZ wejść do Zliczacza i ręcznie skasować tę fakturę.",
                max_length=10,
                null=True,
                verbose_name="ID faktury z ratami w zliczacz",
                blank=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="uczestnik",
            name="nr_faktury",
            field=models.CharField(
                max_length=80, verbose_name="nr faktury", blank=True
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="uczestnik",
            name="nr_proformy",
            field=models.CharField(
                help_text="jeżeli wystawiono",
                max_length=40,
                verbose_name="nr proformy",
                blank=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="uczestnik",
            name="zliczacz_faktura_no",
            field=models.CharField(
                help_text="Jeśli chcesz usunąć omyłkowo wystawioną fakturę, to należy usunąć niniejsze pole (id faktury w zliczacz) ORAZ wejść do Zliczacza i ręcznie skasować tę fakturę.",
                max_length=10,
                null=True,
                verbose_name="ID faktury 'zwykłej' w zliczacz",
                blank=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="uczestnik",
            name="zliczacz_proforma_no",
            field=models.CharField(
                help_text="Jeśli chcesz usunąć omyłkowo wystawioną fakturę, to należy usunąć niniejsze pole (id faktury w zliczacz) ORAZ wejść do Zliczacza i ręcznie skasować tę fakturę.",
                max_length=10,
                null=True,
                verbose_name="ID faktury proforma w zliczacz",
                blank=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="uczestnik",
            name="faktura_raty_data_wykonania_uslugi",
            field=models.DateField(
                help_text="Określa datę wykonania usługi na fakturze. Jeśli nie podany zostanie automatycznie obliczony.",
                null=True,
                verbose_name="data wykonania usługi faktury (ratalnej)",
                blank=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="uczestnik",
            name="faktura_raty_data_wystawienia",
            field=models.DateField(
                help_text="Jeśli nie podana zostanie ustawiony dzień w chwili generowania faktury.",
                null=True,
                verbose_name="data wystawienia faktury (ratalnej)",
                blank=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="uczestnik",
            name="faktura_data_wykonania_uslugi",
            field=models.DateField(
                help_text="Określa datę wykonania usługi na fakturze. Jeśli nie podany zostanie automatycznie obliczony.",
                null=True,
                verbose_name="data wykonania usługi faktury",
                blank=True,
            ),
            preserve_default=True,
        ),
    ]
