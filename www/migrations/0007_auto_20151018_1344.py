import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("www", "0006_prowadzacy_pracowal_do"),
    ]

    operations = [
        migrations.AddField(
            model_name="szkolenie",
            name="podzbior_dni",
            field=models.CommaSeparatedIntegerField(
                help_text="Np: 1,2,3",
                max_length=100,
                verbose_name="podzbiór dni",
                blank=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="szkolenie",
            name="szkolenie_nadrzedne",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                verbose_name="szkolenie nadrzędne",
                blank=True,
                to="www.Szkolenie",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="szkolenie",
            name="wiele_rat",
            field=models.BooleanField(default=True, verbose_name="dozwolone wiele rat"),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="terminszkolenia",
            name="termin_nadrzedny",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="nadrzedne",
                verbose_name="termin nadrzędny",
                blank=True,
                to="www.TerminSzkolenia",
                null=True,
            ),
            preserve_default=True,
        ),
    ]
