import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("www", "0024_szkolenie_cel_pdf"),
    ]

    operations = [
        migrations.CreateModel(
            name="TagTechnologiaProwadzacy",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                (
                    "ordering",
                    models.PositiveSmallIntegerField(
                        default=1, verbose_name="kolejnoś<PERSON>"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True,
                        null=True,
                        verbose_name="utworzono",
                        db_index=True,
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        auto_now=True, verbose_name="aktualizowano", null=True
                    ),
                ),
                (
                    "prowadzacy",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        verbose_name="prowadzący",
                        to="www.Prowadzacy",
                    ),
                ),
                (
                    "tagtechnologia",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="www.TagTechnologia",
                    ),
                ),
            ],
            options={
                "ordering": ["ordering"],
                "verbose_name_plural": "Prowadzący",
            },
            bases=(models.Model,),
        ),
        migrations.AlterUniqueTogether(
            name="tagtechnologiaprowadzacy",
            unique_together=set([("tagtechnologia", "prowadzacy")]),
        ),
    ]
