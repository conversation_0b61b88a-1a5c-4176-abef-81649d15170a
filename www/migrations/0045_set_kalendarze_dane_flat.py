from django.conf import settings
from django.db import migrations, models


def set_kalendarze_dane_flat(apps, schema_editor):
    TerminSzkolenia = apps.get_model("www", "TerminSzkolenia")

    for termin in TerminSzkolenia.objects.all():
        termin.kalendarze_dane_flat = termin.get_kalendarze_dane_flat(as_json=True)
        termin.save(update_fields=["kalendarze_dane_flat"], _raw_save=True)


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("www", "0044_auto_20170425_1238"),
    ]

    operations = [
        migrations.RunPython(set_kalendarze_dane_flat),
    ]
