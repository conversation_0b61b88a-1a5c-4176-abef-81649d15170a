from django.conf import settings
from django.db import migrations, models


def set_piec_rat(apps, schema_editor):
    Szkolenie = apps.get_model("www", "Szkolenie")

    for obj in Szkolenie.objects.all():
        if obj.tag_dlugosc.slug != "kurs-zawodowy":
            obj.wiele_rat = False
            obj.save()


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("www", "0007_auto_20151018_1344"),
    ]

    operations = [
        migrations.RunPython(set_piec_rat),
    ]
