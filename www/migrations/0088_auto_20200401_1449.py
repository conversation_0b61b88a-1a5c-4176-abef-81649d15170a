import stdimage.models
from django.db import migrations, models

import www.models
import www.utils


class Migration(migrations.Migration):

    dependencies = [
        ("www", "0087_auto_20200331_1134"),
    ]

    operations = [
        migrations.AddField(
            model_name="terminszkoleniamail",
            name="attachment",
            field=models.FileField(
                upload_to=www.utils.get_upload_path,
                storage=www.models.AttachmentFileSystemStorage(),
                verbose_name="załącznik",
                blank=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="terminszkoleniamail",
            name="participant_status",
            field=models.IntegerField(
                default=1,
                choices=[
                    (-3, "zgłoszenie z internetu, trzeba potwierdzić"),
                    (-2, "zgłoszenie tel/mail, trzeba potwierdzić"),
                    (
                        -1,
                        "powiedzieliśmy że niepewne, skontaktować się znów przed szkoleniem",
                    ),
                    (0, "trzeba przemówić"),
                    (1, "potwierdzone, odbywa się, uczestnik wszystko wie"),
                    (3, "przeszkolony"),
                    (5, "nieprzeszkolony (nie odbyło się), ale nadal zainteresowany"),
                    (4, "zrezygnował"),
                    (99, "WSZYSCY NIEZREZYGNOWANI"),
                ],
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="prowadzacy",
            name="fotka",
            field=stdimage.models.StdImageField(
                blank=True,
                storage=www.models.AssetsFileSystemStorage(),
                upload_to="prowadzacy",
            ),
        ),
    ]
