import datetime

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models

import www.validators


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("www", "0069_szkolenie_krotka_nazwa"),
    ]

    operations = [
        migrations.CreateModel(
            name="Leave",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                (
                    "dates",
                    models.TextField(
                        verbose_name="daty szczegółowo",
                        validators=[www.validators.MultiDatesValidator()],
                    ),
                ),
                (
                    "days",
                    models.PositiveSmallIntegerField(
                        help_text="Wyliczane automatycznie.", verbose_name="liczba dni"
                    ),
                ),
                (
                    "start",
                    models.DateField(
                        help_text="Wyliczane automatycznie.", verbose_name="początek"
                    ),
                ),
                (
                    "end",
                    models.DateField(
                        help_text="Wyliczane automatycznie.", verbose_name="koniec"
                    ),
                ),
                (
                    "leave_type",
                    models.CharField(
                        default="w",
                        max_length=20,
                        verbose_name="typ urlopu",
                        choices=[
                            ("w", "wypoczynkowy"),
                            ("b", "bezpłatny"),
                            ("o", "okolicznościowy"),
                            ("dz", "opieka nad dzieckiem"),
                        ],
                    ),
                ),
                ("year", models.IntegerField(verbose_name="za rok")),
                (
                    "accepted",
                    models.BooleanField(
                        default=False,
                        help_text="Uzupełnia moderator.",
                        verbose_name="zaakceptowany",
                    ),
                ),
                ("comment", models.TextField(verbose_name="komentarz", blank=True)),
                (
                    "created_at",
                    models.DateTimeField(
                        default=datetime.datetime.now,
                        null=True,
                        verbose_name="utworzono",
                        db_index=True,
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        auto_now=True,
                        null=True,
                        verbose_name="aktualizowano",
                        db_index=True,
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        verbose_name="użytkownik",
                        to=settings.AUTH_USER_MODEL,
                        help_text="Uzupełniane automatycznie lub przez moderatora.",
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
                "verbose_name": "wniosek urlopowy",
                "verbose_name_plural": "wnioski urlopowe",
            },
            bases=(models.Model,),
        ),
    ]
