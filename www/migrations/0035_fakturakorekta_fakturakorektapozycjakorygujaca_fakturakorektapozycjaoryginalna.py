import datetime

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("www", "0034_auto_20161110_1122"),
    ]

    operations = [
        migrations.CreateModel(
            name="FakturaKorekta",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("faktura_id", models.IntegerField(verbose_name="zliczacz ID")),
                (
                    "faktura_numer",
                    models.CharField(max_length=100, verbose_name="numer faktury"),
                ),
                (
                    "faktura_data_sprzedazy",
                    models.DateField(null=True, verbose_name="data sprzedaży"),
                ),
                (
                    "faktura_data_zaplaty",
                    models.DateField(
                        null=True, verbose_name="data zapłaty", blank=True
                    ),
                ),
                (
                    "faktura_data_wystawienia",
                    models.DateField(null=True, verbose_name="data wystawienia"),
                ),
                (
                    "faktura_termin_platnosci",
                    models.DateField(null=True, verbose_name="termin płatno<PERSON>"),
                ),
                (
                    "faktura_sposob_platnosci",
                    models.CharField(
                        max_length=255, verbose_name="sposób płatności", blank=True
                    ),
                ),
                (
                    "faktura_miejsce_wystawienia",
                    models.CharField(
                        max_length=255, verbose_name="miejsce wystawienia"
                    ),
                ),
                (
                    "faktura_typ_daty",
                    models.IntegerField(
                        null=True,
                        verbose_name="typ",
                        choices=[
                            (1, "Data wykonania usługi"),
                            (2, "Data wpłaty"),
                            (3, "Data sprzedaży"),
                        ],
                    ),
                ),
                ("faktura_uwagi", models.TextField(verbose_name="uwagi", blank=True)),
                (
                    "faktura_numer_korekty",
                    models.CharField(
                        unique=True,
                        max_length=100,
                        verbose_name="numer faktury korygującej",
                    ),
                ),
                (
                    "faktura_data_wystawienia_korekty",
                    models.DateField(
                        default=datetime.date.today,
                        verbose_name="data wystawienia korekty",
                    ),
                ),
                (
                    "faktura_miejsce_wystawienia_korekty",
                    models.CharField(
                        default="Warszawa",
                        max_length=255,
                        verbose_name="miejsce wystawienia",
                    ),
                ),
                (
                    "faktura_powod_korekty",
                    models.TextField(
                        default="Niezebrana grupa szkoleniowa.",
                        verbose_name="powód korekty",
                    ),
                ),
                (
                    "faktura_sposob_platnosci_korekty",
                    models.CharField(
                        max_length=255, verbose_name="sposób płatności", blank=True
                    ),
                ),
                (
                    "faktura_termin_platnosci_korekty",
                    models.DateField(
                        null=True, verbose_name="termin płatności", blank=True
                    ),
                ),
                (
                    "cena_bilans",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="Wyliczana automatycznie (jeśli zaznaczone pole 'Wyliczaj automatycznie bilans').<br>W przypadku opłaconych faktur, jest to różnica 'SUMA KOREKTA' - 'SUMA ORYGINAŁ' (znak ujemny to zwrot, a dodatni dopłata). W przypadku faktur nieopłaconych jest to po prostu 'SUMA KOREKTA'.<br>PS. Opłacona faktura to taka, która ma ustawiony status w polu 'Faktura - Oryginał' / 'Status faktury' na 'zapłacona'.",
                        max_digits=12,
                        verbose_name="cena - bilans",
                    ),
                ),
                (
                    "wyliczaj_bilans",
                    models.BooleanField(
                        default=True, verbose_name="wyliczaj automatycznie bilans"
                    ),
                ),
                (
                    "typ_faktury",
                    models.IntegerField(null=True, verbose_name="typ faktury"),
                ),
                (
                    "status_faktury",
                    models.IntegerField(
                        null=True,
                        verbose_name="status faktury",
                        choices=[
                            (2, "gotowa"),
                            (3, "wysłana"),
                            (4, "zapłacona"),
                            (5, "niewindykowalna"),
                            (6, "zapłacona częściowo"),
                        ],
                    ),
                ),
                (
                    "faktura_kod_pocztowy_sprzedawcy",
                    models.CharField(max_length=15, verbose_name="kod pocztowy"),
                ),
                (
                    "faktura_nazwa_sprzedawcy",
                    models.CharField(max_length=255, verbose_name="nazwa"),
                ),
                (
                    "faktura_adres_sprzedawcy",
                    models.CharField(max_length=255, verbose_name="adres"),
                ),
                (
                    "faktura_miasto_sprzedawcy",
                    models.CharField(max_length=255, verbose_name="miejscowość"),
                ),
                (
                    "faktura_numer_konta",
                    models.CharField(max_length=255, verbose_name="numer konta"),
                ),
                (
                    "faktura_nip_sprzedawcy",
                    models.CharField(max_length=50, verbose_name="NIP"),
                ),
                (
                    "faktura_nazwa_nabywcy",
                    models.CharField(max_length=255, verbose_name="nazwa"),
                ),
                (
                    "faktura_adres_nabywcy",
                    models.CharField(max_length=255, verbose_name="adres"),
                ),
                (
                    "faktura_nip_nabywcy",
                    models.CharField(max_length=50, verbose_name="NIP", blank=True),
                ),
                (
                    "faktura_miasto_nabywcy",
                    models.CharField(max_length=255, verbose_name="miejscowość"),
                ),
                (
                    "faktura_kod_pocztowy_nabywcy",
                    models.CharField(max_length=15, verbose_name="kod pocztowy"),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True,
                        null=True,
                        verbose_name="utworzono",
                        db_index=True,
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        auto_now=True, verbose_name="aktualizowano", null=True
                    ),
                ),
            ],
            options={
                "ordering": ("-created_at",),
                "verbose_name": "korekta faktury",
                "verbose_name_plural": "korekty faktur",
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="FakturaKorektaPozycjaKorygujaca",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("pozycja_lp", models.IntegerField(verbose_name="LP")),
                ("pozycja_opis", models.TextField(verbose_name="opis")),
                (
                    "pozycja_cena_jednostkowa_netto",
                    models.DecimalField(
                        verbose_name="cena jednostkowa netto",
                        max_digits=12,
                        decimal_places=2,
                    ),
                ),
                (
                    "pozycja_wartosc_netto",
                    models.DecimalField(
                        verbose_name="wartość netto", max_digits=12, decimal_places=2
                    ),
                ),
                (
                    "pozycja_liczba_jednostek",
                    models.DecimalField(
                        verbose_name="liczba jednostek", max_digits=12, decimal_places=2
                    ),
                ),
                (
                    "pozycja_wartosc_brutto",
                    models.DecimalField(
                        verbose_name="wartość brutto", max_digits=12, decimal_places=2
                    ),
                ),
                (
                    "pozycja_kwota_vat",
                    models.DecimalField(
                        verbose_name="kwota VAT", max_digits=12, decimal_places=2
                    ),
                ),
                (
                    "stawka_vat",
                    models.IntegerField(
                        verbose_name="stawka VAT",
                        choices=[
                            (1, "22% (22)"),
                            (2, "7% (7)"),
                            (3, "3% (3)"),
                            (4, "0% (0)"),
                            (5, "zwolniony (zw.)"),
                            (6, "nie podlega opodatkowaniu (NP)"),
                            (7, "8% (8)"),
                            (8, "23% (23)"),
                        ],
                    ),
                ),
                (
                    "pozycja_zaliczka",
                    models.BooleanField(default=False, verbose_name="zaliczka?"),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True,
                        null=True,
                        verbose_name="utworzono",
                        db_index=True,
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        auto_now=True, verbose_name="aktualizowano", null=True
                    ),
                ),
                (
                    "faktura_korekta",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="www.FakturaKorekta",
                    ),
                ),
            ],
            options={
                "ordering": ("-created_at",),
                "verbose_name": "pozycja korygująca",
                "verbose_name_plural": "pozycje korygujące",
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="FakturaKorektaPozycjaOryginalna",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("pozycja_lp", models.IntegerField(verbose_name="LP")),
                ("pozycja_opis", models.TextField(verbose_name="opis")),
                (
                    "pozycja_cena_jednostkowa_netto",
                    models.DecimalField(
                        verbose_name="cena jednostkowa netto",
                        max_digits=12,
                        decimal_places=2,
                    ),
                ),
                (
                    "pozycja_liczba_jednostek",
                    models.DecimalField(
                        verbose_name="liczba jednostek", max_digits=12, decimal_places=2
                    ),
                ),
                (
                    "pozycja_wartosc_netto",
                    models.DecimalField(
                        verbose_name="wartość netto", max_digits=12, decimal_places=2
                    ),
                ),
                (
                    "pozycja_wartosc_brutto",
                    models.DecimalField(
                        verbose_name="wartość brutto", max_digits=12, decimal_places=2
                    ),
                ),
                (
                    "pozycja_kwota_vat",
                    models.DecimalField(
                        verbose_name="kwota VAT", max_digits=12, decimal_places=2
                    ),
                ),
                (
                    "stawka_vat",
                    models.IntegerField(
                        verbose_name="stawka VAT",
                        choices=[
                            (1, "22% (22)"),
                            (2, "7% (7)"),
                            (3, "3% (3)"),
                            (4, "0% (0)"),
                            (5, "zwolniony (zw.)"),
                            (6, "nie podlega opodatkowaniu (NP)"),
                            (7, "8% (8)"),
                            (8, "23% (23)"),
                        ],
                    ),
                ),
                (
                    "pozycja_zaliczka",
                    models.BooleanField(default=False, verbose_name="zaliczka?"),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True,
                        null=True,
                        verbose_name="utworzono",
                        db_index=True,
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        auto_now=True, verbose_name="aktualizowano", null=True
                    ),
                ),
                (
                    "faktura_korekta",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="www.FakturaKorekta",
                    ),
                ),
            ],
            options={
                "ordering": ("-created_at",),
                "verbose_name": "pozycja oryginalna",
                "verbose_name_plural": "pozycje oryginalne",
            },
            bases=(models.Model,),
        ),
    ]
