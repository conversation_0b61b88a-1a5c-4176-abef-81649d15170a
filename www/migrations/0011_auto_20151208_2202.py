import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("www", "0010_auto_20151202_1906"),
    ]

    operations = [
        migrations.CreateModel(
            name="TerminSzkoleniaLog",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                (
                    "log_type",
                    models.CharField(
                        max_length=200,
                        verbose_name="typ powiadomienia",
                        choices=[
                            ("kontynuacja_do_trenera", "Mail z kontynuacją do trenera"),
                            ("16_dni_przed_startem", "16 dni przed startem"),
                            (
                                "grupa_rusza_prawie_zebrana",
                                "Grupa rusza (prawie zebrana)",
                            ),
                            (
                                "grupa_rusza_szybkie_potwierdzenie",
                                "Grupa rusza (szybkie potwierdzenie)",
                            ),
                            (
                                "potencjalnie_zainteresowani_z_innych_miast",
                                "Potencjalnie zainteresowani z innych miast",
                            ),
                            ("czy_reklamowac", "Włączone reklamowanie"),
                            ("odbylo_sie", "Uruchomione"),
                        ],
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True,
                        null=True,
                        verbose_name="utworzono",
                        db_index=True,
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        auto_now=True, verbose_name="aktualizowano", null=True
                    ),
                ),
                (
                    "term",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        verbose_name="termin szkolenia",
                        to="www.TerminSzkolenia",
                    ),
                ),
            ],
            options={
                "verbose_name": "log terminu szkolenia",
                "verbose_name_plural": "logi terminów szkoleń",
            },
            bases=(models.Model,),
        ),
        migrations.AlterUniqueTogether(
            name="terminszkolenialog",
            unique_together=set([("term", "log_type")]),
        ),
    ]
