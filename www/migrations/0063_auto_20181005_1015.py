import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("www", "0062_discountcode_comment"),
    ]

    operations = [
        migrations.AddField(
            model_name="discountcode",
            name="available_from",
            field=models.DateTimeField(
                db_index=True, null=True, verbose_name="ważny od", blank=True
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="discountcode",
            name="available_to",
            field=models.DateTimeField(
                db_index=True, null=True, verbose_name="ważny do", blank=True
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="discountcode",
            name="limit",
            field=models.PositiveSmallIntegerField(
                default=1,
                validators=[django.core.validators.MinValueValidator(1)],
                blank=True,
                help_text="Gdy pusty użytkownik może korzystać z kodu dowoli.",
                null=True,
                verbose_name="limit użyć",
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="szkolenie",
            name="kontynuacja",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="posiadajace_kontynuacje",
                verbose_name="szkolenie będące kontynuacją",
                blank=True,
                to="www.Szkolenie",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="terminszkolenia",
            name="maile_o_kontynuacji",
            field=models.BooleanField(
                default=False,
                help_text="Jeśli tak, to obiekt szkolenia musi mieć ustawioną kontynuację.",
                verbose_name="wysyłać maile o kontynuacji?",
            ),
            preserve_default=True,
        ),
    ]
