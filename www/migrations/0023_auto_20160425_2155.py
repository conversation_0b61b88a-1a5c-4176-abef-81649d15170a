from django.db import migrations, models

import www.models
import www.utils


class Migration(migrations.Migration):

    dependencies = [
        ("www", "0022_szkolenie_archiwalna_liczba_przeszkolonych"),
    ]

    operations = [
        migrations.AddField(
            model_name="prowadzacy",
            name="created_at",
            field=models.DateTimeField(
                auto_now_add=True, null=True, verbose_name="utworzono", db_index=True
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="prowadzacy",
            name="updated_at",
            field=models.DateTimeField(
                auto_now=True, verbose_name="aktualizowano", null=True
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="tagtechnologia",
            name="archiwalna_liczba_przeszkolonych",
            field=models.IntegerField(
                help_text="Liczba przeszkolonych osób do momentu wprowadzenia ankiet. Wartość z tego pola (jeśli podana) dodawana jest do ogólnej liczby ocen na podstronie Taga.",
                null=True,
                verbose_name="archiwalna liczba przeszkolonych",
                blank=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="tagtechnologia",
            name="top_background",
            field=models.ImageField(
                storage=www.models.AssetsFileSystemStorage(),
                help_text="Duża grafika na tło pod cały obszar topu górnego strony.",
                upload_to=www.utils.get_upload_path_for_backgrounds,
                max_length=250,
                verbose_name="obrazek tła",
                blank=True,
            ),
            preserve_default=True,
        ),
    ]
