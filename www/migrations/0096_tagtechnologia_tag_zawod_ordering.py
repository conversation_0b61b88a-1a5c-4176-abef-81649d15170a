# -*- coding: utf-8 -*-
# Generated by Django 1.11.29 on 2021-03-21 16:30
from __future__ import unicode_literals

import re

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("www", "0095_auto_20210217_1702"),
    ]

    operations = [
        migrations.AddField(
            model_name="tagtechnologia",
            name="tag_zawod_ordering",
            field=models.CharField(
                blank=True,
                help_text='Sortuje tagi zawodu w obrębie podstrony danego Tech Taga. Pole przyjmuje ID tagów zawodu (liczby całkowite) oddzielone przecinkami, np: 1,2,3,4. <PERSON> jest typu "fail silently" - je<PERSON><PERSON> jakieś ID nie będzie się zgadzać z listą zawodów, zostanie pominięte.',
                max_length=250,
                null=True,
                validators=[
                    django.core.validators.RegexValidator(
                        re.compile("^\\d+(?:,\\d+)*\\Z"),
                        code="invalid",
                        message="Enter only digits separated by commas.",
                    )
                ],
                verbose_name='Sortowanie "tag zawód"',
            ),
        ),
    ]
