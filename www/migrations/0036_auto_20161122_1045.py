import datetime

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "www",
            "0035_fakturakorekta_fakturakorektapozycjakorygujaca_fakturakorektapozycjaoryginalna",
        ),
    ]

    operations = [
        migrations.AddField(
            model_name="fakturakorekta",
            name="faktura_kolejny_numer_korekty",
            field=models.IntegerField(
                help_text="To jest kolejny numer do pełnego numeru faktury, który będzie miał postać: {kolejny numer}/MM/YYYY/K. MM i YYYY są brane z pola 'Data wystawienia korekty'.",
                verbose_name="kolejny numer",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="fakturakorekta",
            name="faktura_data_wystawienia_korekty",
            field=models.DateField(
                default=datetime.date.today,
                help_text="Na podstawie tej daty budowany jest numer faktury. Zmiana roku lub miesiąca spowoduje zmianę numeru faktury.",
                verbose_name="data wystawienia korekty",
            ),
            preserve_default=True,
        ),
    ]
