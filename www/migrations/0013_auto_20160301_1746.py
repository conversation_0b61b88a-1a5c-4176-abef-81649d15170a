from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("www", "0012_prowadzacy_tagline"),
    ]

    operations = [
        migrations.CreateModel(
            name="CertificateNo",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("date", models.DateField(unique=True)),
                ("counter", models.PositiveSmallIntegerField(default=0)),
            ],
            options={},
            bases=(models.Model,),
        ),
        migrations.AddField(
            model_name="graduate",
            name="number",
            field=models.CharField(
                max_length=10, verbose_name="numer certyfikatu", blank=True
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="graduate",
            name="email",
            field=models.EmailField(max_length=250, verbose_name="adres email"),
            preserve_default=True,
        ),
    ]
