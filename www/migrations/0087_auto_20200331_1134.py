import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("www", "0086_terminszkolenialog_user"),
    ]

    operations = [
        migrations.CreateModel(
            name="TerminSzkoleniaMail",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("subject", models.CharField(max_length=200, verbose_name="tytuł")),
                ("message", models.TextField(verbose_name="treś<PERSON>")),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True,
                        null=True,
                        verbose_name="utworzono",
                        db_index=True,
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        auto_now=True, verbose_name="aktualizowano", null=True
                    ),
                ),
                (
                    "author",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        verbose_name="autor",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "term",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        verbose_name="termin szkolenia",
                        to="www.TerminSzkolenia",
                    ),
                ),
            ],
            options={
                "verbose_name": "mail do grupy",
                "verbose_name_plural": "maile do grupy",
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="TerminSzkoleniaMailUczestnik",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                (
                    "sent_at",
                    models.DateTimeField(null=True, verbose_name="wysłano", blank=True),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True,
                        null=True,
                        verbose_name="utworzono",
                        db_index=True,
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        auto_now=True, verbose_name="aktualizowano", null=True
                    ),
                ),
                (
                    "participant",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        verbose_name="uczestnik",
                        to="www.Uczestnik",
                    ),
                ),
                (
                    "term_mail",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="www.TerminSzkoleniaMail",
                    ),
                ),
            ],
            options={
                "verbose_name": "mail do uczestnika",
                "verbose_name_plural": "maile do uczestników",
            },
            bases=(models.Model,),
        ),
        migrations.AlterUniqueTogether(
            name="terminszkoleniamailuczestnik",
            unique_together=set([("term_mail", "participant")]),
        ),
    ]
