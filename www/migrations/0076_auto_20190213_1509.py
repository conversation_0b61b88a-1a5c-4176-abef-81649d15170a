import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("www", "0075_auto_20190206_1143"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="terminszkolenia",
            name="cena_bazowa_powyzej_n_osob",
        ),
        migrations.AddField(
            model_name="terminszkolenia",
            name="cena_za_osobe_powyzej_n_osob",
            field=models.DecimalField(
                decimal_places=2,
                validators=[django.core.validators.MinValueValidator(1)],
                max_digits=30,
                blank=True,
                null=True,
                verbose_name="wysokość dopłaty za każdą osobę powyżej N osób",
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="terminszkolenia",
            name="liczba_osob_w_grupie",
            field=models.PositiveSmallIntegerField(
                default=None,
                null=True,
                verbose_name="liczba osób (N) w grupie",
                blank=True,
                validators=[django.core.validators.MinValueValidator(1)],
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="terminszkolenia",
            name="cena_bazowa_do_n_osob",
            field=models.DecimalField(
                decimal_places=2,
                validators=[django.core.validators.MinValueValidator(1)],
                max_digits=30,
                blank=True,
                null=True,
                verbose_name="cena za grupę do N osób (włącznie)",
            ),
            preserve_default=True,
        ),
    ]
