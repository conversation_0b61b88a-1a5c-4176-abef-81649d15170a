# Generated by Django 2.2.16 on 2021-09-27 17:09

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("www", "0098_auto_20210919_2022"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="szkoleniewariant",
            options={
                "ordering": ["kolejnosc", "-pk"],
                "verbose_name": "Wariant szkolenia",
                "verbose_name_plural": "Warianty szkoleń",
            },
        ),
        migrations.AddField(
            model_name="szkolenie",
            name="czas_godziny_samodzielne",
            field=models.IntegerField(
                blank=True,
                help_text="Dotyczy wariantu szkolenia: informacja w boxie cenowym: {czas_godziny} + {czas_godziny_samodzielne}",
                null=True,
                verbose_name="czas godziny (samodzielne)",
            ),
        ),
        migrations.AddField(
            model_name="szkolenie",
            name="wariant_cena_opis",
            field=models.CharField(
                blank=True,
                help_text="Dotyczy wariantu szkolenia: tekst obok ceny.",
                max_length=250,
                verbose_name="opis ceny wariantu szkolenia",
            ),
        ),
        migrations.AddField(
            model_name="szkoleniewariant",
            name="kolejnosc",
            field=models.PositiveSmallIntegerField(
                default=1, help_text='Pierwszy na liście będzie wariantem "głównym".'
            ),
        ),
        migrations.AlterField(
            model_name="szkoleniewariant",
            name="wariant",
            field=models.OneToOneField(
                limit_choices_to={"tag_dlugosc__slug": "kurs-zawodowy"},
                on_delete=django.db.models.deletion.CASCADE,
                to="www.Szkolenie",
            ),
        ),
    ]
