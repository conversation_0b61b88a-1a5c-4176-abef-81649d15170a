from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("www", "0023_auto_20160425_2155"),
    ]

    operations = [
        migrations.AddField(
            model_name="szkolenie",
            name="cel_pdf",
            field=models.TextField(
                help_text='<PERSON><PERSON><PERSON> nie ustawione - używane będzie pole "Opis szkolenia - na jego stronę". Należy unikać skomplikowanych konstrukcji html/css. [dozwolony markup: &lt;b&gt;...&lt;/b&gt;, &lt;i&gt;...&lt;/i&gt;, &lt;a href="..."&gt;...&lt;/a&gt;, &lt;img&gt; (wycinane w PDF), listy w stylu textile: *, #, #*, #**].<br>PS. używając znaczników &lt!--pdfignore--&gt i &lt!--endpdfignore--&gt  można wyłączyć dane fragmenty (np. css) z wersji PDF.',
                verbose_name="Opis szkolenia (do PDF)",
                blank=True,
            ),
            preserve_default=True,
        ),
    ]
