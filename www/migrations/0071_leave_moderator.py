import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("www", "0070_leave"),
    ]

    operations = [
        migrations.AddField(
            model_name="leave",
            name="moderator",
            field=models.ForeignKey(
                related_name="moderator",
                on_delete=django.db.models.deletion.PROTECT,
                verbose_name="przełożony",
                to=settings.AUTH_USER_MODEL,
                help_text="Uzupełniane przez moderatora.",
                null=True,
                limit_choices_to={"is_superuser": True},
            ),
            preserve_default=True,
        ),
    ]
