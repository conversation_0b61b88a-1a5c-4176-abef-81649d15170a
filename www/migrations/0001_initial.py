from decimal import Decimal

import django.core.files.storage
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models

import www.models
import www.utils
import www.validators


class Migration(migrations.Migration):

    dependencies = [
        ("crm", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("contenttypes", "0001_initial"),
        ("i18n", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Asset",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                (
                    "file",
                    models.FileField(
                        storage=www.models.AssetsFileSystemStorage(),
                        upload_to=www.utils.get_upload_path_for_assets,
                        max_length=250,
                        verbose_name="plik",
                    ),
                ),
                (
                    "description",
                    models.TextField(verbose_name="pomocniczy opis", blank=True),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="utworzono"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="aktualizowano"),
                ),
            ],
            options={},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="Autoryzacja",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                (
                    "language",
                    models.CharField(
                        default="pl",
                        max_length=2,
                        verbose_name="j\u0119zyk",
                        choices=[("pl", "polski"), ("en", "angielski")],
                    ),
                ),
                ("nazwa", models.CharField(max_length=100, verbose_name="name")),
                ("opis_krotki", models.TextField(verbose_name="short description")),
                (
                    "opis_dlugi",
                    models.TextField(
                        help_text="Mo\u017cna u\u017cywa\u0107 znaczników HTML.",
                        verbose_name="long description",
                    ),
                ),
                (
                    "base_translation",
                    models.ForeignKey(
                        limit_choices_to={"language__exact": "pl"},
                        related_name="translation_set",
                        blank=True,
                        to="www.Autoryzacja",
                        help_text="Tu wybieramy odpowiednik obiektu w j\u0119zyku polskim.",
                        null=True,
                        verbose_name="obiekt bazowy",
                        on_delete=django.db.models.deletion.PROTECT,
                    ),
                ),
            ],
            options={
                "verbose_name": "authorization",
                "verbose_name_plural": "authorizations",
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="CalendarUpdate",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("deleted_termin_id", models.IntegerField(null=True, blank=True)),
                ("calendar", models.CharField(max_length=256)),
                ("tries", models.IntegerField(default=0)),
                ("creation_time", models.DateTimeField()),
                (
                    "update_type",
                    models.CharField(
                        max_length=128,
                        choices=[("update", "update"), ("delete", "delete")],
                    ),
                ),
                (
                    "done",
                    models.CharField(
                        default="no",
                        max_length=128,
                        choices=[
                            ("no", "no"),
                            ("yes", "yes"),
                            ("superseded", "superseded"),
                        ],
                    ),
                ),
                ("last_failure_reason", models.TextField(blank=True)),
            ],
            options={},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="CertificateGroup",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        max_length=200, verbose_name="imi\u0119 i nazwisko"
                    ),
                ),
                (
                    "slug",
                    models.SlugField(max_length=200, verbose_name="slug", blank=True),
                ),
                (
                    "mail_sent_counter",
                    models.PositiveSmallIntegerField(
                        default=0, verbose_name="licznik wys\u0142anych maili"
                    ),
                ),
                (
                    "mail_sent_at",
                    models.DateTimeField(
                        null=True, verbose_name="email wys\u0142any dnia", blank=True
                    ),
                ),
                ("email", models.EmailField(max_length=75, verbose_name="adres email")),
                (
                    "key",
                    models.UUIDField(
                        unique=True, max_length=32, editable=False, blank=True
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="utworzono"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="aktualizowano"),
                ),
            ],
            options={
                "ordering": ("-created_at",),
                "verbose_name": "grupa certyfikatów",
                "verbose_name_plural": "grupy certyfikatów",
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="Certyfikat",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("opis", models.CharField(max_length=256, blank=True)),
                (
                    "plik",
                    models.FileField(
                        storage=django.core.files.storage.FileSystemStorage(
                            base_url="/uploads/"
                        ),
                        upload_to="certyfikaty",
                    ),
                ),
            ],
            options={
                "verbose_name": "za\u0142\u0105cznik",
                "verbose_name_plural": "za\u0142\u0105czniki",
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="Change",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                (
                    "change_type",
                    models.CharField(
                        max_length=128,
                        choices=[
                            ("CHANGE", "CHANGE"),
                            ("CREATE", "CREATE"),
                            ("DELETE", "DELETE"),
                        ],
                    ),
                ),
                ("change_time", models.DateTimeField()),
                ("object_id", models.PositiveIntegerField()),
                ("object_name", models.TextField(blank=True)),
                ("field", models.CharField(max_length=256, blank=True)),
                ("old_value", models.TextField(blank=True)),
                ("new_value", models.TextField(blank=True)),
                (
                    "content_type",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="contenttypes.ContentType",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        blank=True,
                        to=settings.AUTH_USER_MODEL,
                        null=True,
                    ),
                ),
            ],
            options={
                "ordering": ["-change_time"],
                "verbose_name": "zmiana",
                "verbose_name_plural": "zmiany",
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="ContentGroup",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("title", models.CharField(max_length=100, verbose_name="tytu\u0142")),
                ("slug", models.SlugField(unique=True, verbose_name="slug")),
            ],
            options={
                "verbose_name": "grupa tre\u015bci",
                "verbose_name_plural": "grupy tre\u015bci",
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="Domena",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("url", models.CharField(max_length=250)),
            ],
            options={
                "ordering": ["url"],
                "verbose_name": "domena",
                "verbose_name_plural": "domeny",
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="Graduate",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        max_length=200, verbose_name="imi\u0119 i nazwisko"
                    ),
                ),
                ("email", models.EmailField(max_length=75, verbose_name="adres email")),
                (
                    "slug",
                    models.SlugField(
                        verbose_name="slug", max_length=200, editable=False, blank=True
                    ),
                ),
                (
                    "mail_sent_counter",
                    models.PositiveSmallIntegerField(
                        default=0, verbose_name="licznik wys\u0142anych maili"
                    ),
                ),
                (
                    "mail_sent_at",
                    models.DateTimeField(
                        null=True, verbose_name="email wys\u0142any dnia", blank=True
                    ),
                ),
                (
                    "template",
                    models.TextField(verbose_name="program szkolenia", blank=True),
                ),
                (
                    "pdf",
                    models.FileField(
                        upload_to=www.utils.get_upload_path,
                        storage=www.models.CertificateFileSystemStorage(),
                        verbose_name="plik pdf",
                        blank=True,
                    ),
                ),
                (
                    "key",
                    models.UUIDField(
                        unique=True, max_length=32, editable=False, blank=True
                    ),
                ),
                (
                    "public_key",
                    models.UUIDField(
                        unique=True, max_length=32, editable=False, blank=True
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="utworzono"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="aktualizowano"),
                ),
                (
                    "remote_addr",
                    models.IPAddressField(
                        verbose_name="adres IP", null=True, editable=False, blank=True
                    ),
                ),
            ],
            options={
                "ordering": ("-created_at",),
                "verbose_name": "absolwent",
                "verbose_name_plural": "absolwenci",
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="GrupaZaszeregowania",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("nazwa", models.CharField(max_length=128)),
                ("cena_u_klienta_waw", models.CharField(max_length=128, blank=True)),
                ("cena_u_nas_waw", models.CharField(max_length=128, blank=True)),
                ("cena_u_klienta_krk", models.CharField(max_length=128, blank=True)),
                ("cena_u_nas_krk", models.CharField(max_length=128, blank=True)),
                ("max_rozmiar_grupy", models.CharField(max_length=128, blank=True)),
                ("stawka_godzinowa", models.IntegerField(null=True, blank=True)),
                ("uwagi", models.TextField(blank=True)),
            ],
            options={
                "ordering": ["nazwa"],
                "verbose_name": "grupa zaszeregowania",
                "verbose_name_plural": "grupy zaszeregowania",
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="Highlight",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                (
                    "language",
                    models.CharField(
                        default="pl",
                        max_length=2,
                        verbose_name="j\u0119zyk",
                        choices=[("pl", "polski"), ("en", "angielski")],
                    ),
                ),
                ("tytul", models.CharField(max_length=256)),
                ("tytul_h2", models.CharField(max_length=256)),
                ("opis_internal", models.CharField(max_length=256, blank=True)),
                ("tresc", models.TextField()),
                ("link", models.CharField(max_length=256, blank=True)),
                (
                    "obrazek",
                    models.ImageField(
                        help_text="Gdy tagtech nie ma trenerów - obrazek pójdzie na praw\u0105 stron\u0119. Gdy trenerzy s\u0105 - pójdzie pod tekst topu, niezale\u017cnie od tego jaka si\u0119 wybierze wersja renderowania topu. Sugerowany rozmiar obrazka: ok. 140\xd7106px.",
                        upload_to="highlights/",
                        verbose_name="image",
                        blank=True,
                    ),
                ),
                ("uwagi_internal", models.TextField(blank=True)),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True,
                        null=True,
                        verbose_name="utworzono",
                        db_index=True,
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        auto_now=True,
                        null=True,
                        verbose_name="aktualizowano",
                        db_index=True,
                    ),
                ),
                (
                    "base_translation",
                    models.ForeignKey(
                        limit_choices_to={"language__exact": "pl"},
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="translation_set",
                        blank=True,
                        to="www.Highlight",
                        help_text="Tu wybieramy odpowiednik obiektu w j\u0119zyku polskim.",
                        null=True,
                        verbose_name="obiekt bazowy",
                    ),
                ),
            ],
            options={
                "ordering": ["tytul"],
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="HighlightPlacement",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("object_id", models.PositiveIntegerField()),
                ("initially_selected", models.BooleanField(default=False)),
                ("ordering", models.IntegerField(null=True, blank=True)),
                (
                    "content_type",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="contenttypes.ContentType",
                    ),
                ),
                (
                    "highlight",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="www.Highlight"
                    ),
                ),
            ],
            options={
                "ordering": ["ordering"],
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="Logo",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("nazwa", models.CharField(max_length=250)),
                ("plik", models.CharField(max_length=250)),
            ],
            options={
                "ordering": ["nazwa"],
                "verbose_name": "logo",
                "verbose_name_plural": "loga",
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="Lokalizacja",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("shortname", models.CharField(max_length=32)),
                ("fullname", models.CharField(max_length=256)),
                (
                    "fullname_miejscownik",
                    models.CharField(
                        help_text='Nazwa lokalizacji (miasta) w miejscowniku. Wykorzystywane w formie "... odb\u0119dzie si\u0119 w Warszawie"',
                        max_length=256,
                        blank=True,
                    ),
                ),
                (
                    "instrukcja_dotarcia",
                    models.CharField(
                        help_text="Krótka informacja, jak odnale\u017a\u0107 siedzib\u0119 lub sal\u0119 - wykorzystywane w automatycznych powiadomieniach o starcie kursu.",
                        max_length=256,
                        blank=True,
                    ),
                ),
                ("numer", models.IntegerField()),
                (
                    "reklamowana",
                    models.BooleanField(
                        default=False,
                        help_text="np. w najbli\u017cszych terminach. Determinuje tak\u017ce wy\u015bwietlanie danej lokalizacji w zapisach na powiadomienia.",
                    ),
                ),
                (
                    "calendar",
                    models.CharField(
                        help_text="sam calendar ID, nie URL", max_length=256, blank=True
                    ),
                ),
                (
                    "domyslna_cena_obiadu",
                    models.DecimalField(
                        help_text="<b>Uwaga</b>: Cen\u0119 podajemy w walucie, w której odbywaj\u0105 si\u0119 szkolenia w danej lokalizacji.Je\u015bli w jednej lokalizacji mog\u0105 by\u0107 szkolenia w wielu walutach, nale\u017cy zg\u0142osi\u0107 to programistom.",
                        null=True,
                        max_digits=30,
                        decimal_places=2,
                        blank=True,
                    ),
                ),
                (
                    "ulica_i_numer",
                    models.CharField(
                        help_text="np. <i>ul. Jasna 14/16A</i>",
                        max_length=100,
                        blank=True,
                    ),
                ),
                (
                    "panstwo",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        verbose_name="pa\u0144stwo",
                        to="i18n.Panstwo",
                    ),
                ),
            ],
            options={
                "ordering": ["numer"],
                "verbose_name": "lokalizacja",
                "verbose_name_plural": "lokalizacje",
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="MenuItem",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                (
                    "language",
                    models.CharField(
                        default="pl",
                        max_length=2,
                        verbose_name="j\u0119zyk",
                        choices=[("pl", "polski"), ("en", "angielski")],
                    ),
                ),
                ("title", models.CharField(max_length=100)),
                ("ordering", models.IntegerField()),
                ("enabled", models.BooleanField(default=True)),
                ("indent_level", models.IntegerField(default=0)),
                ("link_url", models.CharField(max_length=200, null=True, blank=True)),
                ("separator_above", models.BooleanField(default=False)),
            ],
            options={
                "ordering": ["ordering"],
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="MyFlatPage",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                (
                    "language",
                    models.CharField(
                        default="pl",
                        max_length=2,
                        verbose_name="j\u0119zyk",
                        choices=[("pl", "polski"), ("en", "angielski")],
                    ),
                ),
                ("header_title", models.CharField(max_length=200)),
                ("h1_title", models.CharField(max_length=200, blank=True)),
                ("slug", models.CharField(max_length=50, blank=True)),
                ("header_kwd", models.TextField(null=True, blank=True)),
                ("header_descr", models.TextField(null=True, blank=True)),
                ("ordering", models.IntegerField(null=True, blank=True)),
                ("enabled", models.BooleanField(default=True)),
                ("lead", models.TextField(null=True, blank=True)),
                ("content", models.TextField(null=True, blank=True)),
                (
                    "internal_comments",
                    models.TextField(verbose_name="uwagi internal", blank=True),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True,
                        null=True,
                        verbose_name="utworzono",
                        db_index=True,
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        auto_now=True, verbose_name="aktualizowano", null=True
                    ),
                ),
                (
                    "base_translation",
                    models.ForeignKey(
                        limit_choices_to={"language__exact": "pl"},
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="translation_set",
                        blank=True,
                        to="www.MyFlatPage",
                        help_text="Tu wybieramy odpowiednik obiektu w j\u0119zyku polskim.",
                        null=True,
                        verbose_name="obiekt bazowy",
                    ),
                ),
                (
                    "content_group",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        verbose_name="grupa tre\u015bci",
                        blank=True,
                        to="www.ContentGroup",
                        null=True,
                    ),
                ),
                (
                    "domena",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        blank=True,
                        to="www.Domena",
                        null=True,
                    ),
                ),
                (
                    "logo",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        blank=True,
                        to="www.Logo",
                        null=True,
                    ),
                ),
            ],
            options={
                "ordering": ["ordering"],
                "verbose_name_plural": "Strony statyczne (my flat pages)",
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="PotencjalnyChetny",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                (
                    "imie_nazwisko",
                    models.CharField(
                        max_length=100, verbose_name="Imi\u0119 i nazwisko", blank=True
                    ),
                ),
                (
                    "data_ostatniego_kontaktu",
                    models.DateField(
                        help_text="jak zostawisz puste, ustawi si\u0119 na dzisiaj",
                        blank=True,
                    ),
                ),
                ("email", models.EmailField(max_length=75, blank=True)),
                ("telefon", models.CharField(max_length=100, blank=True)),
                ("firma", models.CharField(max_length=100, blank=True)),
                ("uwagi", models.TextField(blank=True)),
                (
                    "stan_sprawy",
                    models.CharField(
                        default="aktualne",
                        max_length=100,
                        choices=[
                            ("aktualne", "zg\u0142oszenie aktualne"),
                            ("zapisal sie", "rozwi\u0105zany - zapisa\u0142 si\u0119"),
                            ("zrezygnowal", "zamkniety - zrezygnowa\u0142"),
                            (
                                "zrezygnowalismy",
                                "zamkni\u0119ty - my zrezygnowali\u015bmy - zbyt dawno",
                            ),
                        ],
                    ),
                ),
            ],
            options={
                "verbose_name": "potencjalny ch\u0119tny",
                "verbose_name_plural": "potencjalni ch\u0119tni",
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="Prowadzacy",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("imie", models.CharField(max_length=50)),
                ("nazwisko", models.CharField(max_length=50)),
                (
                    "calendar",
                    models.CharField(
                        blank=True,
                        help_text="sam calendar ID, nie URL",
                        max_length=256,
                        validators=[www.validators.GoogleCalendarIDValidator()],
                    ),
                ),
                ("komentarz", models.TextField(blank=True)),
                (
                    "fotka",
                    models.ImageField(
                        storage=django.core.files.storage.FileSystemStorage(
                            base_url="/uploads/"
                        ),
                        upload_to="prowadzacy",
                        blank=True,
                    ),
                ),
                ("sylwetka", models.TextField(blank=True)),
                (
                    "pokazywac",
                    models.BooleanField(
                        default=False,
                        help_text='Decyduje o pokazywaniu na stronie "Wyk\u0142adowcy". UWAGA: przy e-certyfikatach opis trenera b\u0119dzie pokazywany nawet bez w\u0142\u0105czania tej opcji.',
                        verbose_name="pokazywa\u0107",
                    ),
                ),
                ("ordering", models.IntegerField(null=True, blank=True)),
            ],
            options={
                "ordering": ["nazwisko", "imie"],
                "verbose_name": "prowadz\u0105cy",
                "verbose_name_plural": "prowadz\u0105cy",
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="Referencja",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                (
                    "typ",
                    models.CharField(
                        max_length=256,
                        choices=[
                            ("laurka ogolna", "laurka ogólna"),
                            ("laurka ze szczegolami", "laurka ze szczegó\u0142ami"),
                            ("formalny protokol", "formalny protokó\u0142"),
                            ("w trakcie", "w trakcie za\u0142atwiania"),
                            ("czeka na odpowiedz", "czeka na odpowied\u017a"),
                            ("niezalatwialne", "nieza\u0142atwialne"),
                        ],
                    ),
                ),
                (
                    "plik",
                    models.FileField(
                        storage=django.core.files.storage.FileSystemStorage(
                            base_url="/uploads/"
                        ),
                        null=True,
                        upload_to="referencje",
                        blank=True,
                    ),
                ),
                ("nazwa", models.TextField(blank=True)),
                ("uwagi", models.TextField(blank=True)),
                ("czas_dodania_pliku", models.DateTimeField(null=True, blank=True)),
            ],
            options={
                "verbose_name": "referencja",
                "verbose_name_plural": "referencje",
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="Sala",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("nazwa", models.TextField()),
                (
                    "calendar",
                    models.CharField(
                        blank=True,
                        help_text="sam calendar ID, nie URL",
                        max_length=256,
                        validators=[www.validators.GoogleCalendarIDValidator()],
                    ),
                ),
                ("sprawdzaj_konflikty", models.BooleanField(default=True)),
                (
                    "lokalizacja",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="www.Lokalizacja",
                    ),
                ),
            ],
            options={
                "ordering": ["lokalizacja", "nazwa"],
                "verbose_name_plural": "Sale",
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="Sciezka",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                (
                    "language",
                    models.CharField(
                        default="pl",
                        max_length=2,
                        verbose_name="j\u0119zyk",
                        choices=[("pl", "polski"), ("en", "angielski")],
                    ),
                ),
                ("nazwa", models.CharField(max_length=200)),
                ("slug", models.CharField(max_length=50, blank=True)),
                ("ordering", models.IntegerField(null=True, blank=True)),
                ("opis", models.TextField(blank=True)),
                ("dlugi_opis", models.TextField(blank=True)),
                (
                    "obrazek_height",
                    models.IntegerField(
                        help_text="w pikselach, default if not set=200",
                        null=True,
                        blank=True,
                    ),
                ),
                (
                    "base_translation",
                    models.ForeignKey(
                        limit_choices_to={"language__exact": "pl"},
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="translation_set",
                        blank=True,
                        to="www.Sciezka",
                        help_text="Tu wybieramy odpowiednik obiektu w j\u0119zyku polskim.",
                        null=True,
                        verbose_name="obiekt bazowy",
                    ),
                ),
            ],
            options={
                "ordering": ["ordering"],
                "verbose_name": "\u015bcie\u017cka",
                "verbose_name_plural": "\u015bcie\u017cki",
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="SiteModule",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                (
                    "language",
                    models.CharField(
                        default="pl",
                        max_length=2,
                        verbose_name="j\u0119zyk",
                        choices=[("pl", "polski"), ("en", "angielski")],
                    ),
                ),
                (
                    "title",
                    models.CharField(
                        help_text="W przypadku pustej warto\u015bci, zostanie ona pobrana z nadpisywanego obiektu.",
                        max_length=100,
                        blank=True,
                    ),
                ),
                (
                    "ordering",
                    models.IntegerField(
                        help_text="W przypadku pustej warto\u015bci, zostanie ona pobrana z nadpisywanego obiektu.",
                        null=True,
                        blank=True,
                    ),
                ),
                (
                    "enabled",
                    models.NullBooleanField(
                        help_text="W przypadku pustej warto\u015bci, zostanie ona pobrana z nadpisywanego obiektu."
                    ),
                ),
                (
                    "pages",
                    models.IntegerField(
                        default=1,
                        help_text="Czy ta opcja jest jeszcze u\u017cywana, mo\u017cna j\u0105 wywali\u0107?",
                        choices=[(1, "all"), (2, "main"), (3, "not main")],
                    ),
                ),
                (
                    "position",
                    models.IntegerField(
                        default=1,
                        help_text="Czy ta opcja jest jeszcze u\u017cywana, mo\u017cna j\u0105 wywali\u0107?",
                        choices=[(1, "left"), (2, "right")],
                    ),
                ),
                (
                    "content",
                    models.TextField(
                        help_text="W przypadku pustej warto\u015bci, zostanie ona pobrana z nadpisywanego obiektu.",
                        blank=True,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True,
                        null=True,
                        verbose_name="utworzono",
                        db_index=True,
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        auto_now=True,
                        null=True,
                        verbose_name="aktualizowano",
                        db_index=True,
                    ),
                ),
                (
                    "content_group",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        blank=True,
                        to="www.ContentGroup",
                        help_text="Je\u017celi nie ustawiona, modu\u0142 b\u0119dzie wy\u015bwietlany wsz\u0119dzie.",
                        null=True,
                        verbose_name="grupa tre\u015bci",
                    ),
                ),
                (
                    "overrides",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        blank=True,
                        to="www.SiteModule",
                        help_text="Nadpisywany modu\u0142 (je\u017celi modu\u0142 nadpisuj\u0105cy przynale\u017cy do grupy tre\u015bci). Z nadpisywanego modu\u0142u b\u0119d\u0105 pobierane warto\u015bci dla j\u0119zyka i dla pustych pól.",
                        null=True,
                        verbose_name="nadpisuje",
                        limit_choices_to={"overrides": None},
                    ),
                ),
            ],
            options={
                "ordering": ["ordering"],
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="Szkolenie",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                (
                    "language",
                    models.CharField(
                        default="pl",
                        max_length=2,
                        verbose_name="j\u0119zyk",
                        choices=[("pl", "polski"), ("en", "angielski")],
                    ),
                ),
                ("nazwa", models.CharField(max_length=200)),
                (
                    "kod",
                    models.CharField(
                        help_text="powinien by\u0107 biznesowo brzmi\u0105cy i krótki",
                        max_length=50,
                    ),
                ),
                (
                    "slug",
                    models.SlugField(
                        help_text="nazwa widoczna w URL-ach. Je\u015bli zostawisz puste, zostanie wygenerowana automatycznie jako nazwa szkolenia bez polskich liter ze spacjami zamienionymi na my\u015blniki",
                        max_length=100,
                        blank=True,
                    ),
                ),
                (
                    "strona_z_opisem_slug",
                    models.CharField(
                        help_text="u\u017cywane dla kursów zawodowych; wype\u0142nienie tego pola spowoduje, \u017ce zamiast domy\u015blnej strony szkolenia pojawia si\u0119 wskazana slugiem strona typu my flatpage.",
                        max_length=100,
                        null=True,
                        blank=True,
                    ),
                ),
                (
                    "ordering",
                    models.IntegerField(
                        help_text="warto\u015b\u0107 tego pola decyduje o kolejno\u015bci na li\u015bcie szkole\u0144. Prosz\u0119: NIE zostawiaj tego pola pustego!",
                        null=True,
                        blank=True,
                    ),
                ),
                (
                    "aktywne",
                    models.BooleanField(
                        default=True, help_text="czyli opublikowane (na stronie)."
                    ),
                ),
                (
                    "jest_czescia_kursu_zawodowego",
                    models.NullBooleanField(
                        help_text="A zatem mo\u017ce odbywa\u0107 si\u0119 równocze\u015bnie z kursem w jednej sali, i nie b\u0119dzie stanowi\u0107 to konfliktu terminów.",
                        verbose_name="jest cz\u0119\u015bci\u0105 kursu zawodowego",
                    ),
                ),
                (
                    "header_title",
                    models.CharField(max_length=200, null=True, blank=True),
                ),
                ("header_kwd", models.TextField(null=True, blank=True)),
                ("header_descr", models.TextField(null=True, blank=True)),
                (
                    "opis",
                    models.TextField(
                        help_text="najbardziej skrócony - u\u017cywany do tabeli pe\u0142na lista szkole\u0144 i do tabeli przy przegl\u0105daniu po grupach/technologiach. Mo\u017ce (i zwykle nawet powinien) by\u0107 krótszy ni\u017c opis g\u0142ówny."
                    ),
                ),
                (
                    "czas_dni",
                    models.IntegerField(
                        help_text="domy\u015blnie nale\u017cy wpisywa\u0107 tylko dni, chyba \u017ce (a) kurs zawodowy => wtedy podajemy tylko godziny, lub (b) szkolenie o niestandardowych d\u0142ugo\u015bciach dni - wtedy mo\u017cemy poda\u0107 i liczb\u0119 dni, i liczb\u0119 godzin.",
                        null=True,
                        blank=True,
                    ),
                ),
                ("czas_godziny", models.IntegerField(null=True, blank=True)),
                (
                    "materialy_plik",
                    models.FileField(
                        upload_to=www.utils.get_upload_path_for_trainings_pdf,
                        storage=www.models.SzkolenieFileSystemStorage(),
                        max_length=250,
                        blank=True,
                        null=True,
                        verbose_name="Plik z materia\u0142ami",
                    ),
                ),
                (
                    "program_szkolenia",
                    models.TextField(
                        help_text="Program szkolenia (potrzebny do e-certyfikatu)",
                        blank=True,
                    ),
                ),
                (
                    "szablon_certyfikatu",
                    models.TextField(
                        help_text="Pole mo\u017ce zawiera\u0107 elementy szablonów Django. Do szablonu przekazywane s\u0105 zmienne: {{ uczestnik }}, {{ nazwa_szkolenia }}, {{ data_szkolenia }}, {{ program_szkolenia }}, {{ miejscowosc }}, {{ liczba_dni }}",
                        blank=True,
                        verbose_name="Szablon e-certyfikatu",
                        validators=[www.validators.DjangoTemplateValidator()],
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True,
                        null=True,
                        verbose_name="utworzono",
                        db_index=True,
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        auto_now=True,
                        null=True,
                        verbose_name="aktualizowano",
                        db_index=True,
                    ),
                ),
                (
                    "cena_bazowa",
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=30,
                        blank=True,
                        help_text="W przypadku jej braku wy\u015bwietlana cena zostanie obliczona na podstawie ceny szkolenia bazowego (dotyczy t\u0142umacze\u0144).",
                        null=True,
                        verbose_name="price",
                    ),
                ),
                (
                    "zawiera_obiady",
                    models.BooleanField(
                        default=True,
                        help_text="generalnie: w wypadku szkole\u0144 mówimy, \u017ce TAK, w wypadku kursów zawodowych NIE.",
                    ),
                ),
                (
                    "opis_egzaminu",
                    models.TextField(
                        help_text="Wy\u015bwietlany na stronie szkolenia po opisie autoryzacji. Mo\u017cna u\u017cywa\u0107 znaczników HTML.",
                        verbose_name="exam description",
                        blank=True,
                    ),
                ),
                (
                    "cena_autoryzacji_bazowa",
                    models.IntegerField(
                        help_text="W przypadku jej braku wy\u015bwietlana cena zostanie obliczona na podstawie ceny szkolenia bazowego (dotyczy t\u0142umacze\u0144).",
                        null=True,
                        verbose_name="authorization price",
                        blank=True,
                    ),
                ),
                (
                    "nazwa_autoryzacji",
                    models.CharField(
                        max_length=200, verbose_name="authorization name", blank=True
                    ),
                ),
                (
                    "kod_autoryzacji",
                    models.CharField(
                        max_length=50, verbose_name="authorization code", blank=True
                    ),
                ),
                (
                    "min_grupa_bazowa",
                    models.IntegerField(
                        default=2,
                        help_text="informacja oficjalna od ilu osob odpalamy grupy zamkni\u0119te. default pisa\u0107: 2. W przypadku pustej warto\u015bci zostanie ona pobrana z obiektu bazowego.",
                        null=True,
                        verbose_name="minimalna grupa",
                        blank=True,
                    ),
                ),
                (
                    "cel",
                    models.TextField(
                        help_text='To jest tak naprawd\u0119 opis g\u0142ówny, cel i opis szkolenia. Ten tekst jest widoczny na samej górze na stronie szczegó\u0142ów szkolenia - i w PDF-ie. [dozwolony markup: &lt;b&gt;...&lt;/b&gt;, &lt;i&gt;...&lt;/i&gt;, &lt;a href="..."&gt;...&lt;/a&gt;, &lt;img&gt; (wycinane w PDF), listy w stylu textile: *, #, #*, #**].<br>PS. u\u017cywaj\u0105c znaczników &lt!--pdfignore--&gt i &lt!--endpdfignore--&gt  mo\u017cna wy\u0142\u0105czy\u0107 dane fragmenty (np. css) z wersji PDF.<br>Uwaga: Je\u015bli pole zawiera tag &ltstyle&gt sk\u0142adnia textile *nie* jest interpretowana.'
                    ),
                ),
                (
                    "przeznaczony_dla",
                    models.TextField(
                        help_text='[dozwolony markup: &lt;b&gt;...&lt;/b&gt;, &lt;i&gt;...&lt;/i&gt;, &lt;a href="..."&gt;...&lt;/a&gt;, &lt;img&gt; (wycinane w PDF), listy w stylu textile: *, #, #*, #**].<br>PS. u\u017cywaj\u0105c znaczników &lt!--pdfignore--&gt i &lt!--endpdfignore--&gt  mo\u017cna wy\u0142\u0105czy\u0107 dane fragmenty (np. css) z wersji PDF.',
                        null=True,
                        blank=True,
                    ),
                ),
                (
                    "program",
                    models.TextField(
                        help_text='[dozwolony markup: &lt;b&gt;...&lt;/b&gt;, &lt;i&gt;...&lt;/i&gt;, &lt;a href="..."&gt;...&lt;/a&gt;, &lt;img&gt; (wycinane w PDF), listy w stylu textile: *, #, #*, #**].<br>PS. u\u017cywaj\u0105c znaczników &lt!--pdfignore--&gt i &lt!--endpdfignore--&gt  mo\u017cna wy\u0142\u0105czy\u0107 dane fragmenty (np. css) z wersji PDF.'
                    ),
                ),
                (
                    "wymagania",
                    models.TextField(
                        help_text='w razie niepewno\u015bci / braku weny twórczej mo\u017cna wype\u0142ni\u0107 same wymagania tzn. bez przeznaczony dla. [dozwolony markup: &lt;b&gt;...&lt;/b&gt;, &lt;i&gt;...&lt;/i&gt;, &lt;a href="..."&gt;...&lt;/a&gt;, &lt;img&gt; (wycinane w PDF), listy w stylu textile: *, #, #*, #**].<br>PS. u\u017cywaj\u0105c znaczników &lt!--pdfignore--&gt i &lt!--endpdfignore--&gt  mo\u017cna wy\u0142\u0105czy\u0107 dane fragmenty (np. css) z wersji PDF.',
                        null=True,
                        blank=True,
                    ),
                ),
                ("forma_zajec", models.TextField(null=True, blank=True)),
                ("nabyte_umiejetnosci", models.TextField(null=True, blank=True)),
                ("certyfikaty", models.TextField(null=True, blank=True)),
                ("lokalizacje", models.TextField(null=True, blank=True)),
                ("uwagi", models.TextField(null=True, blank=True)),
                (
                    "harmonogram",
                    models.TextField(
                        help_text="aktualnie nieu\u017cywane.", null=True, blank=True
                    ),
                ),
                (
                    "powinno_miec_termin",
                    models.BooleanField(
                        default=False,
                        help_text="flaga mówi\u0105ca biuru, \u017ce szkolenie zawsze powinno mie\u0107 co najmniej jeden termin w przysz\u0142o\u015bci, oraz skryptom pilnuj\u0105cym, aby tego pilnowa\u0107 i w razie potrzeby przypomina\u0107.",
                        verbose_name="powinno mie\u0107 termin (WAW)",
                    ),
                ),
                (
                    "powinno_miec_termin_krk",
                    models.BooleanField(
                        default=False,
                        help_text="flaga mówi\u0105ca biuru, \u017ce szkolenie zawsze powinno mie\u0107 co najmniej jeden termin w przysz\u0142o\u015bci, oraz skryptom pilnuj\u0105cym, aby tego pilnowa\u0107 i w razie potrzeby przypomina\u0107.",
                        verbose_name="powinno mie\u0107 termin (KRK)",
                    ),
                ),
                ("uwagi_internal", models.TextField(blank=True)),
                (
                    "stawka_vat",
                    models.DecimalField(
                        default=Decimal("0.2300"),
                        help_text="Do wyrzucenia, nie u\u017cywa\u0107.",
                        max_digits=30,
                        decimal_places=4,
                        choices=[(Decimal("0.0000"), "0%"), (Decimal("0.2300"), "23%")],
                    ),
                ),
                (
                    "cena_przed_promocja_bazowa",
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=30,
                        blank=True,
                        help_text="W przypadku jej braku wy\u015bwietlana cena zostanie obliczona na podstawie ceny szkolenia bazowego (dotyczy t\u0142umacze\u0144).",
                        null=True,
                        verbose_name="cena przed promocj\u0105",
                    ),
                ),
                (
                    "cena_w_grupie_2_os_bazowa",
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=30,
                        blank=True,
                        help_text="W przypadku jej braku wy\u015bwietlana cena zostanie obliczona na podstawie ceny szkolenia bazowego (dotyczy t\u0142umacze\u0144).",
                        null=True,
                        verbose_name="cena w grupie 2-os.",
                    ),
                ),
                (
                    "cena_indywidualnie_bazowa",
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=30,
                        blank=True,
                        help_text="W przypadku jej braku wy\u015bwietlana cena zostanie obliczona na podstawie ceny szkolenia bazowego (dotyczy t\u0142umacze\u0144).",
                        null=True,
                        verbose_name="cena indywidualnie",
                    ),
                ),
                (
                    "godzin_w_trybie_indywidualnym",
                    models.IntegerField(null=True, blank=True),
                ),
                ("zawartosc_certyfikatu", models.TextField(blank=True)),
                (
                    "wysylaj_powiadomienia_proformy",
                    models.BooleanField(
                        default=True,
                        help_text="normalnie wysy\u0142ane s\u0105 powiadomienia i proformy, ale np. dla egzaminów nie",
                    ),
                ),
                (
                    "akredytacje",
                    models.ManyToManyField(
                        related_name="akredytacja_set",
                        db_table="www_akredytacja_szkolenia",
                        to="www.Lokalizacja",
                        blank=True,
                    ),
                ),
                (
                    "autoryzacja",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        verbose_name="authorization",
                        blank=True,
                        to="www.Autoryzacja",
                        null=True,
                    ),
                ),
                (
                    "base_translation",
                    models.ForeignKey(
                        limit_choices_to={"language__exact": "pl"},
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="translation_set",
                        blank=True,
                        to="www.Szkolenie",
                        help_text="Tu wybieramy odpowiednik obiektu w j\u0119zyku polskim.",
                        null=True,
                        verbose_name="obiekt bazowy",
                    ),
                ),
                (
                    "domena",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        blank=True,
                        to="www.Domena",
                        null=True,
                    ),
                ),
                (
                    "grupa_zaszeregowania",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        blank=True,
                        to="www.GrupaZaszeregowania",
                        null=True,
                    ),
                ),
                (
                    "logo",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        blank=True,
                        to="www.Logo",
                        null=True,
                    ),
                ),
                (
                    "lokalizacje_powinne_miec_terminy",
                    models.ManyToManyField(
                        to="www.Lokalizacja",
                        verbose_name="lokalizacje - powinny mie\u0107 terminy",
                        blank=True,
                    ),
                ),
                (
                    "prowadzacy",
                    models.ManyToManyField(
                        related_name="szkolenie_set", to="www.Prowadzacy", blank=True
                    ),
                ),
                ("sciezki", models.ManyToManyField(to="www.Sciezka", blank=True)),
            ],
            options={
                "ordering": ["ordering"],
                "verbose_name_plural": "Szkolenia",
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="Tab",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("slug", models.CharField(max_length=50, blank=True)),
                ("title", models.CharField(max_length=200)),
                ("content", models.TextField(blank=True)),
                ("ordering", models.IntegerField(null=True, blank=True)),
                (
                    "flatpage",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT, to="www.MyFlatPage"
                    ),
                ),
            ],
            options={
                "ordering": ["ordering"],
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="TagDlugosc",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("nazwa", models.CharField(max_length=50)),
                ("nazwa_en", models.CharField(max_length=50)),
                ("slug", models.CharField(unique=True, max_length=50, blank=True)),
                ("ordering", models.IntegerField(null=True, blank=True)),
                ("opis", models.TextField(null=True, blank=True)),
            ],
            options={
                "ordering": ["ordering"],
                "verbose_name_plural": "Tagi - d\u0142ugo\u015b\u0107",
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="TagTechnologia",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                (
                    "language",
                    models.CharField(
                        default="pl",
                        max_length=2,
                        verbose_name="j\u0119zyk",
                        choices=[("pl", "polski"), ("en", "angielski")],
                    ),
                ),
                ("nazwa", models.CharField(max_length=50)),
                ("slug", models.CharField(max_length=50, blank=True)),
                ("ordering", models.IntegerField(null=True, blank=True)),
                (
                    "kursy_po_szkoleniach",
                    models.BooleanField(
                        default=False, verbose_name="listowa\u0107 kursy po szkoleniach"
                    ),
                ),
                (
                    "opis",
                    models.TextField(
                        help_text="Pojawi si\u0119 na stronie z list\u0105 szkole\u0144 w tej technologii. HTML wypisywany \u017cywcem bez \u017cadnego filtrowania.",
                        null=True,
                        blank=True,
                    ),
                ),
                ("domena_redirect", models.BooleanField(default=False)),
                ("meta_title", models.CharField(max_length=100, blank=True)),
                ("meta_description", models.TextField(blank=True)),
                ("meta_keywords", models.TextField(blank=True)),
                ("widoczny_publicznie", models.BooleanField(default=True)),
                ("uwagi_internal", models.TextField(blank=True)),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True,
                        null=True,
                        verbose_name="utworzono",
                        db_index=True,
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        auto_now=True, verbose_name="aktualizowano", null=True
                    ),
                ),
                (
                    "base_translation",
                    models.ForeignKey(
                        limit_choices_to={"language__exact": "pl"},
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="translation_set",
                        blank=True,
                        to="www.TagTechnologia",
                        help_text="Tu wybieramy odpowiednik obiektu w j\u0119zyku polskim.",
                        null=True,
                        verbose_name="obiekt bazowy",
                    ),
                ),
                (
                    "content_group",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        verbose_name="grupa tre\u015bci",
                        blank=True,
                        to="www.ContentGroup",
                        null=True,
                    ),
                ),
                (
                    "domena",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        null=True,
                        blank=True,
                        to="www.Domena",
                        unique=True,
                    ),
                ),
                (
                    "domena_slash_tech",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="tagtechnologia_slash_set",
                        blank=True,
                        to="www.Domena",
                        null=True,
                    ),
                ),
                (
                    "logo",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        blank=True,
                        to="www.Logo",
                        null=True,
                    ),
                ),
                ("sciezki", models.ManyToManyField(to="www.Sciezka", blank=True)),
            ],
            options={
                "ordering": ["ordering"],
                "verbose_name_plural": "Tagi - technologia",
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="TagZawod",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                (
                    "language",
                    models.CharField(
                        default="pl",
                        max_length=2,
                        verbose_name="j\u0119zyk",
                        choices=[("pl", "polski"), ("en", "angielski")],
                    ),
                ),
                ("nazwa", models.CharField(max_length=50)),
                ("slug", models.CharField(max_length=50, blank=True)),
                ("ordering", models.IntegerField(null=True, blank=True)),
                ("opis", models.TextField(null=True, blank=True)),
                ("widoczny_publicznie", models.BooleanField(default=True)),
                (
                    "base_translation",
                    models.ForeignKey(
                        limit_choices_to={"language__exact": "pl"},
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="translation_set",
                        blank=True,
                        to="www.TagZawod",
                        help_text="Tu wybieramy odpowiednik obiektu w j\u0119zyku polskim.",
                        null=True,
                        verbose_name="obiekt bazowy",
                    ),
                ),
            ],
            options={
                "ordering": ["ordering"],
                "verbose_name_plural": "Tagi - zawód",
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="TekstOTerminach",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("tekst", models.TextField()),
                (
                    "lokalizacja",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="www.Lokalizacja",
                    ),
                ),
                (
                    "szkolenie",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT, to="www.Szkolenie"
                    ),
                ),
            ],
            options={},
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="TerminSzkolenia",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("termin", models.DateField()),
                (
                    "termin_zakonczenia",
                    models.DateField(
                        help_text="wpisywa\u0107 dla kursów zawodowych oraz celem nadpisania standardowej d\u0142ugo\u015bci szkolenia (np. dla konsultacji indywidualnych i wynajmów sal)",
                        null=True,
                        blank=True,
                    ),
                ),
                (
                    "gwarantowany",
                    models.BooleanField(
                        default=False,
                        help_text="zaznaczy\u0107, aby powiedzie\u0107, \u017ce wiadomo ju\u017c \u017ce na pewno si\u0119 odb\u0119dzie. W praktyce ze wzgl\u0119dów marketingowych NIE u\u017cywamy (mówienie, \u017ce to si\u0119 odb\u0119dzie sugeruje, \u017ce inne nie...)",
                    ),
                ),
                (
                    "tryb",
                    models.IntegerField(
                        default=1,
                        choices=[
                            (1, "Day classes"),
                            (2, "Evening classes"),
                            (3, "Weekend classes"),
                        ],
                    ),
                ),
                (
                    "ile_dni",
                    models.IntegerField(
                        help_text="wymagane dla kursów zawodowych je\u015bli obiady s\u0105 opcjonalne",
                        null=True,
                        blank=True,
                    ),
                ),
                (
                    "opis",
                    models.CharField(
                        help_text="najkrótszy dopisek, wsz\u0119dzie przy terminach szkolenia; u\u017cywa\u0107 przy kursach wieczorowych \u017ceby poda\u0107 dni tygodnia. Tu NIE uzywac nawiasow - wstawiaja sie same.",
                        max_length=200,
                        null=True,
                        verbose_name="parametry trybu",
                        blank=True,
                    ),
                ),
                (
                    "dodatkowe_uwagi",
                    models.TextField(
                        help_text="d\u0142u\u017cszy dopisek, l\u0105duj\u0105cy na stron\u0119 g\u0142ówn\u0105 przy terminach kursów; mo\u017cna u\u017cywa\u0107 przy wszystkich kursach, modelowo innych ni\u017c wieczorowe, \u017ceby poda\u0107 tryb zaj\u0119\u0107 opisowo; tutaj wpisujemy te\u017c uwagi w rodzaju [DATA]: ZOSTA\u0141Y TYLKO DWA WOLNE MIEJSCA. Mo\u017cna u\u017cywa\u0107 znaczników HTML. Tutaj jesli chcemy nawiasy na stronie, to trzeba je recznie podac. Je\u015bli w tym tek\u015bcie jest napis '[[noappend]]', to nie doklei si\u0119 tekst 'n wolnych miejsc'.",
                        null=True,
                        verbose_name="uwagi www",
                        blank=True,
                    ),
                ),
                (
                    "odbylo_sie",
                    models.NullBooleanField(default=None, verbose_name="czy robimy"),
                ),
                (
                    "czy_reklamowac",
                    models.BooleanField(default=False, verbose_name="czy promowac"),
                ),
                ("zliczacz_task_id", models.IntegerField(null=True, editable=False)),
                ("certyfikaty_przekazane", models.BooleanField(default=False)),
                ("materialy_wydrukowane", models.BooleanField(default=False)),
                ("sala_przygotowana", models.BooleanField(default=False)),
                ("materialy_uwagi", models.TextField(blank=True)),
                (
                    "zamkniete",
                    models.BooleanField(
                        default=False,
                        help_text="zaznacz, je\u017celi: (a) termin zosta\u0142 odwo\u0142any, lub (b) szkolenie prawdziwie zamkni\u0119te tj. dost\u0119pne tylko dla ludzi z firmy, która je zamówi\u0142a",
                        verbose_name="nie publikuj",
                    ),
                ),
                ("uwagi", models.TextField(verbose_name="uwagi internal", blank=True)),
                (
                    "harmonogram",
                    models.TextField(
                        help_text="wpisz, je\u017celi harmonogram jest inny, ni\u017c wynika\u0142oby to z pola daty szczegó\u0142owo",
                        blank=True,
                    ),
                ),
                (
                    "obiady",
                    models.CharField(
                        blank=True,
                        help_text="jak zostawisz puste, to spróbuj\u0119 zgadn\u0105\u0107",
                        max_length=128,
                        choices=[
                            ("obiady-wliczone", "obiady wliczone"),
                            ("obiady-opcjonalne", "obiady opcjonalne"),
                            ("nie-ma-obiadow", "nie ma obiadów"),
                        ],
                    ),
                ),
                (
                    "cena_obiadu",
                    models.DecimalField(
                        help_text="zostaw puste, \u017ceby skopiowa\u0107 domy\u015bln\u0105 cen\u0119 dla lokalizacji",
                        null=True,
                        max_digits=30,
                        decimal_places=2,
                        blank=True,
                    ),
                ),
                (
                    "autoryzacja_aktywna",
                    models.BooleanField(
                        default=False,
                        help_text="Okre\u015bla, czy autoryzacja powinna by\u0107 dost\u0119pna w tym terminie szkolenia (opcja dost\u0119pna tylko, je\u015bli szkolenie ma przypisan\u0105 autoryzacj\u0119).",
                    ),
                ),
                (
                    "snz_opis",
                    models.CharField(
                        help_text="opis dla szkole\u0144 na zamówienie",
                        max_length=128,
                        verbose_name="SnZ opis",
                        blank=True,
                    ),
                ),
                ("daty_szczegolowo", models.TextField(blank=True)),
                (
                    "program_szkolenia",
                    models.TextField(
                        help_text="Je\u015bli pole zostanie puste tekst zostanie pobrany z obiketu szkolenia.",
                        verbose_name="Program szkolenia (potrzebny do e-certyfikatu)",
                        blank=True,
                    ),
                ),
                (
                    "szablon_certyfikatu",
                    models.TextField(
                        help_text="Pole mo\u017ce zawiera\u0107 elementy szablonów Django. Do szablonu przekazywane s\u0105 zmienne: {{ uczestnik }}, {{ nazwa_szkolenia }}, {{ data_szkolenia }}, {{ program_szkolenia }}, {{ miejscowosc }}, {{ liczba_dni }}<br>Je\u015bli pole zostanie puste tekst zostanie pobrany z obiektu szkolenia (je\u015bli istnieje).",
                        blank=True,
                        verbose_name="Szablon e-certyfikatu",
                        validators=[www.validators.DjangoTemplateValidator()],
                    ),
                ),
                (
                    "notyfikacje_o_certyfikatach",
                    models.DateTimeField(null=True, blank=True),
                ),
                (
                    "jobs_state",
                    models.DateTimeField(
                        db_index=True,
                        verbose_name="Potwierdzono",
                        null=True,
                        editable=False,
                        blank=True,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True,
                        null=True,
                        verbose_name="utworzono",
                        db_index=True,
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        auto_now=True,
                        null=True,
                        verbose_name="aktualizowano",
                        db_index=True,
                    ),
                ),
                (
                    "dodatkowi_prowadzacy",
                    models.ManyToManyField(
                        help_text="Wykorzystywani przy generowaniu ankiet",
                        related_name="dodatkowe_terminy_szkolen",
                        verbose_name="dodatkowi prowadz\u0105cy",
                        to="www.Prowadzacy",
                        blank=True,
                    ),
                ),
                (
                    "lokalizacja",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="www.Lokalizacja",
                    ),
                ),
                (
                    "prowadzacy",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        verbose_name="prowadz\u0105cy",
                        blank=True,
                        to="www.Prowadzacy",
                        null=True,
                    ),
                ),
                (
                    "sala",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        blank=True,
                        to="www.Sala",
                        null=True,
                    ),
                ),
                (
                    "szkolenie",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT, to="www.Szkolenie"
                    ),
                ),
            ],
            options={
                "ordering": ["termin", "szkolenie"],
                "verbose_name_plural": "Terminy Szkole\u0144",
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="Testimonial",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                (
                    "content",
                    models.TextField(
                        help_text="HTML tags are allowed.", verbose_name="Content"
                    ),
                ),
                (
                    "obrazek",
                    models.ImageField(
                        storage=django.core.files.storage.FileSystemStorage(
                            base_url="/uploads/"
                        ),
                        null=True,
                        upload_to="testimoniale/",
                        blank=True,
                    ),
                ),
                (
                    "imie_nazwisko",
                    models.CharField(
                        max_length=100, verbose_name="full name", blank=True
                    ),
                ),
                ("firma", models.CharField(max_length=100, blank=True)),
                ("data_testimoniala", models.DateField(null=True, blank=True)),
                ("uwagi_internal", models.TextField(blank=True)),
                (
                    "tagtechnologia",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="www.TagTechnologia",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Testimoniale",
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="Uczestnik",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                (
                    "imie_nazwisko",
                    models.TextField(
                        help_text="or list of participants, if more than one",
                        verbose_name="full name",
                    ),
                ),
                (
                    "osoba_do_kontaktu",
                    models.CharField(
                        help_text="if different than the participant",
                        max_length=100,
                        verbose_name="contact person",
                        blank=True,
                    ),
                ),
                (
                    "prywatny",
                    models.BooleanField(
                        default=False, verbose_name="I am a private person"
                    ),
                ),
                (
                    "chce_fakture",
                    models.BooleanField(
                        default=False,
                        verbose_name="I want to receive a printed invoice",
                    ),
                ),
                (
                    "faktura_firma",
                    models.CharField(
                        max_length=100, verbose_name="company", blank=True
                    ),
                ),
                (
                    "faktura_adres",
                    models.CharField(
                        max_length=100, verbose_name="address", blank=True
                    ),
                ),
                (
                    "faktura_miejscowosc_kod",
                    models.CharField(
                        max_length=100, verbose_name="city, postal code", blank=True
                    ),
                ),
                (
                    "faktura_kraj",
                    models.CharField(
                        default="PL",
                        max_length=2,
                        verbose_name="country",
                        choices=[
                            ("PL", "Polska"),
                            ("GB", "Wielka Brytania"),
                            ("DE", "Niemcy"),
                            ("AF", "Afganistan"),
                            ("AL", "Albania"),
                            ("DZ", "Algeria"),
                            ("AD", "Andora"),
                            ("AO", "Angola"),
                            ("AI", "Anguilla"),
                            ("AQ", "Antarktyda"),
                            ("AG", "Antigua i Barbuda"),
                            ("SA", "Arabia Saudyjska"),
                            ("AR", "Argentyna"),
                            ("AM", "Armenia"),
                            ("AW", "Aruba"),
                            ("AU", "Australia"),
                            ("AT", "Austria"),
                            ("AZ", "Azerbejd\u017can"),
                            ("BS", "Bahamy"),
                            ("BH", "Bahrajn"),
                            ("BD", "Bangladesz"),
                            ("BB", "Barbados"),
                            ("BE", "Belgia"),
                            ("BZ", "Belize"),
                            ("BJ", "Benin"),
                            ("BM", "Bermudy"),
                            ("BT", "Bhutan"),
                            ("BY", "Bia\u0142oru\u015b"),
                            ("BO", "Boliwia"),
                            ("BQ", "Bonaire, Sint Eustatius and Saba"),
                            ("BA", "Bo\u015bnia i Hercegowina"),
                            ("BW", "Botswana"),
                            ("BR", "Brazylia"),
                            ("BN", "Brunei"),
                            ("IO", "Brytyjskie Terytorium Oceanu Indyjskiego"),
                            ("VG", "Brytyjskie Wyspy Dziewicze"),
                            ("VI", "Brytyjskie Wyspy Dziewicze"),
                            ("BG", "Bu\u0142garia"),
                            ("BF", "Burkina Faso"),
                            ("BI", "Burundi"),
                            ("CV", "Cabo Verde"),
                            ("CL", "Chile"),
                            ("CN", "Chiny"),
                            ("HR", "Chorwacja"),
                            ("CW", "Cura\xe7ao"),
                            ("CY", "Cyprs"),
                            ("TD", "Czad"),
                            ("ME", "Czarnogóra"),
                            ("CZ", "Czechy"),
                            ("UM", "Dalekie Wyspy Mniejsze Stanów Zjednoczonych"),
                            ("DK", "Dania"),
                            ("DM", "Dominika"),
                            ("DO", "Dominikana"),
                            ("DJ", "D\u017cibuti"),
                            ("EG", "Egipt"),
                            ("EC", "Ekwador"),
                            ("ER", "Erytrea"),
                            ("EE", "Estonia"),
                            ("ET", "Etiopia"),
                            ("FK", "Falklandy"),
                            ("FJ", "Fid\u017ci"),
                            ("PH", "Filipiny"),
                            ("FI", "Finlandia"),
                            ("FR", "Francja"),
                            (
                                "TF",
                                "Francuskie Terytoria Po\u0142udniowe i Antarktyczne",
                            ),
                            ("GA", "Gabon"),
                            ("GM", "Gambia"),
                            (
                                "GS",
                                "Georgia Po\u0142udniowa i Sandwich Po\u0142udniowy",
                            ),
                            ("GH", "Ghana"),
                            ("GI", "Gibraltar"),
                            ("GR", "Grecja"),
                            ("GD", "Grenada"),
                            ("GL", "Grenlandia"),
                            ("GE", "Gruzja"),
                            ("GU", "Guam"),
                            ("GG", "Guernsey"),
                            ("GY", "Gujana"),
                            ("GF", "Gujana Francuska"),
                            ("GP", "Gwadelupa"),
                            ("GT", "Gwatemala"),
                            ("GN", "Gwinea"),
                            ("GW", "Gwinea Bissau"),
                            ("GQ", "Gwinea Równikowa"),
                            ("HT", "Haiti"),
                            ("ES", "Hiszpania"),
                            ("NL", "Holandia"),
                            ("HN", "Honduras"),
                            ("HK", "Hong Kong"),
                            ("IN", "Indie"),
                            ("ID", "Indonezja"),
                            ("IQ", "Irak"),
                            ("IR", "Iran"),
                            ("IE", "Irlandia"),
                            ("IS", "Islandia"),
                            ("IL", "Izrael"),
                            ("JM", "Jamajka"),
                            ("JP", "Japonia"),
                            ("YE", "Jemen"),
                            ("JE", "Jersey"),
                            ("JO", "Jordania"),
                            ("KY", "Kajmany"),
                            ("KH", "Kambod\u017ca"),
                            ("CM", "Kamerun"),
                            ("CA", "Kanada"),
                            ("QA", "Katar"),
                            ("KZ", "Kazachstan"),
                            ("KE", "Kenia"),
                            ("KG", "Kirgistan"),
                            ("KI", "Kiribati"),
                            ("CO", "Kolumbia"),
                            ("KM", "Komory"),
                            ("CG", "Kongo"),
                            ("KP", "Korea Pó\u0142nocna"),
                            ("KR", "Korea Po\u0142udniowa"),
                            ("CR", "Kostaryka"),
                            ("CU", "Kuba"),
                            ("KW", "Kuwejt"),
                            ("LA", "Laos"),
                            ("LS", "Lesotho"),
                            ("LB", "Liban"),
                            ("LR", "Liberia"),
                            ("LY", "Libia"),
                            ("LI", "Liechtenstein"),
                            ("LT", "Litwa"),
                            ("LU", "Luksembourg"),
                            ("MK", "Macedonia"),
                            ("MG", "Madagaskar"),
                            ("YT", "Majotta"),
                            ("MO", "Makau"),
                            ("MW", "Malawi"),
                            ("MV", "Malediwy"),
                            ("MY", "Malezja"),
                            ("ML", "Mali"),
                            ("MT", "Malta"),
                            ("MP", "Mariany Pó\u0142nocne"),
                            ("MA", "Maroko"),
                            ("MQ", "Martynika"),
                            ("MR", "Mauritania"),
                            ("MU", "Mauritius"),
                            ("MX", "Meksyk"),
                            ("FM", "Mikronezja"),
                            ("MM", "Mjanma"),
                            ("MD", "Mo\u0142dawia"),
                            ("MC", "Monako"),
                            ("MN", "Mongolia"),
                            ("MS", "Montserrat"),
                            ("MZ", "Mozambik"),
                            ("NA", "Nambia"),
                            ("NR", "Nauru"),
                            ("NP", "Nepal"),
                            ("NE", "Niger"),
                            ("NG", "Nigeria"),
                            ("NI", "Nikaragua"),
                            ("NU", "Niue"),
                            ("NF", "Norfolk"),
                            ("NO", "Norwegia"),
                            ("NC", "Nowa Kaledonia"),
                            ("NZ", "Nowa Zelandia"),
                            ("OM", "Oman"),
                            ("PK", "Pakistan"),
                            ("PW", "Palau"),
                            ("PS", "Palestine, State of"),
                            ("PA", "Palestyna"),
                            ("PG", "Papua-Nowa Gwinea"),
                            ("PY", "Paragwaj"),
                            ("PE", "Peru"),
                            ("PN", "Pitcairn"),
                            ("PF", "Polinezja Francuska"),
                            ("PR", "Portoryko"),
                            ("PT", "Portugalia"),
                            ("CD", "Republika Kongo"),
                            ("ZA", "Republika Po\u0142udniowej Afryki"),
                            ("CF", "Republika \u015arodkowoafryka\u0144ska"),
                            ("RE", "Reunion"),
                            ("RU", "Rosja"),
                            ("RO", "Rumunia"),
                            ("RW", "Rwanda"),
                            ("EH", "Sahara Zachodnia"),
                            ("BL", "Saint Barth\xe9lemy"),
                            ("KN", "Saint Kitts i Nevis"),
                            ("LC", "Saint Lucia"),
                            ("PM", "Saint Pierre i Miquelon"),
                            ("VC", "Saint Vincent i Grenadyny"),
                            ("MF", "Saint-Martin"),
                            ("SV", "Salwador"),
                            ("WS", "Samoa"),
                            ("AS", "Samoa Ameryka\u0144skie"),
                            ("SM", "San Marino"),
                            ("SN", "Senegal"),
                            ("RS", "Serbia"),
                            ("SC", "Seszele"),
                            ("SL", "Sierra Leone"),
                            ("SG", "Singapur"),
                            ("SX", "Sint Maarten"),
                            ("SO", "Somalia"),
                            ("SK", "S\u0142owacja"),
                            ("SI", "S\u0142owenia"),
                            ("LK", "Sri Lanka"),
                            ("US", "Stany Zjednoczone"),
                            ("SZ", "Suazi"),
                            ("SD", "Sudan"),
                            ("SS", "Sudan Po\u0142udniowy"),
                            ("SR", "Suriname"),
                            ("SJ", "Svalbard i Jan Mayen"),
                            ("SY", "Syria"),
                            ("CH", "Szwajcaria"),
                            ("SE", "Szwecja"),
                            ("TJ", "Tad\u017cykistan"),
                            ("TH", "Tajlandia"),
                            ("TW", "Tajwan"),
                            ("TZ", "Tanzania"),
                            ("TL", "Timor Wschodni"),
                            ("TG", "Togo"),
                            ("TK", "Tokelau"),
                            ("TO", "Tonga"),
                            ("TT", "Trynidad i Tobago"),
                            ("TN", "Tunezja"),
                            ("TR", "Turkcja"),
                            ("TM", "Turkmenistan"),
                            ("TC", "Turks i Caicos"),
                            ("TV", "Tuvalu"),
                            ("UG", "Uganda"),
                            ("UA", "Ukraina"),
                            ("UY", "Urugwaj"),
                            ("UZ", "Uzbekistan"),
                            ("VU", "Vanuatu"),
                            ("WF", "Wallis i Futuna"),
                            ("VA", "Watykan"),
                            ("HU", "W\u0119gry"),
                            ("VE", "Wenezuela"),
                            ("VN", "Wietnam"),
                            ("IT", "W\u0142ochy"),
                            ("CI", "Wybrze\u017ce Ko\u015bci S\u0142oniowej"),
                            ("BV", "Wyspa Bouveta"),
                            ("CX", "Wyspa Bo\u017cego Narodzenia"),
                            ("IM", "Wyspa Man"),
                            (
                                "SH",
                                "Wyspa \u015awi\u0119tej Heleny, Wyspa Wniebowst\u0105pienia i Tristan da Cunha",
                            ),
                            ("AX", "Wyspy Alandzkie"),
                            ("CK", "Wyspy Cooka"),
                            ("HM", "Wyspy Heard i McDonalda"),
                            ("CC", "Wyspy Kokosowe"),
                            ("MH", "Wyspy Marshalla"),
                            ("FO", "Wyspy Owcze"),
                            ("SB", "Wyspy Salomona"),
                            (
                                "ST",
                                "Wyspy \u015awi\u0119tego Tomasza i Ksi\u0105\u017c\u0119ca",
                            ),
                            ("ZM", "Zambia"),
                            ("ZW", "Zimbabwe"),
                            ("AE", "Zjednoczone Emiraty Arabskie"),
                            ("LV", "\u0141otwa"),
                        ],
                    ),
                ),
                (
                    "faktura_nip",
                    models.CharField(
                        max_length=40,
                        verbose_name="VAT identification number",
                        blank=True,
                    ),
                ),
                (
                    "faktura_vat_id",
                    models.CharField(
                        help_text="or Company ID",
                        max_length=40,
                        verbose_name="VAT ID",
                        blank=True,
                    ),
                ),
                (
                    "chce_egzamin",
                    models.BooleanField(default=False, verbose_name="I order an exam"),
                ),
                (
                    "chce_autoryzacje",
                    models.BooleanField(
                        default=False, verbose_name="I order authorization"
                    ),
                ),
                (
                    "ile_obiadow_wegetarianskich",
                    models.IntegerField(
                        null=True,
                        verbose_name="vegetarian lunches for how many of the participants",
                        blank=True,
                    ),
                ),
                (
                    "za_kurs_zaplace",
                    models.IntegerField(
                        default=1,
                        choices=[
                            (1, "now, in full"),
                            (
                                2,
                                "an advance of 200 GBP now, and the remainder of the fee no later than 7 days before the start of the course",
                            ),
                            (
                                3,
                                "(only private client) an advance of 200 GBP now and the remainder in two equal instalments",
                            ),
                            (
                                10,
                                "(tylko klient indywidualny) wp\u0142acaj\u0105c kwot\u0119 w pi\u0119ciu ratach, zgodnie z <a href='/pl/raty/'>zasadami p\u0142atno\u015bci ratalnej</a>",
                            ),
                            (
                                4,
                                "other payment mode or contract, arranged individually (please describe the arrangements made in the Additional information field)",
                            ),
                            (
                                5,
                                "standard - payment based on a pro forma invoice, due 7 days before the course starts",
                            ),
                            (
                                6,
                                "payment after the course, due 14 days after the course end, additional fee +5%",
                            ),
                            (
                                7,
                                "other payment mode or contract, arranged individually (please describe the arrangements made in the Additional information field)",
                            ),
                        ],
                    ),
                ),
                (
                    "bylem_wczesniej",
                    models.BooleanField(
                        default=False,
                        verbose_name="I have previously participated in an ALX training (also applies to coworkers)",
                    ),
                ),
                (
                    "bylem_na",
                    models.TextField(
                        verbose_name="If yes, please enter the training name and approximate date",
                        blank=True,
                    ),
                ),
                (
                    "cena_obiadow",
                    models.DecimalField(
                        null=True, max_digits=30, decimal_places=2, blank=True
                    ),
                ),
                (
                    "stawka_vat",
                    models.DecimalField(
                        default=Decimal("0.2300"),
                        max_digits=30,
                        decimal_places=4,
                        choices=[(Decimal("0.0000"), "0%"), (Decimal("0.2300"), "23%")],
                    ),
                ),
                (
                    "status",
                    models.IntegerField(
                        default=1,
                        choices=[
                            (
                                -3,
                                "zg\u0142oszenie z internetu, trzeba potwierdzi\u0107",
                            ),
                            (-2, "zg\u0142oszenie tel/mail, trzeba potwierdzi\u0107"),
                            (
                                -1,
                                "powiedzieli\u015bmy \u017ce niepewne, skontaktowa\u0107 si\u0119 znów przed szkoleniem",
                            ),
                            (0, "trzeba przemówi\u0107"),
                            (
                                1,
                                "potwierdzone, odbywa si\u0119, uczestnik wszystko wie",
                            ),
                            (3, "przeszkolony"),
                            (
                                5,
                                "nieprzeszkolony (nie odby\u0142o si\u0119), ale nadal zainteresowany",
                            ),
                            (4, "zrezygnowa\u0142"),
                        ],
                    ),
                ),
                (
                    "imie_nazwisko_zostalo_sprawdzone",
                    models.BooleanField(
                        default=False,
                        help_text="There should be only one fullname per line and nothing else.",
                        verbose_name="I confirm that the data are correct and are adequate to be placed on the invoice",
                    ),
                ),
                (
                    "uczestnik_wieloosobowy_ilosc_osob",
                    models.IntegerField(
                        help_text="Dotyczy tylko zg\u0142osze\u0144 kilkuosobowych - zostaw puste, je\u017celi rejestrujesz pojedynczego uczestnika.",
                        null=True,
                        blank=True,
                    ),
                ),
                (
                    "chce_obiady",
                    models.BooleanField(
                        default=False,
                        help_text="Pole zaznaczane automatycznie przy tworzeniu uczestnika w przypadku, gdy obiady s\u0105 wliczone w cen\u0119 terminu szkolenia.",
                        verbose_name="zamawiam obiady",
                    ),
                ),
                (
                    "email",
                    models.EmailField(
                        help_text="do kontaktu",
                        max_length=75,
                        verbose_name="email",
                        blank=True,
                    ),
                ),
                (
                    "telefon",
                    models.CharField(max_length=50, verbose_name="telefon", blank=True),
                ),
                (
                    "adres",
                    models.CharField(
                        help_text="korespondencyjny (je\u015bli wpisano adres do faktury, i do korespondencji jest taki sam, to mo\u017cna nie wpisywa\u0107)",
                        max_length=100,
                        verbose_name="adres",
                        blank=True,
                    ),
                ),
                (
                    "miejscowosc_kod",
                    models.CharField(
                        max_length=100,
                        verbose_name="miejscowo\u015b\u0107, kod pocztowy",
                        blank=True,
                    ),
                ),
                (
                    "papier",
                    models.IntegerField(
                        default=1,
                        choices=[
                            (1, "brak papieru, zg\u0142oszenie internetowe"),
                            (2, "brak papieru, tylko lu\u017ane telefony/maile"),
                            (3, "oficjalne zg\u0142oszenie mailowe"),
                            (4, "podpisany formularz zg\u0142oszeniowy"),
                            (5, "podpisane zamówienie pisemne lub faksowe"),
                            (6, "podpisana umowa szkoleniowa"),
                            (7, "brak papieru, ale szkolenie ju\u017c zap\u0142acone"),
                        ],
                    ),
                ),
                (
                    "kwota_do_zaplaty",
                    models.DecimalField(
                        help_text="zostaw pust\u0105, \u017ceby wzi\u0105\u0107 domy\u015bln\u0105 cen\u0119 ze szkolenia",
                        null=True,
                        max_digits=30,
                        decimal_places=2,
                        blank=True,
                    ),
                ),
                (
                    "termin_zaplaty",
                    models.DateField(
                        help_text="zostaw pusty, \u017ceby ustawi\u0107 na 7 dni przed terminem szkolenia; je\u015bli zostanie wybrana opcja p\u0142atno\u015bci z zaliczk\u0105 i reszt\u0105, lub z zaliczk\u0105 i ratami, to termin zaliczki ustawi si\u0119 na ju\u017c oraz: reszty na 7 dni przed szkoleniem, lub rat zgodnie z nasz\u0105 procedur\u0105",
                        null=True,
                        blank=True,
                    ),
                ),
                (
                    "nr_proformy",
                    models.CharField(
                        help_text="je\u017celi wystawiono", max_length=40, blank=True
                    ),
                ),
                ("nr_faktury", models.CharField(max_length=80, blank=True)),
                ("zaplacone", models.DateField(null=True, blank=True)),
                (
                    "zaliczka_kwota",
                    models.DecimalField(
                        null=True, max_digits=30, decimal_places=2, blank=True
                    ),
                ),
                ("zaliczka_termin", models.DateField(null=True, blank=True)),
                ("zaliczka_zaplacone", models.DateField(null=True, blank=True)),
                (
                    "rata1_kwota",
                    models.DecimalField(
                        null=True, max_digits=30, decimal_places=2, blank=True
                    ),
                ),
                ("rata1_termin", models.DateField(null=True, blank=True)),
                ("rata1_zaplacone", models.DateField(null=True, blank=True)),
                (
                    "rata2_kwota",
                    models.DecimalField(
                        null=True, max_digits=30, decimal_places=2, blank=True
                    ),
                ),
                ("rata2_termin", models.DateField(null=True, blank=True)),
                ("rata2_zaplacone", models.DateField(null=True, blank=True)),
                (
                    "rata3_kwota",
                    models.DecimalField(
                        null=True, max_digits=30, decimal_places=2, blank=True
                    ),
                ),
                ("rata3_termin", models.DateField(null=True, blank=True)),
                ("rata3_zaplacone", models.DateField(null=True, blank=True)),
                (
                    "rata4_kwota",
                    models.DecimalField(
                        null=True, max_digits=30, decimal_places=2, blank=True
                    ),
                ),
                ("rata4_termin", models.DateField(null=True, blank=True)),
                ("rata4_zaplacone", models.DateField(null=True, blank=True)),
                ("uwagi", models.TextField(blank=True)),
                ("uwagi_klienta", models.TextField(blank=True)),
                (
                    "czas_dodania",
                    models.DateTimeField(
                        help_text="tj. czas wpisania niniejszego rekordu; zostaw puste, aby ustawi\u0107 chwil\u0119 obecn\u0105",
                        blank=True,
                    ),
                ),
                (
                    "podmiot_publiczny",
                    models.BooleanField(
                        default=False,
                        help_text="dla zwolnie\u0144 z VAT",
                        verbose_name="podmiot publiczny lub finansowanie ze \u015brodków publicznych",
                    ),
                ),
                (
                    "faktura_po_szkoleniu",
                    models.BooleanField(
                        default=False,
                        verbose_name="Wystawi\u0107 faktur\u0119 VAT do zap\u0142aty",
                    ),
                ),
                (
                    "faktura_po_szkoleniu_data",
                    models.DateField(
                        help_text="zostaw\u0105\xa0pust\u0105, wówczas ustawiony zostanie ostatni dzie\u0144 szkolenia",
                        null=True,
                        verbose_name="data wystawienia fv",
                        blank=True,
                    ),
                ),
                (
                    "wystaw_proforme_automatycznie",
                    models.BooleanField(
                        default=True,
                        help_text="Automatyczna pro-forma dost\u0119pna tylko dla uczestników szkole\u0144 otwartych, którzy p\u0142ac\u0105\xa0standardowo 7 dni przed startem. Opcja ta wymaga podania danych teleadresowych osoby prywatnej (Imi\u0119 i nazwisko, email, adres, kod pocztowy) lub firmy (Nazwa firmy, adres firmy, kraj, kod pocztowy firmy, NIP).",
                        verbose_name="wystaw i wy\u015blij faktur\u0119 pro-forma automatycznie w momencie potwierdzenia grupy",
                    ),
                ),
                (
                    "odeslal_umowe",
                    models.BooleanField(
                        default=False, verbose_name="odes\u0142a\u0142 umow\u0119"
                    ),
                ),
                (
                    "odeslal_podpisany_formularz",
                    models.BooleanField(
                        default=False,
                        verbose_name="odes\u0142a\u0142 podpisany formularz",
                    ),
                ),
                (
                    "zliczacz_proforma_no",
                    models.CharField(
                        verbose_name="ID faktury",
                        max_length=10,
                        null=True,
                        editable=False,
                        blank=True,
                    ),
                ),
                (
                    "firma",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        blank=True,
                        to="crm.Firma",
                        null=True,
                    ),
                ),
                (
                    "termin",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="www.TerminSzkolenia",
                    ),
                ),
                (
                    "waluta",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT, to="i18n.Waluta"
                    ),
                ),
            ],
            options={
                "ordering": ["-czas_dodania"],
                "verbose_name_plural": "uczestnicy",
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="UserCoursesNotification",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                (
                    "source",
                    models.CharField(
                        default="www",
                        max_length=20,
                        verbose_name="\u017aród\u0142o zapisu",
                        db_index=True,
                        choices=[
                            ("www", "strona www"),
                            ("system", "automat"),
                            ("staff", "dodany przez Biuro"),
                        ],
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="utworzono"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="aktualizowano"),
                ),
                (
                    "locations",
                    models.ManyToManyField(
                        to="www.Lokalizacja", verbose_name="lokalizacje"
                    ),
                ),
                (
                    "training",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        verbose_name="szkolenie",
                        to="www.Szkolenie",
                    ),
                ),
            ],
            options={
                "ordering": ("-created_at",),
                "verbose_name": "szkolenie u\u017cytkownika",
                "verbose_name_plural": "szkolenia u\u017cytkownika",
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="UserNotification",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                (
                    "email",
                    models.EmailField(
                        unique=True, max_length=200, verbose_name="adres email"
                    ),
                ),
                (
                    "status",
                    models.SmallIntegerField(
                        default=0,
                        db_index=True,
                        verbose_name="status",
                        choices=[
                            (-1, "nigdy niezweryfikowany"),
                            (0, "niezweryfikowany"),
                            (1, "zweryfikowany"),
                            (2, "wypisany"),
                        ],
                    ),
                ),
                (
                    "send_notifications",
                    models.BooleanField(
                        default=True,
                        help_text="Je\u015bli odznaczone, konto jest w pe\u0142ni aktywne i funkcjonalne, ale same powiadomienia o nowych terminach nie s\u0105 wysy\u0142ane.",
                        verbose_name="wysy\u0142aj notyfikacje",
                    ),
                ),
                (
                    "internal_comments",
                    models.TextField(verbose_name="uwagi internal", blank=True),
                ),
                (
                    "full_name",
                    models.CharField(
                        max_length=200, verbose_name="imi\u0119 i nazwisko", blank=True
                    ),
                ),
                (
                    "phone_number",
                    models.CharField(
                        max_length=150, verbose_name="telefon", blank=True
                    ),
                ),
                (
                    "company_name",
                    models.CharField(max_length=255, verbose_name="firma", blank=True),
                ),
                (
                    "key",
                    models.UUIDField(
                        verbose_name="klucz dost\u0119pu",
                        unique=True,
                        max_length=32,
                        editable=False,
                        blank=True,
                    ),
                ),
                (
                    "language",
                    models.CharField(
                        default="pl",
                        max_length=2,
                        verbose_name="j\u0119zyk",
                        choices=[("pl", "polski"), ("en", "angielski")],
                    ),
                ),
                (
                    "source",
                    models.CharField(
                        default="www",
                        max_length=20,
                        verbose_name="\u017aród\u0142o rejestracji",
                        db_index=True,
                        choices=[
                            ("www", "strona www"),
                            ("system", "automat"),
                            ("staff", "dodany przez Biuro"),
                        ],
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="utworzono", db_index=True
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="aktualizowano"),
                ),
                (
                    "last_email_sent_at",
                    models.DateTimeField(
                        db_index=True,
                        null=True,
                        verbose_name="ostatnie powiadomienie",
                        blank=True,
                    ),
                ),
                (
                    "activation_at",
                    models.DateTimeField(
                        null=True, verbose_name="data aktywacji", blank=True
                    ),
                ),
                (
                    "activation_email_sent_at",
                    models.DateTimeField(
                        null=True,
                        verbose_name="data wys\u0142ania emaila aktywacyjnego",
                        blank=True,
                    ),
                ),
                (
                    "resend_activation_email_counter",
                    models.PositiveSmallIntegerField(
                        default=0,
                        verbose_name="liczba wys\u0142anych maili aktywayjnych",
                    ),
                ),
                (
                    "remote_addr",
                    models.IPAddressField(
                        null=True, verbose_name="adres IP", blank=True
                    ),
                ),
                (
                    "user_agent",
                    models.CharField(
                        max_length=255, verbose_name="user agent", blank=True
                    ),
                ),
                ("staff_updated_at", models.DateTimeField(null=True, blank=True)),
                (
                    "staff_updated_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        blank=True,
                        to=settings.AUTH_USER_MODEL,
                        null=True,
                    ),
                ),
            ],
            options={
                "verbose_name": "powiadomienie o szkoleniu",
                "verbose_name_plural": "powiadomienia o szkoleniach",
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="UserNotificationLog",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                (
                    "email_sent_at",
                    models.DateTimeField(
                        auto_now_add=True,
                        verbose_name="data wys\u0142ania emaila",
                        db_index=True,
                    ),
                ),
                (
                    "notification_type",
                    models.CharField(
                        max_length=50,
                        verbose_name="typ powiadomienia",
                        choices=[
                            ("term_created", "termin utworzony"),
                            ("term_started", "termin uruchomiony"),
                            (
                                "before_start",
                                "termin ma odpowiedni\u0105 ilo\u015b\u0107 uczestników",
                            ),
                            (
                                "other_locations",
                                "potencjalnie zainteresowani z innych miast",
                            ),
                        ],
                    ),
                ),
                (
                    "location",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        verbose_name="lokalizacja",
                        to="www.Lokalizacja",
                    ),
                ),
                (
                    "term",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        verbose_name="termin szkolenia",
                        to="www.TerminSzkolenia",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="www.UserNotification",
                    ),
                ),
            ],
            options={
                "verbose_name": "powiadomienie",
                "verbose_name_plural": "historia powiadomie\u0144",
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="Zgloszenie",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                (
                    "imie_nazwisko",
                    models.TextField(
                        help_text="or list of participants, if more than one",
                        verbose_name="full name",
                    ),
                ),
                (
                    "osoba_do_kontaktu",
                    models.CharField(
                        help_text="if different than the participant",
                        max_length=100,
                        verbose_name="contact person",
                        blank=True,
                    ),
                ),
                (
                    "prywatny",
                    models.BooleanField(
                        default=False, verbose_name="I am a private person"
                    ),
                ),
                (
                    "chce_fakture",
                    models.BooleanField(
                        default=False,
                        verbose_name="I want to receive a printed invoice",
                    ),
                ),
                (
                    "faktura_firma",
                    models.CharField(
                        max_length=100, verbose_name="company", blank=True
                    ),
                ),
                (
                    "faktura_adres",
                    models.CharField(
                        max_length=100, verbose_name="address", blank=True
                    ),
                ),
                (
                    "faktura_miejscowosc_kod",
                    models.CharField(
                        max_length=100, verbose_name="city, postal code", blank=True
                    ),
                ),
                (
                    "faktura_kraj",
                    models.CharField(
                        default="PL",
                        max_length=2,
                        verbose_name="country",
                        choices=[
                            ("PL", "Polska"),
                            ("GB", "Wielka Brytania"),
                            ("DE", "Niemcy"),
                            ("AF", "Afganistan"),
                            ("AL", "Albania"),
                            ("DZ", "Algeria"),
                            ("AD", "Andora"),
                            ("AO", "Angola"),
                            ("AI", "Anguilla"),
                            ("AQ", "Antarktyda"),
                            ("AG", "Antigua i Barbuda"),
                            ("SA", "Arabia Saudyjska"),
                            ("AR", "Argentyna"),
                            ("AM", "Armenia"),
                            ("AW", "Aruba"),
                            ("AU", "Australia"),
                            ("AT", "Austria"),
                            ("AZ", "Azerbejd\u017can"),
                            ("BS", "Bahamy"),
                            ("BH", "Bahrajn"),
                            ("BD", "Bangladesz"),
                            ("BB", "Barbados"),
                            ("BE", "Belgia"),
                            ("BZ", "Belize"),
                            ("BJ", "Benin"),
                            ("BM", "Bermudy"),
                            ("BT", "Bhutan"),
                            ("BY", "Bia\u0142oru\u015b"),
                            ("BO", "Boliwia"),
                            ("BQ", "Bonaire, Sint Eustatius and Saba"),
                            ("BA", "Bo\u015bnia i Hercegowina"),
                            ("BW", "Botswana"),
                            ("BR", "Brazylia"),
                            ("BN", "Brunei"),
                            ("IO", "Brytyjskie Terytorium Oceanu Indyjskiego"),
                            ("VG", "Brytyjskie Wyspy Dziewicze"),
                            ("VI", "Brytyjskie Wyspy Dziewicze"),
                            ("BG", "Bu\u0142garia"),
                            ("BF", "Burkina Faso"),
                            ("BI", "Burundi"),
                            ("CV", "Cabo Verde"),
                            ("CL", "Chile"),
                            ("CN", "Chiny"),
                            ("HR", "Chorwacja"),
                            ("CW", "Cura\xe7ao"),
                            ("CY", "Cyprs"),
                            ("TD", "Czad"),
                            ("ME", "Czarnogóra"),
                            ("CZ", "Czechy"),
                            ("UM", "Dalekie Wyspy Mniejsze Stanów Zjednoczonych"),
                            ("DK", "Dania"),
                            ("DM", "Dominika"),
                            ("DO", "Dominikana"),
                            ("DJ", "D\u017cibuti"),
                            ("EG", "Egipt"),
                            ("EC", "Ekwador"),
                            ("ER", "Erytrea"),
                            ("EE", "Estonia"),
                            ("ET", "Etiopia"),
                            ("FK", "Falklandy"),
                            ("FJ", "Fid\u017ci"),
                            ("PH", "Filipiny"),
                            ("FI", "Finlandia"),
                            ("FR", "Francja"),
                            (
                                "TF",
                                "Francuskie Terytoria Po\u0142udniowe i Antarktyczne",
                            ),
                            ("GA", "Gabon"),
                            ("GM", "Gambia"),
                            (
                                "GS",
                                "Georgia Po\u0142udniowa i Sandwich Po\u0142udniowy",
                            ),
                            ("GH", "Ghana"),
                            ("GI", "Gibraltar"),
                            ("GR", "Grecja"),
                            ("GD", "Grenada"),
                            ("GL", "Grenlandia"),
                            ("GE", "Gruzja"),
                            ("GU", "Guam"),
                            ("GG", "Guernsey"),
                            ("GY", "Gujana"),
                            ("GF", "Gujana Francuska"),
                            ("GP", "Gwadelupa"),
                            ("GT", "Gwatemala"),
                            ("GN", "Gwinea"),
                            ("GW", "Gwinea Bissau"),
                            ("GQ", "Gwinea Równikowa"),
                            ("HT", "Haiti"),
                            ("ES", "Hiszpania"),
                            ("NL", "Holandia"),
                            ("HN", "Honduras"),
                            ("HK", "Hong Kong"),
                            ("IN", "Indie"),
                            ("ID", "Indonezja"),
                            ("IQ", "Irak"),
                            ("IR", "Iran"),
                            ("IE", "Irlandia"),
                            ("IS", "Islandia"),
                            ("IL", "Izrael"),
                            ("JM", "Jamajka"),
                            ("JP", "Japonia"),
                            ("YE", "Jemen"),
                            ("JE", "Jersey"),
                            ("JO", "Jordania"),
                            ("KY", "Kajmany"),
                            ("KH", "Kambod\u017ca"),
                            ("CM", "Kamerun"),
                            ("CA", "Kanada"),
                            ("QA", "Katar"),
                            ("KZ", "Kazachstan"),
                            ("KE", "Kenia"),
                            ("KG", "Kirgistan"),
                            ("KI", "Kiribati"),
                            ("CO", "Kolumbia"),
                            ("KM", "Komory"),
                            ("CG", "Kongo"),
                            ("KP", "Korea Pó\u0142nocna"),
                            ("KR", "Korea Po\u0142udniowa"),
                            ("CR", "Kostaryka"),
                            ("CU", "Kuba"),
                            ("KW", "Kuwejt"),
                            ("LA", "Laos"),
                            ("LS", "Lesotho"),
                            ("LB", "Liban"),
                            ("LR", "Liberia"),
                            ("LY", "Libia"),
                            ("LI", "Liechtenstein"),
                            ("LT", "Litwa"),
                            ("LU", "Luksembourg"),
                            ("MK", "Macedonia"),
                            ("MG", "Madagaskar"),
                            ("YT", "Majotta"),
                            ("MO", "Makau"),
                            ("MW", "Malawi"),
                            ("MV", "Malediwy"),
                            ("MY", "Malezja"),
                            ("ML", "Mali"),
                            ("MT", "Malta"),
                            ("MP", "Mariany Pó\u0142nocne"),
                            ("MA", "Maroko"),
                            ("MQ", "Martynika"),
                            ("MR", "Mauritania"),
                            ("MU", "Mauritius"),
                            ("MX", "Meksyk"),
                            ("FM", "Mikronezja"),
                            ("MM", "Mjanma"),
                            ("MD", "Mo\u0142dawia"),
                            ("MC", "Monako"),
                            ("MN", "Mongolia"),
                            ("MS", "Montserrat"),
                            ("MZ", "Mozambik"),
                            ("NA", "Nambia"),
                            ("NR", "Nauru"),
                            ("NP", "Nepal"),
                            ("NE", "Niger"),
                            ("NG", "Nigeria"),
                            ("NI", "Nikaragua"),
                            ("NU", "Niue"),
                            ("NF", "Norfolk"),
                            ("NO", "Norwegia"),
                            ("NC", "Nowa Kaledonia"),
                            ("NZ", "Nowa Zelandia"),
                            ("OM", "Oman"),
                            ("PK", "Pakistan"),
                            ("PW", "Palau"),
                            ("PS", "Palestine, State of"),
                            ("PA", "Palestyna"),
                            ("PG", "Papua-Nowa Gwinea"),
                            ("PY", "Paragwaj"),
                            ("PE", "Peru"),
                            ("PN", "Pitcairn"),
                            ("PF", "Polinezja Francuska"),
                            ("PR", "Portoryko"),
                            ("PT", "Portugalia"),
                            ("CD", "Republika Kongo"),
                            ("ZA", "Republika Po\u0142udniowej Afryki"),
                            ("CF", "Republika \u015arodkowoafryka\u0144ska"),
                            ("RE", "Reunion"),
                            ("RU", "Rosja"),
                            ("RO", "Rumunia"),
                            ("RW", "Rwanda"),
                            ("EH", "Sahara Zachodnia"),
                            ("BL", "Saint Barth\xe9lemy"),
                            ("KN", "Saint Kitts i Nevis"),
                            ("LC", "Saint Lucia"),
                            ("PM", "Saint Pierre i Miquelon"),
                            ("VC", "Saint Vincent i Grenadyny"),
                            ("MF", "Saint-Martin"),
                            ("SV", "Salwador"),
                            ("WS", "Samoa"),
                            ("AS", "Samoa Ameryka\u0144skie"),
                            ("SM", "San Marino"),
                            ("SN", "Senegal"),
                            ("RS", "Serbia"),
                            ("SC", "Seszele"),
                            ("SL", "Sierra Leone"),
                            ("SG", "Singapur"),
                            ("SX", "Sint Maarten"),
                            ("SO", "Somalia"),
                            ("SK", "S\u0142owacja"),
                            ("SI", "S\u0142owenia"),
                            ("LK", "Sri Lanka"),
                            ("US", "Stany Zjednoczone"),
                            ("SZ", "Suazi"),
                            ("SD", "Sudan"),
                            ("SS", "Sudan Po\u0142udniowy"),
                            ("SR", "Suriname"),
                            ("SJ", "Svalbard i Jan Mayen"),
                            ("SY", "Syria"),
                            ("CH", "Szwajcaria"),
                            ("SE", "Szwecja"),
                            ("TJ", "Tad\u017cykistan"),
                            ("TH", "Tajlandia"),
                            ("TW", "Tajwan"),
                            ("TZ", "Tanzania"),
                            ("TL", "Timor Wschodni"),
                            ("TG", "Togo"),
                            ("TK", "Tokelau"),
                            ("TO", "Tonga"),
                            ("TT", "Trynidad i Tobago"),
                            ("TN", "Tunezja"),
                            ("TR", "Turkcja"),
                            ("TM", "Turkmenistan"),
                            ("TC", "Turks i Caicos"),
                            ("TV", "Tuvalu"),
                            ("UG", "Uganda"),
                            ("UA", "Ukraina"),
                            ("UY", "Urugwaj"),
                            ("UZ", "Uzbekistan"),
                            ("VU", "Vanuatu"),
                            ("WF", "Wallis i Futuna"),
                            ("VA", "Watykan"),
                            ("HU", "W\u0119gry"),
                            ("VE", "Wenezuela"),
                            ("VN", "Wietnam"),
                            ("IT", "W\u0142ochy"),
                            ("CI", "Wybrze\u017ce Ko\u015bci S\u0142oniowej"),
                            ("BV", "Wyspa Bouveta"),
                            ("CX", "Wyspa Bo\u017cego Narodzenia"),
                            ("IM", "Wyspa Man"),
                            (
                                "SH",
                                "Wyspa \u015awi\u0119tej Heleny, Wyspa Wniebowst\u0105pienia i Tristan da Cunha",
                            ),
                            ("AX", "Wyspy Alandzkie"),
                            ("CK", "Wyspy Cooka"),
                            ("HM", "Wyspy Heard i McDonalda"),
                            ("CC", "Wyspy Kokosowe"),
                            ("MH", "Wyspy Marshalla"),
                            ("FO", "Wyspy Owcze"),
                            ("SB", "Wyspy Salomona"),
                            (
                                "ST",
                                "Wyspy \u015awi\u0119tego Tomasza i Ksi\u0105\u017c\u0119ca",
                            ),
                            ("ZM", "Zambia"),
                            ("ZW", "Zimbabwe"),
                            ("AE", "Zjednoczone Emiraty Arabskie"),
                            ("LV", "\u0141otwa"),
                        ],
                    ),
                ),
                (
                    "faktura_nip",
                    models.CharField(
                        max_length=40,
                        verbose_name="VAT identification number",
                        blank=True,
                    ),
                ),
                (
                    "faktura_vat_id",
                    models.CharField(
                        help_text="or Company ID",
                        max_length=40,
                        verbose_name="VAT ID",
                        blank=True,
                    ),
                ),
                (
                    "chce_egzamin",
                    models.BooleanField(default=False, verbose_name="I order an exam"),
                ),
                (
                    "chce_autoryzacje",
                    models.BooleanField(
                        default=False, verbose_name="I order authorization"
                    ),
                ),
                (
                    "ile_obiadow_wegetarianskich",
                    models.IntegerField(
                        null=True,
                        verbose_name="vegetarian lunches for how many of the participants",
                        blank=True,
                    ),
                ),
                (
                    "za_kurs_zaplace",
                    models.IntegerField(
                        default=1,
                        choices=[
                            (1, "now, in full"),
                            (
                                2,
                                "an advance of 200 GBP now, and the remainder of the fee no later than 7 days before the start of the course",
                            ),
                            (
                                3,
                                "(only private client) an advance of 200 GBP now and the remainder in two equal instalments",
                            ),
                            (
                                10,
                                "(tylko klient indywidualny) wp\u0142acaj\u0105c kwot\u0119 w pi\u0119ciu ratach, zgodnie z <a href='/pl/raty/'>zasadami p\u0142atno\u015bci ratalnej</a>",
                            ),
                            (
                                4,
                                "other payment mode or contract, arranged individually (please describe the arrangements made in the Additional information field)",
                            ),
                            (
                                5,
                                "standard - payment based on a pro forma invoice, due 7 days before the course starts",
                            ),
                            (
                                6,
                                "payment after the course, due 14 days after the course end, additional fee +5%",
                            ),
                            (
                                7,
                                "other payment mode or contract, arranged individually (please describe the arrangements made in the Additional information field)",
                            ),
                        ],
                    ),
                ),
                (
                    "bylem_wczesniej",
                    models.BooleanField(
                        default=False,
                        verbose_name="I have previously participated in an ALX training (also applies to coworkers)",
                    ),
                ),
                (
                    "bylem_na",
                    models.TextField(
                        verbose_name="If yes, please enter the training name and approximate date",
                        blank=True,
                    ),
                ),
                (
                    "cena_obiadow",
                    models.DecimalField(
                        null=True, max_digits=30, decimal_places=2, blank=True
                    ),
                ),
                (
                    "stawka_vat",
                    models.DecimalField(
                        default=Decimal("0.2300"),
                        max_digits=30,
                        decimal_places=4,
                        choices=[(Decimal("0.0000"), "0%"), (Decimal("0.2300"), "23%")],
                    ),
                ),
                ("time", models.DateTimeField(auto_now_add=True)),
                ("ip", models.IPAddressField(null=True, editable=False, blank=True)),
                ("token", models.CharField(unique=True, max_length=16, editable=False)),
                (
                    "email",
                    models.EmailField(max_length=75, verbose_name="contact e-mail"),
                ),
                (
                    "telefon",
                    models.CharField(max_length=50, verbose_name="contact phone"),
                ),
                (
                    "adres",
                    models.CharField(
                        max_length=100, verbose_name="address for correspondence"
                    ),
                ),
                (
                    "miejscowosc_kod",
                    models.CharField(max_length=100, verbose_name="city, postal code"),
                ),
                (
                    "podmiot_publiczny",
                    models.BooleanField(
                        default=False,
                        help_text="Checking this option means that you are exempt from VAT. In case of any doubts, please contact us.",
                        verbose_name="public entity or funded from public funds",
                    ),
                ),
                (
                    "uczestnik_wieloosobowy_ilosc_osob",
                    models.IntegerField(
                        help_text="If you are ordering training for a group, please enter the number of participants",
                        null=True,
                        verbose_name="number of participants",
                        blank=True,
                    ),
                ),
                (
                    "chce_obiady",
                    models.BooleanField(default=False, verbose_name="I order lunches"),
                ),
                ("cena", models.DecimalField(max_digits=30, decimal_places=2)),
                (
                    "uwagi_klienta",
                    models.TextField(verbose_name="Additional info", blank=True),
                ),
                (
                    "firma",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        blank=True,
                        to="crm.Firma",
                        null=True,
                    ),
                ),
                (
                    "termin",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        verbose_name="date",
                        to="www.TerminSzkolenia",
                        help_text="and city and mode of training",
                    ),
                ),
                (
                    "waluta",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT, to="i18n.Waluta"
                    ),
                ),
            ],
            options={
                "ordering": ["-time"],
                "verbose_name": "zg\u0142oszenie",
                "verbose_name_plural": "zg\u0142oszenia",
            },
            bases=(models.Model,),
        ),
        migrations.AlterUniqueTogether(
            name="usernotificationlog",
            unique_together=set([("user", "term", "location", "notification_type")]),
        ),
        migrations.AddField(
            model_name="usercoursesnotification",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="preferences",
                to="www.UserNotification",
            ),
            preserve_default=True,
        ),
        migrations.AlterUniqueTogether(
            name="usercoursesnotification",
            unique_together=set([("user", "training")]),
        ),
        migrations.AlterUniqueTogether(
            name="tagzawod",
            unique_together=set([("language", "slug")]),
        ),
        migrations.AlterUniqueTogether(
            name="tagtechnologia",
            unique_together=set([("language", "slug")]),
        ),
        migrations.AddField(
            model_name="szkolenie",
            name="tag_dlugosc",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                default=2,
                to="www.TagDlugosc",
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="szkolenie",
            name="tag_zawod",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT, to="www.TagZawod"
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="szkolenie",
            name="tagi_technologia",
            field=models.ManyToManyField(to="www.TagTechnologia"),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="szkolenie",
            name="teksty_o_terminach",
            field=models.ManyToManyField(
                related_name="szkolenia_tot",
                through="www.TekstOTerminach",
                to="www.Lokalizacja",
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="szkolenie",
            name="waluta",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT, to="i18n.Waluta"
            ),
            preserve_default=True,
        ),
        migrations.AlterUniqueTogether(
            name="szkolenie",
            unique_together=set(
                [("slug", "language"), ("strona_z_opisem_slug", "language")]
            ),
        ),
        migrations.AlterUniqueTogether(
            name="sciezka",
            unique_together=set([("slug", "language")]),
        ),
        migrations.AddField(
            model_name="referencja",
            name="tagi_technologia",
            field=models.ManyToManyField(to="www.TagTechnologia"),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="referencja",
            name="uczestnicy",
            field=models.ManyToManyField(to="www.Uczestnik", blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="prowadzacy",
            name="szkolenia",
            field=models.ManyToManyField(
                related_name="prowadzacy_set", to="www.Szkolenie", blank=True
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="prowadzacy",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                blank=True,
                to=settings.AUTH_USER_MODEL,
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="potencjalnychetny",
            name="szkolenie",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT, to="www.Szkolenie"
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="potencjalnychetny",
            name="wprowadzil",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="myflatpage",
            name="szkolenie",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                blank=True,
                to="www.Szkolenie",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterUniqueTogether(
            name="myflatpage",
            unique_together=set([("slug", "language")]),
        ),
        migrations.AddField(
            model_name="graduate",
            name="participant",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                verbose_name="uczestnik",
                blank=True,
                to="www.Uczestnik",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="graduate",
            name="term",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                verbose_name="termin szkolenia",
                to="www.TerminSzkolenia",
            ),
            preserve_default=True,
        ),
        migrations.AlterUniqueTogether(
            name="graduate",
            unique_together=set([("term", "email")]),
        ),
        migrations.AddField(
            model_name="certyfikat",
            name="termin",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT, to="www.TerminSzkolenia"
            ),
            preserve_default=True,
        ),
        migrations.AlterOrderWithRespectTo(
            name="certyfikat",
            order_with_respect_to="termin",
        ),
        migrations.AddField(
            model_name="certificategroup",
            name="participant",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                verbose_name="uczestnik",
                to="www.Uczestnik",
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="calendarupdate",
            name="termin",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                blank=True,
                to="www.TerminSzkolenia",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterUniqueTogether(
            name="autoryzacja",
            unique_together=set([("language", "nazwa")]),
        ),
    ]
