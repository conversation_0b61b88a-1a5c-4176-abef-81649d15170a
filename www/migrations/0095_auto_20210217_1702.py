# -*- coding: utf-8 -*-
# Generated by Django 1.11.29 on 2021-02-17 17:02
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("www", "0094_prowadzacy_umowa"),
    ]

    operations = [
        migrations.AlterField(
            model_name="tagtechnologia",
            name="widoczny_na_frontpage",
            field=models.BooleanField(
                default=True,
                help_text="Aby tag by\u0142 widoczny na stronie g\u0142\xf3wnej to pole musi by\u0107 zaznaczone jak te\u017c 'widoczny publicznie' (stan na 2021+: pole prawdopodobnie ju\u017c nie jest u\u017cywane)",
                verbose_name="widoczny na stronie g\u0142\xf3wnej",
            ),
        ),
        migrations.AlterField(
            model_name="tagtechnologia",
            name="widoczny_publicznie",
            field=models.<PERSON><PERSON>an<PERSON>ield(
                default=True, help_text="W praktyce synonim: aktywny."
            ),
        ),
    ]
