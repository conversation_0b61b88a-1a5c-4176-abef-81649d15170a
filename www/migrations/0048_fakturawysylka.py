import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("www", "0047_prowadzacy_tytul_naukowy"),
    ]

    operations = [
        migrations.CreateModel(
            name="FakturaWysylka",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                (
                    "recipient_name",
                    models.CharField(max_length=250, verbose_name="nazwa odbiorcy"),
                ),
                (
                    "recipient_address",
                    models.CharField(max_length=100, verbose_name="ulica odbiorcy"),
                ),
                (
                    "recipient_post_code",
                    models.CharField(max_length=10, verbose_name="kod pocztowy"),
                ),
                (
                    "recipient_city",
                    models.CharField(max_length=150, verbose_name="miejscowość"),
                ),
                (
                    "recipient_country",
                    models.Char<PERSON>ield(default="PL", max_length=10, verbose_name="kraj"),
                ),
                (
                    "faktura_id",
                    models.IntegerField(unique=True, verbose_name="ID faktury"),
                ),
                (
                    "faktura_numer",
                    models.CharField(max_length=100, verbose_name="numer faktury"),
                ),
                (
                    "faktura_uwagi",
                    models.TextField(verbose_name="uwagi na fakturze", blank=True),
                ),
                (
                    "faktura_adres_nabywcy",
                    models.CharField(
                        max_length=200, verbose_name="adres nabywcy z faktury"
                    ),
                ),
                (
                    "dispatch_id",
                    models.CharField(
                        default=None,
                        max_length=100,
                        unique=True,
                        null=True,
                        verbose_name="postivo id",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("1", "Przesyłka przyjęta do realizacji"),
                            ("2", "Przesyłka w trakcie konfekcjonowania"),
                            ("3", "Przesyłka przekazana operatorowi pocztowemu"),
                            (
                                "4",
                                "Przesyłka doręczona do odbiorcy [w przypadku wysyłki listów za potwierdzeniem odbioru]",
                            ),
                            ("5", "Przesyłka zwrócona do Postivo.pl"),
                            ("50", "Faks jest w trakcie wysyłania"),
                            ("51", "Faks wysłany poprawnie"),
                            ("52", "Brak sygnału faksu odbiorcy"),
                            ("53", "Połączenie nieodebrane"),
                            ("54", "Linia zajęta lub nieprawidłowy numer"),
                            ("55", "Nieokreślony rezultat"),
                            ("56", "Faks nie został wysłany"),
                            ("100", "Przesyłka anulowana"),
                            (
                                "200",
                                "Status zewnętrzny – status pochodzący od operatora pocztowego. Treść statusu znajduje się w polu status_description.",
                            ),
                        ],
                        max_length=10,
                        verbose_name="postivo status",
                    ),
                ),
                (
                    "status_date",
                    models.DateTimeField(
                        default=None, null=True, verbose_name="data zmiany statusu"
                    ),
                ),
                (
                    "dispatched_at",
                    models.DateTimeField(
                        default=None, null=True, verbose_name="zlecono wysyłkę"
                    ),
                ),
                (
                    "key",
                    models.UUIDField(
                        unique=True, max_length=32, editable=False, blank=True
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True,
                        null=True,
                        verbose_name="utworzono",
                        db_index=True,
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        auto_now=True, verbose_name="aktualizowano", null=True
                    ),
                ),
                (
                    "uczestnik",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        default=None,
                        blank=True,
                        to="www.Uczestnik",
                        null=True,
                        verbose_name="Uczestnik",
                    ),
                ),
            ],
            options={
                "ordering": ("-created_at",),
                "verbose_name": "wysyłka faktury",
                "verbose_name_plural": "wysyłka faktur",
            },
            bases=(models.Model,),
        ),
    ]
