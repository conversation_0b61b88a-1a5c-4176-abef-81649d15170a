from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("www", "0025_auto_20160427_1904"),
    ]

    operations = [
        migrations.AddField(
            model_name="szkolenie",
            name="tryb_dzienny_opis",
            field=models.TextField(verbose_name="tryb dzienny opis", blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="szkolenie",
            name="tryb_wieczorowy_opis",
            field=models.TextField(verbose_name="tryb wieczorowy opis", blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="szkolenie",
            name="tryb_zaoczny_opis",
            field=models.TextField(verbose_name="tryb zaoczny opis", blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="terminszkolenia",
            name="nie_pokazuj_wolnych_miejsc",
            field=models.BooleanField(
                default=False,
                help_text="UWAGA: dotyczy tylko terminów promowanych",
                verbose_name="nie pokazuj informacji o wolnych miejscach",
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="terminszkolenia",
            name="dodatkowe_uwagi",
            field=models.TextField(
                blank=True,
                help_text="Jeśli podane, zostaną wyświetlone zamiast domyślnego opisu z obiektu Szkolenia.<br>Opis lądujący na stronie głównej przy terminach kursów; można używać przy wszystkich kursach, modelowo innych niż wieczorowe, żeby podać tryb zajęć opisowo; tutaj wpisujemy też uwagi w rodzaju [DATA]: ZOSTAŁY TYLKO DWA WOLNE MIEJSCA. Można używać znaczników HTML. Tutaj jesli chcemy nawiasy na stronie, to trzeba je ręcznie podać.",
                null=True,
                verbose_name="uwagi www",
            ),
            preserve_default=True,
        ),
    ]
