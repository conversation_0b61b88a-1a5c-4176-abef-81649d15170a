# Generated by Django 2.2.16 on 2023-10-01 10:43

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('www', '0105_myflatpage_is_searchable'),
    ]

    operations = [
        migrations.AddField(
            model_name='myflatpage',
            name='blog_author',
            field=models.CharField(blank=True, max_length=250, verbose_name='[blog] autor'),
        ),
        migrations.AddField(
            model_name='myflatpage',
            name='blog_published_at',
            field=models.DateField(blank=True, null=True, verbose_name='[blog] data publikacji'),
        ),
        migrations.AddField(
            model_name='myflatpage',
            name='is_blog_entry',
            field=models.BooleanField(default=False, help_text='Gdy zaznaczone zostanie dodany autor wpisu i data.', verbose_name='wpis na blog?'),
        ),
    ]
