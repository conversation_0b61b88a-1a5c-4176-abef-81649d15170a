from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("www", "0066_auto_20181024_1547"),
    ]

    operations = [
        migrations.AddField(
            model_name="continuationlog",
            name="discount_code",
            field=models.ForeignKey(
                related_name="continuation_mails",
                on_delete=models.deletion.PROTECT,
                default=None,
                blank=True,
                to="www.DiscountCode",
                null=True,
                verbose_name="kod rabatowy",
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="continuationlog",
            name="participant",
            field=models.ForeignKey(
                on_delete=models.deletion.PROTECT,
                verbose_name="uczestnik",
                blank=True,
                to="www.Uczestnik",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterModelOptions(
            name="continuationlog",
            options={
                "ordering": ("-created_at",),
                "verbose_name": "log kontynuacji",
                "verbose_name_plural": "logi kontynuacji",
            },
        ),
    ]
