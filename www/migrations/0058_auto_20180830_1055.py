from decimal import Decimal

import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("www", "0057_szkolenie_mozliwa_rejestracja_jako_firma"),
    ]

    operations = [
        migrations.CreateModel(
            name="DiscountCode",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                (
                    "code",
                    models.SlugField(
                        max_length=20,
                        blank=True,
                        help_text="Zostaw puste aby wygenerować kod automatycznie.",
                        unique=True,
                        verbose_name="kod",
                    ),
                ),
                (
                    "discount",
                    models.DecimalField(
                        default=Decimal("7"),
                        verbose_name="zni<PERSON><PERSON> (%)",
                        max_digits=5,
                        decimal_places=2,
                        validators=[
                            django.core.validators.MaxValueValidator(Decimal("50")),
                            django.core.validators.MinValueValidator(Decimal("1")),
                        ],
                    ),
                ),
                (
                    "source",
                    models.CharField(
                        default="graduate",
                        max_length=50,
                        verbose_name="źródło",
                        choices=[
                            ("graduate", "absolwent"),
                            ("continuation_training", "kontynuacja szkoleń"),
                            ("30days", "zapis 30 dni przd rozpoczęciem kursu"),
                            ("staff", "dodany przez Biuro"),
                        ],
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="utworzono", db_index=True
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="aktualizowano"),
                ),
            ],
            options={
                "verbose_name": "kod rabatowy",
                "verbose_name_plural": "kody rabatowe",
            },
            bases=(models.Model,),
        ),
        migrations.AddField(
            model_name="graduate",
            name="discount_code",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                null=True,
                default=None,
                to="www.DiscountCode",
                blank=True,
                help_text="Zostanie automatycznie wygenerowany.",
                unique=True,
                verbose_name="kod rabatowy",
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="uczestnik",
            name="discount_code",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                null=True,
                default=None,
                to="www.DiscountCode",
                blank=True,
                unique=True,
                verbose_name="kod rabatowy",
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="zgloszenie",
            name="discount_code",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                null=True,
                default=None,
                to="www.DiscountCode",
                blank=True,
                unique=True,
                verbose_name="kod rabatowy",
            ),
            preserve_default=True,
        ),
    ]
