from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("www", "0015_auto_20160301_1747"),
    ]

    operations = [
        migrations.AddField(
            model_name="prowadzacy",
            name="szczegolowy_opis",
            field=models.TextField(verbose_name="szczegółowy opis", blank=True),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="prowadzacy",
            name="sylwetka",
            field=models.TextField(
                help_text="Bez tagów HTML!",
                verbose_name="sylwetka (skrócone bio treera)",
                blank=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="prowadzacy",
            name="szczegolowy_opis",
            field=models.TextField(
                help_text="Dozwolony tagi HTML.",
                verbose_name="pełne bio trenera",
                blank=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="prowadzacy",
            name="tagline",
            field=models.TextField(
                help_text="Tekst, który wyświetla się w prawej kolumnie obok zdjęcia prowadzącego. Można użyć <span> i wtedy tekst nim objęty się nie złamie.",
                verbose_name="Umiejętności trenera",
                blank=True,
            ),
            preserve_default=True,
        ),
    ]
