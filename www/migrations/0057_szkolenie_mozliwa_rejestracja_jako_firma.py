from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("www", "0056_auto_20180808_1057"),
    ]

    operations = [
        migrations.AddField(
            model_name="szkolenie",
            name="mozliwa_rejestracja_jako_firma",
            field=models.BooleanField(
                default=True, verbose_name="możliwa rejestracja jako firma?"
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="uczestnik",
            name="osoba_do_kontaktu",
            field=models.Char<PERSON>ield(
                help_text="If different than the participant, eg. the organizing person or parent in the case of underage participants.",
                max_length=100,
                verbose_name="contact person",
                blank=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="zgloszenie",
            name="email_ksiegowosc",
            field=models.EmailField(
                help_text="If different from above",
                max_length=75,
                verbose_name="contact person email",
                blank=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="zglos<PERSON>ie",
            name="osoba_do_kontaktu",
            field=models.CharField(
                help_text="If different than the participant, eg. the organizing person or parent in the case of underage participants.",
                max_length=100,
                verbose_name="contact person",
                blank=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="uczestnik",
            name="email",
            field=models.EmailField(
                help_text="W przypadku grup adres osoby kontaktowej.",
                max_length=75,
                verbose_name="email kursanta",
                blank=True,
            ),
            preserve_default=True,
        ),
    ]
