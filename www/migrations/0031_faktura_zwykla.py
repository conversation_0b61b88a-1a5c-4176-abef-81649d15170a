from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("www", "0030_przepisanie_tagline"),
    ]

    operations = [
        migrations.AddField(
            model_name="uczestnik",
            name="zliczacz_faktura_no",
            field=models.CharField(
                help_text="Jeśli chcesz usunąć omyłkowo wystawioną fakturę, to należy usunąć niniejsze pole (id faktury w zliczacz) ORAZ wejść do Zliczacza i ręcznie skasować tę fakturę.",
                verbose_name="ID faktury 'zwykłej'",
                max_length=10,
                null=True,
                editable=True,
                blank=True,
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name="uczestnik",
            name="zliczacz_proforma_no",
            field=models.CharField(
                help_text="Jeśli chcesz usunąć omyłkowo wystawioną fakturę, to należy usunąć niniejsze pole (id faktury w zliczacz) ORAZ wejść do Zliczacza i ręcznie skasować tę fakturę.",
                verbose_name="ID faktury proforma",
                max_length=10,
                null=True,
                editable=True,
                blank=True,
            ),
            preserve_default=True,
        ),
    ]
