import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("www", "0003_auto_20150729_1259"),
    ]

    operations = [
        migrations.AddField(
            model_name="asset",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                blank=True,
                editable=False,
                to=settings.AUTH_USER_MODEL,
                null=True,
                verbose_name="autor",
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="uczestnik",
            name="nierentowny",
            field=models.BooleanField(
                default=False, verbose_name="<PERSON>e wlicza<PERSON> do rentowności"
            ),
            preserve_default=True,
        ),
    ]
