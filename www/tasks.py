import datetime
import logging
import re

import Levenshtein
from django.conf import settings
from django.contrib.auth.models import User
from django.core.mail import EmailMessage, send_mail
from django.db import transaction
from django.db.models import F, Q
from django.template.loader import render_to_string
from django.urls import reverse
from django.utils import timezone, translation
from django.utils.html import strip_tags
from django.utils.safestring import mark_safe
from django.utils.text import normalize_newlines
from django.utils.translation import ugettext as _

import www.models
from alxcrm import celery_app as app
from leads.models import LeadUser
from www.helpers import to_sentence

from . import api
from .utils import generate_pdf, send_zgloszenie_mail

logger = logging.getLogger(__name__)
site_url_pl = (
    lambda: (settings.FORCE_SSL and "https" or "http")
    + "://"
    + settings.DOMENY_DLA_JEZYKOW["pl"]
)


@app.task(bind=True)
def zapis_na_termin(self, uczestnik_id, language):
    # mail do staffu
    uczestnik = www.models.Uczestnik.objects.select_related("zgloszenie_form").get(
        pk=uczestnik_id
    )
    send_zgloszenie_mail(uczestnik=uczestnik)

    # mail do uczestnika
    if language == "en":
        mail_from_address_zgloszenie = settings.MAIL_FROM_ADDRESS_ZGLOSZENIE_EN
    else:
        mail_from_address_zgloszenie = settings.MAIL_FROM_ADDRESS_ZGLOSZENIE

    http_start = "https://" if settings.FORCE_SSL else "http://"
    site_url = http_start + settings.DOMENY_DLA_JEZYKOW[language]

    zgloszenie = uczestnik.zgloszenie_form

    with translation.override(language):

        potwierdzenie_html = render_to_string(
            "www/zgloszenie_potwierdzenie_email.html",
            {
                "zgloszenie": zgloszenie,
                "site_url": site_url,
                "language": language,
            },
        )

        msg = EmailMessage(
            subject=_("[Wymagana reakcja] ALX - potwierdzenie zgłoszenia dla %(nazwa)s")
                    % {"nazwa": uczestnik.get_nazwa().replace("\r", "").replace("\n", "")},
            body=potwierdzenie_html,
            from_email=mail_from_address_zgloszenie,
            to=[zgloszenie.email],
        )

        msg.content_subtype = "html"
        msg.send()



@app.task(bind=True)
def poinformuj_zapisanych_o_potwierdzeniu_terminu(self, termin_id, uczestnik_id=None):
    termin = www.models.TerminSzkolenia.objects.get(id=termin_id)

    #if termin.language != "pl":
    #    logger.info("Zadanie wywołane dla terminu anglojęzycznego. Kończenie ...")
    #    return

    with translation.override(termin.language):
        szkolenia_kursu = _("kursu") if termin.is_kurs() else _("szkolenia")
        szkolenie_kurs = "kurs" if termin.is_kurs() else "szkolenie"
        subject = "{} {}: {} ({} {})".format(
            _("Potwierdzenie"),
            szkolenia_kursu,
            termin.szkolenie.nazwa,
            termin.termin,
            termin.lokalizacja.fullname,
        )
        godziny = termin.get_hours()
        miesiace = {
            1: _("stycznia"),
            2: _("lutego"),
            3: _("marca"),
            4: _("kwietnia"),
            5: _("maja"),
            6: _("czerwca"),
            7: _("lipca"),
            8: _("sierpnia"),
            9: _("września"),
            10: _("października"),
            11: _("listopada"),
            12: _("grudnia"),
        }
        tryb = {1: _("dzienny"), 2: _("wieczorowy"), 3: _("zaoczny")}[termin.tryb]

        if termin.is_kurs():
            termin_nazwa = "{} ({} {})".format(termin.szkolenie.nazwa, _("tryb"), tryb)
        elif termin.is_weekendowy():
            termin_nazwa = "{} ({})".format(termin.szkolenie.nazwa, _("tryb weekendowy"))
        else:
            termin_nazwa = termin.szkolenie.nazwa

        opisy_zjazdow = []
        if termin.is_szkolenie():
            for od, do in termin.zjazdy():
                if od.day == do.day and od.month == do.month and od.year == do.year:
                    opisy_zjazdow.append("%d.%02d" % (do.day, do.month))
                elif od.month == do.month and od.year == do.year:
                    opisy_zjazdow.append("%d-%d.%02d" % (od.day, do.day, do.month))
                else:
                    opisy_zjazdow.append(
                        "%d.%02d-%d.%02d" % (od.day, od.month, do.day, do.month)
                    )
        else:
            for od, do in termin.zjazdy():
                if od.day == do.day and od.month == do.month and od.year == do.year:
                    opisy_zjazdow.append(
                        "%d %s %d" % (do.day, miesiace[do.month], do.year)
                    )
                elif od.month == do.month and od.year == do.year:
                    opisy_zjazdow.append(
                        "%d-%d %s %d" % (od.day, do.day, miesiace[do.month], do.year)
                    )
                else:
                    opisy_zjazdow.append(
                        "%d %s %d - %d %s %d"
                        % (
                            od.day,
                            miesiace[od.month],
                            od.year,
                            do.day,
                            miesiace[do.month],
                            do.year,
                        )
                    )
        opisy_zjazdow_string = to_sentence(opisy_zjazdow, _("oraz"))

        invoice_summary = []

        uczestnicy_qs = termin.uczestnicy_niezrezygnowani_potwierdzeni()

        if uczestnik_id:
            uczestnicy_qs = uczestnicy_qs.filter(id__in=[uczestnik_id])

        for uczestnik in uczestnicy_qs:

            if not uczestnik.wyslac_mail_o_potwierdzeniu_terminu():
                continue

            uczestnik_summary = {
                "user": uczestnik,
                "invoice_sent": False,
                "client_created": False,
                "errors": [],
            }

            if uczestnik.email and settings.MAIL_FROM_ADDRESS_POTWIERDZENIE_TERMINU:
                invoice = None

                # Tworzymy fakturę pro-forma dla każdego Uczestnika
                # spełniającego odpowiednie kryteria i dodajemy jako załącznik
                # do maila.
                if (
                    settings.GENERATE_INVOICE_THROUGH_API_ZLICZACZ
                    and uczestnik.allowed_to_get_invoice() 
                    and termin.language == "pl"
                ):
                    if (
                        uczestnik.object_has_required_data_for_invoice()
                        and not uczestnik.nr_proformy
                        and not uczestnik.nr_faktury
                    ):

                        idata, errors = api.generate_invoice(uczestnik)

                        if idata:
                            uczestnik.zliczacz_proforma_no = idata["faktura_id"]
                            uczestnik.nr_proformy = idata["faktura_numer"]
                            uczestnik.termin_zaplaty = idata["faktura_termin_platnosci"]
                            uczestnik.save()

                            uczestnik_summary["client_created"] = idata[
                                "client_created"
                            ]

                            buffer, errors = api.get_invoice_pdf(idata["faktura_id"])

                            if not buffer:
                                uczestnik_summary["errors"].append(errors)
                            else:
                                invoice = buffer
                        else:
                            uczestnik_summary["errors"].append(errors)
                    else:
                        # Coś poszło nie tak. Uczesnik ma pozowlenie na
                        # otrzymanie faktury, a mimo to nie ma wymaganych
                        # danych do niej. Nalezy poinformować o tym fakcie
                        # Biuro.
                        if not uczestnik.object_has_required_data_for_invoice():
                            uczestnik_summary["errors"].append(
                                "Brak wymaganych danych do faktury."
                            )
                        elif uczestnik.nr_proformy or uczestnik.nr_faktury:
                            uczestnik_summary["errors"].append(
                                "Faktura już wystawiona."
                            )

                # Na wszelki wypadek, gdyz obiekt sali moze byc NULL w wyniku
                # jakis bledow.
                if termin.sala:
                    ulica_i_numer = termin.sala.get_ulica_i_numer()
                    instrukcja_dotarcia = termin.sala.get_instrukcja_dotarcia()
                else:
                    ulica_i_numer = termin.lokalizacja.ulica_i_numer
                    instrukcja_dotarcia = termin.lokalizacja.instrukcja_dotarcia

                # Budujemy słownik danych do wyrenderowania wiadomości email
                lokalizacja = "{} w {}".format(
                    ulica_i_numer, termin.lokalizacja.fullname_miejscownik
                )

                if instrukcja_dotarcia:
                    lokalizacja += " ({})".format(instrukcja_dotarcia)

                render_dict = {
                    "szkolenie": termin.szkolenie.tag_dlugosc.slug == "szkolenie",
                    "termin_nazwa": termin_nazwa,
                    "email": uczestnik.email,
                    "godziny": godziny,
                    "lokalizacja": lokalizacja,
                    "opisy_zjazdow_string": opisy_zjazdow_string,
                    "opisy_zjazdow": opisy_zjazdow,
                    "czy_raty": uczestnik.is_raty(),
                    "faktura": False if invoice is None else True,
                    "sala": termin.sala,
                }
                mesg = render_to_string(
                    "www/poinformuj_zapisanych_o_potwierdzeniu_terminu_{}.html".format(
                        szkolenie_kurs if termin.language == "pl" else "en"
                    ),
                    render_dict,
                )

                to_emails = list(
                    set(
                        [
                            uczestnik.email.lower(),
                            uczestnik.get_email_ksiegowosc().lower(),
                        ]
                    )
                )

                email = EmailMessage(
                    subject,
                    mesg,
                    settings.MAIL_FROM_ADDRESS_POTWIERDZENIE_TERMINU,
                    to_emails,
                )

                if invoice:
                    email.attach(
                        "alx-fvproforma-{}.pdf".format(uczestnik.slug()),
                        invoice.getvalue(),
                        "application/pdf",
                    )

                    uczestnik_summary["invoice_sent"] = True
                email.content_subtype = "html"
                email.send()

                uczestnik.mail_o_potwierdzeniu_terminu = timezone.now()
                uczestnik.save(update_fields=["mail_o_potwierdzeniu_terminu"])

            invoice_summary.append(uczestnik_summary)

    # Wyślij do biura podsumowanie o wysylce i statusie faktur
    if invoice_summary:
        with translation.override("pl"):
            msg_content = render_to_string(
                "www/pro_form_task_summary.html",
                {
                    "summary": invoice_summary,
                    "termin": termin,
                    "site_url": site_url_pl(),
                },
            )

            msg = EmailMessage(
                "Podsumowanie wysyłki faktur Pro-forma",
                msg_content,
                settings.MAIL_FROM_ADDRESS,
                [settings.MAIL_TERMINY],
            )
            msg.content_subtype = "html"
            msg.send()


@app.task(bind=True)
def poinformuj_trenerow_o_potwierdzeniu_terminu(self, termin_id, current_user_id):
    termin = www.models.TerminSzkolenia.objects.select_related().get(id=termin_id)

    # Jesli brak potwierdzonych to nie wysyłamy
    if not termin.uczestnicy_niezrezygnowani_potwierdzeni():
        return

    with translation.override("pl"):
        szkolenia_kursu = "kursu" if termin.is_kurs() else "szkolenia"
        subject = "Potwierdzenie {}: {} ({} {})".format(
            szkolenia_kursu,
            termin.szkolenie.nazwa,
            termin.termin,
            termin.lokalizacja.fullname,
        )
        godziny = termin.get_hours()
        miesiace = {
            1: "stycznia",
            2: "lutego",
            3: "marca",
            4: "kwietnia",
            5: "maja",
            6: "czerwca",
            7: "lipca",
            8: "sierpnia",
            9: "września",
            10: "października",
            11: "listopada",
            12: "grudnia",
        }
        tryb = {1: "dzienny", 2: "wieczorowy", 3: "zaoczny"}[termin.tryb]

        if termin.is_kurs():
            termin_nazwa = "{} (tryb {})".format(termin.szkolenie.nazwa, tryb)
        elif termin.is_weekendowy():
            termin_nazwa = "{} (tryb weekendowy)".format(termin.szkolenie.nazwa)
        else:
            termin_nazwa = termin.szkolenie.nazwa

        opisy_zjazdow = []
        if termin.is_szkolenie():
            for od, do in termin.zjazdy():
                if od.day == do.day and od.month == do.month and od.year == do.year:
                    opisy_zjazdow.append("%d.%02d" % (do.day, do.month))
                elif od.month == do.month and od.year == do.year:
                    opisy_zjazdow.append("%d-%d.%02d" % (od.day, do.day, do.month))
                else:
                    opisy_zjazdow.append(
                        "%d.%02d-%d.%02d" % (od.day, od.month, do.day, do.month)
                    )
        else:
            for od, do in termin.zjazdy():
                if od.day == do.day and od.month == do.month and od.year == do.year:
                    opisy_zjazdow.append(
                        "%d %s %d" % (do.day, miesiace[do.month], do.year)
                    )
                elif od.month == do.month and od.year == do.year:
                    opisy_zjazdow.append(
                        "%d-%d %s %d" % (od.day, do.day, miesiace[do.month], do.year)
                    )
                else:
                    opisy_zjazdow.append(
                        "%d %s %d - %d %s %d"
                        % (
                            od.day,
                            miesiace[od.month],
                            od.year,
                            do.day,
                            miesiace[do.month],
                            do.year,
                        )
                    )
        opisy_zjazdow_string = to_sentence(opisy_zjazdow, "oraz")

        konflikty = {}

        # Na wszelki wypadek, gdyz obiekt sali moze byc NULL w wyniku
        # jakis bledow.
        if termin.sala:
            ulica_i_numer = termin.sala.get_ulica_i_numer()
            sala = termin.sala.nazwa
            daty = list(termin.daty())

            potencjalne_konflikty = (
                www.models.TerminSzkolenia.objects.filter(
                    odbylo_sie=True,
                    sala=termin.sala,
                    termin_zakonczenia__gte=termin.termin,
                    termin_zakonczenia__lte=termin.termin + datetime.timedelta(days=60),
                    tryb=2,
                )
                .exclude(pk=termin.pk)
                .select_related("szkolenie")
            )

            for konflikt in potencjalne_konflikty:
                for d in list(konflikt.daty()):
                    if d in daty:
                        try:
                            konflikty[str(konflikt.szkolenie)].append(d)
                        except Exception:
                            konflikty[str(konflikt.szkolenie)] = [d]

        else:
            ulica_i_numer = termin.lokalizacja.ulica_i_numer
            sala = ""

        # Budujemy słownik danych do wyrenderowania wiadomości email
        lokalizacja = "{} w {}".format(
            ulica_i_numer, termin.lokalizacja.fullname_miejscownik
        )
        if sala:
            lokalizacja += " ({})".format(sala)

        author = User.objects.get(pk=current_user_id)

        render_dict = {
            "author": author,
            "termin": termin,
            "szkolenie": termin.szkolenie.tag_dlugosc.slug == "szkolenie",
            "termin_nazwa": termin_nazwa,
            "godziny": godziny,
            "lokalizacja": lokalizacja,
            "opisy_zjazdow_string": opisy_zjazdow_string,
            "opisy_zjazdow": opisy_zjazdow,
            "konflikty": konflikty,
            "domain": settings.DOMENY_DLA_JEZYKOW["pl"],
            "protocol": settings.FORCE_SSL and "https" or "http",
        }
        message = render_to_string(
            "www/emails/poinformuj_trenerow_o_potwierdzeniu_terminu.html", render_dict
        )

        from_email = "{0} {1} <{2}>".format(
            author.first_name, author.last_name, settings.MAIL_OBSLUGA
        )

        emails = []

        for trener in termin.pozostali_prowadzacy():
            if trener.user and trener.user.email:
                emails.append(trener.user.email)

        if (
            termin.prowadzacy
            and termin.prowadzacy.user
            and termin.prowadzacy.user.email
        ):
            emails.append(termin.prowadzacy.user.email)

        for email in list(set(emails)):
            msg = EmailMessage(
                subject, message, from_email, to=[email], bcc=[settings.MAIL_TERMINY]
            )
            msg.content_subtype = "html"
            msg.send()


@app.task(bind=True)
def poinformuj_biuro_o_potwierdzeniu_terminu(self, termin_id, current_user_id, trenerzy_uod_ids=None):
    termin = www.models.TerminSzkolenia.objects.select_related().get(id=termin_id)

    with translation.override("pl"):

        author = User.objects.get(pk=current_user_id)

        from_email = "{0} {1} <{2}>".format(
            author.first_name, author.last_name, settings.MAIL_OBSLUGA
        )

        tryb = {1: "dzienny", 2: "wieczorowy", 3: "zaoczny"}[termin.tryb]

        if termin.is_kurs():
            termin_nazwa = "{} (tryb {})".format(termin.szkolenie.nazwa, tryb)
        elif termin.is_weekendowy():
            termin_nazwa = "{} (tryb weekendowy)".format(termin.szkolenie.nazwa)
        else:
            termin_nazwa = termin.szkolenie.nazwa

        render_dict = {
            "author": author,
            "termin": termin,
            "szkolenie": termin.szkolenie.tag_dlugosc.slug == "szkolenie",
            "termin_nazwa": termin_nazwa,
            "domain": settings.DOMENY_DLA_JEZYKOW["pl"],
            "protocol": settings.FORCE_SSL and "https" or "http",
        }
        if trenerzy_uod_ids:
            prowadzacy_uod = list(www.models.Prowadzacy.objects.filter(id__in=trenerzy_uod_ids))
        else:
            prowadzacy_uod = []

            for trener in termin.pozostali_prowadzacy():
                if trener.umowa == "uod":
                    prowadzacy_uod.append(trener)

            if termin.prowadzacy and termin.prowadzacy.umowa == "uod":
                prowadzacy_uod.append(termin.prowadzacy)

        if prowadzacy_uod:
            render_dict["prowadzacy_uod"] = prowadzacy_uod

            msg = EmailMessage(
                "Termin: {}. Trenerzy z UoD".format(termin.pk),
                render_to_string("www/emails/trenerzy_z_uod.html", render_dict),
                from_email,
                to=[settings.MAIL_KSIEGOWOSC],
            )
            msg.content_subtype = "html"
            msg.send()


@app.task(bind=True)
def poinformuj_o_potencjalnie_zainteresowanych(self, termin_id):
    """
    Zadanie obecnie nie używane!
    """

    with translation.override("pl"):
        termin = www.models.TerminSzkolenia.objects.get(id=termin_id)
        today = datetime.date.today()
        if (
            termin.szkolenie.grupa_zaszeregowania
            and termin.szkolenie.grupa_zaszeregowania.get_number() in range(1, 6)
        ):
            grupa_zaszeregowania_gte_6 = False
            delta_back = today - datetime.timedelta(days=180)
        else:
            grupa_zaszeregowania_gte_6 = True
            delta_back = today - datetime.timedelta(days=270)
        szkolenie_od_terminu = termin.szkolenie
        interesujace_terminy = szkolenie_od_terminu.terminszkolenia_set.filter(
            termin__gte=delta_back
        )
        terminy_od_szkolenia_nie = interesujace_terminy.filter(odbylo_sie=False)
        terminy_od_szkolenia_tak = interesujace_terminy.exclude(id=termin_id).filter(
            odbylo_sie=True
        )
        lista_potencjalnie_zainteresowanych_miasto = []
        lista_potencjalnie_zainteresowanych_inne = []
        lista_potencjalnie_chetnych = []
        uczestnik_do_dolaczenia = ""
        for t in terminy_od_szkolenia_nie:
            for u in t.uczestnik_set.exclude(status=4):
                potencjalnie_przeszkoleni = potencjalne_przeszkolenie(
                    u, t, terminy_od_szkolenia_tak
                )
                nazwa_uczestnika = u.nazwa()
                if not u.prywatny and u.imie_nazwisko:
                    nazwa_uczestnika += (
                        ": " + re.sub(".?\n", ", ", u.imie_nazwisko) + "\n\t"
                    )
                u_status = u.get_status_display()
                if potencjalnie_przeszkoleni:
                    lista_potencjalnie_przeszkolonych = (
                        " \n\t być może przeszkolony: \n"
                        + "\n".join(potencjalnie_przeszkoleni)
                    )
                else:
                    lista_potencjalnie_przeszkolonych = ""
                link_do_uczestnika = (
                    "\n{nazwa_uczestnika} ({site_url_pl}{url_uczestnika})".format(
                        nazwa_uczestnika=nazwa_uczestnika,
                        site_url_pl=site_url_pl(),
                        url_uczestnika=reverse(
                            "admin:www_uczestnik_change", args=(u.id,)
                        ),
                    )
                )
                link_do_terminu = "{termin} ({site_url_pl}{url_terminu})".format(
                    termin=str(t.termin),
                    site_url_pl=site_url_pl(),
                    url_terminu=reverse(
                        "admin:www_terminszkolenia_change", args=(t.id,)
                    ),
                )
                uczestnik_do_dolaczenia = (
                    link_do_uczestnika
                    + ", "
                    + u_status
                    + "\n\t "
                    + link_do_terminu
                    + "\n\t "
                    + u.termin.lokalizacja.shortname
                    + lista_potencjalnie_przeszkolonych
                )

                if t.lokalizacja.shortname == termin.lokalizacja.shortname:
                    lista_potencjalnie_zainteresowanych_miasto.append(
                        uczestnik_do_dolaczenia
                    )
                else:
                    lista_potencjalnie_zainteresowanych_inne.append(
                        uczestnik_do_dolaczenia
                    )

        for p in www.models.PotencjalnyChetny.objects.filter(
            szkolenie=szkolenie_od_terminu,
            stan_sprawy__contains="aktualne",
            data_ostatniego_kontaktu__gte=delta_back,
        ):
            nazwa_uczestnika = p.imie_nazwisko
            if p.firma:
                nazwa_uczestnika += "; " + p.firma
            link_do_uczestnika = (
                "\n{nazwa_uczestnika} ({site_url_pl}{url_uczestnika})".format(
                    nazwa_uczestnika=nazwa_uczestnika,
                    site_url_pl=site_url_pl(),
                    url_uczestnika=reverse(
                        "admin:www_potencjalnychetny_change", args=(p.id,)
                    ),
                )
            )
            uczestnik_do_dolaczenia = (
                ", ".join([link_do_uczestnika, p.email, p.telefon]) + "\n\t " + p.uwagi
            )
            lista_potencjalnie_chetnych.append(uczestnik_do_dolaczenia)

        render_dict = {
            "termin": termin,
            "miasto": termin.lokalizacja.shortname,
            "kod": szkolenie_od_terminu.kod,
            "chetnych_miasto": mark_safe(
                "\n".join(lista_potencjalnie_zainteresowanych_miasto)
            ),
            "potencjalnie_chetnych": mark_safe("\n".join(lista_potencjalnie_chetnych)),
        }

        if grupa_zaszeregowania_gte_6:
            render_dict["chetnych_inne"] = mark_safe(
                "\n".join(lista_potencjalnie_zainteresowanych_inne)
            )

        mesg = render_to_string("www/potencjalnie_zainteresowani.txt", render_dict)
        subject = (
            "Zawiadomić o szkoleniu potencjalnie zainteresowanych"
            + ", "
            + szkolenie_od_terminu.kod
        )
        if uczestnik_do_dolaczenia:
            send_mail(subject, mesg, settings.MAIL_FROM_ADDRESS, [settings.MAIL_ADMINI])


def potencjalne_przeszkolenie(u1, termin_nie, terminy_odbyte):
    ff1 = www.models.Uczestnik.normalizuj_dla_levenshtein(u1.nazwa())
    matches = []

    for t in terminy_odbyte:
        link_do_terminu = "{termin} ({site_url_pl}{url_terminu})".format(
            termin=t.termin,
            site_url_pl=site_url_pl(),
            url_terminu=reverse("admin:www_terminszkolenia_change", args=(t.id,)),
        )
        for u2 in t.uczestnik_set.all():
            if u1 == u2:
                continue
            ff2 = www.models.Uczestnik.normalizuj_dla_levenshtein(u2.nazwa())
            jaro = Levenshtein.jaro(ff1, ff2)
            if jaro > 0.95 or not u1.prywatny and jaro > 0.8:
                nazwa_uczestnika = u2.nazwa()
                if not u2.prywatny and u2.imie_nazwisko:
                    nazwa_uczestnika += (
                        ": " + re.sub(".?\n", ", ", u2.imie_nazwisko) + "\n" + "\t" * 5
                    )
                link_do_uczestnika = (
                    "{nazwa_uczestnika} ({site_url_pl}{url_uczestnika})".format(
                        nazwa_uczestnika=nazwa_uczestnika,
                        site_url_pl=site_url_pl(),
                        url_uczestnika=reverse(
                            "admin:www_uczestnik_change", args=(u2.id,)
                        ),
                    )
                )
                matches.append(
                    [
                        jaro,
                        "\t" * 4
                        + link_do_uczestnika
                        + "\n"
                        + "\t" * 5
                        + link_do_terminu
                        + "\n"
                        + "\t" * 5
                        + t.lokalizacja.shortname,
                    ]
                )
    return [x[1] for x in sorted(matches, key=lambda a: a[0], reverse=True)]


@app.task(bind=True)
def poinformuj_o_potencjalnie_chetnych(self, termin_id):
    """
    Zadanie wysyła do biura listę użytkowników, którzy mogą być zainteresowani
    uruchomionym szkoleniem. Dopasowanie odbywa się przy użyciu modułu
    Powiadomieniń.
    """

    chosen_in_prefered_location = []
    chosen_in_other_locations = []

    # Służy do uwzględniania szkoleń, zakończonych mniej jak 7 dni temu.
    seven_days_earlier = datetime.date.today() - datetime.timedelta(days=7)

    termin = www.models.TerminSzkolenia.objects.get(id=termin_id)
    training = termin.szkolenie

    # Pobieramy użytkowników, którzy:
    #  - są przypisani do powiadomień szkolenia `training`
    #  - ich status to: zweryfikowany
    #  - ich źródło rejestracji to: "strona www", "automat",
    #    "dodany przez Biuro"

    users = (
        www.models.UserNotification.objects.get_active()
        .filter(preferences__training=training)
        .order_by("-phone_number")
    )

    # Sprawdzamy, czy osoba nie jest czasem przyszłym uczestnikiem dowolnego
    # terminu danego szkolenia. Jeśli tak omijamy to szkolenie
    # (z wyjątekim zrezygnowanych lub gdy szkolenie się nie odbyło)
    #
    # Uwaga: Z racji tego, że BOK nie od razu zmienia statusy
    # Uczestników i Szkoleń, uwzględniamy też szkolenia zakończone
    # mniej jak 7 dni temu.

    for user in users:
        already_in = (
            www.models.Uczestnik.objects.filter(
                email__iexact=user.email,
                termin__szkolenie=training,
            )
            .filter(
                Q(termin__termin__gte=seven_days_earlier)
                | Q(
                    termin__termin_zakonczenia__isnull=False,
                    termin__termin_zakonczenia__gte=seven_days_earlier,
                )
            )
            .exclude(Q(status=4) | Q(termin__odbylo_sie=False))
            .count()
        )

        if already_in:
            continue

        # Sortujemy uzytkownikow na dwie grupy:
        # 1. pasujących do lokalizacji szkolenia
        # 2. z wszystkich innych lokalizacji

        try:
            user_course = www.models.UserCoursesNotification.objects.get(
                training=training, user=user
            )
        except www.models.UserNotification.DoesNotExist:
            # Widocznie w miedzyczasie zostal usuniety
            pass
        else:
            locations = user_course.locations.all()

            if termin.lokalizacja.pk in [i.pk for i in locations]:
                chosen_in_prefered_location.append(user)
            else:
                chosen_in_other_locations.append({"user": user, "locations": locations})

    if chosen_in_prefered_location or chosen_in_other_locations:
        with translation.override("pl"):
            # Wysyłamy maila
            msg_content = render_to_string(
                "www/potencjalnie_chetni.html",
                {
                    "chosen_in_prefered_location": chosen_in_prefered_location,
                    "chosen_in_other_locations": chosen_in_other_locations,
                    "termin": termin,
                    "domain": settings.DOMENY_DLA_JEZYKOW["pl"],
                    "protocol": settings.FORCE_SSL and "https" or "http",
                },
            )

            msg = EmailMessage(
                "[Potencjalnie chętni] - {0}".format(training.nazwa),
                msg_content,
                settings.MAIL_FROM_ADDRESS,
                [settings.MAIL_POTENCJALNIE_CHETNI],
            )
            msg.content_subtype = "html"
            msg.send()


@app.task(bind=True)
def potencjalnie_zainteresowani_z_innych_miast(self, termin_id):
    """
    Zadanie wysyła do Uczestników zapisanych na powiadomienie informację o
    potwierdzonym terminie. Wybierani są tylko Uczestnicy, którzy nie mają
    ustawionej preferencji na to miasto. Czyli de facto działamy na odwrót
    niż w notyfikacjach (odwrotne dopasowanie).
    """

    term = www.models.TerminSzkolenia.objects.get(
        pk=termin_id, odbylo_sie=True, termin__gt=datetime.date.today()
    )

    if term.language == "en":
        mail_from = settings.MAIL_FROM_ADDRESS_NOTIFICATION_EN
    else:
        mail_from = settings.MAIL_FROM_ADDRESS_NOTIFICATION

    users = (
        www.models.UserNotification.objects.potencjalnie_zainteresowani_z_innych_miast(
            term
        )
    )

    with translation.override(term.language):
        for user in users:
            try:
                msg_content = render_to_string(
                    "www/emails/potencjalnie_zainteresowani_z_innych_miast_"
                    "{0}.html".format(term.language),
                    {
                        "term": term,
                        "training": term.szkolenie,
                        "custom_content": term.potencjalnie_zainteresowani_mail,
                        "user": user,
                        "domain": settings.DOMENY_DLA_JEZYKOW[term.language],
                        "protocol": settings.FORCE_SSL and "https" or "http",
                    },
                )

                msg = EmailMessage(
                    _("Powiadomienie z firmy ALX (www.alx.pl)"),
                    msg_content,
                    mail_from,
                    [user.email],
                )
                msg.content_subtype = "html"
                msg.send()
            except:
                logger.exception(
                    "Błąd przy wysyłce powiadomienia "
                    "'potencjalnie_zainteresowani_z_innych_miast' dla "
                    "użytkownika ID: {0} ({1})".format(user.pk, user.email)
                )
            else:
                # Dodajemy log do bazy
                www.models.UserNotificationLog.objects.get_or_create(
                    user=user,
                    term=term,
                    location=term.lokalizacja,
                    notification_type="other_locations",
                )


@app.task(bind=True)
def autoresponder(self, email, topic, language, email_data, msg_subject=None):
    """
    Zadanie wysyła auto-odpowiedź pod adres 'email` po wypełnieniu formularza
    na stronie.
    """

    email = email.lower()

    if not www.models.AutoresponderLog.objects.allow_to_send_autoreplay(email):
        logger.error(
            "Wykorzystany limit autoodpowiedzi "
            "dla: {0}. Topic: {1}".format(email, topic)
        )
        return

    with translation.override(language):
        # Wysyłamy maila
        msg_content = render_to_string(
            "www/emails/autoresponder_{0}_{1}.html".format(topic, language),
            {
                "domain": settings.DOMENY_DLA_JEZYKOW[language],
                "protocol": settings.FORCE_SSL and "https" or "http",
                "email_data": email_data,
            },
        )

        if msg_subject:
            subject = msg_subject
        else:
            subject = (
                "Dostaliśmy twój formularz"
                if language == "pl"
                else "We received your request"
            )

        msg = EmailMessage(
            subject,
            msg_content,
            settings.MAIL_FROM_ADDRESS_AUTOREPLAY
            if language == "pl"
            else settings.MAIL_FROM_ADDRESS_AUTOREPLAY_EN,
            [email],
        )
        msg.content_subtype = "html"
        msg.send()

    www.models.AutoresponderLog.objects.create(receiver_email=email, email_type=topic)


##################
# E-certyfikaty
##################


@transaction.atomic
def generate_pdf_and_send_email(
    certificate_id, force_create_pdf=True, force_send_mail=True
):
    """
    Funkcja odpowiada za wygenerowanie certyfikatu PDF dla uczestnika szkolenia
    oraz wysłanie maila z linkiem do strony certyfikatu. Zadaniami można
    sterować za pomocą `force_create_pdf` oraz `force_send_mail`.
    """

    certificate = www.models.Graduate.objects.select_related().get(pk=certificate_id)

    training_term = certificate.term
    training = training_term.szkolenie

    with translation.override(training.language):
        if force_create_pdf:
            # Utwórz szablon dla pliku PDF oraz sam plik
            data = {
                "certificate": certificate,
                "term": training_term,
                "term_date": training_term.date_range(),
                "term_days": len(list(training_term.daty())),
                "language": training.language,
            }

            pdf = generate_pdf(
                template_content=training_term.get_szablon_certyfikatu(), data=data
            )

            # Blokujemy wiersz
            certificate = (
                www.models.Graduate.objects.select_for_update()
                .select_related()
                .get(pk=certificate_id)
            )

            certificate.pdf.save("{0}.pdf".format(certificate.key.hex), pdf, save=False)
            certificate.save()

        if force_send_mail:
            # Wyślij email
            lang = training.language

            msg = render_to_string(
                "www/certificates/certificate_email_{0}.html".format(lang),
                {
                    "training_term": training_term,
                    "training": training,
                    "certificate": certificate,
                    "language": lang,
                    "domain": settings.DOMENY_DLA_JEZYKOW[lang],
                    "protocol": settings.FORCE_SSL and "https" or "http",
                },
            )

            if lang == "pl":
                subject = "Certyfikat ukończenia {0}".format(
                    "szkolenia" if training_term.is_szkolenie() else "kursu"
                )
            else:
                subject = "Your Certificate of Course Completion"

            email = EmailMessage(
                subject, msg, settings.MAIL_FROM_ADDRESS, [certificate.email]
            )
            email.content_subtype = "html"
            email.send()

            # Uaktualnij informacje o wysłanym mailu (data i licznik wysłanych
            # maili).
            www.models.Graduate.objects.select_for_update().filter(
                pk=certificate_id
            ).update(
                mail_sent_at=timezone.now(),
                mail_sent_counter=F("mail_sent_counter") + 1,
            )


@app.task(bind=True)
def create_certificate(self, graduate_id, force_create_pdf=True, force_send_mail=True):
    """
    Zadanie wywołuje funkcję tworzącą plik PDF oraz wysyłającą mail.
    """

    generate_pdf_and_send_email(
        graduate_id, force_create_pdf=force_create_pdf, force_send_mail=force_send_mail
    )


@app.task(bind=True)
def update_certificate(
    self, certificate_id, force_create_pdf=False, force_send_mail=True
):
    """
    Zadanie pozwala na ponowne wygenerowanie certyfikatu (plik PDF) oraz
    wysłanie maila.
    Domyślnie generowanie certfikatu zostało wyłączone.
    """

    generate_pdf_and_send_email(
        certificate_id=certificate_id,
        force_create_pdf=force_create_pdf,
        force_send_mail=force_send_mail,
    )


@app.task(bind=True)
def debug_send_test_email(self, address):
    """
    Zadanie wysyła maila w tle, służy do sprawdzania czy Celery
    działa poprawnie.
    """

    logger.warning("Sending an email ....")
    send_mail(
        "[ALXCRM] Celery test message", "OK", settings.MAIL_FROM_ADDRESS, [address]
    )


def notification_qs_to_dict(queryset):
    out = []
    for element in queryset:
        out.append(
            dict(id=element.id, locations=[loc.id for loc in element.locations.all()])
        )
    return out


@app.task(bind=True)
def user_notifications_alert(self, user_id, old_query_list=None, new_query_list=None):
    """
    Zadanie wysyła maila do Biura zawierającego szkolenia i terminy
    uwzględnione w nowych lub edytowanych notyfikacjach użytkownika.

    Przykładowy email:

        Subject: [Powiadomienie Zapis] - Administrator Linuksa (Warszawa)

        Nowy zapis na powiadomienie:

        Email: <EMAIL>

        Szkolenie: Administrator Linuksa (Warszawa)
        Zapisanych ma powiadomienia: 4
        Zapisanych ma powiadomienia (unikalni względem poniższych terminów): 3

        Aktywne terminy dla tego szkolenia:

        Warszawa (2015-02-01): 6/10
        Warszawa (2015-02-15): 3/10
        Warszawa (2015-02-19): 0/10

        UWAGA: Rozważ "ręczne" skontaktowanie się z zapisanymi na
               powiadomienia, być może jest grupa do zebrania!

        Szkolenie: Administrator Linuksa (Łódź)
        Zapisanych ma powiadomienia: 4
        Zapisanych ma powiadomienia (unikalni względem poniższych terminów): 3

        Aktywne terminy dla tego szkolenia:

        Łódź (2015-02-01): 6/10
        Łódź (2015-02-15): 3/10
        Łódź (2015-02-19): 0/10

        UWAGA: Rozważ "ręczne" skontaktowanie się z zapisanymi na
               powiadomienia, być może jest grupa do zebrania!
    """
    user = www.models.UserNotification.objects.get(id=user_id)
    if not settings.MAIL_TO_NOTIFICATION_ALERT or "donotsendthis" in user.email:
        return

    today = datetime.date.today()

    if not old_query_list and not new_query_list:
        return

    notifications = {}

    if old_query_list:
        # Tutaj mamy sytuację, w której nastąpiła edycja powiadomień
        # użytkownika `user`. W `old_query` mamy informację przed zmianą
        # danych, więc teraz musimy sprawdzić co zostało zmienione.
        # Nie bierzemy pod uwagę usuniętych notyfikacji i lokalizacji
        # - jedynie dodanie nowych.

        for old_course in old_query_list:
            # Sprawdzamy, czy ten kurs jeszcze istnieje (użytkownik mógł go
            # usunąć)

            try:
                course = (
                    www.models.UserCoursesNotification.objects.filter(user=user)
                    .select_related("training", "user")
                    .prefetch_related("locations")
                    .get(pk=old_course["id"])
                )
            except www.models.UserCoursesNotification.DoesNotExist:
                # Najwyraźniej powiadomienie zostało usunięte - nic z tym nie
                # robimy.
                continue

            if course.source != "www":
                # Bieżemy pod uwagę tylko zapisy dokonane świadomie przez
                # użytkownika.
                continue

            # Relacja jest w `prefetch_related`, więc mamy pewność, że dane
            # nie są zaciągane w tym momencie.

            old_locations = [l for l in old_course["locations"]]

            for location in course.locations.all():
                if location.pk not in old_locations:
                    # Ta lokalizacja została dodana - musimy o tym poinformować
                    # Biuro.
                    if course.training.pk in notifications:
                        notifications[course.training.pk]["locations"].append(location)
                    else:
                        notifications[course.training.pk] = {
                            "training": course.training,
                            "locations": [location],
                        }
    elif new_query_list:
        # Tutaj mamy prostą sytuację - użytkownik dodał nowe szkolenie/a do
        # swoich powiadomień.
        for new_course in new_query_list:
            try:
                course = (
                    www.models.UserCoursesNotification.objects.filter(user=user)
                    .select_related("training", "user")
                    .prefetch_related("locations")
                    .get(pk=new_course["id"])
                )
            except www.models.UserCoursesNotification.DoesNotExist:
                # Najwyraźniej powiadomienie zostało w międzyczasie usunięte -
                # nic z tym nie robimy.
                continue

            if course.source != "www":
                # Bieżemy pod uwagę tylko zapisy dokonane świadomie przez
                # użytkownika.
                continue

            notifications[course.training.pk] = {
                "training": course.training,
                "locations": course.locations.all(),
            }

    if notifications:
        # Budujemy content maila/maili dla Biura. Jeden mail może zawierać
        # tylko jedno szkolenie w jednej lub wiecej lokalizacjach.

        for data in list(notifications.values()):
            # Zmienna `data` zawiera obiekt/y lokalizacji oraz obiekt
            # szkolenia.

            data_per_location = []

            locations = data["locations"]
            training = data["training"]

            # Zbieramy statystyki per lokalizacja:
            for location in locations:
                # Pobieramy liczbę wszystkich zapisanych na powiadomienia
                # danego szkolenia `training` w lokalizacji `location`.
                already_subscribed = (
                    www.models.UserNotification.objects.filter(
                        status=1,
                        preferences__training=training,
                        preferences__locations__in=[location.pk],
                    )
                    .distinct()
                    .count()
                )

                # Początkowa struktura danych do treści maila
                terms_data = []

                # Teraz pobieramy wszystkie przyszłe terminy dla szkolenia
                # `training` w lokalizacji `location`.

                terms = www.models.TerminSzkolenia.objects.filter(
                    szkolenie=training,
                    lokalizacja=location,
                    zamkniete=False,
                    termin__gte=today,
                ).prefetch_related("uczestnik_set")

                # Teraz może się zdarzyć tak, że Uczestnik jest jednocześnie
                # zapisany na powiadomienie i termin. Dlatego musimy stworzyć
                # unikalną liczbę użytkowników zapisanych na powiadomienia
                # wykluczając ew. zapis na termin.

                excluded_emails = []

                for term in terms:
                    for obj in term.uczestnicy_niezrezygnowani():
                        if obj.email:
                            excluded_emails.append(obj.email.lower())

                excluded_emails = list(set(excluded_emails))

                already_subscribed_unique = (
                    www.models.UserNotification.objects.filter(
                        status=1,
                        preferences__training=training,
                        preferences__locations__in=[location.pk],
                    )
                    .exclude(email__in=excluded_emails)
                    .distinct()
                    .count()
                )

                for term in terms:
                    # Zbieramy szczegółowe informacje dla każdego terminu.

                    participants_count = len(term.uczestnicy_niezrezygnowani())
                    potential_participants = (
                        participants_count + already_subscribed_unique
                    )

                    # Sprawdzamy, czy ten termin osiągnął wymaganą liczbę
                    # uczestników potrzebnych do uruchomienia terminu.
                    min_group_reached = (
                        potential_participants >= training.min_grupa
                        if training.min_grupa
                        else False
                    )

                    terms_data.append(
                        {
                            "term": term,
                            "participants_count": participants_count,
                            "potential_participants": potential_participants,
                            "min_group_reached": min_group_reached,
                        }
                    )

                data_per_location.append(
                    {
                        "location": location,
                        "data": {
                            "terms": terms_data,
                            "location": location,
                            "already_subscribed": already_subscribed,
                            "already_subscribed_unique": already_subscribed_unique,
                            "min_group_reached": any(
                                [v["min_group_reached"] for v in terms_data]
                            ),
                        },
                    }
                )

            # Wysyłamy maila

            with translation.override("pl"):
                msg_content = render_to_string(
                    "www/notifications/user_notifications_alert.html",
                    {
                        "user": user,
                        "training": training,
                        "data_per_location": data_per_location,
                        "domain": settings.DOMENY_DLA_JEZYKOW["pl"],
                        "protocol": settings.FORCE_SSL and "https" or "http",
                    },
                )

                msg = EmailMessage(
                    "[Powiadomienie Zapis] - {0}".format(training.nazwa),
                    msg_content,
                    settings.MAIL_FROM_ADDRESS,
                    [settings.MAIL_TO_NOTIFICATION_ALERT],
                    [settings.MAIL_ZGLOSZENIE_TO_ADDRESS],
                )
                msg.content_subtype = "html"
                msg.send()


@app.task(bind=True)
def termin_szkolenia_mail_do_grupy(self, pk):
    term_mail = www.models.TerminSzkoleniaMail.objects.select_related(
        "author", "term"
    ).get(pk=pk)

    if term_mail.participant_status == 99:
        users = term_mail.term.uczestnicy_niezrezygnowani()
    else:
        users = term_mail.term.uczestnik_set.filter(status=term_mail.participant_status)

    with translation.override(term_mail.term.language):
        for user in users:
            if user.email:
                (
                    obj,
                    created,
                ) = www.models.TerminSzkoleniaMailUczestnik.objects.get_or_create(
                    participant=user, term_mail=term_mail
                )

                if created:
                    from_email = "{0} {1} <{2}>".format(
                        term_mail.author.first_name,
                        term_mail.author.last_name,
                        settings.MAIL_OBSLUGA,
                    )

                    msg = EmailMessage(
                        term_mail.subject,
                        term_mail.message,
                        from_email,
                        to=[user.email],
                        bcc=[settings.MAIL_ARCHIVE_MONITOR],
                    )
                    msg.content_subtype = "html"

                    if term_mail.attachment:
                        msg.attach_file(term_mail.attachment.path)

                    try:
                        msg.send()
                    except Exception:
                        logger.exception(
                            "Could not send termin_szkolenia_mail_do_grupy. "
                            "User: {}".format(user.pk)
                        )
                        continue
                    obj.sent_at = timezone.now()
                    obj.save()


@app.task(bind=True)
def participant_file_alert(self, file_id):
    obj = www.models.UczestnikPlik.objects.select_related("participant").get(pk=file_id)
    participant = obj.participant

    with translation.override("pl"):
        msg_content = render_to_string(
            "www/emails/participant_file_alert.html",
            {
                "participant": participant,
                "domain": settings.DOMENY_DLA_JEZYKOW["pl"],
                "protocol": settings.FORCE_SSL and "https" or "http",
            },
        )

        name = (
            normalize_newlines(strip_tags(participant.nazwa_oneline()))
            .replace("\n", "")
            .strip()
        )

        msg = EmailMessage(
            "[Nowy plik od Uczestnika] - {}".format(name),
            msg_content,
            settings.MAIL_FROM_ADDRESS,
            [settings.MAIL_TERMINY],
        )
        msg.content_subtype = "html"
        msg.send()


@app.task(bind=True)
def user_lead(self, lead_id, language):
    lead = LeadUser.objects.get(pk=lead_id)

    with translation.override("pl"):
        msg_content = render_to_string(
            "leads/emails/new_lead.html",
            {
                "lead": lead,
                "domain": settings.DOMENY_DLA_JEZYKOW["pl"],
                "protocol": settings.FORCE_SSL and "https" or "http",
            },
        )

        msg = EmailMessage(
            "[Nowy lead] - {}".format(lead.email),
            msg_content,
            settings.MAIL_FROM_ADDRESS,
            [settings.MAIL_OBSLUGA],
        )
        msg.content_subtype = "html"
        msg.send()
