from django import forms
from django.forms import widgets
from django.forms.utils import flatatt
from django.utils.encoding import force_text
from django.utils.safestring import mark_safe
from django_ace import AceWidget


class SelectWithDataAttr(widgets.Select):
    def create_option(self, *args, **kwargs):
        option_data = super().create_option(*args, **kwargs)
        if option_data["value"] is None:
            option_data["value"] = ""
        option_data["value"] = force_text(option_data["value"])
        if option_data["selected"]:
            option_data["attrs"]["selected"] = "selected"
            if not self.allow_multiple_selected:
                option_data["selected"] = False
        data_attrs = {}
        if isinstance(option_data["label"], dict):
            option_label_text = option_data["label"].pop("label")
            for key, value in list(option_data["label"].items()):
                data_attrs["data-" + key] = value

            option_data["attrs"].update(data_attrs)
            option_data["label"] = option_label_text

        return option_data


class DifferentTextarea(forms.Textarea):
    class Media:
        css = {"all": ("css/largetextarea.css",)}

    def __init__(self, attrs=None):
        final_attrs = {"class": self.extra_class}
        if attrs is not None:
            final_attrs.update(attrs)
        super().__init__(attrs=final_attrs)


class LargeTextarea(DifferentTextarea):
    extra_class = "LargeTextarea"


class SmallTextarea(DifferentTextarea):
    extra_class = "SmallTextarea"


class NettoBruttoInput(widgets.Input):
    input_type = "text"

    class Media:
        js = [
            "custom_admin/js/brutto.js",
        ]

    def render(self, name, value, attrs=None, renderer=None):
        if value is None:
            value = ""
        final_attrs = self.build_attrs(
            attrs, extra_attrs={"type": self.input_type, "name": name}
        )
        if value != "":
            # Only add the 'value' attribute if a value is non-empty.
            final_attrs["value"] = force_text(self.format_value(value))
        return mark_safe(
            '<input%s class="cena_netto" />' % flatatt(final_attrs)
            + '<input type="text" id="%s_brutto" />' % final_attrs["id"]
        )


class MultipleDatePicker(widgets.Input):
    class Media:
        css = {
            "all": (
                "custom_admin/css/jquery-ui-lightness.1.12.1/jquery-ui.css",
                "custom_admin/css/jquery-ui.multidatespicker.css",
            ),
        }
        js = (
            "admin/js/jquery.init.js",
            "custom_admin/js/jquery-ui.1.12.1.min.js",
            "custom_admin/js/jquery-ui.multidatespicker.js",
        )

    input_type = "text"

    def render(self, name, value, attrs=None, renderer=None):
        if value is None:
            value = ""
        final_attrs = self.build_attrs(
            attrs, extra_attrs={"type": self.input_type, "name": name}
        )
        dates_string = ""
        default_date_string = ""
        if value != "":
            # Only add the 'value' attribute if a value is non-empty.
            final_attrs["value"] = force_text(self.format_value(value))
            dates = value.split(",")
            dates_string = (
                "addDates: [" + ",".join(["'%s'" % x.strip() for x in dates]) + "],"
            )
            default_date_string = "defaultDate: '" + dates[0] + "',"
        return mark_safe(
            '<input%s class="" size="130" />' % flatatt(final_attrs)
            + """
<div id="%(id)s-container" style="clear: both; margin-left: 106px;"></div>
<script>
$(function() {
    $('#%(id)s-container').multiDatesPicker({
        dateFormat: 'yy-mm-dd',
        altField: '#%(id)s',
        %(dates_string)s
        %(default_date_string)s
        firstDay: 1,
        separator: ","
    });
});
</script>
"""
            % {
                "id": final_attrs["id"],
                "dates_string": dates_string,
                "default_date_string": default_date_string,
            }
        )


class CustomAceWidget(AceWidget):
    @property
    def media(self):
        js = [
            "django_ace/ace/ace.js",
            "custom_admin/js/django_ace.js",
        ]
        if self.mode:
            js.append("django_ace/ace/mode-%s.js" % self.mode)
        if self.theme:
            js.append("django_ace/ace/theme-%s.js" % self.theme)
        css = {
            "screen": ["django_ace/widget.css"],
        }
        return forms.Media(js=js, css=css)


class CityPostalCodeWidget(widgets.MultiWidget):
    def __init__(self, attrs=None):

        widget = (
            widgets.TextInput(attrs=attrs),
            widgets.TextInput(attrs=attrs),
        )
        super().__init__(widgets=widget, attrs=attrs)

    def decompress(self, value):
        if value:
            return value
        return [None, None]

    def format_output(self, rendered_widgets):
        return "".join(rendered_widgets)

    pass
