import datetime
import json
import logging
from io import BytesIO

import requests
from django.conf import settings

logger = logging.getLogger(__name__)


def generate_invoice_installments(user):
    """
    Funkcja tworzy fakturę VAT z ratami po stronie aplikacji
    zliczacz za pomocą API. Wszystkie dane zawarte są w obiekcie uczestnika
    `user`.
    """

    today = datetime.date.today()

    # Po lewej stronie mamy stawkę VAT, a po prawej jej odpowiednik w
    # bazie (czyli ID w bazie zliczacz) i właśnie tą wartość (ID) wysyłamy
    # przez API.
    vat_id = {
        0: 5,
        8: 7,
        23: 8,
    }[int(user.stawka_vat * 100)]

    status_faktury = 2
    faktura_data_zaplaty = None

    if user.za_kurs_zaplace == 2:
        # reszta
        raty = list(range(1, 2))
    elif user.za_kurs_zaplace == 3:
        # 2 raty
        raty = list(range(1, 3))
        raty_slownie = {
            1: "I",
            2: "II",
        }
    else:
        # 5 rat
        raty = list(range(1, 5))
        raty_slownie = {
            1: "II",
            2: "III",
            3: "IV",
            4: "V",
        }

    pozycje_faktury = []
    sposoby_platnosci = []

    for no, nr_raty in enumerate(raty, start=1):
        kwota = getattr(user, "rata{0}_kwota".format(nr_raty))
        termin = getattr(user, "rata{0}_termin".format(nr_raty))
        zaplacone = getattr(user, "rata{0}_zaplacone".format(nr_raty), None)

        if user.za_kurs_zaplace == 2:
            if zaplacone:
                status_faktury = 4
                faktura_data_zaplaty = zaplacone.isoformat()
            pozycja_opis = user.tytul_do_faktury_raty()
        else:
            pozycja_opis = user.tytul_do_faktury_raty() + " - {} rata".format(
                raty_slownie[nr_raty]
            )
            if zaplacone:
                sposoby_platnosci.append(
                    "{} rata zapłacono {}".format(
                        raty_slownie[nr_raty], zaplacone.isoformat()
                    )
                )
            else:
                sposoby_platnosci.append(
                    "{} rata do zapłaty w terminie do {}".format(
                        raty_slownie[nr_raty], termin.isoformat()
                    )
                )

        pozycja_cena_jednostkowa_netto = user.cena_rata(kwota)
        pozycja_wartosc_netto = user.cena_rata(kwota)
        pozycja_kwota_vat = user.kwota_vat_rata(kwota)
        pozycja_wartosc_brutto = user.cena_brutto_rata(kwota)

        pozycje_faktury.append(
            {
                "pozycja_opis": pozycja_opis,
                "pozycja_cena_jednostkowa_netto": str(pozycja_cena_jednostkowa_netto),
                "pozycja_wartosc_netto": str(pozycja_wartosc_netto),
                "pozycja_kwota_vat": str(pozycja_kwota_vat),
                "pozycja_wartosc_brutto": str(pozycja_wartosc_brutto),
                "pozycja_liczba_jednostek": "1",  # fixed
                "stawka_vat": vat_id,
                "pozycja_lp": no,
                "pozycja_zaliczka": False,
            }
        )

        # Z ostatniej raty
        faktura_termin_platnosci = termin.isoformat()

    # Jeśli Uczestnik jest zwolniony z VAT w opisie faktury należy napisać
    # dlaczego.

    faktura_sposob_platnosci = ", ".join(sposoby_platnosci)

    faktura_uwagi = ""

    if vat_id == 5:
        if user.podmiot_publiczny:
            faktura_uwagi += (
                "usługa zwolniona z VAT na podstawie art. "
                "43 ust. 1 pkt 29 lit. c ustawy o VAT"
            )
        elif user.termin.is_posiada_akredytacje():
            faktura_uwagi += (
                "usługa zwolniona z VAT na podstawie art. "
                "43 ust. 1 pkt 29 lit. b ustawy o VAT"
            )
        else:
            logger.warning(
                "Uczestnik ID: {0} ({1}) jest zwolniony z VAT, "
                "ale brak podstawy.".format(user.pk, user.tytul_do_faktury())
            )

    # Tworzymy słownik danych
    data = {
        "faktura_typ_daty": 1,  # DATA_WYKONANIA_USLUGI
        "faktura_sprzedawca": 1669,  # fixed
        "typ_faktury": 1,
        "status_faktury": status_faktury,
        "faktura_data_wystawienia": user.faktura_raty_data_wystawienia.isoformat()
        if user.faktura_raty_data_wystawienia
        else today.isoformat(),
        "faktura_data_sprzedazy": user.faktura_raty_data_wykonania_uslugi.isoformat()
        if user.faktura_raty_data_wykonania_uslugi
        else user.termin.termin.isoformat(),
        "faktura_ostatnia_zmiana": datetime.datetime.now().isoformat(),
        "faktura_termin_platnosci": faktura_termin_platnosci,
        "faktura_data_zaplaty": faktura_data_zaplaty,
        "faktura_wystawiona": True,  # fixed
        "faktura_nabywca": user.dane_do_faktury(),
        "faktura_uwagi": faktura_uwagi,
        "pozycje_faktury": pozycje_faktury,
        "faktura_sposob_platnosci": faktura_sposob_platnosci,
    }

    # Wysyłamy dane do zliczacz
    try:
        response = requests.post(
            settings.API_ZLICZACZ["INVOICES"]["ADD_INVOICE_URL"],
            data=json.dumps(data, ensure_ascii=False).encode("utf-8"),
            auth=(
                settings.API_ZLICZACZ["CREDENTIALS"]["USERNAME"],
                settings.API_ZLICZACZ["CREDENTIALS"]["PASSWORD"],
            ),
            headers={"Content-type": "application/json"},
        )
    except:
        logger.exception("Nie mozna utworzyc faktury.")
        return None, "Nie mozna utworzyc faktury"

    # Wyniki funkcji to dane obiektu i ew. błąd komunikacji: (id, errros)
    if response.status_code == 201:
        return response.json(), None

    error = response.text or "Nie mozna utworzyc faktury"

    logger.warning("Nie mozna utworzyc faktury: {0}".format(error))
    return None, error


def generate_invoice(user, proforma=True):
    """
    Funkcja tworzy fakturę VAT/pro-forma/VAt zaliczkowa po stronie aplikacji
    zliczacz za pomocą API. Wszystkie dane zawarte są w obiekcie uczestnika
    `user`.
    """

    today = datetime.date.today()

    # Po lewej stronie mamy stawkę VAT, a po prawej jej odpowiednik w
    # bazie (czyli ID w bazie zliczacz) i właśnie tą wartość (ID) wysyłamy
    # przez API.
    vat_id = {
        0: 5,
        8: 7,
        23: 8,
    }[int(user.stawka_vat * 100)]

    faktura_uwagi = ""

    # Jeśli Uczestnik jest zwolniony z VAT w opisie faktury należy napisać
    # dlaczego.
    if vat_id == 5:
        if user.podmiot_publiczny:
            faktura_uwagi = (
                "usługa zwolniona z VAT na podstawie art. "
                "43 ust. 1 pkt 29 lit. c ustawy o VAT"
            )
        elif user.termin.is_posiada_akredytacje():
            faktura_uwagi = (
                "usługa zwolniona z VAT na podstawie art. "
                "43 ust. 1 pkt 29 lit. b ustawy o VAT"
            )
        else:
            logger.warning(
                "Uczestnik ID: {0} ({1}) jest zwolniony z VAT, "
                "ale brak podstawy.".format(user.pk, user.tytul_do_faktury())
            )

    # Opis pozycji
    if user.faktura_zaliczkowa:
        pozycja_opis = user.tytul_do_faktury_zaliczkowej()
    else:
        pozycja_opis = user.tytul_do_faktury()

    #
    # 08.11.2017 - rezygnacja z zaliczek!
    #
    # Czy pozycja ma byc traktowana jako zaliczka?
    # if user.faktura_zaliczkowa:  # and not user.zaplacono_pierwsza_rate():
    #     pozycja_zaliczka = True
    # else:
    #     pozycja_zaliczka = False
    pozycja_zaliczka = False

    # `typ_faktury`:
    #     1 - faktura zwykła
    #     2 - faktura proforma
    if proforma:
        typ_faktury = 2
    else:
        typ_faktury = 1

    # Daty i statusy do faktury zaliczkowej
    if user.faktura_zaliczkowa:
        faktura_data_sprzedazy = today.isoformat()

        if not proforma and user.zaliczka_zaplacone:
            # Faktura "zaplacona"
            status_faktury = 4
            faktura_typ_daty = 1  # DATA_WYKONANIA_USLUGI
            faktura_data_zaplaty = user.zaliczka_zaplacone.isoformat()
            faktura_data_sprzedazy = user.termin.termin.isoformat()
        elif not proforma:
            # Faktura "gotowa"
            status_faktury = 2
            faktura_typ_daty = 1  # DATA_WYKONANIA_USLUGI
            faktura_data_zaplaty = None
            faktura_data_sprzedazy = user.termin.termin.isoformat()
        else:
            # Faktura "gotowa"
            status_faktury = 2
            faktura_data_zaplaty = None
            faktura_typ_daty = 3

        if user.zaliczka_termin:
            payment_date = user.zaliczka_termin
        else:
            payment_date = user.termin_platnosci_do_faktury_zaliczkowej()
    else:
        # Daty i statusy do faktury zwykłej
        faktura_data_sprzedazy = today.isoformat()

        if not proforma and user.zaplacone:
            # Faktura "zaplacona" (tylko dla faktury VAT)
            status_faktury = 4
            faktura_typ_daty = 1  # DATA_WYKONANIA_USLUGI
            faktura_data_zaplaty = user.zaplacone.isoformat()
            faktura_data_sprzedazy = user.termin.termin.isoformat()
        elif not proforma and user.faktura_po_szkoleniu:
            # Faktura "gotowa"
            status_faktury = 2
            faktura_typ_daty = 1  # DATA_WYKONANIA_USLUGI
            faktura_data_zaplaty = None
            faktura_data_sprzedazy = user.faktura_po_szkoleniu_data.isoformat()
        elif not proforma:
            # Faktura "gotowa"
            status_faktury = 2
            faktura_typ_daty = 1  # DATA_WYKONANIA_USLUGI
            faktura_data_zaplaty = None
            faktura_data_sprzedazy = user.termin.termin.isoformat()
        else:
            # Faktura "gotowa"
            status_faktury = 2
            faktura_data_zaplaty = None
            faktura_typ_daty = 3

        if user.termin_zaplaty:
            payment_date = user.termin_zaplaty
        else:
            payment_date = datetime.datetime.combine(
                user.termin_platnosci_do_faktury(today), datetime.time()
            ).date()

    if user.faktura_zaliczkowa:
        pozycja_cena_jednostkowa_netto = user.cena_zaliczka()
        pozycja_wartosc_netto = user.cena_zaliczka()
        pozycja_kwota_vat = user.kwota_vat_zaliczka()
        pozycja_wartosc_brutto = user.cena_brutto_zaliczka()
    else:
        pozycja_cena_jednostkowa_netto = user.cena()
        pozycja_wartosc_netto = user.cena()
        pozycja_kwota_vat = user.kwota_vat()
        pozycja_wartosc_brutto = user.cena_brutto()

    # Tworzymy słownik danych
    data = {
        "faktura_typ_daty": faktura_typ_daty,
        "faktura_sprzedawca": 1669,  # fixed
        "typ_faktury": typ_faktury,
        "status_faktury": status_faktury,
        "faktura_data_wystawienia": user.faktura_data_wystawienia.isoformat()
        if user.faktura_data_wystawienia
        else today.isoformat(),
        "faktura_data_sprzedazy": user.faktura_data_wykonania_uslugi.isoformat()
        if user.faktura_data_wykonania_uslugi
        else faktura_data_sprzedazy,
        "faktura_ostatnia_zmiana": datetime.datetime.now().isoformat(),
        "faktura_termin_platnosci": payment_date.isoformat(),
        "faktura_data_zaplaty": faktura_data_zaplaty,
        "faktura_wystawiona": True,  # fixed
        "faktura_nabywca": user.dane_do_faktury(),
        "faktura_uwagi": faktura_uwagi,
        "pozycje_faktury": [
            {
                "pozycja_opis": pozycja_opis,
                "pozycja_cena_jednostkowa_netto": str(pozycja_cena_jednostkowa_netto),
                "pozycja_wartosc_netto": str(pozycja_wartosc_netto),
                "pozycja_kwota_vat": str(pozycja_kwota_vat),
                "pozycja_wartosc_brutto": str(pozycja_wartosc_brutto),
                "pozycja_liczba_jednostek": "1",  # fixed
                "stawka_vat": vat_id,
                "pozycja_lp": 1,
                "pozycja_zaliczka": pozycja_zaliczka,
            }
        ],
    }

    # Wysyłamy dane do zliczacz
    try:
        response = requests.post(
            settings.API_ZLICZACZ["INVOICES"]["ADD_INVOICE_URL"],
            data=json.dumps(data, ensure_ascii=False).encode("utf-8"),
            auth=(
                settings.API_ZLICZACZ["CREDENTIALS"]["USERNAME"],
                settings.API_ZLICZACZ["CREDENTIALS"]["PASSWORD"],
            ),
            headers={"Content-type": "application/json"},
        )
    except:
        logger.exception("Nie mozna utworzyc faktury.")
        return None, "Nie mozna utworzyc faktury"

    # Wyniki funkcji to dane obiektu i ew. błąd komunikacji: (id, errros)
    if response.status_code == 201:
        return response.json(), None

    error = response.text or "Nie mozna utworzyc faktury"

    logger.warning("Nie mozna utworzyc faktury: {0}".format(error))
    return None, error


def update_invoice_status(invoice_id, status, paid_date=None):
    """
    Funkcja aktualizuje status faktury w api-zliczacz..
    """

    api_status = {"ready": 2, "sent": 3, "paid": 4, "zaliczka_paid": 4}[status]

    data = {
        "status_faktury": api_status,
        "faktura_data_zaplaty": paid_date,
    }

    # Wysyłamy dane do zliczacz
    response = requests.patch(
        settings.API_ZLICZACZ["INVOICES"]["UPDATE_INVOICE_URL"].format(id=invoice_id),
        data=json.dumps(data, ensure_ascii=False).encode("utf-8"),
        auth=(
            settings.API_ZLICZACZ["CREDENTIALS"]["USERNAME"],
            settings.API_ZLICZACZ["CREDENTIALS"]["PASSWORD"],
        ),
        headers={"Content-type": "application/json"},
    )

    # Wyniki funkcji to dane obiektu i ew. błąd komunikacji: (id, errros)
    if response.status_code == 200:
        return response.json(), None

    error = response.text

    logger.warning(
        "Nie mozna zmienic statusu faktury: {0}. Blad: {1}".format(invoice_id, error)
    )
    return None, error


def get_invoice_pdf(invoice_id):
    """
    Funkcja pobiera fakturę w pdf i zwraca w postaci obiektu
    `StringIO`.
    """

    url = settings.API_ZLICZACZ["INVOICES"]["GET_INVOICE_PDF_URL"]

    try:
        response = requests.get(
            url.format(id=invoice_id),
            auth=(
                settings.API_ZLICZACZ["CREDENTIALS"]["USERNAME"],
                settings.API_ZLICZACZ["CREDENTIALS"]["PASSWORD"],
            ),
        )
    except:
        logger.exception("Nie mozna pobrac faktury: {0}.".format(invoice_id))
        return None, "Blad pobierania faktury {0} " "z api-zliczacz.".format(invoice_id)

    # Wyniki funkcji to obiekt `BytesIO` faktury pdf i ew. błąd komunikacji:
    # (object, errros)
    if response.status_code == 200:
        return BytesIO(response.content), None

    error = response.text or "Blad pobierania faktury"

    logger.warning("Nie mozna pobrac faktury: {0}. Blad: {1}".format(invoice_id, error))
    return None, error


def get_invoice(invoice_id):
    """
    Funkcja pobiera fakturę w JSON.
    """

    url = settings.API_ZLICZACZ["INVOICES"]["GET_INVOICE_URL"]

    response = requests.get(
        url.format(id=invoice_id),
        auth=(
            settings.API_ZLICZACZ["CREDENTIALS"]["USERNAME"],
            settings.API_ZLICZACZ["CREDENTIALS"]["PASSWORD"],
        ),
    )

    if response.status_code == 200:
        return response.json(), None

    error = response.text

    logger.warning("Nie mozna pobrac faktury: {0}. Blad: {1}".format(invoice_id, error))
    return None, error


def search_invoice(keyword):
    """
    Funkcja przeszukuje zliczacza dla ID lub numeru faktury.
    """

    url = settings.API_ZLICZACZ["INVOICES"]["SEARCH_INVOICE_URL"]

    response = requests.get(
        url.format(search=keyword),
        auth=(
            settings.API_ZLICZACZ["CREDENTIALS"]["USERNAME"],
            settings.API_ZLICZACZ["CREDENTIALS"]["PASSWORD"],
        ),
    )

    if response.status_code == 200:
        return response.json(), None

    error = response.text

    logger.warning(
        "Nie mozna przeszukac listy faktur: {0}. Blad: {1}".format(keyword, error)
    )
    return None, error
