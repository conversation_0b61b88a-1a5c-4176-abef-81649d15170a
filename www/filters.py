import datetime

from django.conf import settings
from django.contrib.admin import SimpleListFilter
from django.db.models import Exists, Q, OuterRef
from django_admin_multiple_choice_list_filter.list_filters import (
    MultipleChoiceListFilter,
)

from newsletter.models import Odbiorca
from .models import (
    STATUSY_UCZESTNIKA,
    Lokalizacja,
    Prowadzacy,
    Sala,
    TagTechnologia,
    TagZawod,
)


class PlikIsEmptyListFilter(SimpleListFilter):
    title = "plik"
    parameter_name = "jest-plik"

    def lookups(self, request, model_admin):
        return (
            ("jest", "jest"),
            ("nie-ma", "nie ma"),
        )

    def queryset(self, request, queryset):
        if self.value() == "jest":
            return queryset.exclude(plik="")
        if self.value() == "nie-ma":
            return queryset.filter(plik="")


# to jest gorsze niż to, co było, bo tamto d<PERSON>ło automatycznie dla wszystkich modeli z base_translation


class HasTranslationsFilter(SimpleListFilter):
    title = "tłumaczenia"
    parameter_name = "has-translations"

    def lookups(self, request, model_admin):
        return (
            ("yes", "ma tłumaczenia lub jest tłumaczeniem"),
            ("no", "nie ma tłumaczeń i nie jest tłumaczeniem"),
        )

    def queryset(self, request, queryset):
        if self.value() == "yes":
            return queryset.filter(
                Q(translation_set__isnull=False) | Q(base_translation__isnull=False)
            )
        if self.value() == "no":
            return queryset.filter(
                Q(translation_set__isnull=True) & Q(base_translation__isnull=True)
            )


class UczestnikStatusFilter(SimpleListFilter):
    title = "status"
    parameter_name = "status"

    def lookups(self, request, model_admin):
        return (("not_rejected", "wszyscy nie zrezygnowani"),) + STATUSY_UCZESTNIKA

    def queryset(self, request, queryset):
        if self.value():
            if self.value() == "not_rejected":
                return queryset.exclude(status=4)
            return queryset.filter(status=self.value())
        return queryset


class NierozliczoneFilter(SimpleListFilter):
    title = "nierozliczone"
    parameter_name = "nierozliczone"

    def lookups(self, request, model_admin):
        return (("yes", "nierozliczone"),)

    def queryset(self, request, queryset):
        if self.value() == "yes":
            return queryset.extra(
                where=[
                    "(select count(*) from www_uczestnik where www_uczestnik.termin_id = www_terminszkolenia.id and www_uczestnik.status in (1, 3) and www_uczestnik.zaplacone is null) > 0"
                ]
            )


class ZUczestnikamiFilter(SimpleListFilter):
    title = "uczestnicy"
    parameter_name = "z-uczestnikami"

    def lookups(self, request, model_admin):
        return (("yes", "z uczestnikami"),)

    def queryset(self, request, queryset):
        if self.value() == "yes":
            return queryset.extra(
                where=[
                    "(select count(*) from www_uczestnik where www_uczestnik.termin_id = www_terminszkolenia.id and www_uczestnik.status <> 4) > 0"
                ]
            )


class ReferencjaTagTechnologiaFilter(SimpleListFilter):
    title = "tagi technologia"
    parameter_name = "tag_technologia_id"

    def lookups(self, request, model_admin):
        return [
            (str(x.id), x.nazwa)
            for x in TagTechnologia.objects.filter(language=settings.DEFAULT_LANGUAGE)
        ]

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(
                Q(tagi_technologia=self.value())
                | Q(uczestnicy__termin__szkolenie__tagi_technologia=self.value())
            ).distinct()


class TagZawodFilter(SimpleListFilter):
    title = "tag zawód"
    parameter_name = "tag_zawod_id__exact"

    def lookups(self, request, model_admin):
        objs = TagZawod.objects.all()
        return [(p.id, p.pretty_nazwa()) for p in objs]

    def queryset(self, request, queryset):

        if self.value():
            return queryset.filter(tag_zawod_id__exact=self.value())
        else:
            return queryset


class TagTechnologiaFilter(SimpleListFilter):
    title = "tagi technologia"
    parameter_name = "tagi_technologia__id__exact"

    def lookups(self, request, model_admin):
        techtag = TagTechnologia.objects.exclude(widoczny_publicznie=False)
        return [(p.id, p.pretty_nazwa()) for p in techtag]

    def queryset(self, request, queryset):

        if self.value():
            return queryset.filter(tagi_technologia__id__exact=self.value())
        else:
            return queryset


class ListDisplayExtenedFilter(SimpleListFilter):
    title = ""
    parameter_name = "list_display_extened"

    def lookups(self, request, model_admin):
        pass

    def queryset(self, request, queryset):
        return queryset


class ProwadzacyPracowalDoListFilter(SimpleListFilter):
    title = "nadal pracuje"
    parameter_name = "nadal_pracuje"

    def lookups(self, request, model_admin):
        return (
            ("True", "Tak"),
            ("False", "Nie"),
        )

    def queryset(self, request, queryset):
        if self.value() == "True":
            return queryset.filter(pracowal_do__isnull=True)
        if self.value() == "False":
            return queryset.filter(pracowal_do__isnull=False)
        return queryset


class DiscountCodeUsedFilter(SimpleListFilter):
    title = "kod wykorzystany?"
    parameter_name = "used"

    def lookups(self, request, model_admin):
        return (
            ("True", "Tak"),
            ("False", "Nie"),
        )

    def queryset(self, request, queryset):
        if self.value() == "True":
            return queryset.filter(used_count__gte=1)
        if self.value() == "False":
            return queryset.filter(used_count=0)
        return queryset


class DiscountCodeValueFilter(SimpleListFilter):
    title = "wartość kodu"
    parameter_name = "value"

    def lookups(self, request, model_admin):
        return (
            ("7", "do 7%"),
            ("7plus", "powyżej 7%"),
        )

    def queryset(self, request, queryset):
        if self.value() == "7":
            return queryset.filter(discount__lte=7)
        if self.value() == "7plus":
            return queryset.filter(discount__gt=7)
        return queryset


class AmazonFilter(SimpleListFilter):
    title = "Od Amazon"
    parameter_name = "amazon"

    def lookups(self, request, model_admin):
        return (
            ("True", "Tak"),
            ("False", "Nie"),
        )

    def queryset(self, request, queryset):
        if self.value() == "True":
            return queryset.filter(uwagi__icontains="amazon")
        if self.value() == "False":
            return queryset.exclude(uwagi__icontains="amazon")
        return queryset


class ZgodaMarketingowaFilter(SimpleListFilter):
    title = "Zgoda marketingowa"
    parameter_name = "zgoda_marketingowa"

    def lookups(self, request, model_admin):
        return (
            ("True", "Tak"),
            ("False", "Nie"),
        )

    def queryset(self, request, queryset):
        qs = Odbiorca.objects.filter(
            tos_agreement__isnull=False,
            status="potwierdzony",
            email__iexact=OuterRef("email"),
        )
        if self.value() == "True":
            return queryset.annotate(zgoda_m=Exists(qs)).filter(zgoda_m=True)
        if self.value() == "False":
            return queryset.annotate(zgoda_m=Exists(qs)).filter(zgoda_m=False)
        return queryset


class CusotmMultipleChoiceListFilter(MultipleChoiceListFilter):
    template = "admin/multiple_choice_list_filter/filter.html"

    def queryset(self, request, queryset):
        return super().queryset(request, queryset).distinct()


class OdbyloSieFilter(CusotmMultipleChoiceListFilter):
    title = "czy robimy"
    parameter_name = "odbylo_sie__in"

    def lookups(self, request, model_admin):
        return (
            (True, "Tak"),
            (False, "Nie"),
            ("unknown", "Nieznany"),
            ("gwarantowany", "Gwarantowany"),
        )

    def queryset(self, request, queryset):
        value = self.value()

        if value:
            q = Q()

            if "True" in value:
                q |= Q(odbylo_sie=True)
            if "False" in value:
                q |= Q(odbylo_sie=False)
            if "unknown" in value:
                q |= Q(odbylo_sie__isnull=True)
            if "gwarantowany" in value:
                q |= Q(gwarantowany=True)
            queryset = queryset.filter(q)
        return queryset


class TrybFilter(CusotmMultipleChoiceListFilter):
    title = "tryb"
    parameter_name = "tryb__in"

    def lookups(self, request, model_admin):
        return ((1, "dzienny"), (2, "wieczorowy"), (3, "zaoczny"))


class SalaFilter(CusotmMultipleChoiceListFilter):
    title = "sala"
    parameter_name = "sala_id__in"

    def lookups(self, request, model_admin):
        objs = Sala.objects.all()
        return [(p.id, p.nazwa) for p in objs]


class LokalizacjaFilter(CusotmMultipleChoiceListFilter):
    title = "lokalizacja"
    parameter_name = "lokalizacja_id__in"

    def lookups(self, request, model_admin):
        objs = Lokalizacja.objects.all()
        return [(p.id, p.fullname) for p in objs]


class ProwadzacyFilter(CusotmMultipleChoiceListFilter):
    title = "wszyscy prowadzący"
    parameter_name = "wszyscy_prowadzacy__in"

    def lookups(self, request, model_admin):
        return [
            (str(prowadzacy.id), prowadzacy.imie_naziwsko())
            for prowadzacy in Prowadzacy.objects.all()
        ]

    def queryset(self, request, queryset):
        if request.GET.get(self.parameter_name):
            ids = request.GET[self.parameter_name].split(",")
            queryset = queryset.filter(
                Q(prowadzacy__id__in=ids) | Q(dni_szkolenia__prowadzacy__id__in=ids)
            ).distinct()
        return queryset


class FuturePastFilter(CusotmMultipleChoiceListFilter):
    title = "przyszłe/przeszłe"
    parameter_name = "future-past"

    def lookups(self, request, model_admin):
        return (
            ("future", "przyszłe"),
            ("past", "przeszłe"),
            ("ongoing", "trwające"),
        )

    def queryset(self, request, queryset):
        value = self.value()

        if value:
            today = datetime.date.today()

            q = Q()

            if "future" in value:
                q |= Q(termin__gte=today)
            if "past" in value:
                q |= Q(termin__lt=today)
            if "ongoing" in value:
                q |= Q(
                    termin__lt=today,
                    termin_zakonczenia__gt=today,
                    termin_zakonczenia__isnull=False,
                    odbylo_sie=True,
                )
            queryset = queryset.filter(q)
        return queryset


class TerminFuturePastFilter(SimpleListFilter):
    title = "przyszłe/przeszłe"
    parameter_name = "future-past"

    def lookups(self, request, model_admin):
        return (
            ("future", "przyszłe"),
            ("past", "przeszłe"),
            ("ongoing", "trwające"),
        )

    def queryset(self, request, queryset):
        today = datetime.date.today()
        if self.value() == "future":
            return queryset.filter(termin__termin__gte=today)
        if self.value() == "past":
            return queryset.filter(termin__termin__lt=today)


class ProwadzacySelect2Filter(SimpleListFilter):
    title = "Prowadzący"
    parameter_name = "prowadzacy"
    template = "admin/dropdown_filter.html"

    def lookups(self, request, model_admin):
        # Zwraca listę (id, imię i nazwisko) dla wszystkich prowadzących
        return [
            (prowadzacy.id, prowadzacy.imie_naziwsko)
            for prowadzacy in Prowadzacy.objects.all()
        ]

    def queryset(self, request, queryset):
        selected_values = request.GET.getlist(self.parameter_name)
        if selected_values:
            self.selected_values = selected_values
            # Filtrowanie po wybranych prowadzących
            return queryset.filter(prowadzacy__id__in=selected_values)
        else:
            self.selected_values = []
        return queryset
