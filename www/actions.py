import csv

from django.http import HttpResponse

from ankiety import csvwriter


def export_as_csv_action(
    description="Export selected objects as CSV file",
    fields=None,
    header=True,
    optout_email_field=None,
):
    def export_as_csv(modeladmin, request, queryset):
        from optout.models import OptOut

        optout_emails = []
        if optout_email_field:
            optout_emails = [e.email.lower() for e in OptOut.objects.all()]

        def get_optout_status(obj):
            return (
                "TAK"
                if (getattr(obj, optout_email_field) or "").lower() in optout_emails
                else ""
            )

        _fields = fields[:]

        if optout_email_field:
            _fields.append(("opt-out", get_optout_status))

        def get_field(o, f):
            return f[1](o)

        opts = modeladmin.model._meta

        response = HttpResponse(content_type="text/csv")
        response["Content-Disposition"] = "attachment; filename=%s.csv" % str(
            opts
        ).replace(".", "_")

        writer = csvwriter.get_excel_csv_writer(response)

        if header:
            writer.writerow([y[0] for y in _fields])
        for obj in queryset:
            writer.writerow([get_field(obj, field) for field in _fields])
        return response

    export_as_csv.short_description = description
    return export_as_csv


def export_as_csv_action_v2(
    description="Export selected objects as CSV file",
    fields=None,
    header=True,
    optout_email_field=None,
):
    def export_as_csv(modeladmin, request, queryset):
        from optout.models import OptOut

        optout_emails = []
        if optout_email_field:
            optout_emails = [e.email.lower() for e in OptOut.objects.all()]

        def get_optout_status(obj):
            return (
                "TAK"
                if (getattr(obj, optout_email_field) or "").lower() in optout_emails
                else ""
            )

        _fields = fields[:]

        if optout_email_field:
            _fields.append(("opt-out", get_optout_status))

        def get_field(o, f):
            return f[1](o)

        opts = modeladmin.model._meta

        response = HttpResponse(content_type="text/csv; charset=utf-8")
        response["Content-Disposition"] = "attachment; filename=%s.csv" % str(
            opts
        ).replace(".", "_")

        writer = csv.writer(response, delimiter=';', quoting=csv.QUOTE_NONNUMERIC)

        if header:
            writer.writerow([y[0] for y in _fields])
        for obj in queryset:
            writer.writerow([get_field(obj, field) for field in _fields])
        return response

    export_as_csv.short_description = description
    return export_as_csv
