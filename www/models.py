import datetime
import hashlib
import json
import os
import re
import string
import traceback
import uuid
from collections import namedtuple
from decimal import ROUND_HALF_UP, ROUND_UP, Decimal

from dateutil import parser
from dateutil.relativedelta import relativedelta
from django.conf import settings
from django.contrib.admin.templatetags.admin_list import _boolean_icon
from django.contrib.auth.models import User
from django.contrib.contenttypes.fields import GenericForeignKey, GenericRelation
from django.contrib.contenttypes.models import ContentType
from django.core.exceptions import ObjectDoesNotExist, ValidationError
from django.core.files.storage import FileSystemStorage
from django.core.mail import mail_admins
from django.core.validators import (
    MaxValueValidator,
    MinValueValidator,
    validate_comma_separated_integer_list,
)
from django.db import connection, models, transaction
from django.db.models import Case, <PERSON>, F, Func, Q, UUIDField, When
from django.db.models.signals import post_delete, post_save
from django.dispatch import receiver
from django.template.loader import render_to_string
from django.urls import NoReverseMatch, reverse
from django.utils import translation
from django.utils.deconstruct import deconstructible
from django.utils.encoding import force_bytes, force_text
from django.utils.functional import cached_property, lazy
from django.utils.html import escape, strip_tags
from django.utils.safestring import mark_safe
from django.utils.translation import ugettext_lazy as _
from lxml.etree import ParserError, XMLSyntaxError
from lxml.html.clean import Cleaner
from sortedm2m.fields import SortedManyToManyField
from stdimage import StdImageField

import www.tasks
from common import my_slugify
from crm.models import Firma
from i18n.fields import CenaDescriptor, PoleSynchronizowane
from i18n.models import Panstwo, Waluta, WersjaJezykowa
from leads.models import LeadCategory, LeadUser
from middleware.common import get_current_user
from newsletter.models import Odbiorca
from optout.utils import email_optout
from www.helpers import to_sentence

from blacklist.models import BlackList
from headers.models import Header
from .choices import COUNTRIES_CHOICES
from .kalendarze import parse_date, update_termin_calendars
from .utils import (
    get_template_from_file,
    get_upload_path,
    get_upload_path_for_assets,
    get_upload_path_for_random_name,
    get_upload_path_for_backgrounds,
    get_upload_path_for_graduate_persons,
    get_upload_path_for_participant_files,
    get_upload_path_for_trainings_pdf,
    random_discount_code,
)
from .validators import (
    DjangoTemplateValidator,
    GoogleCalendarIDValidator,
    MultiDatesValidator,
)

uploads = FileSystemStorage(base_url="/uploads/")


@deconstructible
class SzkolenieFileSystemStorage(FileSystemStorage):
    def __init__(self, **kwargs):
        kwargs.update(
            {
                "location": os.path.join(settings.MEDIA_ROOT, "materialy"),
                "base_url": "/uploads/materialy/",
            }
        )
        super().__init__(**kwargs)


@deconstructible
class CertificateFileSystemStorage(FileSystemStorage):
    def __init__(self, **kwargs):
        kwargs.update(
            {"location": os.path.join(settings.PRIVATE_MEDIA_ROOT, "certificates")}
        )
        super().__init__(**kwargs)


@deconstructible
class AttachmentFileSystemStorage(FileSystemStorage):
    def __init__(self, **kwargs):
        kwargs.update(
            {"location": os.path.join(settings.PRIVATE_MEDIA_ROOT, "attachments")}
        )
        super().__init__(**kwargs)


@deconstructible
class ParticipantFileSystemStorage(FileSystemStorage):
    def __init__(self, **kwargs):
        kwargs.update(
            {"location": os.path.join(settings.PRIVATE_MEDIA_ROOT, "participant_files")}
        )
        super(ParticipantFileSystemStorage, self).__init__(**kwargs)


@deconstructible
class AssetsFileSystemStorage(FileSystemStorage):
    def __init__(self, **kwargs):
        kwargs.update(
            {"location": settings.ASSETS_ROOT, "base_url": settings.ASSETS_URL}
        )
        super().__init__(**kwargs)


szkolenia_uploads = SzkolenieFileSystemStorage()
certificate_uploads = CertificateFileSystemStorage()
assets_uploads = AssetsFileSystemStorage()
attachment_uploads = AttachmentFileSystemStorage()
participant_uploads = ParticipantFileSystemStorage()


class VirtuallyTranslatedModel(models.Model):
    """
    Klasa bazowa dla modeli, które udostępniają atrybut language.
    Klasa umożliwia tłumaczenie argumentów choices określonych na polach.
    Aby to zrobić, należy utworzyć atrybut CHOICE_TRANSLATIONS postaci:
    {
        nazwa_tłumaczonego_pola: {
            kod_języka: choices_w_danym_języku,
        },
    }
    W tej chwili obsługiwane są tylko choices w najprostszej postaci.
    """

    class Meta:
        abstract = True

    def _get_FIELD_display(self, field):
        value = getattr(self, field.attname)
        if hasattr(field.model, "CHOICE_TRANSLATIONS"):
            translations = field.model.CHOICE_TRANSLATIONS.get(field.attname, None)
            if (
                translations
                and self.language in translations
                and value in dict(translations[self.language])
            ):
                return force_text(
                    dict(translations[self.language])[value], strings_only=True
                )
        return models.Model._get_FIELD_display(self, field)


class TranslatedModel(VirtuallyTranslatedModel):
    """
    Klasa bazowa dla przetłumaczonych modeli.
    Bezpośrednio dziedziczą z niej tylko modele, które nie zawierają
    powiązań między różnymi tłumaczeniami.
    """

    class Meta:
        abstract = True

    language = models.CharField(
        "język",
        max_length=2,
        choices=settings.LANGUAGES,
        default=settings.DEFAULT_LANGUAGE,
    )

    CHOICE_TRANSLATIONS = {
        "language": {
            "en": (
                ("pl", "polish"),
                ("en", "English"),
            )
        }
    }

    def get_domain_in_language(self):
        return "{0}://{1}".format(
            settings.FORCE_SSL and "https" or "http",
            settings.DOMENY_DLA_JEZYKOW[self.language],
        )


class MappedTranslatedModel(TranslatedModel):
    """
    Klasa bazowa dla przetłumaczonych modeli z
    odwołaniem do modelu głównego.
    """

    class Meta:
        abstract = True

    base_translation = models.ForeignKey(
        "self",
        verbose_name="obiekt bazowy",
        blank=True,
        null=True,
        limit_choices_to={
            "language__exact": settings.DEFAULT_LANGUAGE,
        },
        help_text="Tu wybieramy odpowiednik obiektu w języku polskim.",
        related_name="translation_set",
        on_delete=models.PROTECT,
    )

    def z_tlumaczeniami(self):
        """
        Zwraca QuerySet zawierający ten obiekt wraz z tłumaczeniami.
        """
        if self.base_translation:
            query_arguments = Q(pk=self.base_translation.pk) | Q(
                base_translation__pk=self.base_translation.pk
            )
        else:
            query_arguments = Q(base_translation__pk=self.pk)
        return self.__class__.objects.filter(query_arguments)

    def w_jezyku(self, language):
        """
        Zwraca odpowiednik tego obiektu w zadanym języku.
        """
        return self.z_tlumaczeniami().get(language=language)

    def translations(self):
        return self.z_tlumaczeniami().exclude(pk=self.pk)

    tlumaczenia = translations

    def en(self):
        en = list(
            self.__class__.objects.filter(base_translation__pk=self.pk, language="en")
        )
        if en:
            return en[0]
        return None

    def en_or_self(self):
        """
        Funkcja zwracająca angielskie tłumaczenie lub self,
        jeżeli self sam jest po angielsku. Docelowo trzeba ją
        pewnie zintegrować z en(), ale trzeba się rozejrzeć,
        czy niczego to nie rozwali.
        """
        return self.language == "en" and self or self.en()


STAWKI_VAT = (
    (Decimal("0.0000"), "0%"),
    (Decimal("0.2300"), "23%"),
)

DEFAULT_VAT = Decimal("0.2300")

ENGLISH_PRICE_RATIO = Decimal("1.25")
EUR_IN_PLN = Decimal("4.1565")


def kwota_brutto(netto, vat):
    if netto is None or vat is None:
        return None
    return (netto * (Decimal("1.0000") + vat)).quantize(
        Decimal("0.01"), rounding=ROUND_HALF_UP
    )


def cena_brutto(obj):
    return kwota_brutto(obj.cena, obj.stawka_vat)


class TagZawod(MappedTranslatedModel):
    class Meta:
        verbose_name_plural = "Tagi - zawód"
        ordering = ["ordering"]
        unique_together = (("language", "slug"),)

    nazwa = models.CharField(max_length=50)
    slug = models.CharField(max_length=50, blank=True)
    ordering = models.IntegerField(null=True, blank=True)
    opis = models.TextField(null=True, blank=True)
    widoczny_publicznie = models.BooleanField(default=True)

    def __str__(self):
        return self.nazwa

    def pretty_nazwa(self):
        if self.language.upper() == "PL":
            return self.nazwa
        else:
            return "{} ({})".format(self.nazwa, self.language.upper())

    def clean(self):
        if not self.slug:
            self.slug = my_slugify.slugify(self.nazwa)


class TagTechnologiaManager(models.Manager):
    def get_etag(self, slug, language):
        """
        Funkcja wylicza po stronie bazy ciąg MD5 używany jako ETag żądania.
        Do wyliczenia ETaga danego tagu używane są:

        - stały prefix
        - id taga
        - data aktualizacji taga
        - data aktualizacji szkoleń i kursów
        - data aktualizacji przyszłych terminów powyższych szkoleń i kursów
        - data modyfikacji obiektów SiteModule
        - data modyfikacji obiektów Highlight przypisanych do taga
        - data modyfikacji obiektów Prowadzacych przypisanych do taga
        - data modyfikacji obiektów MyFlatPage
        """

        cursor = connection.cursor()

        cursor.execute(
            """
            SELECT
                md5(
                    concat(
                        'tag_technologia'::text,
                        '_'::text,
                        www_tagtechnologia.id::text,
                        '_'::text,
                        www_tagtechnologia.updated_at::text,
                        '_'::text,
                        string_agg(www_szkolenie.updated_at::text, '|'
                            ORDER BY www_szkolenie.updated_at ASC),
                        '_'::text,
                        string_agg(www_terminszkolenia.updated_at::text, '|'
                            ORDER BY www_terminszkolenia.updated_at ASC),
                        '_'::text,
                        (SELECT
                            string_agg(www_sitemodule.updated_at::text, '|'
                                ORDER BY www_sitemodule.updated_at ASC)
                        FROM www_sitemodule
                        WHERE www_sitemodule.language = %s
                            AND www_sitemodule.enabled = TRUE
                        ),
                        '_'::text,
                        (SELECT
                            string_agg(www_prowadzacy.updated_at::text, '|'
                                ORDER BY  www_prowadzacy.updated_at ASC)
                        FROM  www_prowadzacy
                        INNER JOIN www_tagtechnologiaprowadzacy
                            ON (www_tagtechnologiaprowadzacy.prowadzacy_id = www_prowadzacy.id
                                AND www_tagtechnologiaprowadzacy.tagtechnologia_id = www_tagtechnologia.id
                            )
                        ),
                        '_'::text,
                        (SELECT
                            string_agg(www_highlight.updated_at::text, '|'
                                ORDER BY  www_highlight.updated_at ASC)
                        FROM  www_highlight
                        INNER JOIN www_highlightplacement
                            ON (www_highlightplacement.highlight_id = www_highlight.id
                                AND www_highlightplacement.object_id = www_tagtechnologia.id
                                AND www_highlightplacement.content_type_id = (
                                    SELECT
                                        django_content_type.id
                                    FROM
                                        django_content_type
                                    WHERE
                                        django_content_type.app_label = 'www'
                                        AND django_content_type.model = 'tagtechnologia'
                                )
                            )
                        ),
                        '_'::text,
                        (SELECT
                            string_agg(www_myflatpage_uq.updated_at::text, '|'
                                ORDER BY www_myflatpage_uq.updated_at ASC)
                        FROM www_myflatpage AS www_myflatpage_uq
                        WHERE www_myflatpage_uq.language = %s
                        )
                    )
                ) AS etag
            FROM www_tagtechnologia
            LEFT JOIN www_szkolenie_tagi_technologia
                ON (www_tagtechnologia.id = www_szkolenie_tagi_technologia.tagtechnologia_id)
            LEFT JOIN www_szkolenie
                ON ((
                        www_szkolenie.id = www_szkolenie_tagi_technologia.szkolenie_id
                        OR
                        www_szkolenie.base_translation_id = www_szkolenie_tagi_technologia.szkolenie_id
                    )
                    AND www_szkolenie.aktywne = TRUE
                    AND www_szkolenie.tag_dlugosc_id
                        IN (SELECT www_tagdlugosc.id
                            FROM www_tagdlugosc
                            WHERE www_tagdlugosc.slug = 'szkolenie'
                                OR www_tagdlugosc.slug = 'kurs-zawodowy'
                        )
                )
            LEFT JOIN www_terminszkolenia
                ON www_terminszkolenia.szkolenie_id = www_szkolenie.id
                    AND www_terminszkolenia.zamkniete = FALSE
                    AND www_terminszkolenia.termin >= CURRENT_DATE
            WHERE www_tagtechnologia.slug = %s
                AND www_tagtechnologia.widoczny_publicznie = TRUE
                AND www_tagtechnologia.language = %s
            GROUP BY www_tagtechnologia.id, www_tagtechnologia.updated_at;
        """,
            [language, language, slug, language],
        )

        try:
            etag = cursor.fetchone()[0]
        except:
            return None
        else:
            return etag


class TagTechnologia(MappedTranslatedModel):
    class Meta:
        verbose_name_plural = "Tagi - technologia"
        ordering = ["ordering"]
        unique_together = (("language", "slug"),)

    nazwa = models.CharField(max_length=50)
    slug = models.CharField(max_length=50, blank=True)
    ordering = models.IntegerField(null=True, blank=True)
    frontpage_ordering = models.PositiveSmallIntegerField(
        "sortowanie na stronie głównej", default=1
    )
    kursy_po_szkoleniach = models.BooleanField(
        "listować kursy po szkoleniach", default=False
    )
    opis = models.TextField(
        null=True,
        blank=True,
        help_text="Pojawi się na stronie z listą szkoleń w tej technologii. HTML wypisywany żywcem bez żadnego filtrowania.",
    )
    highlights = GenericRelation("HighlightPlacement")
    content_group = models.ForeignKey(
        "ContentGroup",
        blank=True,
        null=True,
        verbose_name="grupa treści",
        on_delete=models.PROTECT,
    )
    domena = models.OneToOneField(
        "Domena", blank=True, null=True, on_delete=models.PROTECT
    )
    domena_slash_tech = models.ForeignKey(
        "Domena",
        blank=True,
        null=True,
        related_name="tagtechnologia_slash_set",
        on_delete=models.PROTECT,
    )
    domena_redirect = models.BooleanField(default=False)
    logo = models.ForeignKey("Logo", blank=True, null=True, on_delete=models.PROTECT)
    # obrazek_url = models.CharField(max_length=100, null=True, blank=True)
    meta_title = models.CharField(max_length=100, blank=True)
    meta_description = models.TextField(blank=True)
    meta_keywords = models.TextField(
        blank=True,
        help_text="Wycofane z produkcyjnego użycia od 2022 r. - pole w bazie zachowane dla celów archiwalnych.",
    )
    # custom_url = models.CharField(max_length=250, blank=True)
    tag_zawod_ordering = models.CharField(
        'Sortowanie "tag zawód"',
        null=True,
        blank=True,
        max_length=250,
        help_text='Sortuje tagi zawodu w obrębie podstrony danego Tech Taga. Pole przyjmuje ID tagów zawodu (liczby całkowite) oddzielone przecinkami, np: 1,2,3,4. Pole jest typu "fail silently" - jeśli jakieś ID nie będzie się zgadzać z listą zawodów, zostanie pominięte.',
        validators=[validate_comma_separated_integer_list],
    )

    sciezki = models.ManyToManyField("Sciezka", blank=True)

    widoczny_publicznie = models.BooleanField(
        default=True, help_text="W praktyce synonim: aktywny."
    )
    widoczny_na_frontpage = models.BooleanField(
        "widoczny na stronie głównej",
        default=True,
        help_text=(
            "Aby tag był widoczny na stronie głównej to pole musi być "
            "zaznaczone jak też 'widoczny publicznie' "
            "(stan na 2021+: pole prawdopodobnie już nie jest używane)"
        ),
    )
    uwagi_internal = models.TextField(blank=True)

    top_background = models.ImageField(
        "obrazek tła",
        storage=assets_uploads,
        upload_to=get_upload_path_for_backgrounds,
        max_length=250,
        blank=True,
        help_text="Duża grafika na tło pod cały obszar topu górnego strony.",
    )
    archiwalna_liczba_przeszkolonych = models.IntegerField(
        "archiwalna liczba przeszkolonych",
        null=True,
        blank=True,
        help_text="Liczba przeszkolonych osób do momentu wprowadzenia ankiet. "
        "Wartość z tego pola (jeśli podana) dodawana jest do "
        "ogólnej liczby ocen na podstronie Taga.",
    )
    static_page = models.ForeignKey(
        "MyFlatPage",
        verbose_name="Przekieruj na stronę statyczną",
        blank=True,
        null=True,
        on_delete=models.PROTECT,
        help_text="Jeśli strona zostanie wskazana, po wejściu na techtag nastąpi "
        "przekierowanie do tej strony.",
    )

    # Daty
    created_at = models.DateTimeField(
        "utworzono", auto_now_add=True, null=True, db_index=True
    )
    updated_at = models.DateTimeField("aktualizowano", auto_now=True, null=True)

    objects = TagTechnologiaManager()

    def __str__(self):
        output = "{} ({})".format(self.nazwa, self.language.upper())
        if not self.widoczny_publicznie:
            output += " (inactive)"
        return output

    def archiwalna_liczba_przeszkolonych_lub_zero(self):
        return (
            self.archiwalna_liczba_przeszkolonych
            if self.archiwalna_liczba_przeszkolonych
            else 0
        )

    def pretty_nazwa(self):
        if self.language.upper() == "PL":
            return self.nazwa
        else:
            return "{} ({})".format(self.nazwa, self.language.upper())

    def get_absolute_url(self):
        if self.domena:
            return "http://%s/" % self.domena.url
        else:
            try:
                return reverse(
                    "index_by_technologia_" + self.language,
                    kwargs={"technologia_slug": self.slug},
                )
            except NoReverseMatch:
                return reverse(
                    "index_by_technologia",
                    kwargs={"technologia_slug": self.slug, "language": self.language},
                )

    def clean(self):
        if not self.slug:
            self.slug = my_slugify.slugify(self.nazwa)

    def save(self, **kwargs):
        if not self.ordering:
            cursor = connection.cursor()
            cursor.execute(
                "select coalesce(max(ordering)+100, 100) from www_tagtechnologia"
            )
            row = cursor.fetchone()
            self.ordering = row[0]
        super().save()

    def get_description(self):
        try:
            h = self.highlights.filter(initially_selected=True)
            if not h:
                h = self.highlights.all()[0]
            else:
                h = h.get()
            h = h.highlight
        except:

            return {"nazwa": self.nazwa, "opis": strip_tags(self.opis or "")}
        return {"nazwa": h.tytul_h2 or h.tytul, "opis": strip_tags(h.tresc or "")}

    def clean_content(self):
        cleaner = Cleaner(
            scripts=True,
            javascript=True,
            comments=True,
            style=True,
            links=True,
            meta=True,
            page_structure=True,
            processing_instructions=True,
            embedded=True,
            frames=True,
            forms=True,
            annoying_tags=True,
        )
        try:
            return cleaner.clean_html(self.opis)
        except (ParserError, XMLSyntaxError):
            return ""        


class TagTechnologiaProwadzacy(models.Model):
    tagtechnologia = models.ForeignKey("TagTechnologia", on_delete=models.CASCADE)
    prowadzacy = models.ForeignKey(
        "Prowadzacy", verbose_name="prowadzący", on_delete=models.CASCADE
    )
    ordering = models.PositiveSmallIntegerField("kolejność", default=1)

    created_at = models.DateTimeField(
        "utworzono", auto_now_add=True, null=True, db_index=True
    )
    updated_at = models.DateTimeField("aktualizowano", auto_now=True, null=True)

    class Meta:
        verbose_name_plural = "Prowadzący"
        ordering = ["ordering"]
        unique_together = (("tagtechnologia", "prowadzacy"),)

    def __str__(self):
        return ""


class Testimonial(models.Model):
    """
    Testimoniale dla TagTechnologia
    """

    tagtechnologia = models.ForeignKey("TagTechnologia", on_delete=models.PROTECT)
    content = models.TextField(_("Treść"), help_text=_("Można używać znaczników HTML."))
    obrazek = models.ImageField(
        storage=uploads, upload_to="testimoniale/", null=True, blank=True
    )
    imie_nazwisko = models.CharField(_("imię i nazwisko"), max_length=100, blank=True)
    firma = models.CharField(max_length=100, blank=True)
    data_testimoniala = models.DateField(null=True, blank=True)
    uwagi_internal = models.TextField(blank=True)
    id_opinii_zrodlowej = models.ForeignKey(
        "ankiety.Komentarz",
        verbose_name=_("ID opinii źródłowej"),
        null=True,
        blank=True,
        on_delete=models.PROTECT,
    )

    class Meta:
        verbose_name_plural = "Testimoniale"


class TagDlugosc(models.Model):
    class Meta:
        verbose_name_plural = "Tagi - długość"
        ordering = ["ordering"]

    nazwa = models.CharField(max_length=50)
    nazwa_en = models.CharField(max_length=50)
    slug = models.CharField(max_length=50, unique=True, blank=True)
    ordering = models.IntegerField(null=True, blank=True)
    opis = models.TextField(null=True, blank=True)

    def __str__(self):
        return self.nazwa

    def clean(self):
        if not self.slug:
            self.slug = my_slugify.slugify(self.nazwa)


class Sala(models.Model):
    nazwa = models.TextField()
    lokalizacja = models.ForeignKey("Lokalizacja", on_delete=models.PROTECT)
    calendar = models.CharField(
        max_length=256,
        validators=[
            GoogleCalendarIDValidator(),
        ],
        blank=True,
        help_text="sam calendar ID, nie URL",
    )
    sprawdzaj_konflikty = models.BooleanField(default=True)
    instrukcja_dotarcia = models.CharField(
        max_length=250,
        blank=True,
        help_text="Krótka informacja, jak odnaleźć siedzibę lub salę - "
        "wykorzystywane w automatycznych powiadomieniach o starcie kursu.<br>"
        "<b>Jeśli nie podana zostanie pobrana instrukcja z obiektu lokalizacji "
        "(o ile nie podano też adresu).</b>",
    )
    ulica_i_numer = models.CharField(
        max_length=250,
        blank=True,
        help_text="np. <i>ul. Jasna 14/16A</i>."
        "<br>"
        "<b>Jeśli nie podana zostanie pobrany adres z obiektu "
        "lokalizacji.</b>",
    )
    sala_wirtualna = models.BooleanField(
        "Wirtualna sala?", default=False, help_text="Sala do nauczania zdalnego."
    )

    def __str__(self):
        return "%s (%s)" % (self.nazwa, self.lokalizacja)

    class Meta:
        verbose_name_plural = "Sale"
        ordering = ["lokalizacja", "nazwa"]

    def get_ulica_i_numer(self):
        """
        Pobieramy adres z obiektu sali (pierwszeństwo) lub miasta.
        """

        return self.ulica_i_numer or self.lokalizacja.ulica_i_numer

    def get_instrukcja_dotarcia(self):
        """
        Pobieramy instrukcje dojazdu z obiektu sali (pierwszeństwo) lub miasta.

        Uwaga: jeśli nadpisaliśmy adres to instrukcja dotarcia na pewno nie
               będzie się zgadzać, wiec lepiej zwrócic pustą niz tą z obiektu
               lokalizacji.
        """

        if self.ulica_i_numer:
            return self.instrukcja_dotarcia
        return self.instrukcja_dotarcia or self.lokalizacja.instrukcja_dotarcia


# class SzkolenieManager(models.Manager):
#   use_for_related_fields = True
#   def with_translations_info(self):
#       return self.get_query_set().extra(select={'has_translations': '(SELECT count(*) FROM www_szkolenie AS szkolenia WHERE (szkolenia.base_translation_id = www_szkolenie.id OR szkolenia.id = www_szkolenie.base_translation_id)) > 0'})

QUASI_TEXTILE_HELP_TEXT = '[dozwolony markup: &lt;b&gt;...&lt;/b&gt;, &lt;i&gt;...&lt;/i&gt;, &lt;a href="..."&gt;...&lt;/a&gt;, &lt;img&gt; (wycinane w PDF), listy w stylu textile: *, #, #*, #**]'
QUASI_TEXTILE_FOR_PDF_HELP_TEXT = (
    QUASI_TEXTILE_HELP_TEXT
    + ".<br>PS. używając znaczników &lt!--pdfignore--&gt i &lt!--endpdfignore--&gt  można wyłączyć dane fragmenty (np. css) z wersji PDF."
)


class Sprzet(models.Model):
    nazwa = models.CharField("nazwa zestawu", max_length=250)
    liczba_urzadzen = models.PositiveIntegerField("liczba urządzeń", default=0)
    sprawdzaj_konflikty = models.BooleanField("sprawdzaj konflikty", default=True)

    # Daty
    created_at = models.DateTimeField(
        "utworzono", auto_now_add=True, null=True, db_index=True
    )
    updated_at = models.DateTimeField("aktualizowano", auto_now=True, null=True)

    class Meta:
        verbose_name = "zestaw komputerowy"
        verbose_name_plural = "zestawy komputerowe"

    def __str__(self):
        return self.nazwa


class Autoryzacja(MappedTranslatedModel):
    nazwa = models.CharField(_("nazwa"), max_length=100)
    opis_krotki = models.TextField(_("opis krótki"))
    opis_dlugi = models.TextField(
        _("opis długi"), help_text="Można używać znaczników HTML."
    )

    class Meta:
        verbose_name = _("autoryzacja")
        verbose_name_plural = _("autoryzacje")
        unique_together = ("language", "nazwa")

    def __str__(self):
        return self.nazwa


HELP_TEXT_CEN_BAZOWYCH = "W przypadku jej braku wyświetlana cena zostanie obliczona na podstawie ceny szkolenia bazowego (dotyczy tłumaczeń)."


class SzkolenieManager(models.Manager):
    def get_etag(self, slug, language):
        """
        Funkcja wylicza po stronie bazy ciąg MD5 używany jako ETag żądania.
        Do wyliczenia ETaga danego szkolenia używane są:

        - stały prefix
        - id szkolenia
        - data aktualizacji szkolenia
        - data aktualizacji przyszłych terminów powyższego szkolenia
        - data modyfikacji obiektów SiteModule
        - data modyfikacji obiektów MyFlatPage
        - data modyfikacji wszystkich szkoleń przypisanych do tego samego
          tagu co przetwarzane szkolenie
        """

        cursor = connection.cursor()

        cursor.execute(
            """
            SELECT
                md5(
                    concat(
                        'szkolenie'::text,
                        '_'::text,
                        www_szkolenie.id::text,
                        '_'::text,
                        www_szkolenie.updated_at::text,
                        '_'::text,
                        string_agg(www_terminszkolenia.updated_at::text, '|'
                            ORDER BY www_terminszkolenia.updated_at ASC),
                        '_'::text,
                        (SELECT
                            string_agg(www_sitemodule.updated_at::text, '|'
                                ORDER BY www_sitemodule.updated_at ASC)
                        FROM www_sitemodule
                        WHERE www_sitemodule.language = %s
                            AND www_sitemodule.enabled = TRUE
                        ),
                        '_'::text,
                        (SELECT
                            string_agg(www_myflatpage_uq.updated_at::text, '|'
                                ORDER BY www_myflatpage_uq.updated_at ASC)
                        FROM www_myflatpage AS www_myflatpage_uq
                        WHERE www_myflatpage_uq.language = %s
                        ),
                        '_'::text,
                        (SELECT
                            string_agg(www_szkolenie_uq.updated_at::text, '|'
                                ORDER BY www_szkolenie_uq.updated_at ASC)
                        FROM www_szkolenie AS www_szkolenie_uq
                        WHERE www_szkolenie_uq.aktywne = TRUE
                            AND www_szkolenie_uq.language = %s
                            AND www_szkolenie_uq.id IN (
                                SELECT www_szkolenie_tagi_technologia.szkolenie_id
                                FROM www_szkolenie_tagi_technologia
                                WHERE www_szkolenie_tagi_technologia.tagtechnologia_id IN (
                                    SELECT www_szkolenie_tagi_technologia_uq.tagtechnologia_id
                                    FROM www_szkolenie_tagi_technologia AS www_szkolenie_tagi_technologia_uq
                                    WHERE www_szkolenie_tagi_technologia_uq.szkolenie_id = www_szkolenie.id
                                )
                            )
                        )
                    )
                ) AS etag
            FROM www_szkolenie
            LEFT JOIN www_terminszkolenia
                ON www_terminszkolenia.szkolenie_id = www_szkolenie.id
                    AND www_terminszkolenia.zamkniete = FALSE
                    AND www_terminszkolenia.termin >= CURRENT_DATE
            WHERE www_szkolenie.slug = %s
                AND www_szkolenie.aktywne = TRUE
                AND www_szkolenie.language = %s
            GROUP BY www_szkolenie.updated_at, www_szkolenie.id;
        """,
            [language, language, language, slug, language],
        )

        try:
            etag = cursor.fetchone()[0]
        except:
            return None
        else:
            return etag


class Szkolenie(MappedTranslatedModel):
    FIELDS_SYNCED_BETWEEN_TRANSLATIONS = [
        # 'zawiera_obiady',
        # 'czas_dni',
        # 'czas_godziny',
        # 'stawka_vat',
        # 'autoryzacja',
        # 'kod_autoryzacji',
        # 'nazwa_autoryzacji',
        # 'godzin_w_trybie_indywidualnym',
    ]

    # Pola, które dziedziczy nowy obiekt szkolenia podczas tworzenia z
    # obiektu bazowego `base_translation`.
    FIELDS_INHERITED_BETWEEN_TRANSLATIONS = [
        "zawiera_obiady",
        "czas_dni",
        "czas_godziny",
    ]

    tryb_wieczorowy_opis = models.TextField("tryb wieczorowy opis", blank=True)
    tryb_zaoczny_opis = models.TextField("tryb zaoczny opis", blank=True)
    tryb_dzienny_opis = models.TextField("tryb dzienny opis", blank=True)

    szkolenie_nadrzedne = models.ForeignKey(
        "self",
        verbose_name="szkolenie nadrzędne",
        blank=True,
        null=True,
        on_delete=models.PROTECT,
    )
    podzbior_dni = models.CharField(
        "podzbiór dni",
        max_length=100,
        blank=True,
        help_text="Lista dni oddzielona przecinkami, np. 1,2,3",
        validators=[validate_comma_separated_integer_list],
    )
    wiele_rat = models.BooleanField("dozwolone wiele rat", default=True)
    cena_miesieczna = models.BooleanField(
        "wyświetlaj na stronie cenę raty",
        default=False,
        help_text="W przypadku zaznaczenia na stronie szkolenia w nagłówku wyświetlana "
        "będzie wysokość raty jako cena miesięczna",
    )
    top_background = models.ImageField(
        "obrazek tła",
        storage=assets_uploads,
        upload_to=get_upload_path_for_backgrounds,
        max_length=250,
        blank=True,
    )
    archiwalna_liczba_przeszkolonych = models.IntegerField(
        "archiwalna liczba przeszkolonych",
        null=True,
        blank=True,
        help_text="Liczba przeszkolonych osób do momentu wprowadzenia ankiet. "
        "Wartość z tego pola (jeśli podana) dodawana jest do "
        "ogólnej liczby ocen na podstronie szkolenia/kursu.",
    )

    akredytacje = models.ManyToManyField(
        "Lokalizacja",
        blank=True,
        db_table="www_akredytacja_szkolenia",
        related_name="akredytacja_set",
    )
    nazwa = models.CharField(max_length=200)
    podtytul = models.CharField(max_length=1000, blank=True, help_text="Podtytuł szkolenia.")
    krotka_nazwa = models.CharField(
        "krótka nazwa",
        max_length=200,
        blank=True,
        help_text="Używana np. przy wyświetlaniu alternatywnych szkoleń.",
    )
    kod = models.CharField(
        max_length=50, help_text="powinien być biznesowo brzmiący i krótki"
    )
    tag_zawod = models.ForeignKey(TagZawod, on_delete=models.PROTECT, db_index=True)
    tagi_technologia = models.ManyToManyField(TagTechnologia, db_index=True)
    tag_dlugosc = models.ForeignKey(TagDlugosc, default=2, on_delete=models.PROTECT, db_index=True)
    # slug jest autogenerowany przy zapisie obiektu jeśli nie wpisany. Stąd property blank=True, ale null w bazie już false.
    slug = models.SlugField(
        max_length=100,
        blank=True,
        help_text="nazwa widoczna w URL-ach. Jeśli zostawisz puste, zostanie wygenerowana automatycznie jako nazwa szkolenia bez polskich liter ze spacjami zamienionymi na myślniki",
    )
    strona_z_opisem_slug = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        help_text="używane dla kursów zawodowych; wypełnienie tego pola spowoduje, że zamiast domyślnej strony szkolenia pojawia się wskazana slugiem strona typu my flatpage.",
    )
    ordering = models.IntegerField(
        null=True,
        blank=True,
        help_text="wartość tego pola decyduje o kolejności na liście szkoleń. Proszę: NIE zostawiaj tego pola pustego!",
        db_index=True
    )
    aktywne = models.BooleanField(
        default=True, help_text="czyli opublikowane (na stronie)."
    )

    # --- Oznaczony do usuniecia
    jest_czescia_kursu_zawodowego = models.NullBooleanField(
        "jest częścią kursu zawodowego",
        help_text="A zatem może odbywać się równocześnie z kursem w jednej sali, i nie będzie stanowić to konfliktu terminów.",
    )

    # kontynuacje
    kontynuacja = models.ForeignKey(
        "self",
        verbose_name="szkolenie będące kontynuacją",
        related_name="posiadajace_kontynuacje",
        null=True,
        blank=True,
        on_delete=models.PROTECT,
    )
    maile_o_kontynuacji = models.BooleanField(
        "wysyłać maile o kontynuacji?", default=False
    )
    szkolenia_alternatywne = SortedManyToManyField(
        "self", blank=True, verbose_name="szkolenia alternatywne"
    )

    header_title = models.CharField(max_length=200, null=True, blank=True)
    header_kwd = models.TextField(
        null=True,
        blank=True,
        help_text="Wycofane z produkcyjnego użycia od 2022 r. - pole w bazie zachowane dla celów archiwalnych.",
    )
    tagi_marketingowe = models.TextField(
        "tagi marketingowe", blank=True, help_text="Dopuszczalny HTML"
    )
    header_descr = models.TextField(null=True, blank=True)
    opis = models.TextField(
        help_text="Najbardziej skrócony. Ma być krótszy niż opis główny. Dawniej używany do tabel (pełna lista szkoleń itp.), obecnie - gł. do pola META DESCRIPTION jeśli nie zostało ustawione bezpośrednio."
    )
    opis_dlugi = models.TextField(null=True, blank=True, help_text="Dopuszczalny HTML")
    # zwykle powinny byc dni a jesli nie ma to dlugi kurs o roznych trybach => godziny
    czas_dni = models.IntegerField(
        null=True,
        blank=True,
        help_text="domyślnie należy wpisywać tylko dni, chyba że (a) kurs zawodowy => wtedy podajemy tylko godziny, lub (b) szkolenie o niestandardowych długościach dni - wtedy możemy podać i liczbę dni, i liczbę godzin.",
    )
    czas_dni_kurs = models.IntegerField(
        "czas dni (kurs)",
        null=True,
        blank=True,
        default=None,
        help_text="domyślnie należy wpisywać dni trwania kursu - pole "
        "używane wyłacznie do walidacji w terminach szkolenia.",
    )
    czas_godziny = models.IntegerField(null=True, blank=True)

    czas_godziny_samodzielne = models.IntegerField(
        "czas godziny (samodzielne)",
        null=True,
        blank=True,
        help_text="Dotyczy wariantu szkolenia: informacja w boxie cenowym: {czas_godziny} + {czas_godziny_samodzielne}",
    )
    wariant_cena_opis = models.CharField(
        "opis ceny wariantu szkolenia",
        blank=True,
        max_length=250,
        help_text="Dotyczy wariantu szkolenia: tekst obok ceny.",
    )

    # teksty obok ceny dla wariantu

    materialy_plik = models.FileField(
        "Plik z materiałami",
        max_length=250,
        blank=True,
        null=True,
        storage=szkolenia_uploads,
        upload_to=get_upload_path_for_trainings_pdf,
    )

    program_szkolenia = models.TextField(
        help_text="Program szkolenia (potrzebny do e-certyfikatu)",
        blank=True,
    )
    program_szkolenia_w_pdf = models.BooleanField(
        "Dołącz program szkolenia do e-certyfikatu PDF",
        default=False,
    )

    szablon_certyfikatu = models.TextField(
        "Szablon e-certyfikatu",
        validators=[
            DjangoTemplateValidator(),
        ],
        help_text="Pole może zawierać elementy szablonów Django. "
        "Do szablonu przekazywane są zmienne: {{ uczestnik }}, "
        "{{ nazwa_szkolenia }}, {{ data_szkolenia }}, "
        "{{ program_szkolenia }}, {{ miejscowosc }}, "
        "{{ liczba_dni }}",
        blank=True,
    )

    grupa_zaszeregowania = models.ForeignKey(
        "GrupaZaszeregowania", blank=True, null=True, on_delete=models.PROTECT
    )

    # Dodatkowe pytania
    dodatkowe_pytanie_do_uczestnika = models.TextField(
        "dodatkowe pytanie podczas zgłoszenia", blank=True
    )
    dodatkowe_pytanie_do_uczestnika_widocznosc = models.CharField(
        "widoczność dodatkowego pytania",
        max_length=20,
        choices=(
            ("hidden", "ukryte"),
            ("optional", "opcjonalne"),
            ("required", "wymagane"),
        ),
        default="hidden",
    )
    dodatkowe_pytanie_o_wiek = models.CharField(
        "dodatkowe pytanie o wiek",
        max_length=20,
        choices=(
            ("hidden", "ukryte"),
            ("optional", "opcjonalne"),
            ("required", "wymagane"),
        ),
        default="hidden",
        help_text="Jeśli widoczne, w formularzu zgłoszeniowym pokaże się pole z miejscem na wiek uczestnika.",
    )
    mozliwa_rejestracja_jako_firma = models.BooleanField(
        "możliwa rejestracja jako firma?", default=True
    )

    # Daty
    created_at = models.DateTimeField(
        "utworzono", auto_now_add=True, null=True, db_index=True
    )
    updated_at = models.DateTimeField(
        "aktualizowano", auto_now=True, null=True, db_index=True
    )

    objects = SzkolenieManager()

    class Meta:
        verbose_name_plural = "Szkolenia"
        ordering = ["ordering"]
        unique_together = (("slug", "language"),)

    def is_km(self):
        return self.kod and self.kod.startswith("KM-")

    def info_dla_wariantu(self):
        from www.templatetags.myfilters import cena_info_short

        return strip_tags(
            "{} godzin / {}".format(self.czas_godziny, cena_info_short(self))
        )

    def jest_wariantem(self, aktywne=True):
        """
        Sprawdza, czy dane szkolenie jest wariantem aktywnego/dowolnego kursu.
        """

        # if self.aktywne:
        #     return None

        qs = SzkolenieWariant.objects.filter(wariant_id=self.pk)
        if aktywne:
            qs = qs.filter(szkolenie__aktywne=True)
        obj = qs.select_related("szkolenie").first()
        return obj.szkolenie if obj else None

    def max_czas_trwania_wariantow(self):
        warianty = [e.wariant for e in self.warianty_szkolenia.all()]
        return max([e.czas_godziny for e in warianty]) if warianty else None

    def max_cena_wariantow(self):
        ceny = dict([(e.cena, e) for e in self.warianty()])

        if not ceny:
            return None
        return ceny.get(max(ceny.keys()))

    def warianty(self):
        """
        Pobiera warianty szkoleń.
        """

        return [
            e.wariant for e in self.warianty_szkolenia.select_related("wariant").all()
        ]

    @property
    def krotka_nazwa_lub_nazwa(self):
        return self.krotka_nazwa or self.nazwa

    def cena_rata(self, cena):
        cena = cena / (5 if self.wiele_rat else 3)
        return cena.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)

    @property
    def tagi_marketingowe_as_list(self):
        return [m.strip() for m in self.tagi_marketingowe.split(";")]

    def get_tryb_opis(self, tryb):
        """
        Zwracamy opis w zależności od trybu terminu.
        """

        return {
            1: self.tryb_dzienny_opis,
            2: self.tryb_wieczorowy_opis,
            3: self.tryb_zaoczny_opis,
        }[tryb]

    def archiwalna_liczba_przeszkolonych_lub_zero(self):
        return (
            self.archiwalna_liczba_przeszkolonych
            if self.archiwalna_liczba_przeszkolonych
            else 0
        )

    def liczba_dni_liczbowo(self):
        return self.czas_dni and self.czas_dni or None

    def liczba_dni(self):
        if self.czas_dni:
            jednostki_dni = " {0}".format(
                _("dzień") if self.czas_dni == 1 else _("dni")
            )
            return str(self.czas_dni) + jednostki_dni
        else:
            return ""

    def liczba_dni_no_spaces(self):
        return self.liczba_dni().replace(" ", "&nbsp;")

    def liczba_dni_en(self):  # w przyszłości do usunięcia
        if self.czas_dni:
            return str(self.czas_dni) + (" day" if self.czas_dni == 1 else " days")
        else:
            return ""

    def maksymalna_liczba_osob_dla_grup_otwartych(self):
        if self.tag_dlugosc.slug == "kurs-zawodowy" or (
            self.szkolenie_nadrzedne_id
            and self.szkolenie_nadrzedne.tag_dlugosc.slug == "kurs-zawodowy"
        ):
            return 14
        if self.tag_dlugosc.slug == "szkolenie":
            grupa_nr = (
                self.grupa_zaszeregowania.nazwa[0:2]
                if self.grupa_zaszeregowania
                else "0"
            )
            if grupa_nr in ["1:", "2:", "3:", "4:"]:
                return 14
            elif grupa_nr in ["5:"]:
                return 10
            else:
                return 8

    def maksymalna_sugerowana_liczba_osob_dla_grup_zamknietych(self):
        if self.tag_dlugosc.slug == "kurs-zawodowy":
            return 14
        if self.tag_dlugosc.slug == "szkolenie":
            return 12

    def czas_str(self):
        # if (self.czas_dni and self.czas_godziny):
        #   return str(self.czas_dni) + (" dzień" if self.czas_dni == 1 else " dni") + " (" +
        #  str(self.czas_godziny) + " godzin)"
        if self.czas_dni:
            return self.liczba_dni()
        elif self.czas_godziny:
            if self.czas_godziny_samodzielne:
                return "{} + {}h".format(
                    self.czas_godziny, self.czas_godziny_samodzielne
                )
            return str(self.czas_godziny) + "h"
        else:
            return ""

    def czas_str_no_spaces(self):
        return self.czas_str().replace(" ", "&nbsp;")

    def czas_str_en(self):  # w przyszłości do usunięcia
        # if (self.czas_dni and self.czas_godziny):
        # return str(self.czas_dni) + (" day" if self.czas_dni == 1 else " days") + " (" +
        # str(self.czas_godziny) + " hours)"
        if self.czas_dni:
            return str(self.czas_dni) + (" day" if self.czas_dni == 1 else " days")
        elif self.czas_godziny:
            return str(self.czas_godziny) + "h"
        else:
            return ""

    cena_bazowa = models.DecimalField(
        _("cena"),
        max_digits=30,
        decimal_places=2,
        help_text=HELP_TEXT_CEN_BAZOWYCH,
        blank=True,
        null=True,
    )
    waluta = models.ForeignKey(Waluta, on_delete=models.PROTECT)

    cena = CenaDescriptor("cena_bazowa", exp=Decimal("1E1"), rounding=ROUND_UP)
    cena_autoryzacji = CenaDescriptor(
        "cena_autoryzacji_bazowa", exp=Decimal("1"), rounding=ROUND_UP
    )
    cena_przed_promocja = CenaDescriptor(
        "cena_przed_promocja_bazowa", exp=Decimal("1E1"), rounding=ROUND_UP
    )
    cena_w_grupie_2_os = CenaDescriptor(
        "cena_w_grupie_2_os_bazowa", exp=Decimal("1E1"), rounding=ROUND_UP
    )
    cena_indywidualnie = CenaDescriptor(
        "cena_indywidualnie_bazowa", exp=Decimal("1E1"), rounding=ROUND_UP
    )

    def cena_brutto(self):
        return cena_brutto(self)

    def kwota_vat(self):
        return cena_brutto(self) - self.cena

    zawiera_obiady = models.BooleanField(
        default=False,
        help_text="generalnie: w wypadku szkoleń mówimy, że TAK, w wypadku kursów zawodowych NIE.",
    )

    def zawiera_obiady_str(self):
        if self.zawiera_obiady:
            # FIXME </br>
            return "Cena zawiera pełne wyżywienie: obiady oraz przerwy kawowe<br/>(napoje ciepłe i zimne oraz różnego rodzaju ciasteczka)."
        else:
            return None

    def get_header_kwd(self):
        """Zwraca header_kwd lub pierwszy z header_kwd dla tagów technologia."""
        return self.header_kwd or self.tagi_technologia.all()[0].meta_keywords

    opis_egzaminu = models.TextField(
        _("opis egzaminu"),
        blank=True,
        help_text="Wyświetlany na stronie szkolenia po opisie autoryzacji. Można używać znaczników HTML.",
    )
    autoryzacja = models.ForeignKey(
        Autoryzacja,
        verbose_name=_("autoryzacja"),
        blank=True,
        null=True,
        on_delete=models.PROTECT,
    )
    cena_autoryzacji_bazowa = models.IntegerField(
        _("cena autoryzacji"), blank=True, null=True, help_text=HELP_TEXT_CEN_BAZOWYCH
    )
    nazwa_autoryzacji = models.CharField(
        _("nazwa autoryzacji"), max_length=200, blank=True
    )
    kod_autoryzacji = models.CharField(_("kod autoryzacji"), max_length=50, blank=True)
    # dla szkoleń, jeśli nie ma terminów, to jako termin będzie się pojawiało "Z/<n>" co oznacza "zamknięte od n osób"
    # gdzie n bierzemy właśnie stąd. dla kursów zawodowych nie będziemy pisać nigdy "Z/<n>", ale to już szczegół.
    min_grupa_bazowa = models.IntegerField(
        "minimalna grupa",
        null=True,
        blank=True,
        default=2,
        help_text="informacja oficjalna od ilu osob odpalamy grupy zamknięte. default pisać: 2. W przypadku "
        "pustej wartości zostanie ona pobrana z obiektu bazowego.",
    )
    min_grupa = PoleSynchronizowane("min_grupa_bazowa")

    # dane do generowania strony ze szczegółami
    cel = models.TextField(
        help_text="To jest tak naprawdę opis główny, cel i opis szkolenia. Ten tekst jest widoczny na samej górze na stronie szczegółów szkolenia - i w PDF-ie. "
        + QUASI_TEXTILE_FOR_PDF_HELP_TEXT
        + "<br>Uwaga: Jeśli pole zawiera tag &ltstyle&gt składnia textile *nie* jest interpretowana."
    )
    cel_pdf = models.TextField(
        "Opis szkolenia (do PDF)",
        blank=True,
        help_text="Jeśli nie ustawione - używane będzie pole "
        '"Opis szkolenia - na jego stronę". '
        "Należy unikać skomplikowanych konstrukcji "
        "html/css. " + QUASI_TEXTILE_FOR_PDF_HELP_TEXT,
    )
    przeznaczony_dla = models.TextField(
        null=True, blank=True, help_text=QUASI_TEXTILE_FOR_PDF_HELP_TEXT
    )
    program = models.TextField(help_text=QUASI_TEXTILE_FOR_PDF_HELP_TEXT)
    wymagania = models.TextField(
        null=True,
        blank=True,
        help_text="w razie niepewności / braku weny twórczej można wypełnić same wymagania tzn. bez przeznaczony dla. "
        + QUASI_TEXTILE_FOR_PDF_HELP_TEXT,
    )
    forma_zajec = models.TextField(null=True, blank=True)

    def forma_zajec_str(self):
        if self.forma_zajec:
            return self.forma_zajec
        else:
            return "Wszystkie zajęcia prowadzone są przy komputerach i mają charakter warsztatowy."

    nabyte_umiejetnosci = models.TextField(null=True, blank=True)
    certyfikaty = models.TextField(null=True, blank=True)

    def certyfikaty_str(self):
        if self.certyfikaty:
            return self.certyfikaty
        else:
            str_to_return = "{0}".format(
                _(
                    "Uczestnicy szkolenia otrzymują imienne certyfikaty sygnowane przez ALX."
                )
            )
            return str_to_return

    def certyfikaty_str_en(self):  # do wywalenia w przyszłości
        if self.certyfikaty:
            return self.certyfikaty
        else:
            return "Course participants receive completion certificates signed by ALX."

    lokalizacje = models.TextField(null=True, blank=True)

    def lokalizacje_str(self):
        if self.lokalizacje:
            return self.lokalizacje
        else:
            return "\n".join(self.lokalizacje_list())

    def lokalizacje_list(self):
        """
        Zwraca listę z krótkimi opisami dostępnych lokalizacji.
        """
        wersja_jezykowa = WersjaJezykowa.biezaca()
        if self.lokalizacje:
            opisy_lokalizacji = self.lokalizacje.split("\n")
        else:
            lokalizacje = Lokalizacja.objects.filter(
                reklamowana=True, panstwo__in=wersja_jezykowa.wyswietlane_panstwa.all()
            )
            opisy_lokalizacji = [
                "{0} – {1}".format(lokalizacja.fullname, lokalizacja.ulica_i_numer)
                for lokalizacja in lokalizacje
            ]
            opisy_lokalizacji.append(
                _(
                    "na życzenie dowolne miejsce w Polsce, lub UE (zajęcia prowadzone w języku angielskim)"
                )
            )
        return opisy_lokalizacji

    uwagi = models.TextField(null=True, blank=True)

    def uwagi_str(self):
        if self.uwagi:
            return self.uwagi
        else:
            return "Szkolenie realizowane w trybach otwartym i zamkniętym."

    # dla kursów zawodowych
    harmonogram = models.TextField(
        null=True, blank=True, help_text="aktualnie nieużywane."
    )

    def __str__(self):
        return "%s: %s" % (self.kod, self.nazwa)

    powinno_miec_termin = models.BooleanField(
        "powinno mieć termin (WAW)",
        default=False,
        help_text="flaga mówiąca biuru, że szkolenie zawsze powinno mieć co najmniej jeden termin w przyszłości, oraz skryptom pilnującym, aby tego pilnować i w razie potrzeby przypominać.",
    )
    powinno_miec_termin_krk = models.BooleanField(
        "powinno mieć termin (KRK)",
        default=False,
        help_text="flaga mówiąca biuru, że szkolenie zawsze powinno mieć co najmniej jeden termin w przyszłości, oraz skryptom pilnującym, aby tego pilnować i w razie potrzeby przypominać.",
    )

    lokalizacje_powinne_miec_terminy = models.ManyToManyField(
        "Lokalizacja", blank=True, verbose_name="lokalizacje - powinny mieć terminy"
    )

    uwagi_internal = models.TextField(blank=True)

    prowadzacy = models.ManyToManyField(
        "Prowadzacy",
        blank=True,
        db_table="www_prowadzacy_szkolenia",
        related_name="szkolenie_set",
    )

    domena = models.ForeignKey(
        "Domena", blank=True, null=True, on_delete=models.PROTECT
    )
    logo = models.ForeignKey("Logo", blank=True, null=True, on_delete=models.PROTECT)

    teksty_o_terminach = models.ManyToManyField(
        "Lokalizacja", through="TekstOTerminach", related_name="szkolenia_tot"
    )

    # TODO: wywalić to.
    stawka_vat = models.DecimalField(
        max_digits=30,
        decimal_places=4,
        choices=STAWKI_VAT,
        default=DEFAULT_VAT,
        help_text="Do wyrzucenia, nie używać.",
    )

    cena_przed_promocja_bazowa = models.DecimalField(
        "cena przed promocją",
        max_digits=30,
        decimal_places=2,
        blank=True,
        null=True,
        help_text=HELP_TEXT_CEN_BAZOWYCH,
    )
    cena_w_grupie_2_os_bazowa = models.DecimalField(
        "cena w grupie 2-os.",
        max_digits=30,
        decimal_places=2,
        blank=True,
        null=True,
        help_text=HELP_TEXT_CEN_BAZOWYCH,
    )
    cena_indywidualnie_bazowa = models.DecimalField(
        "cena indywidualnie",
        max_digits=30,
        decimal_places=2,
        blank=True,
        null=True,
        help_text=HELP_TEXT_CEN_BAZOWYCH,
    )
    godzin_w_trybie_indywidualnym = models.IntegerField(blank=True, null=True)
    sciezki = models.ManyToManyField("Sciezka", blank=True)
    zawartosc_certyfikatu = models.TextField(blank=True)

    def terminy_ok_verbose(self):
        # if not (self.powinno_miec_termin or self.powinno_miec_termin_krk):
        #   return (None, 'nie musi mieć terminów')
        # ret = True
        # powody = []
        # if self.powinno_miec_termin and not self.terminy().filter(lokalizacja__shortname='WAW'):
        #   ret = False
        #   powody.append('brakuje terminów w WAW')
        # if self.powinno_miec_termin_krk and not self.terminy().filter(lokalizacja__shortname='KRK'):
        #   ret = False
        #   powody.append('brakuje terminów w KRK')
        # return (ret, ', '.join(powody))
        powinno = self.lokalizacje_powinne_miec_terminy.all()
        if not powinno:
            return (None, "nie musi mieć terminów")
        ret = True
        powody = []
        for l in powinno:
            if not self.terminy().filter(lokalizacja=l):
                ret = False
                powody.append("brakuje terminów w %s" % l.shortname)
        return (ret, ", ".join(powody))

    def terminy_ok(self):
        return self.terminy_ok_verbose()[0]

    terminy_ok.boolean = True
    terminy_ok.short_description = "terminy OK"

    @mark_safe
    def terminy_ok_html(self):
        z = self.terminy_ok_verbose()
        return re.sub(r"/>$", 'title="%s" />' % escape(z[1]), _boolean_icon(z[0]))

    terminy_ok_html.short_description = "terminy OK"

    def clean(self):
        if (
            self.dodatkowe_pytanie_do_uczestnika_widocznosc != "hidden"
            and not self.dodatkowe_pytanie_do_uczestnika
        ):
            raise ValidationError(
                "Jeśli chcesz pokazywać dodatkowe pytanie w formularzu zgłoszeniowym to podaj jego treść :-)"
            )
        if not self.slug:
            self.slug = my_slugify.slugify(self.nazwa)
        if (
            self.autoryzacja
            and not self.base_translation
            and self.cena_autoryzacji_bazowa is None
        ):
            raise ValidationError(
                "Jeśli podano autoryzację, należy wypełnić również jej cenę."
            )
        if not self.cena_bazowa and not self.base_translation:
            raise ValidationError(
                "Dla szkoleń niebędących tłumaczeniami trzeba podać cenę."
            )

    @staticmethod
    def _ustaw_tlumaczony_atrybut(zrodlo, cel, nazwa_atrybutu):
        """
        Pomocnicza funkcja wykorzystywana przy synchronizacji atrybutów
        między tłumaczeniami. Ustawia zadany atrybut ze źródła na cel i
        jeśli jego wartość jest tłumaczonym obiektem, to zmienia ją na
        odpowiednie tłumaczenie. Jeśli tłumaczenie nie istnieje,
        ustawia None.
        """
        wartosc = getattr(zrodlo, nazwa_atrybutu)
        if isinstance(wartosc, MappedTranslatedModel):
            try:
                wartosc = wartosc.w_jezyku(cel.language)
            except wartosc.DoesNotExist:
                wartosc = None
        setattr(cel, nazwa_atrybutu, wartosc)

    def save(self, *args, **kwargs):
        if self.strona_z_opisem_slug == "":
            self.strona_z_opisem_slug = None

        if not self.pk and self.base_translation:
            for f in self.FIELDS_INHERITED_BETWEEN_TRANSLATIONS:
                # Jesli pole nie ma wartości, spróbuj ją pobrać z obiektu
                # bazowego.
                if not getattr(self, f):
                    self._ustaw_tlumaczony_atrybut(self.base_translation, self, f)
        super().save(*args, **kwargs)
        # SYNCHRONIZACJA została wyłączona - 06.2015
        # if self.language == settings.DEFAULT_LANGUAGE:
        #     ts = self.tlumaczenia()
        #     for t in ts:
        #         for f in self.FIELDS_SYNCED_BETWEEN_TRANSLATIONS:
        #             self._ustaw_tlumaczony_atrybut(self, t, f)
        #         t.save()

    def get_absolute_url(self):
        if self.strona_z_opisem_slug:
            if (
                self.strona_z_opisem_slug.startswith("https://")
                or self.strona_z_opisem_slug.startswith("http://")
                or self.strona_z_opisem_slug.startswith("/")
            ):
                return self.strona_z_opisem_slug
            else:
                return reverse(
                    "my_flat_page",
                    kwargs={
                        "slug": self.strona_z_opisem_slug,
                        "language": self.language,
                    },
                )
        else:
            try:
                path = reverse("detail_" + self.language, kwargs={"slug": self.slug})
            except NoReverseMatch:
                path = reverse(
                    "detail", kwargs={"slug": self.slug, "language": self.language}
                )
            if self.domena:
                path = "http://" + self.domena.url + path
            return path

    def get_pdf_cel(self):
        """
        Pobieramy pole "cel" do PDF.
        """

        return self.cel_pdf or self.cel

    def url_with_domain(self):
        """
        Metoda zwraca url z domeną (brana jest pod uwagę tylko domena z
        settings.py), bez protokołu, do szkolenia uwzględniając jego język.
        """

        lang = self.language

        path = reverse("detail_{0}".format(lang), kwargs={"slug": self.slug})

        return "{0}{1}".format(settings.DOMENY_DLA_JEZYKOW[lang], path)

    def url_registration(self):
        """
        Metoda zwraca url z domeną (brana jest pod uwagę tylko domena z
        settings.py), bez protokołu, do formularza rejestracji.
        """

        lang = self.language

        path = reverse(
            "zgloszenie", kwargs={"slug": self.slug, "language": self.language}
        )

        return "{0}{1}".format(settings.DOMENY_DLA_JEZYKOW[lang], path)

    def url_with_domain_suggest_schedule(self):
        """
        Metoda zwraca url z domeną (brana jest pod uwagę tylko domena z
        settings.py), bez protokołu, do formularza propozycji terminu
        szkolenia uwzględniając język szkolenia.
        """

        lang = self.language

        path = reverse(
            "zaproponuj_termin",
            kwargs={
                "slug": self.slug,
                "language": lang,
            },
        )

        return "{0}{1}".format(settings.DOMENY_DLA_JEZYKOW[lang], path)

    def _terminy_z_tlumaczeniami(self):
        """
        Zwraca terminy we wszystkich językach.
        """
        if self.base_translation:
            return self.base_translation._terminy_z_tlumaczeniami()
        else:
            return TerminSzkolenia.objects.filter(
                Q(szkolenie=self) | Q(szkolenie__base_translation=self)
            )

    def _dostepne_terminy_z_tlumaczeniami(self):
        """
        Terminy z tłumaczeniami dostępne w bieżącej wersji językowej.
        """
        return self._terminy_z_tlumaczeniami().filter(
            lokalizacja__panstwo__in=WersjaJezykowa.biezaca().wyswietlane_panstwa.all()
        )

    def terminy(self):
        return self._dostepne_terminy_z_tlumaczeniami().filter(
            zamkniete=False, termin__gte=datetime.date.today()
        )

    def terminy_locale(self):
        """Zwraca terminy tylko w języku szkolenia."""
        # TODO: Zoptymalizować to zapytanie.
        return self.terminy().filter(language=translation.get_language())

    def terminy_trwajace(self):
        # Funkcja używana przy generowaniu harmonogramów.
        return self._dostepne_terminy_z_tlumaczeniami().filter(
            termin_zakonczenia__gte=datetime.date.today(), zamkniete=False
        )

    def terminy_all(self):
        # Funkcja używana w formularzu admina uczestnika.
        return self._terminy_z_tlumaczeniami().filter(termin__gte=datetime.date.today())

    @mark_safe
    def terminy_pretty(self):
        return render_to_string(
            "admin/www/szkolenie/terminy.html",
            {"terminy": self.terminy().select_related("lokalizacja")},
        )

    terminy_pretty.short_description = "terminy"

    def szkolenie_czy_kurs(self):
        return _("szkolenie") if self.tag_dlugosc.slug == "szkolenie" else _("kurs")

    @mark_safe
    def grupa_zaszeregowania_link(self):
        return render_to_string(
            "admin/www/szkolenie/grupa_zaszeregowania.html",
            {"grupa_zaszeregowania": self.grupa_zaszeregowania},
        )

    grupa_zaszeregowania_link.short_description = "grupa"

    def najblizsze_terminy_miastami(self):
        miasta = []
        for miasto in Lokalizacja.objects.filter(reklamowana=True):
            terminy = (
                self.terminy()
                .filter(lokalizacja=miasto)
                .select_related("szkolenie__tag_dlugosc")
                .annotate(liczba_terminow_podrzednych=models.Count("nadrzedne"))
            )
            tot = TekstOTerminach.objects.filter(lokalizacja=miasto, szkolenie=self)
            if terminy or tot:
                miasta.append({"miasto": miasto, "terminy": terminy, "tot": tot})
        return miasta

    def zgodne_z_obiektem_bazowym(self):
        """
        Zwraca True, jeśli wartość synchronizowanych pól jest zgodna z obiektem bazowym.
        FIXME: Sprawdzić, czy to jeszcze jest aktualne/potrzebne/używane.
        """
        if not self.base_translation:
            raise Exception(
                "Wywolano zgodne_z_obiektem_bazowym na szkoleniu bez obiektu bazowego."
            )
        return all(
            getattr(self.base_translation, field_name) == getattr(self, field_name)
            for field_name in self.FIELDS_SYNCED_BETWEEN_TRANSLATIONS
        )

    def widoczne_tagi_technologia(self):
        """
        Oddzielna metoda dla szablonu, bo nie korzystamy z Jinja2.
        """
        return self.tagi_technologia.filter(widoczny_publicznie=True)

    @property
    def content_group(self):
        """
        Pobieramy grupę treści z tagów technologia, o ile one ją posiadają.
        W przypadku niejednoznaczności pobieramy którąkolwiek z dostępnych grup.
        """
        try:
            return self.tagi_technologia.exclude(content_group__isnull=True)[
                0
            ].content_group
        except IndexError:
            return None

    def strona_statyczna(self):
        if self.strona_z_opisem_slug:
            try:
                return self.myflatpage_set.all()[0]
            except:
                pass

    def trenerzy(self):
        return Prowadzacy.objects.filter(
            pokazywac=True, szkolenia__id__in=[self.pk]
        ).order_by("ordering")

    wysylaj_powiadomienia_proformy = models.BooleanField(
        default=True,
        help_text="normalnie wysyłane są powiadomienia i proformy, ale np. dla egzaminów nie",
    )


class SzkolenieWariant(models.Model):
    szkolenie = models.ForeignKey(
        Szkolenie, related_name="warianty_szkolenia", on_delete=models.PROTECT
    )
    wariant = models.OneToOneField(
        Szkolenie,
        limit_choices_to={"tag_dlugosc__slug": "kurs-zawodowy"},
        on_delete=models.CASCADE,
    )
    kolejnosc = models.PositiveSmallIntegerField(
        default=1, help_text='Pierwszy na liście będzie wariantem "głównym".'
    )

    class Meta:
        verbose_name = "Wariant szkolenia"
        verbose_name_plural = "Warianty szkoleń"
        ordering = ["kolejnosc", "-pk"]
        unique_together = ("szkolenie", "wariant")

    def __str__(self):
        return str(self.wariant)


UczestnikIndywidualny = namedtuple(
    "UczestnikIndywidualny",
    (
        "imie_nazwisko",
        "chce_obiady",
        "chce_obiady_opis",
        "chce_autoryzacje",
        "drukowany_certyfikat",
    ),
)


class TerminSzkoleniaManager(models.Manager):
    def get_for_continuation(self, termin_zakonczenia=None):
        """
        Metoda pobiera terminy do rozsyłki maili o kontynuacji.

        Szukamy takich terminów, które:
          - odbywaja się/odbyły się
          - mają ustawioną flagę o kontynuacji
        """

        qs = self.filter(
            odbylo_sie=True,
            szkolenie__maile_o_kontynuacji=True,
            szkolenie__kontynuacja__isnull=False,
            szkolenie__kontynuacja__aktywne=True,
        )

        if termin_zakonczenia is None:
            today = datetime.date.today()

            # Ograniczamy terminy tylko do tych, które odbyły się od 01.01.2018 i
            # zostały zakończone.
            qs = qs.filter(
                termin__gte=datetime.date(2018, 1, 1),
                termin__lt=today,
            ).filter(
                models.Q(termin_zakonczenia__isnull=True)
                | models.Q(termin_zakonczenia__lt=today)
            )
        else:
            qs = qs.filter(termin_zakonczenia=termin_zakonczenia)

        return qs.select_related("szkolenie__kontynuacja")

    def get_for_notifications(
        self, training_id, locations_ids, created_at, term_date, last_action_date
    ):
        """
        Metoda pobiera terminy szkoleń dla powiadomień konkretnego użytkownika.

        Szukamy takich terminów, które:
          - przypisane są do szkolenia z preferencji użytkownika
          - przypisane są do jednej z lokalizacji z preferencji użytkownika
          - zostały dodane min. `created_at` godzin temu
          - nie są zamknięte
          - data szkolenia jest w przyszłości (min. `term_date`)
          - data utworzenia lub staru terminu jest późniejsza niż
            data ostatniej akcji użytkownika (`last_action_date`).
        """

        return (
            self.filter(
                szkolenie_id=training_id,
                lokalizacja_id__in=locations_ids,
                created_at__lte=created_at,
                zamkniete=False,
                created_at__isnull=False,
                termin__gte=term_date,
            )
            # .filter(
            #    models.Q(created_at__gt=last_action_date)
            #    | models.Q(jobs_state__isnull=False, jobs_state__gt=last_action_date)
            # )
            .select_related(
                "lokalizacja", "szkolenie", "szkolenie__tag_dlugosc"
            ).distinct()
        )

    def get_for_untrained(self, max_training_age):
        """
        Metoda pobiera terminy szkoleń dla pozyskiwania nieprzeszkolonych
        użytkowników.

        Szukamy takich terminów, które:
          - nie są starsze niż `max_training_age`
          - nie odbyły się
          - są przypisane do "otwartych" lokalizacji
            (lokalizacja.reklamowana == True)

        Mimo, że z metody korzystamy tylko raz w jednym miejscu, wynisienie
        jej do managera miało sens z punktu wiedzenia testów i patchowania.
        Na chwilę obecną (06.11.2014) testy odpalane są na produkcyjnej bazie,
        w której istnieje wiele terminów szkoleń. Podczas testów musimy
        mieć kontrolę na terminami, które są zwracane.
        """

        today = datetime.date.today()

        return (
            self.filter(
                termin__gte=max_training_age,
                odbylo_sie=False,
                lokalizacja__reklamowana=True,
            )
            .filter(
                models.Q(termin__lt=today) | models.Q(termin__gt=today, zamkniete=False)
            )
            .select_related("szkolenie", "lokalizacja")
        )

    def get_for_grupa_rusza(
        self, max_training_age, min_training_age=None, min_grupa=None
    ):
        """
        Metoda pobiera terminy, które nadają się do wysłania wcześniejszego
        powiadomienia do Biura, że grupa rusza lub prawie rusza.
        """

        qs = self.filter(termin__gt=max_training_age, odbylo_sie=None)

        if min_training_age:
            qs = qs.filter(termin__lte=min_training_age)
        if min_grupa:
            qs = qs.filter(szkolenie__min_grupa_bazowa__gte=min_grupa)
        return qs.select_related("szkolenie", "lokalizacja")


class TerminSzkolenia(VirtuallyTranslatedModel):
    class Meta:
        verbose_name_plural = "Terminy Szkoleń"
        ordering = ["termin", "szkolenie"]

    termin_nadrzedny = models.ForeignKey(
        "self",
        verbose_name="termin nadrzędny",
        blank=True,
        null=True,
        related_name="nadrzedne",
        help_text="Proszę podać, jeśli edytowany Termin Szkolenia jest częścią "
        "innego, większego Terminu Szkolenia",
        on_delete=models.PROTECT,
    )
    termin = models.DateField()
    lokalizacja = models.ForeignKey("Lokalizacja", on_delete=models.PROTECT)
    termin_zakonczenia = models.DateField(
        blank=True,
        null=True,
        help_text="wpisywać dla kursów zawodowych oraz celem nadpisania standardowej długości szkolenia (np. dla konsultacji indywidualnych i wynajmów sal)",
    )
    gwarantowany = models.BooleanField(
        default=False,
        help_text="marketingowo - zaznaczyć, aby powiedzieć, że wiadomo już że na pewno się odbędzie.",
    )
    tryb = models.IntegerField(
        choices=((1, _("dzienny")), (2, _("wieczorowy")), (3, _("zaoczny"))), default=1
    )
    ile_dni = models.IntegerField(
        blank=True,
        null=True,
        help_text="wymagane dla kursów zawodowych jeśli obiady są opcjonalne",
    )
    czas_dni_kurs = models.IntegerField(
        "czas dni (kurs)",
        null=True,
        blank=True,
        default=None,
        help_text="domyślnie należy wpisywać dni trwania kursu - pole "
        "używane wyłacznie do walidacji. Jesli nie podane, zostanie "
        "pobrana wartość z obiektu Szkolenia.",
    )
    opis = models.CharField(
        "parametry trybu",
        max_length=200,
        null=True,
        blank=True,
        help_text="najkrótszy dopisek, wszędzie przy terminach szkolenia; używać przy kursach wieczorowych żeby podać dni tygodnia. Tu NIE uzywac nawiasow - wstawiaja sie same.",
    )
    dodatkowe_uwagi = models.TextField(
        "uwagi www",
        null=True,
        blank=True,
        help_text="Jeśli podane, zostaną wyświetlone zamiast domyślnego "
        "opisu z obiektu Szkolenia.<br>"
        "Opis lądujący na stronie głównej przy terminach kursów; "
        "można używać przy wszystkich kursach, "
        "modelowo innych niż wieczorowe, "
        "żeby podać tryb zajęć opisowo; tutaj wpisujemy też uwagi "
        "w rodzaju [DATA]: ZOSTAŁY TYLKO DWA WOLNE MIEJSCA. "
        "Można używać znaczników HTML. Tutaj jesli chcemy nawiasy "
        "na stronie, to trzeba je ręcznie podać.",
    )
    nie_pokazuj_wolnych_miejsc = models.BooleanField(
        "nie pokazuj informacji o wolnych miejscach",
        default=False,
        help_text="UWAGA: dotyczy tylko terminów promowanych",
    )
    szkolenie = models.ForeignKey(Szkolenie, on_delete=models.PROTECT)
    # szkolenie.szkoleniekurs_filter = True
    sala = models.ForeignKey(Sala, blank=True, null=True, on_delete=models.PROTECT)
    prowadzacy = models.ForeignKey(
        "Prowadzacy",
        verbose_name="prowadzący",
        blank=True,
        null=True,
        on_delete=models.PROTECT,
    )

    secret_key = UUIDField(default=uuid.uuid4)

    liczba_osob_w_grupie = models.PositiveSmallIntegerField(
        "liczba osób (N) w grupie",
        null=True,
        default=None,
        blank=True,
        validators=[MinValueValidator(1)],
    )
    cena_bazowa_do_n_osob = models.DecimalField(
        "cena za grupę do N osób (włącznie)",
        max_digits=30,
        decimal_places=2,
        blank=True,
        null=True,
        validators=[MinValueValidator(1)],
    )
    cena_za_osobe_powyzej_n_osob = models.DecimalField(
        "wysokość dopłaty za każdą osobę powyżej N osób",
        validators=[MinValueValidator(1)],
        max_digits=30,
        decimal_places=2,
        blank=True,
        null=True,
    )
    autoryzacja_w_cenie = models.BooleanField(
        "autoryzacja wliczona w cenę", default=False
    )
    drukowany_certyfikat_w_cenie = models.BooleanField(
        "drukowany certyfikat w cenie", default=False
    )

    # Pole `dodatkowi_prowadzacy` jest juz nie używane.
    dodatkowi_prowadzacy = models.ManyToManyField(
        "Prowadzacy",
        verbose_name="dodatkowi prowadzący",
        blank=True,
        related_name="dodatkowe_terminy_szkolen",
        help_text="Wykorzystywani przy generowaniu ankiet",
    )

    sprzet = models.ManyToManyField(
        Sprzet, verbose_name="zestawy komputerowe", blank=True
    )
    odbylo_sie = models.NullBooleanField("czy robimy", default=None)
    czy_reklamowac = models.BooleanField("czy promowac", default=False)
    zliczacz_task_id = models.IntegerField(null=True, editable=False)
    certyfikaty_przekazane = models.BooleanField(default=False)
    materialy_wydrukowane = models.BooleanField(default=False)
    sala_przygotowana = models.BooleanField(default=False)
    materialy_uwagi = models.TextField(blank=True)
    zamkniete = models.BooleanField(
        "nie publikuj",
        default=False,
        help_text="zaznacz, jeżeli: (a) termin został odwołany, lub (b) szkolenie prawdziwie zamknięte tj. dostępne tylko dla ludzi z firmy, która je zamówiła",
    )
    prywatna_rejestracja = models.BooleanField(
        '"prywatna" rejestracja',
        default=False,
        help_text="zaznacz, jeżeli termin jest zamknięty i chcesz włączyć prywatną (widoczną tylko za pośrednictwem unikalnego linku) rejestrację.",
    )
    # nie_publikuj = models.BooleanField(default=False, help_text="czasami szkolenie niby jest dla zamówienie dla jakiejś firmy, ale nie ma sprzeciwu, żeby doczepić tam jeszcze kogoś luzem. Wtedy datę szkolenia zamkniętego publikujemy jak każdą inną. Jeżeli jednak tak nie jest i nie chcemy publikować - zaznaczamy.")
    uwagi = models.TextField("uwagi internal", blank=True)
    harmonogram = models.TextField(
        blank=True,
        help_text="wpisz, jeżeli harmonogram jest inny, niż wynikałoby to z pola daty szczegółowo",
    )
    obiady = models.CharField(
        max_length=128,
        choices=(
            ("obiady-wliczone", "obiady wliczone"),
            ("obiady-opcjonalne", "obiady opcjonalne"),
            ("nie-ma-obiadow", "nie ma obiadów"),
        ),
        blank=True,
        help_text="jak zostawisz puste, to spróbuję zgadnąć",
    )
    cena_obiadu = models.DecimalField(
        max_digits=30,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="zostaw puste, żeby skopiować domyślną cenę dla lokalizacji",
    )
    autoryzacja_aktywna = models.BooleanField(
        default=False,
        help_text="Określa, czy autoryzacja powinna być dostępna w tym terminie szkolenia (opcja dostępna tylko, jeśli szkolenie ma przypisaną autoryzację).",
    )
    snz_opis = models.CharField(
        "SnZ opis",
        max_length=128,
        blank=True,
        help_text="opis dla szkoleń na zamówienie",
    )
    daty_szczegolowo = models.TextField(blank=True)

    program_szkolenia = models.TextField(
        "Program szkolenia (potrzebny do e-certyfikatu)",
        blank=True,
        help_text="Jeśli pole zostanie puste tekst zostanie pobrany z obiketu "
        "szkolenia.",
    )
    program_szkolenia_w_pdf = models.NullBooleanField(
        "Dołącz program szkolenia do e-certyfikatu PDF",
        default=None,
        help_text='Gdy "nieznany", wartość pobierana jest z obiektu Szkolenia, w przeciwnym razie z tego pola.',
    )

    szablon_certyfikatu = models.TextField(
        "Szablon e-certyfikatu",
        blank=True,
        validators=[
            DjangoTemplateValidator(),
        ],
        help_text="Pole może zawierać elementy szablonów Django. "
        "Do szablonu przekazywane są zmienne: {{ uczestnik }}, "
        "{{ nazwa_szkolenia }}, {{ data_szkolenia }}, "
        "{{ program_szkolenia }}, {{ miejscowosc }}, "
        "{{ liczba_dni }}<br>Jeśli pole zostanie puste tekst "
        "zostanie pobrany z obiektu szkolenia (jeśli istnieje).",
    )

    # Przechowuje datę zlecenia wysłki maili do uczestników. Jeśli wartość jest
    # NULL oznacza to, ze możemy wysłać powiadomienia.
    notyfikacje_o_certyfikatach = models.DateTimeField(blank=True, null=True)

    # Przechowuje datę zlecenie jednorazowych zadań na terminie szkolenia
    # np. wysyłka maili, faktur itp. Zadania te mają być wykonane tylko raz
    # dla jednego obiektu.
    # Pole to używane jest także do powiadomień dotyczących uruchomienia
    # terminu.
    jobs_state = models.DateTimeField(
        "Potwierdzono", editable=False, null=True, blank=True, db_index=True
    )
    # j.w., ale do zadan biurowych
    internal_jobs_state = models.DateTimeField(
        "Potwierdzono", editable=False, null=True, blank=True, db_index=True
    )

    # Daty
    created_at = models.DateTimeField(
        "utworzono", auto_now_add=True, null=True, db_index=True
    )
    updated_at = models.DateTimeField(
        "aktualizowano", auto_now=True, null=True, db_index=True
    )
    niestandardowe_godziny = models.CharField(
        "niestandardowe godziny szkolenia",
        blank=True,
        max_length=100,
        help_text='Np. "10:00 - 15:30"',
    )
    uwagi_dla_trenera = models.TextField(
        "uwagi niestandardowe dla Prowadzącego",
        blank=True,
    )
    potencjalnie_zainteresowani_mail = models.TextField(
        "treść maila dla potencjalnie zainteresowanych z innych miast",
        blank=True,
    )

    # To pole przechowuje stan wpsiów do kalendarzy o jedną wersję wstecz.
    # Wartość tego pola to po prostu JSON zbudowany z danych obiektu terminu
    # i modelu DzienSzkolenia.
    kalendarze_dane_flat = models.TextField(editable=False, blank=True)
    termin_zdalny = models.ForeignKey(
        "self",
        null=True,
        blank=True,
        unique=True,
        on_delete=models.SET_NULL,
        help_text="To samo szkolenie, w tym samym czasie, ale w Zdalnej lokalizacji.",
    )

    objects = TerminSzkoleniaManager()

    # z_uczestnikami_filter_dummy = models.IntegerField(default=0)
    # z_uczestnikami_filter_dummy.z_uczestnikami_filter = True

    # nierozliczone_filter_dummy = models.IntegerField(default=0)
    # nierozliczone_filter_dummy.nierozliczone_filter = True

    @property
    def language(self):
        return self.szkolenie.language

    CHOICE_TRANSLATIONS = {
        "tryb": {
            "en": ((1, "day classes"), (2, "evening classes"), (3, "weekend classes"))
        }
    }

    def utworz_terminy_podrzedne(self):
        szkolenia = Szkolenie.objects.filter(szkolenie_nadrzedne=self.szkolenie)

        for szkolenie in szkolenia:
            # Obliczamy daty_szczegolowo
            daty_szczegolowo = []
            podzbior_dni = szkolenie.podzbior_dni.split(",")
            daty = list(self.daty())

            if self.obiady != "obiady-opcjonalne":
                obiady = self.obiady
            else:
                # Gdy termin nadrzędny ma ustawione obiady na
                # "obiady-opcjonalne" to w terminach podrzędnych
                # próbujemy je zgadnąć.
                obiady = ""

            try:
                for day in podzbior_dni:
                    daty_szczegolowo.append(daty[int(day) - 1])
            except IndexError:
                # To sie nie powinno nigdy wydarzyć, ale na wszelki
                # wypadek (dla starych archiwalnych terminów).
                pass
            else:
                daty_szczegolowo = sorted(daty_szczegolowo)
                datyszczegolowo = ",".join(
                    [date.isoformat() for date in daty_szczegolowo]
                )

                # Sprawdzamy, czy taki termin juz istnieje
                termin_exists = TerminSzkolenia.objects.filter(
                    termin=daty_szczegolowo[0],
                    szkolenie=szkolenie,
                    lokalizacja=self.lokalizacja,
                    daty_szczegolowo=datyszczegolowo,
                    tryb=self.tryb,
                ).exists()

                if not termin_exists:
                    with transaction.atomic():
                        termin = TerminSzkolenia.objects.create(
                            termin=daty_szczegolowo[0],
                            szkolenie=szkolenie,
                            lokalizacja=self.lokalizacja,
                            daty_szczegolowo=datyszczegolowo,
                            tryb=self.tryb,
                            dodatkowe_uwagi="",
                            obiady=obiady,
                            termin_nadrzedny=self,
                        )

                        try:
                            termin.clean()
                        except:
                            pass
                        termin.save()

                        # Przepisujemy teraz przeciążone dni szkolenia,
                        # ale tylko dla dat, które dotyczą naszego terminu
                        # podrzędnego.
                        for ds in self.dni_szkolenia.filter(data__in=daty_szczegolowo):
                            e = DzienSzkolenia.objects.create(
                                terminszkolenia_id=termin.pk,
                                data=ds.data,
                                prowadzacy_id=ds.prowadzacy_id,
                                sala_id=ds.sala_id,
                            )
                            e.sprzet.add(
                                *tuple(ds.sprzet.all().values_list("id", flat=True))
                            )

                        # Robimy aktualizację dla kalendarzy
                        termin.kalendarze_dane_flat = termin.get_kalendarze_dane_flat(
                            as_json=True
                        )
                        termin.save()
                        update_termin_calendars(termin)

    def wyslane_do_zainteresowanych_z_innych_miast(self):
        """
        Sprawdzamy, czy dla danego terminu została zlecona wysyłka do
        zainteresowanych z innych miast.
        """

        try:
            already_sent = TerminSzkoleniaLog.objects.filter(
                log_type="potencjalnie_zainteresowani_z_innych_miast", term=self
            ).get()
        except TerminSzkoleniaLog.DoesNotExist:
            return None
        else:
            return already_sent

    def tryb_as_symbol(self):
        """
        Zwracamy nazwę trybu jako symbol (do listy dat w terminach).
        """

        return {
            1: "D",
            2: "W",
            3: "Z",
        }.get(self.tryb, "")

    def get_hours(self):
        """
        Zwracamy godziny, w których odbywa się szkolenie.
        """

        if self.niestandardowe_godziny:
            return self.niestandardowe_godziny
        return {1: "9:00 - 17:00", 2: "18:00 - 21:00", 3: "9:00 - 17:00"}[self.tryb]

    def nested_objects_with_invalid_dates(self):
        """
        Metoda sprawdza, czy terminy podrzędne pasują do dat terminu
        nadzrędne (czyli `self`).
        """

        terms = []

        if not self.termin_nadrzedny_id:
            termin_nadrzedny_daty = list(self.daty())

            # Szukamy szkoleń podrzędnych (tylko dla szkoleń nadrzędnych).
            objs = TerminSzkolenia.objects.filter(termin_nadrzedny__pk=self.pk)

            for obj in objs:
                podzbior_dni = obj.szkolenie.podzbior_dni.split(",")
                daty = list(obj.daty())

                if len(podzbior_dni) != len(daty):
                    terms.append(obj)
                    continue

                for no, date in enumerate(daty):
                    day = int(podzbior_dni[no]) - 1

                    try:
                        termin_nadrzedny_date = termin_nadrzedny_daty[day]
                    except IndexError:
                        terms.append(obj)
                        break
                    else:
                        if date != termin_nadrzedny_date:
                            terms.append(obj)
                            break
        return terms

    def is_posiada_akredytacje(self):
        """
        Zwraca T/F czy szkolenie posiada akredytację MEN. Akredytacje
        mogą posiadać konkretne kombinacje szkolenia w danej lokalizacji.
        Fakt, że szkolenie posiada akredytację w jednym mieście nie musi
        oznaczać, że posiada je w innym. Szkolenia akredytowane są automatycznie
        zwolnine z VAT (stawka 0%).
        """
        try:
            self.szkolenie.akredytacje.get(pk=self.lokalizacja.pk)
            return True
        except ObjectDoesNotExist:
            return False

    # jest niby automat, ale my będziemy pytać o angielskie brzemienie
    # pola tryb dla polskich terminów, więc automat się nie załącza
    def get_tryb_display_en(self):
        return dict(self.CHOICE_TRANSLATIONS["tryb"]["en"])[self.tryb]

    def __str__(self):
        return "%s, %s" % (self.termin, self.szkolenie.nazwa)

    def additional_info(self):
        parts = []
        if self.szkolenie.tag_dlugosc.slug == "kurs-zawodowy":
            parts.append("kurs zawodowy")
            parts.append(self.get_tryb_display())
            if self.opis:
                parts.append(self.opis)
        if self.gwarantowany:
            parts.append("termin gwarantowany")
        return ("(" + ", ".join(parts) + ")") if parts else ""

    def termin_do(self):
        if self.termin_zakonczenia == self.termin:
            return None
        if self.termin_zakonczenia:
            return self.termin_zakonczenia
        if self.szkolenie.czas_dni and self.szkolenie.czas_dni > 1:
            return self.termin + datetime.timedelta(self.szkolenie.czas_dni - 1)
        else:
            return None

    def daty(
        self,
        daty_szczegolowo=None,
        termin_zakonczenia=None,
        szkolenie=None,
        termin=None,
    ):
        """
        Iterator po datach terminu.
        """

        daty_szczegolowo = daty_szczegolowo or self.daty_szczegolowo
        termin_zakonczenia = termin_zakonczenia or self.termin_zakonczenia
        szkolenie = szkolenie or self.szkolenie
        termin = termin or self.termin

        if daty_szczegolowo:
            for date_string in daty_szczegolowo.split(","):
                yield datetime.datetime.strptime(date_string, "%Y-%m-%d").date()
            return  # raise StopIteration
        if termin_zakonczenia:
            liczba_dni = (termin_zakonczenia - termin).days + 1
            for i in range(liczba_dni):
                yield termin + datetime.timedelta(days=i)
            return  # raise StopIteration
        """
        Dla kursów nie bierzemy pod uwagę atrybutu czas_dn
        """
        if szkolenie.czas_dni and not szkolenie.tag_dlugosc.slug == "kurs-zawodowy":
            for i in range(szkolenie.czas_dni):
                yield termin + datetime.timedelta(days=i)
            return  # raise StopIteration

    def prowadzacy_per_dni(self):
        result = {}

        wszystkie_daty = list(self.daty())
        przeciazone_daty = []

        for dzien in self.dni_szkolenia.all().select_related("prowadzacy"):
            if dzien.prowadzacy.pk not in result:
                result[dzien.prowadzacy.pk] = {
                    "prowadzacy": dzien.prowadzacy,
                    "dni": [dzien.data],
                }
            else:
                result[dzien.prowadzacy.pk]["dni"].append(dzien.data)
            przeciazone_daty.append(dzien.data)

        # Pozostałe daty (nie przeciążone) przypisz do głównego prowadzącego
        # (jeśli został ustawiony)
        if self.prowadzacy_id:
            if self.prowadzacy.pk not in result:
                result[self.prowadzacy.pk] = {"prowadzacy": self.prowadzacy, "dni": []}

            if not przeciazone_daty:
                result[self.prowadzacy.pk]["dni"] = wszystkie_daty
            else:
                for date in wszystkie_daty:
                    if date not in przeciazone_daty:
                        result[self.prowadzacy.pk]["dni"].append(date)
        return result

    def sale_per_dni(self):
        result = {}

        wszystkie_daty = list(self.daty())
        przeciazone_daty = []

        for dzien in self.dni_szkolenia.all().select_related("sala"):
            if dzien.sala.pk not in result:
                result[dzien.sala.pk] = {"sala": dzien.sala, "dni": [dzien.data]}
            else:
                result[dzien.sala.pk]["dni"].append(dzien.data)
            przeciazone_daty.append(dzien.data)

        # Pozostałe daty (nie przeciążone) przypisz do głównej sali
        # (jeśli została ustawiona)
        if self.sala_id:
            if self.sala.pk not in result:
                result[self.sala.pk] = {"sala": self.sala, "dni": []}

            if not przeciazone_daty:
                result[self.sala.pk]["dni"] = wszystkie_daty
            else:
                for date in wszystkie_daty:
                    if date not in przeciazone_daty:
                        result[self.sala.pk]["dni"].append(date)
        return result

    def sprzet_per_dni(self):
        result = {}

        wszystkie_daty = list(self.daty())
        przeciazone_daty = []

        for dzien in self.dni_szkolenia.all():
            for sprzet in dzien.sprzet.all():
                if sprzet.pk not in result:
                    result[sprzet.pk] = {"sprzet": sprzet, "dni": [dzien.data]}
                else:
                    result[sprzet.pk]["dni"].append(dzien.data)
            przeciazone_daty.append(dzien.data)

        # Pozostałe daty (nie przeciążone) przypisz do głownego sprzętu
        # (jeśli został ustawiony)
        glowny_sprzet = self.sprzet.all()
        if glowny_sprzet.count():
            for sprzet in glowny_sprzet:
                if sprzet.pk not in result:
                    result[sprzet.pk] = {"sprzet": sprzet, "dni": []}

                if not przeciazone_daty:
                    result[sprzet.pk]["dni"] = wszystkie_daty
                else:
                    for date in wszystkie_daty:
                        if date not in przeciazone_daty:
                            result[sprzet.pk]["dni"].append(date)
        return result

    def get_kalendarze_dane_flat(self, as_json=False):
        """
        Zbieramy dane z do aktualizacji kalendarza w formie nadającej się do
        zapisania w bazie danych.

        Jesteśmy an starym Postgresie i Django, więc symulujemy pole JSON.
        """

        prowadzacy = []
        sale = []
        lokalizacje = []

        if self.lokalizacja_id:
            lokalizacje.append(
                {
                    "id": str(self.lokalizacja_id),
                    "calendar": self.lokalizacja.calendar if self.lokalizacja else None,
                    "dni": self.daty_szczegolowo,
                }
            )

        for prowadzacy_id, prowadzacy_dane in list(self.prowadzacy_per_dni().items()):
            if prowadzacy_dane["dni"]:
                prowadzacy.append(
                    {
                        "id": str(prowadzacy_id),
                        "calendar": prowadzacy_dane["prowadzacy"].calendar or "",
                        "dni": ",".join(
                            [d.isoformat() for d in sorted(prowadzacy_dane["dni"])]
                        ),
                    }
                )

        for sala_id, sala_dane in list(self.sale_per_dni().items()):
            if sala_dane["dni"]:
                sale.append(
                    {
                        "id": str(sala_id),
                        "calendar": sala_dane["sala"].calendar or "",
                        "dni": ",".join(
                            [d.isoformat() for d in sorted(sala_dane["dni"])]
                        ),
                    }
                )

        result = {
            "lokalizacje": lokalizacje,
            "prowadzacy": prowadzacy,
            "sale": sale,
        }

        # Liczymy sumy kontrolne (aby łatwo porównywać zmiany)
        for key, value in list(result.items()):
            for pos in value:
                pos["hash"] = hashlib.sha1(
                    force_bytes(
                        "{}|{}|{}".format(pos["id"], pos["calendar"], pos["dni"])
                    )
                ).hexdigest()

        if as_json:
            return json.dumps(result)
        return result

    def ciagly(self):
        """
        Zwrócić info, czy daty są ciągłe.
        Nie uwzględnia pola harmonogram.
        """
        daty = list(self.daty())
        return len(daty) - 1 == (daty[-1] - daty[0]).days

    @property
    def wszyscy_prowadzacy(self):
        if self.prowadzacy:
            yield self.prowadzacy
        for dodatkowy_prowadzacy in self.pozostali_prowadzacy():
            if not dodatkowy_prowadzacy == self.prowadzacy:
                yield dodatkowy_prowadzacy

    def pozostali_prowadzacy(self):
        return [
            e.prowadzacy
            for e in self.dni_szkolenia.all().select_related(
                "prowadzacy", "prowadzacy__user"
            )
        ]

    def zjazdy(self):
        """
        Daje liste zjazdow.
        """
        daty = tuple(self.daty())
        if len(daty) == 0:
            return []
        zjazdy = []
        zjazd = [daty[0], daty[0]]
        for data in daty[1:]:
            if data != zjazd[1] + datetime.timedelta(days=1):
                zjazdy.append(zjazd)
                zjazd = [data, data]
            else:
                zjazd[1] = data
        zjazdy.append(zjazd)
        return zjazdy

    @mark_safe
    def link_do_szkolenia(self):
        if self.szkolenie.kod == "SnZ":
            return escape(self.snz_opis)
        else:
            return '<a href="/admin/www/szkolenie/%d/">%s</a>' % (
                self.szkolenie.id,
                escape(str(self.szkolenie)),
            )

    @mark_safe
    def link_do_ankiety(self):
        linki = [
            '<a href="%s">%s</a>'
            % (
                reverse("admin:ankiety_ankieta_change", args=(ankieta.id,)),
                str(ankieta.czas_zakonczenia.date()),
            )
            for ankieta in self.ankieta_set.all()
        ]
        linki.append(
            '<a href="{0}?termin_szkolenia={1}&prowadzacy={2}">utwórz ankietę</a>'.format(
                reverse("admin:ankiety_ankieta_add"),
                self.id,
                ",".join(str(prowadzacy.id) for prowadzacy in self.wszyscy_prowadzacy),
            )
        )
        return "<br/>\n".join(linki)

    link_do_ankiety.short_description = "Ankiety"

    @mark_safe
    def link_do_wypelniania_ankiety(self):
        return "<br/>\n".join(ankieta.html_url() for ankieta in self.ankieta_set.all())

    link_do_wypelniania_ankiety.short_description = "Ankieta link"

    def kod_szkolenia(self):
        return self.szkolenie.kod

    def rozliczone(self):
        return (
            not self.uczestnik_set.filter(status__in=[1, 3], zaplacone=None)
            if (self.odbylo_sie and self.termin <= datetime.date.today())
            else None
        )

    rozliczone.boolean = True

    def ilosc_uczestnikow(
        self,
        nierentowny=None,
        hybrydowe=False,
        not_statuses=None,
        dodaj_termin_nadrzedny=False,
    ):
        # niezrezygnowanych
        def _sum(szkolenie):
            return sum(
                [
                    x.uczestnik_wieloosobowy_ilosc_osob
                    if x.uczestnik_wieloosobowy_ilosc_osob
                    else 1
                    for x in szkolenie.uczestnicy_niezrezygnowani(
                        nierentowny=nierentowny, not_statuses=not_statuses
                    )
                ]
            )

        result = _sum(self)

        if dodaj_termin_nadrzedny and self.termin_nadrzedny_id:
            result += _sum(self.termin_nadrzedny)
        if hybrydowe and self.hybryda:
            result += self.hybryda.ilosc_uczestnikow(
                nierentowny=nierentowny,
                hybrydowe=False,
                not_statuses=not_statuses,
                dodaj_termin_nadrzedny=False,
            )
        if hybrydowe and dodaj_termin_nadrzedny and self.termin_nadrzedny.hybryda:
            result += self.termin_nadrzedny.hybryda.ilosc_uczestnikow(
                nierentowny=nierentowny,
                hybrydowe=False,
                not_statuses=not_statuses,
                dodaj_termin_nadrzedny=False,
            )
        return result

    def ilosc_uczestnikow_potwierdzonych(self):
        return sum(
            [
                x.uczestnik_wieloosobowy_ilosc_osob
                if x.uczestnik_wieloosobowy_ilosc_osob
                else 1
                for x in self.uczestnik_set.filter(status=1)
            ]
        )

    def ilosc_uczestnikow_z_formalnosciami(self, dodaj_termin_nadrzedny=False):
        def _sum(szkolenie):
            return sum(
                [
                    x.uczestnik_wieloosobowy_ilosc_osob
                    if x.uczestnik_wieloosobowy_ilosc_osob
                    else 1
                    for x in szkolenie.uczestnik_set.filter(status__in=[1, -1])
                ]
            )

        result = _sum(self)
        if dodaj_termin_nadrzedny and self.termin_nadrzedny_id:
            result += _sum(self.termin_nadrzedny)
        return result

    def uczestnicy_niezrezygnowani_potwierdzeni(self):
        """
        Metoda robi to samo, co `uczestnicy_niezrezygnowani`, ale zawęża nam
        jeszcze bardziej grono uczestników, tylko do tych zweryfikowanych i
        potwierdzonych. Wykorzystywana min. w wysłyaniu powiadomień o starcie
        terminu i faktur pro-forma.
        """

        return self.uczestnik_set.filter(status=1)

    def uczestnicy_niezrezygnowani(self, nierentowny=None, not_statuses=None):
        """
        Generalnie lepiej byłoby to zrobić w SQLu, ale w widoku admina
        wykorzystujemy prefetch_related i chcemy z niego skorzystać.
        """

        not_statuses = not_statuses or [4]

        if isinstance(nierentowny, bool):
            func = (
                lambda u: not u.status in not_statuses and u.nierentowny is nierentowny
            )
        else:
            func = lambda u: not u.status in not_statuses
        return list(filter(func, self.uczestnik_set.all()))


    def uczestnicy_do_sprawdzenia(self):
        result = []
        uczestnicy = self.uczestnicy_niezrezygnowani() 
        for uczestnik in uczestnicy:
            if not uczestnik.zaliczka_zaplacone and not uczestnik.faktura_po_szkoleniu:
                result.append(uczestnik)
        return result

    def uczestnicy_niezrezygnowani_indywidualni(self):
        uczestnicy_indywidualni = []

        for uczestnik in self.uczestnicy_niezrezygnowani(
            not_statuses=[-3, -2, -1, 0, 3, 5, 4]
        ):
            if not uczestnik.chce_obiady:
                chce_obiady = "nie"
                chce_obiady_opis = ""
            else:
                if (
                    uczestnik.ile_obiadow_wegetarianskich is None
                    or uczestnik.ile_obiadow_wegetarianskich == 0
                ):
                    chce_obiady = "normalne"
                    chce_obiady_opis = ""
                elif (
                    uczestnik.uczestnik_wieloosobowy_ilosc_osob
                    == uczestnik.ile_obiadow_wegetarianskich
                    or uczestnik.uczestnik_wieloosobowy_ilosc_osob is None
                ):
                    chce_obiady = "wege"
                    chce_obiady_opis = ""
                else:
                    chce_obiady = "rozne"
                    chce_obiady_opis = "%s/%s" % (
                        uczestnik.ile_obiadow_wegetarianskich,
                        uczestnik.uczestnik_wieloosobowy_ilosc_osob,
                    )
            if uczestnik.uczestnik_wieloosobowy_ilosc_osob is None:
                uczestnicy_indywidualni.append(
                    UczestnikIndywidualny(
                        imie_nazwisko=uczestnik.imie_nazwisko,
                        chce_obiady=chce_obiady,
                        chce_obiady_opis=chce_obiady_opis,
                        chce_autoryzacje=uczestnik.chce_autoryzacje,
                        drukowany_certyfikat=uczestnik.drukowany_certyfikat,
                    )
                )
            else:
                imiona_nazwiska = uczestnik.imie_nazwisko.split("\n")
                # if len(imiona_nazwiska) != uczestnik.uczestnik_wieloosobowy_ilosc_osob:
                #   pass
                #   liczba osob uczestnika wieloosobowego jest inna niz iosc imion i nazwisko tego uczestnka!
                #   to chyba niedobrze - moze kiedys chcemy tego pilnowac?
                for i in range(uczestnik.uczestnik_wieloosobowy_ilosc_osob):
                    imie_nazwisko = (
                        imiona_nazwiska[i] if len(imiona_nazwiska) > i else "nieznane"
                    )
                    uczestnicy_indywidualni.append(
                        UczestnikIndywidualny(
                            imie_nazwisko=imie_nazwisko,
                            chce_obiady=chce_obiady,
                            chce_obiady_opis=chce_obiady_opis,
                            chce_autoryzacje=uczestnik.chce_autoryzacje,
                            drukowany_certyfikat=uczestnik.drukowany_certyfikat,
                        )
                    )
        return uczestnicy_indywidualni

    def ile_obiadow(self):
        if self.obiady == "nie-ma-obiadow":
            return ""
        obiadow = 0
        wege = 0
        for u in self.uczestnicy_niezrezygnowani():
            if (
                self.obiady == "obiady-wliczone"
                or self.obiady == "obiady-opcjonalne"
                and u.chce_obiady
            ):
                obiadow += (
                    u.uczestnik_wieloosobowy_ilosc_osob
                    if u.uczestnik_wieloosobowy_ilosc_osob
                    else 1
                )
                wege += (
                    u.ile_obiadow_wegetarianskich
                    if u.ile_obiadow_wegetarianskich
                    else 0
                )
        return "%d (%d wege)" % (obiadow, wege)

    def obiady_data_id_do_formularza_zgloszeniowego(self):
        if self.obiady == "obiady-wliczone":
            return "obiady-wliczone"
        if self.obiady == "obiady-opcjonalne":
            return "obiady-opcjonalne"
        if self.szkolenie.zawiera_obiady:
            if self.obiady == "nie-ma-obiadow" or not self.obiady:
                if self.termin.weekday() == 5:
                    return "nie-ma-obiadow-weekend"
                else:
                    return "nie-ma-obiadow"
        else:
            return ""

        raise NotImplementedError("Nieznany typ obiadow")

    def firma_repr(self):
        uczestnicy = list(self.uczestnicy_niezrezygnowani())
        if len(uczestnicy) == 0:
            return ""
        elif len(uczestnicy) == 1:
            u = uczestnicy[0]
            if u.faktura_firma:
                return u.faktura_firma
            return u.imie_nazwisko_oneline()
        else:
            return "różni ludzie"

    @mark_safe
    def czy_odbedzie_sie(self):
        odbedzie_sie = (
            None
            if self.szkolenie.min_grupa is None
            else self.ilosc_uczestnikow(nierentowny=False) >= self.szkolenie.min_grupa
        )
        suma_do_tej_pory = 0
        if self.szkolenie.min_grupa is None:
            suma_wymagana = None
        else:
            suma_wymagana = self.szkolenie.min_grupa * self.szkolenie.cena
        dni = None
        if self.ile_dni:
            dni = self.ile_dni
        elif self.termin_zakonczenia:
            dni = (self.termin_zakonczenia - self.termin).days + 1
        elif self.szkolenie.czas_dni:
            dni = self.szkolenie.czas_dni
        godzin = None
        if self.szkolenie.czas_godziny:
            godzin = self.szkolenie.czas_godziny
        elif dni:
            godzin = dni * 8
        if (
            suma_wymagana
            and self.obiady == "obiady-wliczone"
            and self.cena_obiadu
            and dni
        ):
            suma_wymagana -= self.szkolenie.min_grupa * self.cena_obiadu * dni
        for u in self.uczestnicy_niezrezygnowani(nierentowny=False):
            suma_do_tej_pory += u.kwota_do_zaplaty
            if self.obiady == "obiady-opcjonalne" and u.cena_obiadow:
                suma_do_tej_pory -= u.cena_obiadow
            if self.obiady == "obiady-wliczone" and self.cena_obiadu and dni:
                n = (
                    u.uczestnik_wieloosobowy_ilosc_osob
                    if u.uczestnik_wieloosobowy_ilosc_osob
                    else 1
                )
                suma_do_tej_pory -= self.cena_obiadu * n * dni

        s = _boolean_icon(odbedzie_sie)
        if godzin:
            s += "<br/>"
            s += "%.0f" % (suma_do_tej_pory / godzin)
            if suma_wymagana:
                s += "/"
                s += "%.0f" % (suma_wymagana / godzin)
        return s

    czy_odbedzie_sie.short_description = "opłacalne"

    def get_ile_dni(self):
        if self.ile_dni:
            return self.ile_dni
        elif self.termin_zakonczenia:
            return (self.termin_zakonczenia - self.termin).days + 1
        elif self.szkolenie.czas_dni:
            return self.szkolenie.czas_dni
        return None

    def odbylo_sie3(self):
        # Dlaczego to nie może być po prostu:
        # return self.termin > datetime.date.today() and self.odbylo_sie
        # ? - KJ
        # bo chcemy odróżniać None od False - JF
        return None if self.termin > datetime.date.today() else self.odbylo_sie

    odbylo_sie3.boolean = True
    odbylo_sie3.short_description = "odbyło się"

    def samodzielny_termin(self):
        # termin, który nie jest faktoryzacją, ani głównym terminem

        return not self.termin_nadrzedny_id and not (
            self.liczba_terminow_podrzednych
            if hasattr(self, "liczba_terminow_podrzednych")
            else self.nadrzedne.all().count()
        )

    def tekst_wolne_miejsca_short(self):
        return (
            self.tekst_wolne_miejsca()
            .replace("miejsca", "msc.")
            .replace("miejsce", "msc.")
            .replace("miejsc", "msc.")
            .replace("rezerwowa", "rez.")
        )

    def _tekst_wolne_miejsca(self):
        if not self.czy_reklamowac or self.nie_pokazuj_wolnych_miejsc:
            return ""

        ###########################
        # Dowolny termin podrzedny
        ###########################
        # if self.termin_nadrzedny_id:
        #     if self.language == "en":
        #         return "last&nbsp;spots"
        #     else:
        #         return "ostatnie&nbsp;miejsca"

        pojemnosc_sali = settings.MAX_KURSANTOW_TOTAL_OLD

        ########################
        # Samodzielne szkolenie
        ########################
        if self.is_szkolenie() and self.samodzielny_termin():
            ilosc_uczestnikow = self.ilosc_uczestnikow()
            wolnych_miejsc = pojemnosc_sali - ilosc_uczestnikow

            if self.language == "en":
                if wolnych_miejsc <= 4:
                    return "last&nbsp;places"
                elif wolnych_miejsc > 8:
                    return "8&nbsp;places&nbsp;left"
                elif wolnych_miejsc >= 5:
                    return "%d&nbsp;places&nbsp;left" % wolnych_miejsc
            else:
                if wolnych_miejsc <= 4:
                    return "ostatnie&nbsp;miejsca"
                elif wolnych_miejsc > 8:
                    return "zostało&nbsp;8&nbsp;miejsc"
                elif wolnych_miejsc >= 5:
                    return "zostało&nbsp;%d&nbsp;miejsc" % wolnych_miejsc

        ############################
        # Kazdy inny kurs/szkolenie
        ############################

        if self.hybryda:
            # Dla terminu "zdalnie" miejsca wynikają z liczby zapisów razem vs.
            # max_kursantow_total;
            pojemnosc_sali = settings.MAX_KURSANTOW_TOTAL
            ilosc_uczestnikow = self.ilosc_uczestnikow(
                hybrydowe=True, dodaj_termin_nadrzedny=bool(self.termin_nadrzedny_id)
            )
            if ilosc_uczestnikow >= 12:
                ilosc_uczestnikow = self.ilosc_uczestnikow(
                    hybrydowe=True,
                    not_statuses=[-3, -2, 0, 3, 5, 4],
                    dodaj_termin_nadrzedny=bool(self.termin_nadrzedny_id),
                )
            wolnych_miejsc = pojemnosc_sali - ilosc_uczestnikow
            # dla terminu "stacjonarnie", wolno wpuścić nie więcej niż
            # MAX_KURSANTOW_STACJONARNIE na salę, jak również nie więcej niż
            # MAX_KURSANTOW_TOTAL razem hybrydowo — brać mniejsze z tych dwóch
            if self.hybryda_stacjonarna and wolnych_miejsc > 0:
                pojemnosc_sali = settings.MAX_KURSANTOW_STACJONARNIE
                ilosc_uczestnikow = self.ilosc_uczestnikow(
                    dodaj_termin_nadrzedny=bool(self.termin_nadrzedny_id)
                )
                if ilosc_uczestnikow >= 12:
                    ilosc_uczestnikow = self.ilosc_uczestnikow(
                        not_statuses=[-3, -2, 0, 3, 5, 4],
                        dodaj_termin_nadrzedny=bool(self.termin_nadrzedny_id),
                    )
                wolnych_miejsc = min(wolnych_miejsc, pojemnosc_sali - ilosc_uczestnikow)
        else:
            pojemnosc_sali = settings.MAX_KURSANTOW_TOTAL_OLD
            ilosc_uczestnikow = self.ilosc_uczestnikow()
            wolnych_miejsc = pojemnosc_sali - ilosc_uczestnikow
            # Gdy zostało mało miejsc, liczymy tylko potwierdzonych użytkowników,
            # którzy dokonali formalności:
            #
            #  -1 - powiedzieliśmy że niepewne, skontaktować się znów przed szkoleniem
            #  1 - potwierdzone, odbywa się, uczestnik wszystko wie
            if ilosc_uczestnikow >= 12:
                wolnych_miejsc = (
                    pojemnosc_sali
                    - self.ilosc_uczestnikow_z_formalnosciami(
                        dodaj_termin_nadrzedny=bool(self.termin_nadrzedny_id)
                    )
                )

        wolnych_miejsc_replace = 8
        if wolnych_miejsc > 8:
            dmod = (
                int(self.created_at.timestamp() * 100) if self.created_at else self.pk
            ) % 3
            if dmod == 0:
                wolnych_miejsc_replace = 7
            elif dmod == 1:
                wolnych_miejsc_replace = 8
            elif dmod == 2:
                wolnych_miejsc_replace = 9

        if self.language == "en":
            if wolnych_miejsc <= 0:
                return "waiting&nbsp;list"
            elif wolnych_miejsc > 8:
                return "{}&nbsp;places&nbsp;left".format(
                    wolnych_miejsc_replace
                )
            elif wolnych_miejsc >= 5:
                # return "only&nbsp;5&nbsp;spots&nbsp;left"
                return "%d&nbsp;places&nbsp;left" % wolnych_miejsc
            elif wolnych_miejsc > 1:
                return "%d&nbsp;places&nbsp;left" % wolnych_miejsc
            return "1&nbsp;place;&nbsp;left"
        else:
            if wolnych_miejsc <= 0:
                # return "lista&nbsp;rezerwowa"
                return "ostatnie&nbsp;miejsca"
            elif wolnych_miejsc > 8:
                return "zostało&nbsp;{}&nbsp;miejsc".format(wolnych_miejsc_replace)
            elif wolnych_miejsc >= 5:
                # return "zostało&nbsp;5&nbsp;miejsc"
                return "zostało&nbsp;%d&nbsp;miejsc" % wolnych_miejsc
            elif wolnych_miejsc > 1:
                return "zostały&nbsp;%d&nbsp;miejsca" % wolnych_miejsc
            return "zostało&nbsp;1&nbsp;miejsce"

    def tekst_wolne_miejsca(self, kurs_gwarantowany=False):
        result = self._tekst_wolne_miejsca()

        if self.odbylo_sie or self.gwarantowany:
            if self.language == "en":
                result = f"guaranteed, {result}" if result else "guaranteed"
            else:
                if self.is_kurs():
                    result = f"gwarantowany, {result}" if result else "gwarantowany"
                elif self.is_szkolenie():
                    result = f"gwarantowane, {result}" if result else "gwarantowane"

        return result


    def tekst_uwagi(self):
        dodatkowe_uwagi = self.szkolenie.get_tryb_opis(self.tryb)

        # Jesli uwagi zostały przeciążone w terminie to je podmieniamy.
        if self.dodatkowe_uwagi and self.dodatkowe_uwagi != dodatkowe_uwagi:
            dodatkowe_uwagi = self.dodatkowe_uwagi
        # Zostawiamy dla wstecznej zgodnosci.
        return dodatkowe_uwagi.replace("[[noappend]]", "").strip()

    def uwagi_i_wolne_miejsca(self):
        uwagi = self.tekst_uwagi()

        wolne_miejsca = self.tekst_wolne_miejsca()
        if uwagi:
            uwagi = " " + uwagi
        if wolne_miejsca:
            wolne_miejsca = ", " + wolne_miejsca
        return uwagi + wolne_miejsca

    def kod_szkolenia_lub_snz(self):
        if self.is_snz():
            return "SnZ: {}...".format((self.snz_opis or "")[:30])
        else:
            return self.szkolenie.kod

    def nazwa_szkolenia_lub_snz_opis(self):
        if self.is_snz():
            return "SnZ: " + self.snz_opis
        else:
            return str(self.szkolenie)

    def nazwa_szkolenia_lub_snz(self):
        if self.is_snz():
            return self.snz_opis
        else:
            return self.szkolenie.nazwa

    def nazwa_szkolenia_lub_snz_en(self):
        """
        Zawsze staramy się zwrócić angielski tytuł szkolenia (jeśli
        istanieje), potrzebne np. do nazw certyfikatów na LinkedIn.
        """

        if self.is_snz():
            return self.snz_opis
        else:
            t = self.szkolenie.en_or_self()
            return t.nazwa if t else self.szkolenie.nazwa

    def is_przyszle(self):
        return self.termin > datetime.date.today()

    def is_snz(self):
        return self.szkolenie.kod == "SnZ"

    def is_szkolenie(self):
        return self.szkolenie.tag_dlugosc.slug == "szkolenie"

    def is_kurs(self):
        return self.szkolenie.tag_dlugosc.slug != "szkolenie"

    def is_weekendowy(self):
        return self.termin.weekday() in [5, 6]

    def opisy_zjazdow_dni(self):
        opisy_zjazdow = []
        for od, do in self.zjazdy():
            if od.day == do.day and od.month == do.month and od.year == do.year:
                opisy_zjazdow.append("%d.%02d.%04d" % (do.day, do.month, do.year))
            elif od.month == do.month and od.year == do.year:
                opisy_zjazdow.append(
                    "%d-%d.%02d.%04d" % (od.day, do.day, do.month, do.year)
                )
            else:
                opisy_zjazdow.append(
                    "%d.%02d.%04d-%d.%02d.%04d"
                    % (od.day, od.month, od.year, do.day, do.month, do.year)
                )
        return to_sentence(opisy_zjazdow, _("oraz"))

    def allow_to_generate_certificates(self, print_only=False):
        """
        Metoda sprawdza, czy moderator może wygenerować certyfikaty dla
        uczestników danego terminu. Aby było to możliwe:
          - szkolenie musi być zakończone (tzn. odbylo_sie == True)
          - generowanie certyfikatów nie odbyło się wcześniej [1]

        Zakładamy, że szablon certyfikatu zawsze istnieje (jeśli nie w
        obiekcie to w pliku szablonu)

        [1] Jeśli opcja `print_only == True` nie jest istotne, że generowanie
        odbyło się już wcześniej. Biuro wydrukować może zawsze.
        """

        if print_only:
            return self.odbylo_sie
        else:
            return not self.notyfikacje_o_certyfikatach and self.odbylo_sie

    def get_program_szkolenia(self):
        """
        Funkcja zwraca program szkolenia. Pierszeństwo ma ten nadpisany w
        obiekcie terminu.
        """

        return self.program_szkolenia or self.szkolenie.program_szkolenia

    def get_szablon_certyfikatu(self):
        """
        Funkcja zwraca szablon certyfikatu. Pierszeństwo ma ten nadpisany w
        obiekcie terminu, następnie ten zdefiniowany w obiekcie Szkolenia.
        Gdy oba są puste, zwracany jest domyślny z pliku (uwzględniając język).
        """

        template = self.szablon_certyfikatu or self.szkolenie.szablon_certyfikatu

        if not template:
            # Pobieramy z pliku
            template = get_template_from_file("www/certificates/certificate_pdf.html")

        return template

    def date_range(self):
        """
        Funkja zwraca zakres dat dla certyfikatów. Jeśli jest więcej jak
        jedna, bierzmy skrajne zakresy.
        """

        dates = list(self.daty())

        if len(dates) > 1:
            dates = [dates[0], dates[-1]]
        return dates

    def autoryzacja(self):
        """
        Zwraca autoryzację, jeśli jest ona aktywna dla terminu i szkolenie
        ma przypisaną autoryzację.
        """

        if self.szkolenie.autoryzacja and self.autoryzacja_aktywna:
            return self.szkolenie.autoryzacja
        else:
            return None

    @cached_property
    def hybryda(self):
        return self.powiazany_stacjonarny or self.termin_zdalny

    @cached_property
    def hybryda_zdalna(self):
        return self.powiazany_stacjonarny

    @cached_property
    def hybryda_stacjonarna(self):
        return self.termin_zdalny

    @cached_property
    def powiazany_stacjonarny(self):
        if self.lokalizacja.zdalna:
            return TerminSzkolenia.objects.filter(termin_zdalny_id=self.id).first()

    def ilosc_uczestnikow_rentownych_all_as_text(self):
        text = self.ilosc_uczestnikow_rentownych_as_text()

        if self.termin_zdalny_id:
            text = "zdalnie: {}, stacjonarnie: {}, razem: {}".format(
                self.termin_zdalny.ilosc_uczestnikow_rentownych_as_text(),
                text,
                self.ilosc_uczestnikow_rentownych_z_terminem_zdalnym_as_text(),
            )
        elif self.powiazany_stacjonarny:
            text = "zdalnie: {}, stacjonarnie: {}, razem: {}".format(
                text,
                self.powiazany_stacjonarny.ilosc_uczestnikow_rentownych_as_text(),
                self.ilosc_uczestnikow_rentownych_z_terminem_zdalnym_as_text(),
            )
        return text

    def ilosc_uczestnikow_rentownych_z_terminem_zdalnym_as_text(self):
        """
        Pokazujemy wszystkich użytkowników z informacją ilu jest
        rentownych, np: 5 / 1R + tryb zdalny.
        Przeniesione do modelu z racji potrzeby wykorzystania w różnych
        miejscach apliakcji.
        """

        text = ""

        if not self.termin_zdalny_id and not self.powiazany_stacjonarny:
            return self.ilosc_uczestnikow_rentownych_as_text()

        if self.termin_zdalny:
            text = "{0} / {1}R".format(
                self.ilosc_uczestnikow() + self.termin_zdalny.ilosc_uczestnikow(),
                self.ilosc_uczestnikow(nierentowny=False)
                + self.termin_zdalny.ilosc_uczestnikow(nierentowny=False),
            )
        elif self.powiazany_stacjonarny:
            text = "{0} / {1}R".format(
                self.ilosc_uczestnikow()
                + self.powiazany_stacjonarny.ilosc_uczestnikow(),
                self.ilosc_uczestnikow(nierentowny=False)
                + self.powiazany_stacjonarny.ilosc_uczestnikow(nierentowny=False),
            )
        return text

    def ilosc_uczestnikow_rentownych_as_text(self, add_related=False):
        """
        Pokazujemy wszystkich użytkowników z informacją ilu jest
        rentownych, np: 5 / 1R.
        Przeniesione do modelu z racji potrzeby wykorzystania w różnych
        miejscach apliakcji.
        """

        text = "{0} / {1}R".format(
            self.ilosc_uczestnikow(),
            self.ilosc_uczestnikow(nierentowny=False),
        )

        if add_related:
            # Dwa przypadki:
            # 1. jest podzbiorem
            if self.termin_nadrzedny_id:
                link = reverse(f"admin:{self.termin_nadrzedny._meta.app_label}_{self.termin_nadrzedny._meta.model_name}_change", args=[self.termin_nadrzedny.id])
                text = f"""{text}<br/>
                <a href="{link}">{self.termin_nadrzedny.szkolenie.kod}</a>: 
                { self.termin_nadrzedny.ilosc_uczestnikow_rentownych_as_text(add_related=False)}"""

            else:
                # 2. jest nadzbiorem
                for termin in self.nadrzedne.select_related("szkolenie"):
                    link = reverse(f"admin:{termin._meta.app_label}_{termin._meta.model_name}_change", args=[termin.id])
                    text += f"""<br/><a href='{link}'>{termin.szkolenie.kod}</a>: {termin.ilosc_uczestnikow_rentownych_as_text(add_related=False)}<br>"""

        return text

    def ilosc_uczestnikow_dla_faktoryzacji(self):
        result = []
        if self.termin_nadrzedny_id:
            result.append(
                {
                    "szkolenie": self.termin_nadrzedny.szkolenie,
                    "ilosc_uczestnikow": self.termin_nadrzedny.ilosc_uczestnikow_rentownych_all_as_text(),
                }
            )
        else:
            for termin in self.nadrzedne.select_related("szkolenie"):
                result.append(
                    {
                        "szkolenie": termin.szkolenie,
                        "ilosc_uczestnikow": termin.ilosc_uczestnikow_rentownych_all_as_text(),
                    }
                )
        return result

    def clean(self):
        # to łapanie wyjątków DoesNotExist jest dlatego, że jak człowiek nie
        # wypełni niektórych wymaganych pól w adminie, to tutaj dostajemy
        # takie wyjątki przy próbie dostępu do tych pól ignorujemy je -
        # chcemy, żeby się wyświetliło, że form się nie zwalidował zamiast
        # pińcetki
        if not self.obiady:
            try:
                if self.tryb == 2:
                    self.obiady = "nie-ma-obiadow"
                else:
                    self.obiady = {False: "obiady-opcjonalne", True: "obiady-wliczone"}[
                        self.szkolenie.zawiera_obiady
                    ]
            except Szkolenie.DoesNotExist:
                pass
        if self.obiady == "obiady-opcjonalne" and not self.cena_obiadu:
            try:
                if self.lokalizacja.domyslna_cena_obiadu:
                    self.cena_obiadu = self.lokalizacja.domyslna_cena_obiadu
                else:
                    raise ValidationError(
                        "Jeśli obiady są opcjonalne, to musi być podana cena obiadu."
                    )
            except Lokalizacja.DoesNotExist:
                pass
        try:
            if self.autoryzacja_aktywna and not self.szkolenie.autoryzacja:
                raise ValidationError(
                    "Autoryzacja nie może być aktywna, jeśli szkolenie nie ma przypisanej autoryzacji."
                )
        except Szkolenie.DoesNotExist:
            pass
        try:
            if not self.daty_szczegolowo:
                if self.szkolenie.tag_dlugosc.slug == "szkolenie":
                    if not self.szkolenie.czas_dni:
                        raise ValidationError("Musisz podać szczegółowe daty.")
                    self.daty_szczegolowo = ",".join(
                        [
                            (self.termin + datetime.timedelta(days=i)).isoformat()
                            for i in range(self.szkolenie.czas_dni)
                        ]
                    )
                elif self.szkolenie.tag_dlugosc.slug == "kurs-zawodowy":
                    raise ValidationError(
                        "Kurs musi mieć podane szczegółowe daty kiedy się odbywa."
                    )
        except Szkolenie.DoesNotExist:
            pass
        self.ile_dni = len(self.daty_szczegolowo.split(","))
        try:
            if (
                self.szkolenie.tag_dlugosc.slug == "kurs-zawodowy"
                and self.obiady == "obiady-opcjonalne"
            ):
                if not self.ile_dni:
                    raise ValidationError(
                        "Jeśli kurs zawodowy ma opcjonalne obiady, to musi mieć wpisaną liczbę dni, żeby było wiadomo ile obiadów."
                    )
        except Szkolenie.DoesNotExist:
            pass
        try:
            if (
                self.szkolenie.language == "pl"
                and "Polska" not in self.lokalizacja.panstwo.nazwa
            ):
                raise ValidationError(
                    "Nie można zrobić polskiego szkolenia za granicą."
                )
        except (Szkolenie.DoesNotExist, Lokalizacja.DoesNotExist):
            pass
        if self.daty_szczegolowo:
            termin_zakonczenia_str = self.daty_szczegolowo.split(",")[-1]
            m = re.match(r"(\d+)-(\d+)-(\d+)", termin_zakonczenia_str)
            if m:
                self.termin_zakonczenia = datetime.date(
                    int(m.group(1)), int(m.group(2)), int(m.group(3))
                )
            else:
                raise ValidationError("zły format pola daty_szczegolowo")

        if self.daty_szczegolowo:
            liczba_dat_szczegolowo = len(self.daty_szczegolowo.split(","))

            try:
                if self.szkolenie.tag_dlugosc.slug == "szkolenie":
                    wymagana_liczba_dat = self.szkolenie.liczba_dni_liczbowo()
                    if (
                        wymagana_liczba_dat
                        and liczba_dat_szczegolowo != wymagana_liczba_dat
                    ):
                        raise ValidationError(
                            "Liczba podanych dat szczegółowych ({0}) nie zgadza się z liczbą wymaganą dla szkolenia ({1}).".format(
                                liczba_dat_szczegolowo, wymagana_liczba_dat
                            )
                        )
                else:
                    wymagana_liczba_dat = (
                        self.czas_dni_kurs or self.szkolenie.czas_dni_kurs
                    )
                    if (
                        wymagana_liczba_dat
                        and liczba_dat_szczegolowo != wymagana_liczba_dat
                    ):
                        raise ValidationError(
                            "Liczba podanych dat szczegółowych ({0}) nie zgadza się z liczbą wymaganą dla kursu ({1}).".format(
                                liczba_dat_szczegolowo, wymagana_liczba_dat
                            )
                        )
            except Szkolenie.DoesNotExist:
                pass

        if (
            self.pk
            and not self.odbylo_sie
            and self.uczestnik_set.filter(status=3).exists()
        ):
            raise ValidationError(
                "Termin ma przeszkolonych uczestników – nie można ustawić, że się nie odbywa."
            )

        if self.odbylo_sie:
            self.czy_reklamowac = True

        if self.prywatna_rejestracja and (
            not self.zamkniete
            or not self.liczba_osob_w_grupie
            or not self.cena_bazowa_do_n_osob
            or self.cena_za_osobe_powyzej_n_osob is None
        ):
            raise ValidationError(
                "Uzupełnij dane dotyczące terminu zamkniętego oraz upewnij się, że termin jest oznaczony jako zamknięty."
            )

    def run_celery_jobs_on_conditions(self, current_user_id=None):
        """
        Funkcja zleca wysyłkę powiadomien i faktur pro-forma do uczestników,
        po spełnieniu warunków:

        Jeśli wartość `jobs_state` w nowym obiekcie (`self`) istnieje,
        a stary obiket (`old`) nie istnieje (czyli nastapiło dodanie), lub
        nie posiada wartości dla pola `jobs_state`, to znaczy, że możemy
        zlecić wysyłkę powiadomień.
        """

        # Pobierz wersję obiektu przed zapisem
        old = getattr(self, "_original_state", None)

        if self.internal_jobs_state and (not old or not old["internal_jobs_state"]):
            transaction.on_commit(
                lambda: www.tasks.poinformuj_biuro_o_potwierdzeniu_terminu.apply_async(
                    args=(self.pk, current_user_id),
                    countdown=30,
                )
            )

        if (
            self.jobs_state
            and (not old or not old["jobs_state"])
            and self.szkolenie.wysylaj_powiadomienia_proformy
        ):
            # www.tasks.poinformuj_o_potencjalnie_zainteresowanych.delay(self.pk)
            transaction.on_commit(
                lambda: www.tasks.poinformuj_o_potencjalnie_chetnych.apply_async(
                    args=(self.pk,),
                    countdown=60,
                )
            )
            transaction.on_commit(
                lambda: www.tasks.poinformuj_trenerow_o_potwierdzeniu_terminu.apply_async(
                    args=(self.pk, current_user_id),
                    countdown=60,
                )
            )
            if self.lokalizacja.shortname != "wyjazdowe":
                transaction.on_commit(
                    lambda: www.tasks.poinformuj_zapisanych_o_potwierdzeniu_terminu.apply_async(
                        args=(self.pk,),
                        countdown=60,
                    )
                )

    def save(self, *args, **kwargs):
        _raw_save = kwargs.pop("_raw_save", False)

        super().save(*args, **kwargs)

        if not _raw_save:
            # W przypadku późniejszego wołania `super(TerminSzkolenia, self).
            # save` należy pamiętać, że obiekt wymaga aktualizacji nie inserta.
            kwargs["force_insert"] = False

            old = getattr(self, "_original_state", None)
            save = False

            if (
                not self.jobs_state
                and self.odbylo_sie
                and not self.zamkniete
                and not (old and old["odbylo_sie"] and not old["zamkniete"])
                and not self.termin < datetime.datetime.now().date()
            ):

                # Ustaw flagę `jobs_state` oznaczajacą, że zadania jednarazowe
                # mogą zostać zlecone.
                #
                # Uwaga: tutaj tylko oznaczamy gotowość obiektu do wykonania
                #        zadań Celery. Samo ich wywołanie następuje w
                #        `TerminSzkoleniaAdmin` w metodzie
                #        `after_saving_model_and_related_inlines`. Tam też
                #        znajduje się opis dlaczego tak robimy.
                self.jobs_state = datetime.datetime.now()
                save = True

            if (
                not self.internal_jobs_state
                and self.odbylo_sie
                and not self.termin < datetime.datetime.now().date()
            ):

                # Ustaw flagę `internal_jobs_state` oznaczajacą, że zadania jednarazowe
                # (do uzytku Biura) mogą zostać zlecone.
                #
                # Uwaga: tutaj tylko oznaczamy gotowość obiektu do wykonania
                #        zadań Celery. Samo ich wywołanie następuje w
                #        `TerminSzkoleniaAdmin` w metodzie
                #        `after_saving_model_and_related_inlines`. Tam też
                #        znajduje się opis dlaczego tak robimy.
                self.internal_jobs_state = datetime.datetime.now()
                save = True
            if save:
                return super().save(*args, **kwargs)

            # old = getattr(self, '_original_state', None)

            # if getattr(settings, 'ZLICZACZ_ADD_TASK_URL', None):
            #     if self._zliczacz_add_task_url(old):
            #         super(TerminSzkolenia, self).save(*args, **kwargs)

    def delete(self, *args, **kwargs):
        update_termin_calendars(self, delete=True)
        super().delete(*args, **kwargs)


class DzienSzkolenia(models.Model):
    data = models.DateField("data")
    terminszkolenia = models.ForeignKey(
        "www.TerminSzkolenia",
        verbose_name="termin szkolenia",
        related_name="dni_szkolenia",
        on_delete=models.CASCADE,
    )
    prowadzacy = models.ForeignKey(
        "www.Prowadzacy", verbose_name="prowadzący", on_delete=models.PROTECT
    )
    sala = models.ForeignKey("www.Sala", verbose_name="sala", on_delete=models.PROTECT)
    sprzet = models.ManyToManyField("www.Sprzet", verbose_name="sprzęt")

    # Daty
    created_at = models.DateTimeField("utworzono", auto_now_add=True, null=True)
    updated_at = models.DateTimeField("aktualizowano", auto_now=True, null=True)

    class Meta:
        verbose_name = "dzień szkolenia"
        verbose_name_plural = "dni szkolenia"
        unique_together = ("data", "terminszkolenia")
        ordering = ("data",)

    def __str__(self):
        return ""


# A flatpage is a simple object with a URL, title and content.
# - slug
# - header_kwd
# - header_descr
# - title
# - lead
# - content
# ... and content is rendered by |safe (notice: NOT |textile)


class MyFlatPageManager(models.Manager):
    def get_etag(self, slug, language):
        """
        Funkcja wylicza po stronie bazy ciąg MD5 używany jako ETag żądania.
        Do wyliczenia ETaga danej strony używane są:

        - stały prefix
        - id strony
        - data aktualizacji strony
        - data aktualizacji szkolenia
        - data aktualizacji przyszłych terminów powyższego szkolenia
        - data modyfikacji obiektów SiteModule
        - data modyfikacji obiektów MyFlatPage
        - data modyfikacji obiektów Prowadzacy
        """

        cursor = connection.cursor()

        cursor.execute(
            """
            SELECT
                md5(
                    concat(
                        'flatpage'::text,
                        '_'::text,
                        www_myflatpage.id::text,
                        '_'::text,
                        www_myflatpage.updated_at::text,
                        '_'::text,
                        www_szkolenie.updated_at::text,
                        '_'::text,
                        string_agg(www_terminszkolenia.updated_at::text, '|'
                            ORDER BY www_terminszkolenia.updated_at ASC),
                        '_'::text,
                        (SELECT
                            string_agg(www_sitemodule.updated_at::text, '|'
                                ORDER BY www_sitemodule.updated_at ASC)
                        FROM www_sitemodule
                        WHERE www_sitemodule.language = %s
                            AND www_sitemodule.enabled = TRUE
                        ),
                        '_'::text,
                        (SELECT
                            string_agg(www_myflatpage_uq.updated_at::text, '|'
                                ORDER BY www_myflatpage_uq.updated_at ASC)
                        FROM www_myflatpage AS www_myflatpage_uq
                        WHERE www_myflatpage_uq.language = %s
                        ),
                        '_'::text,
                        (SELECT
                            string_agg(www_prowadzacy.updated_at::text, '|'
                                ORDER BY  www_prowadzacy.updated_at ASC)
                        FROM  www_prowadzacy
                        )
                    )
                ) AS etag
            FROM www_myflatpage
            LEFT JOIN www_szkolenie
                ON www_myflatpage.szkolenie_id = www_szkolenie.id
                    AND www_szkolenie.aktywne = TRUE
            LEFT JOIN www_terminszkolenia
                ON ((
                        www_terminszkolenia.szkolenie_id = www_szkolenie.id
                        OR
                        www_terminszkolenia.szkolenie_id IN (
                            SELECT
                                id
                            FROM www_szkolenie as www_szkolenie_l
                            WHERE www_szkolenie_l.base_translation_id = www_szkolenie.id
                                AND www_szkolenie.language = 'pl'
                        )
                    )
                    AND www_terminszkolenia.zamkniete = FALSE
                    AND www_terminszkolenia.termin >= CURRENT_DATE
                )
            WHERE www_myflatpage.slug = %s
                AND www_myflatpage.enabled = TRUE
                AND www_myflatpage.language = %s
            GROUP BY www_myflatpage.updated_at, www_myflatpage.id, www_szkolenie.updated_at;
        """,
            [language, language, slug, language],
        )

        try:
            etag = cursor.fetchone()[0]
        except:
            return None
        else:
            return etag


class MyFlatPage(MappedTranslatedModel):
    class Meta:
        verbose_name_plural = "Strony statyczne (my flat pages)"
        ordering = ["ordering"]
        unique_together = ("slug", "language")

    header_title = models.CharField(max_length=200)
    h1_title = models.CharField(max_length=200, blank=True)
    slug = models.SlugField(max_length=50, blank=True)
    domena = models.ForeignKey(
        "Domena", blank=True, null=True, on_delete=models.PROTECT
    )
    content_group = models.ForeignKey(
        "ContentGroup",
        blank=True,
        null=True,
        verbose_name="grupa treści",
        on_delete=models.PROTECT,
    )
    logo = models.ForeignKey("Logo", blank=True, null=True, on_delete=models.PROTECT)
    header_kwd = models.TextField(
        null=True,
        blank=True,
        help_text="Wycofane z produkcyjnego użycia od 2022 r. - pole w bazie zachowane dla celów archiwalnych.",
    )
    header_descr = models.TextField(null=True, blank=True)
    ordering = models.IntegerField(null=True, blank=True)
    enabled = models.BooleanField(default=True)
    lead = models.TextField(null=True, blank=True)
    css_content = models.TextField(
        "CSS",
        null=True,
        blank=True,
        help_text="Jeśli istnieje zostanie dołączony do sekcji <head> strony.",
    )
    content = models.TextField(null=True, blank=True)
    highlights = GenericRelation("HighlightPlacement")
    is_landingpage = models.BooleanField(
        "landingpage?",
        default=False,
        help_text="Gdy zaznaczone z szablonu usuwane są różne elementy "
        "(np. header, prawe menu itp).",
    )
    is_bootcamp = models.BooleanField(
        "bootcamp?",
        default=False,
        help_text="Gdy zaznaczone stosowany jest nowy szablon (min. header).",
    )
    is_searchable = models.BooleanField(
        "dostępna w wyszukiwarce i dla robotów internetowych?",
        default=True,
        help_text="Gdy zaznaczone strona jest indeksowana w wyszukiwarkach internetowych, Sitemaps i w wyszukiwarce ALX.",
    )    
    # strona_kursu  = models.BooleanField(default=False)
    szkolenie = models.ForeignKey(
        Szkolenie, blank=True, null=True, on_delete=models.PROTECT
    )

    internal_comments = models.TextField("uwagi internal", blank=True)

    # Daty
    created_at = models.DateTimeField(
        "utworzono", auto_now_add=True, null=True, db_index=True
    )
    updated_at = models.DateTimeField("aktualizowano", auto_now=True, null=True)

    is_blog_entry = models.BooleanField(
        "wpis na blog?",
        default=False,
        help_text="Gdy zaznaczone zostanie dodany autor wpisu i data.",
    )
    blog_author = models.CharField("[blog] autor", max_length=250, blank=True)
    blog_published_at = models.DateField("[blog] data publikacji", blank=True, null=True)

    objects = MyFlatPageManager()

    def __str__(self):
        return self.header_title

    def clean(self):
        if not self.slug:
            self.slug = my_slugify.slugify(self.header_title)[:50]

    def get_absolute_url(self):
        return reverse(
            "my_flat_page", kwargs={"slug": self.slug, "language": self.language}
        )

    def get_taby(self):
        taby = self.tab_set.order_by("ordering")
        if taby:
            taby[0].selected = True
        return taby

    def get_program_tab(self):
        return self.tab_set.filter(slug__iexact="program").first()

    def get_title(self):
        return self.h1_title or self.header_title

    def clean_content(self):
        cleaner = Cleaner(
            scripts=True,
            javascript=True,
            comments=True,
            style=True,
            links=True,
            meta=True,
            page_structure=True,
            processing_instructions=True,
            embedded=True,
            frames=True,
            forms=True,
            annoying_tags=True,
        )
        try:
            return cleaner.clean_html(self.content or self.header_descr)
        except (ParserError, XMLSyntaxError):
            return ""


class Tab(models.Model):
    slug = models.CharField(max_length=50, blank=True)
    title = models.CharField(max_length=200)
    content = models.TextField(blank=True)
    flatpage = models.ForeignKey(MyFlatPage, on_delete=models.PROTECT)
    ordering = models.IntegerField(blank=True, null=True)

    def clean(self):
        if not self.slug:
            self.slug = my_slugify.slugify(self.title)

    def __str__(self):
        return self.slug

    class Meta:
        ordering = ["ordering"]


class MenuItem(TranslatedModel):
    class Meta:
        ordering = ["ordering"]

    title = models.CharField(max_length=100)
    ordering = models.IntegerField()
    enabled = models.BooleanField(default=True)
    indent_level = models.IntegerField(default=0)
    # null oznacza: pozycja menu bez linka - po prostu napis (np. nazwa sekcji).
    link_url = models.CharField(max_length=200, null=True, blank=True)
    separator_above = models.BooleanField(default=False)

    def __str__(self):
        return self.title

    def item_type(self):
        if self.url:
            if re.search("http:", self.url):
                return "External"
            else:
                return "Internal"
        else:
            return "No URL"


class ContentGroup(models.Model):
    title = models.CharField("tytuł", max_length=100)
    slug = models.SlugField("slug", unique=True)

    class Meta:
        verbose_name = "grupa treści"
        verbose_name_plural = "grupy treści"

    def __str__(self):
        return self.title

    def replace_modules(self, modules):
        """Zastępuje moduły na przekazanej liście modułów własnymi modułami. Zwraca posortowaną listę."""
        modules = list(modules)
        own_modules = self.sitemodule_set.filter(language=translation.get_language())
        for own_module in own_modules:
            if own_module.overrides:
                # Nie szukamy modułu za pomocą index(), bo to nie muszą być te same obiekty w Pythonie.
                for overriden_module in [
                    module for module in modules if module.pk == own_module.overrides.pk
                ]:
                    modules[modules.index(overriden_module)] = own_module
            else:
                modules.append(own_module)
        modules = [module for module in modules if module.enabled_synced]
        modules = sorted(modules, key=lambda module: module.ordering_synced)
        return modules


class SiteModule(TranslatedModel):
    attribute_inheritance_help_text = (
        "W przypadku pustej wartości, zostanie ona pobrana z nadpisywanego obiektu."
    )
    attribute_inheritance_error_text_template = (
        "Pole {field_name} musi być wypełnione, jeśli nie podano nadpisywanego"
        " obiektu."
    )

    title = models.CharField(
        max_length=100, blank=True, help_text=attribute_inheritance_help_text
    )
    title_cta = models.CharField(
        max_length=250, blank=True, help_text='call to action - np. link'
    )

    pokazuj_tytul = models.BooleanField(
        "pokazuj tytuł",
        default=True,
        help_text="Jeśli odznaczone cały niebieski nagłówek wraz z tytułem nie będzie wyświetlany.",
    )
    content_group = models.ForeignKey(
        ContentGroup,
        blank=True,
        null=True,
        verbose_name="grupa treści",
        help_text="Jeżeli nie ustawiona, moduł będzie wyświetlany wszędzie.",
        on_delete=models.PROTECT,
    )
    overrides = models.ForeignKey(
        "self",
        blank=True,
        null=True,
        limit_choices_to={"overrides": None},
        verbose_name="nadpisuje",
        help_text="Nadpisywany moduł (jeżeli moduł nadpisujący przynależy do grupy treści). Z nadpisywanego modułu będą pobierane wartości dla języka i dla pustych pól.",
        on_delete=models.PROTECT,
    )
    ordering = models.IntegerField(
        blank=True, null=True, help_text=attribute_inheritance_help_text
    )
    enabled = models.NullBooleanField(help_text=attribute_inheritance_help_text)
    pages = models.IntegerField(
        choices=((1, "all"), (2, "main"), (3, "not main")),
        default=1,
        help_text="Czy ta opcja jest jeszcze używana, można ją wywalić?",
    )
    position = models.IntegerField(
        choices=((1, "left"), (2, "right")),
        default=1,
        help_text="Czy ta opcja jest jeszcze używana, można ją wywalić?",
    )
    content = models.TextField(blank=True, help_text=attribute_inheritance_help_text)

    # Daty
    created_at = models.DateTimeField(
        "utworzono", auto_now_add=True, null=True, db_index=True
    )
    updated_at = models.DateTimeField(
        "aktualizowano", auto_now=True, null=True, db_index=True
    )

    class Meta:
        ordering = ["ordering"]

    def __str__(self):
        return self.title_synced

    def clean(self):
        synced_attributes = ("title", "ordering", "enabled", "content")
        for synced_attribute in synced_attributes:
            if getattr(self, synced_attribute) in ["", None] and not self.overrides:
                raise ValidationError(
                    self.attribute_inheritance_error_text_template.format(
                        field_name=self._meta.get_field_by_name(synced_attribute)[
                            0
                        ].verbose_name
                    )
                )
        if self.overrides:
            self.language = self.overrides.language

    def __getattr__(self, attname):
        if attname.endswith("_synced"):
            real_attname = re.sub(r"_synced$", "", attname)
            # Jeśli atrybut u nas jest pusty, pobierzmy go z nadpisywanego obiektu, jeśli ten istnieje.
            local_value = getattr(self, real_attname)
            if local_value in [None, ""] and self.overrides:
                return getattr(self.overrides, real_attname)
            else:
                return local_value
        return super().__getattr__(attname)


mark_safe_lazy = lazy(mark_safe, str)


ZAPLACE_CHOICES = (
    # Opcje dla kursów.
    (1, _("od razu pełną kwotę")),
    (
        2,
        _(
            "wpłacając teraz zaliczkę 500 PLN i pozostałą kwotę do siedmiu dni przed rozpoczęciem kursu"
        ),
    ),
    (
        3,
        mark_safe_lazy(
            _(
                "(tylko klient indywidualny) wpłacając teraz zaliczkę 500 PLN i pozostałą kwotę w dwóch równych ratach, zgodnie z <a href='/pl/raty/'>zasadami płatności ratalnej</a>"
            )
        ),
    ),
    # Uwaga: Płatność w pięciu ratach ma być tylko w formularzu polskim.
    (
        10,
        mark_safe_lazy(
            _(
                "(tylko klient indywidualny) wpłacając kwotę w pięciu ratach, zgodnie z <a href='/pl/raty/'>zasadami płatności ratalnej</a>"
            )
        ),
    ),
    (
        4,
        _(
            "inny tryb płatności lub umowa, ustalone indywidualnie (proszę wpisać dokonane ustalenia w polu uwagi dodatkowe)"
        ),
    ),
    # Opcje dla szkoleń.
    (
        5,
        _(
            "standard - płatność w oparciu o fakturę pro forma na 7 dni przed rozpoczęciem szkolenia"
        ),
    ),
    (6, _("płatność po szkoleniu, 14 dni po zakończeniu, dopłata +5%")),
    (
        7,
        _(
            "inny tryb płatności lub umowa, ustalone indywidualnie (proszę wpisać dokonane ustalenia w polu uwagi dodatkowe)"
        ),
    ),
)


class UczestnikZgloszenie(models.Model):

    """Funkcjonalności wspólne dla uczestnika i zgłoszenia."""

    # (Póki co nie wszystkie).

    waluta = models.ForeignKey(Waluta, on_delete=models.PROTECT)
    imie_nazwisko = models.TextField(
        _("imię i nazwisko"),
        help_text=_("lub lista osób, jeżeli zgłoszenie wieloosobowe"),
    )
    osoba_do_kontaktu = models.CharField(
        _("osoba do kontaktu i rozliczeń"),
        help_text=_(
            "Jeśli inna niż uczestnik, np. pracownik organizujący szkolenie "
            "lub rodzic w przypadku niepełnoletnich uczestników."
        ),
        max_length=100,
        blank=True,
    )
    prywatny = models.BooleanField(_("jestem osobą prywatną"), default=False)
    chce_fakture = models.BooleanField(
        _("proszę o wystawienie papierowej faktury"),
        default=False,
    )

    faktura_firma = models.CharField(_("firma"), max_length=100, blank=True)
    faktura_adres = models.CharField(_("adres"), max_length=100, blank=True)
    faktura_miejscowosc_kod = models.CharField(
        _("miejscowość, kod pocztowy"), max_length=100, blank=True
    )
    faktura_kraj = models.CharField(
        _("kraj"), max_length=2, choices=COUNTRIES_CHOICES, default="PL"
    )
    faktura_nip = models.CharField(_("NIP"), max_length=40, blank=True)
    faktura_vat_id = models.CharField(
        "VAT ID", max_length=40, blank=True, help_text=_("lub ID firmy")
    )

    chce_egzamin = models.BooleanField(
        _("zamawiam egzamin"),
        default=False,
    )
    chce_autoryzacje = models.BooleanField(
        _("zamawiam autoryzację"),
        default=False,
    )

    ile_obiadow_wegetarianskich = models.IntegerField(
        _("dla ilu osób obiady wegetariańskie"), blank=True, null=True
    )

    za_kurs_zaplace = models.IntegerField(choices=ZAPLACE_CHOICES, default=1)

    bylem_wczesniej = models.BooleanField(
        _(
            "byłem wcześniej u was na szkoleniu (dotyczy także innych pracowników firmy)"
        ),
        default=False,
    )
    bylem_na = models.TextField(
        _(
            "Jeśli tak, proszę podać nazwę szkolenia i orientacyjną datę jego rozpoczęcia"
        ),
        blank=True,
    )

    cena_obiadow = models.DecimalField(
        max_digits=30, decimal_places=2, blank=True, null=True
    )

    stawka_vat = models.DecimalField(
        max_digits=30, decimal_places=4, choices=STAWKI_VAT, default=DEFAULT_VAT
    )

    # Informacje do rat
    raty_nazwa_dokumentu = models.CharField(
        "nazwa dokumentu", max_length=200, blank=True
    )
    raty_numer_dokumentu = models.CharField(
        "numer dokumentu", max_length=200, blank=True
    )
    raty_pesel = models.CharField(
        "PESEL",
        max_length=11,
        blank=True,
        help_text="Dotyczy tylko narodowości polskiej.",
    )
    raty_panstwo = models.CharField(
        "kraj",
        max_length=200,
        blank=True,
        choices=(
            ("polska", _("Polska")),
            ("inny", _("inny")),
        ),
    )
    drukowany_certyfikat = models.BooleanField(
        "czy drukować papierowy certyfikat", default=False
    )

    # Dodatkowe pytania
    odpowiedz_na_dodatkowe_pytanie = models.TextField(
        _("Odpowiedź na dodatkowe pytanie"), blank=True
    )
    wiek_uczestnika = models.PositiveSmallIntegerField(
        _("wiek uczestnika"),
        blank=True,
        null=True,
        default=None,
        validators=[MaxValueValidator(99), MinValueValidator(13)],
    )

    discount_code = models.ForeignKey(
        "www.DiscountCode",
        verbose_name="kod rabatowy",
        blank=True,
        null=True,
        default=None,
        on_delete=models.PROTECT,
    )

    class Meta:
        abstract = True

    def is_raty(self):
        return self.za_kurs_zaplace in [10, 3]

    def umowa_ratalna(self):
        """
        Informacja, czy danemu uczestnikowi możemy wygenerować umowę ratalną.
        Umowa jest tylko dla uczestników prywatnych, z polskim obywatelstwem.
        """

        return self.is_raty() and self.prywatny and self.raty_panstwo == "polska"

    def cena_autoryzacji(self):
        """
        Zwraca łączną cenę netto wszystkich autoryzacyj.
        """
        if self.chce_autoryzacje and self.termin.szkolenie.cena_autoryzacji:
            return self.liczba_osob() * self.termin.szkolenie.cena_autoryzacji
        else:
            return 0

    def liczba_osob(self):
        return self.uczestnik_wieloosobowy_ilosc_osob or 1

    liczba_osob.short_description = "liczba osób"

    def przypuszczalna_stawka_vat(self):
        """
        Zgaduje stawkę VAT na podstawie pozostałych danych.
        Stawka jest zwracana, ale nie ustawiana.
        """
        wersja_jezykowa = WersjaJezykowa.biezaca()
        stawka_vat = wersja_jezykowa.stawka_vat

        if self.podmiot_publiczny:
            stawka_vat = 0

        if self.termin.is_posiada_akredytacje():
            stawka_vat = 0

        if stawka_vat is None:
            return 0
        else:
            return stawka_vat


class Zgloszenie(UczestnikZgloszenie):
    time = models.DateTimeField(auto_now_add=True)
    ip = models.GenericIPAddressField(editable=False, blank=True, null=True)
    token = models.CharField(max_length=16, editable=False, unique=True)

    termin = models.ForeignKey(
        TerminSzkolenia,
        help_text=_("oraz miasto i tryb zajęć"),
        verbose_name=_("termin"),
        on_delete=models.PROTECT,
    )
    firma = models.ForeignKey(Firma, blank=True, null=True, on_delete=models.PROTECT)
    email = models.EmailField(
        _("email uczestnika"), help_text=_("W przypadku grup adres osoby kontaktowej.")
    )
    email_ksiegowosc = models.EmailField(
        _("email osoby do rozliczeń"),
        help_text=_("Jeśli inny niż powyższy"),
        blank=True,
    )
    telefon = models.CharField(_("telefon do kontaktu"), max_length=50)
    adres = models.CharField(_("adres korespondencyjny"), max_length=100)
    miejscowosc_kod = models.CharField(_("miejscowość, kod pocztowy"), max_length=100)

    podmiot_publiczny = models.BooleanField(
        _("podmiot publiczny lub finansowanie ze środków publicznych"),
        default=False,
        help_text=_(
            "Zaznaczenie opcji upoważnia do zwolnienia z VAT. W razie pytań lub wątpliwości prosimy o kontakt."
        ),
    )
    uczestnik_wieloosobowy_ilosc_osob = models.IntegerField(
        _("liczność grupy"),
        blank=True,
        null=True,
        help_text=_("w przypadku zgłaszania grupy proszę wpisać liczbę osób"),
    )
    chce_obiady = models.BooleanField(
        _("zamawiam obiady"),
        default=False,
    )

    cena = models.DecimalField(max_digits=30, decimal_places=2)

    uwagi_klienta = models.TextField(_("Uwagi dodatkowe"), blank=True)

    viewed = models.BooleanField(default=False)

    def set_uczestnik_wieloosobowy_ilosc_osob(self):
        if self.prywatny:
            self.uczestnik_wieloosobowy_ilosc_osob = None

    def set_dane_obiadow(self, obiad_wegetarianski=False):
        if self.termin.obiady == "nie-ma-obiadow":
            self.chce_obiady = False
            self.ile_obiadow_wegetarianskich = None
        if (self.chce_obiady or self.termin.obiady == "obiady-wliczone") and (
            self.prywatny or self.ilosc_osob() == 1
        ):
            if obiad_wegetarianski:
                self.ile_obiadow_wegetarianskich = 1
            else:
                self.ile_obiadow_wegetarianskich = None
        if self.termin.obiady == "obiady-opcjonalne" and self.chce_obiady:
            if self.termin.ile_dni:
                dni = self.termin.ile_dni
            elif self.termin.termin_zakonczenia:
                dni = (self.termin.termin_zakonczenia - self.termin.termin).days + 1
            elif self.termin.szkolenie.czas_dni:
                dni = self.termin.szkolenie.czas_dni
            else:
                # FIXME: krzycz
                dni = 0
            self.cena_obiadow = self.ilosc_osob() * self.termin.cena_obiadu * dni
        else:
            self.cena_obiadow = Decimal("0.00")

    def set_cena(self, szkolenie_zamkniete=False):
        if not szkolenie_zamkniete:
            self.cena = self.wylicz_cene()
        else:
            self.cena = self.wylicz_cene_dla_zamknietego()

    def is_doplata_do_faktury(self):
        language = self.termin.szkolenie.language

        if (
            self.chce_fakture
            and self.cena_do_wyliczenia_doplaty()
            < settings.MINIMALNA_CENA_NETTO_SZKOLENIA_DLA_DARMOWEJ_FAKTURY[language]
            + settings.CENA_NETTO_PAPIEROWEJ_FAKTURY[language]
        ):
            return True
        else:
            return False

    def wylicz_cene(self):
        wyliczona_cena = 0

        language = self.termin.szkolenie.language

        discount_code = self.discount_code

        if discount_code:
            cena_bazowa = (
                self.termin.szkolenie.cena * discount_code.to_decimal()
            ).quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)
        else:
            cena_bazowa = self.termin.szkolenie.cena

        if self.termin.is_posiada_akredytacje():
            """
            Jeżeli kurs posiada akredytację w lokalizacji, wówczas obowiązuje
            stawka VAT 0%, a cena nett pozostaje równa cenie bazowej.
            """
            wyliczona_cena = cena_bazowa
        else:
            if (
                self.termin.szkolenie.tag_dlugosc.slug == "kurs-zawodowy"
                and self.prywatny
            ):
                """
                Osoby prywatne mają pokrywany VAT dla kursów zawodowych, więc
                cena wyliczona jest przeliczana tak, aby powiększona o VAT była
                równa pierwotnej cenie bazowej netto.
                """
                wyliczona_cena = cena_bazowa / (1 + self.stawka_vat)
                wyliczona_cena = wyliczona_cena.quantize(
                    Decimal("0.01"), rounding=ROUND_HALF_UP
                )
            else:
                wyliczona_cena = cena_bazowa

        wyliczona_cena *= self.ilosc_osob()

        if (
            self.chce_fakture
            and wyliczona_cena
            < settings.MINIMALNA_CENA_NETTO_SZKOLENIA_DLA_DARMOWEJ_FAKTURY[language]
        ):
            wyliczona_cena += settings.CENA_NETTO_PAPIEROWEJ_FAKTURY[language]

        if self.drukowany_certyfikat:
            wyliczona_cena += self.cena_drukowanego_certyfikatu()

        if self.cena_obiadow:
            wyliczona_cena += self.cena_obiadow

        wyliczona_cena += self.cena_autoryzacji()

        return wyliczona_cena

    def wylicz_cene_dla_zamknietego(self):
        cena = self.termin.cena_bazowa_do_n_osob

        powyzej_n_osob = self.ilosc_osob() - self.termin.liczba_osob_w_grupie

        if powyzej_n_osob > 0:
            cena += self.termin.cena_za_osobe_powyzej_n_osob * powyzej_n_osob
        return cena

    def cena_drukowanego_certyfikatu(self):
        return (
            settings.CENA_NETTO_DRUKOWANEGO_CERTYFIKATU[self.termin.szkolenie.language]
            * self.ilosc_osob()
        )

    def cena_do_wyliczenia_doplaty(self):
        wyliczona_cena = self.cena
        wyliczona_cena -= self.cena_autoryzacji()
        if self.cena_obiadow:
            wyliczona_cena -= self.cena_obiadow
        return wyliczona_cena

    def ilosc_osob(self):
        """
        FIXME: To będzie do poprawy w frontendzie - użytkownik
        nie może zgłosić grupy jeżeli jest prywatny
        """
        if self.prywatny or not self.uczestnik_wieloosobowy_ilosc_osob:
            return 1
        else:
            return self.uczestnik_wieloosobowy_ilosc_osob

    def cena_brutto(self):
        return cena_brutto(self)

    def __str__(self):
        return "%s, %s" % (self.termin.szkolenie, self.termin.termin)

    class Meta:
        verbose_name_plural = "zgłoszenia"
        verbose_name = "zgłoszenie"
        ordering = ["-time"]


STATUSY_UCZESTNIKA = (
    (-3, "zgłoszenie z internetu, trzeba potwierdzić"),
    (-2, "zgłoszenie tel/mail, trzeba potwierdzić"),
    (-1, "powiedzieliśmy że niepewne, skontaktować się znów przed szkoleniem"),
    (0, "trzeba przemówić"),
    (1, "potwierdzone, odbywa się, uczestnik wszystko wie"),
    (3, "przeszkolony"),
    (5, "nieprzeszkolony (nie odbyło się), ale nadal zainteresowany"),
    (4, "zrezygnował"),
)

PAPIERY = (
    (1, "brak papieru, zgłoszenie internetowe"),
    (2, "brak papieru, tylko luźne telefony/maile"),
    (3, "oficjalne zgłoszenie mailowe"),
    (4, "podpisany formularz zgłoszeniowy"),
    (5, "podpisane zamówienie pisemne lub faksowe"),
    (6, "podpisana umowa szkoleniowa"),
    (7, "brak papieru, ale szkolenie już zapłacone"),
)


class Uczestnik(UczestnikZgloszenie):
    zgloszenie_form = models.ForeignKey(
        Zgloszenie,
        verbose_name="zgłoszenie",
        blank=True,
        null=True,
        editable=False,
        default=None,
        on_delete=models.PROTECT,
    )
    termin = models.ForeignKey(TerminSzkolenia, on_delete=models.PROTECT)
    status = models.IntegerField(choices=STATUSY_UCZESTNIKA, default=1)

    imie_nazwisko_zostalo_sprawdzone = models.BooleanField(
        _("Potwierdzam, że dane są poprawne i nadają się umieszczenia na " "fakturze"),
        default=False,
        help_text=_(
            "W jednej linii powinno być tylko jedno imię i nazwisko, " "nic poza tym."
        ),
    )
    nierentowny = models.BooleanField("Nie wliczać do rentowności", default=False)
    uczestnik_wieloosobowy_ilosc_osob = models.IntegerField(
        blank=True,
        null=True,
        help_text="Dotyczy tylko zgłoszeń kilkuosobowych - zostaw "
        + "puste, jeżeli rejestrujesz pojedynczego uczestnika.",
    )

    def uczestnik_wieloosobowy_ilosc_osob2(self):
        if self.uczestnik_wieloosobowy_ilosc_osob is None:
            return ""
        else:
            return self.uczestnik_wieloosobowy_ilosc_osob

    uczestnik_wieloosobowy_ilosc_osob2.short_description = "#osób"
    chce_obiady = models.BooleanField(
        "zamawiam obiady",
        default=False,
        help_text="Pole zaznaczane automatycznie przy tworzeniu uczestnika "
        "w przypadku, gdy obiady są wliczone w cenę terminu szkolenia.",
    )
    firma = models.ForeignKey(Firma, blank=True, null=True, on_delete=models.PROTECT)
    email = models.EmailField(
        "email kursanta",
        blank=True,
        help_text="W przypadku grup adres osoby kontaktowej.",
    )
    email_ksiegowosc = models.EmailField("email do spraw księgowych", blank=True)
    telefon = models.CharField("telefon", max_length=50, blank=True)
    adres = models.CharField(
        "adres",
        max_length=100,
        blank=True,
        help_text="korespondencyjny (jeśli wpisano adres do faktury, i do "
        + "korespondencji jest taki sam, to można nie wpisywać)",
    )
    miejscowosc_kod = models.CharField(
        "miejscowość, kod pocztowy", max_length=100, blank=True
    )

    papier = models.IntegerField(choices=PAPIERY, default=1)

    kwota_do_zaplaty = models.DecimalField(
        max_digits=30,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="zostaw pustą, żeby wziąć domyślną cenę ze szkolenia",
    )
    termin_zaplaty = models.DateField(
        blank=True,
        null=True,
        help_text="zostaw pusty, żeby ustawić na 7 dni przed terminem szkolenia; "
        + "jeśli zostanie wybrana opcja płatności z zaliczką i resztą, "
        + "lub z zaliczką i ratami, to termin zaliczki ustawi się na już oraz: "
        + "reszty na 7 dni przed szkoleniem, lub rat zgodnie z naszą procedurą",
    )
    nr_proformy = models.CharField(
        "nr proformy", max_length=40, blank=True, help_text="jeżeli wystawiono"
    )
    nr_faktury = models.CharField("nr faktury", max_length=80, blank=True)
    nr_faktury_raty = models.CharField("nr faktury z ratami", max_length=80, blank=True)
    zaplacone = models.DateField(blank=True, null=True)

    zaliczka_kwota = models.DecimalField(
        max_digits=30, decimal_places=2, blank=True, null=True
    )
    zaliczka_termin = models.DateField(blank=True, null=True)
    zaliczka_zaplacone = models.DateField(blank=True, null=True)
    rata1_kwota = models.DecimalField(
        max_digits=30, decimal_places=2, blank=True, null=True
    )
    rata1_termin = models.DateField(blank=True, null=True)
    rata1_zaplacone = models.DateField(blank=True, null=True)
    rata2_kwota = models.DecimalField(
        max_digits=30, decimal_places=2, blank=True, null=True
    )
    rata2_termin = models.DateField(blank=True, null=True)
    rata2_zaplacone = models.DateField(blank=True, null=True)
    rata3_kwota = models.DecimalField(
        max_digits=30, decimal_places=2, blank=True, null=True
    )
    rata3_termin = models.DateField(blank=True, null=True)
    rata3_zaplacone = models.DateField(blank=True, null=True)
    rata4_kwota = models.DecimalField(
        max_digits=30, decimal_places=2, blank=True, null=True
    )
    rata4_termin = models.DateField(blank=True, null=True)
    rata4_zaplacone = models.DateField(blank=True, null=True)

    uwagi = models.TextField(blank=True)
    uwagi_klienta = models.TextField(blank=True)
    czas_dodania = models.DateTimeField(
        blank=True,
        help_text="tj. czas wpisania niniejszego rekordu; zostaw puste, aby "
        "ustawić chwilę obecną",
    )
    podmiot_publiczny = models.BooleanField(
        "podmiot publiczny lub finansowanie ze środków publicznych",
        default=False,
        help_text="dla zwolnień z VAT",
    )
    faktura_po_szkoleniu = models.BooleanField(
        "Wystawić fakturę VAT do zapłaty", default=False
    )
    faktura_po_szkoleniu_data = models.DateField(
        "data wystawienia fv",
        blank=True,
        null=True,
        help_text="zostawą pustą, wówczas ustawiony zostanie ostatni dzień "
        "szkolenia",
    )
    wystaw_proforme_automatycznie = models.BooleanField(
        "wystaw i wyślij fakturę pro-forma automatycznie w momencie "
        "potwierdzenia grupy",
        default=True,
        help_text="Automatyczna pro-forma dostępna tylko dla uczestników "
        "szkoleń otwartych, którzy płacą standardowo 7 dni przed "
        "startem. Opcja ta wymaga podania danych teleadresowych "
        "osoby prywatnej (Imię i nazwisko, email, adres, "
        "kod pocztowy) lub firmy (Nazwa firmy, adres firmy, kraj, "
        "kod pocztowy firmy, NIP).",
    )
    odeslal_umowe = models.BooleanField("odesłał umowę", default=False)
    odeslal_podpisany_formularz = models.BooleanField(
        "odesłał podpisany formularz", default=False
    )

    zliczacz_proforma_no = models.CharField(
        "ID faktury proforma w zliczacz",
        blank=True,
        editable=True,
        max_length=10,
        null=True,
        help_text="Jeśli chcesz usunąć omyłkowo wystawioną fakturę, to należy "
        "usunąć niniejsze pole (id faktury w zliczacz) ORAZ wejść "
        "do Zliczacza i ręcznie skasować tę fakturę.",
    )
    zliczacz_faktura_no = models.CharField(
        "ID faktury 'zwykłej' w zliczacz",
        blank=True,
        editable=True,
        max_length=10,
        null=True,
        help_text="Jeśli chcesz usunąć omyłkowo wystawioną fakturę, to "
        "należy usunąć niniejsze pole (id faktury w zliczacz) ORAZ "
        "wejść do Zliczacza i ręcznie skasować tę fakturę.",
    )
    zliczacz_faktura_raty_no = models.CharField(
        "ID faktury z ratami w zliczacz",
        blank=True,
        editable=True,
        max_length=10,
        null=True,
        help_text="Jeśli chcesz usunąć omyłkowo wystawioną fakturę, to "
        "należy usunąć niniejsze pole (id faktury z ratami w "
        "zliczacz) ORAZ wejść do Zliczacza i ręcznie skasować tę "
        "fakturę.",
    )
    proforma_wyslana = models.DateTimeField(blank=True, editable=False, null=True)
    faktura_wyslana = models.DateTimeField(blank=True, editable=False, null=True)
    faktura_raty_wyslana = models.DateTimeField(blank=True, editable=False, null=True)
    korekta_wyslana = models.DateTimeField(blank=True, editable=False, null=True)
    umowa_wyslana = models.DateTimeField(blank=True, editable=False, null=True)
    faktura_status = models.CharField(
        "Status faktury VAT w Zliczacz",
        blank=True,
        max_length=30,
        choices=(
            ("ready", "gotowa"),
            ("sent", "wysłana"),
            ("paid", 'zapłacona (używając daty z pola "Zapłacone")'),
            ("zaliczka_paid", 'zapłacona (używając daty z pola "Zaliczka zapłacone")'),
        ),
    )
    faktura_data_wykonania_uslugi = models.DateField(
        "data wykonania usługi faktury",
        blank=True,
        null=True,
        help_text="Określa datę wykonania usługi na fakturze. "
        "Jeśli nie podany zostanie automatycznie obliczony.",
    )
    faktura_data_wystawienia = models.DateField(
        "data wystawienia faktury",
        blank=True,
        null=True,
        help_text="Jeśli nie podana zostanie ustawiony dzień w chwili "
        "generowania faktury.",
    )

    faktura_raty_data_wykonania_uslugi = models.DateField(
        "data wykonania usługi faktury (ratalnej)",
        blank=True,
        null=True,
        help_text="Określa datę wykonania usługi na fakturze. "
        "Jeśli nie podany zostanie automatycznie obliczony.",
    )
    faktura_raty_data_wystawienia = models.DateField(
        "data wystawienia faktury (ratalnej)",
        blank=True,
        null=True,
        help_text="Jeśli nie podana zostanie ustawiony dzień w chwili "
        "generowania faktury.",
    )

    mail_o_potwierdzeniu_terminu = models.DateTimeField(
        "mail o potwierdzeniu terminu", blank=True, null=True
    )

    class Meta:
        verbose_name_plural = "uczestnicy"
        ordering = ["-czas_dodania"]

    pola_z_ratami = [
        "zaliczka",
        "rata1",
        "rata2",
        "rata3",
        "rata4",
    ]

    def wyslac_mail_o_potwierdzeniu_terminu(self):
        """
        Zwraca informację, czy dla danego uczestnika można wysłać maila o potwierdzeniu
        terminu.
        """

        return bool(
            not self.mail_o_potwierdzeniu_terminu
            and self.termin.termin > datetime.datetime.now().date()
            and self.status == 1
        )

    def get_nazwa(self):
        # - jeśli grupa - zamiast imienia i nazwiska dajmy nazwę firmy
        # - jeśli firma, ale jeden uczestnik - tak jak wcześniej, imię i nazwisko
        #   uczestnika
        if self.ilosc_osob() > 1:
            return self.faktura_firma
        return self.imie_nazwisko

    def get_nazwa_oneline(self):
        if self.ilosc_osob() > 1:
            return self.faktura_firma
        return self.imie_nazwisko_oneline()

    @property
    def faktura_zaliczkowa(self):
        if self.zaliczka_zaplacone and not (
            self.rata1_zaplacone
            or self.rata2_zaplacone
            or self.rata3_zaplacone
            or self.rata4_zaplacone
        ):
            return True

        if self.termin.odbylo_sie and self.status in [1, 3]:
            return False
        # Każdy inny przypadek traktujemy jak fakturę zaliczkową.
        return True

    @property
    def aktywny_newsletter(self):
        """
        Zwraca informację, czy uczestnik zapisał się do newslettera i jest nadal
        aktywny.
        """

        return bool(
            self.email
            and Odbiorca.objects.filter(
                tos_agreement__isnull=False,
                email__iexact=self.email,
                status="potwierdzony",
            ).exists()
        )

    def can_send_first_continuation_email(self):
        """
        Zwraca informację, czy do uczestnika można wysłać pierwszy mail o kontynuacji.
        """

        kontynuacja = self.termin.szkolenie.kontynuacja

        # RODO wyłączone od 18.09.2019 (tos_agreement__isnull=False)
        # https://app.asana.com/0/4723313594922/1126408594163859

        return bool(
            self.email
            and not email_optout(self.email)
            and not ContinuationUnsubscribed.objects.filter(
                email__iexact=self.email
            ).exists()
            and not ContinuationLog.objects.any_continuation_exists(
                self.email, kontynuacja
            )
            and not Uczestnik.objects.filter(
                email__iexact=self.email, termin__szkolenie=kontynuacja
            ).exists()
        )

    def can_send_second_continuation_email(self):
        """
        Zwraca informację, czy do uczestnika można wysłać drugi mail o kontynuacji.
        """

        kontynuacja = self.termin.szkolenie.kontynuacja

        # RODO wyłączone od 18.09.2019 (tos_agreement__isnull=False)
        # https://app.asana.com/0/4723313594922/1126408594163859

        return bool(
            self.email
            and not email_optout(self.email)
            and not ContinuationUnsubscribed.objects.filter(
                email__iexact=self.email
            ).exists()
            and ContinuationLog.objects.allow_second_notification(
                self.email, kontynuacja
            )
            and not Uczestnik.objects.filter(
                email__iexact=self.email, termin__szkolenie=kontynuacja
            ).exists()
        )

    def can_send_proforma_email(self):
        return (
            self.zliczacz_proforma_no
            and not self.zliczacz_faktura_no
            and not self.nr_faktury
            and not self.proforma_wyslana
            and self.email
        )

    def can_send_fvat_email(self):
        return (
            self.zliczacz_faktura_no
            and not self.faktura_wyslana
            and not self.korekta_wyslana
            and self.email
        )

    def can_send_invoice_note_email(self):
        return self.get_invoice_note() and not self.korekta_wyslana and self.email

    def can_send_invoice_installments_email(self):
        return (
            self.zliczacz_faktura_raty_no
            and not self.faktura_raty_wyslana
            and self.email
        )

    def can_send_agreement_email(self):
        return self.umowa_ratalna() and not self.umowa_wyslana and self.email

    def imie_nazwisko_oneline(self):
        imone = [u.strip() for u in self.imie_nazwisko.splitlines()]
        return ", ".join(imone)

    def slug(self):
        return my_slugify.slugify(self.nazwa())

    def nazwa_szkolenia_do_faktury(self):
        # "Darek, dzłbyś radę zrobić podmianę w przypadku tego jednego kursu?
        # (tak wiem że byś mógł, czy to mało roboty, pewnie nie za dużo).
        # Przy generowaniu faktur po prostu dla tego kursu zmiana nazwy z
        # nazwy kursu na "Nauka programowania i Język Java (dla młodzieży)".
        # Dzięki!
        # M."
        if self.termin.szkolenie.kod == "K-PROG-INTRO-M":
            return "Nauka programowania i Język Java (dla młodzieży)"
        return self.termin.szkolenie.nazwa

    def tytul_do_faktury(self):
        daty = list(self.termin.daty())

        tytul = "{}: {} w terminie od {}.".format(
            "Kurs" if self.termin.is_kurs() else "Szkolenie",
            self.nazwa_szkolenia_do_faktury(),
            daty[0].strftime("%d.%m.%Y"),
        )
        if self.imie_nazwisko:
            if not self.uczestnik_wieloosobowy_ilosc_osob:
                tytul += " Uczestnik: {}.".format(self.imie_nazwisko)
            else:
                tytul += " Uczestnicy: {}.".format(self.imie_nazwisko_oneline())
        return tytul

    def tytul_do_faktury_raty(self):
        daty = list(self.termin.daty())

        tytul = "{}: {} w terminie od {}.".format(
            "Kurs" if self.termin.is_kurs() else "Szkolenie",
            self.nazwa_szkolenia_do_faktury(),
            daty[0].strftime("%d.%m.%Y"),
        )
        if self.imie_nazwisko:
            if not self.uczestnik_wieloosobowy_ilosc_osob:
                tytul += " Uczestnik: {}.".format(self.imie_nazwisko)
            else:
                tytul += " Uczestnicy: {}.".format(self.imie_nazwisko_oneline())
        return tytul

    def get_email_ksiegowosc(self):
        return self.email_ksiegowosc or self.email

    # def zaplacono_pierwsza_rate(self):
    #     """
    #     Uwaga: zaliczka traktowana jest jako I rata.
    #     """
    #
    #     # return self.is_raty() and self.zaliczka_zaplacone and \
    #     #     self.termin.odbylo_sie
    #     return self.is_raty()

    def tytul_do_faktury_zaliczkowej(self):
        tytul = self.tytul_do_faktury()

        # if self.za_kurs_zaplace == 10:
        #     tytul += " - I rata - ZALICZKA"
        # else:
        #     tytul += " - ZALICZKA"
        if self.za_kurs_zaplace == 10:
            tytul += " - I rata"
        elif self.za_kurs_zaplace in (2, 3):
            tytul += " - Opłata wstępna"
        return tytul

    def termin_platnosci_do_faktury(self, termin_od):
        """
        Termin płatności powinien być conajmniej 3 dni przed
        szkoleniem, chyba że faktura jest wystawiana później,
        wtedy faktura ma termin płatności w dniu wystawienia.
        Maksymalny termin to 7 dni (w przypadku faktur wystawianych
        10 lub więcej dni przed szkoleniem).
        """
        if termin_od + datetime.timedelta(
            days=7
        ) <= self.termin.termin - datetime.timedelta(days=3):
            return termin_od + datetime.timedelta(days=7)
        if termin_od >= self.termin.termin - datetime.timedelta(days=3):
            return termin_od
        return self.termin.termin - datetime.timedelta(days=3)

    def termin_platnosci_do_faktury_zaliczkowej(self):
        return self.termin.termin + datetime.timedelta(days=1)

    def nazwy_faktur(self):
        if self.faktura_zaliczkowa:
            return {
                "proforma": "Faktura Pro-Forma (zaliczkowa - nowa wersja)",
                "vat": "Faktura VAT (zaliczkowa - nowa wersja)",
                "vat_raty": "Faktura VAT (raty)",
                "korekta": "Faktura korygująca",
            }
        return {
            "proforma": "Faktura Pro-Forma",
            "vat": "Faktura VAT",
            "vat_raty": "Faktura VAT (raty)",
            "korekta": "Faktura korygująca",
        }

    def cena(self):
        return self.kwota_do_zaplaty.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)

    def cena_brutto(self):
        return kwota_brutto(self.cena(), self.stawka_vat)

    def kwota_vat(self):
        return self.cena_brutto() - self.cena()

    def cena_zaliczka(self):
        return self.zaliczka_kwota.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)

    def cena_brutto_zaliczka(self):
        return kwota_brutto(self.cena_zaliczka(), self.stawka_vat)

    def kwota_vat_zaliczka(self):
        return self.cena_brutto_zaliczka() - self.cena_zaliczka()

    def cena_rata(self, kwota):
        return kwota.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)

    def cena_brutto_rata(self, kwota):
        return kwota_brutto(self.cena_rata(kwota), self.stawka_vat)

    def kwota_vat_rata(self, kwota):
        return self.cena_brutto_rata(kwota) - self.cena_rata(kwota)

    def imie_nazwisko_uwagi(self):
        if self.uwagi:
            return "%s || %s" % (self.imie_nazwisko, self.uwagi)
        else:
            return self.imie_nazwisko

    imie_nazwisko_uwagi.short_description = "Imię i nazwisko, uwagi"

    def szkolenie(self):
        return self.termin.szkolenie.kod

    def miejsce(self):
        return self.termin.lokalizacja.shortname

    def zaplacono_dotychczas(self):
        if self.zaplacone is not None:
            return self.kwota_do_zaplaty
        x = 0
        for r in ["rata4", "rata3", "rata2", "rata1", "zaliczka"]:
            if getattr(self, r + "_zaplacone"):
                x += getattr(self, r + "_kwota") or 0
        return x

    def zaplacono_dotychczas_brutto(self):
        return kwota_brutto(self.zaplacono_dotychczas(), self.stawka_vat)

    def kwota_do_zaplaty_brutto(self):
        return kwota_brutto(self.kwota_do_zaplaty, self.stawka_vat)

    def zaplacone3(self):
        return self.verbose_zaplacone3()[0]

    zaplacone3.boolean = True
    zaplacone3.short_description = "OK"

    def verbose_zaplacone3(self):
        # TODO: Ta metoda jest zbyt złożona. Zrobić coś z tym.
        today = datetime.date.today()
        if self.status in [4, 5]:
            return (None, self.get_status_display())
        if self.zaplacone is not None:
            return (True, "zapłacił wszystko")
        if self.termin_zaplaty < today:
            if (
                self.rata2_kwota is not None
            ):  # jeśli jest tylko zaliczka i rata1, to nie nazywamy tego ratą
                return (False, "nie zapłacił ostatniej raty, termin minął")
            else:
                return (False, "nie zapłacił, termin minął")
        for r in ["rata4", "rata3", "rata2", "rata1"] + (
            ["zaliczka"] if self.status != -1 else []
        ):
            if getattr(self, r + "_termin") is not None:
                if getattr(self, r + "_zaplacone"):
                    return (None, "zapłacił %s" % r)
                if getattr(self, r + "_termin") < today:
                    return (
                        False,
                        ("nie zapłacił %s" % r)
                        + (
                            (
                                ", termin minął %d dni temu"
                                % (today - getattr(self, r + "_termin")).days
                            )
                            if r != "zaliczka"
                            else (
                                ", zgłoszenie sprzed %d dni"
                                % (today - self.czas_dodania.date()).days
                            )
                        ),
                    )
        if (
            self.status == -1
            and self.zaliczka_termin is not None
            and not self.zaliczka_zaplacone
            and self.zaliczka_termin < today
        ):
            return (False, "nie zapłacił zaliczki (ma status niepewne)")
        return (None, "jeszcze nic nie musiał płacić")

    @mark_safe
    def zaplacone3_html(self):
        z = self.verbose_zaplacone3()
        return mark_safe(
            re.sub(r"/>$", 'title="%s" />' % escape(z[1]), _boolean_icon(z[0]))
        )

    zaplacone3_html.short_description = "OK"

    def sprawdz_spojnosc_rat(self):
        """
        Rzuć ValidationError, jeśli raty są niespójne.
        """
        self._sprawdz_kompletnosc_danych_rat()
        self._sprawdz_sume_rat()
        self._sprawdz_ciaglosc_rat()

    def _sprawdz_kompletnosc_danych_rat(self):
        for pole in self.pola_z_ratami:
            jakies_pole_wypelnione = getattr(self, pole + "_termin") or getattr(
                self, pole + "_kwota"
            )
            oba_pola_wypelnione = getattr(self, pole + "_termin") and getattr(
                self, pole + "_kwota"
            )
            if jakies_pole_wypelnione and not oba_pola_wypelnione:
                raise ValidationError("Dane w harmonogramie rat nie są kompletne.")

    def _sprawdz_sume_rat(self):
        if any(
            not getattr(self, pole + "_kwota") is None for pole in self.pola_z_ratami
        ):
            suma_rat = sum(
                getattr(self, pole + "_kwota") or 0 for pole in self.pola_z_ratami
            )
            if not self.kwota_do_zaplaty == suma_rat:
                raise ValidationError("Suma rat nie jest równa kwocie do zapłaty.")

    def _sprawdz_ciaglosc_rat(self):
        nry_pol_z_wypelnionymi_ratami = list(
            filter(
                lambda i: getattr(self, self.pola_z_ratami[i] + "_termin"),
                list(range(len(self.pola_z_ratami))),
            )
        )
        if nry_pol_z_wypelnionymi_ratami:
            najdalszy_nr_pola = max(nry_pol_z_wypelnionymi_ratami)
            if any(
                getattr(self, self.pola_z_ratami[i] + "_termin") is None
                for i in range(najdalszy_nr_pola)
            ):
                raise ValidationError("Harmonogram rat jest nieciągły.")

    def dane_do_faktury(self):
        if self.prywatny:
            miasto_kod = parse_miejscowosc_kod(self.miejscowosc_kod)

            if self.osoba_do_kontaktu:
                klient = self.osoba_do_kontaktu
            else:
                klient = self.imie_nazwisko

            return {
                "klient": klient,
                "klient_adres": self.adres,
                "klient_adres_kopertowy": self.adres,
                "klient_aktywny": True,
                "klient_email": self.get_email_ksiegowosc(),
                "klient_faktura_co_miesiac": False,
                "klient_miasto": miasto_kod["miejscowosc"],
                "klient_kod_pocztowy": miasto_kod["kod"],
                "klient_konto": "",
                "klient_logo_plik_id": None,
                "klient_nazwa": klient,
                "klient_szkoleniowy": True,
                "klient_wystawca": False,
                "klient_wysylac_maile": False,
            }
        else:
            miasto_kod = parse_miejscowosc_kod(self.faktura_miejscowosc_kod)
            return {
                "klient": self.faktura_firma,
                "klient_adres": self.faktura_adres,
                "klient_adres_kopertowy": self.faktura_adres,
                "klient_nip": self.faktura_nip,
                "klient_aktywny": True,
                "klient_email": self.get_email_ksiegowosc(),
                "klient_faktura_co_miesiac": False,
                "klient_miasto": miasto_kod["miejscowosc"],
                "klient_kod_pocztowy": miasto_kod["kod"],
                "klient_konto": "",
                "klient_logo_plik_id": None,
                "klient_nazwa": self.faktura_firma,
                "klient_szkoleniowy": True,
                "klient_wystawca": False,
                "klient_wysylac_maile": False,
            }

    def allowed_to_get_invoice(self):
        """
        Metoda sprawdza, czy Uczestnikowi może zostać automatycznie
        wygenerowana faktura proforma via API_ZLICZACZ i wysłana mailem.

        To jest przypadek automatycznego generowania pro-forma podczas
        startu terminu - tylko proform!
        """

        return (
            self.wystaw_proforme_automatycznie
            and not self.termin.zamkniete
            and self.termin.is_szkolenie()
            and self.za_kurs_zaplace in [5]
        )

    def allowed_to_get_invoice_installments_by_admin(self):
        already_exist = bool(self.nr_faktury_raty)
        allowed_statuses = [1, 3]
        za_kurs_zaplace = self.za_kurs_zaplace in [2, 3, 10]
        additional_condition = True

        if self.za_kurs_zaplace == 2:
            for nr_raty in range(1, 2):
                if not getattr(
                    self, "rata{0}_kwota".format(nr_raty), None
                ) or not getattr(self, "rata{0}_termin".format(nr_raty), None):
                    additional_condition = False
                    break
        elif self.za_kurs_zaplace == 3:
            for nr_raty in range(1, 3):
                if not getattr(
                    self, "rata{0}_kwota".format(nr_raty), None
                ) or not getattr(self, "rata{0}_termin".format(nr_raty), None):
                    additional_condition = False
                    break
        elif self.za_kurs_zaplace == 10:
            for nr_raty in range(1, 5):
                if not getattr(
                    self, "rata{0}_kwota".format(nr_raty), None
                ) or not getattr(self, "rata{0}_termin".format(nr_raty), None):
                    additional_condition = False
                    break

        return (
            settings.GENERATE_INVOICE_THROUGH_API_ZLICZACZ
            and self.status in allowed_statuses
            and za_kurs_zaplace
            and self.wystaw_proforme_automatycznie
            and not already_exist
            and self.object_has_required_data_for_invoice()
            and additional_condition
        )

    def allowed_to_get_invoice_by_admin(self, proforma=True, fvat_raty=False):
        """
        Metoda sprawdza, czy Uczestnikowi może zostać automatycznie wygenerowana
        faktura via API_ZLICZACZ i wysłana mailem (ręcznie przez Panel Adm.)
        """

        if fvat_raty:
            return self.allowed_to_get_invoice_installments_by_admin()

        already_exist = (
            (self.nr_proformy or self.nr_faktury) if proforma else self.nr_faktury
        )

        allowed_statuses = (
            [-3, -2, -1, 0, 1, 3] if (proforma or self.faktura_zaliczkowa) else [1, 3]
        )

        if self.faktura_zaliczkowa and not all(
            [self.zaliczka_kwota, self.zaliczka_termin]
        ):
            additional_condition = False
        else:
            additional_condition = True

        return (
            settings.GENERATE_INVOICE_THROUGH_API_ZLICZACZ
            and self.status in allowed_statuses
            and self.wystaw_proforme_automatycznie
            and not already_exist
            and self.object_has_required_data_for_invoice()
            and additional_condition
        )

    def get_invoice_note(self):
        try:
            return self.fakturakorekta
        except FakturaKorekta.DoesNotExist:
            pass

    def allowed_to_get_invoice_note_by_admin(self):
        """
        Zwrot możemy zrobić, gdy szkolenie się nie odbywa, a faktura
        została wystawiona.
        """

        return (
            (self.nr_faktury or self.zliczacz_faktura_no)
            and self.termin.odbylo_sie is False
            and not self.get_invoice_note()
        )

    def allowed_to_get_invoice_by_admin_readable_conditions(self, proforma=True):
        already_exist = bool(
            (self.nr_proformy or self.nr_faktury) if proforma else self.nr_faktury
        )

        allowed_statuses = (
            [-3, -2, -1, 0, 1, 3] if (proforma or self.faktura_zaliczkowa) else [1, 3]
        )
        allowed_status = self.status in allowed_statuses

        c = []
        c.append(("Faktura jeszcze nie generowana", not already_exist))
        c.append(("Poprawny status uczestnika", allowed_status))
        c.append(
            (
                "Zaznaczone 'wystaw proforme automatycznie'",
                self.wystaw_proforme_automatycznie,
            )
        )
        c.append(
            (
                "Uzupełnione dane teleadresowe",
                self.object_has_required_data_for_invoice(),
            )
        )

        if self.faktura_zaliczkowa:
            c.append(("Uzupełnione pole 'zaliczka termin'", bool(self.zaliczka_termin)))
            c.append(("Uzupełnione pole 'zaliczka kwota'", bool(self.zaliczka_kwota)))

        s = "Typ: {0} - {1}<br><br>".format(
            "proforma" if proforma else "VAT",
            "ZALICZKOWA (nowa wersja)" if self.faktura_zaliczkowa else "ZWYKŁA",
        )

        for row in c:
            s += '{0}: <span style="color: {1}; font-weight: bold">{2}</span><br>'.format(
                row[0],
                "green" if row[1] else "red",
                "poprawne" if row[1] else "błędne",
            )
        return s

    def object_has_required_data_for_certificate(self):
        """
        Metoda sprawdza, czy obiekt posiada dane potrzebne do wygenerowania
        certyfikatu. Obecnie są to: imię i nazwisko oraz adres email.
        """

        return self.email and self.imie_nazwisko

    @staticmethod
    def has_required_data_for_invoice(data):
        """
        Metoda sprawdza, czy obiekt posiada dane potrzebne do wystawienia
        faktury VAT. Sprawdzanie odbywa się w zależności od tego czy Uczestnik
        jest firmą, czy osobą prywatną.
        """

        if data.get("prywatny"):
            return all(
                [
                    data.get("imie_nazwisko"),
                    bool(data.get("imie_nazwisko_zostalo_sprawdzone")),
                    data.get("adres"),
                    data.get("miejscowosc_kod"),
                    data.get("email"),
                ]
            )
        else:
            return all(
                [
                    data.get("faktura_firma"),
                    bool(data.get("imie_nazwisko_zostalo_sprawdzone")),
                    data.get("faktura_adres"),
                    data.get("faktura_miejscowosc_kod"),
                    data.get("faktura_kraj"),
                    data.get("faktura_nip"),
                    data.get("email"),
                ]
            )

    def object_has_required_data_for_invoice(self):
        """
        Rozdzielenie tej metody na dwie `object_has_required_data_for_invoice`
        oraz `has_required_data_for_invoice` (metoda statyczna) wynika z
        konieczności sprawdzania tego warunku w róznych miejscach z użyciem
        danych słownikowych i obiketów.
        """

        data = self.__dict__
        return self.has_required_data_for_invoice(data)

    def clean(self):
        # Linijka poniżej jest docelowo do odkomentowania.
        # self.sprawdz_spojnosc_rat()

        # Jeśli uczestnik jest firmą wymuś podanie danych tej firmy
        # if not self.prywatny:
        #     check = all([
        #         self.faktura_firma,
        #         self.faktura_adres,
        #         self.faktura_miejscowosc_kod,
        #         self.faktura_nip,
        #     ])
        #     if not check:
        #         raise ValidationError(
        #             "Jeśli uczestnik nie jest osobą prywatną musisz wypełnić "
        #             "dane jego firmy (nazwa, adres, miejscowość, NIP, VAT ID)."
        #         )
        #
        # Walidacja danych firmowych wyłączona, bo w obnecnej sytuacji nie ma sensu.
        # Biuro musi mieć możliwość wpisania uczestnika bez danych, do tego czasem
        # faktury nie są wystawiane (voucher, etc), więc na razie walidacja jest
        # wstrzymana. Docelowo zobaczymy jak się potoczy sytuacja.
        #
        pass

    def save(self, *args, **kwargs):
        # TODO: Ta metoda jest zbyt złożona. Wynieść logikę związaną z ratami gdzie indziej.
        if not self.pk and self.termin.obiady == "obiady-wliczone":
            self.chce_obiady = True
        if not self.czas_dodania:
            self.czas_dodania = datetime.datetime.now()
        if not self.kwota_do_zaplaty:
            if (
                self.termin.szkolenie.tag_dlugosc.slug == "kurs-zawodowy"
                and self.prywatny
            ):
                self.kwota_do_zaplaty = (
                    self.termin.szkolenie.cena / (1 + self.stawka_vat)
                ).quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)
            else:
                self.kwota_do_zaplaty = self.termin.szkolenie.cena
        if not self.termin_zaplaty:
            if int(self.za_kurs_zaplace) == 1:
                self.termin_zaplaty = min(
                    self.termin.termin,
                    datetime.date.today() + datetime.timedelta(days=7),
                )
            else:
                self.termin_zaplaty = self.termin.termin - datetime.timedelta(days=7)

        if int(self.za_kurs_zaplace) in [2, 4] and self.zaliczka_kwota is None:
            self.zaliczka_kwota = self.termin.szkolenie.waluta.zaliczka
            self.zaliczka_termin = datetime.date.today() + datetime.timedelta(days=7)
            self.rata1_kwota = self.kwota_do_zaplaty - self.zaliczka_kwota
            self.rata1_termin = self.termin.termin - datetime.timedelta(days=7)
            # self.termin_zaplaty = self.rata1_termin
            self.zaliczka_termin = min(self.zaliczka_termin, self.rata1_termin)
        elif int(self.za_kurs_zaplace) == 3 and self.zaliczka_kwota is None:
            self.zaliczka_kwota = self.termin.szkolenie.waluta.zaliczka
            self.zaliczka_termin = datetime.date.today() + datetime.timedelta(days=7)
            self.rata1_kwota = (self.kwota_do_zaplaty - self.zaliczka_kwota) / 2
            self.rata1_termin = self.termin.termin - datetime.timedelta(days=7)
            self.rata2_kwota = self.rata1_kwota
            self.rata2_termin = self.termin.termin + datetime.timedelta(days=30)
            # self.termin_zaplaty = self.rata2_termin
            self.zaliczka_termin = min(self.zaliczka_termin, self.rata1_termin)
        elif int(self.za_kurs_zaplace) == 10 and self.zaliczka_kwota is None:
            self.zaliczka_kwota = self.kwota_do_zaplaty / 5
            # „w terminie 7 dni, ale nie później niż na dzień roboczy przed startem kursu”
            # W uproszczeniu zakładamy, że „dzień roboczy” to „nie weekend”.
            self.zaliczka_termin = max(
                [
                    day
                    for day in [
                        datetime.date.today() + datetime.timedelta(days=i)
                        for i in range(1, 8)
                    ]
                    if day.weekday() < 5
                ]
            )
            for nr_raty in range(1, 5):
                setattr(self, "rata{0}_kwota".format(nr_raty), self.zaliczka_kwota)
            self.rata1_termin = max(
                [self.termin.termin, datetime.date.today() + relativedelta(months=1)]
            )
            for nr_raty in range(2, 5):
                wczesniejszy_termin = getattr(
                    self, "rata{0}_termin".format(nr_raty - 1)
                )
                setattr(
                    self,
                    "rata{0}_termin".format(nr_raty),
                    wczesniejszy_termin + relativedelta(months=1),
                )
        elif not self.pk and int(self.za_kurs_zaplace) in [1, 5, 6, 7]:
            # Dodajemy do pozostałych płatności zaliczkę w wysokości całej
            # kwoty, aby móc wystawiać faktury zaliczkowe.
            if self.zaliczka_kwota is None:
                self.zaliczka_kwota = self.kwota_do_zaplaty
            if self.zaliczka_termin is None:
                self.zaliczka_termin = self.termin_zaplaty
            if self.zaplacone and self.zaliczka_zaplacone is None:
                self.zaliczka_zaplacone = self.zaplacone

        if int(self.za_kurs_zaplace) == 2:
            if self.rata1_termin:
                self.termin_zaplaty = self.rata1_termin
        elif int(self.za_kurs_zaplace) == 3:
            if self.rata2_termin:
                self.termin_zaplaty = self.rata2_termin
        elif int(self.za_kurs_zaplace) == 7:
            if self.rata4_termin:
                self.termin_zaplaty = self.rata4_termin
            elif self.rata3_termin:
                self.termin_zaplaty = self.rata3_termin
            elif self.rata2_termin:
                self.termin_zaplaty = self.rata2_termin
            elif self.rata1_termin:
                self.termin_zaplaty = self.rata1_termin
        if not self.firma:
            self.firma = Firma.utworz_lub_pobierz_z_uczestnika(self)

        """
        Jeżeli wybrano płatność 6 lub 10, to ustaw fakturę na ostatni dzień
        szkolenia. #22561
        """

        if self.nr_faktury and self.faktura_po_szkoleniu:
            self.faktura_po_szkoleniu = False
            self.faktura_po_szkoleniu_data = None
        elif not self.nr_faktury and self.za_kurs_zaplace in [6, 10]:
            self.faktura_po_szkoleniu = True

        if self.faktura_po_szkoleniu:
            if not self.faktura_po_szkoleniu_data:
                if self.termin.daty_szczegolowo:
                    try:
                        termin_faktury = parser.parse(
                            self.termin.daty_szczegolowo.split(",")[-1]
                        )
                        termin_faktury = self.rata1_termin or termin_faktury.date()
                        self.faktura_po_szkoleniu_data = termin_faktury
                    except ValueError:
                        """
                        FIXME: Powiadomienie via Sentry
                        """
                        pass

        if self.termin.szkolenie.wysylaj_powiadomienia_proformy is False:
            self.wystaw_proforme_automatycznie = False

        super().save(*args, **kwargs)
        update_termin_calendars(self.termin)

        # Po zapisie uczestnika aktualizujemy termin (aby ew. odświeżyć etagi na froncie
        # (np. liczba dostępnych miejsc), oczywiście tylko wtedy, gdy termin jest
        # promowany.
        termin = TerminSzkolenia.objects.get(pk=self.termin_id)
        if termin.czy_reklamowac:
            termin.save(update_fields=["updated_at"])

    def ilosc_osob(self):
        return (
            not self.uczestnik_wieloosobowy_ilosc_osob
            and 1
            or self.uczestnik_wieloosobowy_ilosc_osob
        )

    def __str__(self):
        return (
            "%s (%s)" % (self.imie_nazwisko_oneline(), self.termin.szkolenie.kod)
            + (" (%s)" % self.faktura_firma if self.faktura_firma else "")
        )[0:100]

    def nazwa(self):
        """
        Imię i nazwisko (dla uczestników prywatnych) lub nazwa firmy.
        """
        if self.prywatny:
            return self.imie_nazwisko
        else:
            """
            Teoretycznie uczestnik firmowy powinien mieć podaną
            nazwę firmy, w praktyce różnie z tym bywa.
            """
            return self.faktura_firma or self.imie_nazwisko

    def nazwa_oneline(self):
        parts = [u.strip() for u in self.nazwa().splitlines() if u]
        return " ".join(parts)

    @staticmethod
    def normalizuj_dla_levenshtein(nazwa):
        nazwa = nazwa.lower()
        for string_do_wyciecia in [" ", ".", "-", "spzoo", "polska", "poland"]:
            nazwa = nazwa.replace(string_do_wyciecia, "")
        return nazwa


@receiver(post_save, sender=Uczestnik)
@receiver(post_delete, sender=Uczestnik)
def aktualizuj_statystyki_firmy(sender, instance, **kwargs):
    if instance.firma:
        instance.firma.aktualizuj_pola_ze_statystykami()


class ProwadzacyManager(models.Manager):
    def active(self, date=None):
        """
        Pobieramy tylko tych prowadzących z którymi nadal współpracujemy.
        """

        if date:
            return self.filter(
                models.Q(pracowal_do__isnull=True) | models.Q(pracowal_do__gte=date)
            )
        else:
            return self.filter(pracowal_do__isnull=True)


class Prowadzacy(models.Model):
    imie = models.CharField(max_length=50)
    nazwisko = models.CharField(max_length=50)
    tytul_naukowy = models.CharField("tytuł naukowy", max_length=50, blank=True)
    calendar = models.CharField(
        max_length=256,
        validators=[
            GoogleCalendarIDValidator(),
        ],
        blank=True,
        help_text="sam calendar ID, nie URL",
    )
    szkolenia = models.ManyToManyField(
        Szkolenie,
        blank=True,
        through=Szkolenie.prowadzacy.through,
        related_name="prowadzacy_set",
    )
    komentarz = models.TextField(blank=True)
    # fotka = models.ImageField(storage=uploads, upload_to='prowadzacy', blank=True)
    fotka = StdImageField(
        storage=assets_uploads,
        upload_to="prowadzacy",
        blank=True,
        variations={
            "sylwetka": {"width": 66, "height": 66, "crop": True},
            "sylwetka_big": {"width": 160, "height": 160, "crop": True},
        },
    )
    podpis = StdImageField(
        storage=assets_uploads,
        upload_to=get_upload_path_for_random_name,
        blank=True,
        variations={
            "certyfikat": {"width": 233, "height": 79, "crop": True},
        },
    )    
    sylwetka = models.TextField(
        "sylwetka (skrócone bio treera)",
        blank=True,
        help_text="Opis krótki - na stronę; może być "
        "luźniejszy, zgodnie z aktualną "
        "konwencją strony. Bez tagów HTML.",
    )
    tagline = models.TextField(
        "Umiejętności trenera",
        blank=True,
        help_text="Tekst, który wyświetla się w prawej kolumnie obok "
        "zdjęcia prowadzącego. Można użyć <span> i wtedy tekst nim "
        "objęty się nie złamie.",
    )
    tagline_en = models.TextField(
        "Umiejętności trenera (wersja EN)",
        blank=True,
        help_text="Tekst, który wyświetla się w prawej kolumnie obok "
        "zdjęcia prowadzącego. Można użyć <span> i wtedy tekst nim "
        "objęty się nie złamie.",
    )
    opis_do_certyfikatow = models.TextField(
        "opis do certyfikatów",
        blank=True,
        help_text="Ma być bardzo poważny. Bez tagów HTML.",
    )
    szczegolowy_opis = models.TextField(
        "pełne bio trenera", blank=True, help_text="Dozwolony tagi HTML."
    )
    pokazywac = models.BooleanField(
        "pokazywać",
        default=False,
        help_text='Decyduje o pokazywaniu na stronie "Wykładowcy". '
        "UWAGA: przy e-certyfikatach opis trenera będzie pokazywany "
        "nawet bez włączania tej opcji.",
    )
    nie_pokazuj_w_kolumnie = models.BooleanField(
        "*nie* pokazuj w prawej kolumnie",
        default=False,
        help_text="Decyduje o pokazywaniu lub nie w prawej kolumnie na "
        "podstronach serwisu. Działa tylko wtedy, gdy opcja "
        '"pokazywać" jest włączona, w przeciwnym razie wartość tego '
        "pola nie ma znaczenia.",
    )
    ordering = models.IntegerField(blank=True, null=True)
    user = models.ForeignKey(User, blank=True, null=True, on_delete=models.PROTECT)
    pracowal_do = models.DateField("pracował do dnia", blank=True, null=True)
    umowa = models.CharField(
        "umowa",
        choices=(
            ("b2b", "B2B / faktura"),
            ("uop", "umowa o pracę"),
            ("uz", "umowa zlecenie"),
            ("uod", "umowa o dzieło"),
        ),
        max_length=100,
        blank=True,
    )

    created_at = models.DateTimeField(
        "utworzono", auto_now_add=True, null=True, db_index=True
    )
    updated_at = models.DateTimeField("aktualizowano", auto_now=True, null=True)

    objects = ProwadzacyManager()

    def __str__(self):
        if self.tytul_naukowy:
            return "%s %s %s" % (self.tytul_naukowy, self.imie, self.nazwisko)
        return "%s %s" % (self.imie, self.nazwisko)

    def imie_naziwsko(self):
        if self.tytul_naukowy:
            return "%s %s %s" % (self.tytul_naukowy, self.imie, self.nazwisko)
        return "%s %s" % (self.imie, self.nazwisko)

    def tytul_imie(self):
        if self.tytul_naukowy:
            return "%s (%s)" % (self.imie, self.tytul_naukowy)
        return self.imie

    def sylwetka_image_url(self):
        if self.fotka:
            return self.fotka.sylwetka.url
        # return reverse('prowadzacy_fotka', args=['sylwetka', self.id])

    def sylwetka_big_image_url(self):
        if self.fotka:
            return self.fotka.sylwetka_big.url
        # return reverse('prowadzacy_fotka', args=['sylwetka_big', self.id])

    @mark_safe
    def zestawienie_link(self):
        return '<a href="%s">zestawienie</a>' % reverse(
            "wykladowca_zestawienie", args=[self.id]
        )

    zestawienie_link.short_description = "Zestawienie"

    def clean(self):
        if self.pokazywac and not self.fotka:
            raise ValidationError(
                "Jeśli wykładowca ma być pokazywany na stronie, to musi mieć fotkę."
            )

    class Meta:
        verbose_name = "prowadzący"
        verbose_name_plural = "prowadzący"
        ordering = ["nazwisko", "imie"]


class Leave(models.Model):
    user = models.ForeignKey(
        User,
        verbose_name="użytkownik",
        on_delete=models.PROTECT,
        help_text="Uzupełniane automatycznie lub przez moderatora.",
    )
    moderator = models.ForeignKey(
        User,
        verbose_name="przełożony",
        on_delete=models.PROTECT,
        help_text="Uzupełniane przez moderatora.",
        null=True,
        limit_choices_to={"is_superuser": True},
        related_name="moderator",
    )
    dates = models.TextField("daty szczegółowo", validators=[MultiDatesValidator()])
    days = models.PositiveSmallIntegerField(
        "liczba dni", help_text="Wyliczane automatycznie."
    )
    start = models.DateField("początek", help_text="Wyliczane automatycznie.")
    end = models.DateField("koniec", help_text="Wyliczane automatycznie.")
    leave_type = models.CharField(
        "typ urlopu",
        max_length=20,
        choices=(
            ("w", "wypoczynkowy"),
            ("b", "bezpłatny"),
            ("o", "okolicznościowy"),
            ("dz", "opieka nad dzieckiem"),
        ),
        default="w",
    )
    year = models.IntegerField("za rok")
    accepted = models.BooleanField(
        "zaakceptowany", default=False, help_text="Uzupełnia moderator."
    )
    comment = models.TextField("komentarz", blank=True)

    created_at = models.DateTimeField(
        "utworzono", default=datetime.datetime.now, null=True, db_index=True
    )
    updated_at = models.DateTimeField(
        "aktualizowano", auto_now=True, null=True, db_index=True
    )

    class Meta:
        verbose_name = "wniosek urlopowy"
        verbose_name_plural = "wnioski urlopowe"
        ordering = ["-created_at"]

    def __str__(self):
        return self.dates

    def human_type_name(self):
        return dict(
            (
                ("w", "wypoczynkowego"),
                ("b", "bezpłatnego"),
                ("o", "okolicznościowego"),
                ("dz", "opieki nad dzieckiem"),
            )
        )[self.leave_type]

    def save(self, *args, **kwargs):
        dates = [parse_date(d) for d in self.dates.split(",")]
        self.days = len(dates)
        self.start = min(dates)
        self.end = max(dates)
        return super().save(*args, **kwargs)


STANY_SPRAWY = (
    ("aktualne", "zgłoszenie aktualne"),
    ("zapisal sie", "rozwiązany - zapisał się"),
    ("zrezygnowal", "zamkniety - zrezygnował"),
    ("zrezygnowalismy", "zamknięty - my zrezygnowaliśmy - zbyt dawno"),
)


class PotencjalnyChetny(models.Model):
    imie_nazwisko = models.CharField("Imię i nazwisko", max_length=100, blank=True)
    szkolenie = models.ForeignKey(Szkolenie, on_delete=models.PROTECT)
    data_ostatniego_kontaktu = models.DateField(
        blank=True, help_text="jak zostawisz puste, ustawi się na dzisiaj"
    )
    email = models.EmailField(blank=True)
    telefon = models.CharField(max_length=100, blank=True)
    firma = models.CharField(max_length=100, blank=True)
    uwagi = models.TextField(blank=True)
    wprowadzil = models.ForeignKey(User, on_delete=models.PROTECT)
    stan_sprawy = models.CharField(
        max_length=100, choices=STANY_SPRAWY, default="aktualne"
    )

    def save(self, *args, **kwargs):
        if self.data_ostatniego_kontaktu is None:
            self.data_ostatniego_kontaktu = datetime.date.today()
        super().save(*args, **kwargs)

    def __str__(self):
        return self.imie_nazwisko

    class Meta:
        verbose_name = "potencjalny chętny"
        verbose_name_plural = "potencjalni chętni"


class Lokalizacja(models.Model):
    shortname = models.CharField(max_length=32)
    fullname = models.CharField(max_length=256)
    fullname_miejscownik = models.CharField(
        max_length=256,
        blank=True,
        help_text="Nazwa lokalizacji (miasta) w miejscowniku. Wykorzystywane "
        'w formie "... odbędzie się w Warszawie"',
    )
    instrukcja_dotarcia = models.CharField(
        max_length=256,
        blank=True,
        help_text="Krótka informacja, jak odnaleźć siedzibę lub salę - wykorzystywane w automatycznych "
        "powiadomieniach o starcie kursu.",
    )
    numer = models.IntegerField()
    reklamowana = models.BooleanField(
        default=False,
        help_text="np. w najbliższych terminach. Determinuje także "
        "wyświetlanie danej lokalizacji w zapisach na powiadomienia.",
    )
    calendar = models.CharField(
        max_length=256, blank=True, help_text="sam calendar ID, nie URL"
    )
    domyslna_cena_obiadu = models.DecimalField(
        max_digits=30,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="<b>Uwaga</b>: Cenę podajemy w walucie, w której odbywają się szkolenia w danej lokalizacji."
        "Jeśli w jednej lokalizacji mogą być szkolenia w wielu walutach, należy zgłosić to programistom.",
    )
    panstwo = models.ForeignKey(
        Panstwo, verbose_name="państwo", on_delete=models.PROTECT
    )
    ulica_i_numer = models.CharField(
        max_length=100,
        blank=True,
        help_text="np. <i>ul. Jasna 14/16A</i>",
    )

    def __str__(self):
        return self.shortname

    class Meta:
        verbose_name = "lokalizacja"
        verbose_name_plural = "lokalizacje"
        ordering = ["numer"]

    ZDALNA_IDS = {
        "pl": 11,
        "en": 13,        
    }

    @cached_property
    def zdalna(self):
        name = self.shortname.lower()
        return (
            "online" in name
            or "zdalnie" in name
            or "zdalna" in name
            or self.id in list(settings.SALA_ZDALNA_IDS.values())
        )

    @classmethod
    def get_zdalna(cls, language):
        return cls.objects.filter(id=settings.SALA_ZDALNA_IDS[language]).first()


class GrupaZaszeregowania(models.Model):
    nazwa = models.CharField(max_length=128)
    cena_u_klienta_waw = models.CharField(max_length=128, blank=True)
    cena_u_nas_waw = models.CharField(max_length=128, blank=True)
    cena_u_klienta_krk = models.CharField(max_length=128, blank=True)
    cena_u_nas_krk = models.CharField(max_length=128, blank=True)
    max_rozmiar_grupy = models.CharField(max_length=128, blank=True)
    stawka_godzinowa = models.IntegerField(blank=True, null=True)
    uwagi = models.TextField(blank=True)

    def __str__(self):
        return self.nazwa

    @mark_safe
    def szkolenia(self):
        return ", ".join(
            '<a href="/admin/www/szkolenie/%d/">%s</a>' % (x.id, escape(x.kod))
            for x in self.szkolenie_set.filter(
                language=settings.DEFAULT_LANGUAGE, aktywne=True
            )
        )

    def get_number(self):
        try:
            numer_grupy = int(self.nazwa.split(":")[0])
            return numer_grupy
        except ValueError:
            return 0

    class Meta:
        verbose_name = "grupa zaszeregowania"
        verbose_name_plural = "grupy zaszeregowania"
        ordering = ["nazwa"]


class Domena(models.Model):
    url = models.CharField(max_length=250)

    def __str__(self):
        return self.url

    class Meta:
        verbose_name = "domena"
        verbose_name_plural = "domeny"
        ordering = ["url"]


class TekstOTerminach(models.Model):
    szkolenie = models.ForeignKey(Szkolenie, on_delete=models.PROTECT)
    lokalizacja = models.ForeignKey(Lokalizacja, on_delete=models.PROTECT)
    tekst = models.TextField()


TYPY_REFERENCJI = (
    ("laurka ogolna", "laurka ogólna"),
    ("laurka ze szczegolami", "laurka ze szczegółami"),
    ("formalny protokol", "formalny protokół"),
    ("w trakcie", "w trakcie załatwiania"),
    ("czeka na odpowiedz", "czeka na odpowiedź"),
    ("niezalatwialne", "niezałatwialne"),
)


class Referencja(models.Model):
    typ = models.CharField(max_length=256, choices=TYPY_REFERENCJI)
    plik = models.FileField(
        storage=uploads, upload_to="referencje", blank=True, null=True
    )
    nazwa = models.TextField(blank=True)
    uwagi = models.TextField(blank=True)
    uczestnicy = models.ManyToManyField(Uczestnik, blank=True)
    czas_dodania_pliku = models.DateTimeField(blank=True, null=True)
    tagi_technologia = models.ManyToManyField(TagTechnologia)

    def __str__(self):
        return "Referencja #%d" % self.id

    def uczestnicy_repr(self):
        # u = ' '.join([x.faktura_firma if x.faktura_firma else x.imie_nazwisko for x in self.uczestnicy.all()])
        if self.uczestnicy.all():
            x = self.uczestnicy.all()[0]
            u = x.faktura_firma if x.faktura_firma else x.imie_nazwisko
        else:
            u = ""
        if len(u) > 100:
            u = u[:100] + "..."
        return u

    uczestnicy_repr.short_description = "Kto"

    def szkolenia_repr(self):
        kody = {}
        for u in self.uczestnicy.all():
            kody["%s %s" % (u.termin.szkolenie.kod, u.termin.termin)] = True
        return ", ".join(list(kody.keys()))

    szkolenia_repr.short_description = "Szkolenie"

    @mark_safe
    def plik_link(self):
        if self.plik:
            return '<a href="%s">%s</a>' % (
                reverse("uploads_referencje", kwargs={"path": self.plik.name}),
                escape(self.plik.name),
            )
        else:
            return ""

    plik_link.short_description = "Plik"

    def save(self):
        if self.czas_dodania_pliku is None and self.plik:
            self.czas_dodania_pliku = datetime.datetime.now()
        super().save()

    class Meta:
        verbose_name = "referencja"
        verbose_name_plural = "referencje"


class CalendarUpdate(models.Model):
    termin = models.ForeignKey(
        TerminSzkolenia, blank=True, null=True, on_delete=models.PROTECT
    )
    deleted_termin_id = models.IntegerField(blank=True, null=True)
    calendar = models.CharField(max_length=256)
    tries = models.IntegerField(default=0)
    creation_time = models.DateTimeField()
    update_type = models.CharField(
        max_length=128, choices=(("update", "update"), ("delete", "delete"))
    )
    done = models.CharField(
        max_length=128,
        choices=(("no", "no"), ("yes", "yes"), ("superseded", "superseded")),
        default="no",
    )
    last_failure_reason = models.TextField(blank=True)
    daty_szczegolowo = models.TextField(blank=True)


class Logo(models.Model):
    nazwa = models.CharField(max_length=250)
    plik = models.CharField(max_length=250)

    def __str__(self):
        return self.nazwa

    class Meta:
        verbose_name = "logo"
        verbose_name_plural = "loga"
        ordering = ["nazwa"]


class Sciezka(MappedTranslatedModel):
    class Meta:
        verbose_name = "ścieżka"
        verbose_name_plural = "ścieżki"
        ordering = ["ordering"]
        unique_together = ("slug", "language")

    nazwa = models.CharField(max_length=200)
    slug = models.CharField(max_length=50, blank=True)
    ordering = models.IntegerField(null=True, blank=True)
    opis = models.TextField(blank=True)
    dlugi_opis = models.TextField(blank=True)
    obrazek_height = models.IntegerField(
        help_text="w pikselach, default if not set=200", null=True, blank=True
    )
    highlights = GenericRelation("HighlightPlacement")

    def __str__(self):
        return self.nazwa

    def clean(self):
        if not self.slug:
            self.slug = my_slugify.slugify(self.nazwa)

    def get_svg_url(self):
        return "/static/sciezki/%s/%s.svg?2" % (self.language, self.slug)

    def get_swf_url(self):
        return "/static/sciezki/%s/flash/%s.swf" % (self.language, self.slug)

    def get_png_url(self):
        return "/static/sciezki/%s/%s.png" % (self.language, self.slug)

    def get_big_png_url(self):
        return "/static/sciezki/%s/%s-big.png" % (self.language, self.slug)

    def get_absolute_url(self):
        return reverse("sciezki_detail", kwargs={"slug": self.slug})

    def szkolenia(self):
        return self.szkolenie_set.filter(aktywne=True)

    def get_obrazek_height(self):
        if self.obrazek_height:
            return self.obrazek_height
        return 200


class Certyfikat(models.Model):
    termin = models.ForeignKey(TerminSzkolenia, on_delete=models.PROTECT)
    opis = models.CharField(max_length=256, blank=True)
    plik = models.FileField(storage=uploads, upload_to="certyfikaty")

    class Meta:
        verbose_name = "załącznik"
        verbose_name_plural = "załączniki"
        order_with_respect_to = "termin"

    def __str__(self):
        return "Załącznik #%s" % self.id


class Highlight(MappedTranslatedModel):
    tytul = models.CharField(max_length=256)
    tytul_h2 = models.CharField(max_length=256)
    opis_internal = models.CharField(max_length=256, blank=True)
    tresc = models.TextField()
    link = models.CharField(max_length=256, blank=True)
    obrazek = models.ImageField(
        _("obrazek"),
        upload_to="highlights/",
        blank=True,
        help_text="Gdy tagtech nie ma trenerów - obrazek pójdzie na prawą stronę. Gdy trenerzy są - pójdzie pod tekst topu, niezależnie od tego jaka się wybierze wersja renderowania topu. Sugerowany rozmiar obrazka: ok. 140×106px.",
    )
    uwagi_internal = models.TextField(blank=True)

    # Daty
    created_at = models.DateTimeField(
        "utworzono", auto_now_add=True, null=True, db_index=True
    )
    updated_at = models.DateTimeField(
        "aktualizowano", auto_now=True, null=True, db_index=True
    )

    def __str__(self):
        return self.tytul + (
            (" (%s)" % self.opis_internal) if self.opis_internal else ""
        )

    class Meta:
        ordering = ["tytul"]


class HighlightPlacement(models.Model):
    content_type = models.ForeignKey(ContentType, on_delete=models.PROTECT)
    object_id = models.PositiveIntegerField()
    page = GenericForeignKey("content_type", "object_id")
    initially_selected = models.BooleanField(default=False)
    highlight = models.ForeignKey(Highlight, on_delete=models.CASCADE)
    ordering = models.IntegerField(blank=True, null=True)

    class Meta:
        ordering = ["ordering"]


class Change(models.Model):
    user = models.ForeignKey(User, blank=True, null=True, on_delete=models.PROTECT)
    change_type = models.CharField(
        max_length=128,
        choices=(("CHANGE", "CHANGE"), ("CREATE", "CREATE"), ("DELETE", "DELETE")),
    )
    change_time = models.DateTimeField()
    # model_name = models.CharField(max_length=256)
    content_type = models.ForeignKey(ContentType, on_delete=models.PROTECT)
    object_id = models.PositiveIntegerField()
    object_name = models.TextField(blank=True)
    field = models.CharField(max_length=256, blank=True)
    old_value = models.TextField(blank=True)
    new_value = models.TextField(blank=True)

    @mark_safe
    def object_link(self):
        if self.change_type == "DELETE":
            return str(self.object_id)
        else:
            try:
                return '<a href="%s">%d</a>' % (
                    reverse(
                        "admin:%s_%s_change"
                        % (self.content_type.app_label, self.content_type.model),
                        args=[self.object_id],
                    ),
                    self.object_id,
                )
            except:  # niektóre typy obiektów nie mają własnej strony do edycji w adminie
                return str(self.object_id)

    object_link.short_description = "object_id"

    class Meta:
        verbose_name = "zmiana"
        verbose_name_plural = "zmiany"
        ordering = ["-change_time"]


############################
# Autoresponder
############################


class AutoresponderLogManager(models.Manager):
    def allow_to_send_autoreplay(self, email):
        """
        Metoda sprawdza, czy może wysłać do użytkownika autoodpowiedź. Ograniczenia:
          1. Nie więcej niż 5 maili na godzinę pod ten sam adres
          2. Nie więcej niż 15 maili na godzinę w ogóle
        """

        delta = datetime.datetime.now() - datetime.timedelta(hours=1)

        result = self.filter(email_sent_at__gte=delta).values_list(
            "receiver_email", flat=True
        )[:15]

        if len(result) < 15 and list(result).count(email) < 5:
            return True
        return False


class AutoresponderLog(models.Model):
    AUTORESPONDER_TYPE_CHOICES = (
        ("formularz_kontaktowy", "Formularz kontaktowy"),
        ("masz_pytanie", "Masz pytanie"),
        ("formularz_rejestracji", "Formularz rejestracji"),
        ("propozycja_terminu", "Propozycja terminu"),
        ("link_rejestracyjny", "Link rejestracyjny do szkolenia"),
    )
    receiver_email = models.EmailField("email", max_length=250)
    email_sent_at = models.DateTimeField(
        "data wysłania emaila", auto_now_add=True, db_index=True
    )
    email_type = models.CharField(
        "typ autoodpowiedzi",
        max_length=50,
        choices=AUTORESPONDER_TYPE_CHOICES,
    )

    objects = AutoresponderLogManager()

    class Meta:
        verbose_name = "log autoodpowiedzi"
        verbose_name_plural = "logi autoodpowiedzi"

    def __str__(self):
        return self.receiver_email


############################
# Absolwenci i certyfikaty
############################


class CertificateNo(models.Model):
    """
    Pomocniczy model generujacy numery certyfikatow, w formie: ALXDDDNNNY

    NNN - numer kolejny danego dnia od 001
    DDD - numer dnia w roku,
    Y - Rok 2016=A, 2017=B

    """

    date = models.DateField(unique=True)
    counter = models.PositiveSmallIntegerField(default=0)

    @property
    def number(self):
        letters = string.ascii_uppercase
        year_as_letter = letters[self.date.year - 2016]

        return "ALX{day:03d}{no:03d}{year}".format(
            day=self.date.timetuple().tm_yday,
            no=self.counter,
            year=year_as_letter,
        )


class Graduate(models.Model):
    participant = models.ForeignKey(
        Uczestnik,
        verbose_name="uczestnik",
        null=True,
        blank=True,
        on_delete=models.PROTECT,
    )
    term = models.ForeignKey(
        TerminSzkolenia, verbose_name="termin szkolenia", on_delete=models.PROTECT
    )
    number = models.CharField("numer certyfikatu", unique=True, max_length=10)

    name = models.CharField("imię i nazwisko", max_length=200)
    email = models.EmailField("adres email", max_length=250)
    slug = models.SlugField("slug", max_length=200, blank=True, editable=False)

    # Certyfikat
    mail_sent_counter = models.PositiveSmallIntegerField(
        "licznik wysłanych maili", default=0
    )
    mail_sent_at = models.DateTimeField("email wysłany dnia", blank=True, null=True)

    template = models.TextField("program szkolenia", blank=True)
    template_in_pdf = models.BooleanField(
        "dołącz program szkolenia do PDF",
        default=False,
    )
    pdf = models.FileField(
        "plik pdf", blank=True, storage=certificate_uploads, upload_to=get_upload_path
    )

    # Unikalny, losowy klucz
    key = UUIDField(
        default=uuid.uuid4,
        editable=False,
    )
    public_key = UUIDField(
        default=uuid.uuid4,
        editable=False,
    )

    created_at = models.DateTimeField("utworzono", auto_now_add=True)
    updated_at = models.DateTimeField("aktualizowano", auto_now=True)
    remote_addr = models.GenericIPAddressField(
        "adres IP", blank=True, null=True, editable=False
    )
    accepted = models.BooleanField(
        "zaakceptowany",
        default=True,
        help_text="Obecnie nie ma na nic wpływu. W przyszłości zostanie "
        "wykorzystany do automatyzacji wysyłek.",
    )

    discount_code = models.OneToOneField(
        "www.DiscountCode",
        verbose_name="kod rabatowy",
        blank=True,
        null=True,
        default=None,
        help_text="Zostanie automatycznie wygenerowany.",
        related_name="graduate",
        on_delete=models.PROTECT,
    )

    is_active = models.BooleanField("widoczny na stronie", default=True)

    class Meta:
        ordering = ("-created_at",)
        verbose_name = "absolwent"
        verbose_name_plural = "absolwenci"
        unique_together = ("term", "email")

    def __str__(self):
        return "{0} ({1})".format(self.name, self.email)

    def ready_to_run_celery_task(self):
        """
        Metoda zwraca wartośc logiczną mówiącą o tym, czy obiekt może zostać
        przekazany do Celery w celu wygenerowania certyfikatu PDF oraz wysłania
        maila. Obecnie można to zrobić tylko raz.
        """

        return not self.mail_sent_at

    def get_absolute_url(self):
        return reverse(
            "certificate",
            kwargs={
                "language": self.term.szkolenie.language,
                "slug": self.slug,
                "key": self.key.hex,
            },
        )

    def get_public_absolute_url(self):
        return reverse(
            "public_certificate",
            kwargs={
                "language": self.term.szkolenie.language,
                "slug": self.slug,
                "public_key": self.public_key.hex,
            },
        )

    @staticmethod
    def generate_slug(value):
        return my_slugify.slugify(value) or "no-name"

    def save(self, *args, **kwargs):
        # Jeśli slug nie istniej utwórz go na podstawie pola `name`.
        # Slug pownien być tworzony raz, gdyż jest elementem URL i jego zmiana
        # spowoduje błędne działanie linków.
        if not self.slug:
            self.slug = Graduate.generate_slug(self.name)

        # Zapisujemy program szkolenia lokalnie w obiekcie absolwenta
        if not self.pk:
            self.template = self.term.get_program_szkolenia()

            self.template_in_pdf = (
                self.term.program_szkolenia_w_pdf
                if self.term.program_szkolenia_w_pdf is not None
                else self.term.szkolenie.program_szkolenia_w_pdf
            )

            # Każdy absolwent otrzymuje kod rabatowy na 7%
            if not self.discount_code:
                self.discount_code = DiscountCode.objects.create(
                    discount=Decimal("7"), source="graduate", limit=1
                )

        if not self.number:
            cno, created = CertificateNo.objects.get_or_create(
                date=datetime.date.today()
            )
            number = CertificateNo.objects.select_for_update().get(pk=cno.pk)
            number.counter += 1
            number.save()
            self.number = number.number

        # Zawsze wykonywana normalizacja adresu email
        self.email = (self.email or "").lower()
        super().save(*args, **kwargs)


class CertificateGroupManager(models.Manager):
    def create_from_participant(self, participant):
        """
        Metoda tworzy obiekt modelu `CertificateGroup` oraz `Certificate` dla
        wszystkich uczestników z obiektu `participant`.

        UWAGA: metoda nie sprawdza, czy uczestnik jest grupą wieloosobową,
               to zostaje w gestii programisty.
        """

        def save():
            with transaction.atomic():
                # Uwaga na przypadek z nazwą firmy. Wiele szkoleń mimo grupy
                # wieloosobowej jej nie posiada, dlatego w przypadku jej braku
                # wstawiana jest wartość "Firma".
                obj = self.create(
                    name=participant.faktura_firma or "Firma",
                    email=participant.email,
                    participant=participant,
                )

                names = [u.strip() for u in participant.imie_nazwisko.splitlines()]

                # Uwaga `bulk_create` nie wywołuje metody `save` ani nie wysyła
                # sygnałów, dlatego należy utworzyć slug "ręcznie".
                Graduate.objects.bulk_create(
                    [
                        Graduate(
                            name=name,
                            participant=participant,
                            group=obj,
                            slug=Graduate.generate_slug(name),
                        )
                        for name in names
                    ]
                )
            return obj

        return save()


class CertificateGroup(models.Model):
    """
    UWAGA: Model obecnie nie jest wykorzystywany - nie generujemy
           certyfikatów dla opiekuna grupy - każdy uczestnik dostaje swój
           po zakończonej ankiecie.
    """

    participant = models.ForeignKey(
        Uczestnik, verbose_name="uczestnik", on_delete=models.PROTECT
    )

    name = models.CharField("imię i nazwisko", max_length=200)
    slug = models.SlugField("slug", max_length=200, blank=True)

    mail_sent_counter = models.PositiveSmallIntegerField(
        "licznik wysłanych maili", default=0
    )
    mail_sent_at = models.DateTimeField("email wysłany dnia", blank=True, null=True)

    email = models.EmailField("adres email")

    # Unikalny, losowy klucz
    key = UUIDField(
        default=uuid.uuid4,
        editable=False,
    )
    created_at = models.DateTimeField("utworzono", auto_now_add=True)
    updated_at = models.DateTimeField("aktualizowano", auto_now=True)

    def __str__(self):
        return self.name

    objects = CertificateGroupManager()

    class Meta:
        verbose_name = "grupa certyfikatów"
        verbose_name_plural = "grupy certyfikatów"
        ordering = ("-created_at",)

    @staticmethod
    def generate_slug(value):
        return my_slugify.slugify(value) or "no-name"

    def save(self, *args, **kwargs):
        # Jeśli slug nie istniej utwórz go na podstawie pola `name`.
        # Slug pownien być tworzony raz, gdyż jest elementem URL i jego zmiana
        # spowoduje błędne działanie linków.
        if not self.slug:
            self.slug = CertificateGroup.generate_slug(self.name)

        # Zawsze wykonywana normalizacja adresu email
        self.email = (self.email or "").lower()
        super().save(*args, **kwargs)


######################
# Notyfikacje
######################


class UserNotificationManager(models.Manager):
    def ready_for_notifications(self, sending_interval):
        """
        Metoda pobiera zweryfikowanych użytkowników, uwzględniając datę
        ostatniej wysyłki.
        """

        delta = datetime.datetime.now() - datetime.timedelta(hours=sending_interval)

        # Data ostatniej wysyłki jest wyliczana na dwa sposoby, biorąc pod
        # uwagę pole `last_email_sent_at`, gdy nie jest NULL, w  przeciwnym
        # razie role tego pola pełni pole `created_at` aż do czasu pierwszej
        # wysłki.

        # RODO wyłączone od 18.09.2019 (tos_agreement__isnull=False)
        # https://app.asana.com/0/4723313594922/1140411033746673

        return self.filter(status=1, send_notifications=True).filter(
            models.Q(last_email_sent_at__isnull=True, created_at__lte=delta)
            | models.Q(last_email_sent_at__isnull=False, last_email_sent_at__lte=delta)
        )

    def get_created_by_system(self):
        """
        Metoda pobiera użytkowników, którzy:

           - są aktywni
           - zostali dodani do systemu przez automat
           - zostali zapisani na wszystkie szkolenia przez automat
        """

        return (
            self.filter(status=1, source="system")
            .exclude(preferences__source__in=["www", "staff"])
            .distinct()
        )

    def potencjalnie_zainteresowani_z_innych_miast(self, term):
        result = []

        # Pobierz tylko takich Uczestników, którzy:
        #  - mają w preferencjach to szkolenie
        #  - *nie* mają w preferencjach tego miasta

        # RODO wyłączone od 18.09.2019 (tos_agreement__isnull=False)
        # https://app.asana.com/0/4723313594922/1140411033746673

        users_ids = list(
            UserCoursesNotification.objects.filter(
                user__status=1, training__pk=term.szkolenie.pk
            )
            .exclude(locations__pk__in=[term.lokalizacja.pk])
            .values_list("user_id", flat=True)
        )

        users = self.filter(pk__in=users_ids)

        for user in users:
            # Sprawdź, czy Uczestnik dostał już wcześniej jakąkolwiek
            # notyfikację o tym terminie.
            already_sent = UserNotificationLog.objects.filter(
                user=user, term_id=term.pk
            ).count()

            if already_sent:
                continue
            result.append(user)
        return result

    def get_active(self):
        """
        Metoda pobiera użytkowników, którzy są aktywni.
        """

        return self.filter(status=1).prefetch_related("preferences")


class UserNotification(models.Model):
    email = models.EmailField("adres email", max_length=200, unique=True)
    status = models.SmallIntegerField(
        "status",
        choices=(
            (-1, "nigdy niezweryfikowany"),
            (0, "niezweryfikowany"),
            (1, "zweryfikowany"),
            (2, "wypisany"),
        ),
        default=0,
        db_index=True,
    )
    send_notifications = models.BooleanField(
        "wysyłaj notyfikacje",
        default=True,
        help_text="Jeśli odznaczone, konto jest w pełni aktywne i "
        "funkcjonalne, ale same powiadomienia o nowych terminach "
        "nie są wysyłane.",
    )
    internal_comments = models.TextField("uwagi internal", blank=True)
    full_name = models.CharField("imię i nazwisko", max_length=200, blank=True)
    phone_number = models.CharField("telefon", max_length=150, blank=True)
    company_name = models.CharField("firma", max_length=255, blank=True)

    # Unikalny, losowy klucz
    key = UUIDField(default=uuid.uuid4, editable=False, verbose_name="klucz dostępu")

    # Jezyk oraz źródło rejestracji
    language = models.CharField(
        "język",
        max_length=2,
        choices=settings.LANGUAGES,
        default=settings.DEFAULT_LANGUAGE,
    )
    source = models.CharField(
        "źródło rejestracji",
        choices=(
            ("www", "strona www"),
            ("system", "automat"),
            ("staff", "dodany przez Biuro"),
        ),
        default="www",
        max_length=20,
        db_index=True,
    )

    # Daty
    created_at = models.DateTimeField("utworzono", auto_now_add=True, db_index=True)
    updated_at = models.DateTimeField("aktualizowano", auto_now=True)
    last_email_sent_at = models.DateTimeField(
        "ostatnie powiadomienie", null=True, blank=True, db_index=True
    )
    activation_at = models.DateTimeField("data aktywacji", null=True, blank=True)
    activation_email_sent_at = models.DateTimeField(
        "data wysłania emaila aktywacyjnego", null=True, blank=True
    )
    resend_activation_email_counter = models.PositiveSmallIntegerField(
        "liczba wysłanych maili aktywayjnych", default=0
    )

    remote_addr = models.GenericIPAddressField("adres IP", blank=True, null=True)
    user_agent = models.CharField("user agent", max_length=255, blank=True)

    # Edycja BOK
    staff_updated_at = models.DateTimeField(null=True, blank=True)
    staff_updated_by = models.ForeignKey(
        User, null=True, blank=True, on_delete=models.PROTECT
    )

    # Zgody
    tos_agreement = models.DateTimeField(
        "zgoda na przesyłanie informacji handlowej",
        null=True,
        blank=True,
        default=datetime.datetime.now,
    )

    objects = UserNotificationManager()

    class Meta:
        verbose_name = "powiadomienie o szkoleniu"
        verbose_name_plural = "powiadomienia o szkoleniach"

    def __str__(self):
        return self.email

    def save(self, *args, **kwargs):
        # Zawsze wykonywana normalizacja adresu email
        self.email = self.email.lower()
        super().save(*args, **kwargs)

    def set_as_cancelled(self):
        self.status = 2
        self.save()

        # Usuń logi i preferencje
        UserCoursesNotification.objects.filter(user=self).delete()
        UserNotificationLog.objects.filter(user=self).delete()

    def set_as_never_activated(self):
        self.status = -1
        self.save()

    def set_as_activated(self):
        self.status = 1
        self.activation_at = datetime.datetime.now()
        self.save()

    def is_system(self):
        return self.source == "system"


class UserCoursesNotificationManager(models.Manager):
    def reports_query(self, locations, **filters):
        """
        Metoda, która robi query dla raportu zapisanych użytkowników.

        Interesuje nas liczba unikalnych użytkowników dla każdego szkolenia,
        oraz liczba zapisów dla poszczególnego miasta.
        """

        # Budujemy filtr poprzez który pobierzemy sumarycznie, wszystkie
        # lokalizacje dla danego szkolenia.
        locations_annotate = {}

        for location in locations:
            locations_annotate["{0}".format(location.pk)] = Count(
                Case(
                    When(
                        condition=Q(locations__pk=location.pk),
                        then=1,
                    )
                )
            )

        return (
            self.filter(**filters)
            .values("training")
            .annotate(
                total_users=models.Count("pk", distinct=True),
                total_users_in_locations=Count(
                    Case(
                        When(
                            condition=Q(locations__pk__in=[l.pk for l in locations]),
                            then=1,
                        )
                    )
                ),
                **locations_annotate,
            )
            .filter(total_users__gt=0)
            .order_by("-total_users")
            .distinct()
        )

    def stats_query(self, locations, **filters):
        """
        Metoda, która robi query dla statystyk (wykresy) zapisanych
        użytkowników.

        Interesuje nas liczba użytkowników dla każdego miasta pogrupowana
        według miesięcy.
        """

        # Budujemy filtr poprzez który pobierzemy sumarycznie, wszystkie
        # lokalizacje dla danej daty.
        locations_annotate = {}

        for location in locations:
            locations_annotate["{0}".format(location.pk)] = Count(
                Case(
                    When(
                        condition=Q(locations__pk=location.pk),
                        then=1,
                    )
                )
            )

        # Uwaga! W relacji również istnieje pole o nazwie `created_at` - musimy
        # zadbać o to, aby baza wiedziała o które nam chodzi - dodajemy
        # prefix.
        truncate_date = connection.ops.date_trunc_sql(
            "month", "{0}.created_at".format(self.model._meta.db_table)
        )

        qs = (
            self.filter(**filters)
            .annotate(
                month=Func(
                    F("created_at"),
                    function="DATE_TRUNC",
                    template="%(function)s('month', %(expressions)s)",
                )
            )
            .values("month")
            .annotate(**locations_annotate)
        )
        qs.query.group_by = ["month"]
        return qs.order_by("month")


class UserCoursesNotification(models.Model):
    user = models.ForeignKey(
        UserNotification, related_name="preferences", on_delete=models.CASCADE
    )
    training = models.ForeignKey(
        Szkolenie, verbose_name="szkolenie", on_delete=models.PROTECT
    )
    locations = models.ManyToManyField(Lokalizacja, verbose_name="lokalizacje")
    source = models.CharField(
        "źródło zapisu",
        choices=(
            ("www", "strona www"),
            ("system", "automat"),
            ("staff", "dodany przez Biuro"),
        ),
        default="www",
        max_length=20,
        db_index=True,
    )

    # Daty
    created_at = models.DateTimeField("utworzono", auto_now_add=True)
    updated_at = models.DateTimeField("aktualizowano", auto_now=True)

    objects = UserCoursesNotificationManager()

    class Meta:
        ordering = ("-created_at",)
        verbose_name = "szkolenie użytkownika"
        verbose_name_plural = "szkolenia użytkownika"
        unique_together = ("user", "training")

    def __str__(self):
        # Ten warunek jest bardzo ważny, inaczej podczas usuwania inline
        # w Adminie dostaniemy wyjątek "Maximum recursion depth".
        if self.pk:
            locations = ", ".join([x.fullname for x in self.locations.all()])
            return "{}: {}".format(self.training, locations)
        return ""


class UserNotificationLogManager(models.Manager):
    def get_queryset(self):
        """
        Przeciążamy domyślne `get_queryset` w celu pobrania danych z relacji
        (np. do wyświetlania w Panelu Adm.)
        """

        qs = super().get_queryset()
        return qs.select_related("term", "term__szkolenie", "location")


class UserNotificationLog(models.Model):
    NOTIFICATION_TYPE_CHOICES = (
        ("term_created", "termin utworzony"),
        ("term_started", "termin uruchomiony"),
        ("before_start", "termin ma odpowiednią ilość uczestników"),
        ("other_locations", "potencjalnie zainteresowani z innych miast"),
    )

    user = models.ForeignKey(UserNotification, on_delete=models.PROTECT)
    term = models.ForeignKey(
        TerminSzkolenia, verbose_name="termin szkolenia", on_delete=models.PROTECT
    )
    location = models.ForeignKey(
        Lokalizacja, verbose_name="lokalizacja", on_delete=models.PROTECT
    )
    email_sent_at = models.DateTimeField(
        "data wysłania emaila", auto_now_add=True, db_index=True
    )
    notification_type = models.CharField(
        "typ powiadomienia",
        max_length=50,
        choices=NOTIFICATION_TYPE_CHOICES,
    )

    objects = UserNotificationLogManager()

    class Meta:
        verbose_name = "log powiadomień"
        verbose_name_plural = "logi powiadomień"
        unique_together = ("user", "term", "location", "notification_type")

    def __str__(self):
        return "{} / {}".format(self.user, self.term)

    @staticmethod
    def get_status(is_started, before_start):
        if before_start:
            return "before_start"
        if is_started:
            return "term_started"
        return "term_created"


#######################
# Historie absolwentów
#######################


class GraduateStory(models.Model):
    name = models.CharField("imię i nazwisko", max_length=200)
    slug = models.SlugField(
        "slug",
        help_text="UWAGA: zmiana tego pola zmieni URL do strony absolwenta!",
        max_length=200,
        unique=True,
    )
    short_info = models.TextField(
        "krótki tekst na strone zbiorczą", help_text="Bez tagów HTML."
    )
    text1 = models.TextField(
        "tekst po prawej stronie zdjęcia", help_text="Dozwolone tagi HTML."
    )
    text2 = models.TextField("tekst pod zdjęciem", help_text="Dozwolone tagi HTML.")

    photo = models.ImageField(
        "zdjęcie",
        storage=assets_uploads,
        upload_to=get_upload_path_for_graduate_persons,
        max_length=250,
    )

    ordering = models.IntegerField("kolejność", default=1)
    created_at = models.DateTimeField("utworzono", auto_now_add=True)
    updated_at = models.DateTimeField("aktualizowano", auto_now=True)

    is_active = models.BooleanField("widoczny na stronie", default=True)

    class Meta:
        ordering = ("ordering",)
        verbose_name = "sylwetka absolwenta"
        verbose_name_plural = "sylwetki absolwentów"

    def __str__(self):
        return self.name

    def get_absolute_url(self):
        return reverse(
            "graduate_story",
            kwargs={
                "language": "pl",
                "slug": self.slug,
            },
        )


#############
# Assets
#############


class Asset(models.Model):
    file = models.FileField(
        "plik",
        storage=assets_uploads,
        upload_to=get_upload_path_for_assets,
        max_length=250,
    )

    description = models.TextField("pomocniczy opis", blank=True)

    user = models.ForeignKey(
        User,
        verbose_name="autor",
        blank=True,
        null=True,
        editable=False,
        on_delete=models.PROTECT,
    )

    # Daty
    created_at = models.DateTimeField("utworzono", auto_now_add=True)
    updated_at = models.DateTimeField("aktualizowano", auto_now=True)

    def delete(self, *args, **kwargs):
        self.file.delete()
        super().delete(*args, **kwargs)


##############
# Kontynuacje
##############


class ContinuationUnsubscribed(models.Model):
    email = models.EmailField("email", unique=True)

    # Daty
    created_at = models.DateTimeField("utworzono", auto_now_add=True)
    updated_at = models.DateTimeField("aktualizowano", auto_now=True)

    class Meta:
        ordering = ("-created_at",)
        verbose_name = "anulowanie kontynuacji"
        verbose_name_plural = "anulowanie kontynuacji"

    def save(self, *args, **kwargs):
        self.email = self.email.lower()
        super().save(*args, **kwargs)

    def __str__(self):
        return self.email


class ContinuationLogManager(models.Manager):
    def allow_second_notification(self, email, training):
        return (
            not self.filter(email__iexact=email, training=training)
            .filter(
                models.Q(notification_type="second_notification")
                | models.Q(
                    models.Q(notification_type="first_notification")
                    & models.Q(
                        created_at__gte=datetime.datetime.now()
                        - datetime.timedelta(days=42)
                    )
                )
            )
            .exists()
        )

    def any_continuation_exists(self, email, training):
        return self.filter(
            email__iexact=email,
            training=training,
            notification_type__in=["first_notification", "second_notification"],
        ).exists()


class ContinuationLog(models.Model):
    NOTIFICATION_TYPE_CHOICES = (
        ("first_notification", "pierwsze powiadomienie"),
        ("second_notification", "drugie powiadomienie"),
    )

    term = models.ForeignKey(
        TerminSzkolenia, verbose_name="termin szkolenia", on_delete=models.PROTECT
    )
    training = models.ForeignKey(
        Szkolenie, verbose_name="szkolenie (kontynuacja)", on_delete=models.PROTECT
    )
    email = models.EmailField("email", db_index=True)
    participant = models.ForeignKey(
        Uczestnik,
        verbose_name="uczestnik",
        null=True,
        blank=True,
        on_delete=models.PROTECT,
    )
    notification_type = models.CharField(
        "typ powiadomienia",
        max_length=50,
        choices=NOTIFICATION_TYPE_CHOICES,
    )
    discount_code = models.ForeignKey(
        "www.DiscountCode",
        verbose_name="kod rabatowy",
        blank=True,
        null=True,
        default=None,
        on_delete=models.PROTECT,
        related_name="continuation_mails",
    )

    # Daty
    created_at = models.DateTimeField("utworzono", auto_now_add=True)
    updated_at = models.DateTimeField("aktualizowano", auto_now=True)

    objects = ContinuationLogManager()

    class Meta:
        ordering = ("-created_at",)
        verbose_name = "log kontynuacji"
        verbose_name_plural = "logi kontynuacji"
        unique_together = ("training", "email", "notification_type")

    def __str__(self):
        return self.email


class UczestnikNotification(models.Model):
    NOTIFICATION_TYPE_CHOICES = (
        ("rata_1_niezaplacona", "rata I niezapłacona"),
        ("rata_2_niezaplacona", "rata II niezapłacona"),
        ("rata_3_niezaplacona", "rata III niezapłacona"),
        ("rata_4_niezaplacona", "rata IV niezapłacona"),
        ("rata_5_niezaplacona", "rata V niezapłacona"),
    )

    participant = models.ForeignKey(
        Uczestnik, verbose_name="uczestnik", on_delete=models.PROTECT
    )
    email = models.EmailField("email uczestnika")
    notification_type = models.CharField(
        "typ powiadomienia",
        max_length=50,
        choices=NOTIFICATION_TYPE_CHOICES,
    )
    message = models.TextField("wiadomość")

    # Daty
    created_at = models.DateTimeField("utworzono", auto_now_add=True)
    updated_at = models.DateTimeField("aktualizowano", auto_now=True)

    class Meta:
        ordering = ("-created_at",)
        verbose_name = "notyfikacja uczestnika"
        verbose_name_plural = "notyfikacje uczestników"
        unique_together = ("participant", "notification_type")

    def __str__(self):
        return self.notification_type


####################
# Auto wysyłka
# (postivo)
####################


class FakturaWysylkaManager(models.Manager):
    def create_from_zliczacz(self, api_data):
        # usuwamy NULLe
        for f in ("faktura_uwagi", "faktura_nip_nabywcy"):
            if api_data[f] is None:
                api_data[f] = ""

        try:
            uczestnik = Uczestnik.objects.filter(
                models.Q(nr_proformy=api_data["faktura_numer"])
                | models.Q(nr_faktury=api_data["faktura_numer"])
                | models.Q(zliczacz_proforma_no=api_data["faktura_id"])
                | models.Q(zliczacz_faktura_no=api_data["faktura_id"])
            )[0]
        except (IndexError, AttributeError, TypeError):
            uczestnik = None

        recipient_name = api_data["faktura_nazwa_nabywcy"]

        if uczestnik and uczestnik.osoba_do_kontaktu:
            recipient_name = "{}, {}".format(
                uczestnik.osoba_do_kontaktu, recipient_name
            )

        fk_data = {
            "faktura_id": api_data["faktura_id"],
            "faktura_numer": api_data["faktura_numer"],
            "faktura_uwagi": api_data["faktura_uwagi"],
            "faktura_adres_nabywcy": api_data["faktura_adres_nabywcy"],
            "recipient_name": recipient_name,
            "recipient_address": api_data["faktura_adres_nabywcy"],
            "recipient_post_code": api_data["faktura_kod_pocztowy_nabywcy"],
            "recipient_city": api_data["faktura_miasto_nabywcy"],
            "uczestnik": uczestnik,
        }
        fk = self.model(**fk_data)
        fk.save()
        return fk


class FakturaWysylka(models.Model):
    recipient_name = models.CharField("nazwa odbiorcy", max_length=250)
    recipient_address = models.CharField("ulica odbiorcy", max_length=100)
    recipient_post_code = models.CharField("kod pocztowy", max_length=10)
    recipient_city = models.CharField("miejscowość", max_length=150)
    recipient_country = models.CharField("kraj", default="PL", max_length=10)

    faktura_id = models.IntegerField("ID faktury", unique=True)
    faktura_numer = models.CharField("numer faktury", max_length=100)
    faktura_uwagi = models.TextField("uwagi na fakturze", blank=True)
    faktura_adres_nabywcy = models.CharField("adres nabywcy z faktury", max_length=200)
    uczestnik = models.ForeignKey(
        Uczestnik,
        verbose_name="Uczestnik",
        null=True,
        blank=True,
        default=None,
        on_delete=models.PROTECT,
    )

    dispatch_id = models.CharField(
        "postivo id", max_length=100, unique=True, null=True, default=None
    )
    status = models.CharField(
        "postivo status",
        blank=True,
        max_length=10,
        choices=(
            ("1", "Przesyłka przyjęta do realizacji"),
            ("2", "Przesyłka w trakcie konfekcjonowania"),
            ("3", "Przesyłka przekazana operatorowi pocztowemu"),
            (
                "4",
                "Przesyłka doręczona do odbiorcy [w przypadku wysyłki "
                "listów za potwierdzeniem odbioru]",
            ),
            ("5", "Przesyłka zwrócona do Postivo.pl"),
            ("50", "Faks jest w trakcie wysyłania"),
            ("51", "Faks wysłany poprawnie"),
            ("52", "Brak sygnału faksu odbiorcy"),
            ("53", "Połączenie nieodebrane"),
            ("54", "Linia zajęta lub nieprawidłowy numer"),
            ("55", "Nieokreślony rezultat"),
            ("56", "Faks nie został wysłany"),
            ("100", "Przesyłka anulowana"),
            (
                "200",
                "Status zewnętrzny – status pochodzący od operatora "
                "pocztowego. Treść statusu znajduje się w polu "
                "status_description.",
            ),
        ),
    )
    status_date = models.DateTimeField("data zmiany statusu", null=True, default=None)
    dispatched_at = models.DateTimeField("zlecono wysyłkę", null=True, default=None)
    key = UUIDField(
        default=uuid.uuid4,
        editable=False,
    )

    # Daty
    created_at = models.DateTimeField(
        "utworzono", auto_now_add=True, null=True, db_index=True
    )
    updated_at = models.DateTimeField("aktualizowano", auto_now=True, null=True)

    objects = FakturaWysylkaManager()

    class Meta:
        verbose_name = "wysyłka faktury"
        verbose_name_plural = "wysyłka faktur"
        ordering = ("-created_at",)

    def __str__(self):
        return self.faktura_numer

    def get_recipient_data(self):
        return {
            "recipient_name": self.recipient_name,
            "recipient_address": self.recipient_address,
            "recipient_post_code": self.recipient_post_code,
            "recipient_city": self.recipient_city,
            "recipient_country": self.recipient_country,
        }

    def has_required_data(self):
        return all(self.get_recipient_data().values())

    @property
    def callback_url(self):
        """
        Metoda zwraca url z domeną do powiadomien postivo.pl
        """

        protocol = settings.FORCE_SSL and "https" or "http"

        return "{0}://{1}{2}".format(
            protocol,
            settings.DOMENY_DLA_JEZYKOW["pl"],
            reverse("postivo_callback", kwargs={"key": self.key.hex}),
        )


####################
# Faktura korekta
####################


class FakturaKorektaManager(models.Manager):
    def get_faktura_kolejny_numer_korekty(self, month, year):
        """
        Generujemy kolejny numer faktury MAX(faktura_kolejny_numer_korekty) + 1
        """

        cnt = (
            self.filter(
                faktura_data_wystawienia_korekty__month=month,
                faktura_data_wystawienia_korekty__year=year,
            )
            .aggregate(models.Max("faktura_kolejny_numer_korekty"))
            .get("faktura_kolejny_numer_korekty__max")
            or 0
        )
        return cnt + 1

    def create_from_zliczacz(self, api_data, make_defaults=True):
        pozycje_faktury = api_data.pop("pozycje_faktury", [])

        # usuwamy NULLe
        for f in ("faktura_uwagi", "faktura_nip_nabywcy"):
            if api_data[f] is None:
                api_data[f] = ""

        today = datetime.date.today()

        fk_data = {}
        for f in self.model._meta.fields:
            if f.name in api_data:
                fk_data[f.name] = api_data[f.name]

        fk_data["faktura_data_wystawienia_korekty"] = today
        fk_data[
            "faktura_kolejny_numer_korekty"
        ] = self.model.objects.get_faktura_kolejny_numer_korekty(
            today.month, today.year
        )
        fk = self.model(**fk_data)
        fk.save()

        for pos in pozycje_faktury:
            pos_data = dict(pos)
            pos_data_clean = {}

            for f in FakturaKorektaPozycjaOryginalna._meta.fields:
                if f.name in pos_data:
                    pos_data_clean[f.name] = pos_data[f.name]

            if "pozycja_zaliczka" in pos_data_clean:
                pos_data_clean["pozycja_zaliczka"] = bool(
                    pos_data_clean["pozycja_zaliczka"]
                )

            pos_data_clean["faktura_korekta"] = fk
            FakturaKorektaPozycjaOryginalna(**pos_data_clean).save()

            if make_defaults:
                pos_data_clean["pozycja_cena_jednostkowa_netto"] = 0
                pos_data_clean["pozycja_wartosc_netto"] = 0
                pos_data_clean["pozycja_kwota_vat"] = 0
                pos_data_clean["pozycja_wartosc_brutto"] = 0
                FakturaKorektaPozycjaKorygujaca(**pos_data_clean).save()
        if fk.wyliczaj_bilans:
            fk.set_cena_bilans()
        return fk


class FakturaKorekta(models.Model):
    FAKTURA_TYP_DATY = (
        (1, "Data wykonania usługi"),
        (2, "Data wpłaty"),
        (3, "Data sprzedaży"),
    )

    FAKTURA_STATUS = (
        (2, "gotowa"),
        (3, "wysłana"),
        (4, "zapłacona"),
        (5, "niewindykowalna"),
        (6, "zapłacona częściowo"),
    )

    # Oryginał
    faktura_id = models.IntegerField("zliczacz ID")
    faktura_numer = models.CharField("numer faktury", max_length=100)
    status_faktury = models.IntegerField(
        "status faktury", null=True, choices=FAKTURA_STATUS
    )
    faktura_data_sprzedazy = models.DateField("data sprzedaży", null=True)
    faktura_data_zaplaty = models.DateField("data zapłaty", blank=True, null=True)
    faktura_data_wystawienia = models.DateField("data wystawienia", null=True)
    faktura_termin_platnosci = models.DateField("termin płatności", null=True)
    faktura_sposob_platnosci = models.CharField(
        "sposób płatności", max_length=255, blank=True
    )
    faktura_miejsce_wystawienia = models.CharField(
        "miejsce wystawienia", max_length=255
    )
    faktura_typ_daty = models.IntegerField("typ", null=True, choices=FAKTURA_TYP_DATY)
    faktura_uwagi = models.TextField("uwagi", blank=True)

    # Korekta
    faktura_numer_korekty = models.CharField(
        "numer faktury korygującej", max_length=100, unique=True
    )
    faktura_kolejny_numer_korekty = models.IntegerField(
        "kolejny numer",
        null=True,
        help_text="To jest kolejny numer do pełnego numeru faktury, który "
        "będzie miał postać: {kolejny numer}/MM/YYYY/K. MM i YYYY "
        "są brane z pola 'Data wystawienia korekty'.",
    )
    faktura_data_wystawienia_korekty = models.DateField(
        "data wystawienia korekty",
        default=datetime.date.today,
        help_text="Na podstawie tej daty budowany jest numer faktury. "
        "Zmiana roku lub miesiąca spowoduje zmianę numeru faktury.",
    )
    faktura_miejsce_wystawienia_korekty = models.CharField(
        "miejsce wystawienia", max_length=255, default="Warszawa"
    )
    faktura_powod_korekty = models.TextField(
        "powód korekty", default="Niezebrana grupa szkoleniowa."
    )
    faktura_sposob_platnosci_korekty = models.CharField(
        "sposób płatności", max_length=255, blank=True
    )
    faktura_termin_platnosci_korekty = models.DateField(
        "termin płatności", null=True, blank=True
    )
    cena_bilans = models.DecimalField(
        "cena - bilans",
        max_digits=12,
        decimal_places=2,
        default=0,
        help_text="Wyliczana automatycznie (jeśli zaznaczone pole "
        "'Wyliczaj automatycznie bilans').<br>W przypadku opłaconych "
        "faktur, jest to różnica 'SUMA KOREKTA' - 'SUMA ORYGINAŁ' (znak ujemny to "
        "zwrot, a dodatni dopłata). W przypadku faktur nieopłaconych "
        "jest to po prostu 'SUMA KOREKTA'.<br>PS. Opłacona faktura to "
        "taka, która ma ustawiony status w polu 'Faktura - Oryginał' / "
        "'Status faktury' na 'zapłacona'.",
    )
    wyliczaj_bilans = models.BooleanField("wyliczaj automatycznie bilans", default=True)

    # Nie pokazywać
    typ_faktury = models.IntegerField("typ faktury", null=True)

    # Sprzedawca
    faktura_kod_pocztowy_sprzedawcy = models.CharField("kod pocztowy", max_length=15)
    faktura_nazwa_sprzedawcy = models.CharField("nazwa", max_length=255)
    faktura_adres_sprzedawcy = models.CharField("adres", max_length=255)
    faktura_miasto_sprzedawcy = models.CharField("miejscowość", max_length=255)
    faktura_numer_konta = models.CharField("numer konta", max_length=255)
    faktura_nip_sprzedawcy = models.CharField("NIP", max_length=50)

    # Nabywca
    faktura_nazwa_nabywcy = models.CharField("nazwa", max_length=255)
    faktura_adres_nabywcy = models.CharField("adres", max_length=255)
    faktura_nip_nabywcy = models.CharField("NIP", max_length=50, blank=True)
    faktura_miasto_nabywcy = models.CharField("miejscowość", max_length=255)
    faktura_kod_pocztowy_nabywcy = models.CharField("kod pocztowy", max_length=15)

    uczestnik = models.OneToOneField(
        Uczestnik,
        verbose_name="Uczestnik",
        null=True,
        blank=True,
        default=None,
        on_delete=models.PROTECT,
    )

    # Daty
    created_at = models.DateTimeField(
        "utworzono", auto_now_add=True, null=True, db_index=True
    )
    updated_at = models.DateTimeField("aktualizowano", auto_now=True, null=True)

    objects = FakturaKorektaManager()

    class Meta:
        verbose_name = "korekta faktury"
        verbose_name_plural = "korekty faktur"
        ordering = ("-created_at",)

    def __str__(self):
        return self.faktura_numer

    def save(self, *args, **kwargs):
        self.faktura_numer_korekty = self.get_faktura_numer_korekty()

        add_info = not self.pk and self.uczestnik_id
        obj = super().save(*args, **kwargs)

        # Jeśli dodajemy korekte i jest przypisany Uczestnik to dodajemy do
        # pola z jego uwami, info o korekcie.
        if add_info:
            if self.uczestnik.uwagi:
                self.uczestnik.uwagi += "\n\n"
            self.uczestnik.uwagi += "Korekta numer: {}".format(
                self.faktura_numer_korekty
            )
            self.uczestnik.save()
        return obj

    def clean(self):
        if self.id:
            if (
                self.__class__.objects.exclude(pk=self.pk)
                .filter(faktura_numer_korekty=self.get_faktura_numer_korekty())
                .exists()
            ):
                raise ValidationError(
                    {
                        "faktura_kolejny_numer_korekty": "Taki numer już istnieje "
                        "dla takiej daty wystawienia."
                    }
                )
        return super().clean()

    @property
    def is_zaplacona(self):
        return self.status_faktury and self.status_faktury == 4

    def get_faktura_numer_korekty(self):
        return "{0}/{1:02d}/{2}/K".format(
            self.faktura_kolejny_numer_korekty,
            self.faktura_data_wystawienia_korekty.month,
            self.faktura_data_wystawienia_korekty.strftime("%y"),
        )

    def set_cena_bilans(self):
        cena_oryg = 0
        cena_korekta = 0

        for pos in self.fakturakorektapozycjaoryginalna_set.all():
            cena_oryg += pos.pozycja_wartosc_brutto

        for pos in self.fakturakorektapozycjakorygujaca_set.all():
            cena_korekta += pos.pozycja_wartosc_brutto

        if not self.is_zaplacona:
            self.cena_bilans = cena_korekta
        else:
            self.cena_bilans = cena_korekta - cena_oryg
        self.save()


FAKTURA_STAWKA_VAT = (
    (1, "22% (22)"),
    (2, "7% (7)"),
    (3, "3% (3)"),
    (4, "0% (0)"),
    (5, "zwolniony (zw.)"),
    (6, "nie podlega opodatkowaniu (NP)"),
    (7, "8% (8)"),
    (8, "23% (23)"),
)

FAKTURA_STAWKA_VAT_MAP = {
    1: {
        "stawka_vat_nazwa": "22%",
        "stawka_vat_skrot": "22",
        "stawka_vat_mnoznik": Decimal("0.2200"),
    },
    2: {
        "stawka_vat_nazwa": "7%",
        "stawka_vat_skrot": "7",
        "stawka_vat_mnoznik": Decimal("0.0700"),
    },
    3: {
        "stawka_vat_nazwa": "3%",
        "stawka_vat_skrot": "3",
        "stawka_vat_mnoznik": Decimal("0.0300"),
    },
    4: {
        "stawka_vat_nazwa": "0%",
        "stawka_vat_skrot": "0",
        "stawka_vat_mnoznik": Decimal("0.0000"),
    },
    5: {
        "stawka_vat_nazwa": "zwolniony",
        "stawka_vat_skrot": "zw.",
        "stawka_vat_mnoznik": Decimal("0.0000"),
    },
    6: {
        "stawka_vat_nazwa": "nie podlega opodatkowaniu",
        "stawka_vat_skrot": "NP",
        "stawka_vat_mnoznik": Decimal("0.0000"),
    },
    7: {
        "stawka_vat_nazwa": "8%",
        "stawka_vat_skrot": "8",
        "stawka_vat_mnoznik": Decimal("0.0800"),
    },
    8: {
        "stawka_vat_nazwa": "23%",
        "stawka_vat_skrot": "23",
        "stawka_vat_mnoznik": Decimal("0.2300"),
    },
}


class FakturaKorektaPozycjaOryginalna(models.Model):
    faktura_korekta = models.ForeignKey(FakturaKorekta, on_delete=models.CASCADE)
    pozycja_lp = models.IntegerField("LP")
    pozycja_opis = models.TextField("opis")
    pozycja_cena_jednostkowa_netto = models.DecimalField(
        "cena jednostkowa netto", max_digits=12, decimal_places=2
    )
    pozycja_liczba_jednostek = models.DecimalField(
        "liczba jednostek", max_digits=12, decimal_places=2
    )
    pozycja_wartosc_netto = models.DecimalField(
        "wartość netto", max_digits=12, decimal_places=2
    )
    pozycja_wartosc_brutto = models.DecimalField(
        "wartość brutto", max_digits=12, decimal_places=2
    )
    pozycja_kwota_vat = models.DecimalField(
        "kwota VAT", max_digits=12, decimal_places=2
    )
    stawka_vat = models.IntegerField("stawka VAT", choices=FAKTURA_STAWKA_VAT)
    pozycja_zaliczka = models.BooleanField("zaliczka?", default=False)

    # Daty
    created_at = models.DateTimeField(
        "utworzono", auto_now_add=True, null=True, db_index=True
    )
    updated_at = models.DateTimeField("aktualizowano", auto_now=True, null=True)

    class Meta:
        verbose_name = "pozycja oryginalna"
        verbose_name_plural = "pozycje oryginalne"
        ordering = ("-created_at",)

    def __str__(self):
        return self.pozycja_opis


class FakturaKorektaPozycjaKorygujaca(models.Model):
    faktura_korekta = models.ForeignKey(FakturaKorekta, on_delete=models.CASCADE)
    pozycja_lp = models.IntegerField("LP")
    pozycja_opis = models.TextField("opis")
    pozycja_cena_jednostkowa_netto = models.DecimalField(
        "cena jednostkowa netto", max_digits=12, decimal_places=2
    )
    pozycja_wartosc_netto = models.DecimalField(
        "wartość netto", max_digits=12, decimal_places=2
    )
    pozycja_liczba_jednostek = models.DecimalField(
        "liczba jednostek", max_digits=12, decimal_places=2
    )
    pozycja_wartosc_brutto = models.DecimalField(
        "wartość brutto", max_digits=12, decimal_places=2
    )
    pozycja_kwota_vat = models.DecimalField(
        "kwota VAT", max_digits=12, decimal_places=2
    )
    stawka_vat = models.IntegerField("stawka VAT", choices=FAKTURA_STAWKA_VAT)
    pozycja_zaliczka = models.BooleanField("zaliczka?", default=False)

    # Daty
    created_at = models.DateTimeField(
        "utworzono", auto_now_add=True, null=True, db_index=True
    )
    updated_at = models.DateTimeField("aktualizowano", auto_now=True, null=True)

    class Meta:
        verbose_name = "pozycja korygująca"
        verbose_name_plural = "pozycje korygujące"
        ordering = ("-created_at",)

    def __str__(self):
        return self.pozycja_opis


class DiscountCodeManager(models.Manager):
    def generate_code(self):
        """
        Generuje wartoś kodu rabatowego.

        Dla pewności sprawdzamy 15 losowych tokenów pod kątem ich istanienia
        w bazie danych. Szansa że poniższa pętla wykona się więcej jak jeden
        raz jest praktycznie równa zeru.
        """

        for __ in range(10):
            codes = [random_discount_code(length=6) for _loop in range(15)]

            existing = set(
                self.get_queryset()
                .filter(code__in=codes)
                .values_list("code", flat=True)
            )

            free_to_use = list(set(codes) - existing)

            if not free_to_use:
                continue
            return free_to_use[0]
        raise Exception("Could not generate a discount code.")

    def active(self):
        """
        Filtruje tylko aktywne kody promocyjne.
        """

        now = datetime.datetime.now()

        return (
            self.filter(is_active=True)
            .filter(
                models.Q(available_from__isnull=True)
                | models.Q(available_from__lte=now)
            )
            .filter(
                models.Q(available_to__isnull=True) | models.Q(available_to__gt=now)
            )
        )

    def get_active(self, code):
        """
        Zwraca kod rabatowy, którego można użyć przy zapisie.
        """

        return (
            self.active()
            .annotate(used_count=models.Count("uczestnik"))
            .filter(
                models.Q(limit__isnull=True)
                | models.Q(used_count__lt=models.F("limit"))
            )
            .filter(code__iexact=code)
            .first()
        )


class DiscountCode(models.Model):
    code = models.SlugField(
        "kod",
        max_length=20,
        unique=True,
        blank=True,
        help_text="Zostaw puste aby wygenerować kod automatycznie.",
    )

    discount = models.DecimalField(
        "zniżka (%)",
        default=Decimal("7"),
        max_digits=5,
        decimal_places=2,
        validators=[MaxValueValidator(Decimal("50")), MinValueValidator(Decimal("1"))],
    )

    # None - użytkownik może korzystać z kodu dowoli
    # != None - użytkownik może wykorzystać kod `limit` razy
    limit = models.PositiveSmallIntegerField(
        "limit użyć",
        blank=True,
        null=True,
        default=1,
        validators=[MinValueValidator(1)],
        help_text="Gdy pusty użytkownik może korzystać z kodu dowoli.",
    )

    available_from = models.DateTimeField(
        "ważny od", blank=True, null=True, db_index=True
    )
    available_to = models.DateTimeField(
        "ważny do", blank=True, null=True, db_index=True
    )
    is_active = models.BooleanField("aktywny?", default=True)

    source = models.CharField(
        "źródło",
        choices=(
            ("graduate", "absolwent"),
            ("continuation_training", "kontynuacja szkoleń"),
            ("30days", "zapis 30 dni przd rozpoczęciem kursu"),
            ("staff", "dodany przez Biuro"),
        ),
        default="graduate",
        max_length=50,
    )
    comment = models.TextField("komentarz", blank=True)

    # Daty
    created_at = models.DateTimeField("utworzono", auto_now_add=True, db_index=True)
    updated_at = models.DateTimeField("aktualizowano", auto_now=True)

    objects = DiscountCodeManager()

    class Meta:
        verbose_name = "kod rabatowy"
        verbose_name_plural = "kody rabatowe"

    def save(self, *args, **kwargs):
        if not self.code:
            self.code = DiscountCode.objects.generate_code()
        return super().save(*args, **kwargs)

    def __str__(self):
        return "{0} ({1}%, źródło: {2})".format(
            self.code, str(self.discount), self.get_source_display()
        )

    def to_decimal(self):
        return (Decimal(100) - self.discount) / Decimal(100)

    def get_graduate(self):

        return self.graduate if hasattr(self, "graduate") else None

        # return self.graduate.objects.select_related(
        #     'participant__termin__szkolenie',
        #     'term'
        # ).first()


#############
# Tools
#############

MODELS_CHANGE_LOGGED = (
    Domena,
    GrupaZaszeregowania,
    Lokalizacja,
    MenuItem,
    PotencjalnyChetny,
    Prowadzacy,
    Sala,
    SiteModule,
    MyFlatPage,
    Szkolenie,
    TagDlugosc,
    TagTechnologia,
    TagZawod,
    TerminSzkolenia,
    Uczestnik,
    Referencja,
    Logo,
    Sciezka,
    Certyfikat,
    TekstOTerminach,
    Highlight,
    Waluta,
    UserNotification,
    UserCoursesNotification,
    Tab,
    FakturaKorekta,
    FakturaKorektaPozycjaOryginalna,
    FakturaKorektaPozycjaKorygujaca,
    Sprzet,
    DzienSzkolenia,
    FakturaWysylka,
    Leave,
    GraduateStory,
    DiscountCode,
    LeadCategory,
    LeadUser,
    BlackList,
    Header,
)


def pretty_value(obj, field):
    # display = getattr(obj, 'get_%s_display' % field, None)
    # if display:
    #   return display()
    # else:
    #   return getattr(obj, field, None)
    return field.value_from_object(obj)


def parse_miejscowosc_kod(miejscowosc_kod):
    match = re.search("\d{2}-?\d{3}", miejscowosc_kod)
    if match:
        kod = match.group(0)
        miejscowosc = re.sub(re.escape(kod) + r"|,", r"", miejscowosc_kod).strip()
        return {"kod": kod, "miejscowosc": miejscowosc}
    else:
        return {"kod": "", "miejscowosc": ""}


#############
# Signals
#############

from django.db.models.signals import post_init
from django.dispatch import receiver


@receiver(post_init)
def save_original_state(sender, instance, **kwargs):
    if sender not in MODELS_CHANGE_LOGGED:
        return
    if instance.id is None:
        return
    instance._original_state = dict(
        [(x.name, pretty_value(instance, x)) for x in instance._meta.fields]
    )


@receiver(post_save)
def log_change(sender, instance, created, **kwargs):
    if sender not in MODELS_CHANGE_LOGGED:
        return
    if not created and getattr(instance, "_original_state", None) is None:
        return

    now = datetime.datetime.now()
    if created:
        change = Change()
        change.user = get_current_user()
        change.change_time = now
        change.change_type = "CREATE"
        change.content_type = ContentType.objects.get_for_model(instance)
        change.object_id = instance.pk
        change.object_name = str(instance)
        change.save()
    else:
        orig = instance._original_state
        try:
            changes = []
            for field in instance._meta.fields:
                if field.name == "czas_dodania":
                    continue
                value = orig[field.name]
                new_value = pretty_value(instance, field)
                if value != new_value:
                    change = Change()
                    change.user = get_current_user()
                    change.change_time = now
                    change.change_type = "CHANGE"
                    change.content_type = ContentType.objects.get_for_model(instance)
                    change.object_id = instance.pk
                    change.object_name = str(instance)
                    change.field = field.name
                    change.old_value = str(value)
                    change.new_value = str(new_value)
                    changes.append(change)
            Change.objects.bulk_create(changes)
        except:
            if not settings.DEBUG:
                mail_admins(
                    "AL-WWW error in log_change()",
                    "%s %s\n\n%s" % (repr(sender), instance.id, traceback.format_exc()),
                )
            else:
                raise


@receiver(post_delete)
def log_delete(sender, instance, **kwargs):
    if sender not in MODELS_CHANGE_LOGGED:
        return
    now = datetime.datetime.now()
    change = Change()
    change.user = get_current_user()
    change.change_time = now
    change.change_type = "DELETE"
    change.content_type = ContentType.objects.get_for_model(instance)
    change.object_id = instance.pk
    try:
        change.object_name = str(instance)
    except:
        change.object_name = "error"
    change.save()


#############
# End of Signals
#############


class TerminSzkoleniaLog(models.Model):
    """
    Model przechowujacy zdarzenia, wykonywane na TerminieSzkolenia.
    Np. wysyłamy powiadomienia, czy grupa rusza, czy nie i chcemy to
    zrobić tylko raz.
    """

    LOG_TYPE_CHOICES = (
        ("kontynuacja_do_trenera", "Mail z kontynuacją do trenera"),
        ("16_dni_przed_startem", "16 dni przed startem"),
        ("grupa_rusza_prawie_zebrana", "Grupa rusza (prawie zebrana)"),
        ("grupa_rusza_szybkie_potwierdzenie", "Grupa rusza (szybkie potwierdzenie)"),
        (
            "potencjalnie_zainteresowani_z_innych_miast",
            "Potencjalnie zainteresowani z innych miast",
        ),
        ("czy_reklamowac", "Włączone reklamowanie"),
        ("odbylo_sie", "Uruchomione"),
    )

    term = models.ForeignKey(
        TerminSzkolenia, verbose_name="termin szkolenia", on_delete=models.PROTECT
    )
    log_type = models.CharField(
        "typ powiadomienia",
        max_length=200,
        choices=LOG_TYPE_CHOICES,
    )

    user = models.ForeignKey(
        User, blank=True, null=True, default=None, on_delete=models.PROTECT
    )

    # Daty
    created_at = models.DateTimeField(
        "utworzono", auto_now_add=True, null=True, db_index=True
    )
    updated_at = models.DateTimeField("aktualizowano", auto_now=True, null=True)

    class Meta:
        verbose_name = "log terminu szkolenia"
        verbose_name_plural = "logi terminów szkoleń"
        unique_together = ("term", "log_type")


class TerminSzkoleniaMail(models.Model):
    PARTICIPANT_STATUSES = STATUSY_UCZESTNIKA + ((99, "WSZYSCY NIEZREZYGNOWANI"),)

    term = models.ForeignKey(
        TerminSzkolenia, verbose_name="termin szkolenia", on_delete=models.PROTECT
    )
    subject = models.CharField(
        "tytuł",
        max_length=200,
    )
    message = models.TextField(
        "treść",
    )
    attachment = models.FileField(
        "załącznik", blank=True, storage=attachment_uploads, upload_to=get_upload_path
    )
    participant_status = models.IntegerField(choices=PARTICIPANT_STATUSES, default=1)

    author = models.ForeignKey(User, verbose_name="autor", on_delete=models.PROTECT)

    # Daty
    created_at = models.DateTimeField(
        "utworzono", auto_now_add=True, null=True, db_index=True
    )
    updated_at = models.DateTimeField("aktualizowano", auto_now=True, null=True)

    class Meta:
        verbose_name = "mail do grupy"
        verbose_name_plural = "maile do grupy"


class TerminSzkoleniaMailUczestnik(models.Model):
    term_mail = models.ForeignKey(TerminSzkoleniaMail, on_delete=models.PROTECT)
    participant = models.ForeignKey(
        Uczestnik, verbose_name="uczestnik", on_delete=models.PROTECT
    )

    sent_at = models.DateTimeField("wysłano", null=True, blank=True)

    # Daty
    created_at = models.DateTimeField(
        "utworzono", auto_now_add=True, null=True, db_index=True
    )
    updated_at = models.DateTimeField("aktualizowano", auto_now=True, null=True)

    class Meta:
        verbose_name = "mail do uczestnika"
        verbose_name_plural = "maile do uczestników"
        unique_together = ("term_mail", "participant")


class UczestnikPlik(models.Model):
    participant = models.ForeignKey(
        Uczestnik, verbose_name="uczestnik", on_delete=models.PROTECT
    )

    file = models.FileField(
        "plik",
        storage=participant_uploads,
        upload_to=get_upload_path_for_participant_files,
        max_length=250,
    )

    remote_addr = models.GenericIPAddressField(
        "adres IP", blank=True, null=True, editable=False
    )

    # Daty
    created_at = models.DateTimeField(
        "utworzono", auto_now_add=True, null=True, db_index=True
    )
    updated_at = models.DateTimeField("aktualizowano", auto_now=True, null=True)

    class Meta:
        verbose_name = "plik"
        verbose_name_plural = "pliki"

    def __str__(self):
        return ""

    def delete(self, *args, **kwargs):
        self.file.delete()
        super().delete(*args, **kwargs)
