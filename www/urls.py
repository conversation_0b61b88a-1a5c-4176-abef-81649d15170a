from django.conf import settings
from django.urls import path, re_path

from . import views as www_views
from .views import prowadzacy_autocomplete

urlpatterns = [
    path(
        "",
        www_views.frontpage,
        {
            "language": settings.DEFAULT_LANGUAGE,
        },
    ),

    # Bare tech roots should 404 to avoid redirect loops
    re_path(r"^pl/tech/$", www_views.tech_root_404),
    re_path(r"^tech/$", www_views.tech_root_404),

    re_path(r"^(?P<language>pl|en)/$", www_views.frontpage),
    path("pl/frontpage-eng-new/", www_views.frontpage, name="frontpage_eng_new"),
    path("s/", www_views.index, name="pelna_lista_szkolen2"),
    path("szkolenia/", www_views.index, name="pelna_lista_szkolen"),
    path("pl/szkolenia/", www_views.index, kwargs={"language": "pl"}, name="pelna_lista_szkolen_pl_prefixed"),
    path(
        "en/courses/",
        www_views.index,
        kwargs={"language": "en"},
        name="pelna_lista_szkolen_en",
    ),
    path("najblizsze/", www_views.najblizsze_szkolenia, name="najblizsze_szkolenia"),
    path(
        "en/dates/",
        www_views.najblizsze_szkolenia,
        kwargs={"language": "en"},
        name="najblizsze_szkolenia_en",
    ),
    # Last-Minute
    path("pl/last_minute/", www_views.last_minute, name="last_minute"),
    re_path(
        r"^pl/last_minute/(?P<target>kursy|szkolenia)/$",
        www_views.last_minute,
        name="last_minute",
    ),
    re_path(
        r"^(?P<language>pl|en)/post_contact_form/$",
        www_views.post_contact_form,
        name="post_contact_form",
    ),
    re_path(
        r"^(?P<language>pl|en)/(?P<slug>[^\/]+)/send_registration_link/$",
        www_views.send_registration_link,
        name="send_registration_link",
    ),
    re_path(
        r"^(?P<language>pl)/absolwent/(?P<slug>[^\/]+)/$",
        www_views.graduate_story,
        kwargs={"language": "pl"},
        name="graduate_story",
    ),
    re_path(
        r"^(?P<language>pl)/historie-absolwentow/$",
        www_views.graduates,
        kwargs={"language": "pl"},
        name="graduates",
    ),

    re_path(
        r"^szkolenia/(?P<slug>[^\/]+)/(?P<tab_slug>[a-zA-Z0-9\-_.]+)/$",
        www_views.detail,
        kwargs={"language": "pl"},
    ),
    # Prefixed Polish routes for szkolenia to avoid prefix redirect loops
    # TODO: clean
    re_path(
        r"^pl/szkolenia/(?P<slug>[^\/]+)/(?P<tab_slug>[a-zA-Z0-9\-_.]+)/$",
        www_views.detail,
        kwargs={"language": "pl"},
        name="detail_pl_tab_prefixed",
    ),
    re_path(
        r"^szkolenia/(?P<slug>[^\/]+)/$",
        www_views.detail,
        kwargs={"language": "pl"},
        name="detail_pl",
    ),

    # TODO: clean
    re_path(
        r"^pl/szkolenia/(?P<slug>[^\/]+)/$",
        www_views.detail,
        kwargs={"language": "pl"},
        name="detail_pl_prefixed",
    ),
    re_path(
        r"^en/courses/(?P<slug>[^\/]+)/(?P<tab_slug>[a-zA-Z0-9\-_.]+)/$",
        www_views.detail,
        kwargs={"language": "en"},
    ),
    re_path(
        r"^en/courses/(?P<slug>[^\/]+)/$",
        www_views.detail,
        kwargs={"language": "en"},
        name="detail_en",
    ),
    re_path(
        r"^pdf/(?P<slug>[^\/]+)/$",
        www_views.detail_pdf,
        kwargs={"language": "pl"},
        name="detail_pdf",
    ),
    re_path(
        r"^en/pdf/(?P<slug>[^\/]+)/$",
        www_views.detail_pdf,
        kwargs={"language": "en"},
        name="detail_pdf",
    ),
    re_path(
        r"^zgloszenie/(?P<slug>[^\/]+)/$",
        www_views.zgloszenie,
        kwargs={"language": "pl"},
        name="zgloszenie",
    ),  # never_cache
    re_path(
        r"^en/order/(?P<slug>[^\/]+)/$",
        www_views.zgloszenie,
        kwargs={"language": "en"},
        name="zgloszenie",
    ),  # never_cache
    re_path(
        r"^zgloszenie/(?P<slug>[^\/]+)/(?P<secret_key>[a-zA-Z0-9]{32})/$",
        www_views.zamkniete_zgloszenie,
        kwargs={"language": "pl"},
        name="zamkniete_zgloszenie",
    ),  # never_cache
    re_path(
        r"^en/order/(?P<slug>[^\/]+)/(?P<secret_key>[a-zA-Z0-9]{32})/$",
        www_views.zamkniete_zgloszenie,
        kwargs={"language": "en"},
        name="zamkniete_zgloszenie",
    ),  # never_cache
    re_path(
        r"^dziekujemy_za_zgloszenie/(?P<token>.{16})/$",
        www_views.dziekujemy_za_zgloszenie,
        kwargs={"conversion": True, "language": "pl"},
        name="dziekujemy_za_zgloszenie",
    ),
    re_path(
        r"^en/thank_you/(?P<token>.{16})/$",
        www_views.dziekujemy_za_zgloszenie,
        kwargs={"conversion": True, "language": "en"},
        name="dziekujemy_za_zgloszenie",
    ),
    re_path(
        r"^zgloszenie_formularz/(?P<token>.{16})/$",
        www_views.dziekujemy_za_zgloszenie,
        kwargs={"conversion": False, "language": "pl"},
        name="zgloszenie_formularz",
    ),
    re_path(
        r"^en/order_form/(?P<token>.{16})/$",
        www_views.dziekujemy_za_zgloszenie,
        kwargs={"conversion": False, "language": "en"},
        name="zgloszenie_formularz",
    ),
    re_path(
        r"^zgloszenie_barcode/(?P<id>\d+)/(?P<token>.{16})/$",
        www_views.zgloszenie_barcode,
        name="zgloszenie_barcode",
    ),
    re_path(
        r"^en/suggest_schedule/(?P<slug>[^\/]+)/$",
        www_views.zaproponuj_termin,
        kwargs={"language": "en"},
        name="zaproponuj_termin",
    ),
    re_path(
        r"^zaproponuj_termin/(?P<slug>[^\/]+)/$",
        www_views.zaproponuj_termin,
        kwargs={"language": "pl"},
        name="zaproponuj_termin",
    ),
    re_path(
        r"^dziekujemy_za_propozycje_terminu/$",
        www_views.dziekujemy_za_propozycje_terminu,
        kwargs={"language": "pl"},
        name="dziekujemy_za_propozycje_terminu",
    ),
    re_path(
        r"^en/thank_you_for_schedule_proposal$",
        www_views.dziekujemy_za_propozycje_terminu,
        kwargs={"language": "en"},
        name="dziekujemy_za_propozycje_terminu",
    ),
    re_path(
        r"^en/tech/(?P<technologia_slug>[^\/]+)/$",
        www_views.index_by_technologia,
        kwargs={"language": "en"},
        name="index_by_technologia",
    ),
    re_path(
        r"^en/tech/(?P<technologia_slug>[^\/]+)/(?P<tab_slug>[a-zA-Z0-9\-_.]+)/$",
        www_views.index_by_technologia,
        kwargs={"language": "en"},
    ),

    # Explicit Polish-prefixed tech routes to avoid locale redirect loops
    re_path(
        r"^pl/tech/(?P<technologia_slug>[^\/]+)/$",
        www_views.index_by_technologia,
        {
            "language": "pl",
        },
        name="index_by_technologia_pl_prefixed",
    ),
    re_path(
        r"^pl/tech/(?P<technologia_slug>[^\/]+)/(?P<tab_slug>[a-zA-Z0-9\-_.]+)/$",
        www_views.index_by_technologia,
        {
            "language": "pl",
        },
        name="index_by_technologia_pl_tab_prefixed",
    ),
    re_path(
        r"^tech/(?P<technologia_slug>[^\/]+)/$",
        www_views.index_by_technologia,
        {
            "language": "pl",
        },
        name="index_by_technologia_pl",
    ),
    re_path(
        r"^tech/(?P<technologia_slug>[^\/]+)/(?P<tab_slug>[a-zA-Z0-9\-_.]+)/$",
        www_views.index_by_technologia,
        {
            "language": "pl",
        },
    ),
    path("windykacja/", www_views.windykacja, name="windykacja"),
    path("niespojnosc-rat/", www_views.niespojnosc_rat, name="niespojnosc_rat"),
    re_path(r"^rss/(?:(?P<tech>[^\/]+)/)?$", www_views.feed, name="feed"),
    path("wykladowcy/", www_views.wykladowcy, name="wykladowcy"),
    re_path(
        r"^uploads/(?P<path>referencje/.*)",
        www_views.uploads_referencje,
        name="uploads_referencje",
    ),
    re_path(r"^uploads/(?P<path>certyfikaty/.*)", www_views.uploads_certyfikaty),
    re_path(r"^uploads/(?P<path>highlights/.*)", www_views.uploads_highlights),
    re_path(r"^uploads/(?P<path>testimoniale/.*)", www_views.uploads_testimoniale),
    path("stats/", www_views.stats, name="stats"),
    path("sciezki/", www_views.sciezki),
    re_path(
        r"^sciezki/(?P<slug>[^\/]+)/$", www_views.sciezki_detail, name="sciezki_detail"
    ),
    re_path(
        r"^lista_obecnosci/(?P<termin_id>\d+)/(?P<z_obiadami>[0|1])/$",
        www_views.lista_obecnosci,
        name="lista_obecnosci",
    ),
    re_path(
        r"^wykladowcy/zestawienie/(?P<prowadzacy_id>\d+)/$",
        www_views.wykladowca_zestawienie,
        name="wykladowca_zestawienie",
    ),
    # E-certyfikaty
    # tmp uwaga w celu zachowania kompatybilnosci stary format uuid (bez myslnikow)
    re_path(
        r"^certyfikat/zarzadzaj/(?P<slug>[a-zA-Z0-9-_]+)/(?P<key>[a-zA-Z0-9]{32})/$",
        www_views.certificate,
        kwargs={"language": "pl"},
        name="certificate",
    ),
    re_path(
        r"^en/certificate/manage/(?P<slug>[a-zA-Z0-9-_]+)/(?P<key>[a-zA-Z0-9]{32})/$",
        www_views.certificate,
        kwargs={"language": "en"},
        name="certificate",
    ),
    re_path(
        r"^certyfikat/(?P<slug>[a-zA-Z0-9-_]+)/(?P<public_key>[a-zA-Z0-9]{32})/$",
        www_views.public_certificate,
        kwargs={"language": "pl"},
        name="public_certificate",
    ),
    re_path(
        r"^en/certificate/(?P<slug>[a-zA-Z0-9-_]+)/(?P<public_key>[a-zA-Z0-9]{32})/$",
        www_views.public_certificate,
        kwargs={"language": "en"},
        name="public_certificate",
    ),  # tmp zostaje do sprawdzenia czy potrzeba
    re_path(
        r"^certificate/(?P<slug>[a-zA-Z0-9-_]+)/(?P<key>[a-zA-Z0-9]{32})/pdf/$",
        www_views.get_certificate_pdf,
        name="get_certificate_pdf",
    ),
    # wypis
    path(
        "mailing/wypiszmnie/",
        www_views.cancel_all_subscriptions,
        kwargs={"language": "pl"},
        name="cancel_all_subscriptions",
    ),
    path(
        "en/mailing/unsubscribeme/",
        www_views.cancel_all_subscriptions,
        kwargs={"language": "en"},
        name="cancel_all_subscriptions",
    ),
    re_path(
        r"^mailing/wypiszmnie/(?P<token>[a-zA-Z0-9:.=_\-]{1,300})/$",
        www_views.do_cancel_all_subscriptions,
        kwargs={"language": "pl"},
        name="do_cancel_all_subscriptions",
    ),
    re_path(
        r"^en/mailing/unsubscribeme/(?P<token>[a-zA-Z0-9:.=_\-]{1,300})/$",
        www_views.do_cancel_all_subscriptions,
        kwargs={"language": "en"},
        name="do_cancel_all_subscriptions",
    ),
    # Kontynuacje
    re_path(
        r"^kontynuacje/anuluj/(?P<token>[a-zA-Z0-9:.=_\-]{1,300})/$",
        www_views.cancel_continuation,
        kwargs={"language": "pl"},
        name="cancel_continuation",
    ),
    re_path(
        r"^en/continuations/cancel/(?P<token>[a-zA-Z0-9:.=_\-]{1,300})/$",
        www_views.cancel_continuation,
        kwargs={"language": "en"},
        name="cancel_continuation",
    ),
    # Notyfikacje
    re_path(
        r"^subskrypcja/anuluj/(?P<key>[a-zA-Z0-9]{32})/$",
        www_views.cancel_subscription,
        kwargs={"language": "pl"},
        name="cancel_subscription",
    ),
    re_path(
        r"^en/subscription/cancel/(?P<key>[a-zA-Z0-9]{32})/$",
        www_views.cancel_subscription,
        kwargs={"language": "en"},
        name="cancel_subscription",
    ),
    re_path(
        r"^subskrypcja/aktywuj/(?P<key>[a-zA-Z0-9]{32})/$",
        www_views.activate_subscription,
        kwargs={"language": "pl"},
        name="activate_subscription",
    ),
    re_path(
        r"^en/subscription/activate/(?P<key>[a-zA-Z0-9]{32})/$",
        www_views.activate_subscription,
        kwargs={"language": "en"},
        name="activate_subscription",
    ),
    re_path(
        r"^subskrypcja/nowa/(?P<training_id>[0-9]+)/$",
        www_views.subscription_form,
        kwargs={"language": "pl"},
        name="subscription_form",
    ),
    re_path(
        r"^en/subscription/new/(?P<training_id>[0-9]+)/$",
        www_views.subscription_form,
        kwargs={"language": "en"},
        name="subscription_form",
    ),
    re_path(
        r"^subskrypcja/zarzadzaj/(?P<key>[a-zA-Z0-9]{32})/$",
        www_views.manage_subscriptions,
        kwargs={"language": "pl"},
        name="manage_subscriptions",
    ),
    re_path(
        r"^en/subscription/manage/(?P<key>[a-zA-Z0-9]{32})/$",
        www_views.manage_subscriptions,
        kwargs={"language": "en"},
        name="manage_subscriptions",
    ),
    re_path(
        r"^postivo/callback/(?P<key>[a-zA-Z0-9]{32})/$",
        www_views.postivo_callback,
        name="postivo_callback",
    ),
    re_path(
        r"^rodo/(?P<activation_type>notification-and-newsletter|notification|newsletter){1}/$",
        www_views.rodo,
        name="rodo",
    ),
    re_path(
        r"^(?P<language>pl|en)/(?P<slug>[^\/]+)/(?P<tab_slug>[a-zA-Z0-9\-_.]+)/$",
        www_views.my_flat_page
    ),
    re_path(
        r"^(?P<language>pl|en)/(?P<slug>[^\/]+)/$",
        www_views.my_flat_page,
        name="my_flat_page",
    ),
    re_path(r"^pl/(?P<slug>[^\/]+)/$", www_views.my_flat_page),
    path('admin/prowadzacy-autocomplete/', prowadzacy_autocomplete, name='prowadzacy-autocomplete'),

]
