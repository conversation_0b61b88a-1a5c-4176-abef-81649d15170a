from django.core.exceptions import ValidationError
from django.core.validators import validate_email
from django.utils.deconstruct import deconstructible
from workalendar.europe import Poland

from www.kalendarze import parse_date

from .utils import extract_variables, render_template

__all__ = (
    "TextVariablesValidator",
    "DjangoTemplateValidator",
    "GoogleCalendarIDValidator",
)


@deconstructible
class TextVariablesValidator(object):
    """
    Walidator odpowiada za sprawdzenie, czy wszystkie wymaganae i/lub dozwolone
    zmienne znalazły się w tekście.

    Zmienne normalizowane są do małych znkaów.

    Uwaga: obecnie ten walidator nie jest wykorzystywany.
    """

    def __init__(self, allowed_vars, required_vars=None):
        self.allowed_vars = [v.lower() for v in allowed_vars]
        self.required_vars = [v.lower() for v in required_vars] if required_vars else []

    def __call__(self, value):
        if not value:
            return

        variables = extract_variables(value)

        # Przypadek, gdy zdefiniowaliśmy wymagane zmienne, a w tekście nie ma
        # żadnej.
        if not variables and self.required_vars:
            raise ValidationError(
                "Musisz wprowadzić wymagane zmienne: %(variables)s."
                % {
                    "variables": ", ".join(self.required_vars),
                },
                code="no_variables",
            )

        # Przypadek, gdy zdefiniowaliśmy wymagane zmienne, a w tekście którejś
        # brakuje.
        for v in self.required_vars:
            if v not in variables:
                raise ValidationError(
                    "Musisz wprowadzić wymaganą zmienną: %(variable)s."
                    % {
                        "variable": v,
                    },
                    code="no_variable",
                )

        # Przypadek, gdy w tekście są zmienne, które nie są dopuszczone do
        # użytku.
        for v in variables:
            if v not in self.allowed_vars:
                raise ValidationError(
                    "Zmienna %(variable)s jest niedozwolona."
                    % {
                        "variable": v,
                    },
                    code="variable_not_allowed",
                )

    def __eq__(self, other):
        return (
            self.allowed_vars == other.allowed_vars
            and self.required_vars == other.required_vars
        )


@deconstructible
class DjangoTemplateValidator(object):
    """
    Walidator odpowiada za sprawdzenie, czy podany tekst jest składniowo
    zgodny z systemem szablonów Django.
    """

    def __call__(self, value):
        if not value:
            return

        try:
            render_template(value, {})
        except Exception as err:
            raise ValidationError(
                "Błąd w składni szablonu. Treść błędu: %(error)s"
                % {
                    "error": err,
                },
                code="invalid",
            )

    def __eq__(self, other):
        return True


@deconstructible
class GoogleCalendarIDValidator(object):
    """
    Walidator odpowiada za sprawdzenie, czy podane ID kalendarza Google jest
    poprawne. Dopuszczalne formaty to:

    1. <EMAIL>
    2. http://www.google.com/calendar/feeds/<EMAIL>/private-salt/basic

    """

    def __call__(self, value):
        if not value:
            return

        # Najpierw sprawdzamy, czy jest to URL - zakładamy, że zawsze zaczyna
        # się od http://www.google.com/calendar/feeds/
        try:
            parts = value.split("/")
            calendar_id = parts[5]
        except IndexError:
            calendar_id = value

        # Teraz sprawdzamy poprawność ID - format adresu email.as
        try:
            validate_email(calendar_id)
        except:
            raise ValidationError(
                "ID kalendarza powinno mieć postać adresu email lub "
                "adresu url (http://www.google.com/calendar/feeds/...)",
                code="invalid",
            )

    def __eq__(self, other):
        return True


@deconstructible
class MultiDatesValidator(object):
    """
    Waliduje daty podane w ciągu data1,data2,...
    """

    def __call__(self, value):
        if not value:
            return

        cal = Poland()

        dates = value.split(",")

        parsed_dates = []

        for date in dates:
            try:
                dt = parse_date(date)
                parsed_dates.append(dt)
            except Exception:
                raise ValidationError(
                    "Niepoprawny format daty: {0}.".format(date), code="invalid"
                )

            if not cal.is_working_day(dt):
                raise ValidationError(
                    "Dzień {} jest dniem wolnym od pracy.".format(dt.isoformat()),
                    code="invalid",
                )

        if len(list(set(parsed_dates))) != len(dates):
            raise ValidationError("Usuń duplikaty dat.", code="invalid")

    def __eq__(self, other):
        return True
