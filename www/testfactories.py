import datetime
from decimal import Decimal

import factory
from faker import Faker

from alx_auth.factories import UserFactory
from ankiety import models as ankiety_models
from i18n import models as i18n_models
from newsletter import models as newsletter_models
from www import models, testdata

fake = Faker(["pl_PL"])


##############
# i18n
##############


class WalutaFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = i18n_models.Waluta

    nazwa = factory.Sequence(lambda n: "srebrniki{0}".format(n))
    symbol = factory.Sequence(lambda n: "SRB{0}".format(n))
    kurs_kupna = 1
    zaliczka = Decimal("406.50")

    @classmethod
    def create_once(cls, **kwargs):
        """
        Funkcja tworzy wpisy w tabeli dla walut.

        1 | "euro"           | "EUR" | 4.1000 | 1.00 | 200.00
        2 | "funt szterling" | "£"   | 4.9000 | 2.50 | 200.00
        3 | "złoty"          | "PLN" | 1.0000 | 1.00 | 406.50
        """

        try:
            i18n_models.Waluta.objects.get(symbol="EUR")
        except i18n_models.Waluta.DoesNotExist:
            cls.create(
                nazwa="euro",
                symbol="EUR",
                kurs_kupna=Decimal("4.1"),
                mnoznik_ceny=Decimal("1.0"),
                zaliczka=Decimal("200.0"),
            )

        try:
            i18n_models.Waluta.objects.get(symbol="£")
        except i18n_models.Waluta.DoesNotExist:
            cls.create(
                nazwa="funt szterling",
                symbol="£",
                kurs_kupna=Decimal("4.9"),
                mnoznik_ceny=Decimal("2.5"),
                zaliczka=Decimal("200.0"),
            )

        try:
            i18n_models.Waluta.objects.get(symbol="PLN")
        except i18n_models.Waluta.DoesNotExist:
            cls.create(
                nazwa="złoty",
                symbol="PLN",
                kurs_kupna=Decimal("1.0"),
                mnoznik_ceny=Decimal("1.0"),
                zaliczka=Decimal("406.5"),
            )


class PanstwoFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = i18n_models.Panstwo
        django_get_or_create = ("nazwa",)

    nazwa = factory.Sequence(lambda n: "panstwo{0}".format(n))

    @classmethod
    def create_once(cls, **kwargs):
        """
        Funkcja tworzy dwa wpisy w tabeli dla Polski i Wielkiej Brytanii.

        1 | "Polska"
        2 | "Wielka Brytania"
        """

        cls.create(nazwa="Polska")
        cls.create(nazwa="Wielka Brytania")


class WersjaJezykowaFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = i18n_models.WersjaJezykowa

    nazwa = factory.Sequence(lambda n: "panstwo{0}".format(n))

    @classmethod
    def create_once(cls, **kwargs):
        """
        Funkcja tworzy dwa wpisy w tabeli dla jezyka polskiego i
        angielskiego.

        1 | "polska"    | "pl" | 0.2300
        2 | "angielska" | "en" |
        """

        try:
            i18n_models.WersjaJezykowa.objects.get(kod_jezyka="pl")
        except i18n_models.WersjaJezykowa.DoesNotExist:
            obj = cls.create(
                nazwa="polska", kod_jezyka="pl", stawka_vat=Decimal("0.23")
            )
            obj.wyswietlane_panstwa = list(
                i18n_models.Panstwo.objects.filter(
                    nazwa__in=["Polska", "Wielka Brytania"]
                )
            )
            obj.save()

        try:
            i18n_models.WersjaJezykowa.objects.get(kod_jezyka="en")
        except i18n_models.WersjaJezykowa.DoesNotExist:
            obj = cls.create(nazwa="angielska", kod_jezyka="en")
            obj.wyswietlane_panstwa = list(
                i18n_models.Panstwo.objects.filter(nazwa__in=["Wielka Brytania"])
            )
            obj.save()


##############
# Ankiety
##############


class SzablonFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = ankiety_models.Szablon

    nazwa = factory.Sequence(lambda n: "Nazwa {0}".format(n))
    opis = factory.Sequence(lambda n: "Opis {0}".format(n))


class AnkietaFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = ankiety_models.Ankieta

    czas_zakonczenia = factory.Sequence(
        lambda n: datetime.datetime.now() + datetime.timedelta(days=5)
    )
    termin_szkolenia = factory.SubFactory("www.testfactories.TerminSzkoleniaFactory")
    szablon = factory.SubFactory(SzablonFactory)
    lancuch_kontrolny = "abcd"


class PytanieFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = ankiety_models.Pytanie

    nazwa = factory.Sequence(lambda n: "Nazwa {0}".format(n))
    kolejnosc = factory.Sequence(lambda n: n)
    szablon = factory.SubFactory(SzablonFactory)
    dotyczy_prowadzacych = False


class PodpytanieFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = ankiety_models.Podpytanie

    nazwa = factory.Sequence(lambda n: "Nazwa {0}".format(n))
    kolejnosc = factory.Sequence(lambda n: n)
    pytanie = factory.SubFactory(PytanieFactory)


class OdpowiedzFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = ankiety_models.Odpowiedz

    pytanie = factory.SubFactory(PytanieFactory)
    nazwa = factory.Sequence(lambda n: "Nazwa {0}".format(n))
    kolejnosc = factory.Sequence(lambda n: n)


class LogFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = ankiety_models.Log

    ip = "127.0.0.1"
    czas = factory.Sequence(lambda n: datetime.datetime.now())
    ankieta = factory.SubFactory(AnkietaFactory)


class WyborFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = ankiety_models.Wybor

    odpowiedz = factory.SubFactory(OdpowiedzFactory)
    pytanie = factory.SubFactory(PytanieFactory)
    ankieta = factory.SubFactory(AnkietaFactory)
    log = factory.SubFactory(LogFactory)


class KomentarzFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = ankiety_models.Komentarz

    tresc = factory.Sequence(lambda n: "Treść {0}".format(n))
    pytanie = factory.SubFactory(PytanieFactory)
    ankieta = factory.SubFactory(AnkietaFactory)
    log = factory.SubFactory(LogFactory)


##############
# Newsletter
##############


class OdbiorcaFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = newsletter_models.Odbiorca

    status = "potwierdzony"
    email = factory.Sequence(lambda n: "{}@email.com".format(n))


##############
# www
##############


class MenuItemFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.MenuItem

    title = factory.Sequence(lambda n: "title{0}".format(n))
    ordering = factory.Sequence(lambda n: n)
    language = "pl"


class AutoryzacjaFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.Autoryzacja
        django_get_or_create = ("nazwa", "language")

    nazwa = factory.Sequence(lambda n: "nazwa{0}".format(n))
    opis_krotki = factory.Sequence(lambda n: "opis_krotki{0}".format(n))
    opis_dlugi = factory.Sequence(lambda n: "opis_dlugi{0}".format(n))
    language = "pl"


class TagZawodFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.TagZawod

    nazwa = factory.Sequence(lambda n: "Zawod{0}".format(n))
    slug = factory.Sequence(lambda n: "slug{0}".format(n))
    language = "pl"


class TagDlugoscFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.TagDlugosc
        django_get_or_create = ("slug",)

    nazwa = factory.Sequence(lambda n: "Nazwa {0}".format(n))
    nazwa_en = factory.Sequence(lambda n: "Nazwa EN {0}".format(n))
    slug = factory.Sequence(lambda n: "slug{0}".format(n))

    @classmethod
    def create_once(cls, **kwargs):
        """
        Funkcja tworzy dwa wpisy w tabeli dla Szkoleń i Kursów.

        1 | "Kurs zawodowy (kilkumiesięczny)" | "kurs-zawodowy" | 100
        2 | "Szkolenie"                       | "szkolenie"     | 50
        """

        try:
            models.TagDlugosc.objects.get(slug="kurs-zawodowy")
        except models.TagDlugosc.DoesNotExist:
            cls.create(
                nazwa="Kurs zawodowy (kilkumiesięczny)",
                slug="kurs-zawodowy",
                ordering=100,
            )

        try:
            models.TagDlugosc.objects.get(slug="szkolenie")
        except models.TagDlugosc.DoesNotExist:
            cls.create(nazwa="Szkolenie", slug="szkolenie", ordering=50)


class LokalizacjaFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.Lokalizacja
        django_get_or_create = ("shortname", "fullname")

    # panstwo = factory.SubFactory(PanstwoFactory)
    panstwo = factory.LazyAttribute(
        lambda x: models.Panstwo.objects.get(nazwa="Polska")
    )
    numer = factory.Sequence(lambda n: str(n + 10000))
    shortname = factory.Sequence(lambda n: "Jasiow#{}".format(str(n)))
    fullname = factory.Sequence(lambda n: "Jasiów#{}".format(str(n)))
    fullname_miejscownik = factory.Sequence(lambda n: "Jasiowie#{}".format(str(n)))
    ulica_i_numer = "ul. Dziwna 7"

    @classmethod
    def create_once(cls, **kwargs):
        """
        Funkcja tworzy domyślne lokalizacje szkoleń.
        """

        for location in (
            "Gdańsk",
            "Katowice",
            "Łódź",
            "Poznań",
            "Wrocław",
            "Kraków",
            "Warszawa",
        ):
            cls.create(shortname=location, fullname=location, reklamowana=True)

        for location in ("London",):
            cls.create(
                shortname=location,
                fullname=location,
                reklamowana=True,
                panstwo=models.Panstwo.objects.get(nazwa="Wielka Brytania"),
            )

        cls.create(
            shortname="wyjazdowe",
            fullname="wyjazdowe",
            reklamowana=False,
        )


class SalaFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.Sala

    nazwa = factory.Sequence(lambda n: "Miejscowosc#{0}".format(n))
    lokalizacja = factory.SubFactory(LokalizacjaFactory)
    sprawdzaj_konflikty = False


class ProwadzacyFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.Prowadzacy

    imie = factory.Sequence(lambda n: "Imie#{0}".format(n))
    nazwisko = factory.Sequence(lambda n: "Nazwisko#{0}".format(n))
    fotka = factory.django.ImageField(color="blue")
    pokazywac = True


class TagTechnologiaFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.TagTechnologia

    nazwa = factory.Sequence(lambda n: "Linux#{0}".format(n))
    slug = factory.Sequence(lambda n: "slug{0}".format(n))


class TagTechnologiaProwadzacyFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.TagTechnologiaProwadzacy

    prowadzacy = factory.SubFactory(ProwadzacyFactory)
    tagtechnologia = factory.SubFactory(TagTechnologiaFactory)


class HighlightFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.Highlight

    tytul = factory.Sequence(lambda n: "tytul#{0}".format(n))
    tytul_h2 = factory.Sequence(lambda n: "tytul_h2#{0}".format(n))
    tresc = factory.Sequence(lambda n: "tresc#{0}".format(n))


class HighlightPlacementFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.HighlightPlacement

    highlight = factory.SubFactory(HighlightFactory)


class SzkolenieFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.Szkolenie

    nazwa = factory.Sequence(lambda n: "Szkolenie#{0}".format(n))
    slug = factory.Sequence(lambda n: "szkolenie-{0}".format(n))
    czas_dni = 1
    language = "pl"
    cena_bazowa = Decimal("123.56")
    tag_zawod = factory.SubFactory(TagZawodFactory)
    tag_dlugosc = factory.LazyAttribute(
        lambda x: models.TagDlugosc.objects.get(slug="szkolenie")
    )
    waluta = factory.SubFactory(WalutaFactory)
    program = "program"
    kod = factory.Sequence(lambda n: "KOD_{0}".format(n))
    opis = "Opis ..."
    cel = "Cel ..."
    aktywne = True
    wiele_rat = False

    #  co najmniej do jednego testu są potrzebne tagi_technologia
    #  użycie - SzkolenieFactory(.., tagi_technologia = [jakaś lista tagów])
    @factory.post_generation
    def tagi_technologia(self, create, extracted, **kwargs):
        if not create:
            # Simple build, do nothing.
            return

        if extracted:
            # A list of groups were passed in, use them
            for tagtech in extracted:
                self.tagi_technologia.add(tagtech)
        else:
            tagtech = TagTechnologiaFactory(language="pl")
            self.tagi_technologia.add(tagtech)


class SzkolenieWariantFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.SzkolenieWariant

    szkolenie = factory.SubFactory(SzkolenieFactory)
    wariant = factory.SubFactory(SzkolenieFactory)


class TekstOTerminachFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.TekstOTerminach

    szkolenie = factory.SubFactory(SzkolenieFactory)
    lokalizacja = factory.SubFactory(LokalizacjaFactory)
    tekst = "Tekst ..."


class KursFactory(SzkolenieFactory):
    tag_dlugosc = factory.LazyAttribute(
        lambda x: models.TagDlugosc.objects.get(slug="kurs-zawodowy")
    )


class TerminSzkoleniaFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.TerminSzkolenia

    obiady = "obiady-wliczone"
    lokalizacja = factory.SubFactory(LokalizacjaFactory)
    szkolenie = factory.SubFactory(SzkolenieFactory)
    zamkniete = False
    prowadzacy = factory.SubFactory(ProwadzacyFactory)

    @classmethod
    def _adjust_kwargs(cls, **kwargs):
        if "termin" not in kwargs:
            termin = datetime.date.today() + datetime.timedelta(days=5)
            # Omijamy weekendy
            while termin.weekday() in [5, 6]:
                termin += datetime.timedelta(days=1)
            kwargs["termin"] = termin

        return kwargs

    @classmethod
    def create(cls, **kwargs):
        if "sprzet" not in kwargs:
            kwargs["sprzet"] = SprzetFactory.create_batch(1)
        return super().create(**kwargs)

    @factory.post_generation
    def sprzet(self, create, extracted, **kwargs):
        if not create:
            # Simple build, do nothing.
            return

        if extracted:
            # A list of groups were passed in, use them
            for sprzet in extracted:
                self.sprzet.add(sprzet)


class TerminZamknietySzkoleniaFactory(TerminSzkoleniaFactory):
    zamkniete = True
    prywatna_rejestracja = True
    liczba_osob_w_grupie = 5
    cena_bazowa_do_n_osob = 800
    cena_za_osobe_powyzej_n_osob = 20


class TerminSzkoleniaEnFactory(TerminSzkoleniaFactory):
    szkolenie = factory.SubFactory(SzkolenieFactory, language="en", aktywne=True)
    zamkniete = False
    lokalizacja = factory.SubFactory(LokalizacjaFactory)

    @classmethod
    def create(cls, **kwargs):
        """
        Domyślnie przypisujemy lokalizację w Wielkiej Brytanii (musi być lazy).
        """
        if "lokalizacja" not in kwargs:
            kwargs["lokalizacja"] = LokalizacjaFactory(
                panstwo=models.Panstwo.objects.get(nazwa="Wielka Brytania")
            )
        return super().create(**kwargs)


class TerminZamknietySzkoleniaEnFactory(TerminSzkoleniaEnFactory):
    zamkniete = True
    liczba_osob_w_grupie = 5
    cena_bazowa_do_n_osob = 800
    cena_za_osobe_powyzej_n_osob = 20
    prywatna_rejestracja = True


class SzkolenieZTerminemFactory(SzkolenieFactory):
    termin = factory.RelatedFactory(TerminSzkoleniaFactory)


class SprzetFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.Sprzet

    nazwa = factory.Sequence(lambda n: "Zestaw {0}".format(n))
    liczba_urzadzen = 10


class DzienSzkoleniaFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.DzienSzkolenia

    data = factory.Sequence(
        lambda n: datetime.datetime.today() + datetime.timedelta(days=n)
    )
    terminszkolenia = factory.SubFactory(TerminSzkoleniaFactory)
    prowadzacy = factory.SubFactory(ProwadzacyFactory)
    sala = factory.SubFactory(SalaFactory)

    @classmethod
    def create(cls, **kwargs):
        if "sprzet" not in kwargs:
            kwargs["sprzet"] = SprzetFactory.create_batch(1)
        return super().create(**kwargs)

    @factory.post_generation
    def sprzet(self, create, extracted, **kwargs):
        if not create:
            # Simple build, do nothing.
            return

        if extracted:
            # A list of groups were passed in, use them
            for sprzet in extracted:
                self.sprzet.add(sprzet)


class PotencjalnyChetnyFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.PotencjalnyChetny

    imie_nazwisko = "Jan Potencjalny"
    szkolenie = factory.SubFactory(SzkolenieFactory)
    wprowadzil = factory.SubFactory(UserFactory)
    firma = "Firma Potencjalnego Chetnego"


class UczestnikFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.Uczestnik

    imie_nazwisko = factory.Sequence(lambda n: "Jan Uczestnik#{0}".format(n))
    termin = factory.SubFactory(TerminSzkoleniaFactory)
    waluta = factory.SubFactory(WalutaFactory)
    email = factory.Sequence(lambda n: "jasiu#{}@somehosting.com".format(n))


class UczestnikNotificationFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.UczestnikNotification

    participant = factory.SubFactory(UczestnikFactory)
    email = factory.Sequence(lambda n: "jasiu{}@somehosting.com".format(n))
    notification_type = "rata_1_niezaplacona"
    message = factory.Sequence(lambda n: "message {}".format(n))


class GrupaZaszeregowaniaFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.GrupaZaszeregowania

    nazwa = factory.Sequence(lambda n: "GrupaZaszeregowania{0}".format(n))


class ZgloszenieFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.Zgloszenie

    termin = factory.SubFactory(TerminSzkoleniaFactory)
    waluta = factory.SubFactory(WalutaFactory)
    adres = factory.LazyFunction(lambda: fake.street_address())
    imie_nazwisko = factory.LazyFunction(lambda: fake.name())
    miejscowosc_kod = factory.LazyFunction(
        lambda: "{} {}".format(fake.city(), fake.postcode())
    )
    email = factory.LazyFunction(lambda: fake.ascii_safe_email())
    token = factory.Sequence(lambda n: "%016d" % n)
    telefon = factory.LazyFunction(lambda: fake.phone_number())
    cena = Decimal("123.56")


class ContentGroupFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.ContentGroup

    title = factory.Sequence(lambda n: "title{}".format(n))
    slug = factory.Sequence(lambda n: "slug{}".format(n))


class MyFlatPageFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.MyFlatPage
        django_get_or_create = ("slug", "language")

    header_title = factory.Sequence(lambda n: "header title{}".format(n))
    h1_title = factory.Sequence(lambda n: "h1 title{}".format(n))
    slug = factory.Sequence(lambda n: "slug{}".format(n))
    content = factory.Sequence(lambda n: "content{}".format(n))
    lead = factory.Sequence(lambda n: "lead{}".format(n))
    szkolenie = factory.SubFactory(SzkolenieFactory)
    language = "pl"

    @classmethod
    def create_once(cls, **kwargs):
        """
        Funkcja tworzy standardowe strony statyczne (głównie includowane
        w szablonach lub pobierane przez context_preprocessor).
        """

        cls.create(
            slug="frontpage",
            language="en",
            szkolenie=None,
            content=testdata.FLAT_PAGES["frontpage"]["en"],
        )

        cls.create(
            slug="frontpage",
            language="pl",
            szkolenie=None,
            content=testdata.FLAT_PAGES["frontpage"]["pl"].format(
                warszawa=models.Lokalizacja.objects.get(shortname="Warszawa").pk,
                krakow=models.Lokalizacja.objects.get(shortname="Kraków").pk,
                wroclaw=models.Lokalizacja.objects.get(shortname="Wrocław").pk,
                lodz=models.Lokalizacja.objects.get(shortname="Łódź").pk,
                poznan=models.Lokalizacja.objects.get(shortname="Poznań").pk,
                katowice=models.Lokalizacja.objects.get(shortname="Katowice").pk,
                gdansk=models.Lokalizacja.objects.get(shortname="Gdańsk").pk,
            ),
        )

        for lang in ("pl", "en"):
            cls.create(
                slug="footer",
                language=lang,
                szkolenie=None,
                content=testdata.FLAT_PAGES["footer"][lang],
            )
            cls.create(
                slug="menu",
                language=lang,
                szkolenie=None,
                content=testdata.FLAT_PAGES["menu"][lang],
            )
            cls.create(slug="administrator-linuksa", language=lang, szkolenie=None)
            cls.create(
                slug="administrator-linux-specjalistyczny",
                language=lang,
                szkolenie=None,
            )
            cls.create(slug="kurs-php-sql", language=lang, szkolenie=None)
            cls.create(slug="kurs-java-programowanie", language=lang, szkolenie=None)
            cls.create(
                slug="zaawansowany-deweloper-webowy", language=lang, szkolenie=None
            )
            cls.create(slug="www-grafika", language=lang, szkolenie=None)
            cls.create(slug="zamkniete-wyjazdowe", language=lang, szkolenie=None)
            cls.create(slug="konsultacje-include", language=lang, szkolenie=None)
            cls.create(slug="rabaty", language=lang, szkolenie=None)
            cls.create(slug="platnosc-ratalna-za-kursy", language=lang, szkolenie=None)
            cls.create(
                slug="oferta-anglojezyczna-wprowadzenie-include-en",
                language=lang,
                szkolenie=None,
            )
            cls.create(slug="legenda", language=lang, szkolenie=None)

        cls.create(
            header_title="Thank you for sending application",
            slug="thank-you-for-sending-application",
            lead="""
                <p>Thank you for sending application. We will respond in the "
                 "next 24 hours.</p>
            """,
            language="en",
            szkolenie=None,
        )
        cls.create(
            header_title="Thank you for sending form",
            slug="thank-you-for-sending-form",
            lead="""
                <p data-ga-category="contact" data-ga-action="contactus">
                Thank you for sending form.</p>
            """,
            language="en",
            szkolenie=None,
        )
        cls.create(
            header_title="Dziękujemy za wysłanie zgłoszenia",
            slug="dziekujemy-za-wyslanie-zgloszenia",
            lead="""
                <p>Dziękujemy za wysłanie zgłoszenia. Odpowiemy w ciągu "
                 "najbliższych 24 godzin.</p>
            """,
            language="pl",
            szkolenie=None,
        )
        cls.create(
            header_title="Dziękujemy za wysłanie formularza",
            slug="dziekujemy-za-wyslanie-formularza",
            lead="""
                <p data-ga-category="contact" data-ga-action="contactus">
                Dziękujemy za wysłanie formularza.
                </p>
            """,
            language="pl",
            szkolenie=None,
        )


class SiteModuleFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.SiteModule

    title = factory.Sequence(lambda n: "title{}".format(n))
    enabled = True
    language = "pl"
    pages = 1
    position = 1

    @classmethod
    def create_once(cls, **kwargs):
        """
        Funkcja tworzy standardowe obiekty SiteModule (na razie tylko
        box Kontakt).
        """

        cls.create(
            title='<a href="/pl/kontakt/">Kontakt</a>',
            content=testdata.SITE_MODULES["contact"]["pl"],
            language="pl",
        )
        cls.create(
            title='<a href="/en/contact-us/">Contact us</a>',
            content=testdata.SITE_MODULES["contact"]["en"],
            language="en",
        )

        cls.create(
            title="Lokalizacje",
            content=testdata.SITE_MODULES["location"]["pl"],
            language="pl",
            ordering=200,
        )
        cls.create(
            title="Our location",
            content=testdata.SITE_MODULES["location"]["en"],
            language="en",
            ordering=1300,
        )


class TerminSzkoleniaLogFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.TerminSzkoleniaLog

    term = factory.SubFactory(TerminSzkoleniaFactory)


class AutoresponderLogFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.AutoresponderLog

    receiver_email = factory.Sequence(lambda n: "email{}@example.com".format(n))
    email_type = "formularz_kontaktowy"


###################
# Discount codes
###################


class DiscountCodeFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.DiscountCode

    available_from = factory.LazyAttribute(lambda o: datetime.datetime.now())
    available_to = factory.LazyAttribute(
        lambda o: (o.available_from or datetime.datetime.now())
        + datetime.timedelta(days=2)
    )


###################
# Faktury
###################


class FakturaWysylkaFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.FakturaWysylka

    faktura_id = factory.Sequence(lambda n: str(n + 10000))
    faktura_numer = factory.Sequence(lambda n: "{0}/11/15".format(n + 1))

    recipient_name = "Piotr Nowak"
    recipient_address = "Złota"
    recipient_post_code = "00-234"
    recipient_city = "Warszawa"


class FakturaKorektaFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.FakturaKorekta

    faktura_id = factory.Sequence(lambda n: str(n + 10000))
    faktura_miejsce_wystawienia = "Warszawa"
    faktura_kod_pocztowy_sprzedawcy = "02-298"
    faktura_numer = factory.Sequence(lambda n: "{0}/11/15".format(n + 1))
    faktura_data_sprzedazy = factory.Sequence(
        lambda n: datetime.date.today() - datetime.timedelta(days=5)
    )
    faktura_adres_nabywcy = "ul. Złota 18a/5"
    typ_faktury = 1
    faktura_data_zaplaty = None
    faktura_nip_nabywcy = "100-200-301"
    faktura_data_wystawienia = factory.Sequence(
        lambda n: datetime.date.today() - datetime.timedelta(days=5)
    )
    faktura_nazwa_sprzedawcy = "Nazwa sprzedawcy"
    faktura_miasto_nabywcy = "Kraków"
    faktura_kod_pocztowy_nabywcy = "53-312"
    faktura_termin_platnosci = factory.Sequence(
        lambda n: datetime.date.today() + datetime.timedelta(days=5)
    )
    faktura_numer_konta = "123456789"
    faktura_nazwa_nabywcy = "Piotr Nowak"
    status_faktury = 2
    faktura_adres_sprzedawcy = "ul. Złota 34"
    faktura_miasto_sprzedawcy = "Kraków"
    faktura_typ_daty = 2
    faktura_nip_sprzedawcy = "1-1-1-1-1-1"
    faktura_data_wystawienia_korekty = factory.Sequence(lambda n: datetime.date.today())
    faktura_kolejny_numer_korekty = factory.Sequence(lambda n: str(n + 1))


###################
# Urlopy
###################


class LeaveFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.Leave

    user = factory.SubFactory(UserFactory)
    dates = "01-01-2019,03-01-2019"
    year = 2019


###################
# Absolwenci
###################


class GraduateFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.Graduate

    participant = factory.SubFactory(UczestnikFactory)
    email = factory.Sequence(lambda n: "email{0}@test.com".format(n))
    name = "Imię i nazwisko ółążźćń"
    term = factory.SubFactory(TerminSzkoleniaFactory)


###################
# Certyfikaty
###################


class CertificateGroupFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.CertificateGroup

    participant = factory.SubFactory(UczestnikFactory)
    email = factory.Sequence(lambda n: "email{0}@test.com".format(n))
    name = "Nazwa firmy"


class CertificateNoFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.CertificateNo

    date = factory.Sequence(
        lambda n: datetime.datetime.now() + datetime.timedelta(days=n)
    )
    counter = 0


###################
# Powiadomienia
###################


class UserNotificationFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.UserNotification

    email = factory.Sequence(lambda n: "email{0}@test.com".format(n))
    status = 1  # zweryfikowany
    language = "pl"
    source = "www"


class UserCoursesNotificationFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.UserCoursesNotification

    user = factory.SubFactory(UserNotificationFactory)
    training = factory.SubFactory(SzkolenieFactory)

    @factory.post_generation
    def locations(self, create, extracted, **kwargs):
        if not create:
            return

        if extracted:
            for location in extracted:
                self.locations.add(location)
        else:
            location = LokalizacjaFactory()
            self.locations.add(location)


class UserNotificationLogFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.UserNotificationLog

    user = factory.SubFactory(UserNotificationFactory)
    term = factory.SubFactory(TerminSzkoleniaFactory)
    location = factory.SubFactory(LokalizacjaFactory)


###################
# Assets
###################


class AssetFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.Asset

    file = factory.django.FileField()


###################
# Historie
###################


class GraduateStoryFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.GraduateStory

    photo = factory.django.ImageField()
    name = factory.Sequence(lambda n: "Imię {0}".format(n))
    slug = factory.Sequence(lambda n: "imie{0}".format(n))
    text1 = factory.Sequence(lambda n: "text1 {0}".format(n))
    text2 = factory.Sequence(lambda n: "text2 {0}".format(n))


###################
# Kontynuacje
###################


class ContinuationLogFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.ContinuationLog

    term = factory.SubFactory(TerminSzkoleniaFactory)
    training = factory.SubFactory(SzkolenieFactory)
    email = factory.Sequence(lambda n: "{}@email.com".format(n))
    notification_type = "first_notification"
    discount_code = factory.SubFactory(DiscountCodeFactory)
    participant = factory.SubFactory(UczestnikFactory)


class ContinuationUnsubscribedFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.ContinuationUnsubscribed

    email = factory.Sequence(lambda n: "{}@email.com".format(n))


class TerminSzkoleniaMailFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.TerminSzkoleniaMail

    term = factory.SubFactory(TerminSzkoleniaFactory)
    subject = factory.Sequence(lambda n: "subject {}".format(n))
    message = factory.Sequence(lambda n: "message {}".format(n))
    author = factory.SubFactory(UserFactory)


class TerminSzkoleniaMailUczestnikFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.TerminSzkoleniaMailUczestnik

    term_mail = factory.SubFactory(TerminSzkoleniaMailFactory)
    participant = factory.SubFactory(UczestnikFactory)


class UczestnikPlikFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.UczestnikPlik

    participant = factory.SubFactory(UczestnikFactory)
    file = factory.django.ImageField()
