{% load myfilters %}
{% load mytags %}
{% load i18n %}
{% get_current_language as LANGUAGE_CODE %}
<div class="lp-courseDates-cointainer">
    <table>
        <thead>
            <tr>
                <th>{% trans "Nazwa szkolenia" context "header" %} / {% trans "Kod" %}</th>
                <th class="lp-courseDates">{% trans "Terminy" %}</th>
                <th class="lp-courseTime">{% trans "Czas" %}</th>
                <th class="lp-coursePrice">
                    <p data-legend="{% trans "Ceny netto. Dla podmiotów publicznych istnieje możliwość zwolnienia z VAT. Dalsze informacje - patrz legenda poniżej tabeli." %}">
                        {% trans "Cena" %} <span class="lp-tooltipTriangle">TooltipTriangle</span>
                    </p>
                </th>
            </tr>
        </thead>
        <tbody>
            {% for zawod in zawod_list %}
                <tr class="lp-borderBottomBlue">
                    <td colspan="4"
                        class="lp-courseCategory"
                        {% if add_sub_id %} id="{% trans 'zawod' context 'slug' %}{{ forloop.parentloop.counter }}tech{{ forloop.counter }}"{% endif %}>
                        {{ zawod.grouper }}
                    </td>
                </tr>
                {% with zawod.list as lista_szkolen2 %}
                    {% for szkolenie in lista_szkolen2 %}
                        <tr class="lp-courseRow"
                            {% if dawaj_idy_szkoleniom %}id="szkolenie_{{ szkolenie.id }}"{% endif %}
                            id="{% trans 'kurs' context 'slug' %}-{{ k.slug }}"
                            onclick="window.location.href='{{ szkolenie.get_absolute_url }}';"
                            style="cursor: pointer;"
                        >
                            <td class="lp-courseName">
                                <a href="{{ szkolenie.get_absolute_url }}">{{ szkolenie.nazwa }}</a>
                                <p>
                                    {{ szkolenie.podtytul }}
                                </p>
                                <p>
                                    {{ szkolenie.kod }}
                                    {% if szkolenie.kod_autoryzacji %}<span>({{ szkolenie.kod_autoryzacji }})</span>{% endif %}
                                </p>
                            </td>
                            {% with terminy=szkolenie.terminy %}
                                <td>
                                    {% if terminy %}
                                        {% terms_by_location terminy as terminy_wedlug_lokalizacji %}
                                        <div class="lp-cityContainer">
                                            {% for row in terminy_wedlug_lokalizacji %}
                                                <div class="lp-cityRow clearfix">
                                                    <p class="h5">{{ row.location.shortname }}</p>
                                                    <ul>
                                                        {% for t in row.terms %}
                                                            <li>
                                                                {{ t.termin|termin_short }}
                                                                {% if pl %}
                                                                    {% if t.tryb_as_symbol == "Z" %}
                                                                        <span data-legend="{% if t.szkolenie.language == "pl" %}Tryb{% endif %} {{ t.get_tryb_display }}">({{ t.tryb_as_symbol }})<span class="lp-tooltipTriangle">TooltipTriangle</span></span>
                                                                    {% endif %}
                                                                    {% if pokaz_tekst_wolne_miejsca %}
                                                                        {% with t.tekst_wolne_miejsca_short as tekst_wolne_miejsca %}
                                                                            {% if tekst_wolne_miejsca %}<span class="warning-text">{{ tekst_wolne_miejsca|safe }}</span>{% endif %}
                                                                        {% endwith %}
                                                                    {% endif %}
                                                                {% endif %}
                                                            </li>
                                                        {% endfor %}
                                                    </ul>
                                                </div>
                                            {% endfor %}
                                        </div>
                                        {% if szkolenie.min_grupa %}
                                            {% with min_os=szkolenie.min_grupa %}
                                                <p class="lp-individualLessons"
                                                   data-legend='{% if min_os == 1 or min_os is None %}{% blocktrans %}szkolenie jest także organizowane na zamówienie (w trybie zamkniętym){% endblocktrans %}{% else %}{% blocktrans %}szkolenie jest także organizowane na zamówienie (w trybie zamkniętym) dla grup od {{ min_os }} osób{% endblocktrans %}{% endif %}'>
                                                    {% if min_os == 1 or min_os is None %}
                                                        {% blocktrans %}Na zamówienie{% endblocktrans %}
                                                    {% else %}
                                                        {% blocktrans %}Na zamówienie od {{ min_os }} os.{% endblocktrans %}
                                                    {% endif %}
                                                    <span class="lp-tooltipTriangle">TooltipTriangle</span>
                                                </p>
                                            {% endwith %}
                                        {% endif %}
                                    {% else %}
                                        {% if szkolenie.min_grupa %}
                                            {% with min_os=szkolenie.min_grupa %}
                                                <p class="lp-individualLessons"
                                                   data-legend='{% if min_os == 1 or min_os is None %}{% blocktrans %}szkolenie jest organizowane na zamówienie (w trybie zamkniętym){% endblocktrans %}{% else %}{% blocktrans %}szkolenie jest organizowane na zamówienie (w trybie zamkniętym) dla grup od {{ min_os }} osób{% endblocktrans %}{% endif %}'>
                                                    {% if min_os == 1 or min_os is None %}
                                                        {% blocktrans %}Na zamówienie{% endblocktrans %}
                                                    {% else %}
                                                        {% blocktrans %}Na zamówienie od {{ min_os }} os.{% endblocktrans %}
                                                    {% endif %}
                                                    <span class="lp-tooltipTriangle">TooltipTriangle</span>
                                                </p>
                                            {% endwith %}
                                        {% else %}
                                            <p>{% trans "tel." context "contat us" %}</p>
                                        {% endif %}
                                    {% endif %}
                                </td>
                                <td class="lp-courseTime">{{ szkolenie.liczba_dni_no_spaces|safe }}</td>
                                <td class="lp-coursePrice">
                                    <p class="lp-coursePriceNormal">
                                        {% for szkolenie in terminy|przypisane_szkolenia:szkolenie %}
                                            {% if not forloop.first %}<br />{% endif %}
                                            {% if szkolenie.cena_przed_promocja %}
                                                <span class="price-before-promotion">
                                                    {% blocktrans with s_cena_przed_promocja=szkolenie.cena_przed_promocja s_waluta_symbol=szkolenie.waluta.symbol %}
                                      {{ s_cena_przed_promocja }} <small>{{ s_waluta_symbol }}</small>
                                    {% endblocktrans %}
                                                </span>
                                                <br />
                                                <span class="promotional-price">
                                                    <strong>{{ szkolenie|cena_info_short|safe }}</strong>
                                                </span>
                                            {% else %}
                                                {% if LANGUAGE_CODE == "pl" %}
                                                <strong>{{ szkolenie|cena_info_short_with_suffix|safe }}</strong>
                                                {% else %}
                                                <strong>{{ szkolenie|cena_info_short|safe }}</strong>
                                                {% endif %}
                                            {% endif %}
                                        {% endfor %}
                                    </p>
                                </td>
                            {% endwith %}
                        </tr>
                    {% endfor %}
                {% endwith %}
            {% endfor %}
        </tbody>
    </table>
</div>
