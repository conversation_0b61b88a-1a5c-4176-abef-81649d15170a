{% load mytags %}
{% load i18n %}
{% with miasta=szkolenie.najblizsze_terminy_miastami %}
  {% if miasta %}
    <table class='course-calendar'>
      {% for m in miasta %}
        <tr>
          <th>{{ m.miasto.fullname }}:</th>
          <td class='dates-list'>
            {% if m.terminy %}
              {% for t in m.terminy %}
                <strong>{{ t.termin }}</strong> - {{ t.get_tryb_display|safe }}{{ t.uwagi_i_wolne_miejsca|safe }} {% if t.language != language %}{% language_icon t %}{% endif %}<br>
              {% endfor %}
            {% else %}
              {{ m.tot.0.tekst }}
            {% endif %}
          </td>

          {% if forloop.first %}
            <td rowspan='{{ miasta|length }}' class="zapisz-sie">
              <p><a href="{% url 'zgloszenie' slug=szkolenie.slug language=language %}" class="zapisz-sie call-to-action">{% trans "Zapisz się" %}</a></p>
            </td>
          {% endif %}
        </tr>
      {% endfor %}
    </table>
  {% endif %}
{% endwith %}

<ul class="actions">
  <li class="footer">
    {% include "www/na_zyczenie_info.html" %}
  </li>
</ul>
