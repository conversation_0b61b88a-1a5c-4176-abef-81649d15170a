{% extends "www/formularz-base.html" %}
{% load myfilters mytags %}
{% load i18n %}

{% block data_layer %}

{% endblock %}

{% block extra_head %}
    {{ block.super }}
    {% include 'www/facebook_pixel.html' with track_complete_registration=first_visit %}

    {% if upload_allowed %}
        <script src="{% staticbuster new/js/dropzone/dropzone.min.js %}"></script>
        <link rel="stylesheet" href="{% staticbuster new/js/dropzone/dropzone.min.css %}">

        <script>
            Dropzone.prototype.defaultOptions.dictDefaultMessage = "Upuść tutaj pliki, które chcesz przesłać.";
            Dropzone.prototype.defaultOptions.dictFileTooBig = "Plik jest za duży ({{filesize}}MiB). Maksymalny rozmiar: {{maxFilesize}}MiB.";
            Dropzone.prototype.defaultOptions.dictInvalidFileType = "Możesz przesłać tylko pliki graficzne (jpg, png).";
            Dropzone.prototype.defaultOptions.dictResponseError = "Serwer odpowiedzaił statusem {{statusCode}}.";
            Dropzone.prototype.defaultOptions.dictCancelUpload = "Anuluj przesyłanie";
            Dropzone.prototype.defaultOptions.dictCancelUploadConfirmation = "Czy jesteś pewien?";
            Dropzone.prototype.defaultOptions.dictMaxFilesExceeded = "Nie możesz dodać więcej plików.";

            Dropzone.options.uploadFiles = {
              paramName: "file",
              maxFilesize: 15,
              parallelUploads: 1,
              maxFiles: 2,
              acceptedFiles: ".jpg,.png"
            };
        </script>
    {% endif %}
{% endblock extra_head %}

{% block body %}

<div class="dontprint dontprintinfo">
  {% if zgloszenie.termin.szkolenie.tag_dlugosc.slug == "szkolenie" %}


     <p>{% trans "Dziękujemy za wypełnienie formularza." %}</p>

      <p>
      {% if pl %}
          Wypełnienie formularza przez internet dokonuje wstępnej rezerwacji, ważnej przez 48 godzin.
      {% endif %}

      {% if en %}
          Submitting the online application form results solely in a pre-booking. For a formal confirmation of the application, <br> it is necessary to:
      {% endif %}

      {% if zamkniete %}
          {% trans "Dla formalnego potwierdzenia zgłoszenia <strong>konieczne jest</strong> wydrukowanie niniejszego formularza, podpisanie i przesłanie jako skan albo zdjęcie e-mailem <NAME_EMAIL>." %}
      {% else %}
                {% trans "Dla formalnego potwierdzenia zgłoszenia <strong>konieczne jest</strong> wydrukowanie niniejszego formularza, podpisanie i przesłanie nam:" %}
        {% trans "faksem (22 266 06 95), lub jako skan albo zdjęcie e-mailem (<EMAIL>)." %}
      {% endif %}
      </p>

      {% if en %}
        <p>
          Submitting the online application form results solely in a pre-booking. For a formal confirmation of the application, <br> <strong>it is necessary to</strong>:
        </p>

        <ol>
          <li>
            <strong>Print</strong> the form after completing it (printing will be possible on the next screen, after pressing "send application"), <strong>sign it and send it to us</strong> as a scan or photo by e-mail (<EMAIL>).
            <br/>Please note: After submitting the signed form, you will be contacted by our office.
          </li>
          <li>
            The final confirmation of the application takes place after transferring the advance payment or the full payment for the course to our account within 7 days of submitting the application, or after signing the training contract (in the case of non-standard applications). The contract is concluded after ALX confirms receipt of the application. The condition for the proper execution of the contract by ALX is the payment by the Customer of the full fee for the course, individually agreed payment or partial payment (according to the agreed payment schedule).
          </li>
        </ol>
      {% endif %}

      <p>
      {% if pl %}
          Dalszy kontakt z naszej strony nastąpi po przesłaniu podpisanego formularza.
      {% endif %}
      </p>

      <p>
      {% trans "Link do tej strony wysłaliśmy również na podany w zgłoszeniu adres e-mail." %}
      {% if pl %}
          W razie pytań lub wątpliwości, prosimy o kontakt z naszym biurem.
      {% endif %}
      </p>

      <div class="printouter">
        <a href="#" class="print">{% trans "Wydrukuj formularz" %}</a>
      </div>

  {% else %}

      <p>{% trans "Dziękujemy za wypełnienie formularza." %}</p>
    {% if pl %}
      <p>
        Wypełnienie formularza przez Internet dokonuje jedynie wstępnej rezerwacji.
        Dla formalnego potwierdzenia zgłoszenia <strong>konieczne jest</strong>:
      </p>

      <ol>
        <li>
          <strong>Wydrukowanie</strong> niniejszej strony, <strong>podpisanie i przesłanie</strong>
          nam jako skan lub zdjęcie e-mailem (<EMAIL>), lub faksem
          (22 266 06 95).
          <br/>Uwaga: Po przesłaniu podpisanego formularza nastąpi kontakt
          z naszego biura.
        </li>
        <li>
          Ostateczne potwierdzenie zgłoszenia następuje po przelaniu na nasze konto zaliczki lub pełnej opłaty za kurs w terminie 7 dni od dokonania zgłoszenia, bądź po podpisaniu umowy szkoleniowej (w wypadku niestandardowych zgłoszeń). Umowa zawarta zostaje po potwierdzeniu przyjęcia zgłoszenia przez ALX. Warunkiem prawidłowego wykonania umowy przez ALX jest zapłata przez Klienta pełnej opłaty za kurs lub zapłaty częściowej (zgodnie z ustalonym harmonogramem płatności) lub indywidualnie ustalonej zapłaty.
        </li>
      </ol>
    {% endif %}
    {% if en %}
      <p>
        Submitting the online application form results solely in a pre-booking. For a formal confirmation of the application, <strong>it is necessary to</strong>:
      </p>

      <ol>
        <li>
          <strong>Print</strong> the form after completing it (printing will be possible on the next screen, after pressing "send application"), <strong>sign it and send it to us</strong> as a scan or photo by e-mail (<EMAIL>).
          <br/>Please note: After submitting the signed form, you will be contacted by our office.
        </li>
        <li>
          The final confirmation of the application takes place after transferring the advance payment or the full payment for the course to our account within 7 days of submitting the application, or after signing the training contract (in the case of non-standard applications). The contract is concluded after ALX confirms receipt of the application. The condition for the proper execution of the contract by ALX is the payment by the Customer of the full fee for the course, individually agreed payment or partial payment (according to the agreed payment schedule).
        </li>
      </ol>
    {% endif %}
    

      <p>
        {% trans "Link do tej strony wysłaliśmy również na podany w zgłoszeniu adres email." %}
        {% if pl %}
            W razie pytań lub wątpliwości, prosimy o kontakt z naszym biurem.
        {% endif %}
      </p>

      <div class="printouter">
        <a href="#" class="print">{% trans "Wydrukuj formularz" %}</a>
      </div>

  {% endif %}
  {% if upload_allowed %}
      <form action="{{ request.path }}" class="dropzone" id="uploadFiles" method="POST">
          {% csrf_token %}
      </form>
  {% endif %}
</div>

<img src="{% url 'zgloszenie_barcode' id=zgloszenie.id token=zgloszenie.token %}" align="right" />

{% if zamkniete %}
    <h1>{% trans "Formularz zgłoszeniowy (zamówienie) − szkolenie zamknięte" %}</h1>
{% else %}
    <h1>{% trans "Formularz zgłoszeniowy − zamówienie" %}</h1>
{% endif %}

<h2>{{ zgloszenie.termin.nazwa_szkolenia_lub_snz|striptags }} ({% trans "KOD" %}: {{ zgloszenie.termin.szkolenie.kod }})</h2>
<h2>{% trans "Termin" %}: {{ zgloszenie.termin.termin }}{% if zgloszenie.termin.termin_do %} - {{ zgloszenie.termin.termin_do }}{% endif %} ({{ zgloszenie.termin.lokalizacja.shortname }})</h2>
<h2>{% trans "Cena detaliczna" %} {{ zgloszenie|cena_info:"pokaz_brutto"|safe }}{% if zgloszenie.discount_code %}, {% trans "uwzględniono rabat w wysokości" %} {{ zgloszenie.discount_code.discount }}%{% endif %}

{% if zgloszenie.is_doplata_do_faktury %}
<br/>
<small>
  {% blocktrans %}Cena zawiera dopłatę w wysokości {{ cena_papierowej_faktury }} PLN netto za przesłanie papierowej faktury.{% endblocktrans %}
</small>
{% endif %}
</h2>

<div align="center">
  <table border="1" class="dane">
    <tr><th>{% if zgloszenie.uczestnik_wieloosobowy_ilosc_osob %}
                {% trans "Lista uczestników" %}
            {% else %}
                {% trans "Imię i nazwisko" %}
                {% if zgloszenie.osoba_do_kontaktu %}
                    {% trans "uczestnika" %}
                {% endif %}
            {% endif %}</th>
        <td>{{ zgloszenie.imie_nazwisko|linebreaksbr }}</td>
    </tr>
    {% if zgloszenie.osoba_do_kontaktu %}
      <tr><th>{% trans "Osoba do kontaktu" %}</th><td><span id="g-zgloszenie-osoba_do_kontaktu">{{ zgloszenie.osoba_do_kontaktu }}</span></td></tr>
    {% endif %}
    <tr><th>E-mail</th><td><span id="g-zgloszenie-email">{{ zgloszenie.email }}</span></td></tr>
    {% if zgloszenie.email_ksiegowosc %}
      <tr><th>{% trans "E-mail kontaktowy" %}</th><td><span id="g-zgloszenie-email_ksiegowosc">{{ zgloszenie.email_ksiegowosc }}</span></td></tr>
    {% endif %}
    <tr><th>{% trans "Telefon" %}</th><td><span id="g-zgloszenie-telefon">{{ zgloszenie.telefon }}</span></td></tr>
    <tr><th>{% trans "Adres" %}</th><td><span id="g-zgloszenie-adres">{{ zgloszenie.adres }}</span></td></tr>
    <tr><th>{% trans "Miejscowość, kod pocztowy" %}</th><td><span id="g-zgloszenie-miejscowosc_kod">{{ zgloszenie.miejscowosc_kod }}</span></td></tr>
    {% if zgloszenie.za_kurs_zaplace == 3 or zgloszenie.za_kurs_zaplace == 10 %}
        {% if zgloszenie.raty_panstwo == 'polska' %}
            <tr><th>{% trans "Numer dowodu osobistego" %}</th><td>{{ zgloszenie.raty_numer_dokumentu }}</td></tr>
            <tr><th>{% trans "PESEL" %}</th><td>{{ zgloszenie.raty_pesel }}</td></tr>
        {% endif %}
    {% endif %}
  </table>
</div>

{% if not zgloszenie.prywatny %}
  <p>{% trans "Dane do faktury:" %}</p>
  <div align="center">
    <table border="1" class="dane">
      <tr><th>{% trans "Firma" %}</th><td>{{ zgloszenie.faktura_firma }}</td></tr>
      <tr><th>{% trans "Adres" %}</th><td>{{ zgloszenie.faktura_adres }}</td></tr>
      <tr><th>{% trans "Miejscowość, kod pocztowy" %}</th><td>{{ zgloszenie.faktura_miejscowosc_kod }}</td></tr>
      <tr><th>{% trans "NIP" %}</th><td>{{ zgloszenie.faktura_nip }}</td></tr>
    </table>
  </div>
{% endif %}
{% if not zgloszenie.chce_fakture %}
  <p>{% trans "Zgadzam się na otrzymanie mailem faktury w wersji elektronicznej, nie potrzebuję papierowej." %}
  </p>
{% endif %}

{% if zgloszenie.drukowany_certyfikat %}
  <p>
    {% if zamkniete %}
        {% trans "Zamawiam certyfikat ukończenia szkolenia w wersji elektronicznej oraz papierowej." %}
    {% else %}
        {% blocktrans %}Zamawiam certyfikat ukończenia szkolenia w wersji elektronicznej oraz papierowej (dopłata {{ cena_drukowanego_certyfikatu }} PLN netto za osobę).{% endblocktrans %}
    {% endif %}
  </p>
{% else %}
    <p>{% trans "Wybieram certyfikat ukończenia szkolenia w wersji elektronicznej, nie potrzebuję papierowego." %}</p>
{% endif %}

{% if zgloszenie.podmiot_publiczny %}
  <p>{% trans "Szkolenie finansowane ze środków publicznych." %}</p>
{% endif %}

{% if zgloszenie.uczestnik_wieloosobowy_ilosc_osob %}
  <p>{% trans "Liczność grupy" %}: {{ zgloszenie.uczestnik_wieloosobowy_ilosc_osob }}</p>
{% endif %}
{% if zgloszenie.termin.obiady == 'obiady-opcjonalne' and zgloszenie.chce_obiady %}
  <p>{% blocktrans with termin_cena_obiadu=zgloszenie.termin.cena_obiadu szkolenie_waluta_symbol=zgloszenie.termin.szkolenie.waluta.symbol zgloszenie_vat_info=zgloszenie|vat_info%}
       Zamawiam obiady w cenie {{ termin_cena_obiadu }} {{ szkolenie_waluta_symbol }} {{zgloszenie_vat_info}} za obiad za osobę.
     {% endblocktrans %}
  </p>
{% endif %}

{% if zgloszenie.ile_obiadow_wegetarianskich %}
  <p>{% trans "Proszę o obiady wegetariańskie" %}{% if zgloszenie.ile_obiadow_wegetarianskich > 1 %} {% trans "dla" %} {{ zgloszenie.ile_obiadow_wegetarianskich }} {% trans "osób" %}{% else %}{% if zgloszenie.uczestnik_wieloosobowy_ilosc_osob %} {% trans "dla 1 osoby" %}{% endif %}{% endif %}.
{% endif %}

{% if zgloszenie.chce_autoryzacje %}
   {% if zgloszenie.termin.szkolenie.cena_autoryzacji %}
       <p>{% blocktrans with zts_cena_autoryzacji=zgloszenie.termin.szkolenie.cena_autoryzacji zts_waluta_symbol=zgloszenie.termin.szkolenie.waluta.symbol %}
         Zamawiam autoryzację o cenie {{zts_cena_autoryzacji }} {{ zts_waluta_symbol }} netto.
       {% endblocktrans %}</p>
   {% else %}
      <p>{{ zgloszenie.termin.szkolenie.autoryzacja.opis_krotki }}</p>
   {% endif %}
{% endif %}

{% if zgloszenie.bylem_wczesniej %}
  <p>{% trans "Byłem wcześniej u was na szkoleniu (dotyczy także innych pracowników firmy)" %}:</p>
  <p>{{ zgloszenie.bylem_na|linebreaksbr }}</p>
{% endif %}

<p>
  {% blocktrans with zgloszenie.termin.szkolenie.szkolenie_czy_kurs as zdarzenie and zgloszenie.get_za_kurs_zaplace_display as sposob %}Za {{ zdarzenie }} zapłacę: {{ sposob }}.{% endblocktrans %}
</p>

    {% if zamkniete %}
      <p>
        {% trans "Dodatkowe informacje:" %}
          </p>
            <p>
        {% blocktrans %}
            <ul>
                <li>Nagrywanie, filmowanie szkolenia, w całości lub choćby w części jest zabronione.</li>
            <li>Ze szkolenia można nieodpłatnie zrezygnować, lub przełożyć za porozumieniem stron na inny termin, najpóźniej na 14 dni przed rozpoczęciem zajęć. Szkolenie odwołane po tym terminie jest pełnopłatne.</li>
            <li>Odpowiedzialność wykonawcy z tytułu niewykonania lub nienależytego wykonania umowy jest ograniczona do wysokości wartości zamówienia.</li>
            </ul>
        {% endblocktrans %}
    {% else %}
      <p>
        {% if pl %}Akceptuję regulamin szkoleń ALX.{% else %}I accept the ALX training terms and conditions.{% endif %}
      </p>
    {% endif %}

{% if zgloszenie.uwagi_klienta %}
  <p>
    {% trans "Uwagi dodatkowe:" %}
  </p>
  <p>
    {{ zgloszenie.uwagi_klienta|escape }}
  </p>
{% endif %}

<div class="podpis">
  <table width="90%">
    <tr><td width="50%">&nbsp;</td><td width="50%" align="center">.........................................................<br/>{% trans "(podpis*)" %}</td></tr>
  </table>
</div>

<div class="fineprint">
  <ul>
  {% if zamkniete %}
      <li>
	    {% trans "Prosimy o podpisanie formularza i przesłanie go emailem <NAME_EMAIL>." %}
      </li>

  {% else %}
          {% if zgloszenie.termin.szkolenie.tag_dlugosc.slug == "szkolenie" %}
      <li>
	{% trans "Prosimy o podpisanie formularza i przesłanie go faksem na numer 22 266 06 95 lub wskanowanie i przesłanie emailem <NAME_EMAIL>." %}
      </li>
    {% else %}
      <li>
	{% blocktrans %}Prosimy pamiętać, że samo wypełnienie formularza (zarówno internetowego, jak i tradycyjnego) dokonuje jedynie rezerwacji wstępnej. Ostateczne potwierdzenie zgłoszenia następuje po przelaniu na nasze konto zaliczki lub pełnej opłaty za kurs w terminie 7 dni od dokonania zgłoszenia, bądź po podpisaniu umowy szkoleniowej (w wypadku niestandardowych zgłoszeń). Umowa zawarta zostaje po potwierdzeniu przyjęcia zgłoszenia przez ALX. Warunkiem prawidłowego wykonania umowy przez ALX jest zapłata przez Klienta pełnej opłaty za kurs lub zapłaty częściowej (zgodnie z ustalonym harmonogramem płatności) lub indywidualnie ustalonej zapłaty.{% endblocktrans %}
      </li>
    {% endif %}
  {% endif %}

    <li>
      {% trans "Opłaty prosimy kierować na konto:" %}
    <div align="center">
      {% blocktrans %}
        ALX Academy sp. z o.o., ul. Jasna 14/16A, 00-041 Warszawa<br/>
        BNP Paribas Bank Polska S.A., 93 ******** ******** ********
      {% endblocktrans %}
    </div>
    </li>
      {% if uczestnik %}
          {% if zamkniete %}
                <li>
                    {% blocktrans with kod=zgloszenie.termin.szkolenie.kod termin=zgloszenie.termin.termin nazwa=uczestnik.get_nazwa|truncatechars:32 %}W tytule przelewu prosimy podawać: nazwę firmy (lub w przypadku zgłoszenia indywidualnego imię i nazwisko osoby zgłaszającej), datę rozpoczęcia kursu, kod kursu<br>(<strong>{{ nazwa }}, {{ termin }}, {{ kod }}</strong>). Wykonanie płatności z innym tytułem może spowodować opóźnienie zaksięgowania wpłaty.{% endblocktrans %}
                </li>
              {% else %}
                  <li>
                    {% blocktrans with kod=zgloszenie.termin.szkolenie.kod termin=zgloszenie.termin.termin nazwa=uczestnik.get_nazwa|truncatechars:32 %}W tytule przelewu prosimy podawać: imię i nazwisko uczestnika (lub nazwę firmy w przypadku grupy), datę rozpoczęcia kursu, kod kursu<br>(<strong>{{ nazwa }}, {{ termin }}, {{ kod }}</strong>). Wykonanie płatności z innym tytułem może spowodować opóźnienie zaksięgowania wpłaty.{% endblocktrans %}
                  </li>
              {% endif %}
      {% endif %}
    <li>
        {% if zamkniete %}
            {% trans "Zastrzegamy sobie prawo odwołania szkolenia lub jego przełożenia (zwrot wpłaconych pieniędzy lub akceptacja nowego terminu), w przypadku zaistnienia zdarzenia losowego uniemożliwiającego realizację szkolenia." %}
        {% else %}
            {% trans "Zastrzegamy sobie prawo odwołania kursu lub jego przełożenia (zwrot wpłaconych pieniędzy lub akceptacja nowego terminu), jeśli nie zgłosi się na niego wystarczająca liczba uczestników." %}
    {% endif %}
    </li>
    {% if zgloszenie.termin.szkolenie.tag_dlugosc.slug == 'szkolenie' and zgloszenie.termin.obiady == 'obiady-opcjonalne' and not zgloszenie.chce_obiady %}
      <li>
	{% trans "Informujemy, że nie ma możliwości dokupienia obiadów w trakcie trwania szkolenia poprzez korektę naszej faktury. Podczas szkolenia, można dokonać zakupu, zapłaty i uzyskać fakturę jedynie bezpośrednio w restauracji." %}
      </li>
    {% endif %}
    <li>
      {% trans "Na wszelkie pytania chętnie odpowiemy mailem lub telefonicznie."  %}
    </li>
  </ul>
  {% if pl %}* - podpis osoby upoważnionej do składania zamówienia w imieniu firmy / przy zgłoszeniach od osób prywatnych podpis zgłaszającego.{% endif %}{% if en %}{% endif %}
</div>

<div class="footer">
  {% blocktrans %}
    ALX Academy sp. z o.o., ul. Jasna 14/16A, 00-041 Warszawa<br/>
    tel. 22 63 64 164, faks 22 266 06 95<br/>
    <i><EMAIL> http://www.alx.pl/</i>
  {% endblocktrans %}
</div>

{% if conversion and first_visit %}
<div class="dontprint" data-ga-category="application" data-ga-action="form">
{% include 'www/conversion_tracker.html' %}
</div>
{% endif %}

<div class="dontprint footer"></div>

{% endblock body %}
