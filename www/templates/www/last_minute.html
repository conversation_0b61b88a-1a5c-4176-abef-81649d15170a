{% extends "www/base.html" %}

{% load mytags %}
{% load i18n %}

{% block title %}{% trans "Last Minute" %}{% endblock %}
{% block h1 %}<h1>{% trans "Szkolenia - Last Minute" %}</h1>{% endblock h1 %}

{% block body %}

{% if last_minute %}
<table class="courses-data">
	<col class="date-section"/>
	<col class="code-section"/>
	<col class="title-section"/>
	<col class="details-section"/>
        <col class="details-section" />

        <thead>
	<tr>
		<th class="courses-date">{% trans "Termin" %}</th>
		<th><span class="course-code example">{% trans "Kod" %}</span></th>
		<th>{% trans "Nazwa szkolenia" %}</th>
		{# Na angielskiej stronie wyświetlamy język w wynikach #}
		<th class="courses-details">{% trans "Czas" %}</th>
		<th class="courses-details">{% trans "Cena" %}</th>
	</tr>
	</thead>
	<tbody>
		{% for t in last_minute %}

		<tr>
			<td class="courses-date">{{ t.termin }} <br/> <em class="city">{{ t.lokalizacja }}</em></td>
			<td><a href="{% if pl %}{{ t.szkolenie.get_absolute_url }}{% endif %}{% if en %}{{ t.szkolenie.en_or_self.get_absolute_url }}{% endif %}" class='course-code'>{% if pl %}{{ t.szkolenie.kod }}{% endif %}{% if en %}{{ t.szkolenie.en_or_self.kod }}{% endif %}{% if t.szkolenie.kod_autoryzacji %}<br/>({{ t.szkolenie.kod_autoryzacji }}){% endif %}</a></td>
			<td>
				<a href="{% if pl %}{{ t.szkolenie.get_absolute_url }}{% endif %}{% if en %}{{ t.szkolenie.en_or_self.get_absolute_url }}{% endif %}">{% if pl %}{{ t.szkolenie.nazwa }}{% endif %}{% if en %}{{ t.szkolenie.en_or_self.nazwa }}{% endif %}{% if t.language != language %} {% language_icon t.szkolenie %}{% endif %}</a>
			</td>
			<td class="courses-details">{{ t.szkolenie.czas_str }}
			{# Dla kursów polskich wyświetlamy też info o trybie. #}
			{% if pl and t.szkolenie.tag_dlugosc.slug == 'kurs-zawodowy' %}
			  <br/>{{ t.get_tryb_display }}
			{% endif %}
			</td>
			<td class="courses-details">
			  {% if t.szkolenie.cena_przed_promocja %}
			    <span class="price-before-promotion">
			    {% blocktrans with szkolenie_cena_przed_promocja=t.szkolenie.cena_przed_promocja szkolenie_waluta_symbol=t.szkolenie.waluta.symbol %}
			      {{ szkolenie_cena_przed_promocja }} {{ szkolenie_waluta_symbol }}
			    {% endblocktrans %}
			    </span>
			    <br/>
			  {% endif %}

			  <span{% if t.szkolenie.cena_przed_promocja %} class="promotional-price"{% endif %}>
			  {% blocktrans with szkolenie_cena=t.szkolenie.cena szkolenie_waluta_symbol=t.szkolenie.waluta.symbol %}
			    {{ szkolenie_cena }} {{ szkolenie_waluta_symbol }}
			  {% endblocktrans %}
			</span>
		      </td>
		</tr>

		{% endfor %}
	</tbody>

</table>
{% else %}
<h2>{% trans "Brak terminów." %}</h2>
{% endif %}

{% endblock body %}
