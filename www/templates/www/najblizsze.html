{% extends "www/base.html" %}

{% load mytags %}
{% load i18n %}

{% block title %}{% trans "Najbliższe terminy szkoleń" %}{% endblock %}
{% block h1 %}<h1>{% trans "Szkolenia - najbliższe terminy" %}</h1>{% endblock h1 %}

{% block extra_head %}
    <meta name="robots" content="noindex,follow" />
{% endblock extra_head %}

{% block body %}

<div class="filtr_najblizsze form">
	<ul class='fieldset'>
		<li class="field">
			<div class='field-option'>
				<span class="label">{% trans "Miasto:" %}</span>
				<select onchange="location.href='?lokalizacja=' + this.value + '{% if tag_dlugosc %}&amp;tag_dlugosc={{ tag_dlugosc.slug }}{% endif %}{% if tag_technologia %}&amp;tag_technologia={{ tag_technologia.slug }}{% endif %}{% if tag_zawod %}&amp;tag_zawod={{ tag_zawod.slug }}{% endif %}{% if data %}&amp;data={{ data }}{% endif%}{% if filtering_language %}&amp;language={{ filtering_language }}{% endif %}{% if filtering_tryb %}&amp;tryb={{ filtering_tryb }}{% endif %}';">
					<option value=""{% if not lokalizacja %} selected="selected"{% endif %}>{% trans "wszystkie" %}</option>
					{% for l in lokalizacje %}
						<option value="{{ l.id }}"{% if l == lokalizacja %} selected="selected"{% endif %}>{{ l.fullname }}</option>
					{% endfor %}
				</select>
			</div>

			<div class='field-option'>
				<span class='label'>{% trans "Data:" %}</span>
				<select onchange="location.href='?lokalizacja={% if lokalizacja %}{{ lokalizacja.id }}{% endif %}{% if tag_dlugosc %}&amp;tag_dlugosc={{ tag_dlugosc.slug }}{% endif %}{% if tag_technologia %}&amp;tag_technologia={{ tag_technologia.slug }}{% endif %}{% if tag_zawod %}&amp;tag_zawod={{ tag_zawod.slug }}{% endif %}&amp;data=' + this.value + '{% if filtering_language %}&amp;language={{ filtering_language }}{% endif %}{% if filtering_tryb %}&amp;tryb={{ filtering_tryb }}{% endif %}';">
					<option value=""{% if not data %} selected="selected"{% endif %}>{% trans "wszystkie" %}</option>
					{% for d in data_opcje %}
						<option value="{{ d|date:"Y-m" }}" {% if d|date:"Y-m" == data %} selected="selected"{% endif %}>{% if pl %}{{ d|date:"Y F"|lower }}{% else %}{{ d|date:"Y F" }}{% endif %}</option>
					{% endfor %}
				</select>
			</div>
		</li>
		<li class="field">
			<span class='label'>{% trans "Technologia:" %}</span>
			<select onchange="location.href='?lokalizacja={% if lokalizacja %}{{ lokalizacja.id }}{% endif %}{% if tag_dlugosc %}&amp;tag_dlugosc={{ tag_dlugosc.slug }}{% endif %}&amp;tag_technologia=' + this.value + '{% if tag_zawod %}&amp;tag_zawod={{ tag_zawod.slug }}{% endif %}{% if data %}&amp;data={{ data }}{% endif %}{% if filtering_language %}&amp;language={{ filtering_language }}{% endif %}{% if filtering_tryb %}&amp;tryb={{ filtering_tryb }}{% endif %}';">
				<option value=""{% if not tag_technologia %} selected="selected"{% endif %}>{% trans "wszystkie" %}</option>
				{% for tt in tag_technologia_opcje %}
					<option value="{{ tt.slug }}"{% if tt == tag_technologia %} selected="selected"{% endif %}>{% if pl %}{{ tt.nazwa }}{% endif %}{% if en %}{{ tt.en.nazwa }}{% endif %}</option>
				{% endfor %}
			</select>
		</li>
		<li class='field'>
			<span class='label'>{% trans "Zawód:" %}</span>
			<select onchange="location.href='?lokalizacja={% if lokalizacja %}{{ lokalizacja.id }}{% endif%}{% if tag_dlugosc %}&amp;tag_dlugosc={{ tag_dlugosc.slug }}{% endif %}{% if tag_technologia %}&amp;tag_technologia={{ tag_technologia.slug }}{% endif %}&amp;tag_zawod=' + this.value + '{% if data %}&amp;data={{ data }}{% endif %}{% if filtering_language %}&amp;language={{ filtering_language }}{% endif %}{% if filtering_tryb %}&amp;tryb={{ filtering_tryb }}{% endif %}';">
				<option value=""{% if not tag_zawod %} selected="selected"{% endif %}>{% trans "wszystkie" %}</option>
				{% for tz in tag_zawod_opcje %}
					<option value="{{ tz.slug }}" {% if tz == tag_zawod %} selected="selected"{% endif %}>{% if pl %}{{ tz.nazwa }}{% endif %}{% if en %}{{ tz.en.nazwa }}{% endif %}</option>
				{% endfor %}
			</select>
		</li>
		<li class='field'>
			<span class='label'>{% trans "Typ&nbsp;zajęć:" %}</span>
			<select onchange="location.href='?lokalizacja={% if lokalizacja %}{{ lokalizacja.id }}{% endif %}&amp;tag_dlugosc=' + this.value + '{% if tag_technologia %}&amp;tag_technologia={{ tag_technologia.slug }}{% endif %}{% if tag_zawod %}&amp;tag_zawod={{ tag_zawod.slug }}{% endif %}{% if data %}&amp;data={{ data }}{% endif %}{% if filtering_language %}&amp;language={{ filtering_language }}{% endif %}{% if filtering_tryb %}&amp;tryb={{ filtering_tryb }}{% endif %}';">
				<option value=""{% if not tag_dlugosc %} selected="selected"{% endif %}>{% trans "wszystkie" %}</option>
				{% for td in tag_dlugosc_opcje %}
					<option value="{{ td.slug }}"{% if td == tag_dlugosc %} selected="selected"{% endif %}>{% if pl %}{{ td.nazwa }}{% endif %}{% if en %}{{ td.nazwa_en }}{% endif %}</option>
				{% endfor %}
			</select>
		</li>


		<li class='field'>
			<span class='label'>{% trans "Tryb:" %}</span>
			<select onchange="location.href='?lokalizacja={% if lokalizacja %}{{ lokalizacja.id }}{% endif %}{% if tag_dlugosc %}&amp;tag_dlugosc={{ tag_dlugosc.slug }}{% endif %}{% if tag_technologia %}&amp;tag_technologia={{ tag_technologia.slug }}{% endif %}{% if tag_zawod %}&amp;tag_zawod={{ tag_zawod.slug }}{% endif %}{% if data %}&amp;data={{ data }}{% endif %}{% if filtering_language %}&amp;language={{ filtering_language }}{% endif %}&amp;tryb='+this.value;">
				<option value=""{% if not filtering_tryb %} selected="selected"{% endif %}>{% trans "wszystkie" %}</option>
				{% for tr in tryb_opcje %}
					<option value="{{ tr.0 }}"{% if tr.0 == filtering_tryb %} selected="selected"{% endif %}>{{ tr.1 }}</option>
				{% endfor %}
			</select>
		</li>

                {% if language_opcje|length > 1 %}
		  <li class='field'>
		    <span class='label'>{% trans "Język szkolenia" %}</span>
		    <select onchange="location.href='?lokalizacja={% if lokalizacja %}{{ lokalizacja.id }}{% endif %}{% if tag_dlugosc %}&amp;tag_dlugosc={{ tag_dlugosc.slug }}{% endif %}{% if tag_technologia %}&amp;tag_technologia={{ tag_technologia.slug }}{% endif %}{% if tag_zawod %}&amp;tag_zawod={{ tag_zawod.slug }}{% endif %}{% if filtering_tryb %}&amp;tryb={{ filtering_tryb }}{% endif %}{% if data %}&amp;data={{ data }}{% endif %}&amp;language=' + this.value;">
		      <option value=""{% if not filtering_language %} selected="selected"{% endif %}>{% trans "wszystkie" %}</option>
		      {% for language_opcja in language_opcje %}
		        <option value="{{ language_opcja}}"{% if filtering_language == language_opcja %} selected="selected"{% endif %}>{% if language_opcja == "en" %}{% trans "angielski" %}{% endif %}{% if language_opcja == "pl"%}{% trans "polski" %}{% endif %}</option>
		      {% endfor %}
		    </select>
		  </li>
                {% endif %}
		{% if lokalizacja or tag_dlugosc or tag_technologia or tag_zawod or data or filtering_language or filtering_tryb %}
			<li class='field control'>
				<a href="?">{% trans "wyczyść wszystkie filtry" %}</a>
			</li>
		{% endif %}

	</ul>
</div>


{% if najblizsze %}
<table class="courses-data">
	<col class="date-section"/>
	<col class="code-section"/>
	<col class="title-section"/>
	<col class="details-section"/>
        <col class="details-section" />

        <thead>
	<tr>
		<th class="courses-date">{% trans "Termin" %}</th>
		<th><span class="course-code example">{% trans "Kod" %}</span></th>
		<th>{% trans "Nazwa szkolenia" %}</th>
		{# Na angielskiej stronie wyświetlamy język w wynikach #}
		<th class="courses-details">{% trans "Czas" %}</th>
		<th class="courses-details">{% trans "Cena" %}</th>
	</tr>
	</thead>
	<tbody>
		{% for t in najblizsze %}

		<tr>
			<td class="courses-date">{{ t.termin }} <br/> <em class="city">{{ t.lokalizacja }}</em></td>
			<td><a href="{% if pl %}{{ t.szkolenie.get_absolute_url }}{% endif %}{% if en %}{{ t.szkolenie.en_or_self.get_absolute_url }}{% endif %}" class='course-code'>{% if pl %}{{ t.szkolenie.kod }}{% endif %}{% if en %}{{ t.szkolenie.en_or_self.kod }}{% endif %}{% if t.szkolenie.kod_autoryzacji %}<br/>({{ t.szkolenie.kod_autoryzacji }}){% endif %}</a></td>
			<td>
				<a href="{% if pl %}{{ t.szkolenie.get_absolute_url }}{% endif %}{% if en %}{{ t.szkolenie.en_or_self.get_absolute_url }}{% endif %}">{% if pl %}{{ t.szkolenie.nazwa }}{% endif %}{% if en %}{{ t.szkolenie.en_or_self.nazwa }}{% endif %}{% if t.language != language %} {% language_icon t.szkolenie %}{% endif %}</a>
			</td>
			<td class="courses-details">{{ t.szkolenie.czas_str }}
			{# Dla kursów polskich wyświetlamy też info o trybie. #}
			{% if pl and t.szkolenie.tag_dlugosc.slug == 'kurs-zawodowy' %}
			  <br/>{{ t.get_tryb_display }}
			{% endif %}
			</td>
			<td class="courses-details">
			  {% if t.szkolenie.cena_przed_promocja %}
			    <span class="price-before-promotion">
			    {% blocktrans with szkolenie_cena_przed_promocja=t.szkolenie.cena_przed_promocja szkolenie_waluta_symbol=t.szkolenie.waluta.symbol %}
			      {{ szkolenie_cena_przed_promocja }} {{ szkolenie_waluta_symbol }}
			    {% endblocktrans %}
			    </span>
			    <br/>
			  {% endif %}

			  <span{% if t.szkolenie.cena_przed_promocja %} class="promotional-price"{% endif %}>
			  {% blocktrans with szkolenie_cena=t.szkolenie.cena szkolenie_waluta_symbol=t.szkolenie.waluta.symbol %}
			    {{ szkolenie_cena }} {{ szkolenie_waluta_symbol }}
			  {% endblocktrans %}
			</span>
		      </td>
		</tr>

		{% endfor %}
	</tbody>

</table>
{% else %}
<h2>{% trans "Brak terminów." %}</h2>
{% endif %}

{% endblock body %}
