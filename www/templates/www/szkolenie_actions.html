{% load myfilters %}
{% load mytags %}
{% load i18n %}


{% with miasta=szkolenie.najblizsze_terminy_miastami %}
<div class="lp-newTop lp-courseTop{% if not szkolenie.top_background %} lp-newTop-background001{% endif %}"
    {% if szkolenie.top_background %} style="background-image: url('{{ szkolenie.top_background.url }}');" {% endif %}>
    <div class="lp-newTop-content clearfix">
        <div class="lp-newTop-left clearfix">
            <div class="lp-newTop-header clearfix">
                <h1><span class="lp-courseTitle">{% if szkolenie.nazwa|does_not_start_with:"warsztat" %}{% trans "Szkolenie" %}: {% endif %}{{ szkolenie.nazwa }}</span><span
                        class="lp-courseID">{{ szkolenie.kod }}</span></h1>
                {% if szkolenie.podtytul %}<h2 class="lp-subtitle">{{ szkolenie.podtytul }}</h2>{% endif %}
                {% if szkolenie.tagi_marketingowe %}
                <ul class="small-tags">
                    {% for mtag in szkolenie.tagi_marketingowe_as_list %}<li>{{ mtag }}</li>{% endfor %}
                </ul>
                {% endif %}
                {% if szkolenie.autoryzacja %}
                <p class="lp-courseDescription">
                    {{ szkolenie.nazwa_autoryzacji }}
                    {% if szkolenie.kod_autoryzacji %}({{ szkolenie.kod_autoryzacji }}){% endif %}
                </p>
                {% endif %}
                <p class="lp-noTerm{% if miasta %} lp-displayNone{% endif %}">
                    {% trans "Dostępne na zamówienie dla grup." %}
                </p>
            </div>

            {% if miasta %}

            {% czy_zastosowac_uproszczony_widok_kursow miasta as czy_uproszczony_widok %}

            {% if en or czy_uproszczony_widok %}
            <div class="lp-cityContainerLess clearfix">
                {% for m in miasta %}
                <div class="lp-cityRow clearfix">
                    <div class="box-h5"> {% if pl and m.miasto.shortname == "London" %} class="lp-londyn" {% endif %}>{{ m.miasto.shortname }}</div>
                        {% if m.terminy %}
                        <ul>
                            {% for t in m.terminy %}
                            {% uwagi_i_wolne_miejsca t as uwagi_i_wolne_miejsca_text %}
                            <li class="formatted-date">
                                <div class="main-text">
                                    <strong>{{ t.termin|termin_short }}</strong>
                                    <span> - {{ t.get_tryb_display|safe }}
                                        {{ uwagi_i_wolne_miejsca_text.uwagi|safe }}

                                        {% if t.tryb == 2 and t.opis %}
                                        ({{ t.opis|safe }})
                                        {% endif %}
                                    </span>
                                </div>
                                {% if uwagi_i_wolne_miejsca_text.wolne_miejsca %}
                                <span class="warning-text">{{ uwagi_i_wolne_miejsca_text.wolne_miejsca|safe }}</span>
                                {% endif %}
                                {% if uwagi_i_wolne_miejsca_mock_text.wolne_miejsca %}
                                <span class="warning-text">{{ uwagi_i_wolne_miejsca_text.wolne_miejsca|safe }}</span>
                                {% endif %}
                            </li>
                            {% endfor %}
                        </ul>
                        {% else %}
                        <ul>
                            <li>{{ m.tot.0.tekst }}</li>
                        </ul>
                        {% endif %}
                </div>
                {% endfor %}
            </div>
            {% else %}

            {% tabela_kursow szkolenie miasta as tabela_kursow_result %}



            <!-- terminy kursów -->
            <table class="lp-trainingCalendar clearfix">
                <thead>
                    <tr>
                        <th></th>
                        <th>
                            <strong>Terminy dzienne</strong>
                            {% if tabela_kursow_result.header.D %}{{ tabela_kursow_result.header.D|safe }}{% endif %}

                        </th>
                        <th>
                            <strong>Terminy <span class="lp-mobileHide">weekendowe (</span>zaoczne<span
                                    class="lp-mobileHide">)</span></strong>
                            {% if tabela_kursow_result.header.Z %}{{ tabela_kursow_result.header.Z|safe }}{% endif %}
                        </th>
                    </tr>
                </thead>
                {% for row in tabela_kursow_result.data %}
                <tbody>
                    {% for t in row.terms %}
                    <tr>
                        <td class="lp-trainingCity">

                            <p{% if pl and row.location.shortname == "London" %} class="lp-londyn" {% endif %}>
                                {{ row.location.shortname }}</p>
                        </td>
                        <td class="lp-trainingDaily">
                            {% if t.D %}
                            <span
                                class="date-format-full">{% if wariant_szkolenia %}{{ t.D|termin_full:"warianty" }}{% else %}{{ t.D|termin_full }}{% endif %}</span>
                            <span class="date-format-short"
                                style="display: none">{% if wariant_szkolenia %}{{ t.D|termin_full_mobile:"warianty" }}{% else %}{{ t.D|termin_full_mobile }}{% endif %}</span>

                            {% if t.D.tryb == 2 %}
                            {% if t.D.opis %}
                            <span class="lp-trainingDescription">(wieczorowy, {{ t.D.opis|safe }})</span>
                            {% else %}
                            <span class="lp-trainingDescription">(wieczorowy)</span>
                            {% endif %}
                            {% endif %}

                            {% uwagi_i_wolne_miejsca t.D as uwagi_i_wolne_miejsca_text %}

                            {% if uwagi_i_wolne_miejsca_text.uwagi and uwagi_i_wolne_miejsca_text.uwagi != tabela_kursow_result.header.D %}
                            <span class="lp-trainingDescription">{{ uwagi_i_wolne_miejsca_text.uwagi|safe }}</span>
                            {% endif %}

                            {% if uwagi_i_wolne_miejsca_text.wolne_miejsca %}
                            <span class="lp-trainingLast">{{ uwagi_i_wolne_miejsca_text.wolne_miejsca|safe }}</span>
                            {% endif %}
                            {% endif %}
                        </td>
                        <td class="lp-trainingWeekly">
                            {% if t.Z %}
                            <span
                                class="date-format-full">{% if wariant_szkolenia %}{{ t.Z|termin_full:"warianty" }}{% else %}{{ t.Z|termin_full }}{% endif %}</span>
                            <span class="date-format-short"
                                style="display: none">{% if wariant_szkolenia %}{{ t.Z|termin_full_mobile:"warianty" }}{% else %}{{ t.Z|termin_full_mobile }}{% endif %}</span>

                            {% uwagi_i_wolne_miejsca t.Z as uwagi_i_wolne_miejsca_text %}

                            {% if uwagi_i_wolne_miejsca_text.uwagi and uwagi_i_wolne_miejsca_text.uwagi != tabela_kursow_result.header.Z %}
                            <span class="lp-trainingDescription">{{ uwagi_i_wolne_miejsca_text.uwagi|safe }}</span>
                            {% endif %}

                            {% if uwagi_i_wolne_miejsca_text.wolne_miejsca %}
                            <span class="lp-trainingLast">{{ uwagi_i_wolne_miejsca_text.wolne_miejsca|safe }}</span>
                            {% endif %}
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
                {% endfor %}

            </table>
            <!-- END terminy kursów -->

            {% endif %}
            {% endif %}
            <style>
                .my-link {
                    color: white;
                    text-decoration: underline;
                }
            </style>
<script>
$(document).ready(function(){
    var rowCount = $('.lp-trainingCity').children().length;

    if(rowCount >= 12) {

        $('.lp-courseInformationWrap').html('{% trans "Żaden termin nie pasuje? " %}<a href="{% url 'zaproponuj_termin' slug=szkolenie.slug language=language %}" class="my-link">{% trans "Zaproponuj własny termin szkolenia" %}</a>');

    }
});
</script>
            <div class="lp-courseInformationWrap clearfix">
                <div class="lp-proposeTerm">
                    <strong>{% trans "Żaden termin nie pasuje?" %}</strong>
                    <a href="{% url 'zaproponuj_termin' slug=szkolenie.slug language=language %}"
                        rel="nofollow">{% trans "Zaproponuj własny termin szkolenia" %}</a>
                </div>
            </div>

        <p class="lp-courseInformation">
            {% include "www/na_zyczenie_info.html" %}
        </p>

        <div class="lp-clr"></div>
    </div>

    <div class="lp-newTop-right">

        {% ocena_szkolenia szkolenie as stars %}

        {% if stars %}
        <div class="lp-newTop-rating clearfix">
            <div class="rate">
                {{ stars.avg }}<span>/5</span> <span>({{ stars.count }})</span>
            </div>
            <div class="stars-cointaner">
                <div class="stars stars-{{ stars.css }}">Stars</div>
            </div>
        </div>
        {% endif %}

        <div class="lp-topCoursePrice{% if wariant_szkolenia %} warianty{% endif %} clearfix">
            <div class="lp-topCoursePriceRight">
                <div class="lp-info-icon">
                    <div class="lp-tooltip">
                        {% if pl %}
                        cena netto<br>
                        dla podmiotów publicznych - zwolnione z VAT
                        {% else %}
                        exempt from VAT
                        {% endif %}
                        <span class="lp-tooltip-triangle"></span>
                    </div>
                </div>
            </div>

            {% if wariant_szkolenia %}

            {% tabela_kursow_ilosc_terminow tabela_kursow_result.data as row_count %}

            {% if row_count > 7 %}
            {% with szkolenie.trenerzy as trenerzy %}
            {% if trenerzy %}

            <div class="tenerzy-sm">
                {% for t in trenerzy %}
                <div class="trener-sm">

                    {% if pl %}
                    <a href="{% url 'wykladowcy' %}#trener-{{ t.pk }}"><img src="{{ t.sylwetka_big_image_url }}"
                            alt="{{ t.tytul_imie }}"></a>
                    {% else %}
                    <img src="{{ t.sylwetka_big_image_url }}" alt="{{ t.tytul_imie }}">
                    {% endif %}

                    <div>
                        <p>{% trans "trener" %}</p>
                        <p class="name">{{ t.tytul_imie }}</p>
                        <p class="coach-tech">{{ t.tagline|safe }}
                        <p>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% endif %}
            {% endwith %}
            {% endif %}


            <div class="pakiety-sm">

                <div class="pakiet-1">
                    <p>
                        Pakiet Standard:
                    <p class="pakiet-2-opis">
                        dla tych, którzy chcą wejść do świata IT, porządnie nauczyć się programowania, wykorzystać
                        Javę w małych projektach

                    </p>

                    {% if szkolenie.cena_przed_promocja %}
                    <span
                        class="lp-topCoursePriceStandard v2">{{ szkolenie|cena_info_short:"cena_przed_promocja"|striptags }}</span>
                    <span class="lp-topCoursePricePromotion v2">{{ szkolenie|cena_info_short|safe }}</span>
                    {% else %}
                    <span class="lp-topCoursePriceStandard v2">{{ szkolenie|cena_info_short|safe }}</span>
                    {% endif %}

                    </p>
                    <span>
                        {% if pl and szkolenie.wiele_rat %}
                        lub {{ szkolenie|wysokosc_raty|safe }} <span style="text-transform: uppercase;">PLN</span>
                        <span class="wiele-rat-part">miesięcznie (<a href="/pl/raty/">5 rat</a>)</span>
                        {% else %}
                        {% blocktrans %}możliwość rozłożenia na <a href="/pl/raty/">3 raty</a>{% endblocktrans %}
                        {% endif %}
                    </span>
                    <p class="dlugosc-pakietu">{{ szkolenie.czas_godziny }} {% trans "godzin" %}
                </div>
            </div>


            <div class="pakiet-2">
                <p>
                    Pakiet XL:
                <p class="pakiet-2-opis">
                    dla tych, którzy po kursie chcą osiągnąć poziom junior developera aplikacji webowych
                </p>

                {% if wariant_szkolenia.cena_przed_promocja %}
                <span
                    class="lp-topCoursePriceStandard v2">{{ wariant_szkolenia|cena_info_short:"cena_przed_promocja"|striptags }}</span>
                <span class="lp-topCoursePricePromotion v2">{{ wariant_szkolenia|cena_info_short|safe }}</span>
                {% else %}
                <span class="lp-topCoursePriceStandard v2">{{ wariant_szkolenia|cena_info_short|safe }}</span>
                {% endif %}

                </p>
                <span>
                    {% if pl and wariant_szkolenia.wiele_rat %}
                    lub {{ wariant_szkolenia|wysokosc_raty|safe }} <span style="text-transform: uppercase;">PLN</span>
                    <span class="wiele-rat-part">miesięcznie (<a href="/pl/raty/">5 rat</a>)</span>
                    {% else %}
                    {% blocktrans %}możliwość rozłożenia na <a href="/pl/raty/">3 raty</a>{% endblocktrans %}
                    {% endif %}
                </span>
                <p class="dlugosc-pakietu">{{ wariant_szkolenia.czas_godziny }} {% trans "godzin" %}
            </div>
        </div>


        <div class="zalety-sm">

            <div class="lp-platnoscRatalna">
                <svg style="width: 14px; height: auto; margin-right:2px" xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 640 512" fill="#7d7d7d">
                    <path
                        d="M512 64v256H128V64h384m16-64H112C85.5 0 64 21.5 64 48v288c0 26.5 21.5 48 48 48h416c26.5 0 48-21.5 48-48V48c0-26.5-21.5-48-48-48zm100 416H389.5c-3 0-5.5 2.1-5.9 5.1C381.2 436.3 368 448 352 448h-64c-16 0-29.2-11.7-31.6-26.9-.5-2.9-3-5.1-5.9-5.1H12c-6.6 0-12 5.4-12 12v36c0 26.5 21.5 48 48 48h544c26.5 0 48-21.5 48-48v-36c0-6.6-5.4-12-12-12z">
                    </path>
                </svg>
                {% trans "stanowisko komputerowe w cenie" %}
            </div>
            <div class="lp-platnoscRatalna">
                <svg style="width: 14px; height: auto; margin-right:2px" xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 640 512" fill="#7d7d7d">
                    <path
                        d="M192 384h192c53 0 96-43 96-96h32c70.6 0 128-57.4 128-128S582.6 32 512 32H120c-13.3 0-24 10.7-24 24v232c0 53 43 96 96 96zM512 96c35.3 0 64 28.7 64 64s-28.7 64-64 64h-32V96h32zm47.7 384H48.3c-47.6 0-61-64-36-64h583.3c25 0 11.8 64-35.9 64z">
                    </path>
                </svg>
                {% trans "poczęstunek w cenie" %}
            </div>


            <div class="lp-platnoscRatalna">
                <svg style="width: 14px; height: auto; margin-right:2px" xmlns="http://www.w3.org/2000/svg"
                    fill="#7d7d7d" viewBox="0 0 640 512">
                    <path
                        d="M112 224c61.9 0 112-50.1 112-112S173.9 0 112 0 0 50.1 0 112s50.1 112 112 112zm0-160c26.5 0 48 21.5 48 48s-21.5 48-48 48-48-21.5-48-48 21.5-48 48-48zm224 224c-61.9 0-112 50.1-112 112s50.1 112 112 112 112-50.1 112-112-50.1-112-112-112zm0 160c-26.5 0-48-21.5-48-48s21.5-48 48-48 48 21.5 48 48-21.5 48-48 48zM392.3.2l31.6-.1c19.4-.1 30.9 21.8 19.7 37.8L77.4 501.6a23.95 23.95 0 0 1-19.6 10.2l-33.4.1c-19.5 0-30.9-21.9-19.7-37.8l368-463.7C377.2 4 384.5.2 392.3.2z" />
                </svg>
                {% trans "first minute (30+ dni do startu)" %} - 3%
            </div>


            {% if szkolenie.akredytacje.all.count %}
            <div class="lp-platnoscRatalna">
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="11.2" viewBox="0 0 3.7041666 2.9633334"
                    version="1.1" id="svg16">
                    <g id="layer1" transform="translate(0,-294.03665)">
                        <path
                            style="fill:#fcb00f;stroke:#fcb00f;stroke-width:0.2400887px;stroke-linecap:butt;stroke-linejoin:miter;stroke-opacity:1"
                            d="m 1.8227659,294.19995 c -1.6921854,1.15284 -1.72187287,1.15284 -1.6921854,1.15284 0.0296876,0 1.6921854,0.69779 1.6921854,0.69779 l 1.6328103,-0.72811 z"
                            id="path3722" />
                        <path
                            style="fill:#fcb00f;stroke:#fcb00f;stroke-width:0.26458332px;stroke-linecap:butt;stroke-linejoin:miter;stroke-opacity:1"
                            d="m 3.455576,295.32247 0.01708,1.61137 v 0 0" id="path3730" />
                        <path
                            style="fill:#fcb00f;stroke:#fcb00f;stroke-width:0.24877325px;stroke-linecap:butt;stroke-linejoin:miter;stroke-opacity:1"
                            d="m 0.59531249,295.8755 v 0.70172 l 0.30916814,0.1578 1.74135267,0.0176 0.3482938,-0.17778 0.015508,-0.69938 -1.1469093,0.5854 z"
                            id="path3734" />
                    </g>
                </svg>
                {% trans "akredytacja kuratorium oświaty" %}
            </div>
            {% endif %}

        </div>

    </div>


    {% else %}

    <div class="lp-topCoursePriceLeft{% if szkolenie.cena_przed_promocja %} lp-coursePromotion{% endif %}">

        {% if szkolenie.cena_przed_promocja %}
        <p class="lp-topCoursePriceStandard">
            {% trans "Cena standardowa" %}:
            <strong>{{ szkolenie|cena_info_short:"cena_przed_promocja"|striptags }}</strong>
        </p>
        <p class="lp-topCoursePricePromotion">
            {% trans "Promocja" %}: <strong>{{ szkolenie|cena_info_short|safe }}</strong>
        </p>
        {% else %}
        <p class="lp-topCoursePriceStandard">
            {% trans "Cena szkolenia" %}: <strong>{{ szkolenie|cena_info_short|safe }}</strong>
        </p>
        {% endif %}

        {% if not szkolenie.cena_miesieczna %}
        <p class="lp-topCourseInstallment">
            <i class="fa" aria-hidden="true"></i>
            {% if pl and szkolenie.wiele_rat %}
            <span class="raty-5-info">lub {{ szkolenie|wysokosc_raty|safe }} <span
                    style="text-transform: uppercase;">PLN</span> miesięcznie (<a href="/pl/raty/">5 rat</a>)</span>
            {% else %}
              {# {% blocktrans %}możliwość rozłożenia na <a href="/pl/raty/">3 raty</a>{% endblocktrans %} #}
            {% endif %}
        </p>
        {% endif %}



        <p class="lp-topCourseInstallment">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512" fill="#fcaf12">
                <path
                    d="M192 384h192c53 0 96-43 96-96h32c70.6 0 128-57.4 128-128S582.6 32 512 32H120c-13.3 0-24 10.7-24 24v232c0 53 43 96 96 96zM512 96c35.3 0 64 28.7 64 64s-28.7 64-64 64h-32V96h32zm47.7 384H48.3c-47.6 0-61-64-36-64h583.3c25 0 11.8 64-35.9 64z" />
            </svg>
            {% trans "poczęstunek w cenie" %}
        </p>
        <p class="lp-topCourseInstallment">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512" fill="#fcaf12">
                <path
                    d="M512 64v256H128V64h384m16-64H112C85.5 0 64 21.5 64 48v288c0 26.5 21.5 48 48 48h416c26.5 0 48-21.5 48-48V48c0-26.5-21.5-48-48-48zm100 416H389.5c-3 0-5.5 2.1-5.9 5.1C381.2 436.3 368 448 352 448h-64c-16 0-29.2-11.7-31.6-26.9-.5-2.9-3-5.1-5.9-5.1H12c-6.6 0-12 5.4-12 12v36c0 26.5 21.5 48 48 48h544c26.5 0 48-21.5 48-48v-36c0-6.6-5.4-12-12-12z" />
            </svg>
            {% trans "stanowisko komputerowe w cenie" %}
        </p>

    </div>


    {% endif %}


</div>

</div>

{% if not wariant_szkolenia and szkolenie.akredytacje.all.count %}
<div class="lp-akredytacja{% if miasta|liczba_terminow_kursu < 5 %} lp-small{% endif %}">{% trans "Akredytacja" %}</div>
{% endif %}

</div>
</div>
{% endwith %}