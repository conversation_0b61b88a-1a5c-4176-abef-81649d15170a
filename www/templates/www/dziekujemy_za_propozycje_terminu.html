{% extends "www/base.html" %}
{% load i18n %}

{% block data_layer %}

{% endblock %}

{% block h1 %}
<h1>
{% trans "Dziękujemy" %}
</h1>
{% endblock %}

{% block extra_head %}
    {{ block.super }}
    <meta name="robots" content="noindex,follow">
{% endblock extra_head %}

{% block body %}

<p class="info_thanks" data-ga-category="contact" data-ga-action="proposal">
{% trans "Dziękujemy za propozycję terminu. Skontaktujemy się celem omówienia szczegółów." %}
</p>

<form method="post" action="/newsletter/signup/" class='form form-newsletter' style="display: none;">
  {% csrf_token %}
  <div style='display: none'>
   <input type="hidden" name="email" value=""/>
   <input type="hidden" name="zrodlo_kontaktu" value="termin"/>
   <input type="hidden" name="referer" value=""/>
   <input type="hidden" name="lang" value=""/>
   <input type="hidden" name="tos" value="1"/>
  </div>
  <ul class='fieldset'>
    <li class='field field_lower form-thanks'>
      <h3 class="ok">
        {% trans "Dziękujemy za propozycję terminu" %}
      </h3>
      <p>
        {% trans "Skontaktujemy się celem omówienia szczegółów" %}
      </p>
    </li>
  </ul>
  <ul class='fieldset form-questions'>
    <li class='field_lower'>
      <strong>
        {% trans "Czy chcesz otrzymywać newsletter?" %}
      </strong>
      <p>
         {% trans "Raz w miesiącu przesyłamy informacje o nowych technologiach związanych z naszymi skoleniami, o większych zmianach w naszej ofercie oraz okresowo pojawiających się promocjach. Informujemy także o terminach kursów zawodowych w semestrach zimowym i letnim oraz sporadycznie przesyłamy artykuły, ciekawostki i zagadki." %}
      </p>
    </li>
    <li class='field field_radio'>
      <input type="radio" id="radio-newsletter_subscribe-y" name="radio-newsletter_subscribe" value="tak"/>
      <label for='radio-newsletter_subscribe-y'>
        {% trans "Tak, chcę otrzymywać newsletter." %}</label>
          <p style="margin: 10px">{% blocktrans context "newsletter" %}Wyrażam zgodę na przetwarzanie danych osobowych w postaci adresu e-mail przez ALX Academy sp. z o.o. z siedzibą w Warszawie (00-041) przy ul. Jasnej
              14/16 w celu korzystania z usługi „Newsletter ALX”.{% endblocktrans %}</p>
      <input type="radio" id="radio-newsletter_subscribe-n" name="radio-newsletter_subscribe" value="nie" checked="checked"/>
      <label for='radio-newsletter_subscribe-n'>
        {% trans "Nie, dziękuję." %}
      </label>
    </li>
      <li class='field control'>
          <p>
              <span style="font-size: 11px">{% blocktrans %}Administratorem podanego adresu e-mail jest ALX Academy sp. z o.o. z siedzibą w Warszawie (00-041) przy ul. Jasnej 14/16. Dane będą przetwarzane w celu korzystania z „Newslettera ALX” przez okres niezbędny do realizacji celu przetwarzania. Osobie, której dane dotyczą przysługuje prawo dostępu do treści swoich danych i możliwości ich poprawiania. Powyższa zgoda może być odwołana w każdym czasie, co skutkować będzie usunięciem podanego adresu e-mail z listy dystrybucyjnej usługi „Newsletter ALX”. Z subskrypcji „Newsletter ALX” można w dowolnym momencie zrezygnować, wystarczy wysłać maila na adres: <EMAIL>.{% endblocktrans %}</span>
          </p>
      </li>
    <li class='field control'>
      <input type="submit" value='{% trans "Zapisz" %}' formnovalidate />
    </li>
  </ul>
</form>

<script>
  $(function(){
    var form_newsletter = $('form.form-newsletter');
    var email = '{{ email }}';
    var referer_s = "{{ request.META.HTTP_REFERER }}";
    if (email.length > 0) {
      form_newsletter.find('input[name=email]').val('{{ email }}');
      form_newsletter.find('input[name=referer]').val(referer_s);
      form_newsletter.show();
      $('p.info_thanks').hide();
    }
    function hide_newsletter() {
      form_newsletter.find('.form-questions').hide();
      form_newsletter.find('.form-legend').hide();
      form_newsletter.find('.form-thanks').show();
    }
    form_newsletter.submit(function(){
      //deleteCookie('form_kontakt');
      if ($("#radio-newsletter_subscribe-y").is(':checked')) {
        $.ajax({
          type: form_newsletter.attr('method'),
          url: form_newsletter.attr('action'),
          data: form_newsletter.serialize(),
          success: function(data) {
            hide_newsletter();
          },
          failure: function(data) {
            form_contact.find('input[type="submit"]').removeAttr('disabled');
            $('.form-error').show();
            $(document).scrollTop(form_contact.offset().top - 150);
          }
        })
      }
      else {
        hide_newsletter();
      }
      return false;
    });
  });
</script>

{% endblock body %}



{% block extra_bottom_scripts %}

<script>
  dataLayer.push({ gtp: null });
  dataLayer.push({ gbv: null });

	dataLayer.push({
    'event': 'gbv',
	  'gbv': {
      'event_name': 'view_item',
      'value': {{ cena|default_if_none:"0.00"|floatformat:"0" }},
      'items': {
        'id': '{{ kod }}',
        'google_business_vertical': 'education'
        }
		},
	  'gtp': {
      'edu_pagetype': 'complete',
      'edu_pid': '{{ kod }}',
      'edu_totalvalue': {{ cena|default_if_none:"0.00"|floatformat:"0" }},
      'edu_plocid': 'Warszawa'
    }
	});
  dataLayer.push({
      'event': 'zaproponujTermin',
      'userEmail':'{{ email }}' ,
      'userPhone':'{{ phone }}',
      'value': {{ cena|default_if_none:"0.00"|floatformat:"0" }},
      'id': '{{ kod }}'
  });

  //console.log("szkolenie: ", "{{ kod }}", {{cena}})
</script>

{% endblock extra_bottom_scripts %}

