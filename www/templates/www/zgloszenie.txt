{% load mytags %}{{ termin.nazwa_szkolenia_lub_snz_opis|striptags }}{% if zmieniony_termin %}
[na ID: {{ termin.pk }}] {{ termin.termin }}, {{ termin.lokalizacja.shortname }}, {{ termin.get_tryb_display }}{% if termin.dodatkowe_uwagi %}, {{ termin.dodatkowe_uwagi }}{% endif %}
[z  ID: {{ zmieniony_termin.pk }}] {{ zmieniony_termin.termin }}, {{ zmieniony_termin.lokalizacja.shortname }}, {{ zmieniony_termin.get_tryb_display }}, {{ zmieniony_termin.nazwa_szkolenia_lub_snz_opis|striptags }}{% if zmieniony_termin.dodatkowe_uwagi %}, {{ zmieniony_termin.dodatkowe_uwagi }}{% endif %}; zmienione przez: {% if moderator.get_full_name %}{{ moderator.get_full_name }} {% endif %}[{{ moderator }}]
{% else %}
{{ termin.termin }}, {{ termin.lokalizacja.shortname }}, {{ termin.get_tryb_display }}{% if termin.dodatkowe_uwagi %}, {{ termin.dodatkowe_uwagi }}{% endif %}
{% if moderator %}Wprowadzone przez: {% if moderator.get_full_name %}{{ moderator.get_full_name }} {% endif %}[{{ moderator }}]{% endif %}
{% endif %}
W tej chwili w sumie zgłoszonych osób: {{ termin.ilosc_uczestnikow_rentownych_all_as_text }}{% if termin.odbylo_sie %}. Termin już potwierdzony{% if termin.jobs_state %} (potwierdzono {{ termin.jobs_state|date:'Y-m-d H:i' }}){% endif %}.{% elif termin.czy_reklamowac %}. Termin jest promowany.{% endif %}{% if uczestnik.nierentowny %} Ten uczestnik jest oznaczony jako nierentowny.{% endif %}
{% with termin.ilosc_uczestnikow_dla_faktoryzacji as ilosc_uczestnikow_dla_faktoryzacji %}{% if ilosc_uczestnikow_dla_faktoryzacji %}

Dodatkowe zgłoszenia z faktoryzacji:
{% for row in ilosc_uczestnikow_dla_faktoryzacji %}
    {{ row.szkolenie }}: {{ row.ilosc_uczestnikow }}
{% endfor %}
{% endif %}{% endwith %}

{{ site_url_pl }}/admin/www/uczestnik/{{ uczestnik.id }}/
{{ site_url_pl }}{% url 'admin:www_terminszkolenia_change' termin.id %}

Imię i nazwisko/lista osób:

{{ uczestnik.imie_nazwisko }}

Osoba do kontaktu: {{ uczestnik.osoba_do_kontaktu }}
Email: {{ uczestnik.email }}
Email do rozliczeń: {{ uczestnik.email_ksiegowosc }}
{% if url_maybe %}Strona (?): {{ url_maybe }}{% endif %}
Telefon: {{ uczestnik.telefon }}
Adres: {{ uczestnik.adres }}
Miejscowość, kod: {{ uczestnik.miejscowosc_kod }}
Uczestnik prywatny: {{ uczestnik.prywatny|yesno:"tak,nie" }}
Proszę o wystawienie papierowej faktury: {{ uczestnik.chce_fakture|yesno:"tak,nie" }}
Proszę o drukowany certyfikat: {{ uczestnik.drukowany_certyfikat|yesno:"tak,nie" }}
{% if not uczestnik.prywatny %}
Firma: {{ uczestnik.faktura_firma }}
Adres: {{ uczestnik.faktura_adres }}
Miejscowość, kod: {{ uczestnik.faktura_miejscowosc_kod }}
NIP: {{ uczestnik.faktura_nip }}
Podmiot publiczny: {{ uczestnik.podmiot_publiczny|yesno:"tak,nie" }}
Ilość osób: {{ uczestnik.uczestnik_wieloosobowy_ilosc_osob|default_if_none:"" }}
{% endif %}
{% if termin.autoryzacja_aktywna %}Autoryzacja: {{ uczestnik.chce_autoryzacje|yesno:"tak,nie" }}{% endif %}
{% if termin.obiady == 'obiady-opcjonalne' %}Zamawiam obiady: {{ uczestnik.chce_obiady }}{% endif %}
{% if termin.obiady == 'obiady-wliczone' or uczestnik.chce_obiady %}Dla ilu osób obiady wegetariańskie: {{ uczestnik.ile_obiadow_wegetarianskich|default_if_none:"brak informacji" }}{% endif %}
{% if uczestnik.chce_obiady %}Obliczona cena obiadów: {{ uczestnik.cena_obiadow }}{% endif %}
Za kurs zapłacę: {{ uczestnik.get_za_kurs_zaplace_display }}
{% if uczestnik.wiek_uczestnika %}Wiek uczestnika: {{ uczestnik.wiek_uczestnika }}{% endif %}
Byłem wcześniej: {{ uczestnik.bylem_wczesniej|yesno:"tak,nie" }}
{% if uczestnik.bylem_wczesniej %}Byłem na:
{{ uczestnik.bylem_na }}{% endif %}
{% if uczestnik.odpowiedz_na_dodatkowe_pytanie %}Odpowiedź na dodatkowe pytanie:
{{ uczestnik.odpowiedz_na_dodatkowe_pytanie }}{% endif %}
Uwagi:
{{ uczestnik.uwagi_klienta|striptags }}

Cena: {{ uczestnik.kwota_do_zaplaty }} {{ uczestnik.waluta.symbol }}, VAT: {{ uczestnik.stawka_vat|vat }}{% if uczestnik.stawka_vat > 0 %}, brutto: {{ uczestnik.kwota_do_zaplaty_brutto }} {% endif %}
{% with uczestnik.discount_code as discount_code %}{% if discount_code %}Użyto kodu rabatowego: {{ discount_code.code }} ({{ discount_code.discount|floatformat }}%{% with discount_code.get_graduate as graduate %}{% if graduate %} wygenerowany dla: {{ graduate.participant|default:"-brak danych-" }}, termin: {{ site_url_pl }}/admin/www/terminszkolenia/{{ graduate.term.pk }}/{% endif %}{% endwith %}).{% endif %}{% endwith %}


ID: {{ uczestnik.id }}
Czas: {{ uczestnik.czas_dodania }}
Użytkownik zapisany na powiadomienie: {% if user_subscription %}tak (źródło: {{  user_subscription.get_source_display }}){% else %}nie{% endif %}
Opt-out: {% if opt_out %}TAK{% else %}nie{% endif %}
{% include 'www/eager_maybe.txt' %}
{% include 'www/seen_before.txt' %}
