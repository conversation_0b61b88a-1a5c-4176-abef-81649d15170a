{% load myfilters %}
{% load mytags %}
{% load i18n %}

{% with miasta=szkolenie.najblizsze_terminy_miastami %}

<div class="bootcamp">
    <div class="lp-newTop lp-courseTop pages-header{% if not szkolenie.top_background %} lp-newTop-background001{% endif %}">
        <div class="lp-newTop-content mw-content clearfix">

            <div class="lp-newTop-left clearfix">
                <div class="lp-newTop-header clearfix">

                    {% ocena_szkolenia szkolenie as stars %}

                    {% if stars %}
                    <div class="lp-newTop-rating clearfix">
                        <div class="rate">
                            {{ stars.avg }}<span>/5</span> <span>({{ stars.count }})</span>
                        </div>
                        <div class="stars-cointaner">
                            <div class="stars stars-{{ stars.css }}">Stars</div>
                        </div>
                    </div>
                    {% endif %}

                    <span class="lp-courseID">{{ szkolenie.kod }}</span>

                    <h1><span class="lp-courseTitle">{{ szkolenie.nazwa }}</span></h1>
                    {% if szkolenie.podtytul %}<h2 class="lp-subtitle">{{ szkolenie.podtytul }}</h2>{% endif %}
                    <p class="mw-short-description">

                        {% if szkolenie.autoryzacja %}
                        {{ szkolenie.nazwa_autoryzacji }}
                        {% if szkolenie.kod_autoryzacji %}({{ szkolenie.kod_autoryzacji }}){% endif %}<br>
                        {% endif %}

                        {% if szkolenie.opis %}{{ szkolenie.opis|safe }}{% endif %}
                    </p>

                    {% trainers_included taby.0.content language as mobile_trainers %}

                    {% if mobile_trainers %}
                    {{ mobile_trainers|safe }}
                    {% endif %}

                    {% if szkolenie.tagi_marketingowe %}
                    <ul class="small-tags">
                        {% for mtag in szkolenie.tagi_marketingowe_as_list %}<li>{{ mtag }}</li>{% endfor %}
                    </ul>
                    {% endif %}

                </div>

            </div>
            <div class="lp-newTop-right">


                <div class="lp-topCoursePrice clearfix">


                    <div class="mw-right-panel">

                        <p class="mw-new-price-tag">

                            {% if szkolenie.cena_przed_promocja %}
                        <p class="lp-topCoursePriceStandard">
                            {% trans "Cena standardowa" %}:
                            <strong>{{ szkolenie|cena_info_short:True|safe }}</strong>
                        </p>
                        <p class="lp-topCoursePricePromotion">
                            {% trans "Promocja" %}: <strong>{{ szkolenie|cena_info_short|safe }}</strong>
                        </p>
                        {% else %}
                        <p class="lp-topCoursePriceStandard">
                            {% trans "Cena kursu" %}: <strong>{{ szkolenie|cena_info_short|safe }}</strong>
                        </p>
                        {% endif %}

                        <div class="lp-price-info">
                            <div class="lp-info-icon">
                                <div class="lp-tooltip">
                                    {% if pl %}
                                    dla firm - netto<br>
                                    dla osób prywatnych - brutto (pokrywamy VAT)<br>
                                    dla podmiotów publicznych - zwolnione z VAT
                                    {% else %}
                                    exempt from VAT
                                    {% endif %}
                                    <span class="lp-tooltip-triangle"></span>
                                </div>
                            </div>
                        </div>


                        {% if not szkolenie.cena_miesieczna %}
                        <p class="lp-topCourseInstallment">
                            <i class="fa" aria-hidden="true"></i>
                            {% if pl and szkolenie.wiele_rat %}
                            <span class="raty-5-info">lub {{ szkolenie|wysokosc_raty|safe }} <span
                                    style="text-transform: uppercase;">PLN</span> miesięcznie (<a href="/pl/raty/">5
                                    rat</a>)</span>
                            {% else %}
                            {% blocktrans %}możliwość rozłożenia na <a href="/pl/raty/">3 raty</a>{% endblocktrans %}
                            {% endif %}
                        </p>
                        {% endif %}

                        </p>


                        <p class="lp-topCourseInstallment">
                            <!-- <svg style="width: 12px; height: auto; margin-right: 3px; margin-bottom: -2px;" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512" fill="#fcaf12"><path d="M192 384h192c53 0 96-43 96-96h32c70.6 0 128-57.4 128-128S582.6 32 512 32H120c-13.3 0-24 10.7-24 24v232c0 53 43 96 96 96zM512 96c35.3 0 64 28.7 64 64s-28.7 64-64 64h-32V96h32zm47.7 384H48.3c-47.6 0-61-64-36-64h583.3c25 0 11.8 64-35.9 64z"></path></svg> -->
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512" fill="#fcaf12">
                                <path
                                    d="M192 384h192c53 0 96-43 96-96h32c70.6 0 128-57.4 128-128S582.6 32 512 32H120c-13.3 0-24 10.7-24 24v232c0 53 43 96 96 96zM512 96c35.3 0 64 28.7 64 64s-28.7 64-64 64h-32V96h32zm47.7 384H48.3c-47.6 0-61-64-36-64h583.3c25 0 11.8 64-35.9 64z">
                                </path>
                            </svg>
                            {% trans "poczęstunek w cenie" %}
                        </p>
                        <p class="lp-topCourseInstallment">
                            <!-- <svg style="width: 12px; height: auto; margin-right: 3px; margin-bottom: -2px;" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512" fill="#fcaf12"><path d="M512 64v256H128V64h384m16-64H112C85.5 0 64 21.5 64 48v288c0 26.5 21.5 48 48 48h416c26.5 0 48-21.5 48-48V48c0-26.5-21.5-48-48-48zm100 416H389.5c-3 0-5.5 2.1-5.9 5.1C381.2 436.3 368 448 352 448h-64c-16 0-29.2-11.7-31.6-26.9-.5-2.9-3-5.1-5.9-5.1H12c-6.6 0-12 5.4-12 12v36c0 26.5 21.5 48 48 48h544c26.5 0 48-21.5 48-48v-36c0-6.6-5.4-12-12-12z"></path></svg> -->
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512" fill="#fcaf12">
                                <path
                                    d="M512 64v256H128V64h384m16-64H112C85.5 0 64 21.5 64 48v288c0 26.5 21.5 48 48 48h416c26.5 0 48-21.5 48-48V48c0-26.5-21.5-48-48-48zm100 416H389.5c-3 0-5.5 2.1-5.9 5.1C381.2 436.3 368 448 352 448h-64c-16 0-29.2-11.7-31.6-26.9-.5-2.9-3-5.1-5.9-5.1H12c-6.6 0-12 5.4-12 12v36c0 26.5 21.5 48 48 48h544c26.5 0 48-21.5 48-48v-36c0-6.6-5.4-12-12-12z">
                                </path>
                            </svg>
                            {% trans "stanowisko komputerowe w cenie" %}
                        </p>

                        <p class="lp-topCourseInstallment">
                            <!-- <svg style="width: 12px; height: auto; margin-right: 3px; margin-bottom: -2px;"
                                xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512" fill="#fcaf12">
                                <path
                                    d="M112 224c61.9 0 112-50.1 112-112S173.9 0 112 0 0 50.1 0 112s50.1 112 112 112zm0-160c26.5 0 48 21.5 48 48s-21.5 48-48 48-48-21.5-48-48 21.5-48 48-48zm224 224c-61.9 0-112 50.1-112 112s50.1 112 112 112 112-50.1 112-112-50.1-112-112-112zm0 160c-26.5 0-48-21.5-48-48s21.5-48 48-48 48 21.5 48 48-21.5 48-48 48zM392.3.2l31.6-.1c19.4-.1 30.9 21.8 19.7 37.8L77.4 501.6a23.95 23.95 0 0 1-19.6 10.2l-33.4.1c-19.5 0-30.9-21.9-19.7-37.8l368-463.7C377.2 4 384.5.2 392.3.2z" />
                                </svg> -->
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512" fill="#fcaf12">
                                <path
                                    d="M112 224c61.9 0 112-50.1 112-112S173.9 0 112 0 0 50.1 0 112s50.1 112 112 112zm0-160c26.5 0 48 21.5 48 48s-21.5 48-48 48-48-21.5-48-48 21.5-48 48-48zm224 224c-61.9 0-112 50.1-112 112s50.1 112 112 112 112-50.1 112-112-50.1-112-112-112zm0 160c-26.5 0-48-21.5-48-48s21.5-48 48-48 48 21.5 48 48-21.5 48-48 48zM392.3.2l31.6-.1c19.4-.1 30.9 21.8 19.7 37.8L77.4 501.6a23.95 23.95 0 0 1-19.6 10.2l-33.4.1c-19.5 0-30.9-21.9-19.7-37.8l368-463.7C377.2 4 384.5.2 392.3.2z" />
                            </svg>
                            {% trans "first minute (30+ dni do startu)" %} - 3%
                        </p>

                    </div>

                </div>

            </div>


            {% if szkolenie.akredytacje.all.count %}
            <div class="lp-akredytacja{% if miasta|liczba_terminow_kursu < 5 %} lp-small{% endif %}">
                {% trans "Akredytacja" %}</div>
            {% endif %}

        </div>
    </div>
</div>
{% endwith %}