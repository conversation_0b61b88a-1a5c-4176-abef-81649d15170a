Podsumowanie wysyłki faktur pro-forma dla szkolenia <strong><a href="{{ site_url }}{% url 'admin:www_terminszkolenia_change' termin.pk %}">{{ termin.szkolenie.nazwa }}, {{ termin.lokalizacja.fullname }}, {{ termin.termin }}</a></strong><br><br>

{% for row in summary %}
    <p><strong>Uczestnik: {{ row.user.imie_nazwisko_oneline }}</strong></p>

    {% url 'admin:www_uczestnik_change' row.user.pk as link %}
    - link: <a href="{{ site_url }}{{ link }}">{{ site_url }}{{ link }}</a><br>
    - automatyczna faktura: {% if row.user.allowed_to_get_invoice %}tak{% else %}nie{% endif %}
    {% if row.user.allowed_to_get_invoice %}
        <br>
    - faktura wysłana: {% if row.invoice_sent %}tak{% else %}nie{% endif %}<br>
    - klient utworzony w API: {% if row.client_created %}tak{% else %}nie{% endif %}<br>
    - błędy API: {% if row.errors %}{{ row.errors|safe }}{% else %}brak{% endif %}
    {% endif %}
    <br><br>
{% endfor %}
