{% if termin.hybryda %}
<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, że szkolenie {{ termin.szkolenie }} w terminie {{ termin.termin }} ({{ termin.lokalizacja }}) rusza{% if faktoryzacja %} na podstawie decyzji o uruchomieniu zajęć których niniejszy kurs/szkolenie jest faktoryzacją{% endif %}

Razem hybrydowo: {{ termin.ilosc_uczestnikow_rentownych_z_terminem_zdalnym_as_text }} chętnych, min_grupa {{ termin.szkolenie.min_grupa }}
W tym:
 - {{ termin.lokalizacja }} - {{ termin.ilosc_uczestnikow_rentownych_as_text }} chętnych, bieżący status czy robimy: {{ termin.odbylo_sie|yesno:"tak,nie,nie wiadomo" }}, {{ site_url }}{% url 'admin:www_terminszkolenia_change' termin.pk %}
   U<PERSON><PERSON><PERSON><PERSON>: {% for uczestnik in termin.uczestnicy_niezrezygnowani %}{% if uczestnik.faktura_firma %}{{ uczestnik.faktura_firma }} - {% endif %}{{ uczestnik.imie_nazwisko }}{% if not forloop.last %}, {% endif %}{% endfor %}

 - {{ termin.hybryda.lokalizacja }} - {{ termin.hybryda.ilosc_uczestnikow_rentownych_as_text }} chętnych, bieżący status czy robimy: {{ termin.hybryda.odbylo_sie|yesno:"tak,nie,nie wiadomo" }}, {{ site_url }}{% url 'admin:www_terminszkolenia_change' termin.hybryda.pk %}
   Uczestnicy: {% for uczestnik in termin.hybryda.uczestnicy_niezrezygnowani %}{% if uczestnik.faktura_firma %}{{ uczestnik.faktura_firma }} - {% endif %}{{ uczestnik.imie_nazwisko }}{% if not forloop.last %}, {% endif %}{% endfor %}
{% with termin.ilosc_uczestnikow_dla_faktoryzacji as ilosc_uczestnikow_dla_faktoryzacji %}{% if ilosc_uczestnikow_dla_faktoryzacji %}

Dodatkowe zgłoszenia z faktoryzacji:
{% for row in ilosc_uczestnikow_dla_faktoryzacji %}
    {{ row.szkolenie }}: {{ row.ilosc_uczestnikow }}
{% endfor %}

{% endif %}{% endwith %}
{% else %}
Potwierdzić, że szkolenie {{ termin.szkolenie }} w terminie {{ termin.termin }} ({{ termin.lokalizacja }}) rusza{% if faktoryzacja %} na podstawie decyzji o uruchomieniu zajęć których niniejszy kurs/szkolenie jest faktoryzacją{% endif %} (jest {{ termin.ilosc_uczestnikow_rentownych_as_text }} chętnych, min_grupa {% if faktoryzacja %}(dla grupy uruchamianej samodzielnie) wynosiłaby {{ termin.szkolenie.min_grupa }}{% else %}{{ termin.szkolenie.min_grupa }}{% endif %}, bieżący status czy robimy: {{ termin.odbylo_sie|yesno:"tak,nie,nie wiadomo" }}).
{% with termin.ilosc_uczestnikow_dla_faktoryzacji as ilosc_uczestnikow_dla_faktoryzacji %}{% if ilosc_uczestnikow_dla_faktoryzacji %}

Dodatkowe zgłoszenia z faktoryzacji:
{% for row in ilosc_uczestnikow_dla_faktoryzacji %}
    {{ row.szkolenie }}: {{ row.ilosc_uczestnikow }}
{% endfor %}

{% endif %}{% endwith %}
{{ site_url }}/admin/www/terminszkolenia/{{ termin.id }}/

{% for uczestnik in termin.uczestnicy_niezrezygnowani %}{{ uczestnik.faktura_firma }} - {{ uczestnik.imie_nazwisko }}
{% endfor %}
{% endif %}

Pozdrowienia,
System ALX

{% include 'www/sig.txt' %}