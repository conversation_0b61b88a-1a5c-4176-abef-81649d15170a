{% load myfilters %}
<seqreset />
<para fontSize="16" fontName="DejaVuSansBold" alignment="left">
{{ szkolenie.nazwa }} (code: {{ szkolenie.kod }})
</para>
{% if szkolenie.autoryzacja %}
  <para fontSize="13" spaceBefore="0.2cm" alignment="left">
    {{ szkolenie.nazwa_autoryzacji }} {% if szkolenie.kod_autoryzacji %}({{ szkolenie.kod_autoryzacji }}){% endif %}
  </para>
{% endif %}
<para spaceBefore="0.5cm">
! Overview
{{ szkolenie.get_pdf_cel|pdfignore|safe }}
</para>
{% if szkolenie.autoryzacja %}
<para spaceBefore="0.5cm">
  {{ szkolenie.autoryzacja.opis_dlugi|pdfignore|safe }}
</para>
{% endif %}
<para spaceBefore="0.5cm">
! Duration
{{ szkolenie.czas_str }}
</para>
<para spaceBefore="0.5cm">
! Agenda
{{ szkolenie.program|pdfignore|safe }}
</para>
<para spaceBefore="0.5cm">
! Target audience and prerequisites
{% if szkolenie.przeznaczony_dla %}
{{ szkolenie.przeznaczony_dla|pdfignore|safe }}
{% endif %}
{% if szkolenie.wymagania %}
{{ szkolenie.wymagania|pdfignore|safe }}
{% endif %}
{% if not szkolenie.przeznaczony_dla and not szkolenie.wymagania %}
There are no prerequisites for this course.
{% endif %}
</para>
<para spaceBefore="0.5cm">
! Certificates
{{ szkolenie.certyfikaty_str|pdfignore }}
</para>
<para spaceBefore="0.5cm">
! Locations
{% for l in szkolenie.lokalizacje_list %}* {{ l }}
{% endfor %}</para>
<para spaceBefore="0.5cm">
! Price
{{ szkolenie|cena_info|safe }}

The price includes:

* course materials,{% if szkolenie.zawiera_obiady %}
* meals,{% endif %}
* snacks, coffee, tea and soft drinks,
* course completion certificate,
* one-time consultation with the instructor after course completion.

{% if szkolenie.uwagi %}
<para spaceBefore="0.5cm">
! Additional info
{{ szkolenie.uwagi|pdfignore }}
</para>
{% endif %}
