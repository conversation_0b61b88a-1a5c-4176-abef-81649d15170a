<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
</head>
<body>
<h1>{{ prowadzacy }}</h1>
<p>Dni total: {{ dni_total }}</p>
<p>Dni Office<sup>*</sup>: {{ dni_office }}</p>
<p>Dni Project: {{ dni_project }}</p>
<p>Dni Excel: {{ dni_excel }}</p>
<p>Excel szczegółowo:</p>
<table>
{% for r in excel_szczegolowo %}
<tr><td>{{ r.0 }}:</td><td>{{ r.1 }}</td></tr>
{% endfor %}
</table>
<hr>
<p><small>* - Office zawiera tagi: {% for t in office_tags %}<tt>{{ t }}</tt>{% if not forloop.last %}, {% endif %}{% endfor %}.</small></p>
</body>
</html>
