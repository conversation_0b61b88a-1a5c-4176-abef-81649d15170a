{% extends "www/base.html" %}
{% load i18n %}
{% load mytags %}
{% load myfilters %}
{% block title %}
 {% trans "Formularz zgłoszeniowy" %}
{% endblock %}
{% block h1 %}
 <h1>{% trans "Formularz zgłoszeniowy" %}</h1>
{% endblock %}
{% block extra_head %}
 {{ block.super }}
 <meta name="robots" content="noindex,follow">
{% endblock extra_head %}
{% block body %}
 <script src="{% staticbuster js/zgloszenie-zamkniete-formularz.js %}"></script>
 <div class="hidden tekst_o_fakturze">
  <strong>{% trans "UWAGA:" %}</strong>
  {% blocktrans %}Wybór faktury papierowej oznacza dopłatę do ceny szkolenia w wysokości {{ cena_papierowej_faktury }} zł netto.{% endblocktrans %}
 </div>
 <h2>
  {{ termin.nazwa_szkolenia_lub_snz|striptags }}
  <span class="subtitle">
   <strong class='course-code'>{{ szkolenie.kod }}</strong>
  </span>
 </h2>
 <p>{% trans "Termin szkolenia" %}: {{ termin.opisy_zjazdow_dni }}</p>
 {% if form.errors %}
  <p class="errors_in_form">{% trans "Popraw proszę poniższe błędy." %}</p>
 {% endif %}
 <form class='form formularz_zgloszeniowy' action="" method="post">
  {% csrf_token %}
  <table class="fieldset">
   {{ form }}
   {% if LANGUAGE_CODE == "pl" %}
    <tr>
     <td class="label">Zgody</td>
     <td class="input">
      <ul>
       <li class="{{ form.akceptuje_regulamin.css_classes }}">
        {{ form.akceptuje_regulamin.errors }}
        {{ form.akceptuje_regulamin }} {{ form.akceptuje_regulamin.label_tag }} <small>{{ form.akceptuje_regulamin.help_text|safe }}</small>
       </li>
       <li>
        <br>
        {{ form.chce_zapisac_sie_na_newsletter.errors }}
        {{ form.chce_zapisac_sie_na_newsletter }} {{ form.chce_zapisac_sie_na_newsletter.label_tag }} <small>{{ form.chce_zapisac_sie_na_newsletter.help_text|safe }}</small>
       </li>
      </ul>
     </td>
    </tr>
   {% endif %}
   <tr class='field control'>
    <td colspan="2">
     <span style="font-size: 11px">{% blocktrans %}Administratorem podanych w formularzu danych osobowych jest ALX Academy sp. z o.o. z siedzibą w Warszawie (00-041) przy ul. Jasnej 14/16. („Administrator“). Dane osobowe przetwarzane będą w zakresie niezbędnym do wykonania umowy, której stroną jest osoba, której dane dotyczą, lub do podjęcia działań na żądanie osoby, której dane dotyczą, przed zawarciem umowy (zgodnie z Rozporządzeniem Parlamentu Europejskiego i Rady 2016/679). W przypadku wyrażenia zgody na otrzymywanie informacji handlowych poprzez subskrypcję „Newslettera ALX”, podany adres e-mail będzie przetwarzany również w tym celu. Dane będą przetwarzane przez okres niezbędny do realizacji określonego celu przetwarzania. Osobie, której dane dotyczą przysługuje prawo dostępu do treści swoich danych osobowych, ich sprostowania, usunięcia lub ograniczenia przetwarzania, prawo do wniesienia sprzeciwu wobec przetwarzania, a także prawo do żądania przenoszenia danych. Podanie danych osobowych jest dobrowolne, jednak niezbędne do podjęcia działań według dyspozycji osoby, której dotyczą.{% endblocktrans %}</span>
    </td>
   </tr>
   <tr class='field control'>
    <td>
     <input type="submit"
            value="{% trans 'wyślij zgłoszenie' %}"
            id="submit_button"
            formnovalidate />
    </td>
    <td>
     <p class='form-status' id="please_wait" style='display: none;'>
      {% trans "Zgłoszenie jest wysyłane, proszę czekać..." %}
     </p>
     <p class='form-status' id="try_again" style='display: none;'>
      {% trans "Brak odpowiedzi przy wysyłaniu zgłoszenia. Możesz spróbować ponownie." %}
     </p>
    </td>
   </tr>
  </table>
  <p class='form-legend' data-gwiazdeczka="{% trans 'pole wymagane' %}">* - {% trans "pole jest wymagane" %}</p>
 </form>
 <p>
  {% blocktrans %}
        Po kliknięciu "wyślij", na kolejnej stronie, będzie dostępny gotowy dokument z zamówieniem do wydruku, podpisania i przesłania do nas.
    {% endblocktrans %}
 </p>
 <script>
 dataLayer.push({ gtp: null });
 dataLayer.push({
  'event': 'gtp',
  'gtp': {
    'edu_pagetype': 'Lead',
    'edu_pid': '{{szkolenie.kod}}',
    'edu_totalvalue': {{ szkolenie.cena|default_if_none:"0.00"|floatformat:"0" }},
    'edu_plocid': 'Warszawa'
   }
  });

 </script>
{% endblock body %}
