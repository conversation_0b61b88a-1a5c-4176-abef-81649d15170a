{% load myfilters %}
{% load mytags %}
{% load i18n %}
<div class="lp-courseDates-cointainer">
  <table>
    <thead>
      <tr>
        <th>{% trans "Nazwa kursu" %}</th>
        <th class="lp-courseTime">{% trans "Czas" %}</th>
        <th class="lp-coursePrice">{% trans "Cena" %}</th>
      </tr>
    </thead>
    <tbody>
      {% for obiekt in obiekty %}
        <tr class="lp-courseRow">
            <td class="lp-courseName">
              <a href="{{ obiekt.get_absolute_url }}">{{ obiekt.nazwa }}</a>
              <p>
                <span>{{ obiekt.kod }}</span>
              </p>
            </td>
            <td class="lp-courseTime">
                {{ obiekt.czas_str_no_spaces|safe }}
                {% if max_czas_trwania_wariantow and 0 %}-{{ max_czas_trwania_wariantow }}h{% endif %}
            </td>
            <td class="lp-coursePrice ">
              {% if obiekt.cena_przed_promocja %}
              <span class="price-before-promotion">{{ obiekt|cena_info_short:"cena_przed_promocja"|safe }}</span>
              <br />
              {% endif %}
              <span {% if obiekt.cena_przed_promocja %}class="promotional-price"{% endif %}>
              <strong>{{ obiekt|cena_info_short|safe }}</strong>
              </span>
            </td>

        </tr>
      {% endfor %}

    </tbody>
  </table>
</div>
