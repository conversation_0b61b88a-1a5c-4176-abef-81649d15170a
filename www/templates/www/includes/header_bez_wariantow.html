{% load myfilters %}
{% load mytags %}
{% load i18n %}
{% load static %}
<div class="lp-newTop lp-courseTop{% if not szkolenie.top_background %} lp-newTop-background001{% endif %}"
     {% if szkolenie.top_background %}style="background-image: url('{{ szkolenie.top_background.url }}');"{% endif %}>
  <div class="lp-newTop-content clearfix">
    <div class="lp-newTop-left clearfix">
      <div class="lp-newTop-header clearfix">
        <h1>
          <span class="lp-courseTitle">
            {% if szkolenie.nazwa|does_not_start_with:"bootcamp" %}
              {% trans "Kurs" %}
            {% endif %}
            {{ szkolenie.nazwa }}</span><span class="lp-courseID">{{ szkolenie.kod }}</span>
          </h1>
          {% if szkolenie.podtytul %}<h2 class="lp-subtitle">{{ szkolenie.podtytul }}</h2>{% endif %}
          {% if szkolenie.tagi_marketingowe %}
            <ul class="small-tags">
              {% for mtag in szkolenie.tagi_marketingowe_as_list %}<li>{{ mtag }}</li>{% endfor %}
            </ul>
          {% endif %}
          {% if szkolenie.autoryzacja %}
            <p class="lp-courseDescription">
              {{ szkolenie.nazwa_autoryzacji }}
              {% if szkolenie.kod_autoryzacji %}({{ szkolenie.kod_autoryzacji }}){% endif %}
            </p>
          {% endif %}
          <p class="lp-noTerm{% if miasta %} lp-displayNone{% endif %}">
            {% trans "Obecnie brak terminów otwartych. Dostępne na zamówienie dla grup." %}
          </p>
        </div>
        {% if miasta %}
          {% czy_zastosowac_uproszczony_widok_kursow miasta as czy_uproszczony_widok %}
          {% if en or czy_uproszczony_widok %}
            <div class="lp-cityContainerLess clearfix">
              {% for m in miasta %}
                <div class="lp-cityRow clearfix">
                  <p class="h5" {% if pl and m.miasto.shortname == "London" %}class="lp-londyn"{% endif %}>{{ m.miasto.shortname }}</p>
                  {% if m.terminy %}
                    <ul>
                      {% for t in m.terminy %}
                        {% uwagi_i_wolne_miejsca t as uwagi_i_wolne_miejsca_text %}
                        <li class="formatted-date">
                          <div class="main-text">
                            <strong>{{ t.termin|termin_short }}</strong>
                            <span> - {{ t.get_tryb_display|safe }}
                              {{ uwagi_i_wolne_miejsca_text.uwagi|safe }}
                              {% if t.tryb == 2 and t.opis %}({{ t.opis|safe }}){% endif %}
                            </span>
                          </div>
                          {% if uwagi_i_wolne_miejsca_text.wolne_miejsca %}
                            <span class="warning-text">{{ uwagi_i_wolne_miejsca_text.wolne_miejsca|safe }}</span>
                          {% endif %}
                        </li>
                      {% endfor %}
                    </ul>
                  {% else %}
                    <ul>
                      <li>{{ m.tot.0.tekst }}</li>
                    </ul>
                  {% endif %}
                </div>
              {% endfor %}
            </div>
          {% else %}
            {% tabela_kursow szkolenie miasta as tabela_kursow_result %}
            <!-- terminy kursów -->
            {% include "www/includes/terminy_kursow.html" %}
            <!-- END terminy kursów -->
          {% endif %}
        {% endif %}
        {% if not miasta %}
          <div class="lp-courseInformationWrap clearfix">
            <div class="lp-proposeTerm">
              <a href="{% url 'zaproponuj_termin' slug=szkolenie.slug language=language %}"
                 rel="nofollow">{% trans "Zaproponuj własny termin kursu" %}</a>
            </div>
          </div>
        {% endif %}
        <p class="lp-courseInformation">{% include "www/na_zyczenie_info.html" %}</p>
        <div class="lp-clr"></div>
      </div>
      <div class="lp-newTop-right">
        {% include "www/includes/gwiazdki.html" %}
        <div class="lp-topCoursePrice{% if wariant_szkolenia %} warianty{% endif %} clearfix">
          {% include "www/includes/tooltip_info_price.html" %}
          {% if wariant_szkolenia %}
            {# TODO: ta noga tutaj jest prawdopodobnie zbędna #}
            {% include "www/includes/include_xx_wariant_szkolenia.html" %}
            {# end TODO #}
          {% else %}
            <div class="lp-topCoursePriceLeft{% if szkolenie.cena_przed_promocja %} lp-coursePromotion{% endif %}">
              {% include "www/includes/cena_kursu.html" %}
              {% include 'www/includes/kurs_installements_with_yellow_icons.html' %}
            </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
