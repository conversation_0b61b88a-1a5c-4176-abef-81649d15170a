{% load myfilters %}
{% load mytags %}
<table class="lp-trainingCalendar clearfix">
  <thead>
    <tr>
      <th></th>
      <th>
        <strong><PERSON><PERSON><PERSON><PERSON> dzienna</strong>
        {% if tabela_kursow_result.header.D %}{{ tabela_kursow_result.header.D|safe }}{% endif %}
      </th>
      <th>
        <strong>Edy<PERSON>ja <span class="lp-mobileHide">weekendowa (</span>zaoczna<span class="lp-mobileHide">)</span></strong>
        {% if tabela_kursow_result.header.Z %}{{ tabela_kursow_result.header.Z|safe }}{% endif %}
      </th>
    </tr>
  </thead>
  {% for row in tabela_kursow_result.data %}
    <tbody>
      {% for t in row.terms %}
        <tr>
          <td class="lp-trainingCity">
            <p {% if pl and row.location.shortname == "London" %}class="lp-londyn"{% endif %}>{{ row.location.shortname }}</p>
          </td>
          <td class="lp-trainingDaily">
            {% if t.D %}
              <span class="date-format-full">
                {% if wariant_szkolenia %}
                  {{ t.D|termin_full:"warianty" }}
                {% else %}
                  {{ t.D|termin_full }}
                {% endif %}
              </span>
              {# TODO: potrzebujemy tego? #}
              <span class="date-format-short" style="display: none">
                {% if wariant_szkolenia %}
                  {{ t.D|termin_full_mobile:"warianty" }}
                {% else %}
                  {{ t.D|termin_full_mobile }}
                {% endif %}
              </span>
              {# end TODO #}
              {% if t.D.tryb == 2 %}
                {% if t.D.opis %}
                  <span class="lp-trainingDescription">(wieczorowy, {{ t.D.opis|safe }})</span>
                {% else %}
                  <span class="lp-trainingDescription">(wieczorowy)</span>
                {% endif %}
              {% endif %}
              {% uwagi_i_wolne_miejsca t.D as uwagi_i_wolne_miejsca_text %}
              {% if uwagi_i_wolne_miejsca_text.uwagi and uwagi_i_wolne_miejsca_text.uwagi != tabela_kursow_result.header.D %}
                <span class="lp-trainingDescription">{{ uwagi_i_wolne_miejsca_text.uwagi|safe }}</span>
              {% endif %}
              {% if uwagi_i_wolne_miejsca_text.wolne_miejsca %}
                <span class="lp-trainingLast">{{ uwagi_i_wolne_miejsca_text.wolne_miejsca|safe }}</span>
              {% endif %}
            {% endif %}
          </td>
          <td class="lp-trainingWeekly">
            {% if t.Z %}
              <span class="date-format-full">
                {% if wariant_szkolenia %}
                  {{ t.Z|termin_full:"warianty" }}
                {% else %}
                  {{ t.Z|termin_full }}
                {% endif %}
              </span>
              {# TODO: potrzebujemy tego? #}
              <span class="date-format-short" style="display: none">
                {% if wariant_szkolenia %}
                  {{ t.Z|termin_full_mobile:"warianty" }}
                {% else %}
                  {{ t.Z|termin_full_mobile }}
                {% endif %}
              </span>
              {# end TODO #}
              {% uwagi_i_wolne_miejsca t.Z as uwagi_i_wolne_miejsca_text %}
              {% if uwagi_i_wolne_miejsca_text.uwagi and uwagi_i_wolne_miejsca_text.uwagi != tabela_kursow_result.header.Z %}
                <span class="lp-trainingDescription">{{ uwagi_i_wolne_miejsca_text.uwagi|safe }}</span>
              {% endif %}
              {% if uwagi_i_wolne_miejsca_text.wolne_miejsca %}
                <span class="lp-trainingLast">{{ uwagi_i_wolne_miejsca_text.wolne_miejsca|safe }}</span>
              {% endif %}
            {% endif %}
          </td>
        </tr>
      {% endfor %}
    </tbody>
  {% endfor %}
</table>
