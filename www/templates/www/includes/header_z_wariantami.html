{% load myfilters %}
{% load mytags %}
{% load i18n %}
{% load static %}
<div class="pages-header">
  <div class="pages-header--inner">
    <div class="pages-header--content-left">
      <h1 class="title">
        <span>
          {% if szkolenie.nazwa|does_not_start_with:"bootcamp" %}
            {% trans "Kurs" %}
          {% endif %}
        {{ szkolenie.nazwa }}</span>
        <span class="id">{{ szkolenie.kod }}</span>

      </h1>
      <h2 class="lp-subtitle-1">{{ szkolenie.podtytul }}</h2>
      {% if szkolenie.tagi_marketingowe %}
        <ul class="tags">
          {% for mtag in szkolenie.tagi_marketingowe_as_list %}<li>{{ mtag }}</li>{% endfor %}
        </ul>
      {% endif %}
      {% if szkolenie.autoryzacja %}
        <p class="description">
          {{ szkolenie.nazwa_autoryzacji }}
          {% if szkolenie.kod_autoryzacji %}({{ szkolenie.kod_autoryzacji }}){% endif %}
        </p>
      {% endif %}
      {% if not miasta %}
        <p class="no-deadlines">{% trans "Obecnie brak terminów otwartych. Dostępne na zamówienie dla grup." %}</p>
      {% endif %}
      {% if miasta %}
        {% czy_zastosowac_uproszczony_widok_kursow miasta as czy_uproszczony_widok %}
        {% if en or czy_uproszczony_widok %}
          <div class="deadlines">
            {% for m in miasta %}
              <div class="deadlines--row">
                <h5 {% if pl and m.miasto.shortname == "London" %}class="lp-londyn"{% endif %}>{{ m.miasto.shortname }}</h5>
                {% if m.terminy %}
                  <ul>
                    {% for t in m.terminy %}
                      {% uwagi_i_wolne_miejsca t as uwagi_i_wolne_miejsca_text %}
                      <li>
                        <div class="main-text">
                          <strong>{{ t.termin|termin_short }}</strong>
                          <span> - {{ t.get_tryb_display|safe }}
                            {{ uwagi_i_wolne_miejsca_text.uwagi|safe }}
                            {% if t.tryb == 2 and t.opis %}({{ t.opis|safe }}){% endif %}
                          </span>
                        </div>
                        {% if uwagi_i_wolne_miejsca_text.wolne_miejsca %}
                          <span class="warning-text">{{ uwagi_i_wolne_miejsca_text.wolne_miejsca|safe }}</span>
                        {% endif %}
                      </li>
                    {% endfor %}
                  </ul>
                {% else %}
                  <ul>
                    <li>{{ m.tot.0.tekst }}</li>
                  </ul>
                {% endif %}
              </div>
            {% endfor %}
          </div>
        {% else %}
          {% tabela_kursow szkolenie miasta as tabela_kursow_result %}
          <!-- terminy kursów -->
          {% include "www/includes/terminy_kursow.html" %}
          <!-- END terminy kursów -->
        {% endif %}
      {% endif %}
      {% if not miasta %}
        <div class="suggest-dates">
          <div class="suggest-dates--inner">
            <a href="{% url 'zaproponuj_termin' slug=szkolenie.slug language=language %}"
               rel="nofollow">{% trans "Zaproponuj własny termin kursu" %}</a>
          </div>
        </div>
      {% endif %}
      <p class="include-information">{% include "www/na_zyczenie_info.html" %}</p>
    </div>
    <div class="pages-header--content-right">
      {% ocena_szkolenia szkolenie as stars %}
      <div class="pages-header--rating">
        {% if stars %}
          <div class="rate">
            {{ stars.avg }}<span>/5</span> <span>({{ stars.count }})</span>
          </div>
          <div class="stars-cointaner">
            <div class="stars stars-{{ stars.css }}">Stars</div>
          </div>
        {% endif %}
      </div>
      <div class="pages-header--price" style="margin:auto;">
        {% include "www/includes/bialy_box_w_naglowku_z_wariantami.html" %}
      </div>
    </div>
  </div>
</div>
