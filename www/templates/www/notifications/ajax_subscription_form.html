{% load i18n %}

<div id="subscription-container" class="lang-{{ LANGUAGE_CODE }}">
    <div class="morph-button morph-button-modal morph-button-modal-2">
        <div class="morph-content">
            <div>
                <div class="content-style-form content-style-form-1">
                    <div class="header-bg">
                        <span class="icon-close">Close</span>
                        <h2>{% trans "Powiadomienie o nowych terminach" %}</h2>
                    </div>
                    <div class="modal-content">
                        <p>
                        {% blocktrans %}Wyślemy Ci wiadomość e-mail, kiedy zaplanujemy nowy termin tego szkolenia, lub jeden z już podanych na stronie terminów zostanie oficjalnie potwierdzony. Podaj nam swój adres e-mail.{% endblocktrans %}
                        </p>
                        <form id="subscription-ajax-form">
                            {% csrf_token %}
                            {% if form.locations %}
                                {% for group in form.grouped_locations %}
                                    <div class="form-row">
                                        {% for location in group %}
                                            <div class="city">
                                                <input id="id_locations_{{ forloop.counter0 }}" name="locations" type="checkbox" value="{{ location.pk }}"
                                                       {% if location.pk in form.locations.data %}checked="checked"{% endif %}>
                                                <label for="id_locations_{{ forloop.counter0 }}">{{ location }}</label>
                                            </div>
                                        {% endfor %}
                                    </div>
                                {% endfor %}

                                {{ form.locations.errors.as_ul }}
                            {% endif %}
                            <div class="your-email">
                                <label for="email">{% trans "Adres E-Mail" %}</label>
                                {{ form.email }}
                                {{ form.email.errors.as_ul }}
                            </div>

                            <div class="tos">
                                <p>
                                    <span style="padding-right: 5px">{{ form.tos }}</span>
                                    {% blocktrans context "newsletter" %}Wyrażam zgodę na przetwarzanie danych osobowych w postaci adresu e-mail w celu otrzymania informacji o terminach szkoleń organizowanych przez ALX Academy sp. z o.o. z siedzibą w Warszawie.{% endblocktrans %}
                                </p>

                                {% if form.tos.errors %}<ul class="errorlist" style="top: 4px;"><li>{{ form.tos.errors.as_text|cut:"* " }}</li></ul>{% endif %}

                                <p style="font-size: 11px"><br>{% blocktrans %}Administratorem adresu e-mail jest ALX Academy sp. z o.o. z siedzibą w Warszawie (00-041) przy ul. Jasnej 14/16. Wyrażenie zgody jest dobrowolne. Osobie, której dane dotyczą przysługuje prawo dostępu do treści swoich danych i możliwości ich poprawiania. Powyższa zgoda może być odwołana w każdym czasie, co skutkować będzie usunięciem podanego adresu e-mail z listy dystrybucyjnej Administratora.{% endblocktrans %}<br/><br/></p>
                            </div>

                            <div class="buttons">
                                <input class="btn btn-red" type="submit" value="{% trans 'Zapisz' %}" id="subscription-ajax-form-submit" formnovalidate />
                                <a href="#" class="btn btn-gray btn-close">{% trans "Anuluj" %}</a>
                                <p>{% blocktrans %}Na podany adres E-Mail będziemy przesłyać tylko informacje o wybranym terminie. Każda wiadomość będzie zawierać odnośnik umożliwiający zmianę preferencji oraz łatwą rezygnację z otrzymywania powiadomień.{% endblocktrans %}</p>
                            </div>

                        </form>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


