{% load i18n %}
<div id="subscription-container"
     data-lang="{{ LANGUAGE_CODE }}"
     class="lang-{{ LANGUAGE_CODE }}">
    <div class="morph-button morph-button-modal morph-button-modal-2">
        <div class="morph-content">
            <div>
                <div class="content-style-form content-style-form-1">
                    <div class="header-bg">
                        <span class="icon-close">Close</span>
                        <h2>{% trans "Powiadomienie o nowych terminach" %}</h2>
                    </div>
                    <div class="modal-content">
                        <p id="thank-you">{% trans "Dziękujemy." %}</p>
                        <p>
                            {% if send_activation_email %}
                        <strong>{% trans "UWAGA!" %}</strong>
                        {% blocktrans %}Na podany adres (<span id="notifications-activation-email">{{ activation_email }}</span>) wysłaliśmy wiadomość z odnośnikiem prowadzącym do potwierdzenia zapisu. Aby otrzymywać wiadomości o nowych i aktualizowanych terminach konieczne jest otwarcie wiadomości i kliknięcie w przesłany odnośnik.{% endblocktrans %}
                        {% else %}
                        {% blocktrans %}Zmiany zostały zapisane.{% endblocktrans %}
                        {% endif %}
                        </P>

                       <p><br><br><a href="#" class="btn btn-gray btn-close">{% trans "Zamknij okno" %}</a></p>
                       <!-- Event snippet for Powiadomienie o terminach conversion page -->
                        <script>
                            dataLayer.push({
                                'event': 'powiadomienieTerminy',
                                'userEmail': '{{ activation_email }}',
                                'value': {{ training.cena|default_if_none:"0.00"|floatformat:"0" }},
                                'id': '{{ training.kod }}'
                            });
                            dataLayer.push({gtp: null});
                            dataLayer.push({gbv: null});
                            dataLayer.push({
                                'event': 'gbv',
                                'gbv': {
                                    'event_name': 'view_item',
                                    'value': {{ training.cena|default_if_none:"0.00"|floatformat:"0" }},
                                    'items': {
                                        'id': '{{ training.kod }}',
                                        'google_business_vertical': 'education'
                                    }
                                },

                                'gtp': {
                                    'edu_pagetype': 'program',
                                    'edu_pid': '{{ training.kod }}',
                                    'edu_totalvalue': {{ training.cena|default_if_none:"0.00"|floatformat:"0" }},
                                    'edu_plocid': 'Warszawa'

                                }
                            });

                        </script>


                       </div>
                </div>
            </div>
        </div>
    </div>
</div>
