{% extends "www/base.html" %}

{% load i18n %}
{% load mytags%}
{% load myfilters %}

{% block data_layer %}

<script>
dataLayer.push({ gtp: null });
dataLayer.push({
  'event': 'gtp',
  'gtp': {
    'edu_pagetype': 'Lead',
    'edu_pid': '{{szkolenie.kod}}',
    'edu_totalvalue': {{ szkolenie.cena|default_if_none:"0.00"|floatformat:"0" }},
    'edu_plocid': 'Warszawa'
  }
});
</script>

{% endblock %}

{% block title %}
{% trans "Formularz zgłoszeniowy" %}
{% endblock %}

{% block h1 %}
<h1>{% trans "Formularz zgłoszeniowy" %}</h1>
{% endblock %}

{% block extra_head %}
    {{ block.super }}
    <meta name="robots" content="noindex,follow">

    <script type="text/javascript">
      var terminy_obiady = {{ terminy_obiady|safe }};
      var cena_autoryzacji = "{{ szkolenie.cena_autoryzacji }}";
      {% if szkolenie.autoryzacja %}
        var krotka_nazwa_autoryzacji = "{{ szkolenie.autoryzacja.opis_krotki }}";
      {% else %}
        var krotka_nazwa_autoryzacji = "";
      {% endif %}
    </script>

    <script src="{% staticbuster js/zgloszenie-formularz.js %}"></script>
{% endblock extra_head %}


{% block body %}

<div class="hidden tekst_o_kodzie_rabatowym">
  {% blocktrans %}Jeśli posiadasz kod rabatowy <a href="#" id="discount_coupon_help_text_link">kliknij tutaj</a>.{% endblocktrans %}
</div>

<div class="hidden tekst_o_fakturze">
  <strong>{% trans "UWAGA:" %}</strong>
  {% blocktrans %}Wybór faktury papierowej oznacza dopłatę do ceny szkolenia w wysokości {{ cena_papierowej_faktury }} zł netto.{% endblocktrans %}
</div>

<div class="hidden tekst_o_certyfikacie">
  <strong>{% trans "UWAGA:" %}</strong>
  {% comment %}{% blocktrans %}Wybór drukowanego certyfikatu oznacza dopłatę do ceny szkolenia w wysokości {{ cena_drukowanego_certyfikatu }} zł netto.{% endblocktrans %}{% endcomment %}

  {% blocktrans %}Wybór drukowanego certyfikatu oznacza dopłatę do ceny szkolenia w wysokości {{ cena_drukowanego_certyfikatu }} zł netto (cena zawiera przesyłkę pocztą we wzmocnionej kopercie za potwierdzeniem, na podany adres do korespondencji).{% endblocktrans %}

</div>

{% for termin_obiad_id, termin_obiad in terminy_obiady_dict.items %}

  {% if termin_obiad.obiady_data_id != "" %}
    <div data-id="{{ termin_obiad_id }}" class="obiady hidden">
  {% endif %}

  {% if termin_obiad.obiady_data_id == "obiady-wliczone" %}
    <i> {% trans "Obiady są wliczone w cenę." %} </i>
  {% endif %}

  {% if termin_obiad.obiady_data_id == "nie-ma-obiadow" %}
    <i> {% trans "Brak obiadów i brak możliwości ich dokupienia." %} </i>
  {% endif %}

  {% if termin_obiad.obiady_data_id == "nie-ma-obiadow-weekend" %}
    <i>
      {% trans "Termin weekendowy." %}
      {% trans "Brak obiadów i brak możliwości ich dokupienia." %}
    </i>
  {% endif %}

  {% if termin_obiad.obiady_data_id == "obiady-opcjonalne" %}
    <i>
      {% trans "Obiady nie są wliczone w cenę." %}
      {% with szkolenie.waluta.symbol as waluta and termin_obiad.cena_obiadu as cena_obiadu %}
      {% if termin_obiad.stawka_vat %}
        {% blocktrans %}Koszt obiadu {{ cena_obiadu }} {{ waluta }} netto za osobę za obiad.{% endblocktrans %}
      {% else %}
        {% blocktrans %}Koszt obiadu {{ cena_obiadu }} {{ waluta }} za osobę za obiad.{% endblocktrans %}
      {% endif %}
      {% endwith %}
      <br/>
      <br/>
      {% blocktrans %}W przypadku wykupienia obiadów, faktura VAT zostanie wystawiona z jedną całościową pozycją "szkolenie" - tj. obiady nie będą wyszczególniane osobno.{% endblocktrans %}
      <br/>
      <br/>
      {% trans "Nie ma możliwości dokupienia obiadów w trakcie trwania szkolenia poprzez korektę naszej faktury. Podczas szkolenia, można dokonać zakupu, zapłaty i uzyskać fakturę jedynie bezpośrednio w restauracji." %}
    </i>
  {% endif %}

  {% if termin_obiad.obiady_data_id != "" %}
    </div>
  {% endif %}

{% endfor %}

<h2>
    {{ szkolenie.nazwa }}{% if wariant_szkolenia %} / {{ szkolenie.info_dla_wariantu }}{% endif %}
    <span class="subtitle">
        <strong class='course-code'>{{ szkolenie.kod }}</strong>
    </span>
</h2>

    {% if warianty_szkolenia %}
    Dostępne warianty:
    <ul>
    {% for wariant in warianty_szkolenia %}
        <li><a href="{% url 'zgloszenie' slug=wariant.slug language=language %}" rel="nofollow">{{ wariant.nazwa }} / {{ wariant.info_dla_wariantu }}</a></li>
    {% endfor %}
    </ul>
    {% endif %}

    {% comment %}

        {% if wariant_szkolenia %}
            <p>Zapisujesz się na wariant {% if not wariant_szkolenia.aktywne  %}"standard"{% else %}"XL"{% endif %}.
                Dostępny jest również wariant: <a href="{% url 'zgloszenie' slug=wariant_szkolenia.slug language=language %}" rel="nofollow">
                    {{ wariant_szkolenia.nazwa }} / {{ wariant_szkolenia.info_dla_wariantu }}</a> (kliknij w link, aby zmienić zapis na ten wariant kursu).</p>
        {% endif %}

    {% endcomment %}

<div class="special-section">
  {% if pl %}
    <p>
        <b>Uwaga: Jeżeli wolą Państwo nie korzystać z formularza on-line</b>,
        można również dokonać zapisu poprzez:
    </p>
    <ul>
        <li>
            tradycyjny formularz papierowy - do pobrania ze strony
            <a href="/pl/formularz-zgloszeniowy/">formularze zgłoszeniowe</a>,
        </li>
        <li>
            umowę szkolenia lub zamówienie według wzoru Klienta -
            w tym wypadku prosimy o <a href="/pl/kontakt">kontakt</a>.
        </li>
    </ul>
    <p>
        W wypadku jakiechkolwiek pytań lub wątpliwości dotyczących
        zapisów, zapraszamy do <a href="/pl/kontakt">kontaktu</a>.
    </p>
  {% endif %}

  {% if en %}
    {# To jest wykomentowane, bo nie ma flatpage’a z formularzami angielskimi. #}
    {% comment %}
      <p><b>If you would rather not use an online order form</b>, you can also apply via: </p>
      <ul>
        <li>
          {# TODO: przetłumaczyć formularz. #}
	  a traditional paper order form - available at <a href="/pl/formularz-zgloszeniowy/">order forms</a>,
	</li>
        <li>
	  training contract or other contract drafted according to your needs - please <a href=/en/contact-us>contact us</a>.
	</li>
      </ul>
      <p>In case of any doubts or questions concerning the order process, please <a href=/en/contact-us>contact us</a>.</p>
    {% endcomment %}
  {% endif %}
</div>

{% if terminy %}

{% if form.errors %}

<p class="errors_in_form">{% trans "Popraw proszę poniższe błędy." %}</p>

{% endif %}

<form class='form formularz_zgloszeniowy' action="" method="post" name="zgloszenie_form">
    {% csrf_token %}
    <table class="fieldset">

    {{ form }}

        {% if LANGUAGE_CODE == "pl" %}

                <tr>
                <td class="label">
                    Zgody
                </td>
                <td class="input">
                    <ul>
                        <li class="{{ form.akceptuje_regulamin.css_classes }}">
                            {{ form.akceptuje_regulamin.errors }}
                            {{ form.akceptuje_regulamin }} {{ form.akceptuje_regulamin.label_tag }} <small>{{ form.akceptuje_regulamin.help_text|safe }}</small>
                        </li>

                        <li>
                            <br>
                            {{ form.chce_zapisac_sie_na_newsletter.errors }}
                            {{ form.chce_zapisac_sie_na_newsletter }} {{ form.chce_zapisac_sie_na_newsletter.label_tag }} <small>{{ form.chce_zapisac_sie_na_newsletter.help_text|safe }}</small>
                        </li>
                    </ul>
                </td>
            </tr>
        {% else %}
                <tr>
                <td class="label">
                    Consent
                </td>
                <td class="input">
                    <ul>
                        <li class="{{ form.akceptuje_regulamin.css_classes }}">
                            {{ form.akceptuje_regulamin.errors }}
                            {{ form.akceptuje_regulamin }} {{ form.akceptuje_regulamin.label_tag }} <small>{{ form.akceptuje_regulamin.help_text|safe }}</small>
                        </li>
                    </ul>
                </td>
            </tr>        
        {% endif %}


        <tr class='field control'>
            <td colspan="2">
                <span style="font-size: 11px">{% blocktrans %}Administratorem podanych w formularzu danych osobowych jest ALX Academy sp. z o.o. z siedzibą w Warszawie (00-041) przy ul. Jasnej 14/16. („Administrator“). Dane osobowe przetwarzane będą w zakresie niezbędnym do wykonania umowy, której stroną jest osoba, której dane dotyczą, lub do podjęcia działań na żądanie osoby, której dane dotyczą, przed zawarciem umowy (zgodnie z Rozporządzeniem Parlamentu Europejskiego i Rady 2016/679). W przypadku wyrażenia zgody na otrzymywanie informacji handlowych poprzez subskrypcję „Newslettera ALX”, podany adres e-mail będzie przetwarzany również w tym celu. Dane będą przetwarzane przez okres niezbędny do realizacji określonego celu przetwarzania. Osobie, której dane dotyczą przysługuje prawo dostępu do treści swoich danych osobowych, ich sprostowania, usunięcia lub ograniczenia przetwarzania, prawo do wniesienia sprzeciwu wobec przetwarzania, a także prawo do żądania przenoszenia danych. Podanie danych osobowych jest dobrowolne, jednak niezbędne do podjęcia działań według dyspozycji osoby, której dotyczą.{% endblocktrans %}</span>
            </td>
        </tr>
        <tr class='field control'>
            <td>
                <input type="submit" value="{% trans 'wyślij zgłoszenie' %}" id="submit_button" formnovalidate />
            </td>
            <td>
                <p class='form-status' id="please_wait" style='display: none;'>
                  {% trans "Zgłoszenie jest wysyłane, proszę czekać..." %}
                </p>
                <p class='form-status' id="try_again" style='display: none;'>
                  {% trans "Brak odpowiedzi przy wysyłaniu zgłoszenia. Możesz spróbować ponownie." %}
                </p>
            </td>
        </tr>
    </table>
    <p class='form-legend' data-gwiazdeczka="{% trans 'pole wymagane' %}">
          * - {% trans "pole jest wymagane" %}
    </p>
</form>

{% if szkolenie.tag_dlugosc.slug == "szkolenie" %}
    <p>
        {% blocktrans %}
        Przesłanie formularza zgłoszeniowego przez internet dokonuje wstępnej
        rezerwacji, ważnej przez 48 godzin. Dla formalnego potwierdzenia
        zgłoszenia po wypełnieniu powyższego formularza <strong>jest konieczne</strong> jego
        wydrukowanie (będzie to możliwe na kolejnym ekranie, po naciśnięciu
        "wyślij zgłoszenie"), <strong>podpisanie i przesłanie</strong> nam: faksem (22 266 06 95),
        lub jako skan e-mailem (<EMAIL>).
        {% endblocktrans %}
          {# FIXME 2013-10-17 - wersja angielska niepoprawiona #}
    </p>

{% else %}

    <p>
        {% if pl %}
          Przesłanie formularza zgłoszeniowego przez internet dokonuje jedynie
          wstępnej rezerwacji. Dla formalnego potwierdzenia zgłoszenia
          <strong>konieczne jest</strong>:
          <ol class="zgloszenie">
            <li>
              Po wypełnieniu powyższego formularza, <strong>wydrukowanie</strong>
              go (będzie to możliwe na kolejnym ekranie, po naciśnięciu "wyślij
              zgłoszenie"), <strong>podpisanie i przesłanie nam</strong> jako skan
              lub zdjęcie e-mailem (<EMAIL>), lub faksem (22 266 06 95).
              Uwaga: Po przesłaniu podpisanego formularza nastąpi kontakt
              z naszego biura.
            </li>
            <li>
              Ostateczne potwierdzenie zgłoszenia następuje po przelaniu na nasze konto zaliczki lub pełnej opłaty za kurs w terminie 7 dni od dokonania zgłoszenia, bądź po podpisaniu umowy szkoleniowej (w wypadku niestandardowych zgłoszeń). Umowa zawarta zostaje po potwierdzeniu przyjęcia zgłoszenia przez ALX. Warunkiem prawidłowego wykonania umowy przez ALX jest zapłata przez Klienta pełnej opłaty za kurs lub zapłaty częściowej (zgodnie z ustalonym harmonogramem płatności) lub indywidualnie ustalonej zapłaty.
            </li>
          </ol>
        {% endif %}

        {% if en %}


Submitting the online application form results solely in a pre-booking. For a formal confirmation of the application, <strong>it is necessary to</strong>:

<ol class="zgloszenie">

<li><strong>Print</strong> the form after completing it (printing will be possible on the next screen, after pressing "send application"), <strong>sign it and send it to us</strong> as a scan or photo by e-mail (<EMAIL>) or by fax (22 266 06 95). Please note: After submitting the signed form, you will be contacted by our office.</li>

<li>The final confirmation of the application takes place after transferring the advance payment or the full payment for the course to our account within 7 days of submitting the application, or after signing the training contract (in the case of non-standard applications). The contract is concluded after ALX confirms receipt of the application. The condition for the proper execution of the contract by ALX is the payment by the Customer of the full fee for the course, individually agreed payment or partial payment (according to the agreed payment schedule).</li>
</ol>

        {% endif %}
    </p>

{% endif %}

{% else %}

<p>
    {% trans "To szkolenie nie ma w tej chwili ustalonych terminów" %}
    (<a href="{% url 'zaproponuj_termin' slug=szkolenie.slug language=language %}" rel="nofollow">
    {% trans "zaproponuj własny termin" %}
    </a>).
</p>

{% endif %}

{% endblock body %}
