{% if termin.hybryda %}
Od<PERSON><PERSON><PERSON> chętnych na szkolenie {{ termin.szkolenie }} w terminie {{ termin.termin }} ({{ termin.lokalizacja }}). Nie rusza - jest:

Razem hybrydowo: {{ termin.ilosc_uczestnikow_rentownych_z_terminem_zdalnym_as_text }} chętnych, min_grupa {{ termin.szkolenie.min_grupa }}
W tym:
 - {{ termin.lokalizacja }} - {{ termin.ilosc_uczestnikow_rentownych_as_text }} chętnych, bieżący status czy robimy: {{ termin.odbylo_sie|yesno:"tak,nie,nie wiadomo" }}, {{ site_url }}{% url 'admin:www_terminszkolenia_change' termin.pk %}
   Uczestnicy: {% for uczestnik in termin.uczestnicy_niezrezygnowani %}{% if uczestnik.faktura_firma %}{{ uczestnik.faktura_firma }} - {% endif %}{{ uczestnik.imie_nazwisko }}{% if not forloop.last %}, {% endif %}{% endfor %}

 - {{ termin.hybryda.lokalizacja }} - {{ termin.hybryda.ilosc_uczestnikow_rentownych_as_text }} chętnych, bieżący status czy robimy: {{ termin.hybryda.odbylo_sie|yesno:"tak,nie,nie wiadomo" }}, {{ site_url }}{% url 'admin:www_terminszkolenia_change' termin.hybryda.pk %}
   Uczestnicy: {% for uczestnik in termin.hybryda.uczestnicy_niezrezygnowani %}{% if uczestnik.faktura_firma %}{{ uczestnik.faktura_firma }} - {% endif %}{{ uczestnik.imie_nazwisko }}{% if not forloop.last %}, {% endif %}{% endfor %}

{% with termin.ilosc_uczestnikow_dla_faktoryzacji as ilosc_uczestnikow_dla_faktoryzacji %}{% if ilosc_uczestnikow_dla_faktoryzacji %}

Dodatkowe zgłoszenia z faktoryzacji:
{% for row in ilosc_uczestnikow_dla_faktoryzacji %}
    {{ row.szkolenie }}: {{ row.ilosc_uczestnikow }}
{% endfor %}

{% endif %}{% endwith %}

{% else %}
Odwołać chętnych na szkolenie {{ termin.szkolenie }} w terminie {{ termin.termin }} ({{ termin.lokalizacja }}). Nie rusza - jest {{ termin.ilosc_uczestnikow_rentownych_as_text }} chętnych, min_grupa {{ termin.szkolenie.min_grupa }}, bieżący status czy robimy: {{ termin.odbylo_sie|yesno:"tak,nie,nie wiadomo" }}.

{% with termin.ilosc_uczestnikow_dla_faktoryzacji as ilosc_uczestnikow_dla_faktoryzacji %}{% if ilosc_uczestnikow_dla_faktoryzacji %}

Dodatkowe zgłoszenia z faktoryzacji:
{% for row in ilosc_uczestnikow_dla_faktoryzacji %}
    {{ row.szkolenie }}: {{ row.ilosc_uczestnikow }}
{% endfor %}

{% endif %}{% endwith %}
{{ site_url }}/admin/www/terminszkolenia/{{ termin.id }}/

{% for uczestnik in termin.uczestnicy_niezrezygnowani %}{{ uczestnik.faktura_firma }} - {{ uczestnik.imie_nazwisko }}
{% endfor %}
{% endif %}

Pozdrowienia,
System ALX

{% include 'www/sig.txt' %}
