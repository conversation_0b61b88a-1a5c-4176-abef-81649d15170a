{% if termin.hybryda %}
<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, że szkolenie {{ termin.szkolenie }}{% if termin.snz_opis %}({{ termin.snz_opis }}){% endif %} w terminie {{ termin.termin }} ({{ termin.lokalizacja }}) rusza{% if faktoryzacja %} na podstawie decyzji o uruchomieniu zajęć których niniejszy kurs/szkolenie jest faktoryzacją{% endif %}
<br><br>
Razem hybrydowo: {{ termin.ilosc_uczestnikow_rentownych_z_terminem_zdalnym_as_text }} chętnych, min_grupa {{ termin.szkolenie.min_grupa }}
<br>
W tym:
<br>
 - {{ termin.lokalizacja }} - {{ termin.ilosc_uczestnikow_rentownych_as_text }} chętnych, bieżący status czy robimy: {{ termin.odbylo_sie|yesno:"tak,nie,nie wiadomo" }}, <a href="{{ site_url }}{% url 'admin:www_terminszkolenia_change' termin.pk %}">{{ site_url }}{% url 'admin:www_terminszkolenia_change' termin.pk %}</a><br>
   Uczestnicy: {% for uczestnik in termin.uczestnicy_niezrezygnowani %}{% if uczestnik.faktura_firma %}{{ uczestnik.faktura_firma }} - {% endif %}{{ uczestnik.imie_nazwisko }}{% if not forloop.last %}, {% endif %}{% endfor %}
<br>
 - {{ termin.hybryda.lokalizacja }} - {{ termin.hybryda.ilosc_uczestnikow_rentownych_as_text }} chętnych, bieżący status czy robimy: {{ termin.hybryda.odbylo_sie|yesno:"tak,nie,nie wiadomo" }}, <a href="{{ site_url }}{% url 'admin:www_terminszkolenia_change' termin.hybryda.pk %}">{{ site_url }}{% url 'admin:www_terminszkolenia_change' termin.hybryda.pk %}</a><br>
   Uczestnicy: {% for uczestnik in termin.hybryda.uczestnicy_niezrezygnowani %}{% if uczestnik.faktura_firma %}{{ uczestnik.faktura_firma }} - {% endif %}{{ uczestnik.imie_nazwisko }}{% if not forloop.last %}, {% endif %}{% endfor %}

{% with termin.ilosc_uczestnikow_dla_faktoryzacji as ilosc_uczestnikow_dla_faktoryzacji %}
    {% if ilosc_uczestnikow_dla_faktoryzacji %}
        <br><br>
        Dodatkowe zgłoszenia z faktoryzacji:
        {% for row in ilosc_uczestnikow_dla_faktoryzacji %}
            <br>{{ row.szkolenie }}: {{ row.ilosc_uczestnikow }}
        {% endfor %}

    {% endif %}
{% endwith %}
{% else %}
Potwierdzić, że szkolenie {{ termin.szkolenie }} {% if termin.snz_opis %}({{ termin.snz_opis }}){% endif %}w terminie {{ termin.termin }} ({{ termin.lokalizacja }}) rusza{% if faktoryzacja %} na podstawie decyzji o uruchomieniu zajęć których niniejszy kurs/szkolenie jest faktoryzacją{% endif %} (jest {{ termin.ilosc_uczestnikow_rentownych_as_text }} chętnych, min_grupa {% if faktoryzacja %}(dla grupy uruchamianej samodzielnie) wynosiłaby {{ termin.szkolenie.min_grupa }}{% else %}{{ termin.szkolenie.min_grupa }}{% endif %}, bieżący status czy robimy: {{ termin.odbylo_sie|yesno:"tak,nie,nie wiadomo" }}).

    {% with termin.ilosc_uczestnikow_dla_faktoryzacji as ilosc_uczestnikow_dla_faktoryzacji %}
        {% if ilosc_uczestnikow_dla_faktoryzacji %}
<br><br>
            Dodatkowe zgłoszenia z faktoryzacji:
            {% for row in ilosc_uczestnikow_dla_faktoryzacji %}
                <br>{{ row.szkolenie }}: {{ row.ilosc_uczestnikow }}
            {% endfor %}

        {% endif %}
{% endwith %}

<br><br>
<a href="{{ site_url }}/admin/www/terminszkolenia/{{ termin.id }}/">{{ site_url }}/admin/www/terminszkolenia/{{ termin.id }}/</a>

<br>
{% for uczestnik in termin.uczestnicy_niezrezygnowani %}
    <br>{{ uczestnik.faktura_firma }} - {{ uczestnik.imie_nazwisko }}
{% endfor %}
{% endif %}

<br><br>
Pozdrowienia, <br/>
System ALX
<br/><br/>

--<br/>
{% include "www/includes/dane_firmowe_pl.html" %}