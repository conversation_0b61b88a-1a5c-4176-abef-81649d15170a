{% if termin.hybryda %}
Od<PERSON><PERSON><PERSON> chętnych na szkolenie {{ termin.szkolenie }} w terminie {{ termin.termin }} ({{ termin.lokalizacja }}). Nie rusza - jest:
<br><br>
Ra<PERSON><PERSON> hybrydowo: {{ termin.ilosc_uczestnikow_rentownych_z_terminem_zdalnym_as_text }} chętnych, min_grupa {{ termin.szkolenie.min_grupa }}
<br>
W tym:
<br>
 - {{ termin.lokalizacja }} - {{ termin.ilosc_uczestnikow_rentownych_as_text }} chętnych, bieżący status czy robimy: {{ termin.odbylo_sie|yesno:"tak,nie,nie wiadomo" }}, {{ site_url }}{% url 'admin:www_terminszkolenia_change' termin.pk %}
    <br>
   Uczestnicy: {% for uczestnik in termin.uczestnicy_niezrezygnowani %}{% if uczestnik.faktura_firma %}{{ uczestnik.faktura_firma }} - {% endif %}{{ uczestnik.imie_nazwisko }}{% if not forloop.last %}, {% endif %}{% endfor %}
<br>
 - {{ termin.hybryda.lokalizacja }} - {{ termin.hybryda.ilosc_uczestnikow_rentownych_as_text }} chętnych, bieżący status czy robimy: {{ termin.hybryda.odbylo_sie|yesno:"tak,nie,nie wiadomo" }}, {{ site_url }}{% url 'admin:www_terminszkolenia_change' termin.hybryda.pk %}
    <br>
   Uczestnicy: {% for uczestnik in termin.hybryda.uczestnicy_niezrezygnowani %}{% if uczestnik.faktura_firma %}{{ uczestnik.faktura_firma }} - {% endif %}{{ uczestnik.imie_nazwisko }}{% if not forloop.last %}, {% endif %}{% endfor %}

{% with termin.ilosc_uczestnikow_dla_faktoryzacji as ilosc_uczestnikow_dla_faktoryzacji %}
    {% if ilosc_uczestnikow_dla_faktoryzacji %}
        <br><br>
        Dodatkowe zgłoszenia z faktoryzacji:
        {% for row in ilosc_uczestnikow_dla_faktoryzacji %}
            <br>{{ row.szkolenie }}: {{ row.ilosc_uczestnikow }}
        {% endfor %}

    {% endif %}
{% endwith %}

{% else %}
Odwołać chętnych na szkolenie {{ termin.szkolenie }} w terminie {{ termin.termin }} ({{ termin.lokalizacja }}). Nie rusza - jest {{ termin.ilosc_uczestnikow_rentownych_as_text }} chętnych, min_grupa {{ termin.szkolenie.min_grupa }}, bieżący status czy robimy: {{ termin.odbylo_sie|yesno:"tak,nie,nie wiadomo" }}.

{% with termin.ilosc_uczestnikow_dla_faktoryzacji as ilosc_uczestnikow_dla_faktoryzacji %}
    {% if ilosc_uczestnikow_dla_faktoryzacji %}
        <br><br>
        Dodatkowe zgłoszenia z faktoryzacji:
        {% for row in ilosc_uczestnikow_dla_faktoryzacji %}
            <br>{{ row.szkolenie }}: {{ row.ilosc_uczestnikow }}
        {% endfor %}

    {% endif %}
{% endwith %}

<br><br>
<a href="{{ site_url }}/admin/www/terminszkolenia/{{ termin.id }}/">{{ site_url }}/admin/www/terminszkolenia/{{ termin.id }}/</a>

<br>
{% for uczestnik in termin.uczestnicy_niezrezygnowani %}
    <br>{{ uczestnik.faktura_firma }} - {{ uczestnik.imie_nazwisko }}
{% endfor %}
{% endif %}

<br><br>
Pozdrowienia, <br/>
System ALX
<br/><br/>

--<br/>
ALX Academy sp. z o.o.<br/>
ul. Jasna 14/16a, 00-041 Warszawa<br/>tel. 22 63 64 164, fax 22 26 60 695<br/>
NIP 5272642198, REGON 142681010, KRS 0001001192<br/>
Sąd Rejonowy dla m. st. Warszawy XII Wydział Gospodarczy