{% load mytags %}
{{ termin.nazwa_szkolenia_lub_snz_opis|striptags }}

{% if zmieniony_termin %}
    <br>
    [na ID: {{ termin.pk }}] {{ termin.termin }}, {{ termin.lokalizacja.shortname }}, {{ termin.get_tryb_display }}{% if termin.dodatkowe_uwagi %}, {{ termin.dodatkowe_uwagi }}{% endif %}
    <br>
    [z  ID: {{ zmieniony_termin.pk }}] {{ zmieniony_termin.termin }}, {{ zmieniony_termin.lokalizacja.shortname }}, {{ zmieniony_termin.get_tryb_display }}, {{ zmieniony_termin.nazwa_szkolenia_lub_snz_opis|striptags }}{% if zmieniony_termin.dodatkowe_uwagi %}, {{ zmieniony_termin.dodatkowe_uwagi }}{% endif %}; zmienione przez: {% if moderator.get_full_name %}{{ moderator.get_full_name }} {% endif %}[{{ moderator }}]
{% else %}
     {{ termin.termin }}, {{ termin.lokalizacja.shortname }}, {{ termin.get_tryb_display }}{% if termin.dodatkowe_uwagi %}, {{ termin.dodatkowe_uwagi }}{% endif %}
    {% if moderator %}<br>Wprowadzone przez: {% if moderator.get_full_name %}{{ moderator.get_full_name }} {% endif %}[{{ moderator }}]{% endif %}
{% endif %}
<br><br>
W tej chwili w sumie zgłoszonych osób: {{ termin.ilosc_uczestnikow_rentownych_all_as_text }}{% if termin.odbylo_sie %}. Termin już potwierdzony{% if termin.jobs_state %} (potwierdzono {{ termin.jobs_state|date:'Y-m-d H:i' }}){% endif %}.{% elif termin.czy_reklamowac %}. Termin jest promowany.{% endif %}{% if uczestnik.nierentowny %} Ten uczestnik jest oznaczony jako nierentowny.{% endif %}
<br>

{% if not termin.job_state and termin.gwarantowany %}
  Termin jest gwarantowany.<br>
{% endif %}

{% with termin.ilosc_uczestnikow_dla_faktoryzacji as ilosc_uczestnikow_dla_faktoryzacji %}
    {% if ilosc_uczestnikow_dla_faktoryzacji %}
        <br>
        Dodatkowe zgłoszenia z faktoryzacji:<br>
        {% for row in ilosc_uczestnikow_dla_faktoryzacji %}
            {{ row.szkolenie }}: {{ row.ilosc_uczestnikow }}<br>
        {% endfor %}
    {% endif %}
{% endwith %}
<br>
<a href="{{ site_url_pl }}/admin/www/uczestnik/{{ uczestnik.id }}/">{{ site_url_pl }}/admin/www/uczestnik/{{ uczestnik.id }}/</a><br>
<a href="{{ site_url_pl }}{% url 'admin:www_terminszkolenia_change' termin.id %}">{{ site_url_pl }}{% url 'admin:www_terminszkolenia_change' termin.id %}</a>
<br><br>
Imię i nazwisko/lista osób:
<br><br>
{{ uczestnik.imie_nazwisko }}
<br><br>
Osoba do kontaktu: {{ uczestnik.osoba_do_kontaktu }}<br>
Email: {{ uczestnik.email }}<br>
Email do rozliczeń: {{ uczestnik.email_ksiegowosc }}<br>
{% if url_maybe %}Strona (?): {{ url_maybe }}<br>{% endif %}
Telefon: {{ uczestnik.telefon }}<br>
Adres: {{ uczestnik.adres }}<br>
Miejscowość, kod: {{ uczestnik.miejscowosc_kod }}<br>
Uczestnik prywatny: {{ uczestnik.prywatny|yesno:"tak,nie" }}<br>
Proszę o wystawienie papierowej faktury: {{ uczestnik.chce_fakture|yesno:"tak,nie" }}<br>
Proszę o drukowany certyfikat: {{ uczestnik.drukowany_certyfikat|yesno:"tak,nie" }}<br>
{% if not uczestnik.prywatny %}
    <br>
    Firma: {{ uczestnik.faktura_firma }}<br>
    Adres: {{ uczestnik.faktura_adres }}<br>
    Miejscowość, kod: {{ uczestnik.faktura_miejscowosc_kod }}<br>
    NIP: {{ uczestnik.faktura_nip }}<br>
    Podmiot publiczny: {{ uczestnik.podmiot_publiczny|yesno:"tak,nie" }}<br>
    Ilość osób: {{ uczestnik.uczestnik_wieloosobowy_ilosc_osob|default_if_none:"" }}<br>
    <br>
{% endif %}
{% if termin.autoryzacja_aktywna %}Autoryzacja: {{ uczestnik.chce_autoryzacje|yesno:"tak,nie" }}<br>{% endif %}
{% if termin.obiady == 'obiady-opcjonalne' %}Zamawiam obiady: {{ uczestnik.chce_obiady }}<br>{% endif %}
{% if termin.obiady == 'obiady-wliczone' or uczestnik.chce_obiady %}Dla ilu osób obiady wegetariańskie: {{ uczestnik.ile_obiadow_wegetarianskich|default_if_none:"brak informacji" }}<br>{% endif %}
{% if uczestnik.chce_obiady %}Obliczona cena obiadów: {{ uczestnik.cena_obiadow }}<br>{% endif %}
Za kurs zapłacę: {{ uczestnik.get_za_kurs_zaplace_display }}<br>
{% if uczestnik.wiek_uczestnika %}Wiek uczestnika: {{ uczestnik.wiek_uczestnika }}<br>{% endif %}
Byłem wcześniej: {{ uczestnik.bylem_wczesniej|yesno:"tak,nie" }}<br>
{% if uczestnik.bylem_wczesniej %}
    Byłem na: {{ uczestnik.bylem_na }}<br>
{% endif %}
{% if uczestnik.odpowiedz_na_dodatkowe_pytanie %}
    Odpowiedź na dodatkowe pytanie: {{ uczestnik.odpowiedz_na_dodatkowe_pytanie }}<br>
{% endif %}
Uwagi: {{ uczestnik.uwagi_klienta|striptags }}
<br><br>
Cena: {{ uczestnik.kwota_do_zaplaty }} {{ uczestnik.waluta.symbol }}, VAT: {{ uczestnik.stawka_vat|vat }}{% if uczestnik.stawka_vat > 0 %}, brutto: {{ uczestnik.kwota_do_zaplaty_brutto }} {% endif %}<br>
{% with uczestnik.discount_code as discount_code %}{% if discount_code %}Użyto kodu rabatowego: {{ discount_code.code }} ({{ discount_code.discount|floatformat }}%{% with discount_code.get_graduate as graduate %}{% if graduate %} wygenerowany dla: {{ graduate.participant|default:"-brak danych-" }}, termin: {{ site_url_pl }}/admin/www/terminszkolenia/{{ graduate.term.pk }}/{% endif %}{% endwith %}).<br>{% endif %}{% endwith %}
<br>
ID: {{ uczestnik.id }}<br>
Czas: {{ uczestnik.czas_dodania }}<br>
Użytkownik zapisany na powiadomienie: {% if user_subscription %}tak (źródło: {{  user_subscription.get_source_display }}){% else %}nie{% endif %}<br>
Opt-out: {% if opt_out %}TAK{% else %}nie{% endif %}<br>
Czarna/Szara lista: {% if blacklisted_links %}TAK ({{ blacklisted_links|length }}): {% for url in blacklisted_links %}{{ url|safe }} | {% endfor %}{% else %}nie{% endif %}
{% include 'www/emails/_eager_maybe.html' %}
{% include 'www/emails/_seen_before.html' %}
