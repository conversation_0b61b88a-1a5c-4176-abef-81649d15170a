{% if term.hybryda %}
    Szkolenie {{ term.szkolenie }} w terminie {{ term.termin }} ({{ term.lokalizacja }}) rusza. Jest:
    <br>
    Raze<PERSON> hybrydowo: {{ term.ilosc_uczestnikow_rentownych_z_terminem_zdalnym_as_text }} chętnych, min_grupa {{ term.szkolenie.min_grupa }}
    <br>
    W tym:
    <br><br>
    <strong>{{ term.lokalizacja }}</strong> - {{ term.ilosc_uczestnikow_rentownych_as_text }} chętnych, bieżący status czy robimy: {{ term.odbylo_sie|yesno:"tak,nie,nie wiadomo" }}, <a href="{{ protocol }}://{{ domain }}{% url 'admin:www_terminszkolenia_change' term.pk %}">{{ protocol }}://{{ domain }}{% url 'admin:www_terminszkolenia_change' term.pk %}</a>
    <br>
    U<PERSON><PERSON><PERSON><PERSON>: {% for uczestnik in term.uczestnicy_niezrezygnowani %}{% if uczestnik.faktura_firma %}{{ uczestnik.faktura_firma }} - {% endif %}{{ uczestnik.imie_nazwisko }}{% if not forloop.last %}, {% endif %}{% endfor %}
    <br><br>
    <strong>{{ term.hybryda.lokalizacja }}</strong> - {{ term.hybryda.ilosc_uczestnikow_rentownych_as_text }} chętnych, bieżący status czy robimy: {{ term.hybryda.odbylo_sie|yesno:"tak,nie,nie wiadomo" }}, <a href="{{ protocol }}://{{ domain }}{% url 'admin:www_terminszkolenia_change' term.hybryda.pk %}">{{ protocol }}://{{ domain }}{% url 'admin:www_terminszkolenia_change' term.hybryda.pk %}</a>
    Uczestnicy: {% for uczestnik in term.hybryda.uczestnicy_niezrezygnowani %}{% if uczestnik.faktura_firma %}{{ uczestnik.faktura_firma }} - {% endif %}{{ uczestnik.imie_nazwisko }}{% if not forloop.last %}, {% endif %}{% endfor %}

    {% with term.ilosc_uczestnikow_dla_faktoryzacji as ilosc_uczestnikow_dla_faktoryzacji %}{% if ilosc_uczestnikow_dla_faktoryzacji %}
    <br><br>
    Dodatkowe zgłoszenia z faktoryzacji:
    {% for row in ilosc_uczestnikow_dla_faktoryzacji %}
        <br>{{ row.szkolenie }}: {{ row.ilosc_uczestnikow }}
    {% endfor %}
    {% endif %}{% endwith %}

{% else %}
    Szkolenie {{ term.szkolenie }} w terminie {{ term.termin }} ({{ term.lokalizacja }}) rusza (jest {{ term.ilosc_uczestnikow_rentownych_as_text }} chętnych, min_grupa {{ term.szkolenie.min_grupa }}, bieżący status czy robimy: {{ term.odbylo_sie|yesno:"tak,nie,nie wiadomo" }}).
    {% with term.ilosc_uczestnikow_dla_faktoryzacji as ilosc_uczestnikow_dla_faktoryzacji %}{% if ilosc_uczestnikow_dla_faktoryzacji %}
    <br><br>
    Dodatkowe zgłoszenia z faktoryzacji:
    {% for row in ilosc_uczestnikow_dla_faktoryzacji %}
        <br>{{ row.szkolenie }}: {{ row.ilosc_uczestnikow }}
    {% endfor %}
    {% endif %}{% endwith %}
    <br><br>
    <a href="{{ protocol }}://{{ domain }}{% url 'admin:www_terminszkolenia_change' term.pk %}">{{ protocol }}://{{ domain }}{% url 'admin:www_terminszkolenia_change' term.pk %}</a>
    <br><br>

    {% for uczestnik in term.uczestnicy_niezrezygnowani %}
        {% if uczestnik.faktura_firma %}{{ uczestnik.faktura_firma }} - {% endif %}{{ uczestnik.imie_nazwisko }}<br>
    {% endfor %}
{% endif %}
<br><br>
Pozdrowienia, <br/>
System ALX
<br/><br/>

--<br/>
ALX Academy sp. z o.o.<br/>
ul. Jasna 14/16a, 00-041 Warszawa<br/>tel. 22 63 64 164, fax 22 26 60 695<br/>
NIP 5272642198, REGON 142681010, KRS 0001001192<br/>
Sąd Rejonowy dla m. st. Warszawy XII Wydział Gospodarczy