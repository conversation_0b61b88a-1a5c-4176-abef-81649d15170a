<!DOCTYPE html>
{% load myfilters %}
{% lineless %}
<html lang="{{ LANGUAGE_CODE }}">
 {% load mytags %}
 {% load myfilters %}
 {% load i18n %}
 <head>
  {% include "www/includes/google_tag_manager_head.html" %}
  <!-- block data layer -->
  {% block data_layer %}{% endblock %}
  <!-- end block data layer -->
  <script>
      var KOD = "{% if szkolenie %}{{ szkolenie.kod }}{% endif %}"
      var CENA = "{% if szkolenie %}{{ szkolenie.cena|default_if_none:"0.00"|floatformat:"0" }}{% endif %}"
  </script>
  <meta charset="utf-8">
  <!--[if IE]>
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <![endif]-->
  <title>
   {% block title %}
    ALX
   {% endblock title %}
  </title>
  {% block super_descr %}
   <meta name="description"
         content="{% block descr %}{% trans 'Szkolenia Linux, PHP, Java, programowanie, bazy danych i Office. Warszawa, Kraków, Wrocław, Łódź, Poznań, Katowice, Gdańsk. Intensywne kursy oraz profesjonalne szkolenia dla firm.' %}{% endblock descr %}" />
  {% endblock super_descr %}
  <meta name="google-site-verification"
        content="9j3rQhDGfiQZUon9Q1zBkwAxtXfzsUvVrSzxycg2Sdo" />
  <meta name="facebook-domain-verification"
        content="k1k6yyz7rbx7zcx1y6xjxxm0o0xkd4" />
  <link rel="shortcut icon" href="/static/icons/favicon.ico" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport"
        content="width=device-width, initial-scale=1, maximum-scale=5">
  <link rel="stylesheet"
        href="{% staticbuster assets/public/css/main.css %}"
        type="text/css"
        media="all">
  <script src="{% staticbuster js/jquery.min.js %}"></script>
  <link rel="stylesheet"
        href="{% staticbuster css/custom.css %}"
        type="text/css"
        media="all">
  {% include "www/alternate_versions.html" %}
  {% block extra_head %}
  {% endblock extra_head %}
  {% if dynamic_custom_header %}{{ dynamic_custom_header|safe }}{% endif %}
 </head>
 <body {% block body_attrs %}{% endblock body_attrs %}>

  <script type="application/ld+json">
  {{ schema_json|safe }}
  </script>

  {% include "www/includes/google_tag_manager_body.html" %}
  {% include 'www/facebook_pixel.html' %}
  <header>
  <div id="site-header" class="nocontent">
   <!--[if lte IE 6]>
   <div id='ie6-info'>
    Przepraszamy, ale przeglądarka Internet Explorer w wersji 6 i starszych nie jest w pełni obsługiwana przez naszą witrynę.
    Poniżej znajduje się jej uproszczona wersja, dla niewspieranych w pełni przeglądarek.
   </div>
   <![endif]-->
   {% if request.GET.ie %}
    <div id='ie6-info'>
     Przepraszamy, ale przeglądarka Internet Explorer w wersji 6 i starszych nie jest w pełni obsługiwana przez
     naszą
     witrynę. Poniżej znajduje się jej uproszczona wersja, dla niewspieranych w pełni przeglądarek.
    </div>
   {% endif %}
  <nav>
   <div class="top-nav">
    {% if search_url %}
     <form action="{{ search_url }}" method="get" class="searchbox">
      <input type="text" name="q" value="{% block search_query %}{% endblock %}" aria-label="{% trans 'Szukaj' %}"/>
      <input type="submit" value="{% trans 'Szukaj' %}" formnovalidate />
     </form>
    {% endif %}
    <ul class="nav">
     {% for t in tlumaczenia|default:default_tlumaczenia %}
      <li>
       <a href="https://{{ t.0|domain_for_language }}{{ t.1 }}">{{ t.0|pretty_lang_version }}</a>
      </li>
     {% endfor %}
    </ul>
   </div>
   {{ menu.content|safe }}
  </nav>
  </div>
  </header>
  <main>
  {% block meta_content %}
   {% if highlights %}
    <div class="lp-newTop lp-homePageTop">
     <div class="lp-newTop-content clearfix">
      {% block highlights_text %}{% endblock %}
      <!-- Highlights -->
      <div id="lp-tabs" class="lp-textBox">
       <ul>
        {% for h in highlights %}
         <li>
          <a href="#highlight-{{ h.pk }}">{{ h.highlight.tytul }}</a>
         </li>
         {% if h.initially_selected %}
          <script>
						$(document).ready(function () {
							$('a[href="#highlight-{{ h.pk }}"]').click();
						});
					
          </script>
         {% endif %}
        {% endfor %}
       </ul>
       {% for h in highlights %}
        <div id="highlight-{{ h.pk }}">
         <h3>{{ h.highlight.tytul_h2 }}</h3>
         {{ h.highlight.tresc|safe }}
        </div>
       {% endfor %}
      </div>
      <!-- END Highlights -->
      {% if pl %}<div class="lp-akredytacja">Akredytacja</div>{% endif %}
     </div>
    </div>
   {% else %}
    {% if wariant_szkolenia %}
     <div class="pages-header standard-bg">
      <div class="pages-header--inner">
       <div class="pages-header--content-left">
       {% else %}
        <div class="lp-newTop lp-newTopZbiorczy lp-newTop-background001 lp-newTopStatic">
         <div class="lp-newTop-content clearfix">
          <div class="lp-newTop-left clearfix">
           <div class="lp-newTop-header">
           {% endif %}
           {% block option_header %}
            <h2>
             {% block h1 %}
             {% endblock h1 %}
            </h2>
           {% endblock option_header %}
           {% if wariant_szkolenia %}
           </div>
          </div>
         </div>
        {% else %}
        </div>
        <div class="lp-clr"></div>
       </div>
      </div>
     </div>
    {% endif %}
   {% endif %}
  {% endblock meta_content %}
  <div id="site-content">
   {% block site_content %}
    {% block taby %}
     <div id="lp-stickyTop">
      <div id="content-header">
       {% if taby %}
        <div class="lp-left-stickyHeader">
         <div class="lp-stickyHeader-logo">Logo</div>
         <h4>
          {% block sticky_h4 %}
          {% endblock sticky_h4 %}
         </h4>
        </div>
        <div class="inner-content">
         <div class="lp-contentHeader-middle" id="js-sticky">
          <ul class="nav">
           {% if tab_as_hyperlink %}
            {% for t in taby %}
             <li class="tab tab{{ t.slug }}{% if t.slug == active_tab.slug %} current{% endif %}">
              <a href="{{ base_tab_url }}{% if not t.selected %}{{ t.slug }}/{% endif %}">{{ t.title }}</a>
             </li>
            {% endfor %}
           {% else %}
            {% for t in taby %}
             <li class="tab js-tab-anchor tab{{ t.slug }}{% if t.selected %} current{% endif %}">
              <a href="#{{ t.slug }}">{{ t.title }}</a>
             </li>
            {% endfor %}
           {% endif %}
          </ul>
          {% training_has_terms szkolenie.najblizsze_terminy_miastami as szkolenie_ma_terminy %}
         </div>
         {% if terminy or szkolenie_ma_terminy %}
          <div class="lp-zapisz-btn">
           <a href="{% url 'zgloszenie' slug=szkolenie.slug language=szkolenie.language %}"
              rel="nofollow">
            {% if szkolenie.tag_dlugosc.slug == 'szkolenie' %}
             {% trans "Zapisz się" %}
            {% else %}
             {% trans "Zapisz się na kurs" %}
            {% endif %}
           </a>
          </div>
         {% elif pokazuj_notyfikacje %}
          <div class="lp-question-btn">
           <a href="#"
              data-action="notifications-modal-run"
              data-url="{% url 'subscription_form' training_id=szkolenie.pk language=language %}">{% trans "Powiadomienie o terminach" %}</a>
          </div>
         {% endif %}
        </div>
       {% endif %}
      </div>
     </div>
    {% endblock taby %}
    {% block outerbody %}
      <article>
       <div {% block body_article_id %}id="article"{% endblock %}
            {% block body_article_class %}
            class="article{% if not site_modules %} article-full{% endif %}"
            {% endblock %}>
        {% block body %}
        {% endblock body %}
       </div>
      </article>
    {% endblock outerbody %}
    {% block aside %}
      {% if site_modules %}
       <aside>
        <div id="aside" class="nocontent">
       {% if pokazuj_notyfikacje %}
        <div class="lp-setNotification clearfix">
         <a href="#"
            data-action="notifications-modal-run"
            id="id_notifications-modal-run"
            data-url="{% url 'subscription_form' training_id=szkolenie.pk language=language %}">
          {% trans "Ustaw powiadomienie o kolejnych terminach" %}
         </a>
        </div>
       {% endif %}
       {% if pokazuj_probki_materialow %}
        <a href="" class="call-to-action-download">
         <img width="60"
              data-src="/static/images/download-icon-2.png"
              alt="download">
         <p>{% trans "Zobacz próbkę materiałów do tego szkolenia" %}</p>
         <div style="clear: both;"></div>
        </a>
       {% endif %}
       {% for sm in site_modules %}
        <div class="lp-rightSection-wrap">
          {% if sm.pokazuj_tytul %}
            <div class="lp-rightSection-header">
            <div class="aside-box-h3">{{ sm.title_synced|safe }}</div>
            <div class="aside-box-cta">{{ sm.title_cta_synced|safe }}</div>
            </div>
          {% endif %}
         {% if sm.content == 'SYLWETKI' %}
          <div class="lp-rightSection-content lp-rightSection-coach">
           {% get_wykladowcy wykladowcy 2 %}
           <ul>
            {% for w in wykladowcy %}
             <li class="clearfix">
              <a href="{% url 'wykladowcy' %}#trener-{{ w.pk }}">
               <div class="lp-coachImageWrap">
                <div class="lp-coachImage">
                 <img data-src="{{ w.sylwetka_image_url }}" alt="{{ w.tytul_imie }} trener">
                </div>
               </div>
               <div class="lp-coachName">
                <p>
                 <b>{{ w.tytul_imie }}</b>
                 <small>{{ w.tagline|safe }}</small>
                </p>
               </div>
              </a>
             </li>
            {% endfor %}
           </ul>
          </div>
         {% else %}
          {{ sm.content_synced|safe }}
         {% endif %}
        </div>
        {% if pokazuj_video_o_szkoleniach and sm.pokazuj_tytul and "kontakt" in sm.title_synced|striptags|lower %}
         <div class="lp-rightSection-wrap lp_advert_youtube">
          <div class="lp-rightSection-header">
           <div class="aside-box-h3">Historia naszego absolwenta</div>
          </div>
          <div class="lp-rightSection-content lp-rightSection-location lp-rightSection-youtubepromoContainer">
           <script>
						jQuery(function ($) {
							$('.js_youtubepromo_play').click(function (e) {
								e.preventDefault();
								$.alx.youtubeClipShow($('.js_youtubepromo_play').data('videoid'));
							})
						})
					
           </script>
           <img class="js_youtubepromo_play"
                data-videoid="2vzfSI3IopE"
                data-src="{{ STATIC_URL }}assets/public/img/youtube_clip_candidate_KatarzynaHP.jpg"
                style="display: block;
                       width: 90%;
                       height: auto;
                       margin: 0 auto;
                       cursor: pointer"
                alt="movie">
          </div>
         </div>
        {% endif %}
       {% endfor %}
      </div>
       </aside>
     {% endif %}
    {% endblock aside %}
   {% endblock site_content %}
  </div>
  </main>
  <footer>
  <div id="site-footer" class="nocontent">{{ footer.content|safe }}</div>
  </footer>
  <aside>
  {% include "www/zadaj_szybkie_pytanie.html" %}
  </aside>
  <script src="{% url 'jsi18n' %}?language={{ LANGUAGE_CODE }}"></script>
  <script>var JS_LANGUAGE_CODE = "{{ LANGUAGE_CODE }}";</script>
  <script src="{% staticbuster new/js/jquery.flexnav.min.js %}"></script>
  <script>jQuery(document).ready(function ($) { $(".flexnav").flexNav(); });</script>
  <script src="{% staticbuster new/js/utils.js %}"></script>
  <script src="{% staticbuster new/js/zdp.js %}"></script>
  {% if highlights %}
   <script src="{% staticbuster new/js/jquery-ui-1.11.4.custom.min.js %}"></script>
   <script>
		$(document).ready(function () {
			$("#lp-tabs").tabs().addClass("ui-tabs-vertical ui-helper-clearfix");
			$("#lp-tabs li").removeClass("ui-corner-top").addClass("ui-corner-left");
		});
	
   </script>
  {% endif %}
  <script src="{% staticbuster js/jquery.simplemodal.js %}"></script>
  <script src="https://www.youtube.com/iframe_api"></script>
  <script src="{% staticbuster new/js/bootcamp_youtube.js %}"></script>
  {% if pokazuj_notyfikacje %}
   <script src="{% staticbuster new/js/notifications.js %}"></script>
  {% endif %}
  <script src="{% staticbuster new/js/slick/slick.min.js %}"></script>
  <script src="{% staticbuster new/js/math-height.js %}"></script>
  <script src="{% staticbuster assets/public/js/main.js %}"></script>
  {% block extra_bottom_scripts %}
  {% endblock extra_bottom_scripts %}
 </body>
</html>
{% endlineless %}
