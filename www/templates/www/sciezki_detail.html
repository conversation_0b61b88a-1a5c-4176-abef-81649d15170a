{% extends "www/base.html" %}
{% load myfilters %}

{% block data_layer %}
  {% include "www/includes/data_layer_other.html" %}
{% endblock %}

{% block title %}{{ sciezka.nazwa }}{% endblock title %}
{% block h1 %}<h1>Ścieżka: {{ sciezka.nazwa }}</h1>{% endblock h1 %}

{% block extra_head %}
<script type="text/javascript" src='/static/js/sciezki.js?5'></script>
<script type="text/javascript">
$(function() {
	viewer_open("{{ sciezka.get_svg_url }}", "innerscrollpane-{{ sciezka.slug }}");
});
</script>
{% endblock extra_head %}

{% block body %}

<div class="sciezka_container">
{% include 'www/sciezka.html' %}
<div>
{% if sciezka.dlugi_opis %}
{{ sciezka.dlugi_opis|my_textile }}
{% else %}
{{ sciezka.opis|my_textile }}
{% endif %}
</div>
</div>
<div>
<h2>Szkolenia w ścieżce</h2>
<ul>
{% for s in sciezka.szkolenia %}
<li><a href="{{ s.get_absolute_url }}">{{ s.nazwa }}</a></li>
{% endfor %}
</ul>
</div>

{% endblock body %}
