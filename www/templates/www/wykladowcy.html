{% extends "www/base.html" %}

{% block data_layer %}
  {% include "www/includes/data_layer_other.html" %}
{% endblock %}

{% block title %}W<PERSON><PERSON><PERSON>wcy{% endblock title %}

{% block h1 %}
    <h1>W<PERSON><PERSON><PERSON>wcy</h1>

    <p>Nasze szkolenia są prowadzone przez osoby doświadczone w dzieleniu
    się wiedzą informatyczną, praktyków, autorów publikacji popularnych i naukowych w swoich dziedzinach.</p>

    <p><b>Wśród prelegentów organizowanych przez nas szkoleń znajdują się m.in.:</b></p>

    <ul>
        <li>zaawansowani administratorzy sieci; bezpośrednio należący do środowiska open source, znani, zaangażowani w projekty wolnego oprogramowania, lub np. tworzenie dystrybucji Linuksa,</li>
        <li>doświadczeni programiści i kierownicy zespołów programistycznych,</li>
        <li>analitycy i projektanci z wieloletnim doświadczeniem projektowym,</li>
        <li>deweloperzy systemów operacyjnych, w szczególności jądra systemu Linux; osoby blisko związane z konkretnie omawianym środowiskiem (np. kulturą programowania danego języka) i jego rozwojem,</li>
        <li>pracownicy naukowi.</li>
    </ul>
{% endblock h1 %}

{% block body_article_class %}class="article lp-coachAll"{% endblock body_article_class %}

{% block body %}

	<h2 class="lp-specialH2 lp-specialTrainer">Wybrane sylwetki naszych trenerów</h2>


	<div class="lp-coachAll-single">

		{% for w in wykladowcy %}
            <a name="trener-{{ w.pk }}"></a>
            <div class="lp-coachSingleWrap">
                <div class="lp-coachSingleContainer">
                    <div class="lp-singleDescription">
                            <h3>{{ w.tytul_imie }}</h3>
                            <h4>{{ w.tagline|safe }}</h4>

                            <p>
                            {{ w.sylwetka }}
                            </p>

                            {% if w.szczegolowy_opis %}<button class="more-less" id="show-coach-{{ w.pk }}" data-coach="{{ w.pk }}">więcej o trenerze</button>{% endif %}
                    </div>

                    <div class="lp-coachSingleImageWrap">
                        <div class="lp-coachSingleImage">
                            <img src="{{ w.sylwetka_big_image_url }}" alt="{{ w.tytul_imie }}">
                        </div>
                    </div>
                </div>

                {% if w.szczegolowy_opis %}

                    <div class="lp-coachSingleMore lp-infoTrener-{{ w.pk }}" >
                        {{ w.szczegolowy_opis|safe }}

                        <button class="lp-closeTrener" id="hide-coach-{{ w.pk }}" data-coach="{{ w.pk }}">zwiń informację o trenerze</button>
                    </div>
                {% endif %}
            </div>
        {% endfor %}
    </div>

{% endblock body %}
