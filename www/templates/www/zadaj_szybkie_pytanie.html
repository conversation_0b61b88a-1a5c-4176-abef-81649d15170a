{# partial #}
{% load i18n %}
{% load mytags %}


<div class="zadaj-szybkie-pytanie">
    <div class="lp-rightSlidePanel{% if LANGUAGE_CODE == "en" %} en{% endif %}">
        <div class="lp-rightSlidePanel-left">
            {% trans "Masz pytanie?" %}
        </div>

        {% get_captcha_contact_form as captcha_contact_form %}

        <div class="lp-rightSlidePanel-right">

            <!-- content formularza -->
            <div class="lp-rightSlidePanel-first" id="qcf-inner-contact">
                <p>
                {% trans "Tu możesz zadać niezobowiązujące i szybkie pytanie na temat szkolenia" %}
                </p>

                <form id="quick-contact-form" action="{% url 'post_contact_form' language=language %}" method="post">
                    {% csrf_token %}
                    <input name="imie_nazwisko" type="hidden" value="">
                    <input name="nazwa_firmy" type="hidden" value="">

                    <div>  <!--class="error" -->
                        <input class="placeholderStyle" type="text" name="email_telefon" placeholder="{% trans "Twój e-mail / numer telefonu" %}">
                        <!--<div class="error-message">Wypełnij pole</div>-->
                    </div>
                    <div class="userCode" aria-hidden="true">
                        <input class="placeholderStyle" type="text" autocomplete="off" value="" tabindex="-1" name="usercode">
                        <input class="placeholderStyle" type="text" autocomplete="off" value="" tabindex="-1" name="username">
                    </div>
                    <div>
                        <textarea name="tresc" placeholder="{% trans 'Treść wiadomości' %}"></textarea>
                        <!--<div class="error-message">Wypełnij pole</div>-->
                    </div>
                    <div class="tos">
                        <input type="checkbox" name="tos" value="1"> <span class="text"><span class="red-text"> * </span>{% blocktrans %}Wyrażam zgodę na przetwarzanie danych osobowych przez ALX Academy sp. z o.o. z siedzibą w Warszawie w celu realizacji zgłoszenia.{% endblocktrans %}</span> <span class="info-legend" data-legend='{% blocktrans %}Administratorem danych osobowych jest ALX Academy sp. z o.o. z siedzibą w Warszawie (00-041) przy ul. Jasnej 14/16. Wyrażenie zgody jest dobrowolne. Dane będą przetwarzane przez okres niezbędny do realizacji określonego celu przetwarzania. Osobie, której dane dotyczą przysługuje prawo dostępu do treści swoich danych osobowych, ich sprostowania, usunięcia lub ograniczenia przetwarzania, prawo do wniesienia sprzeciwu wobec przetwarzania, a także prawo do żądania przenoszenia danych. Podanie danych osobowych jest dobrowolne, jednak niezbędne do realizacji zgłoszenia.{% endblocktrans %}'></span>
                    </div>
                    <div id="captcha-verification-error" style="display: none; font-weight: bold; padding-top: 5px">Błędna weryfikacja. Spróbuj ponownie.</div>
                    <div>
                        {{ captcha_contact_form.captcha }}
                    </div>
                    <input name="message_body" type="text">
                    <input name="sendto" type="text">
                    <button type="submit" formnovalidate>{% trans 'Wyślij' context 'newsletter' %}</button>
                </form>
            </div>
            <!-- END content formularza -->

            <div class="lp-rightSlidePanel-second" id="qcf-inner-thanks" style="display: none;">
                    <div class="zdp-h3">{% trans "Dziękujemy" %}</div>
                    <p>{% trans "Twoja wiadomość została przesłana" %}</p>
            </div>

            <!-- content newslettera -->
            <div class="lp-rightSlidePanel-second" id="qcf-inner-newsletter" style="display: none;">
                <div class="zdp-h3">{% trans "Dziękujemy" %}</div>
                <p>{% trans "Twoja wiadomość została przesłana" %}</p>

                <div class="zdp-h3">{% trans "Czy chcesz otrzymywać nasz firmowy Newsletter?" %}</div>
                <p>{% trans "Raz w miesiącu przesyłamy informacje o technologiach związanych z naszymi szkoleniami i zmianach w naszej ofercie." %}</p>


                <form method="post" id="quick-newsletter-form" action="{% url 'newsletter_signup' language=language %}">
                    {% csrf_token %}
                    <div style='display: none'>
                        <input type="hidden" name="email" value=""/>
                        <input type="hidden" name="zrodlo_kontaktu" value="box-pytanie"/>
                        <input type="hidden" name="tos" value="1"/>
                      </div>

                    <div class="lp-newsletterRow">
                        <input type="radio" id="radio-newsletter_subscribe-y" name="radio-newsletter_subscribe" value="tak">
                        <label for="radio-newsletter_subscribe-y">
                            {% trans "Tak, chcę newsletter!" %}
                        </label>
                        <p>{% blocktrans context "newsletter" %}Wyrażam zgodę na przetwarzanie danych osobowych w postaci adresu e-mail przez ALX Academy sp. z o.o. z siedzibą w Warszawie (00-041) przy ul. Jasnej 14/16 w celu korzystania z usługi „Newsletter ALX”.{% endblocktrans %}<span class="info-legend" data-legend='{% blocktrans %}Administratorem podanego adresu e-mail jest ALX Academy sp. z o.o. z siedzibą w Warszawie (00-041) przy ul. Jasnej 14/16. Dane będą przetwarzane w celu korzystania z „Newslettera ALX” przez okres niezbędny do realizacji celu przetwarzania. Osobie, której dane dotyczą przysługuje prawo dostępu do treści swoich danych i możliwości ich poprawiania. Powyższa zgoda może być odwołana w każdym czasie, co skutkować będzie usunięciem podanego adresu e-mail z listy dystrybucyjnej usługi „Newsletter ALX”. Z subskrypcji „Newsletter ALX” można w dowolnym momencie zrezygnować, wystarczy wysłać maila na adres: <EMAIL>.{% endblocktrans %}'></span></p>
                    </div>

                    <div class="lp-newsletterRow">
                        <input type="radio" id="radio-newsletter_subscribe-n" name="radio-newsletter_subscribe" value="nie" checked="checked">
                        <label for="radio-newsletter_subscribe-n">
                            {% trans "Nie, dziękuję." %}
                        </label>
                    </div>

                    <div class="lp-newsletterRow">
                        <button type="submit" formnovalidate>{% trans 'Wyślij' context 'newsletter' %}</button>
                    </div>

                </form>
            </div>
        </div>
    </div>
</div>
