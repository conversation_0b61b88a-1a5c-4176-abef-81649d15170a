{% load myfilters %}
{% load mytags %}
{% load i18n %}
<div class="lp-courseDates-cointainer">
    <table>
        <thead>
            <tr>
                <th>{% trans "Nazwa kursu" %}</th>
                <th class="lp-courseDates">{% trans "Terminy" %}</th>
                <th class="lp-courseTime">{% trans "Czas" %}</th>
                <th class="lp-coursePrice">{% trans "Cena" %}</th>
            </tr>
        </thead>
        <tbody>
            {% for k in kursy_zawodowe %}
                {% with terminy=k.terminy %}
                    <tr class="lp-courseRow"
                        id="{% trans 'kurs' context 'slug' %}-{{ k.slug }}"
                        onclick="window.location.href='{{ k.get_absolute_url }}';" style="cursor: pointer;"
                    >
                        <td class="lp-courseName">
                            <a href="{{ k.get_absolute_url }}">{{ k.nazwa }}</a>
                            <p>{{ k.podtytul }}</p>
                            <p>
                                <span>{{ k.kod }}</span>
                            </p>
                        </td>
                        <td>
                            {% if terminy %}
                                {% terms_by_location terminy as terminy_wedlug_lokalizacji %}
                                <div class="lp-cityContainer">
                                    {% for row in terminy_wedlug_lokalizacji %}
                                        <div class="lp-cityRow clearfix">
                                            <p class="h5">{{ row.location.shortname }}</p>
                                            <ul>
                                                {% for t in row.terms %}
                                                    <li>
                                                        {{ t.termin|termin_short }}
                                                        {% if pl %}
                                                            <span data-legend="{% if t.szkolenie.language == "pl" %}Tryb{% endif %} {{ t.get_tryb_display }}">
                                                            ({{ t.tryb_as_symbol }})<span class="lp-tooltipTriangle">TooltipTriangle</span></span>
                                                            {% if pokaz_tekst_wolne_miejsca %}
                                                                {% with t.tekst_wolne_miejsca_short as tekst_wolne_miejsca %}
                                                                    {% if tekst_wolne_miejsca %}<span class="warning-text">{{ tekst_wolne_miejsca|safe }}</span>{% endif %}
                                                                {% endwith %}
                                                            {% endif %}
                                                        {% endif %}
                                                    </li>
                                                {% endfor %}
                                            </ul>
                                        </div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </td>
                        {% with k.max_czas_trwania_wariantow as max_czas_trwania_wariantow %}
                            <td class="lp-courseTime">
                                {{ k.czas_str_no_spaces|safe }}
                                {% if max_czas_trwania_wariantow and 0 %}-{{ max_czas_trwania_wariantow }}h{% endif %}
                            </td>
                            <td class="lp-coursePrice">
                                <p class="lp-coursePriceNormal">
                                    {% for kurs in terminy|przypisane_szkolenia:k %}
                                        {% with k.max_cena_wariantow as max_cena_wariantow %}
                                            {% if not forloop.first|floatformat %}<br />{% endif %}
                                            {% if max_cena_wariantow and 0 %}
                                                {# na razie wylaczamy! #}
                                                <span>
                                                    <strong>od {{ kurs|cena_info_short|safe }}
                                                        <br>
                                                    do {{ max_cena_wariantow|cena_info_short|safe }}</strong>
                                                </span>
                                            {% else %}
                                                {% if kurs.cena_przed_promocja %}
                                                    <span class="price-before-promotion">{{ kurs|cena_info_short:True|safe }}</span>
                                                    <br />
                                                {% endif %}
                                                <span {% if kurs.cena_przed_promocja %}class="promotional-price"{% endif %}>
                                                {% if LANGUAGE_CODE == "pl" %}
                                                <strong>{{ kurs|cena_info_short_with_suffix|safe }}</strong>
                                                {% else %}
                                                <strong>{{ kurs|cena_info_short|safe }}</strong>
                                                {% endif %}
                                                </span>
                                            {% endif %}
                                        {% endwith %}
                                    {% endfor %}
                                </p>
                            </td>
                        </tr>
                    {% endwith %}
                {% endwith %}
            {% endfor %}
        </tbody>
    </table>
</div>
