{% extends "www/base.html" %}
{% load myfilters mytags %}
{% block data_layer %}
  {% include "www/includes/data_layer_other.html" %}
{% endblock %}
{% block body_attrs %}
  id="graduate-stories"
{% endblock body_attrs %}
{% block title %}
  Historie absolwentów - {{ person.name }}
{% endblock title %}
{% block extra_head %}
  {{ block.super }}
  <script src="{% staticbuster new/js/graduate.js %}"></script>
{% endblock extra_head %}
{% block sticky_h4 %}
{% endblock sticky_h4 %}
{% block option_header %}
  <div class="lp-newTop-content clearfix">
  <h1>Historie naszych absolwentów.</h1>
  <h2>
   <PERSON>znaj ludzi, któ<PERSON>y nam zaufali, zrobili z nami pierwszy krok
   <br>
   i ciężko pracowali na swój sukces w IT.
  </h2>
 </div>
{% endblock option_header %}
{% block taby %}
{% endblock taby %}
{% block aside %}
{% endblock aside %}
{% block body_article_id %}
{% endblock body_article_id %}
{% block body_article_class %}
{% endblock body_article_class %}
{% block body %}
  <div class="single_person">
  <p>
   <img src="{{ person.photo.url }}">
  </p>
  <p class="the_name">
   <span>{{ person.name }}</span>
  </p>
  <div class="text-right">{{ person.text1|safe }}</div>
  <div class="text-main">{{ person.text2|safe }}</div>
 </div>
  {% if graduates %}
    <div class="graduates">
   <h1>Poznaj inne historie</h1>
   <div class="histories-slider">
    {% for graduate in graduates %}
      <div>
      <a href="{{ graduate.get_absolute_url }}">
       <img src="{{ graduate.photo.url }}">
      </a>
      <p class="the_name">
       <span><a href="{{ graduate.get_absolute_url }}">{{ graduate.name }}</a></span>
      </p>
     </div>
    {% endfor %}
   </div>
  </div>
  {% endif %}
  {% include "www/includes/first_step_contact.html" %}
{% endblock body %}
<script>
$(document).ready(function () {
  $(".histories-slider").slick({
    infinite: true,
    arrows: true,
    slidesToShow: 4,
    slidesToScroll: 1,
    responsive: [
      {
        breakpoint: 768,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 1,
        },
      },
    ],
  });
});
</script>
