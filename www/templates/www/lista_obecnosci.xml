<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<w:document xmlns:wpc="http://schemas.microsoft.com/office/word/2010/wordprocessingCanvas" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:m="http://schemas.openxmlformats.org/officeDocument/2006/math" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:wp14="http://schemas.microsoft.com/office/word/2010/wordprocessingDrawing" xmlns:wp="http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing" xmlns:w10="urn:schemas-microsoft-com:office:word" xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main" xmlns:w14="http://schemas.microsoft.com/office/word/2010/wordml" xmlns:wpg="http://schemas.microsoft.com/office/word/2010/wordprocessingGroup" xmlns:wpi="http://schemas.microsoft.com/office/word/2010/wordprocessingInk" xmlns:wne="http://schemas.microsoft.com/office/word/2006/wordml" xmlns:wps="http://schemas.microsoft.com/office/word/2010/wordprocessingShape" mc:Ignorable="w14 wp14">
<w:body>
    <w:p w:rsidR="00606BCC" w:rsidRDefault="004B46CA">
        <w:pPr>
            <w:pStyle w:val="Standard"/>
        </w:pPr>
        <w:r>
            <w:rPr>
                <w:noProof/>
                <w:lang w:eastAsia="pl-PL"/>
            </w:rPr>
            <w:drawing>
                <wp:inline distT="0" distB="0" distL="0" distR="0">
                    <wp:extent cx="2057400" cy="438116"/>
                    <wp:effectExtent l="0" t="0" r="0" b="34"/>
                    <wp:docPr id="1" name="Obraz 2"/>
                    <wp:cNvGraphicFramePr/>
                    <a:graphic xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main">
                    <a:graphicData uri="http://schemas.openxmlformats.org/drawingml/2006/picture">
                    <pic:pic xmlns:pic="http://schemas.openxmlformats.org/drawingml/2006/picture">
                    <pic:nvPicPr>
                        <pic:cNvPr id="0" name=""/>
                        <pic:cNvPicPr/>
                    </pic:nvPicPr>
                    <pic:blipFill>
                        <a:blip r:embed="rId7">
                            <a:lum/>
                            <a:alphaModFix/>
                        </a:blip>
                        <a:srcRect/>
                        <a:stretch>
                            <a:fillRect/>
                        </a:stretch>
                    </pic:blipFill>
                    <pic:spPr>
                        <a:xfrm>
                            <a:off x="0" y="0"/>
                            <a:ext cx="2057400" cy="438116"/>
                        </a:xfrm>
                        <a:prstGeom prst="rect">
                            <a:avLst/>
                        </a:prstGeom>
                        <a:noFill/>
                        <a:ln>
                            <a:noFill/>
                            <a:prstDash/>
                        </a:ln>
                    </pic:spPr>
                </pic:pic>
            </a:graphicData>
        </a:graphic>
    </wp:inline>
</w:drawing>
</w:r>
</w:p>
<w:p w:rsidR="007D3858" w:rsidRDefault="004B46CA">
    <w:pPr>
        <w:pStyle w:val="Standard"/>
        <w:rPr>
            <w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorHAnsi"/>
            <w:sz w:val="28"/>
            <w:szCs w:val="28"/>
        </w:rPr>
    </w:pPr>
    <w:r>
        <w:br/>
    </w:r>
    <w:r w:rsidRPr="004516F3">
        <w:rPr>
            <w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorHAnsi"/>
            <w:sz w:val="28"/>
            <w:szCs w:val="28"/>
        </w:rPr>
        <w:t xml:space="preserve">Lista obecności </w:t>
    </w:r>
    <w:r w:rsidR="007D3858">
        <w:rPr>
            <w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorHAnsi"/>
            <w:sz w:val="28"/>
            <w:szCs w:val="28"/>
        </w:rPr>
        <w:t xml:space="preserve">na szkoleniu </w:t>
    </w:r>
    <w:r w:rsidR="004516F3" w:rsidRPr="004516F3">
        <w:rPr>
            <w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorHAnsi"/>
            <w:sz w:val="28"/>
            <w:szCs w:val="28"/>
        </w:rPr>
        <w:t>„{{ termin.szkolenie.nazwa }}”</w:t>
    </w:r>
</w:p>
<w:p w:rsidR="00606BCC" w:rsidRDefault="004516F3">
    <w:pPr>
        <w:pStyle w:val="Standard"/>
        <w:rPr>
            <w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorHAnsi"/>
            <w:sz w:val="28"/>
            <w:szCs w:val="28"/>
        </w:rPr>
    </w:pPr>
    <w:r w:rsidRPr="004516F3">
        <w:rPr>
            <w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorHAnsi"/>
            <w:sz w:val="28"/>
            <w:szCs w:val="28"/>
        </w:rPr>
        <w:t xml:space="preserve">w dniach </w:t>
    </w:r>
    <w:r w:rsidR="004B46CA" w:rsidRPr="004516F3">
        <w:rPr>
            <w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorHAnsi"/>
            <w:sz w:val="28"/>
            <w:szCs w:val="28"/>
        </w:rPr>
        <w:t xml:space="preserve">{{ w_dniach }}</w:t>
    </w:r>
</w:p>
<w:p w:rsidR="00606BCC" w:rsidRDefault="004516F3">
    <w:pPr>
        <w:pStyle w:val="Standard"/>
        <w:rPr>
            <w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorHAnsi"/>
            <w:sz w:val="28"/>
            <w:szCs w:val="28"/>
        </w:rPr>
    </w:pPr>
    <w:r w:rsidRPr="004516F3">
        <w:rPr>
            <w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorHAnsi"/>
            <w:sz w:val="28"/>
            <w:szCs w:val="28"/>
        </w:rPr>
        <w:t xml:space="preserve">Sala: </w:t>
    </w:r>
    <w:r w:rsidR="004B46CA" w:rsidRPr="004516F3">
        <w:rPr>
            <w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorHAnsi"/>
            <w:sz w:val="28"/>
            <w:szCs w:val="28"/>
        </w:rPr>
        <w:t xml:space="preserve">{{ sala }}</w:t>
    </w:r>
</w:p>
<w:p w:rsidR="00606BCC" w:rsidRDefault="004516F3">
    <w:pPr>
        <w:pStyle w:val="Standard"/>
        <w:rPr>
            <w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorHAnsi"/>
            <w:sz w:val="28"/>
            <w:szCs w:val="28"/>
        </w:rPr>
    </w:pPr>
    <w:r>
        <w:rPr>
            <w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorHAnsi"/>
            <w:sz w:val="28"/>
            <w:szCs w:val="28"/>
        </w:rPr>
        <w:t xml:space="preserve">szkolenie w godz.</w:t>
    </w:r>
</w:p>

{% if jedyna_firma %}
<w:p w:rsidR="007D3858" w:rsidRPr="004516F3" w:rsidRDefault="007D3858">
    <w:pPr>
        <w:pStyle w:val="Standard"/>
        <w:rPr>
            <w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorHAnsi"/>
            <w:sz w:val="28"/>
            <w:szCs w:val="28"/>
        </w:rPr>
    </w:pPr>
    <w:bookmarkStart w:id="0" w:name="_GoBack"/>
    <w:bookmarkEnd w:id="0"/>
    <w:r>
        <w:rPr>
            <w:rFonts w:asciiTheme="minorHAnsi" w:hAnsiTheme="minorHAnsi" w:cstheme="minorHAnsi"/>
            <w:sz w:val="28"/>
            <w:szCs w:val="28"/>
        </w:rPr>
        <w:t>{{ jedyna_firma }}</w:t>
    </w:r>
</w:p>
{% endif %}

<w:tbl>
    <w:tblPr>
        <!-- <w:tblW w:w="8931" w:type="dxa"/> -->
        <w:tblInd w:w="-34" w:type="dxa"/>
        <w:tblLayout w:type="fixed"/>
        <w:tblCellMar>
            <w:left w:w="10" w:type="dxa"/>
            <w:right w:w="10" w:type="dxa"/>
        </w:tblCellMar>
        <w:tblLook w:val="0000" w:firstRow="0" w:lastRow="0" w:firstColumn="0" w:lastColumn="0" w:noHBand="0" w:noVBand="0"/>
    </w:tblPr>
    <w:tblGrid>
        <w:gridCol w:w="{{ szerokosc.imie_nazwisko }}"/>
        {% if z_obiadami %}
        <w:gridCol w:w="{{ szerokosc.obiady }}"/>
        {% endif %}

        {% if z_autoryzacjami %}
        <w:gridCol w:w="{{ szerokosc.autoryzacje }}"/>
        {% endif %}
	
	{% for dzien in dni %}
	
        <w:gridCol w:w="{{ szerokosc.data }}"/>
        <w:gridCol w:w="{{ szerokosc.id }}"/>
	
	{% endfor %}
	
    </w:tblGrid>
    <w:tr w:rsidR="007D3858" w:rsidTr="007D3858">
        <w:trPr>
            <w:trHeight w:val="378"/>
        </w:trPr>
        <w:tc>
            <w:tcPr>
                <w:tcW w:w="{{ szerokosc.imie_nazwisko }}" w:type="dxa"/>
                <w:tcBorders>
                    <w:top w:val="single" w:sz="4" w:space="0" w:color="000001"/>
                    <w:left w:val="single" w:sz="4" w:space="0" w:color="000001"/>
                    <w:bottom w:val="single" w:sz="4" w:space="0" w:color="000001"/>
                    <w:right w:val="single" w:sz="4" w:space="0" w:color="000001"/>
                </w:tcBorders>
                <w:shd w:val="clear" w:color="auto" w:fill="auto"/>
                <w:tcMar>
                    <w:top w:w="0" w:type="dxa"/>
                    <w:left w:w="108" w:type="dxa"/>
                    <w:bottom w:w="0" w:type="dxa"/>
                    <w:right w:w="108" w:type="dxa"/>
                </w:tcMar>
            </w:tcPr>
            <w:p w:rsidR="006F770D" w:rsidRDefault="006F770D">
                <w:pPr>
                    <w:pStyle w:val="Standard"/>
                    <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
                </w:pPr>
                <w:r>
                    <w:rPr>
                        <w:sz w:val="{{ wielkosc_liter_w_tabeli }}"/>
                    </w:rPr>
                    <w:t>Imię i nazwisko</w:t>
                </w:r>
            </w:p>
        </w:tc>

        {% if z_obiadami %}
        <w:tc>
            <w:tcPr>
                <w:tcW w:w="{{ szerokosc.obiady }}" w:type="dxa"/>
                <w:tcBorders>
                    <w:top w:val="single" w:sz="4" w:space="0" w:color="000001"/>
                    <w:left w:val="single" w:sz="4" w:space="0" w:color="000001"/>
                    <w:bottom w:val="single" w:sz="4" w:space="0" w:color="000001"/>
                    <w:right w:val="single" w:sz="4" w:space="0" w:color="000001"/>
                </w:tcBorders>
            </w:tcPr>
            <w:p w:rsidR="006F770D" w:rsidRDefault="006F770D" w:rsidP="004516F3">
                <w:pPr>
                    <w:pStyle w:val="Standard"/>
                    <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
                    <w:jc w:val="center"/>
                    <w:rPr>
                        <w:sz w:val="28"/>
                        <w:szCs w:val="28"/>
                    </w:rPr>
                </w:pPr>
            </w:p>
        </w:tc>
        {% endif %}

        {% if z_autoryzacjami %}
        <w:tc>
            <w:tcPr>
                <w:tcW w:w="{{ szerokosc.autoryzacje }}" w:type="dxa"/>
                <w:tcBorders>
                    <w:top w:val="single" w:sz="4" w:space="0" w:color="000001"/>
                    <w:left w:val="single" w:sz="4" w:space="0" w:color="000001"/>
                    <w:bottom w:val="single" w:sz="4" w:space="0" w:color="000001"/>
                    <w:right w:val="single" w:sz="4" w:space="0" w:color="000001"/>
                </w:tcBorders>
            </w:tcPr>
            <w:p w:rsidR="006F770D" w:rsidRDefault="006F770D" w:rsidP="004516F3">
                <w:pPr>
                    <w:pStyle w:val="Standard"/>
                    <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
                    <w:jc w:val="center"/>
                    <w:rPr>
                        <w:sz w:val="28"/>
                        <w:szCs w:val="28"/>
                    </w:rPr>
                </w:pPr>
            </w:p>
        </w:tc>
        {% endif %}

	{% for dzien in dni %}
	<!-- początek kolumny z datą -->
	<w:tc>
            <w:tcPr>
                <w:tcW w:w="{{ szerokosc.data }}" w:type="dxa"/>
                <w:tcBorders>
                    <w:top w:val="single" w:sz="4" w:space="0" w:color="000001"/>
                    <w:left w:val="single" w:sz="4" w:space="0" w:color="000001"/>
                    <w:bottom w:val="single" w:sz="4" w:space="0" w:color="000001"/>
                    <w:right w:val="single" w:sz="4" w:space="0" w:color="000001"/>
                </w:tcBorders>
                <w:shd w:val="clear" w:color="auto" w:fill="auto"/>
                <w:tcMar>
                    <w:top w:w="0" w:type="dxa"/>
                    <w:left w:w="108" w:type="dxa"/>
                    <w:bottom w:w="0" w:type="dxa"/>
                    <w:right w:w="108" w:type="dxa"/>
                </w:tcMar>
            </w:tcPr>
            <w:p w:rsidR="006F770D" w:rsidRDefault="006F770D" w:rsidP="004516F3">
                <w:pPr>
                    <w:pStyle w:val="Standard"/>
                    <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
                    <w:jc w:val="center"/>
                </w:pPr>
                <w:r>
                    <w:rPr>
                        <w:sz w:val="{{ wielkosc_liter_w_tabeli }}"/>
                    </w:rPr>
                    <w:t>{{ dzien }}</w:t>
                </w:r>
            </w:p>
        </w:tc>
	<!-- koniec kolumny z datą -->

	<!-- początek kolumny id -->
	<w:tc>
            <w:tcPr>
                <w:tcW w:w="{{ szerokosc.id }}" w:type="dxa"/>
                <w:tcBorders>
                    <w:top w:val="single" w:sz="4" w:space="0" w:color="000001"/>
                    <w:left w:val="single" w:sz="4" w:space="0" w:color="000001"/>
                    <w:bottom w:val="single" w:sz="4" w:space="0" w:color="000001"/>
                    <w:right w:val="single" w:sz="4" w:space="0" w:color="000001"/>
                </w:tcBorders>
                <w:shd w:val="clear" w:color="auto" w:fill="auto"/>
                <w:tcMar>
                    <w:top w:w="0" w:type="dxa"/>
                    <w:left w:w="108" w:type="dxa"/>
                    <w:bottom w:w="0" w:type="dxa"/>
                    <w:right w:w="108" w:type="dxa"/>
                </w:tcMar>
            </w:tcPr>
            <w:p w:rsidR="006F770D" w:rsidRDefault="006F770D" w:rsidP="006F770D">
                <w:pPr>
                    <w:pStyle w:val="Standard"/>
                    <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
                    <w:jc w:val="center"/>
                </w:pPr>
                <w:r>
                    <w:rPr>
                        <w:sz w:val="{{ wielkosc_liter_w_tabeli }}"/>
                    </w:rPr>
                    <w:t>id</w:t>
                </w:r>
            </w:p>
        </w:tc>
	<!-- koniec kolumny id -->
	{% endfor %}

    </w:tr>

    {% for czlowiek in ludzie %}
    <!-- początek wiersza z uczestnikiem -->
    <w:tr w:rsidR="007D3858" w:rsidTr="007D3858">
        <w:trPr>
            <w:trHeight w:val="258"/>
        </w:trPr>
	
	<!-- początek kolumny imie_nazwisko -->
        <w:tc>
            <w:tcPr>
                <w:tcW w:w="{{ szerokosc.imie_nazwisko }}" w:type="dxa"/>
                <w:tcBorders>
                    <w:top w:val="single" w:sz="4" w:space="0" w:color="000001"/>
                    <w:left w:val="single" w:sz="4" w:space="0" w:color="000001"/>
                    <w:bottom w:val="single" w:sz="4" w:space="0" w:color="000001"/>
                    <w:right w:val="single" w:sz="4" w:space="0" w:color="000001"/>
                </w:tcBorders>
                <w:shd w:val="clear" w:color="auto" w:fill="auto"/>
                <w:tcMar>
                    <w:top w:w="0" w:type="dxa"/>
                    <w:left w:w="108" w:type="dxa"/>
                    <w:bottom w:w="0" w:type="dxa"/>
                    <w:right w:w="108" w:type="dxa"/>
                </w:tcMar>
            </w:tcPr>
            <w:p w:rsidR="006F770D" w:rsidRDefault="006F770D">
                <w:pPr>
                    <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
                    <w:jc w:val="both"/>
                    <w:textAlignment w:val="auto"/>
                </w:pPr>
                <w:r>
                    <w:rPr>
                        <w:sz w:val="{{ wielkosc_liter_w_tabeli }}"/>
                    </w:rPr>
                    <w:t>{{ czlowiek.imie_nazwisko }}{% if z_certyfikatami and czlowiek.drukowany_certyfikat %} (C){% endif %}</w:t>
                </w:r>
            </w:p>
        </w:tc>
	<!-- koniec kolumny imie_nazwisko -->
	
        {% if z_obiadami %}
	<!-- początek kolumny obiady -->
        <w:tc>
            <w:tcPr>
                <w:tcW w:w="{{ szerokosc.obiady }}" w:type="dxa"/>
                <w:tcBorders>
                    <w:top w:val="single" w:sz="4" w:space="0" w:color="000001"/>
                    <w:left w:val="single" w:sz="4" w:space="0" w:color="000001"/>
                    <w:bottom w:val="single" w:sz="4" w:space="0" w:color="000001"/>
                    <w:right w:val="single" w:sz="4" w:space="0" w:color="000001"/>
                </w:tcBorders>
            </w:tcPr>
            <w:p w:rsidR="006F770D" w:rsidRDefault="006F770D">
                <w:pPr>
                    <w:pStyle w:val="Standard"/>
                    <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
                    <w:jc w:val="center"/>
                </w:pPr>
		{% if czlowiek.chce_obiady %}
		<!-- informacja o obiadach -->
                <w:r>
                    <w:rPr>
                        <w:sz w:val="{{ wielkosc_liter_w_tabeli }}"/>
                    </w:rPr>
			{% if czlowiek.chce_obiady == "normalne" or czlowiek.chce_obiady == "wege" %}
				{% if czlowiek.chce_obiady == "normalne" %}
					<w:sym w:font="Webdings" w:char="F0E4"/>
				{% else %}
					<w:sym w:font="Webdings" w:char="F02C"/>
				{% endif %}
			{% endif %}

			{% if czlowiek.chce_obiady == "rozne" %}
				<w:t>{{ czlowiek.chce_obiady_opis }}</w:t>
			{% endif %}

                </w:r>
		{% endif %}
            </w:p>
        </w:tc>
	<!-- koniec kolumny obiady -->
	{% endif %}

        {% if z_autoryzacjami %}
	<!-- początek kolumny autoryzacje -->
        <w:tc>
            <w:tcPr>
                <w:tcW w:w="{{ szerokosc.autoryzacje }}" w:type="dxa"/>
                <w:tcBorders>
                    <w:top w:val="single" w:sz="4" w:space="0" w:color="000001"/>
                    <w:left w:val="single" w:sz="4" w:space="0" w:color="000001"/>
                    <w:bottom w:val="single" w:sz="4" w:space="0" w:color="000001"/>
                    <w:right w:val="single" w:sz="4" w:space="0" w:color="000001"/>
                </w:tcBorders>
            </w:tcPr>
            <w:p w:rsidR="006F770D" w:rsidRDefault="006F770D">
                <w:pPr>
                    <w:pStyle w:val="Standard"/>
                    <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
                    <w:jc w:val="center"/>
                </w:pPr>
		{% if czlowiek.chce_autoryzacje %}
		<!-- informacja o autoryzacjach -->
                <w:r>
                    <w:rPr>
                        <w:sz w:val="{{ wielkosc_liter_w_tabeli }}"/>
                    </w:rPr>
		    <w:t>{{ czlowiek.chce_autoryzacje|yesno:"Ⓐ," }}</w:t>
                </w:r>
		{% endif %}
            </w:p>
        </w:tc>
	<!-- koniec kolumny autoryzacje -->
	{% endif %}

	{% for dzien in dni %}
	<!-- początek kolumny data -->
	<w:tc>
            <w:tcPr>
                <w:tcW w:w="{{ szerokosc.data }}" w:type="dxa"/>
                <w:tcBorders>
                    <w:top w:val="single" w:sz="4" w:space="0" w:color="000001"/>
                    <w:left w:val="single" w:sz="4" w:space="0" w:color="000001"/>
                    <w:bottom w:val="single" w:sz="4" w:space="0" w:color="000001"/>
                    <w:right w:val="single" w:sz="4" w:space="0" w:color="000001"/>
                </w:tcBorders>
                <w:shd w:val="clear" w:color="auto" w:fill="auto"/>
                <w:tcMar>
                    <w:top w:w="0" w:type="dxa"/>
                    <w:left w:w="108" w:type="dxa"/>
                    <w:bottom w:w="0" w:type="dxa"/>
                    <w:right w:w="108" w:type="dxa"/>
                </w:tcMar>
            </w:tcPr>
            <w:p w:rsidR="006F770D" w:rsidRDefault="006F770D">
                <w:pPr>
                    <w:pStyle w:val="Standard"/>
                    <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
                    <w:rPr>
                        <w:sz w:val="28"/>
                        <w:szCs w:val="28"/>
                    </w:rPr>
                </w:pPr>
            </w:p>
        </w:tc>
	<!-- koniec kolumny data -->

	<!-- początek kolumny id -->
	<w:tc>
            <w:tcPr>
                <w:tcW w:w="{{ szerokosc.id }}" w:type="dxa"/>
                <w:tcBorders>
                    <w:top w:val="single" w:sz="4" w:space="0" w:color="000001"/>
                    <w:left w:val="single" w:sz="4" w:space="0" w:color="000001"/>
                    <w:bottom w:val="single" w:sz="4" w:space="0" w:color="000001"/>
                    <w:right w:val="single" w:sz="4" w:space="0" w:color="000001"/>
                </w:tcBorders>
                <w:shd w:val="clear" w:color="auto" w:fill="auto"/>
                <w:tcMar>
                    <w:top w:w="0" w:type="dxa"/>
                    <w:left w:w="108" w:type="dxa"/>
                    <w:bottom w:w="0" w:type="dxa"/>
                    <w:right w:w="108" w:type="dxa"/>
                </w:tcMar>
            </w:tcPr>
            <w:p w:rsidR="006F770D" w:rsidRDefault="006F770D">
                <w:pPr>
                    <w:pStyle w:val="Standard"/>
                    <w:spacing w:after="0" w:line="240" w:lineRule="auto"/>
                    <w:rPr>
                        <w:sz w:val="28"/>
                        <w:szCs w:val="28"/>
                    </w:rPr>
                </w:pPr>
            </w:p>
        </w:tc>
	<!-- koniec kolumny id -->
	{% endfor %}

</w:tr>

    <!-- koniec wiersza z uczestnikiem -->
    {% endfor %}

</w:tbl>
<w:p w:rsidR="00606BCC" w:rsidRDefault="00606BCC">
    <w:pPr>
        <w:pStyle w:val="Standard"/>
        <w:rPr>
            <w:sz w:val="24"/>
            <w:szCs w:val="24"/>
        </w:rPr>
    </w:pPr>
</w:p>
<w:sectPr w:rsidR="00606BCC">
    <w:pgSz w:w="16838" w:h="11906" w:orient="landscape"/>
    <w:pgMar w:top="851" w:right="720" w:bottom="284" w:left="1134" w:header="708" w:footer="708" w:gutter="0"/>
    <w:cols w:space="708"/>
</w:sectPr>
</w:body>
</w:document>
