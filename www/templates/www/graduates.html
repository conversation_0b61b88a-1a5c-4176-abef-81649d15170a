{% extends "www/base.html" %}
{% load myfilters mytags static i18n %}
{% block data_layer %}
 {% include "www/includes/data_layer_other.html" %}
{% endblock %}
{% block body_attrs %}
 id="graduate-stories"
{% endblock body_attrs %}
{% block title %}
 Historie naszych absolwentów
{% endblock title %}
{% block extra_head %}
 {{ block.super }}
 <script src="{% staticbuster new/js/graduate.js %}"></script>
{% endblock extra_head %}
{% block sticky_h4 %}
{% endblock sticky_h4 %}
{% block option_header %}
 <div class="lp-newTop-content clearfix">
  <h1>Historie naszych absolwentów.</h1>
  <h2>
   <PERSON>znaj ludzi, któ<PERSON>y nam zaufali, zrobili z nami pierwszy krok
   <br>
   i ciężko pracowali na swój sukces w IT.
  </h2>
 </div>
{% endblock option_header %}
{% block taby %}
{% endblock taby %}
{% block aside %}
{% endblock aside %}
{% block body_article_id %}
{% endblock body_article_id %}
{% block body_article_class %}
{% endblock body_article_class %}
{% block body %}
 <div class="lp-homePageContent histories">
  <div class="verticals">
   {% if graduates.0 %}
    <div class="vertical-history">
     <a href="{{ graduates.0.get_absolute_url }}">
      <img src="{{ graduates.0.photo.url }}">
     </a>
     <p class="st-name">
      <span><a href="{{ graduates.0.get_absolute_url }}">{{ graduates.0.name }}</a></span>
     </p>
     <p>{{ graduates.0.short_info }}</p>
     <a href="{{ graduates.0.get_absolute_url }}"
        class="lp-btn-blue lp-btn-arrowLeft">Więcej</a>
    </div>
   {% endif %}
   {% if graduates.1 %}
    <div class="vertical-history">
     <a href="{{ graduates.1.get_absolute_url }}">
      <img src="{{ graduates.1.photo.url }}">
     </a>
     <p class="st-name">
      <span><a href="{{ graduates.1.get_absolute_url }}">{{ graduates.1.name }}</a></span>
     </p>
     <p>{{ graduates.1.short_info }}</p>
     <a href="{{ graduates.1.get_absolute_url }}"
        class="lp-btn-blue lp-btn-arrowLeft">Więcej</a>
    </div>
   {% endif %}
  </div>
  <br />
 </div>
 <div class="marketing-movie">
  <div class="promo-movie" data-videoid="2vzfSI3IopE">
   <div>
    <img alt="film z trenerem"
         src="/media/01ytkatarzyna.jpg"
         style="display: block;
                cursor: pointer" />
    <svg width="1792"
         height="1792"
         viewBox="0 0 1792 1792"
         xmlns="http://www.w3.org/2000/svg">
     <path d="M711 1128l484-250-484-253v503zm185-862q168 0 324.5 4.5t229.5 9.5l73 4q1 0 17 1.5t23 3 23.5 4.5 28.5 8 28 13 31 19.5 29 26.5q6 6 15.5 18.5t29 58.5 26.5 101q8 64 12.5 136.5t5.5 113.5v176q1 145-18 290-7 55-25 99.5t-32 61.5l-14 17q-14 15-29 26.5t-31 19-28 12.5-28.5 8-24 4.5-23 3-16.5 1.5q-251 19-627 19-207-2-359.5-6.5t-200.5-7.5l-49-4-36-4q-36-5-54.5-10t-51-21-56.5-41q-6-6-15.5-18.5t-29-58.5-26.5-101q-8-64-12.5-136.5t-5.5-113.5v-176q-1-145 18-290 7-55 25-99.5t32-61.5l14-17q14-15 29-26.5t31-19.5 28-13 28.5-8 23.5-4.5 23-3 17-1.5q251-18 627-18z" fill="#5395e3">
     </path>
    </svg>
   </div>
  </div>
  <div class="new-video-desc">
   <h3>
    Zrealizuj swoje marzenia
    <br />
    - tak jak Kasia
   </h3>
   <p>
    <strong>Można z sukcesem zmienić branżę i wejść do świata IT.</strong>
    <br />
    <span>
     <br />
    </span>Nasi absolwenci są tego przykładem. Jednak nic nie dzieje się samo. To spore wyzwanie, dużo pracy i&nbsp;ciągła nauka, która nie kończy się ostatniego dnia kursu. Ale jeśli jesteś zmotywowany i&nbsp;ambitny - uda Ci się! <span>
    <br />
   </span>Posłuchaj Kasi, graficzki która została programistką <span style="white-space: nowrap">front-end.</span>
  </p>
  <br />
 </div>
</div>
<div class="lp-homePageContent histories">
 {% if other_graduates %}
  <div class="graduates">
   <h1>Poznaj inne historie</h1>
   <div class="histories-slider">
    {% for graduate in other_graduates %}
     <div>
      <a href="{{ graduate.get_absolute_url }}">
       <img src="{{ graduate.photo.url }}">
      </a>
      <p class="the_name">
       <span><a href="{{ graduate.get_absolute_url }}">{{ graduate.name }}</a></span>
      </p>
     </div>
    {% endfor %}
    {% for i in empty_graduates %}
     <div class="empty">
      <img src="/media/soon.jpg">
     </div>
    {% endfor %}
   </div>
  </div>
 {% endif %}
 <div class="horizontals">
  {% if graduates.2 %}
   <div class="horizontal-history">
    <a href="{{ graduates.2.get_absolute_url }}">
     <img src="{{ graduates.2.photo.url }}">
    </a>
    <div>
     <p class="st-name">
      <span><a href="{{ graduates.2.get_absolute_url }}">{{ graduates.2.name }}</a></span>
     </p>
     <p>{{ graduates.2.short_info }}</p>
     <a href="{{ graduates.2.get_absolute_url }}"
        class="lp-btn-blue lp-btn-arrowLeft">Więcej</a>
    </div>
   </div>
  {% endif %}
 </div>
</div>
{% include "www/includes/first_step_contact.html" %}
{% endblock body %}
