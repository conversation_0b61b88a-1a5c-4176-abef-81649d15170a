{% extends "www/base.html" %}
{% load mytags myfilters %}
{% load i18n %}

{% block data_layer %}

<script>
  dataLayer.push({ gtp: null });
  dataLayer.push({ gbv: null });
  dataLayer.push({
    'event': 'gbv',
    'gbv': {
      'event_name': 'view_item_list',
      'items': [
        // pierwsze 5 pozycji kursów
        {% for obj in kursy_zawodowe|slice:"5" %}
          {
            'id': "{{ obj.kod }}",
            'google_business_vertical': 'education'
          },
        {% endfor %}
        // pierwsze 5 pozycji szkoleń
        {% for obj in lista_szkolen|slice:"5" %}
          {
            'id': "{{ obj.kod }}",
            'google_business_vertical': 'education'
          },
        {% endfor %}
        ]
      },
    'gtp': {
      'edu_pagetype': 'searchresults',
      'edu_pid': [
          {% for obj in kursy_zawodowe|slice:"5" %}"{{ obj.kod }}",{% endfor %}
          {% for obj in lista_szkolen|slice:"5" %}"{{ obj.kod }}",{% endfor %}
      ]
    }
  });
</script>
{% endblock %}



{% block sticky_h4 %}{% trans "Szkolenia" context "naglowek index_by_technologia" %}: {{ tech.nazwa }}{% endblock sticky_h4 %}
{% block title %}
    {% if tech.meta_title %}
        {{ tech.meta_title }}
    {% else %}
        {% trans "Szkolenia" %} {{ tech.nazwa }}
    {% endif %}
{% endblock title %}
{% block super_descr %}
    {% if tech.meta_description %}<meta name="description" content="{{ tech.meta_description }}" />{% endif %}
{% endblock super_descr %}
{% block extra_head %}
    {{ block.super }}
    {% if tab_as_hyperlink and active_tab and not is_a_first_tab and non_indexable_tab %}
        <meta name="robots" content="noindex,follow">
    {% endif %}
{% endblock extra_head %}
{% block extra_bottom_scripts %}
    <script src="{% staticbuster new/js/slick.min.js %}" type="text/javascript"></script>
    <script type="text/javascript">
      $(document).ready(function(){
          {% if tech_trenerzy %}
             $('.lp-newTop-coach').slick({dots: true});
          {% endif %}
      });
    </script>
{% endblock extra_bottom_scripts %}
{% block meta_content %}
    {% ocena_tech_taga tech as stars %}
    {% index_by_technologia_top tech top_highlight stars tech_trenerzy pl en %}
{% endblock meta_content %}
{% block body %}
    {% comment %}
    <a href="/rss/{{ tech.slug }}/"><img align="right" src="/static/icons/feed-icon-14x14.png" style="border:none;" alt="rss-feed-icon" /></a>
    {% endcomment %}
    {% if active_tab.id == "lista" %}
        <div class="tabcontent tabcontentlista" id="{% tab_slug 'lista' %}">
            {% if en %}
                {% flatpage "en" "oferta-anglojezyczna-wprowadzenie-include-en" %}
            {% endif %}
            {% if kursy_zawodowe and not tech.kursy_po_szkoleniach %}
                {% include 'www/kursy_zawodowe_naglowek.html' %}
                {% include 'www/kursy_zawodowe_tabelka.html' with pokaz_tekst_wolne_miejsca=1 %}
            {% endif %}
            {% if kursy_zawodowe %}
                <h2>
                    <span data-legend='{% trans "Klasyczne, typowo kilkudniowe. Odbywają się zazwyczaj w dni powszednie, w godzinach pracy." %}'>{% trans "Szkolenia" %} - {% trans "stacjonarne i zdalne" %}</span>
                </h2>
            {% else %}
                <span></span>
            {% endif %}
            {#  regroup lista_szkolen by tag_zawod as zawod_list #}
            {% include 'www/lista_szkolen.html' with dawaj_idy_szkoleniom=1 pokaz_tekst_wolne_miejsca=1 %}
            {% if kursy_zawodowe and tech.kursy_po_szkoleniach %}
                {% include 'www/kursy_zawodowe_naglowek.html' %}
                {% include 'www/kursy_zawodowe_tabelka.html' with pokaz_tekst_wolne_miejsca=1 %}
            {% endif %}
            {% if pl and tech.sciezki.all %}
                <div>
                    <h2>Powiązane ścieżki szkoleniowe</h2>
                    {% with tech.sciezki.all as sciezki %}
                        {% include 'www/sciezki_linki.html' %}
                    {% endwith %}
                </div>
            {% endif %}
            {% flatpage language "legenda" %}
            {% if tech.opis %}
                <div class="tabcontent tabcontentinfo" id="{% tab_slug 'info' %}">
                    <div>{{ tech.opis|safe }}</div>
                </div>
            {% endif %}
        </div>
    {% endif %}
    {% if active_tab.id == "zamkniete" %}
        <div class="tabcontent tabcontentzamkniete"
             id="{% tab_slug 'zamkniete' %}">{% flatpage language "zamkniete-wyjazdowe" %}</div>
    {% endif %}
    {% if active_tab.id == "konsultacje" %}
        <div class="tabcontent tabcontentkonsultacje"
             id="{% tab_slug 'konsultacje' %}">{% flatpage language "konsultacje-include" %}</div>
    {% endif %}
    {% if active_tab.id == "rabaty" %}
        <div class="tabcontent tabcontentrabaty" id="{% tab_slug 'rabaty' %}">
            {% flatpage language "rabaty" %}
            {% if kursy_zawodowe %}
                {% flatpage language "platnosc-ratalna-za-kursy" %}
            {% endif %}
        </div>
    {% endif %}
    {% if highlights and highlights|length > 1 %}
        <div id="lp-tabs" class="lp-textBox clearfix">
            <p class="as-h2">
                {% trans "<strong>Zobacz</strong> też" %}
            </p>
            <div class="lp-seeMore-Wrap">
                <ul>
                    {% for h in highlights %}
                        {% if not h.initially_selected %}
                            <li>
                                <a href="#highlight{{ h.id }}">{{ h.highlight.tytul }}</a>
                            </li>
                        {% endif %}
                    {% endfor %}
                </ul>
            </div>
            {% for h in highlights %}
                {% if not h.initially_selected %}
                    <div id="highlight{{ h.id }}">
                        <h3>{{ h.highlight.tytul_h2 }}</h3>
                        {{ h.highlight.tresc|safe }}
                        {% if h.highlight.obrazek %}
                            <ul class="lp-newTop-certifications">
                                <li>
                                    <img src="/uploads/{{ h.highlight.obrazek.name }}">
                                </li>
                            </ul>
                        {% endif %}
                        {% if h.highlight.link %}
                            <p>
                                <a class="lp-tabsMoreLink" href="{{ h.highlight.link }}">{% trans "Czytaj więcej" %}</a>
                            </p>
                        {% endif %}
                    </div>
                {% endif %}
            {% endfor %}
        </div>
    {% endif %}
{% endblock body %}
