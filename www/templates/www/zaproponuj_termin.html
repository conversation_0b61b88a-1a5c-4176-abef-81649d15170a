{% extends "www/base.html" %}
{% load i18n %}

{% load myfilters %}

{% block title %}
{% trans "Zaproponuj termin szkolenia" %}
{% endblock %}

{% block h1 %}
<h1>{% trans "Zaproponuj termin szkolenia" %}</h1>
{% endblock %}

{% block extra_head %}
    {{ block.super }}
    <meta name="robots" content="noindex,follow">
{% endblock extra_head %}


{% block body %}

<h2>
  {{ szkolenie.nazwa }}
  {% comment %}
  <span class="subtitle">
    <strong class='course-code'>
      (kod: {% endcomment %}{{ szkolenie.kod }}{% comment %})
    </strong>
  </span>
  {% endcomment %}
</h2>

<form class="formularz_zgloszeniowy form" action="" method="POST">
  {% csrf_token %}
  <ul class='fieldset'>
    <li class='field'>
      {{ form.termin.errors }}
      <span class="label">{{ form.termin.label_tag }}</span>
      {{ form.termin }}
    </li>
    <li class='field'>
      {{ form.lokalizacja.errors }}
      <span class="label">{{ form.lokalizacja.label_tag }}</span>
      {{ form.lokalizacja }}
    </li>
    <li class='field'>
      {{ form.liczba_osob.errors }}
      <span class="label">{{ form.liczba_osob.label_tag }}</span>
      {{ form.liczba_osob }}
    </li>
    <li class='field required'>
      {{ form.imie_nazwisko.errors }}
      <span class="label">{{ form.imie_nazwisko.label_tag }}</span>
      {{ form.imie_nazwisko }}
    </li>
    <li class='field required'>
      {{ form.email.errors }}
      <span class="label">{{ form.email.label_tag }}</span>
      {{ form.email }}
    </li>
    <li class='field'>
      {{ form.kontakt.errors }}
      <span class="label">{{ form.kontakt.label_tag }}</span>
      {{ form.kontakt }}
    </li>
    <li class='field'>
      {{ form.nazwa_firmy.errors }}
      <span class="label">{{ form.nazwa_firmy.label_tag }}</span>
      {{ form.nazwa_firmy }}
    </li>
    <li class='field'>
      {{ form.uwagi.errors }}
      <span class="label">{{ form.uwagi.label_tag }}</span>
      {{ form.uwagi }}
    </li>
    <li class='field control required'>
      {{ form.captcha.errors }}
      <span class="label">{{ form.captcha.label_tag }}</span>
      {{ form.captcha }}
    </li>
      <li class='field'>
          <p>
              <span style="font-size: 11px">{% blocktrans %}Administratorem podanych w formularzu danych osobowych jest ALX Academy sp. z o.o. z siedzibą w Warszawie (00-041) przy ul. Jasnej 14/16. („Administrator“). Dane osobowe przetwarzane będą w zakresie niezbędnym do wykonania umowy, której stroną jest osoba, której dane dotyczą, lub do podjęcia działań na żądanie osoby, której dane dotyczą, przed zawarciem umowy (art. 6 ust. 1 b Ogólnego rozporządzenia Rady (UE) i PE o ochronie danych osobowych). Dane będą przetwarzane przez okres niezbędny do realizacji określonego celu przetwarzania. Osobie, której dane dotyczą przysługuje prawo dostępu do treści swoich danych osobowych, ich sprostowania, usunięcia lub ograniczenia przetwarzania, prawo do wniesienia sprzeciwu wobec przetwarzania, a także prawo do żądania przenoszenia danych. Podanie danych osobowych jest dobrowolne, jednak niezbędne do podjęcia działań przed zawarciem umowy.{% endblocktrans %}</span>
          </p>
      </li>
    <li class='field control'>
      <input type="submit" value="{% trans 'Wyślij' %}" formnovalidate />
    </li>
  </ul>
  <p class='form-legend'>* - {% trans "pola wymagane" %}</p>
</form>

<script>
dataLayer.push({ gtp: null });
dataLayer.push({
  'event': 'gtp',
  'gtp': {
    'edu_pagetype': 'Lead',
    'edu_pid': '{{szkolenie.kod}}',
    'edu_totalvalue': {{ szkolenie.cena|default_if_none:"0.00"|floatformat:"0" }},
    'edu_plocid': 'Warszawa'
  }
});

</script>

{% endblock body %}
