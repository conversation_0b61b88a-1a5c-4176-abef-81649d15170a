<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
    "http://www.w3.org/TR/html4/loose.dtd">
<html>
    <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <style>
        @page {
          size: a4 landscape;
          /*background-image: url({{ STATIC_PDF_ROOT }}/certificate_bg.pdf);*/
          margin-left: 100px;
          margin-right: 100px;
          margin-top: 100px;
          margin-bottom: 80px;
        }

        h1 {
          -pdf-outline: false;
        }

        @font-face {
           font-family: "DejaVu";
           src: url({{ STATIC_PDF_ROOT }}/dejavu-sans.ttf);
        }

        @font-face {
           font-family: "Aver";
           src: url({{ STATIC_PDF_ROOT }}/aver.ttf);
        }

        @font-face {
           font-family: "Aver";
           src: url({{ STATIC_PDF_ROOT }}/aver_bold.ttf);
           font-weight: bold;
        }

        @font-face {
           font-family: "Aver";
           src: url({{ STATIC_PDF_ROOT }}/aver_italic.ttf);
           font-style: italic;
        }

        @font-face {
           font-family: "Aver";
           src: url({{ STATIC_PDF_ROOT }}/aver_bold_italic.ttf);
           font-weight: bold;
           font-style: italic;
        }

        @font-face {
           font-family: "Antp";
           src: url({{ STATIC_PDF_ROOT }}/antpr.ttf);
        }

        @font-face {
           font-family: "Antp";
           src: url({{ STATIC_PDF_ROOT }}/antpb.ttf);
           font-weight: bold;
        }

        @font-face {
           font-family: "Antp";
           src: url({{ STATIC_PDF_ROOT }}/antpri.ttf);
           font-style: italic;
        }

        @font-face {
           font-family: "Antp";
           src: url({{ STATIC_PDF_ROOT }}/antpbi.ttf);
           font-weight: bold;
           font-style: italic;
        }

        html, body, table, caption, tbody, tfoot, thead, tr, th, td {
            font-family: Antp;
            line-height: normal;
        }

        p {
            margin: 0;
            padding: 0;
        }

        .logo {
            display: block;
            font-family: DejaVu;
            font-size: 70px;
        }

        .red {
            color: red;
        }

        .green {
            color: green;
        }

        .black {
            color: black;
        }

        .text-layer {
            left: 0px;
            right: 0px;
            bottom: 0px;
            top: 0px;
            color: #000;
            position: absolute;
            font-family: Antp;
            overflow: hidden;
            overflow-x: hidden;
            overflow-y: hidden;
            width: 1403px;
            height: 991px;
        }

        .head {
            text-align: center;
            font-size: 35px;
            margin-top: 50px;
            margin-bottom: 40px;
        }

        .head-more-space {
            text-align: center;
            font-size: 35px;
            margin-top: 80px;
            margin-bottom: 40px;
        }

        .name {
            text-align: center;
            font-size: 60px;
            font-weight: bold;
        }

        .normal {
            text-align: center;
            font-size: 17px;
            margin-top: 20px;
        }

        .subject {
            text-align: center;
            font-size: 30px;
            margin-top: 20px;
            font-weight: bold;
        }

        .small {
            font-size: 12px;
            margin-left: 200px;
            margin-right: 200px;
            text-align: justify;
            width: 700px;
            margin-top: 20px;
        }

        .person-title-more-space, .person-title, .person-name, .person-comp {
            font-size: 16px;
            margin-left: 600px;
            text-align: center;
            max-width: 100px;
        }

        .person-title {
            border-top: 1px solid black;
            margin-top: 35px;
            padding: 10px;
            font-style: italic;
        }

        .person-title-more-space {
            border-top: 1px solid black;
            margin-top: 80px;
            padding: 10px;
            font-style: italic;
        }

        .person-name {
            margin-top: 30px;
        }

        .person-comp {
            margin-left: 600px;
        }
    </style>
    <body>
        {% for content in contents %}
            {{ content|safe }}
            {% if not forloop.last %}
                <pdf:nextpage />
            {% endif %}
        {% endfor %}
    </body>
</html>