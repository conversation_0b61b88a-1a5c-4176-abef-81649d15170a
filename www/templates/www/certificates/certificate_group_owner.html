<!DOCTYPE html>
<html>
{% load i18n %}
	<head>
		<meta charset="utf-8">
		<title></title>
		<script src="{{ STATIC_URL}}js/jquery.min.js"></script>
        <script src="{{ STATIC_URL}}js/csrf.js"></script>
        <script src="{{ STATIC_URL}}js/certificates.js"></script>
	</head>

	<body>

    {% for form in form_set %}
        <h1>{{ form.instance.name }}</h1>
        <form class="change-certificate" action="{% url 'change_certificate' language=language slug=instance.slug key=instance.key.hex certificate_pk=form.instance.pk %}" method="POST">
            {% csrf_token %}
            {{ form.email }}
            <div class="errors"></div>
            {% if not form.instance.mail_sent_at %}
                <input class="submit-certificate-form" type="submit" value="{% trans 'Wyślij' %}" formnovalidate>
                <div class="success" style="display: none">{% trans "Certyfikat został wysłany." %}</div>
            {% else %}
                <p>{% trans "Wysłano dnia" %}: {{ form.instance.mail_sent_at }}.</p>
            {% endif %}
        </form>
    {% endfor %}


</body>
</html>