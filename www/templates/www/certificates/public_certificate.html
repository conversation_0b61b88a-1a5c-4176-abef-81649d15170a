{% extends "www/certificates/certificate.html" %}
{% load i18n %}

{% block data_layer %}
  {% include "www/includes/data_layer_other.html" %}
{% endblock %}

{% block title %}{% trans "Certyfikat" %} - {{ certificate.name }}{% endblock title %}

{% block extra_head %}
    {{ block.super }}

    {% if language == "pl" %}
        <meta property="og:title" content="{{ certificate.name }} ukończył(a) {% if term.is_szkolenie %}szkolenie{% else %}kurs{% endif %} - {{ term.nazwa_szkolenia_lub_snz }}" />
        <meta property="og:description" content="Elektroniczny certyfikat ukończenia {% if term.is_szkolenie %}szkolenia{% else %}kursu{% endif %} {{ term.nazwa_szkolenia_lub_snz }}, zorganizowanego w terminie {% if term_days > 1 %}{% if term_date.0.month == term_date.1.month and term_date.0.year == term_date.1.year %}{{ term_date.0|date:"j" }} - {{ term_date.1|date:"j E Y"|lower }}{% else %}{{ term_date.0|date:"j E Y"|lower }} - {{ term_date.1|date:"j E Y"|lower }}{% endif %}{% else %}{{ term_date.0|date:"j E Y"|lower }}{% endif %} przez firmę szkoleniową ALX." />
    {% else %}
        <meta property="og:title" content="{{ certificate.name }} completed the training - {{ term.nazwa_szkolenia_lub_snz }}" />
        <meta property="og:description" content="e-Certificate {{ term.nazwa_szkolenia_lub_snz }}, organized by ALX Training on {% if term_days > 1 %}{% if term_date.0.month == term_date.1.month and term_date.0.year == term_date.1.year %}{{ term_date.0|date:"j" }} - {{ term_date.1|date:"j E Y" }}{% else %}{{ term_date.0|date:"j E Y" }} - {{ term_date.1|date:"j E Y" }}{% endif %}{% else %}{{ term_date.0|date:"j E Y" }}{% endif %}." />
    {% endif %}
    <meta property="og:site_name" content="ALX"/>
    <meta property="og:url" content="{{ protocol }}://{{ domain }}{% url 'public_certificate' slug=certificate.slug public_key=certificate.public_key.hex language=language %}" />
    <meta property="og:image" content="{{ protocol }}://{{ domain }}{{ STATIC_URL }}assets/public/img/certyfikatImg.jpg" />
    <meta property="fb:app_id" content="{{ facebook_app_id }}" />
{% endblock extra_head %}

{% block share_button %}{% endblock share_button %}