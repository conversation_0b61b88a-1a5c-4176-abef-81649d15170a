{% extends "www/base.html" %}
{% load i18n %}

{% block data_layer %}
  {% include "www/includes/data_layer_other.html" %}
{% endblock %}

{% block title %}{% trans "Zarządzaj certyfikatem" %}{% endblock title %}

{% block extra_head %}
    {{ block.super }}
    <meta name="robots" content="noindex,nofollow" />
{% endblock extra_head %}

{% block meta_content %}
    <script>
      window.fbAsyncInit = function() {
        FB.init({
          appId            : '{{ facebook_app_id }}',
          autoLogAppEvents : true,
          xfbml            : true,
          version          : 'v3.1'
        });
      };

      (function(d, s, id){
         var js, fjs = d.getElementsByTagName(s)[0];
         if (d.getElementById(id)) {return;}
         js = d.createElement(s); js.id = id;
         js.src = "https://connect.facebook.net/pl_PL/sdk.js";
         fjs.parentNode.insertBefore(js, fjs);
       }(document, 'script', 'facebook-jssdk'));
    </script>

	<div class="lp-newTop lp-newTop-background002">
		<div class="lp-newTop-content clearfix">

			<div class="lp-newTop-left clearfix">

				<div>
                    {% if language == "pl" %}
    					<h1>{% trans "e-Certyfikat" %}</h1>
    					<p>
                            {% blocktrans with years_delta as years %}
    						To elektroniczne potwierdzenie szkolenia zrealizowanego przez ALX.<br>
                                - jesteśmy na rynku szkoleń informatycznych od {{ years }} lat<br>
                                - szkolenia prowadzone są przez wybitnych specjalistów<br>
                                - posiadamy wpis do rejestru instytucji szkoleniowych (RIS)
                            {% endblocktrans %}
    					</p>
                    {% endif %}
				</div>

				<div class="lp-clr"></div>
			</div>

		</div>
	</div>
{% endblock meta_content %}

{% block site_content %}
<div class="lp-certyfikatWrap certificate">

	<div class="lp-certyfikatTop clearfix">
		<img class="lp-certyfikatLogo" src="{{ STATIC_URL }}assets/public/img/certyfikat-logo.png" alt="ALX" />

        {% block share_button %}
            <ul class="lp-certyfikatShare">
                {% if certificate.pdf %}
                    <li class="lp-certyfikatPDF lp-certyfikatShareBtn"><a href="{% url 'get_certificate_pdf' slug=certificate.slug key=certificate.key.hex %}">{% trans "Pobierz PDF" %}</a></li>
                {% endif %}

                <li class="lp-certyfikatLink lp-certyfikatShareBtn"><a href="#">{% trans "Uzyskaj link do udostępniania" %}</a></li>

				<div id="simplemodal-container" class="lp-modal">
                    <div class="lp-modalHeader">
                        <h2>{% trans "Link do publicznej wersji Twojego certyfikatu" %}</h2>
                    </div>
                    <div class="modal-content">
						<p class="lp-linkHeader">
							{% trans "Skopiuj poniższy link i wklej gdziekolwiek zechcesz. Każdy kto otrzyma ten link zobaczy Twój certyfikat." %}
						</p>
						<p class="lp-linkCertificate">
							<a href="{{ protocol }}://{{ domain }}{% url 'public_certificate' slug=certificate.slug public_key=certificate.public_key.hex language=language %}">{{ protocol }}://{{ domain }}{% url 'public_certificate' slug=certificate.slug public_key=certificate.public_key.hex language=language %}</a>
						</p>
                    </div>
				</div>
                <li><a class="lp-certyfikatSocial lp-certyfikatShareFb" href="#" id="facebook-share-link"> </a></li>
                <li><a class="lp-certyfikatSocial lp-certyfikatShareIn" href="{{ linkedin_certificate_url }}&pfCertificationName={{ certificate.term.nazwa_szkolenia_lub_snz_en }}&pfCertificationUrl={{ protocol }}://{{ domain }}{% url 'public_certificate' slug=certificate.slug public_key=certificate.public_key.hex language=language %}&pfLicenseNo={{ certificate.number }}&pfCertStartDate={{ term_date.0|date:"Ym" }}&pfCertFuture={% if term_days > 1 %}{{ term_date.1|date:"Ym" }}{% else %}{{ term_date.0|date:"Ym" }}{% endif %}&trk=onsite_longurl"> </a></li>
            </ul>
        {% endblock share_button %}
	</div>

	<div class="lp-certyfikatHeader clearfix">
		<h3>{% trans "Certyfikat" %}</h3>
		<p>
			{% trans "Numer certyfikatu" %}: <strong>{{ certificate.number }}</strong>
		</p>
	</div>

	<div class="lp-certyfikatPerson clearfix">
		<h4>{{ certificate.name }}</h4>
		<p>
            {% if language == "pl" %}
			    ukończył(a) {% if term.is_szkolenie %}szkolenie{% else %}kurs{% endif %}:
            {% else %}
                completed the training:
            {% endif %}
		</p>
	</div>

	<div class="lp-certyfikatCourse clearfix">
		<h4>{{ term.nazwa_szkolenia_lub_snz }}</h4>
		<p>
            {% if language == "pl" %}
                {% if term.is_szkolenie %}zorganizowane{% else %}zorganizowany{% endif %} przez <strong>ALX</strong> w {% if term_days > 1 %}dniach{% else %}dniu{% endif %}
                <strong>
                    {% if term_days > 1 %}
                        {% if term_date.0.month == term_date.1.month and term_date.0.year == term_date.1.year %}
                            {{ term_date.0|date:"j" }} - {{ term_date.1|date:"j E Y"|lower }} r.
                        {% else %}
                            {{ term_date.0|date:"j E Y"|lower }} - {{ term_date.1|date:"j E Y"|lower }} r.
                        {% endif %}
                    {% else %}
                        {{ term_date.0|date:"j E Y"|lower }} r.
                    {% endif %}
                </strong>
            {% else %}
                organized by ALX Training on
                <strong>
                    {% if term_days > 1 %}
                        {% if term_date.0.month == term_date.1.month and term_date.0.year == term_date.1.year %}
                            {{ term_date.0|date:"j" }} - {{ term_date.1|date:"j E Y" }}
                        {% else %}
                            {{ term_date.0|date:"j E Y" }} - {{ term_date.1|date:"j E Y" }}
                        {% endif %}
                    {% else %}
                        {{ term_date.0|date:"j E Y" }}
                    {% endif %}
                </strong>
            {% endif %}
		</p>
	</div>

    <div class="lp-certyfikatInformation{% if not certificate.template %} lp-seeProgram{% endif %} clearfix">
        {% if not term.is_snz %}
            {% if certificate.template %}
                <h5><span>{% trans "Ramowy program kursu" %}</span></h5>
                {{ certificate.template|safe }}
            {% endif %}
            <a href="{% url 'detail_pdf' slug=term.szkolenie.slug language=term.szkolenie.language %}" class="lp-certyfikatCoachMore">
                {% if certificate.template %}
                    {% trans "czytaj dalej" %}
                {% else %}
                    {% trans "Ramowy program kursu" %}
                {% endif %}
                <span>»</span>
            </a>
        {% else %}
            {% if certificate.template %}
                <h5><span>{% trans "Ramowy program kursu" %}</span></h5>
                {{ certificate.template|safe }}
            {% endif %}
        {% endif %}
    </div>

    <div class="lp-certyfikatInformation lp-certyfikatCoach clearfix">
        {% if term.prowadzacy %}
            <h5><span>{% trans "Trener" %} <strong>{{ term.prowadzacy.imie_naziwsko }}</strong></span></h5>

            {% if language != "en" %}

                {% if term.prowadzacy.opis_do_certyfikatow %}
                    <p>{{ term.prowadzacy.opis_do_certyfikatow }}</p>
                {% endif %}

                {% if term.prowadzacy.szczegolowy_opis %}
                    <div class="lp-certyfikatCoachInfo clearfix">
                        {{ term.prowadzacy.szczegolowy_opis|safe }}
                    </div>

                    <button class="lp-certyfikatCoachMore" id="id-lp-certyfikatCoachMore">
                        <div class="lp-moreTxt">{% trans "Więcej informacji o trenerze" %} <span>»</span></div>
                        <div class="lp-lessTxt">{% trans "Zwiń informację o trenerze" %} <span>»</span></div>
                    </button>
                 {% endif %}
             {% endif %}
        {% endif %}


		<div class="lp-certyfikatSignature clearfix">
            {% if term.prowadzacy %}
                <div class="lp-certyfikatSignature-left"{% if term.prowadzacy.podpis %} style="margin-top: 0;"{% endif %}>
                    {% if term.prowadzacy.podpis %}
                        <img src="{{ term.prowadzacy.podpis.certyfikat.url }}" alt="Signature" />
                    {% endif %}
                    <h4>{{ term.prowadzacy.imie_naziwsko }}</h4>
                    {% trans "Trener" %}
                </div>
            {% endif %}
			<div class="lp-certyfikatSignature-right">
				<img src="{{ STATIC_URL }}assets/public/img/certyfikat-podpis.jpg" alt="Signature" />
                {% if language == "pl" %}
				    <h4>Kierownik Placówki Szkoleniowej</h4>
				    ALX Academy sp. z o.o.
                {% else %}
                    <h4>ALX Training Manager</h4>
                    ALX Academy
                {% endif %}
			</div>
		</div>


	</div>

	<div class="lp-certyfikatStripe"></div>

</div>
{% endblock site_content %}

{% block extra_bottom_scripts %}

  <script type="text/javascript">
	$(document).ready(function(){
		$("#id-lp-certyfikatCoachMore").click(function(){
			$(".lp-certyfikatCoachInfo").toggleClass("lp-certyfikatCoachAddInfo");
			$(".lp-certyfikatCoachMore").toggleClass("lp-certyfikatCoachChangeBtn");
		});

        $('#facebook-share-link').click(function(event){
            event.preventDefault();
            event.stopPropagation();
            FB.ui({
                method: 'share',
                href: '{{ protocol }}://{{ domain }}{% url 'public_certificate' slug=certificate.slug public_key=certificate.public_key.hex language=language %}'
            }, function(response){});
        });
        $(".lp-certyfikatLink").click(function(){
            $("#simplemodal-container").modal({onOpen: function (dialog) {
                dialog.overlay.fadeIn('slow', function () {
                    dialog.container.slideDown('slow', function () {
                        dialog.data.fadeIn('slow');
                    });
                });
            }});
        });
	});
  </script>
{% endblock extra_bottom_scripts %}
