{% load mytags myfilters %}
{% load i18n %}
<!-- top B1 -->
<div class="pages-header{% if not tech.top_background %} standard-bg{% endif %}">
    <div class="pages-header--inner">

        <div class="pages-header--content-left">
                    <h1>{{ top_highlight.highlight.tytul_h2 }}</h1>

                    {{ top_highlight.highlight.tresc|safe }}

                    {% if top_highlight.highlight.link %}
                        <p>
                            <a class="lp-tabsMoreLink" href="{{ top_highlight.highlight.link }}">{% trans "Czytaj więcej" %}</a>
                        </p>
                    {% endif %}

                    {% if top_highlight.highlight.obrazek %}
                        <ul class="certifications">
                            <li><img src="/uploads/{{ top_highlight.highlight.obrazek.name }}"></li>
                        </ul>
                    {% endif %}


                {% if stars.count %}
                    <div class="pages-header--new-people">
                        <p>
                            {% blocktrans with count=stars.count %}wys<PERSON><PERSON><PERSON><PERSON>śmy już <strong>{{ count }} osób</strong>{% endblocktrans %}
                        </p>
                        <img data-src="https://www.alx.pl/media/diamenty-white.png" alt="ALX Diamenty Forbes" id="diamonds" class="hide" style="width: 150px;"/>
                    </div>
                {% endif %}

        </div>

        <div class="pages-header--content-right coach">
            {% if stars.avg %}
            <div class="pages-header--rating">
                <div class="rate">
                    {{ stars.avg }}<span>/5</span>
                </div>
                <div class="stars-cointaner">
                    <div class="stars stars-{{ stars.css }}">Stars</div>
                </div>
            </div>
        {% endif %}
                <div class="lp-newTop-coachWrap">
                    <h3><span>{% trans "Trenerzy" %}</span></h3>

                    <div class="lp-newTop-coach">
                        {% for t in tech_trenerzy %}
                            <div class="lp-newTop-singleCoach">
                                <div class="lp-newTop-coachImg">
                                    {% if pl %}
                                        <a href="{% url 'wykladowcy' %}#trener-{{ t.pk }}"><img src="{{ t.sylwetka_big_image_url }}" alt="{{ t.tytul_imie }}"></a>
                                    {% else %}
                                        <img src="{{ t.sylwetka_big_image_url }}" alt="{{ t.tytul_imie }}">
                                    {% endif %}
                                </div>
                                <p>
                                    <strong>{{ t.tytul_imie }}</strong>
                                    {{ t.tagline|safe }}
                                </P>
                            </div>
                        {% endfor %}
                    </div>
                </div>
        </div>
    </div>
</div>

<script src="{% staticbuster new/js/diamonds.js %}"></script>
