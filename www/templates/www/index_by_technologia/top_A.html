{% load mytags myfilters %}
{% load i18n %}
<!-- top A -->
{% if wariant_szkolenia %}
<div class="pages-header{% if not tech.top_background %} standard-bg{% endif %}">
    <div class="pages-header--inner">

        <div class="pages-header--content-left">

                    <h1>{{ top_highlight.highlight.tytul_h2 }}</h1>

                    {{ top_highlight.highlight.tresc|safe }}

                    {% if top_highlight.highlight.link %}
                        <p>
                            <a class="lp-tabsMoreLink" href="{{ top_highlight.highlight.link }}">{% trans "Czytaj więcej" %}</a>
                        </p>
                    {% endif %}

                    {% if top_highlight.highlight.obrazek %}
                        <ul class="certifications">
                            <li><img src="/uploads/{{ top_highlight.highlight.obrazek.name }}"></li>
                        </ul>
                    {% endif %}

        </div>

        <div class="pages-header--content-right coach">

            {% if stars.avg %}
                <div class="pages-header--rating">
                    <div class="rate">
                        {{ stars.avg }}<span>/5</span>
                    </div>
                    <div class="stars-cointaner">
                        <div class="stars stars-{{ stars.css }}">Stars</div>
                    </div>
                </div>
            {% endif %}

            {% if tech_trenerzy %}

                <div class="lp-newTop-coachWrap">
                    <h3><span>{% trans "Trenerzy" %}</span></h3>

                    <div class="lp-newTop-coach">
                        {% for t in tech_trenerzy %}
                            <div class="lp-newTop-singleCoach">
                                <div class="lp-newTop-coachImg">
                                    {% if pl %}
                                        <a href="{% url 'wykladowcy' %}#trener-{{ t.pk }}"><img src="{{ t.sylwetka_big_image_url }}" alt="{{ t.tytul_imie }}"></a>
                                    {% else %}
                                        <img src="{{ t.sylwetka_big_image_url }}" alt="{{ t.tytul_imie }}">
                                    {% endif %}
                                </div>
                                <p>
                                    <strong>{{ t.tytul_imie }}</strong>
                                    {{ t.tagline|safe }}
                                </P>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            {% endif %}

            {% if stars.count %}
                <div class="pages-header--new-people">
                    <p>
                        {% blocktrans with count=stars.count %}wyszkoliliśmy już <strong>{{ count }} osób</strong>{% endblocktrans %}
                    </p>
                </div>
            {% endif %}

        </div>
    </div>
</div>
{% else %}
<div class="lp-newTop lp-newTopZbiorczy pages-header{% if not tech.top_background %} lp-newTop-background001{% endif %} lp-zbiorczyMax"{% if tech.top_background %} style="background-image: url('{{ tech.top_background.url }}');"{% endif %}>
    <div class="lp-newTop-content clearfix">

        <div class="lp-newTop-left clearfix">

                <div class="lp-newTop-header">
                    <h1 class="lp-optionHeader as_h2">{{ top_highlight.highlight.tytul_h2 }}</h1>

                    {{ top_highlight.highlight.tresc|safe }}

                    {% if top_highlight.highlight.link %}
                        <p>
                            <a class="lp-tabsMoreLink" href="{{ top_highlight.highlight.link }}">{% trans "Czytaj więcej" %}</a>
                        </p>
                    {% endif %}

                    {% if top_highlight.highlight.obrazek %}
                        <ul class="lp-newTop-certifications">
                            <li><img src="/uploads/{{ top_highlight.highlight.obrazek.name }}"></li>
                        </ul>
                    {% endif %}
                    <div class="forbes-diamonds hide">
                        <img src="https://www.alx.pl/media/diamenty-white.png" alt="ALX Diamenty Forbes" id="diamonds"/>
                    </div>
                </div>
            <div class="lp-clr"></div>
        </div>

        <div class="lp-newTop-right">

            {% if stars.avg %}
                <div class="lp-newTop-rating clearfix">
                    <div class="rate">
                        {{ stars.avg }}<span>/5</span>
                    </div>
                    <div class="stars-cointaner">
                        <div class="stars stars-{{ stars.css }}">Stars</div>
                    </div>
                </div>
            {% endif %}

            {% if tech_trenerzy %}

                <div class="lp-newTop-coachWrap">
                    <h3><span>{% trans "Trenerzy" %}</span></h3>

                    <div class="lp-newTop-coach">
                        {% for t in tech_trenerzy %}
                            <div class="lp-newTop-singleCoach">
                                <div class="lp-newTop-coachImg">
                                    {% if pl %}
                                        <a href="{% url 'wykladowcy' %}#trener-{{ t.pk }}"><img src="{{ t.sylwetka_big_image_url }}" alt="{{ t.tytul_imie }}"></a>
                                    {% else %}
                                        <img src="{{ t.sylwetka_big_image_url }}" alt="{{ t.tytul_imie }}">
                                    {% endif %}
                                </div>
                                <p>
                                    <strong>{{ t.tytul_imie }}</strong>
                                    {{ t.tagline|safe }}
                                </P>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            {% endif %}

            {% if stars.count %}
                <div class="lp-newTop-People">
                    <p>
                        {% blocktrans with count=stars.count %}wyszkoliliśmy już <strong>{{ count }} osób</strong>{% endblocktrans %}
                    </p>
                </div>
            {% endif %}

        </div>
    </div>
</div>
{% endif %}

<script src="{% staticbuster new/js/diamonds.js %}"></script>
