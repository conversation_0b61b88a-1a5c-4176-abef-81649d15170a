{% load mytags myfilters %}
{% load i18n %}
<!-- top B2 -->
{% if wariant_szkolenia %}
<div class="pages-header{% if not tech.top_background %} standard-bg{% endif %}">
    <div class="pages-header--inner">

        <div class="pages-header--content-left">
                    <h1>{{ top_highlight.highlight.tytul_h2 }}</h1>

                    {{ top_highlight.highlight.tresc|safe }}

                    {% if top_highlight.highlight.link %}
                        <p>
                            <a class="lp-tabsMoreLink" href="{{ top_highlight.highlight.link }}">{% trans "Czytaj więcej" %}</a>
                        </p>
                    {% endif %}

                {% if stars.avg %}
                    <div class="pages-header--rating">
                        <div class="rate">
                            {{ stars.avg }}<span>/5</span>
                        </div>
                        <div class="stars-cointaner">
                            <div class="stars stars-{{ stars.css }}">Stars</div>
                        </div>
                    </div>
                {% endif %}

                {% if stars.count %}
                    <div class="pages-header--new-people">
                        <p>
                            {% blocktrans with count=stars.count %}wyszkoliliśmy już <strong>{{ count }} osób</strong>{% endblocktrans %}
                        </p>
                    </div>
                {% endif %}

        </div>

        <div class="pages-header--content-right">
            <ul class="certifications">
                <li><img src="/uploads/{{ top_highlight.highlight.obrazek.name }}"></li>
            </ul>
        </div>
    </div>
</div>
{% else %}
<div class="lp-newTop lp-newTopZbiorczy pages-header{% if not tech.top_background %} lp-newTop-background001{% endif %} lp-zbiorczyMedium"{% if tech.top_background %} style="background-image: url('{{ tech.top_background.url }}');"{% endif %}>
    <div class="lp-newTop-content clearfix">

        <div class="lp-newTop-left clearfix">
                <div class="lp-newTop-header">
                    <h2 class="lp-optionHeader">{{ top_highlight.highlight.tytul_h2 }}</h2>

                    {{ top_highlight.highlight.tresc|safe }}

                    {% if top_highlight.highlight.link %}
                        <p>
                            <a class="lp-tabsMoreLink" href="{{ top_highlight.highlight.link }}">{% trans "Czytaj więcej" %}</a>
                        </p>
                    {% endif %}
                </div>

                {% if stars.avg %}
                    <div class="lp-newTop-rating clearfix">
                        <div class="rate">
                            {{ stars.avg }}<span>/5</span>
                        </div>
                        <div class="stars-cointaner">
                            <div class="stars stars-{{ stars.css }}">Stars</div>
                        </div>
                    </div>
                {% endif %}

                {% if stars.count %}
                    <div class="lp-newTop-People">
                        <p>
                            {% blocktrans with count=stars.count %}wyszkoliliśmy już <strong>{{ count }} osób</strong>{% endblocktrans %}
                        </p>
                    </div>
                {% endif %}

            <div class="lp-clr"></div>
        </div>

        <div class="lp-newTop-right">
            <div class="lp-zbiorczyPrawaImg">
                <ul class="lp-newTop-certifications">
                    <li><img src="/uploads/{{ top_highlight.highlight.obrazek.name }}"></li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endif %}
