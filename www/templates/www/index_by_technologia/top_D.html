{% load mytags myfilters %}
{% load i18n %}

<!-- top D -->
{% if wariant_szkolenia %}
<div class="pages-header {% if not tech.top_background %} standard-bg{% endif %}">
    <div class="pages-header--inner">

        <div class="pages-header--content-left">
            <h1>{% trans "Szkolenia" context "naglowek index_by_technologia" %}: {{ tech.nazwa }}</h1>
        </div>
    </div>
</div>

{% else %}
<div class="lp-newTop lp-newTopZbiorczy pages-header{% if not tech.top_background %} lp-newTop-background001{% endif %} lp-zbiorczyMax"{% if tech.top_background %} style="background-image: url('{{ tech.top_background.url }}');"{% endif %}>
    <div class="lp-newTop-content clearfix">

        <div class="lp-newTop-left clearfix">

                <div class="lp-newTop-header">
                    <h2 class="lp-optionHeader">{% trans "Szkolenia" context "naglowek index_by_technologia" %}: {{ tech.nazwa }}</h2>
                </div>

            <div class="lp-clr"></div>
        </div>
    </div>
</div>

{% endif %}