{% extends "www/base.html" %}

{% load markup %}
{% load mytags %}
{% load myfilters %}
{% load i18n %}

{% block data_layer %}
<script>
  dataLayer.push({ gtp: null });
  dataLayer.push({ gbv: null });
  dataLayer.push({
    'event': 'gbv',
    'gbv': {
      'event_name': 'view_item',
      'value': {{ szkolenie.cena|default_if_none:"0.00"|floatformat:"0" }},
      'items': {
        'id': '{{ szkolenie.kod }}',
        'google_business_vertical': 'education'
        }
      },


    'gtp': {
      'edu_pagetype': 'program',
      'edu_pid': '{{szkolenie.kod}}',
      'edu_totalvalue': {{ szkolenie.cena|default_if_none:"0.00"|floatformat:"0" }},
      'edu_plocid': 'Warszawa'

    }
  });
</script>

{% endblock %}

{% block descr %}{% if szkolenie.header_descr %}{{ szkolenie.header_descr }}{% else %}{% if szkolenie.opis %}{{ szkolenie.opis|striptags }}{% else %}{% trans "Szkolenie" %}: {{ szkolenie.nazwa }}{% endif %}{% endif %}{% endblock descr %}

{% block title %}{% if szkolenie.header_title %}{{ szkolenie.header_title }}{% else %}{% trans "Szkolenie" %} - {{ szkolenie.nazwa }}{% endif %}{% endblock title %}

{% block sticky_h4 %}{{ szkolenie.nazwa }}{% endblock sticky_h4 %}

{% block meta_content %}
    {% if szkolenie.tag_dlugosc.slug == 'kurs-zawodowy' %}
        {% include 'www/kurs_actions.html' %}
    {% else %}
        {% include 'www/szkolenie_actions.html' %}
    {% endif %}
{% endblock %}

{% block extra_head %}
    {{ block.super }}

    {% if tab_as_hyperlink and active_tab and not is_a_first_tab and non_indexable_tab %}
        <meta name="robots" content="noindex,follow">
    {% endif %}
{% endblock extra_head %}



{% block body %}

{% if active_tab.id == "opis" %}

<div class="tabcontent tabcontentopis" id="{% tab_slug 'opis' %}">

    <p>
      {% with tagi=szkolenie.widoczne_tagi_technologia %}
        {% if tagi %}
          <b>{% blocktrans count tags_len=tagi|length %}Kategoria{% plural %}Kategorie{% endblocktrans %}:</b>
          {% for t in tagi %}
            <a href="{{ t.get_absolute_url }}">{{ t.nazwa }}</a>{% if not forloop.last %},{% endif %}
          {% endfor %}
        {% endif %}
      {% endwith %}
    </p>

    {% if "<style>" in szkolenie.cel|lower %}
        {{ szkolenie.cel|myincludes|safe }}
    {% else %}
        {{ szkolenie.cel|myincludes|my_textile }}
    {% endif %}

    {% if szkolenie.autoryzacja %}
      <p>{{ szkolenie.autoryzacja.opis_dlugi|safe }}</p>
    {% endif %}

    {% if szkolenie.opis_egzaminu %}
      <p>{{ szkolenie.opis_egzaminu|safe }}</p>
    {% endif %}

    <h2>{% trans "Czas trwania" %}</h2>

    <p>{{ szkolenie.czas_str }}</p>

    {% if pl and szkolenie.sciezki.all %}

        <h2>Szkolenie jest częścią {{ szkolenie.sciezki.all|length|pluralize:"ścieżki,ścieżek" }}:</h2>

        {% with szkolenie.sciezki.all as sciezki %}
        {% include 'www/sciezki_linki.html' %}
        {% endwith %}

    {% endif %}

    <h2>{% trans "Program" %}</h2>

    <div class='program'>
        {{ szkolenie.program|my_textile }}
    </div>

    <p>
        <a class="call-to-action" href="{% url 'detail_pdf' slug=szkolenie.slug language=language %}">
            {% trans "Pobierz w wersji PDF" %}
        </a>
    </p>

    {% if pl %}
        <p>Training also available in English{% with szkolenie.translations as translations %}{% if translations and translations.0.aktywne %} - for more information <a href="{{ translations.0.get_absolute_url }}">see here</a>{% endif %}{% endwith %}.</p>
    {% endif %}

    <h2>{% trans "Przeznaczenie i wymagania" %}</h2>

    {% if szkolenie.przeznaczony_dla %}
        <div>{{ szkolenie.przeznaczony_dla|my_textile }}</div>
    {% endif %}
    {% if szkolenie.wymagania %}
        <div>{{ szkolenie.wymagania|my_textile }}</div>
    {% endif %}
    {% if not szkolenie.przeznaczony_dla and not szkolenie.wymagania %}
            <p>{% trans "Brak szczegółowych wymagań wobec uczestników szkolenia." %}</p>
    {% endif %}

    <h2>{% trans "Certyfikaty" %}</h2>

    {{ szkolenie.certyfikaty_str|my_textile }}

    {% if szkolenie.opis_dlugi %}
      {{ szkolenie.opis_dlugi|safe }}
    {% endif %}


    {% if szkolenie.uwagi %}
        <h2>{% trans "Informacje dodatkowe" %}</h2>
        {{ szkolenie.uwagi }}
    {% endif %}


    <div class='special-section'>
    </div>

    {% include "www/notyfikacje_i_terminy_inline.html" %}
</div>

{% elif active_tab.id == "terminy-lokalizacje" %}

<div class="tabcontent tabcontentterminy" id="{% tab_slug 'terminy-lokalizacje' %}">

{% flatpage language "zakladka-terminy-szkolenia-include" %}

{% if terminy %}

	{% if terminy_z_harmonogramami or not szkolenie.terminy_trwajace|terminy_ciagle %}
		<h2>{% trans "Dokładne harmonogramy kursów" %}</h2>
		{{ szkolenie|harmonogram }}
	{% else %}
		<h2>{% trans "Najbliższe terminy" context "detail.html" %}</h2>
		<ul>
		    {% for termin in terminy %}
			<li>
			   {{ termin.termin|date:"Y.m.d" }}
			   {% if termin.termin_do %}
			       - {{ termin.termin_do|date:"Y.m.d" }}
			   {% endif %}

			   ({{ termin.lokalizacja.shortname }})

			   {% if pl %}
                             {% if termin.opis %}({{ termin.opis }}){% endif %}
                          {% endif %}

			   {% if termin.language != language %}
			       {% language_icon termin %}
			   {% endif %}
			</li>
	            {% endfor %}
		</ul>
	{% endif %}
        {% if pl %}
              <p>
		<a class="call-to-action" href="{% url 'zgloszenie' slug=szkolenie.slug language=szkolenie.language %}" rel="nofollow">Zgłoszenie on-line</a>
		</p>
		<p>
		<a class="call-to-action" href="/media/formularz-szkolenie.pdf">Tradycyjny formularz zgłoszeniowy</a>
	       </p>
	{% endif %} {# pl #}

{% endif %} {# terminy #}

<h2>{% trans "Lokalizacje" %}</h2>
<ul>
  {% for l in szkolenie.lokalizacje_list %}
    <li>{{ l }}</li>
  {% endfor %}
</ul>
<h2>{% if pl %}Godziny szkolenia{% endif %}{% if en %}{% endif %}</h2>
<p>{% if pl %}Zajęcia standardowo odbywają się w godzinach 9-17 (po 8 godzin zegarowych
dziennie, w tym przerwa obiadowa oraz krótkie przerwy w trakcie zajęć).
Przy szkoleniach na zamówienie, dokładne godziny mogą być uzgodnione z
Klientem wedle jego preferencji.{% endif %}{% if en %}{% endif %}</p>
</div>

{% elif active_tab.id == "na-zamowienie" %}

<div class="tabcontent tabcontentnazamowienie" id="{% tab_slug 'na-zamowienie' %}">

{% if pl %}
	{% if terminy %}
	<p>
	    W przypadku zgłoszenia grupy osób {% if szkolenie.min_grupa and szkolenie.min_grupa > 1 %}<b>(min.
	    {{szkolenie.min_grupa}})</b>{% endif %}, możliwe jest również zamówienie szkolenia
	    zamkniętego, w terminie do uzgodnienia.
	</p>

	{% else %} {# not terminy #}

	<p>
	    Szkolenie jest realizowane <b>w trybie zamkniętym{% if szkolenie.min_grupa and szkolenie.min_grupa > 1 %}, dla grup od {{ szkolenie.min_grupa }} osób{% endif %}</b>.
	</p>

	{% endif %} {# if terminy #}

	<p>
	Szkolenia zamknięte prowadzimy w naszych salach, w siedzibie Klienta, lub w dowolnej lokalizacji na terenie Polski, lub UE (w jęz. polskim lub angielskim). Dla grup możliwe są dowolne tryby zajęć - <b>godziny pracy, wieczory, weekendy</b>. Możliwe jest również <b>dostosowanie tematów kursu</b> do indywidualnych potrzeb Klienta.
	</p>

	<p>
Cena szkolenia na zamówienie jest wyliczana indywidualnie dla każdego zamówienia. Dzięki temu koszt szkolenia w przeliczeniu na uczestnika może być znacznie korzystniejszy niż przy szkoleniach w grupach ogólnodostępnych (ceny podane na stronie) - zwłaszcza w przypadku większych grup.
	</p>

	<p>
	<a class="call-to-action" href="{% url 'zaproponuj_termin' slug=szkolenie.slug %}" rel="nofollow">Zapytaj i zaproponuj termin</a>
	</p>
	<p>Lub prosimy o <a href="/pl/kontakt/">kontakt</a>.</p>

{% endif %} {# pl #}

{% if en %}

{% flatpage language "zamkniete-wyjazdowe" %}

{% endif %}

</div>

{% elif active_tab.id == "konsultacje" %}

<div class="tabcontent tabcontentkonsultacje" id="{% tab_slug 'konsultacje' %}">
{% flatpage language "konsultacje-include" %}
</div>

{% elif active_tab.id == "cennik" %}

<div class="tabcontent tabcontentrabaty" id="{% tab_slug 'cennik' %}">

<h2>{% trans "Cena szkolenia" %}</h2>

{% if pl %}
	<p>
	<b>{{ szkolenie|cena_info|safe }}</b>
	</p>

	<p>Cena za jedną osobę, w grupie ogólnodostępnej (otwartej), o standardowej wielkości. Dla grup istnieje również możliwość organizacji szkolenia zamkniętego. Więcej w zakładce „Na zamówienie”.</p>

	<p>W cenę szkoleń organizowanych w naszej siedzibie wliczone są:
	<br/>- {% if szkolenie.cena_autoryzacji == 0 %}autoryzowane{% else %}autorskie{% endif %}
	    materiały szkoleniowe,
	<br/>- indywidualne stanowisko komputerowe do pracy podczas zajęć,
	<br/>- certyfikaty ukończenia szkolenia,
	{% if szkolenie.zawiera_obiady %}<br/>- obiady,{% endif %}
	<br/>- drobny poczęstunek oraz ciepłe i zimne napoje,
	<br/>- możliwość jednorazowego kontaktu z instruktorem (instruktorami) po szkoleniu i zadawania pytań dotyczących materiału szkolenia.
	</p>

	{% if not szkolenie.zawiera_obiady %}
		<p>Cena szkolenia nie zawiera obiadów. Można je dokupić w cenie 35 zł netto za obiad.
		{% if szkolenie.tag_dlugosc.slug == 'szkolenie' %}
			Uwagi:</p>
			<ul>
			<li>W przypadku wykupienia obiadów, faktura VAT zostanie wystawiona z jedną całościową pozycją ("szkolenie") - tj. obiady nie będą wyszczególniane osobno.</li>
			<li>Nie ma możliwości dokupienia obiadów w trakcie trwania szkolenia poprzez korektę naszej faktury. Podczas szkolenia, można dokonać zakupu, zapłaty i uzyskać fakturę jedynie bezpośrednio w restauracji.</li>
			</ul>
		{% else %}
			</p>
		{% endif %}
	{% endif %}

	{% if szkolenie.cena_w_grupie_2_os or szkolenie.cena_indywidualnie %}
		<p>Szkolenie można zamówić także:</p>
		<ul>
		{% if szkolenie.cena_w_grupie_2_os %}
			<li>w grupie 2{% if szkolenie.min_grupa and szkolenie.min_grupa > 3 %}-3{% endif %}-osobowej: {{ szkolenie.cena_w_grupie_2_os }} zł za osobę</li>
		{% endif %}
		{% if szkolenie.cena_indywidualnie %}
			<li>w trybie VIP (indywidualnie{% if szkolenie.godzin_w_trybie_indywidualnym %} {{ szkolenie.godzin_w_trybie_indywidualnym }}h{% endif %}): {{ szkolenie.cena_indywidualnie }} zł</li>
		{% endif %}
		</ul>
		<p>Dostępność oferty mikrogrup oraz konsultacji indywidualnych może zależeć od dodatkowych czynników (m.in. lokalizacji oraz obłożenia trenerów) i wymaga każdorazowego potwierdzenia.</p>
	{% endif %}
	<p>
 W wypadku podmiotów publicznych lub finansowania ze środków publicznych, możliwe jest zwolnienie z VAT. Szczegóły na <a href="/pl/szkolenia-zwolnione-z-vat/">osobnej stronie</a>.
        </p>
{% endif %}

{% if en %}
	<p><b>{{ szkolenie|cena_info|safe }}</b></p>

	<p>The price includes:
	<br/>- course materials,
	{% if szkolenie.zawiera_obiady %}<br/>- meals,{% endif %}
	<br/>- snacks, coffee, tea and soft drinks,
	<br/>- course completion certificate,
	<br/>- one-time consultation with the instructor after course completion.
	</p>
{% endif %}

{% flatpage language "rabaty" %}

{% if szkolenie.tag_dlugosc.slug == 'kurs-zawodowy' %}
{% flatpage language "platnosc-ratalna-za-kursy" %}
{% endif %}
</div>

{% endif %}


{% endblock body %}
