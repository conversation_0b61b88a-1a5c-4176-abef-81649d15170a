{% load mytags %}
{% load myfilters %}
<?xml version="1.0"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  {% include "www/includes/google_tag_manager_head.html" %}

  {% block data_layer %}
  {% endblock %}

  <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
  <meta name="robots" content="noindex,nofollow">
  <title>{% block title %}ALX{% endblock title %}</title>
  <link rel="shortcut icon" href="/static/icons/favicon.ico" />
  <link href="{% staticbuster css/formularz.css %}" rel="stylesheet" type="text/css" />
  <link href="{% staticbuster css/formularz-print.css %}" rel="stylesheet" type="text/css" media="print" />



   <script src="{{ STATIC_URL }}js/jquery.min.js"></script>
   <script src="{% staticbuster js/formularz.js %}"></script>

    {% block extra_head %}{% endblock extra_head %}

  {% if dynamic_custom_header %}
    {{ dynamic_custom_header|safe }}
  {% endif %}

</head>
<body>
{% block body %}
{% endblock body %}
    {% include "www/includes/google_tag_manager_body.html" %}
    <!-- conversions -->
    {% if first_visit %}
      <!-- Event snippet for Zgłoszenie na kurs conversion page -->
      {% if not zgloszenie.termin.szkolenie.kod|starts_with_w %}

        <script>
            dataLayer.push({ gtp: null });
            dataLayer.push({ gbv: null });

            dataLayer.push({
                'event': 'gbv',
                'gbv': {
                  'event_name': 'purchase',
                  'value': {{ konwersja_cena|default_if_none:"0.00"|floatformat:"0" }},
                  'items': {
                    'id': '{{ zgloszenie.termin.szkolenie.kod }}',
                    'google_business_vertical': 'education'
                    }
                  },
                'gtp': {
                  'edu_pagetype': 'complete',
                  'edu_pid': '{{ zgloszenie.termin.szkolenie.kod }}',
                  'edu_totalvalue': {{ konwersja_cena|default_if_none:"0.00"|floatformat:"0" }},
                  'edu_plocid': 'Warszawa'
                      }
                });
            dataLayer.push({
                'event': 'zapisSzkolenie',
                'userEmail':'{{ zgloszenie.email }}' ,
                'userPhone':'{{ zgloszenie.telefon}}',
                'id': '{{ zgloszenie.termin.szkolenie.kod }}',
                'value': {{ konwersja_cena|default_if_none:"0.00"|floatformat:"0" }},

            });

        </script>

      {% endif %}
    {% endif %}

</body>
</html>
