{% load myfilters %}
{% load mytags %}
{% load i18n %}

<ul class="actions" data-terminy='{{ terminy|length }}'>
  {% if terminy %}
    <li>
      <ol class="courses-date">
    	{% for termin in terminy %}
    	  <li>
          {{ termin.termin|date:"Y-m-d" }}
          <em class="city">{{ termin.lokalizacja.shortname }}</em>
          {% if termin.language != language %}
            {% language_icon termin %}
          {% endif %}
        </li>
    	{% endfor %}
      </ol>
    </li>
  {% endif %}

  {% if szkolenie.tag_dlugosc.slug == 'szkolenie' %}
    {% if terminy %}
      <li class="call-to-action-buttons">
        <p>
          <a href="{% url 'zgloszenie' slug=szkolenie.slug language=language %}" class="zapisz-sie call-to-action">
            {% trans "Zapisz się" %}
          </a>
        </p>
        <p>
          <a href="{% url 'zaproponuj_termin' slug=szkolenie.slug language=language %}" class="zaproponuj-termin call-to-action">
            {% trans "Zaproponuj własny termin" %}
          </a>
        </p>
      </li>
    {% endif %}
  {% endif %}

  <li class='footer'>
    {% include "www/na_zyczenie_info.html" %}
  </li>

</ul>
