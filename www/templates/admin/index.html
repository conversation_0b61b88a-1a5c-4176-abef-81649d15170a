{% extends "admin/index.html" %}
{% load i18n static %}
{% block content %}
 {% if request.path != "/admin/" %}
  {{ block.super }}
 {% else %}
  {% if perms.www.change_terminszkolenia %}
   <div id="content-main">
    <h1>Biuro</h1>
    <div class="module">
     <table>
      <tr>
       <th scope="row">
        <a href="www/terminszkolenia/?future-past=future">Terminy Szkoleń</a>
       </th>
       <td>
        <a href="www/terminszkolenia/add/" class="addlink">Dodaj</a>
       </td>
       <td>
        <a href="www/terminszkolenia/" class="changelink">Zmień</a>
       </td>
      </tr>
      <tr>
       <th scope="row">
        &nbsp;&nbsp;&nbsp;&nbsp;<a href="www/terminszkolenia/?future-past=future&odbylo_sie__exact=1&sala_przygotowana__exact=0">Sale do przygotowania</a>
       </th>
      </tr>
      <tr>
       <th scope="row">
        &nbsp;&nbsp;&nbsp;&nbsp;<a href="{% url 'admin:ankiety_ankieta_changelist' %}">Ankiety</a>
       </th>
       <td>
        <a href="{% url 'admin:ankiety_ankieta_add' %}" class="addlink %}">Dodaj</a>
       </td>
      </tr>
      <tr>
       <th scope="row">
        <a href="www/uczestnik/?future-past=future">Uczestnicy</a>
       </th>
       <td>
        <a href="www/uczestnik/add/" class="addlink">Dodaj</a>
       </td>
       <td>
        <a href="www/uczestnik/" class="changelink">Zmień</a>
       </td>
      </tr>
      <tr>
       <th scope="row">
        &nbsp;&nbsp;&nbsp;&nbsp;<a href="/windykacja/">Zalegający z płatnością</a>
       </th>
       <td>&nbsp;</td>
       <td>&nbsp;</td>
      </tr>
      <tr>
       <th scope="row">
        &nbsp;&nbsp;&nbsp;&nbsp;<a href="/niespojnosc-rat/">Niepoprawne harmonogramy rat</a>
       </th>
       <td>&nbsp;</td>
       <td>&nbsp;</td>
      </tr>
      <tr>
       <th scope="row">
        &nbsp;&nbsp;&nbsp;&nbsp;<a href="www/fakturakorekta/">Faktury korygujące</a>
       </th>
       <td>
        <a href="www/fakturakorekta/add/" class="addlink">Dodaj</a>
       </td>
       <td>
        <a href="www/fakturakorekta/" class="changelink">Zmień</a>
       </td>
      </tr>
      <tr>
       <th scope="row">
        &nbsp;&nbsp;&nbsp;&nbsp;<a href="www/fakturawysylka/">Wysyłka Faktur</a>
       </th>
       <td>
        <a href="www/fakturawysylka/add/" class="addlink">Dodaj</a>
       </td>
       <td>
        <a href="www/fakturawysylka/" class="changelink">Zmień</a>
       </td>
      </tr>
      <tr>
       <th scope="row">
        <a href="crm/firma/">Firmy</a>
       </th>
       <td>
        <a href="crm/firma/add/" class="addlink">Dodaj</a>
       </td>
       <td>
        <a href="crm/firma/" class="changelink">Zmień</a>
       </td>
      </tr>
      <tr>
       <th scope="row">
        <a href="www/usernotification/">Powiadomienia użytkowników</a> (<a href="www/usernotificationlog/">logi</a>)
       </th>
       <td>
        <a href="www/usernotification/add/" class="addlink">Dodaj</a>
       </td>
       <td>
        <a href="www/usernotification/" class="changelink">Zmień</a>
       </td>
      </tr>
      <tr>
       <th scope="row">
        <a href="www/graduate/">Absolwenci</a>
       </th>
       <td>
        <a href="www/graduate/add/" class="addlink">Dodaj</a>
       </td>
       <td>
        <a href="www/graduate/" class="changelink">Zmień</a>
       </td>
      </tr>
      <tr>
       <th scope="row">
        <a href="www/szkolenie/?aktywne__exact=1">Szkolenia</a>
       </th>
       <td>
        Dodaj:
        <a href="www/szkolenie/add/?waluta=3&language=pl" class="addlink">PL</a>
        <a href="www/szkolenie/add/?waluta=2&language=en" class="addlink">EN</a>
       </td>
       <td>
        <a href="www/szkolenie/" class="changelink">Zmień</a>
       </td>
      </tr>
      <tr>
       <th scope="row">
        <a href="www/szkolenie/?aktywne__exact=1&list_display_extened=1">Szkolenia z Terminami</a>
       </th>
       <td>
        Dodaj:
        <a href="www/szkolenie/add/?waluta=3&language=pl" class="addlink">PL</a>
        <a href="www/szkolenie/add/?waluta=2&language=en" class="addlink">EN</a>
       </td>
       <td>
        <a href="www/szkolenie/?list_display_extened=1" class="changelink">Zmień</a>
       </td>
      </tr>
      <tr>
       <th scope="row">
        <a href="www/grupazaszeregowania/">Grupy zaszeregowania</a>
       </th>
       <td>
        <a href="www/grupazaszeregowania/add/" class="addlink">Dodaj</a>
       </td>
       <td>
        <a href="www/grupazaszeregowania/" class="changelink">Zmień</a>
       </td>
      </tr>
      <tr>
       <th scope="row">
        &nbsp;&nbsp;&nbsp;&nbsp;<a href="http://docs.google.com/Doc?docid=0ARqblN7un3R6ZDNqZzhiNF8yNWZjbmZoY2Zt">Instrukcja wyceny</a>
       </th>
       <td>&nbsp;</td>
       <td>&nbsp;</td>
      </tr>
      <tr>
       <th scope="row">
        <a href="www/referencja/">Referencje</a>
       </th>
       <td>
        <a href="www/referencja/add/" class="addlink">Dodaj</a>
       </td>
       <td>
        <a href="www/referencja/" class="changelink">Zmień</a>
       </td>
      </tr>
      <tr>
       <th scope="row">
        <a href="www/change/">Historia zmian</a>
       </th>
       <td>
        <a href="www/change/add/" class="addlink">Dodaj</a>
       </td>
       <td>
        <a href="www/change/" class="changelink">Zmień</a>
       </td>
      </tr>
      <tr>
       <th scope="row">
        <a href="/stats/">Statystyki</a>
       </th>
       <td>&nbsp;</td>
       <td>&nbsp;</td>
      </tr>
     </table>
    </div>
    <h1>Webmasterzy</h1>
    <div class="module">
     <table summary="Modele dostępne w aplikacji Auth.">
      <caption><a href="auth/" class="section">Auth</a></caption>
      <tr>
       <th scope="row">
        <a href="auth/group/">Grupy</a>
       </th>
       <td>
        <a href="auth/group/add/" class="addlink">Dodaj</a>
       </td>
       <td>
        <a href="auth/group/" class="changelink">Zmień</a>
       </td>
      </tr>
      <tr>
       <th scope="row">
        <a href="auth/user/">Użytkownicy</a>
       </th>
       <td>
        <a href="auth/user/add/" class="addlink">Dodaj</a>
       </td>
       <td>
        <a href="auth/user/" class="changelink">Zmień</a>
       </td>
      </tr>
      <tr>
       <th scope="row">
        <a href="www/leave/">Urlopy</a>
       </th>
       <td>
        <a href="www/leave/add/" class="addlink">Dodaj</a>
       </td>
       <td>
        <a href="www/leave/" class="changelink">Zmień</a>
       </td>
      </tr>
     </table>
    </div>
    <div class="module">
     <table summary="Modele dostępne w aplikacji Leads.">
      <caption><a href="leads/" class="section">Leads</a></caption>
      <tr>
       <th scope="row">
        <a href="leads/leadcategory/">Kategoria lead</a>
       </th>
       <td>
        <a href="leads/leadcategory/add/" class="addlink">Dodaj</a>
       </td>
       <td>
        <a href="leads/leadcategory/" class="changelink">Zmień</a>
       </td>
      </tr>
      <tr>
       <th scope="row">
        <a href="leads/leaduser/">Użytkownicy lead</a>
       </th>
       <td></td>
       <td>
        <a href="leads/leaduser/" class="changelink">Zmień</a>
       </td>
      </tr>
     </table>
    </div>

    <div class="module">
     <table summary="Modele dostępne w aplikacji Leads.">
      <caption><a href="blacklist/" class="section">Czarna/Szara lista</a></caption>
      <tr>
       <th scope="row">
        <a href="blacklist/blacklist/">Czarna/Szara lista</a>
       </th>
       <td>
        <a href="blacklist/blacklist/add/" class="addlink">Dodaj</a>
       </td>
       <td>
        <a href="blacklist/blacklist/" class="changelink">Zmień</a>
       </td>
      </tr>
     </table>
    </div>
    <div class="module">
     <table summary="Modele dostępne w aplikacji Leads.">
      <caption><a href="blacklist/" class="section">Dynamiczne nagłówki</a></caption>
      <tr>
       <th scope="row">
        <a href="headers/header/">Dynamiczny nagłówek</a>
       </th>
       <td>
        <a href="headers/header/add/" class="addlink">Dodaj</a>
       </td>
       <td>
        <a href="headers/header/" class="changelink">Zmień</a>
       </td>
      </tr>
     </table>
    </div>
    <div class="module">
     <table summary="Modele dostępne w aplikacji Forum.">
      <caption><a href="forum/" class="section">Forum</a></caption>
      <tr>
       <th scope="row">
        <a href="forum/forum/">Fora</a>
       </th>
       <td>
        <a href="forum/forum/add/" class="addlink">Dodaj</a>
       </td>
       <td>
        <a href="forum/forum/" class="changelink">Zmień</a>
       </td>
      </tr>
      <tr>
       <th scope="row">
        <a href="forum/post/">Posty</a>
       </th>
       <td>
        <a href="forum/post/add/" class="addlink">Dodaj</a>
       </td>
       <td>
        <a href="forum/post/" class="changelink">Zmień</a>
       </td>
      </tr>
      <tr>
       <th scope="row">
        <a href="forum/watek/">Wątki</a>
       </th>
       <td>
        <a href="forum/watek/add/" class="addlink">Dodaj</a>
       </td>
       <td>
        <a href="forum/watek/" class="changelink">Zmień</a>
       </td>
      </tr>
     </table>
    </div>
    <div class="module">
     <table summary="Modele dostępne w aplikacji Newsletter.">
      <caption><a href="newsletter/" class="section">Newsletter</a></caption>
      <tr>
       <th scope="row">
        <a href="newsletter/odbiorca/">Odbiorcy newslettera</a>
       </th>
       <td>
        <a href="newsletter/odbiorca/add/" class="addlink">Dodaj</a>
       </td>
       <td>
        <a href="newsletter/odbiorca/" class="changelink">Zmień</a>
       </td>
      </tr>
     </table>
    </div>
    <div class="module">
     <table summary="Modele dostępne w aplikacji Opt-out.">
      <caption><a href="optout/" class="section">Opt-Out</a></caption>
      <tr>
       <th scope="row">
        <a href="optout/optout/">Opt-Out globalny</a>
       </th>
       <td>
        <a href="optout/optout/add/" class="addlink">Dodaj</a>
       </td>
       <td>
        <a href="optout/optout/" class="changelink">Zmień</a>
       </td>
      </tr>
     </table>
    </div>

    <div class="module">
     <table summary="Modele dostępne w aplikacji Www.">
      <caption><a href="www/" class="section">Www</a></caption>
      <tr>
       <th scope="row">
        <a href="www/autoryzacja/">Autoryzacje</a>
       </th>
       <td>
        <a href="www/autoryzacja/add/" class="addlink">Dodaj</a>
       </td>
       <td>
        <a href="www/autoryzacja/" class="changelink">Zmień</a>
       </td>
      </tr>
      <tr>
       <th scope="row">
        <a href="www/contentgroup/">Grupy treści</a>
       </th>
       <td>
        <a href="www/contentgroup/add/" class="addlink">Dodaj</a>
       </td>
       <td>
        <a href="www/contentgroup/" class="changelink">Zmień</a>
       </td>
      </tr>
      <tr>
       <th scope="row">
        <a href="www/logo/">Loga</a>
       </th>
       <td>
        <a href="www/logo/add/" class="addlink">Dodaj</a>
       </td>
       <td>
        <a href="www/logo/" class="changelink">Zmień</a>
       </td>
      </tr>
      <tr>
       <th scope="row">
        <a href="www/lokalizacja/">Lokalizacje</a>
       </th>
       <td>
        <a href="www/lokalizacja/add/" class="addlink">Dodaj</a>
       </td>
       <td>
        <a href="www/lokalizacja/" class="changelink">Zmień</a>
       </td>
      </tr>
      <tr>
       <th scope="row">
        <a href="www/menuitem/">Menu items</a>
       </th>
       <td>
        <a href="www/menuitem/add/" class="addlink">Dodaj</a>
       </td>
       <td>
        <a href="www/menuitem/" class="changelink">Zmień</a>
       </td>
      </tr>
      <tr>
       <th scope="row">
        <a href="www/prowadzacy/?nadal_pracuje=True">Prowadzący</a>
       </th>
       <td>
        <a href="www/prowadzacy/add/" class="addlink">Dodaj</a>
       </td>
       <td>
        <a href="www/prowadzacy/?nadal_pracuje=True" class="changelink">Zmień</a>
       </td>
      </tr>
      <tr>
       <th scope="row">
        <a href="www/sala/">Sale</a>
       </th>
       <td>
        <a href="www/sala/add/" class="addlink">Dodaj</a>
       </td>
       <td>
        <a href="www/sala/" class="changelink">Zmień</a>
       </td>
      </tr>
      <tr>
       <th scope="row">
        <a href="www/sitemodule/">Site modules</a>
       </th>
       <td>
        <a href="www/sitemodule/add/" class="addlink">Dodaj</a>
       </td>
       <td>
        <a href="www/sitemodule/" class="changelink">Zmień</a>
       </td>
      </tr>
      <tr>
       <th scope="row">
        <a href="www/myflatpage/">Strony statyczne</a>
       </th>
       <td>
        <a href="www/myflatpage/add/" class="addlink">Dodaj</a>
       </td>
       <td>
        <a href="www/myflatpage/" class="changelink">Zmień</a>
       </td>
      </tr>
      <tr>
       <th scope="row">
        <a href="www/graduatestory/">Sylwetki Absolwentów</a>
       </th>
       <td>
        <a href="www/graduatestory/add/" class="addlink">Dodaj</a>
       </td>
       <td>
        <a href="www/graduatestory/" class="changelink">Zmień</a>
       </td>
      </tr>
      <tr>
       <th scope="row">
        <a href="www/sprzet/">Sprzęt</a>
       </th>
       <td>
        <a href="www/sprzet/add/" class="addlink">Dodaj</a>
       </td>
       <td>
        <a href="www/sprzet/" class="changelink">Zmień</a>
       </td>
      </tr>
      <tr>
       <th scope="row">
        <a href="www/asset/">Pliki statyczne</a>
       </th>
       <td>
        <a href="www/asset/add/" class="addlink">Dodaj</a>
       </td>
       <td>
        <a href="www/asset/" class="changelink">Zmień</a>
       </td>
      </tr>
      <tr>
       <th scope="row">
        <a href="www/highlight/">Highlighty</a>
       </th>
       <td>
        <a href="www/highlight/add/" class="addlink">Dodaj</a>
       </td>
       <td>
        <a href="www/highlight/" class="changelink">Zmień</a>
       </td>
      </tr>
      <tr>
       <th scope="row">
        <a href="www/tagdlugosc/">Tagi - długość</a>
       </th>
       <td>
        <a href="www/tagdlugosc/add/" class="addlink">Dodaj</a>
       </td>
       <td>
        <a href="www/tagdlugosc/" class="changelink">Zmień</a>
       </td>
      </tr>
      <tr>
       <th scope="row">
        <a href="www/tagtechnologia/?widoczny_publicznie__exact=1">Tagi - technologia</a>
       </th>
       <td>
        <a href="www/tagtechnologia/add/" class="addlink">Dodaj</a>
       </td>
       <td>
        <a href="www/tagtechnologia/?widoczny_publicznie__exact=1"
           class="changelink">Zmień</a>
       </td>
      </tr>
      <tr>
       <th scope="row">
        <a href="www/tagzawod/?widoczny_publicznie__exact=1">Tagi - zawód</a>
       </th>
       <td>
        <a href="www/tagzawod/add/" class="addlink">Dodaj</a>
       </td>
       <td>
        <a href="www/tagzawod/?widoczny_publicznie__exact=1" class="changelink">Zmień</a>
       </td>
      </tr>
      <tr>
       <th scope="row">
        <a href="www/tab/">Taby</a>
       </th>
       <td></td>
       <td>
        <a href="www/tab/" class="changelink">Zmień</a>
       </td>
      </tr>
      <tr>
       <th scope="row">
        <a href="www/sciezka/">Ścieżki</a>
       </th>
       <td>
        <a href="www/sciezka/add/" class="addlink">Dodaj</a>
       </td>
       <td>
        <a href="www/sciezka/" class="changelink">Zmień</a>
       </td>
      </tr>
      <tr>
       <th scope="row">
        <a href="i18n/waluta/">Waluty</a>
       </th>
       <td>
        <a href="i18n/waluta/add/" class="addlink">Dodaj</a>
       </td>
       <td>
        <a href="18n/waluta/" class="changelink">Zmień</a>
       </td>
      </tr>
      <tr>
       <th scope="row">
        <a href="i18n/wersjajezykowa/">Wersje językowe</a>
       </th>
       <td>
        <a href="i18n/wersjajezykowa/add/" class="addlink">Dodaj</a>
       </td>
       <td>
        <a href="18n/wersjajezykowa/" class="changelink">Zmień</a>
       </td>
      </tr>
      <tr>
       <th scope="row">
        <a href="i18n/panstwo/">Państwa</a>
       </th>
       <td>
        <a href="i18n/panstwo/add/" class="addlink">Dodaj</a>
       </td>
       <td>
        <a href="18n/panstwo/" class="changelink">Zmień</a>
       </td>
      </tr>
      <tr>
       <th scope="row">
        <a href="www/fakturakorekta/">Faktury korygujące</a>
       </th>
       <td>
        <a href="www/fakturakorekta/add/" class="addlink">Dodaj</a>
       </td>
       <td>
        <a href="www/fakturakorekta/" class="changelink">Zmień</a>
       </td>
      </tr>
      <tr>
       <th scope="row">
        <a href="www/fakturawysylka/">Wysyłka Faktur</a>
       </th>
       <td>
        <a href="www/fakturawysylka/add/" class="addlink">Dodaj</a>
       </td>
       <td>
        <a href="www/fakturawysylka/" class="changelink">Zmień</a>
       </td>
      </tr>
      <tr>
       <th scope="row">
        <a href="www/zgloszenie/">Zgłoszenia</a>
       </th>
       <td>
        <a href="www/zgloszenie/add/" class="addlink">Dodaj</a>
       </td>
       <td>
        <a href="www/zgloszenie/" class="changelink">Zmień</a>
       </td>
      </tr>
      <tr>
       <th scope="row">
        <a href="www/continuationlog/">Logi notyfikacji o kontynuacji</a>
       </th>
       <td>
        <a href="www/continuationlog/" class="addlink"></a>
       </td>
       <td>
        <a href="www/continuationlog/" class="changelink">Zmień</a>
       </td>
      </tr>
      <tr>
       <th scope="row">
        <a href="www/continuationunsubscribed/">Wypisani z notyfikacji o kontynuacji</a>
       </th>
       <td>
        <a href="www/continuationunsubscribed/" class="addlink"></a>
       </td>
       <td>
        <a href="www/continuationunsubscribed/" class="changelink">Zmień</a>
       </td>
      </tr>
      <tr>
       <th scope="row">
        <a href="www/uczestniknotification/">Notyfikacje uczestnika</a>
       </th>
       <td>
        <a href="www/uczestniknotification/" class="addlink"></a>
       </td>
       <td>
        <a href="www/uczestniknotification/" class="changelink">Zmień</a>
       </td>
      </tr>
      <tr>
       <th scope="row">
        <a href="www/discountcode/">Kody rabatowe</a>
       </th>
       <td>
        <a href="www/discountcode/add/" class="addlink">Dodaj</a>
       </td>
       <td>
        <a href="www/discountcode/" class="changelink">Zmień</a>
       </td>
      </tr>
      <tr>
       <th scope="row">
        <a href="www/terminszkoleniamail/">Maile do grup</a>
       </th>
       <td>
        <a href="www/terminszkoleniamail/" class="addlink"></a>
       </td>
       <td>
        <a href="www/terminszkoleniamail/" class="changelink">Zmień</a>
       </td>
      </tr>
     </table>
    </div>
   {% else %}
    {# Podstawowa wersja strony głównej dla szarych userów #}
    {% if perms.ankiety.ogladanie_wynikow_swoich_ankiet %}
     <div class="module">
      <table summary="Ankiety">
       <caption>Ankiety</caption>
       <tr>
        <th scope="row">
         <a href="{% url 'przegladanie_index' %}">&nbsp;&nbsp;&nbsp;&nbsp;Przeglądanie ankiet</a>
        </th>
        <td></td>
       </tr>
      </table>
     </div>
    {% endif %}
    {% if app_list %}
     {% for app in app_list %}
      <div class="app-{{ app.app_label }} module">
       <table>
        <caption>
         <a href="{{ app.app_url }}"
            class="section"
            title="{% blocktrans with name=app.name %}Models in the {{ name }} application{% endblocktrans %}">{{ app.name }}</a>
        </caption>
        {% for model in app.models %}
         <tr class="model-{{ model.object_name|lower }}">
          {% if model.admin_url %}
           <th scope="row">
            <a href="{{ model.admin_url }}">{{ model.name }}</a>
           </th>
          {% else %}
           <th scope="row">{{ model.name }}</th>
          {% endif %}
          {% if model.add_url %}
           <td>
            <a href="{{ model.add_url }}" class="addlink">{% trans 'Add' %}</a>
           </td>
          {% else %}
           <td>&nbsp;</td>
          {% endif %}
          {% if model.admin_url %}
           <td>
            <a href="{{ model.admin_url }}" class="changelink">{% trans 'Change' %}</a>
           </td>
          {% else %}
           <td>&nbsp;</td>
          {% endif %}
         </tr>
        {% endfor %}
       </table>
      </div>
     {% endfor %}
    {% else %}
     <p>{% trans "You don't have permission to edit anything." %}</p>
    {% endif %}
   {% endif %}
  </div>
 {% endif %}
{% endblock %}
{% block sidebar %}{% endblock %}
