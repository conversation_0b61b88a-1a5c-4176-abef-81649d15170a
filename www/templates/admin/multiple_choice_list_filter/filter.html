{% load i18n %}
<h3>{% blocktrans with filter_title=title %} By {{ filter_title }} {% endblocktrans %}</h3>
<ul class="mulitple-choice">
{% for choice in choices %}
    {% if choice.reset %}
        <li{% if choice.selected %} class="selected"{% endif %}>
            <a href="{{ choice.query_string|iriencode }}" title="Wszystko">Wsz<PERSON>tko</a>
        </li>
    {% endif %}
{% endfor %}
{% for choice in choices %}
    {% if not choice.reset %}
        <li{% if choice.selected %} class="selected"{% endif %}>
          <a href="{{ choice.query_string|iriencode }}" title="{{ choice.display }}" style="display:inline">{{ choice.display }}</a>
          {% if choice.selected and choice.exclude_query_string %}
            <a class="small" href="{{ choice.exclude_query_string|iriencode }}" style="display:inline">(usuń)</a>
          {% endif %}
          {% if not choice.selected and choice.include_query_string %}
            <a class="small" href="{{ choice.include_query_string|iriencode }}" style="display:inline">(dodaj)</a>
          {% endif %}
        </li>
    {% endif %}
{% endfor %}
</ul>