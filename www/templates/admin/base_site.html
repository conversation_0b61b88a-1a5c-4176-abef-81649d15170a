{% extends "admin/base.html" %}
{% load i18n %}

{% block title %}{{ title }} | {% trans 'ALX - administracja' %}{% endblock %}

{% block branding %}
<h1 id="site-name">{% trans 'ALX - administracja' %}</h1>
{% endblock %}

{% block nav-global %}{% endblock %}

{% block messages %}
  {{ block.super }}
  {% if LDAP_ENABLED and request.user.logininfo.password_needs_change %}
    <ul class="messagelist">
      <li class="warning">
        Uwaga! Twoje hasło nie było zmienione przez przynajmniej {{ DNI_WAZNOSCI_HASLA }} dni. Przypominamy o konieczności regularnej zmiany swojego hasła!
        <form method="post" action="{% url 'alx_auth:dismiss_reminder' %}" style="display: inline;"> {% csrf_token %}
          <input type="hidden" name="redirect" value="{{ URL_ZMIANY_HASLA }}">
          <input type="submit" value="Zmień hasło" formnovalidate>
        </form>

        <form method="post" action="{% url 'alx_auth:dismiss_reminder' %}" style="display: inline;"> {% csrf_token %}
          <input type="hidden" name="redirect" value="{{ request.get_full_path }}">
          <input type="submit" value="Zamknij komunikat" formnovalidate>
        </form>
      </li>
    </ul>
  {% endif %}
{% endblock %}

{% block userlinks %}
    <a href="{% url 'reset_nginx_cache' %}"><strong>Wyczyść cache serwisu</strong></a> /
  {% if user.has_usable_password %}
      <a href="{% url 'admin:password_change' %}">{% trans 'Change password' %}</a> /
  {% endif %}

  <a href="{% url 'admin:logout' %}">{% trans 'Log out' %}</a>
{% endblock %}
