{% load i18n %}

<h3>{% blocktrans with filter_title=title %} By {{ filter_title }} {% endblocktrans %}</h3>
<ul>
    <li>
        <form method="GET">
<select id="id_{{ spec.parameter_name }}" name="{{ spec.parameter_name }}" multiple="multiple" style="width: 100%">
    {% for id, name in spec.lookup_choices %}
        <option value="{{ id }}"
            {% if id|stringformat:"s" in spec.selected_values%}
                selected
            {% endif %}
        >{{ name }}</option>
    {% endfor %}
</select>
            <input class="btn btn-info" type="submit" value="{% trans 'Apply' %}">
            <button type="button" class="btn btn-info"><a href="?">{{ _('Clear') }}</a></button>
        </form>
    </li>
</ul>



<script>
    $(document).ready(function() {
        $('#id_{{ spec.parameter_name }}').select2({
            placeholder: "{% trans 'Wybierz prowadzących' %}",
            allowClear: true,
            ajax: {
                url: "{% url 'prowadzacy-autocomplete' %}",
                dataType: 'json',
                delay: 250,
                data: function (params) {
                    return {
                        q: params.term,
                    };
                },
                processResults: function (data) {
                    return {
                        results: data.results
                    };
                },
                cache: true
            }
        });
    });
</script>
