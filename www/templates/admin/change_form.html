{% extends "admin/change_form.html" %}
{% load i18n admin_urls static admin_modify %}


{% block object-tools %}
{% if change %}{% if not is_popup %}
  <ul class="object-tools">
    {% block object-tools-items %}

    <li><a href="{% url 'admin:www_change_changelist' %}?object_id={{ original.pk|admin_urlquote }}&content_type__id__exact={{ content_type_id }}" class="historylink">{% trans "History" %}</a></li>
    {% if has_absolute_url %}<li><a href="{{ absolute_url }}" class="viewsitelink">{% trans "View on site" %}</a></li>{% endif %}
    {% endblock %}
  </ul>
{% endif %}{% endif %}
{% endblock %}
