{% extends "admin/base_site.html" %}
{% load i18n admin_static %}
{% block title %}Zestawienie danych z ankiet | {% trans 'ALX - administracja' %}{% endblock title %}

{% block content %}

<h1>Test</h1>
<div id="changelist">
  <div class="results">
    <table id="result_list">
      <tbody>
        {% for ankieta in ankiety  %}
        <tr>
          <td>
            {{ ankieta.tresc }}
          </td>
        </tr>
        {% endfor %}
      </tbody>
    </table>
  </div>

  <div class="pagination">
    <span class="step-links">
      {% if ankiety.has_previous %}
        <a href="?page={{ contacts.previous_page_number }}">previous</a>
      {% endif %}

      <span class="current">
        Page {{ ankiety.number }} of {{ ankiety.paginator.num_pages }}.
      </span>

      {% if ankiety.has_next %}
        <a href="?page={{ ankiety.next_page_number }}">next</a>
      {% endif %}
    </span>
  </div>
</div>

{% endblock content %}

