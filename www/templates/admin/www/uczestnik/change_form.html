{% extends "admin/change_form.html" %}
{% load admin_static %}

{% block object-tools-items %}
    {% if change and not adminform.form.errors %}
        {% if original.can_send_fvat_email %}
            <li><a class="run-modal" href="{% url 'admin:wyslij_fakture' original.pk 'fvat' %}">Wyślij: {{ original.nazwy_faktur.vat }}</a></li>
        {% elif original.can_send_proforma_email %}
            <li><a class="run-modal" href="{% url 'admin:wyslij_fakture' original.pk 'proforma' %}">Wyślij: {{ original.nazwy_faktur.proforma }}</a></li>
        {% endif%}

        {% if original.can_send_invoice_installments_email %}
            <li><a class="run-modal" href="{% url 'admin:wyslij_fakture' original.pk 'fvat_raty' %}">Wyśli<PERSON>: {{ original.nazwy_faktur.vat_raty }}</a></li>
        {% endif%}

        {% if original.can_send_invoice_note_email %}
            <li><a class="run-modal" href="{% url 'admin:wyslij_fakture' original.pk 'korekta' %}">Wyślij: {{ original.nazwy_faktur.korekta }}</a></li>
        {% endif%}
        {% if original.can_send_agreement_email %}
            <li><a class="run-modal" href="{% url 'admin:wyslij_umowe' original.pk %}">Wyślij umowę</a></li>
        {% endif%}
        {% if original.wyslac_mail_o_potwierdzeniu_terminu %}
            <li><a id="wyslij_mail_o_potwierdzeniu_terminu" onclick="return confirm('Czy na pewno chcesz wysłać powiadomienie do Uczestnika?')" href="{% url 'admin:wyslij_mail_o_potwierdzeniu_terminu' original.pk %}">Wyślij mail o potwierdzeniu terminu</a></li>
        {% endif%}
    {% endif %}
    {{ block.super }}
{% endblock %}

{% block extrahead %}
    {{ block.super }}
    <link rel="stylesheet" type="text/css" href="{% static "custom_admin/css/tooltip.css" %}" />
      <link rel="stylesheet" href="{% static "custom_admin/css/jquery.simplemodal.css" %}" type="text/css">
      <link rel="stylesheet" href="{% static "custom_admin/css/modal.css" %}" type="text/css">
      <link rel="stylesheet" href="{% static "custom_admin/css/modal-normalize.css" %}" type="text/css">
      <!--[if lt IE 7]>
          <link rel="stylesheet" href="{% static "custom_admin/css/jquery.simplemodal.ie.css" %}" media="screen">
      <![endif]-->
    <script type="text/javascript">
        {% if change and not adminform.form.errors %}
            {% if original.faktura_zaliczkowa and original.zaplacone and not original.zaliczka_zaplacone %}
                $(function() {
                    $('a.admin-fvat-link').click(function (e) {
                        e.preventDefault();
                        var r = confirm("Uwaga: faktura jest opłacona, ale nie ma ustawionej daty opłacenia zaliczki, dlatego faktura zaliczkowa, którą generujesz nie będzie oznaczona jako opłacona. Czy na pewno chcesz kontynuować?");
                        if (r == true)   {
                           window.location = $(this).attr('href');
                        }
                        return false;
                    });
                })
            {% endif %}

            {% if original.can_send_invoice_installments_email or original.can_send_proforma_email or original.can_send_fvat_email or original.can_send_invoice_note_email or original.can_send_agreement_email %}
                $(function() {
                    var notifications = {
                        url: null,
                        init: function () {
                            $('a.run-modal').click(function (e) {
                                e.preventDefault();
                                var that = $(this);
                                notifications.url = that.attr('href');
                                $.ajax({
                                    url: notifications.url,
                                    type: 'get',
                                    cache: false,
                                    dataType: 'html',
                                    success: function (data) {
                                        $.modal(data, {
                                            position: ["10%",],
                                            minWidth: "80%",
                                            minHeight: "80%",
                                            maxHeight: "80%",
                                            autoPosition: true,
                                            containerCss: {
                                                'maxHeight' : '80%',
                                                'overflow' : 'auto'
                                            },
                                            overlayId: 'simplemodal-overlay',
                                            containerId: 'simplemodal-container',
                                            dataId: 'simplemodal-data',
                                            onShow: notifications.show,
                                            onClose: notifications.close
                                        });
                                    }
                                });
                            });
                            $(document).on("click", "#subscription-container a.btn", function(e) {
                                $('.simplemodal-close').click();
                            })
                        },
                        show: function (dialog) {
                            var h = 1;
                            var container_h = $('#subscription-container').height();

                            if (container_h) {
                                h += container_h;
                            }

                            dialog.container.animate({
                                height: h
                            });

                            $('#subscription-ajax-form-submit').click(function (e) {
                                e.preventDefault();
                                e.preventDefault();
                                $(this).prop('disabled', true);
                                $(this).prop('value', 'Wysyłam ...');

                                var data = $('#subscription-ajax-form').serializeArray();

                                $.ajax({
                                    url: notifications.url,
                                    data: data,
                                    type: 'post',
                                    cache: false,
                                    dataType: 'html',
                                    success: function (data) {
                                        $('#simplemodal-data').html(data).fadeIn(200);
                                        return notifications.show(dialog);
                                    },
                                    error: notifications.error
                                });
                            });
                        },
                        close: function (dialog) {
                            dialog.data.fadeOut('slow', function () {
                                dialog.container.slideUp('slow', function () {
                                    dialog.overlay.fadeOut('slow', function () {
                                        $.modal.close(); // must call this!
                                    });
                                });
                            });
                            window.location.reload();
                        },
                        error: function (xhr) {
                            $('#simplemodal-data').html("Error ...").fadeIn(200);
                        }
                    };
                    notifications.init();
                });
            {% endif %}
        {% endif %}
    </script>

    <script type="text/javascript">
        jQuery = $;
    </script>
    <script src="{% static "js/jquery.simplemodal.js" %}"></script>
{% endblock extrahead %}

{% block content %}
    <script>
        function uczestnikDragStart(e) {
            var data = {};
            data['faktura_firma'] = document.getElementById('id_faktura_firma').value;
            data['faktura_nip'] = document.getElementById('id_faktura_nip').value;
            data['faktura_adres'] = document.getElementById('id_faktura_adres').value;
            data['faktura_miejscowosc_kod'] = document.getElementById('id_faktura_miejscowosc_kod').value;
            data['email'] = document.getElementById('id_email').value;
            data['adres'] = document.getElementById('id_adres').value;
            data['miejscowosc_kod'] = document.getElementById('id_miejscowosc_kod').value;
            e.dataTransfer.setData('text/plain', JSON.stringify(data));
        };
        $(function() {
            var termin_id = '{{adminform.form.termin.value}}';
            var termin_link = ' &nbsp;<a href="/admin/www/terminszkolenia/' +
                termin_id + '" id="termin_link">' +
               '<img src="/static/custom_admin/img/strzalka_s.png" height="20" alt="Przejdz"/></a>';
            var dodaj_link_terminu = function(){
                $('#add_id_termin').after(termin_link);
            };
            dodaj_link_terminu();

            var pola_faktury = [
                    'faktura_firma',
                    'faktura_adres',
                    'faktura_miejscowosc_kod',
                    'faktura_nip',
                    'faktura_vat_id',
            ];

            var wlaczniki = [
                {
                    pole_wlacznika: "prywatny",
                    pola_zalezne: pola_faktury.concat(['podmiot_publiczny',
                                                       'uczestnik_wieloosobowy_ilosc_osob']),
                    rodzaj: true
                }
            ];

            var zaleznosci = function(pole_wlacznika, pola_zalezne, rodzaj) {
                var obiekt_wlacznika = $("#id_" + pole_wlacznika);
                $.each(pola_zalezne, function(index, pole) {
                    var obiekt_pola = $('#id_' + pole);
                    if (obiekt_pola.attr('type') != 'hidden') {
                        var tr = obiekt_pola[0];
                        obiekt_wlacznika.change(function() {
                            if (rodzaj && obiekt_wlacznika[0].checked){
                               tr.disabled = true;
                            }else{
                               tr.disabled = false;
                            }
                        });
                        if ((rodzaj && obiekt_wlacznika[0].checked) ||
                                (!rodzaj && !obiekt_wlacznika[0].checked)) {
                            tr.disabled = true;
                        }
                    }
                });
            };
            $.each(wlaczniki, function(index, przelacznik) {
                zaleznosci(przelacznik.pole_wlacznika, przelacznik.pola_zalezne, przelacznik.rodzaj);
            });
            var wysylaj_proformy = "{{adminform.form.fields.termin.wysylaj_proformy}}";
            var wysylaj_proformy_checkbox = $("#id_wystaw_proforme_automatycznie")[0];
            if (wysylaj_proformy == 'False'){
                wysylaj_proformy_checkbox.disabled = true;
                wysylaj_proformy_checkbox.checked = false;
            };
        });
    </script>
    <img src="{% static "custom_admin/img/card.png" %}" draggable="true" ondragstart="uczestnikDragStart(event);" />
    <script type="text/javascript" src="{% static "custom_admin/js/uczestnik_change_form.js" %}"></script>
{{ block.super }}
{% endblock content %}