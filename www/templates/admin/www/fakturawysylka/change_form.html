{% extends "admin/change_form.html" %}
{% load admin_static %}


{% block object-tools %}
    {% if change and not original.dispatched_at %}
        <div style="margin:20px">
        <p>
            <strong>UWAGA: Sprawd<PERSON> adres w polach teleadresowych i ew. zmień jeśli ustalenia z Klientem były inne niż dane na fakturze.</strong>
        </p>

        <p>
            Dane uzu<PERSON>łniane są na podstawie <u>faktury</u> (pola z uwagami *nie* są automatycznie uwzględnianie):
            <br><br>
            ID faktury: <strong>{{ original.faktura_id }}</strong><br>
            Numer faktury: <strong>{{ original.faktura_numer }}</strong><br>
            Nazwa: <strong>{{ original.recipient_name }}</strong><br>
            Adres nabywcy z faktury: <strong>{{ original.faktura_adres_nabywcy }}</strong><br>
            Uwagi na fakturze: <strong>{{ original.faktura_uwagi|default:"-" }}</strong><br>
        </p>

        {% if original.uczestnik %}
            <p>
                Dodatkowo na podstawie numeru faktury dowiązaliśmy Uczestnika:
                <br><br>
                Nazwa: <strong>{{ original.uczestnik.dane_do_faktury.klient }}</strong><br>
                Adres: <strong>{{ original.uczestnik.dane_do_faktury.klient_adres }}, {{ original.uczestnik.dane_do_faktury.klient_kod_pocztowy }} {{ original.uczestnik.dane_do_faktury.klient_miasto }}</strong><br>
                Uwagi Klienta: <strong>{{ original.uczestnik.uwagi_klienta|default:"-" }}</strong><br>
                Uwagi Biura: <strong>{{ original.uczestnik.uwagi|default:"-" }}</strong>
            </p>
        {% else %}
            <p><em>Niestety nie udało się odnaleźć Uczestnika na podstawie numeru faktury.</em></p>
        {% endif %}

        </div>
    {% endif %}
    {{ block.super }}
{% endblock %}


{% block object-tools-items %}
    {% if change and not adminform.form.errors and not original.dispatched_at %}
        <li><a href="{% url 'admin:postivo' original.pk %}" id="parcel-dispatch">Zleć wysyłkę</a></li>
    {% endif %}
    {{ block.super }}
{% endblock %}
