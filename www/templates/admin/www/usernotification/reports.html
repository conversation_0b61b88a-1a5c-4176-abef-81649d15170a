{% extends "admin/base_site.html" %}
{% load i18n admin_static admin_list mytags %}
{% load admin_urls %}

{% block title %}Raporty | {% trans 'Django site admin' %}{% endblock %}

{% block extrastyle %}
    {{ block.super }}
    <link rel="stylesheet" type="text/css" href="{% static "admin/css/changelists.css" %}" />
    <link rel="stylesheet" href="//code.jquery.com/ui/1.11.2/themes/smoothness/jquery-ui.css">
    <style>
      #changelist table thead th:first-child {width: inherit}

    #id_date_from, #id_date_to {
        width: 70px;
    }
    </style>
{% endblock %}


{% block extrahead %}
    {{ block.super }}
    <script src="//code.jquery.com/jquery-1.10.2.js"></script>
    <script src="//code.jquery.com/ui/1.11.2/jquery-ui.js"></script>

    <script>
      $(function() {
        $.datepicker.regional['pl'] = {
            closeText: 'Zamknij',
            prevText: '&#x3c;Poprzedni',
            nextText: 'Następny&#x3e;',
            currentText: 'Dziś',
            monthNames: ['Styczeń','Luty','Marzec','Kwiecień','Maj','Czerwiec',
            'Lipiec','Sierpień','Wrzesień','Październik','Listopad','Grudzień'],
            monthNamesShort: ['Sty','Lu','Mar','Kw','Maj','Cze', 'Lip','Sie','Wrz','Pa','Lis','Gru'],
            dayNames: ['Niedziela','Poniedziałek','Wtorek','Środa','Czwartek','Piątek','Sobota'],
            dayNamesShort: ['Nie','Pn','Wt','Śr','Czw','Pt','So'],
            dayNamesMin: ['N','Pn','Wt','Śr','Cz','Pt','So'],
            weekHeader: 'Tydz',
            dateFormat: 'dd.mm.yy',
            firstDay: 1,
            isRTL: false,
            showMonthAfterYear: false,
            yearSuffix: ''
        };
        $.datepicker.setDefaults($.datepicker.regional['pl']);

        $("#id_date_from").datepicker();
        $("#id_date_to").datepicker();
      });
      </script>
{% endblock %}

{% block bodyclass %}change-list{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
<a href="{% url 'admin:index' %}">{% trans 'Home' %}</a>
&rsaquo; <a href="{% url 'admin:app_list' app_label="www" %}">Www</a>
&rsaquo; <a href="{% url 'admin:www_usernotification_changelist' %}">Powiadomienia o szkoleniach</a>
&rsaquo; Raporty
</div>
{% endblock %}

{% block coltype %}flex{% endblock %}

{% block content %}
<div id="content-main">

    <h1>Raporty o zapisanych na powiadomienia</h1>

        <div class="module filtered" id="changelist">
            <div id="toolbar"><form id="changelist-search" action="" method="get">
            <div style="margin: 5px">Unikalne zapisy (per szkolenie): <strong>{{ total_users }}</strong>. Zapisy w miastach: <strong>{{ total_users_in_locations }}</strong></div>
            </form></div>

          <div id="changelist-filter">
            <h2>{% trans 'Filter' %}</h2>
                <form action="." method="GET">
                    <h3>Data zapisu</h3>
                    <ul>
                        <li>
                            Od: {{ filters_form.date_from }} {{ filters_form.date_from.errors.as_ul }}
                        </li>
                        <li>
                            Do: {{ filters_form.date_to }} {{ filters_form.date_to.errors.as_ul }}
                        </li>
                        <li>
                            <input type="submit" value="Zastosuj">
                        </li>
                    </ul>
                </form>
          </div>

            <div class="results">
                    <table width="90%">
                        <tr>
                            <td width="50%">
                                {% for row in results %}
                                    {% notification_reports_tag row locations trainings as res %}

                                    <h3><a href="{% url 'admin:www_usernotification_changelist' %}?q={{ res.training.nazwa }}">{{ res.training }}</a></h3>

                                    <h4>
                                        Unikalne zapisy (per szkolenie): {{ row.total_users }}
                                        <br>
                                        Zapisy we wszystkich miastach: {{ row.total_users_in_locations }}
                                    </h4>

                                    <p>
                                        {% for l in res.locations %}
                                            <a href="{% url 'admin:www_usernotification_changelist' %}?q={{ res.training.nazwa }}&preferences__locations__id__exact={{ l.location.pk }}">{{ l.location.fullname }}</a>: {{ l.users_count }}<br>
                                        {% endfor %}
                                    </p>
                                {% empty %}
                                    <p>Brak wyników</p>
                                {% endfor %}
                            </td>
                            <td valign="top">
                                <h3>Podsumowanie według miast</h3>
                                <br>
                                <p>
                                    {% for k, v in locations_summary.items %}
                                        <a href="{% url 'admin:www_usernotification_changelist' %}?preferences__locations__id__exact={{ v.obj.pk }}">{{ v.obj.fullname }}</a>: {{ v.count }}<br>
                                    {% endfor %}
                                </p>

                            </td>
                        </tr>
                </table>

            </div>

    </div>
  </div>
{% endblock %}
