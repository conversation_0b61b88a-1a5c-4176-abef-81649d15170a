{% extends "admin/change_list.html" %}
{% load i18n admin_static admin_list %}
{% load admin_urls %}

{% block extrastyle %}
  {{ block.super }}
    <style>
        th:nth-child(7) {
             min-width: 200px;
        }
    </style>
{% endblock %}


{% block object-tools %}
    <ul class="object-tools">
      {% block object-tools-items %}
        {% if has_add_permission %}
        <li>
          {% url cl.opts|admin_urlname:'add' as add_url %}
          <a href="{% add_preserved_filters add_url is_popup to_field %}" class="addlink">
            {% blocktrans with cl.opts.verbose_name as name %}Add {{ name }}{% endblocktrans %}
          </a>
        </li>
        {% endif %}
        <li>
            <a href="{% url 'admin:user_notification_reports' %}">Raporty</a>
        </li>
      {% endblock %}
    </ul>
{% endblock %}
