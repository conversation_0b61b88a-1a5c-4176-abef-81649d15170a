<style type='text/css'>
    #live-preview a {
        color: #5b80b2;
    }
    #id_message {
        height: 210px;
        width: 100%
    }
    #id_subject {
        width: 70%
    }
    select, file {
        width: 230px;
        margin: 0 0 0 5px;
        font-family: Arial, sans-serif;
        font-size: 13px;
        color: #818181;
        padding: 10px 8px;
        border: 1px solid #bfbfbf;
    }
</style>
<div id="subscription-container" class="lang-{{ LANGUAGE_CODE }}">
    <div class="morph-button morph-button-modal morph-button-modal-2">
        <div class="morph-content">
            <div>
                <div class="content-style-form content-style-form-1">
                    <div class="header-bg" style="height: 80px">
                        <span class="icon-close">Close</span>
                        <h2>Wyślij email do grupy</h2>
                    </div>


                <div class="modal-content">
                        <form id="subscription-ajax-form" style="margin: 0" enctype="multipart/form-data">
                            {% csrf_token %}

                            <table width="100%">
                                <tr>
                                    <td width="50%" style="padding-right: 20px">
                                        <div class="your-email">
                                            <label for="email" style="max-width: 180px; width: 180px">
                                                Wyślij do:</label>
                                            {{ form.participant_status }}
                                            {{ form.participant_status.errors.as_ul }}
                                        </div>
                                        <div class="your-email">
                                            <label for="email" style="max-width: 180px; width: 180px">
                                                Temat maila</label>
                                            {{ form.subject }}
                                            {{ form.subject.errors.as_ul }}
                                        </div>
                                        <div class="your-email">
                                            <label for="email" style="max-width: 180px; width: 180px">
                                                Załącznik (opcjonalny)</label>
                                            {{ form.attachment }}
                                            {{ form.attachment.errors.as_ul }}
                                        </div>
                                        <div class="your-email">
                                            <h2>Treść maila
                                                (wymagany HTML)</h2>
                                            {{ form.message }}
                                            {{ form.message.errors.as_ul }}
                                        </div>

                                    </td>
                                    <td style="padding-left: 20px">
                                        <h2>Podgląd maila</h2>
                                        <div id="live-preview" style="font-weight: normal; padding-top: 20px"></div>
                                    </td>
                                </tr>
                            </table>

                            <script>
                                $(function () {
                                        $('#id_message').on('keyup', function(e) {
                                            $('#live-preview').html($(this).val());
                                        });

                                        $('#id_message').keyup();
                                });
                            </script>

                            <div class="buttons">
                                <input class="btn btn-red" type="submit"
                                       value="Wyślij"
                                       id="subscription-ajax-form-submit" />
                                <a href="#" class="btn btn-gray btn-close">Anuluj</a>
                            </div>

                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
