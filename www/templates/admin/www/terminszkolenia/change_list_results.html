{% load i18n admin_static myfilters %}
{% if result_hidden_fields %}
<div class="hiddenfields">{# DIV for HTML validation #}
{% for item in result_hidden_fields %}{{ item }}{% endfor %}
</div>
{% endif %}
{% if results %}
<div class="results">
<a onclick="$('.shown_uczestnicy').hide(); $('.hidden_uczestnicy').show();return false;" href="#">zwiń wszystkich uczestników</a></span>
<a onclick="$('.hidden_uczestnicy').hide(); $('.shown_uczestnicy').show();return false;" href="#">rozwiń wszystkich uczestników</a></span>
<table cellspacing="0" id="result_list">
<thead>
<tr>
{% for header in result_headers %}
<th scope="col" {{ header.class_attrib }}>
   {% if header.sortable %}
     {% if header.sort_priority > 0 %}
       <div class="sortoptions">
         <a class="sortremove" href="{{ header.url_remove }}" title="{% trans "Remove from sorting" %}"></a>
         {% if num_sorted_fields > 1 %}<span class="sortpriority" title="{% blocktrans with priority_number=header.sort_priority %}Sorting priority: {{ priority_number }}{% endblocktrans %}">{{ header.sort_priority }}</span>{% endif %}
         <a href="{{ header.url_toggle }}" class="toggle {% if header.ascending %}ascending{% else %}descending{% endif %}" title="{% trans "Toggle sorting" %}"></a>
       </div>
     {% endif %}
   {% endif %}
   <div class="text">{% if header.sortable %}<a href="{{ header.url_primary }}">{{ header.text|capfirst }}</a>{% else %}<span>{{ header.text|capfirst }}</span>{% endif %}</div>
   <div class="clear"></div>
</th>{% endfor %}
</tr>
</thead>
<tbody>
{% for result in results %}
{% if result.form.non_field_errors %}
    <tr><td colspan="{{ result|length }}">{{ result.form.non_field_errors }}</td></tr>
{% endif %}
<tr class="{% cycle 'row1' 'row2' %}">{% for item in result.1 %}{{ item }}{% endfor %}</tr>
{% if result.0.uczestnik_set.all %}
<tr>
<td></td>
<td colspan="7">
<span style="display: none;" class="hidden_uczestnicy hidden_uczestnicy_{{ result.0.id }}"><a onclick="$('.hidden_uczestnicy_{{ result.0.id }}').hide(); $('.shown_uczestnicy_{{ result.0.id }}').show();return false;" href="#">pokaż uczestników</a></span>
<span class="shown_uczestnicy shown_uczestnicy_{{ result.0.id }}"><a onclick="$('.shown_uczestnicy_{{ result.0.id }}').hide(); $('.hidden_uczestnicy_{{ result.0.id }}').show(); return false;" href="#">schowaj uczestników</a></span>
<table class="shown_uczestnicy shown_uczestnicy_{{ result.0.id }}">
{% for u in result.0.uczestnik_set.all %}
<tr class="termin-status-{{ u.status }}">
<td>{{ u.zaplacone3_html }}</td>
<td>
  {% if u.chce_obiady %}
    {% if u.ile_obiadow_wegetarianskich %}

      {% if not u.uczestnik_wieloosobowy_ilosc_osob or u.ile_obiadow_wegetarianskich == u.uczestnik_wieloosobowy_ilosc_osob %}
    	<img src="/static/icons/food_vege16.png"{% if u.status == 4 %} style="opacity: 0.4; -webkit-filter: grayscale(1); filter: url('data:image/svg+xml;utf8,<svg xmlns=\'http://www.w3.org/2000/svg\'><filter id=\'grayscale\'><feColorMatrix type=\'matrix\' values=\'0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0 0 0 1 0\'/></filter></svg>#grayscale'); filter: gray; filter: alpha(opacity=40);"{% endif %} />
      {% else %}
    	<abbr title="liczba obiadów / liczba obiadów wegetariańskich">{{ u.uczestnik_wieloosobowy_ilosc_osob }}/{{ u.ile_obiadow_wegetarianskich }}</abbr>
      {% endif %}
    {% else %}
      <img src="/static/icons/food16.png"{% if u.status == 4 %} style="opacity: 0.4; filter: alpha(opacity=40);"{% endif %} />
    {% endif %}
  {% endif %}
  {% if u.nierentowny %}
      <img src="/static/icons/nierentowny.png"{% if u.status == 4 %} style="opacity: 0.4; filter: alpha(opacity=40);"{% endif %} />
  {% endif %}
  {% if u.drukowany_certyfikat %}
      <span style="font-size: 15px; font-weight: bold"> C</span>
  {% endif %}
  {% if u.chce_fakture %}
      <span style="font-size: 15px; font-weight: bold"> F</span>
  {% endif %}
</td>
{% if u.termin.autoryzacja %}
  <td><abbr title="{{ u.chce_autoryzacje|yesno:'Chce autoryzację,Nie chce autoryzacji' }}" style="{% if u.status != 4 %}{{ u.chce_autoryzacje|yesno:'font-weight: bold; color: #070;,font-weight: 100; color: #aaa;' }}{% endif %}">Ⓐ</abbr></td>
{% endif %}
<td>
<a href="../uczestnik/{{ u.id }}/">{{ u.imie_nazwisko|linebreaksbr }}</a></td>
<td>{{ u.uwagi|linebreaksbr }}</td>
<td>{{ u.faktura_firma }}</td>
<td align="right">{{ u.zaplacono_dotychczas }}&nbsp;({{ u.zaplacono_dotychczas_brutto }})/<br/>{{ u.kwota_do_zaplaty }}&nbsp;({{ u.kwota_do_zaplaty_brutto }})</td>
<td>{{ u.get_status_display }}</td>
<td>{{ u.czas_dodania|date }}</td>
</tr>
{% endfor %}
</table>
<a href="{% url 'lista_obecnosci' termin_id=result.0.id z_obiadami=0 %}" title="Tylko uczestnicy ze statusem 'Potwierdzony'">lista obecności</a>
<a href="{% url 'lista_obecnosci' termin_id=result.0.id z_obiadami=1 %}" title="Tylko uczestnicy ze statusem 'Potwierdzony'">lista obecności z obiadami i papierowymi certyfikatami</a>
</td>
</tr>
{% endif %}
{% endfor %}
</tbody>
</table>
</div>
{% endif %}
