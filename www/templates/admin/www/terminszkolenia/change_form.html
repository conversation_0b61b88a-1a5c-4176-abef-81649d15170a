{% extends 'admin/change_form.html' %}
{% load admin_static %}

{% block object-tools-items %}
    {% if change %}<li id="copy-email-addresses-nav"><a href="#" id="copy-email-addresses">Skopiuj adresy email</a></li>{% endif %}
    {% if 0 and change and not original.wyslane_do_zainteresowanych_z_innych_miast and original.odbylo_sie and not adminform.form.errors and original.is_przyszle %}<li><a id="run-modal" href="{% url 'admin:potencjalnie_zainteresowani_z_innych_miast' original.pk %}">Zainteresowani z innych miast</a></li>{% endif%}
    {% if change and not adminform.form.errors and original.pk %}<li><a id="run-modal" class="wyslij_mail_do_grupy" href="{% url 'admin:wyslij_mail_do_grupy' original.pk %}">Wyślij mail do grupy</a></li>{% endif%}
    <li><a href="{% url 'admin:www_terminszkoleniamail_changelist' %}?term_id={{ original.pk }}">Historia maili do grupy</a></li>
    {{ block.super }}
{% endblock %}

{% block content %}
    {% if not adminform.form.fields.szkolenie.wysylaj_proformy %}
        <div class="infoextra">
            <span>
                Dla tego szkolenia nie można generować automatycznie powiadomień i proform, bo nie jest to standardowe szkolenie.
            </span>
        </div>
    {% endif %}

    {% if change and original.nested_objects_with_invalid_dates %}
        <div class="infoextra">
                <p><span>UWAGA: Niektóre szkolenia podrzędne nie pasują do dat tego terminu:</span></p>
                <ul>
                    {% for nobj in original.nested_objects_with_invalid_dates %}
                        <li><a href="../../{{ nobj.pk }}/change/">{{ nobj }}</a></li>
                    {% endfor %}
                </ul>
        </div>
    {% endif %}

{{ block.super }}
{% endblock content %}

{% block extrahead %}
    {{ block.super }}
      <link rel="stylesheet" href="{% static "custom_admin/css/jquery.simplemodal.css" %}" type="text/css">
      <link rel="stylesheet" href="{% static "custom_admin/css/modal.css" %}" type="text/css">
      <link rel="stylesheet" href="{% static "custom_admin/css/modal-normalize.css" %}" type="text/css">
      <!--[if lt IE 7]>
          <link rel="stylesheet" href="{% static "custom_admin/css/jquery.simplemodal.ie.css" %}" media="screen">
      <![endif]-->
    <link rel="stylesheet" type="text/css" href="{% static "custom_admin/css/tooltip.css" %}" />
    <script type="text/javascript">
        $(function() {
            $("div.field-box.field-wylacz_podrzedne").hide();

            {% if change and not original.termin_nadrzedny %}
                $("#id_zamkniete").click(function (e) {
                    var wylacz_podrzedne = $("#id_wylacz_podrzedne");
                    if($(this).is(':checked')) {
                        $("div.field-box.field-wylacz_podrzedne").show();
                        wylacz_podrzedne.prop("checked", true);
                    } else {
                        wylacz_podrzedne.prop("checked", false);
                        $("div.field-box.field-wylacz_podrzedne").hide();
                    }
                });
            {% endif %}

            $("a#copy-email-addresses").click(function (e) {
                e.preventDefault();

                var emails = "";
                var cnt = 0;

                $("div.inline-related.dynamic-uczestnik_set[id^=uczestnik_set-]").each(function() {

                    var status = $("div.form-row.field-status select[id*=-status]", this).val();

                    if (status === "1" || status === "3" || status === "-1") {
                        var email = $("div.form-row.field-email input[id*=-email]", this).val();
                        if (email) {
                            emails = emails.concat(email, " ");
                            cnt += 1;
                        }
                    }
                });

                if (emails) {
                    var $temp = $("<input>");
                    $("body").append($temp);
                    $temp.val(emails).select();
                    document.execCommand("copy");
                    $temp.remove();
                    $("li#copy-email-addresses-nav").css("background", "none").html("Skopiowano do schowka (" + cnt + ")&nbsp;&nbsp;");
                }
                else {
                    var $temp = $("<input>");
                    $("body").append($temp);
                    $temp.val(" ").select();
                    document.execCommand("copy");
                    $temp.remove();
                    $("li#copy-email-addresses-nav").css("background", "none").html("Brak potwierdzonych uczestników&nbsp;&nbsp;");
                }
            });

        });


        {% if 0 and change and not original.wyslane_do_zainteresowanych_z_innych_miast and original.odbylo_sie and not adminform.form.errors %}
            $(function() {
                var notifications = {
                    url: null,
                    init: function () {
                        $('a#run-modal').click(function (e) {
                            e.preventDefault();
                            var that = $(this);
                            notifications.url = "{% url 'admin:potencjalnie_zainteresowani_z_innych_miast' original.pk %}";
                            $.ajax({
                                url: notifications.url,
                                type: 'get',
                                cache: false,
                                dataType: 'html',
                                success: function (data) {
                                    $.modal(data, {
                                        position: ["10%",],
                                        minWidth: "80%",
                                        minHeight: "80%",
                                        maxHeight: "80%",
                                        autoPosition: true,
                                        containerCss: {
                                            'maxHeight' : '80%',
                                            'overflow' : 'auto'
                                        },
                                        overlayId: 'simplemodal-overlay',
                                        containerId: 'simplemodal-container',
                                        dataId: 'simplemodal-data',
                                        onShow: notifications.show
                                    });
                                }
                            });
                        });
                        $(document).on("click", "#subscription-container a.btn", function(e) {
                            $('.simplemodal-close').click();
                        })
                    },
                    show: function (dialog) {
                        $('#subscription-ajax-form-submit').click(function (e) {
                            e.preventDefault();
                            $(this).prop('disabled', true);
                            $(this).prop('value', 'Wysyłam ...');

                            var data = $('#subscription-ajax-form').serializeArray();
                            $.ajax({
                                url: notifications.url,
                                data: data,
                                type: 'post',
                                cache: false,
                                dataType: 'html',
                                success: function (data) {
                                    window.location.reload();
                                    return
                                },
                                error: notifications.error
                            });
                        });
                    },
                    error: function (xhr) {
                        $('#simplemodal-data').html("Error ...").fadeIn(200);
                    }
                };
                notifications.init();
            });
        {% endif %}


        {% if change and not adminform.form.errors and original.pk %}
            $(function() {
                var notifications = {
                    url: null,
                    init: function () {
                        $('a#run-modal').click(function (e) {
                            e.preventDefault();
                            var that = $(this);
                            notifications.url = "{% url 'admin:wyslij_mail_do_grupy' original.pk %}";
                            $.ajax({
                                url: notifications.url,
                                type: 'get',
                                cache: false,
                                dataType: 'html',
                                success: function (data) {
                                    $.modal(data, {
                                        position: ["10%",],
                                        minWidth: "80%",
                                        minHeight: "80%",
                                        maxHeight: "80%",
                                        autoPosition: true,
                                        containerCss: {
                                            'maxHeight' : '80%',
                                            'overflow' : 'auto'
                                        },
                                        overlayId: 'simplemodal-overlay',
                                        containerId: 'simplemodal-container',
                                        dataId: 'simplemodal-data',
                                        onShow: notifications.show
                                    });
                                }
                            });
                        });
                        $(document).on("click", "#subscription-container a.btn", function(e) {
                            $('.simplemodal-close').click();
                        })
                    },
                    show: function (dialog) {
                        var h = 1;
                        var container_h = $('#subscription-container').height();

                        if (container_h) {
                            h += container_h;
                        }

                        dialog.container.animate({
                            height: h
                        });

                        $('#subscription-ajax-form-submit').click(function (e) {
                            $(this).prop('disabled', true);
                            $(this).prop('value', 'Wysyłam ...');
                            e.preventDefault();

                            var form = $('#subscription-ajax-form')[0];
                            var data = new FormData(form);

                            $.ajax({
                                url: notifications.url,
                                data: data,
                                enctype: 'multipart/form-data',
                                processData: false,
                                contentType: false,
                                type: 'post',
                                cache: false,
                                dataType: 'html',
                                success: function (data) {
                                    $('#simplemodal-data').html(data).fadeIn(200);
                                    return notifications.show(dialog);
                                },
                                error: notifications.error
                            });
                        });
                    },
                    error: function (xhr) {
                        $('#simplemodal-data').html("Error ...").fadeIn(200);
                    }
                };
                notifications.init();
            });
        {% endif %}

        $(function() {

          if ($('.field-box.field-jobs_state p').text() == '(Brak)') {
            $('.field-box.field-jobs_state').hide();
          }

          var show_or_hide_snz_opis = function() {
            $('#id_snz_opis').closest('div.form-row').toggle($('#id_szkolenie')[0].value == '328');
          };

          var show_or_hide_fields_for_odbylo_sie = function(){
              var czy_robimy_text = $("#id_odbylo_sie option:selected").text();
              if (czy_robimy_text == "Tak") {
                  $("[name='czy_reklamowac']").closest('div.form-row').hide();
                  $('#id_sala_przygotowana').closest('div.form-row').show();
              } else {
                  $("[name='czy_reklamowac']").closest('div.form-row').show();
                  $('#id_sala_przygotowana').closest('div.form-row').hide();
              }

              var pokaz_linki = function(){
                  var uczestnik_set_initial = $('#id_uczestnik_set-INITIAL_FORMS') ;
                  var i_max = uczestnik_set_initial[0].value;
                  for (i=0; i<i_max; i++){
                      var uczestnik_id_element = $('#id_uczestnik_set\-' + String(i) + '\-id');
                      var id = uczestnik_id_element[0].value;
                      var uczestnik_link = '&nbsp;<a href="/admin/www/uczestnik/' + id +
                        '" id="uczestnik_link_' + String(i) + '">' +
                          '<img src="/static/custom_admin/img/strzalka_s.png" height="20" alt="Przejdz"/></a>';
                      var uczestnik = $('#uczestnik_set\-' + i + ' h3 .inline_label');
                      uczestnik.after(uczestnik_link);
                  }
              }

            pokaz_linki();
          };

          show_or_hide_snz_opis();
          $('#id_szkolenie').change(show_or_hide_snz_opis);

          show_or_hide_fields_for_odbylo_sie();
          $("#id_odbylo_sie").change(show_or_hide_fields_for_odbylo_sie);

            $('#terminszkolenia_form')[0].onsubmit = function() {
                var termin = $('#id_termin')[0].value;
                var daty_szczegolowo = $('#id_daty_szczegolowo')[0].value;
                if (daty_szczegolowo === '') {
                    return true;
                }
                var data0 = daty_szczegolowo.split(',')[0];
                if (data0 !== termin) {
                    if (confirm('Termin różni się od pierwszej daty w daty_szczegolowo. Zresetować daty_szczegolowo i kontynuować?')) {
                        $('#id_daty_szczegolowo')[0].value = '';
                        return true;
                    } else {
                        return false;
                    }
                }
            };

            var wysylaj_proformy = "{{adminform.form.fields.szkolenie.wysylaj_proformy}}";

            /*var wysylaj_proformy_checkbox = $("input[id*='wystaw_proforme_automatycznie']");
            if (wysylaj_proformy == 'False'){
                var l = wysylaj_proformy_checkbox.length;
                for (k = 0; k < l; k++){
                    wysylaj_proformy_checkbox[k].disabled = true;
                    wysylaj_proformy_checkbox[k].checked = false;
                }
            };*/

        /*var wysylaj_proformy_checkbox = $("input[id*='wystaw_proforme_automatycznie']");
        if (wysylaj_proformy == 'False'){
            var l = wysylaj_proformy_checkbox.length;
            for (k = 0; k < l; k++){
                wysylaj_proformy_checkbox[k].disabled = true;
                wysylaj_proformy_checkbox[k].checked = false;
            }
        };*/

            {% if change %}
                $('body').on('click', 'a#mark_user_as_confirmed', function(e) {
                    e.preventDefault();
                    var counter = 0;

                    $("select[id^=id_uczestnik_set-][id$=-status]").each(function (index) {
                        var imie_i_naziwsko = $('textarea[id=id_uczestnik_set-'+index+'-imie_nazwisko]').val();

                        if (imie_i_naziwsko) {
                            if ($(this).find('option[selected]').val() == 1) {
                                $(this).find("option[value=3]").attr('selected', true);
                                counter += 1;
                            }
                        }
                    });
                    alert("Liczba zmienionych rekordów: " + counter);
                });

                $('div.form-row.field-daty_szczegolowo').append(
                    '<div class="form-row"><a href="#" id="mark_user_as_confirmed">Oznacz uczestników jako "przeszkoleni".</a></div>'
                );
            {% endif %}
        });
    </script>

    <script type="text/javascript" src="{% static "custom_admin/js/terminszkolenia_change_form.js" %}"></script>
    <script src="{% static "custom_admin/js/jquery.simplemodal.js" %}"></script>
{% endblock extrahead %}