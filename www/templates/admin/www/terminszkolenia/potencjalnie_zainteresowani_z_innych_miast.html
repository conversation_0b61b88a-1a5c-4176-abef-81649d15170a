<div id="subscription-container" class="lang-{{ LANGUAGE_CODE }}">
    <div class="morph-button morph-button-modal morph-button-modal-2">
        <div class="morph-content">
            <div>
                <div class="content-style-form content-style-form-1">
                    <div class="header-bg" style="height: 80px">
                        <span class="icon-close">Close</span>
                        <h2>Liczba odbiorców: {{ users|length }}</h2>
                    </div>
                    <div class="modal-content">

                    {% if users|length %}

                        <form id="subscription-ajax-form" style="font-weight: normal; margin: 0; color: #000">
                            {% csrf_token %}

                            <table style="margin: 5px; width: 100%; height: 100%">
                                <tr>
                                    <td style="width: 40%"></td>
                                    <td style="width: 60%"><h2>Podgląda maila</h2></td>
                                </tr>
                                <tr>
                                    <td style="padding: 30px;"><textarea style="height: 210px; width: 100%" placeholder="Możesz dodać opcjonalną treść (dozowolony HTML)..." name="potencjalnie_zainteresowani_mail" id="id_potencjalnie_zainteresowani_mail"></textarea></td>
                                    <td><p>{{ mail_content|safe }}</p></td>
                                </tr>
                                <tr>
                                    <td></td>
                                    <td>                            <div class="buttons">
                                <input class="btn btn-red" type="submit" value="Wyślij" id="subscription-ajax-form-submit" />
                                <a href="#" class="btn btn-gray btn-close">Anuluj</a>
                            </div></td>
                                </tr>
                            </table>

                            <script>
                                $(function () {
                                        $('#id_potencjalnie_zainteresowani_mail').on('keyup', function(e) {
                                            $('#live-preview').html($(this).val());
                                        });
                                });
                            </script>



                        </form>
                    {% else %}
                        <div id="no-users">Nie możesz zlecić wysyłki, gdyż nie znaleziono odbiorców.<br><br><br></div>
                                                <div class="buttons">
                                <a href="#" class="btn btn-gray btn-close">Anuluj</a>
                            </div>
{% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
