import datetime
import json
import logging

import dateutil.parser
from apiclient.errors import HttpError
from django.urls import reverse

from i18n.models import WersjaJezykowa

from . import models

logger = logging.getLogger("www")


def delete_event(calendar, termin):
    if not calendar:
        return
    # wstawiamy id bez referencji w bazie nawet jeśli kasujemy wpis w kalendarzu wskutek zmiany sali itp., a nie skasowania terminu
    # no ale to chyba nie szko<PERSON>, a może i tak powinno być, bo update'y takie powinne przetrwać nawet jak potem termin zostanie skasowany
    cu = models.CalendarUpdate(
        calendar=calendar,
        deleted_termin_id=termin.id,
        creation_time=datetime.datetime.now(),
    )
    cu.update_type = "delete"
    cu.save()


def set_event_from_termin(calendar, termin, daty_szczegolowo):
    if not calendar:
        return
    cu = models.CalendarUpdate(
        calendar=calendar,
        termin=termin,
        daty_szczegolowo=daty_szcz<PERSON><PERSON><PERSON>,
        creation_time=datetime.datetime.now(),
    )
    cu.update_type = "update"
    cu.save()


def get_event_id(termin_id):
    return "termin{0}".format(termin_id)


def really_delete_event(calendar_service, calendar, termin_id):
    if not calendar:
        return

    event_id = get_event_id(termin_id)

    try:
        calendar_service.events().delete(
            calendarId=calendar, eventId=event_id
        ).execute()
    except HttpError:
        # Zgłaszać wyjątek? Skoro takiego wydarzenia nie ma to chyba nie
        # ma sensu.
        pass


def parse_date(s):
    return dateutil.parser.parse(s).date()


def really_set_event_from_termin(calendar_service, calendar, termin, daty=""):
    if not calendar:
        return

    origin_event = None

    event_id = get_event_id(termin.id)

    try:
        origin_event = (
            calendar_service.events()
            .get(calendarId=calendar, eventId=event_id)
            .execute()
        )

        # Robimy update
        insert = False
    except HttpError:
        # Robimy insert
        insert = True

    # Zbierz dane terminu do dodania/aktualizacji eventu
    summary = "%s (%d os. - %s)" % (
        termin.szkolenie.kod,
        termin.ilosc_uczestnikow(),
        termin.firma_repr(),
    )

    location = termin.lokalizacja.shortname if termin.lokalizacja else ""

    url = "https://%s%s" % (
        WersjaJezykowa.biezaca().domain,
        reverse("admin:www_terminszkolenia_change", args=[termin.id]),
    )

    description = url

    recurrence = None

    # Dla wstecznej kompatybilności
    if daty:
        daty_szczegolowo = daty
    else:
        daty_szczegolowo = termin.daty_szczegolowo

    # Pozostawiam parsowanie dat ze starego kodu.
    dates = [parse_date(x) for x in daty_szczegolowo.split(",")]
    start_time = dates[0]
    end_time = start_time
    end_time += datetime.timedelta(days=1)
    contig = True
    for d in dates[1:]:
        if d == end_time:
            end_time += datetime.timedelta(days=1)
        else:
            contig = False
            break

    if not contig:
        end_time = start_time + datetime.timedelta(days=1)
        recur_str = ",".join([x.strftime("%Y%m%d") for x in dates[1:]])
        recurrence = [
            "RDATE;VALUE=DATE:{0}".format(recur_str),
        ]

    start_date = start_time.isoformat()
    end_date = end_time.isoformat()

    event = {
        "id": event_id,
        "status": "confirmed",
        "summary": summary,
        "description": description,
        "start": {
            "date": start_date,
        },
        "end": {
            "date": end_date,
        },
        "attendees": [],
    }

    if location:
        event["location"] = location

    if recurrence:
        event["recurrence"] = recurrence

    # Nie przechwytujemy wyjątków .. niech lecą do pętli w
    # `update_termin_calendars.py` - tam są łapane.
    if insert:
        calendar_service.events().insert(calendarId=calendar, body=event).execute()
    else:
        origin_event.update(event)
        try:
            origin_event["sequence"] += 1
        except:
            pass

        calendar_service.events().update(
            calendarId=calendar, eventId=event_id, body=origin_event
        ).execute()


def ciaglosc_sal(termin, sale):
    if sale:
        # Jeśli choć jedna sala nie ma kalendarza to musmy dodać wpis
        # dla lokalizacji.
        if not all([bool(s["calendar"]) for s in sale]):
            return False

        # Jeśli sale mają kalendarze, ale nie pokrywają wszyskich dat
        # terminu, to też musimy dodać wpis dla lokalizacji
        if len(termin.daty_szczegolowo.split(",")) != sum(
            [len(s["dni"].split(",")) for s in sale]
        ):
            return False

        return True
    return False


def calendar_has_changed(old, current_list):
    for pos in current_list:
        if old["id"] == pos["id"]:
            return False
    # Nie ma obiektu w nowej tablicy, więc oznaczamy, że się zmienił kalendarz
    return True


def _update_termin_calendars(termin, delete=False):
    original0 = getattr(termin, "_original_state", None)
    original = {}
    if original0:
        original["odbylo_sie"] = original0["odbylo_sie"]
        original["kalendarze_dane_flat"] = (
            json.loads(original0["kalendarze_dane_flat"])
            if original0["kalendarze_dane_flat"]
            else {}
        )

    kalendarze_dane_flat = termin.get_kalendarze_dane_flat()

    # ponieważ w przypadku kasowanego terminu bierzemy original_state, to jak ktoś zrobi sekwencję: wyciąg z bazy - zmień coś - save() - delete(), to będzie źle
    # ale gdybyśmy brali bieżące wartości, a nie original_state, to z kolei jak ktoś zrobi: wyciąg z bazy - zmień coś - delete(), toby było źle
    # w sumie ta pierwsza opcja jest bardziej prawdopodobna, ale obie są nieprawdopodobne
    # gdyby przy save() uaktualniać original_state, to można by zrobić to dobrze

    if termin.odbylo_sie is False or delete:
        # Odwolujemy lub usuwamy termin. Kasujemy wszystkie wpisy z
        # kalendarzy.

        if original and original["kalendarze_dane_flat"]:
            # Usun wszystkie wpisy z lokalizacji
            for lokalizacja in original["kalendarze_dane_flat"]["lokalizacje"]:
                delete_event(lokalizacja["calendar"], termin)

            # Usun wszystkie wpisy z sal
            for sala in original["kalendarze_dane_flat"]["sale"]:
                delete_event(sala["calendar"], termin)

            # Usun wszystkie wpisy z prowadzących
            for prowadzacy in original["kalendarze_dane_flat"]["prowadzacy"]:
                delete_event(prowadzacy["calendar"], termin)
    elif termin.odbylo_sie is None:
        # Termin jest niewiadomy. Jesli wcześniej był, że robimy, to
        # usuwamy wpisy. Zostawimy tylko wpis dla lokalizacji.

        if original and original["kalendarze_dane_flat"]:
            if original["odbylo_sie"] is True:
                # Usun wszystkie wpisy z sal
                for sala in original["kalendarze_dane_flat"]["sale"]:
                    delete_event(sala["calendar"], termin)

                # Usun wszystkie wpisy z prowadzących
                for prowadzacy in original["kalendarze_dane_flat"]["prowadzacy"]:
                    delete_event(prowadzacy["calendar"], termin)

            # Zostawiamy tylko wpisy dla lokalziacji, więc warto sprawdzić,
            # czy się zmieniły, aby ew. zrobić tylko aktualziację wpisu.
            for lokalizacja in original["kalendarze_dane_flat"]["lokalizacje"]:
                if calendar_has_changed(
                    lokalizacja, kalendarze_dane_flat["lokalizacje"]
                ):
                    delete_event(lokalizacja["calendar"], termin)

        for lokalizacja in kalendarze_dane_flat["lokalizacje"]:
            set_event_from_termin(
                lokalizacja["calendar"], termin, daty_szczegolowo=lokalizacja["dni"]
            )

    elif termin.odbylo_sie is True:
        # Termin jest uruchomiony. Robimy aktualizację wszystkich wpisów.

        if original and original["kalendarze_dane_flat"]:
            # Usun wszystkie wpisy z sal (jeśli się zmieniły)
            for sala in original["kalendarze_dane_flat"]["sale"]:
                if calendar_has_changed(sala, kalendarze_dane_flat["sale"]):
                    delete_event(sala["calendar"], termin)

            # Usun wszystkie wpisy z prowadzących (jeśli się zmieniły)
            for prowadzacy in original["kalendarze_dane_flat"]["prowadzacy"]:
                if calendar_has_changed(prowadzacy, kalendarze_dane_flat["prowadzacy"]):
                    delete_event(prowadzacy["calendar"], termin)

            # Jeśli szkolenie ma ustawione sale (a te sale mają kalendarze),
            # to usuń wszystkie wpisy w lokalizacjach
            if not original["odbylo_sie"] is True and ciaglosc_sal(
                termin, kalendarze_dane_flat["sale"]
            ):
                for lokalizacja in original["kalendarze_dane_flat"]["lokalizacje"]:
                    delete_event(lokalizacja["calendar"], termin)
            else:
                # Usun wszystkie wpisy z lokalizacji (jeśli się zmieniły)
                for lokalizacja in original["kalendarze_dane_flat"]["lokalizacje"]:
                    if calendar_has_changed(
                        lokalizacja, kalendarze_dane_flat["lokalizacje"]
                    ):
                        delete_event(lokalizacja["calendar"], termin)

        # Dodaj wpisy w salach
        for sala in kalendarze_dane_flat["sale"]:
            set_event_from_termin(
                sala["calendar"], termin, daty_szczegolowo=sala["dni"]
            )

        # Dodaj wpisy w prowadzacych
        for prowadzacy in kalendarze_dane_flat["prowadzacy"]:
            set_event_from_termin(
                prowadzacy["calendar"], termin, daty_szczegolowo=prowadzacy["dni"]
            )

        # Jeśli nie ma ustawionych sal, lub jakaś sala jest bez kalendarza
        # to ustaw wpis w kalendarzu lokalizacji.
        if not ciaglosc_sal(termin, kalendarze_dane_flat["sale"]):
            for lokalizacja in kalendarze_dane_flat["lokalizacje"]:
                set_event_from_termin(
                    lokalizacja["calendar"], termin, daty_szczegolowo=lokalizacja["dni"]
                )


def update_termin_calendars(termin, delete=False):
    if termin.szkolenie.tag_dlugosc.slug == "kurs-zawodowy":
        return

    try:
        _update_termin_calendars(termin, delete=delete)
    except:
        logger.exception("Problem przy eksporcie do kolejki CalendarUpdate")
