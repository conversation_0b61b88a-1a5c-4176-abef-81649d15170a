from haystack import indexes

from .models import <PERSON>Flat<PERSON>age, Szkolenie, TagTechnologia


class SzkolenieIndex(indexes.SearchIndex, indexes.Indexable):
    text = indexes.EdgeNgramField(document=True, use_template=True)
    language = indexes.CharField(model_attr="language")
    aktywne = indexes.BooleanField(model_attr="aktywne")
    rendered = indexes.CharField(use_template=True, indexed=False)

    def get_model(self):
        return Szkolenie

    def index_queryset(self, using=None):
        return self.get_model().objects.filter(aktywne=True).select_related()

    #
    # def prepare(self, obj):
    #     data = super(SzkolenieIndex, self).prepare(obj)
    #     data['boost'] = 1.1
    #     return data


class MyFlatPageIndex(indexes.SearchIndex, indexes.Indexable):
    text = indexes.EdgeNgramField(document=True, use_template=True)
    language = indexes.Char<PERSON>ield(model_attr="language")
    enabled = indexes.BooleanField(model_attr="enabled")
    rendered = indexes.CharField(use_template=True, indexed=False)

    def get_model(self):
        return MyFlatPage

    def index_queryset(self, using=None):
        return (
            self.get_model()
            .objects.filter(enabled=True, szkolenie__isnull=True, is_searchable=True)
            .select_related()
        )

    def prepare(self, obj):
        data = super().prepare(obj)
        data["boost"] = 0.5
        return data


class TagTechnologiaIndex(indexes.SearchIndex, indexes.Indexable):
    text = indexes.EdgeNgramField(document=True, use_template=True)
    language = indexes.CharField(model_attr="language")
    widoczny_publicznie = indexes.BooleanField(model_attr="widoczny_publicznie")
    rendered = indexes.CharField(use_template=True, indexed=False)

    def get_model(self):
        return TagTechnologia

    def index_queryset(self, using=None):
        return (
            self.get_model().objects.filter(widoczny_publicznie=True).select_related()
        )

    # def prepare(self, obj):
    #     data = super(SzkolenieIndex, self).prepare(obj)
    #     data['boost'] = 1.1
    #     return data
