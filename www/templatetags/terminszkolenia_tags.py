# vim: set fileencoding=utf-8 expandtab shiftwidth=4 tabstop=4 softtabstop=4:
from django.contrib.admin.templatetags.admin_list import (
    items_for_result,
    result_headers,
)
from django.template import Library

register = Library()


def results(cl):
    if cl.formset:
        for res, form in zip(cl.result_list, cl.formset.forms):
            yield (res, list(items_for_result(cl, res, form)))
    else:
        for res in cl.result_list:
            yield (res, list(items_for_result(cl, res, None)))


def terminszkolenia_result_list(cl):
    return {
        "cl": cl,
        "result_headers": list(result_headers(cl)),
        "results": list(results(cl)),
    }


terminszkolenia_result_list = register.inclusion_tag(
    "admin/www/terminszkolenia/change_list_results.html"
)(terminszkolenia_result_list)
