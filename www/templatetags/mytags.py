import datetime
import os
import re
from collections import OrderedDict
from decimal import ROUND_HALF_UP, Decimal

from django import template
from django.conf import settings
from django.contrib.contenttypes.models import ContentType
from django.contrib.staticfiles import finders
from django.db.models import Case, Count, Q, Sum, When
from django.template import Variable
from django.template.loader import render_to_string
from django.urls import reverse
from django.utils.html import strip_tags
from django.utils.safestring import mark_safe

from ankiety.models import Podpytanie, <PERSON><PERSON>bor
from i18n.utils import pretty_number

from ..forms import CaptchaContactForm
from ..models import MyFlatPage, Prowadzacy, Szkolenie, TagTechnologia, Uczestnik
from ..utils import language_icon
from .myfilters import get_lang_for_slug, myincludes, staticpage_lista_trenerow

register = template.Library()


@register.tag(name="staticbuster")
def do_staticbuster(parser, token):
    return StaticBusterTag(token)


class StaticBusterTag(template.Node):
    def __init__(self, token):
        try:
            tokens = token.split_contents()
        except ValueError:
            raise template.TemplateSyntaxError(
                "'%r' tag must have one argument" % token.contents.split()[0]
            )

        self.path = tokens[1]

    def render(self, context):
        return (
            settings.STATIC_URL
            + self.path
            + "?"
            + self.get_file_modified(finders.find(self.path))
        )

    def get_file_modified(self, path):
        return datetime.datetime.fromtimestamp(
            os.path.getmtime(os.path.abspath(path))
        ).strftime("%S%M%H%d%m%y")


def do_get_myflatpage(parser, token):
    try:
        split = token.split_contents()
    except ValueError:
        raise template.TemplateSyntaxError("do_get_myflatpage")
    return FPNode(split[1], split[2], split[3])


do_get_myflatpage = register.tag("get_myflatpage", do_get_myflatpage)


class FPNode(template.Node):
    def __init__(self, language, fp_slug, variable):
        self.language = language
        self.fp_slug = fp_slug
        self.variable = variable

    def render(self, context):
        fp = MyFlatPage.objects.get(
            slug=self.fp_slug, language=Variable(self.language).resolve(context)
        )
        context[self.variable] = fp
        return ""


@register.simple_tag
def flatpage(language, slug):
    fp = MyFlatPage.objects.filter(slug=slug, language=language).first()
    if fp is None:
        return ""
    return mark_safe(myincludes(fp.lead + fp.content))


def do_get_n_related(parser, token):
    try:
        split = token.split_contents()
    except ValueError:
        raise template.TemplateSyntaxError("do_get_n_related_articles")
    return RelatedNode(n=split[1], szkolenie=split[2], variable=split[3])


do_get_n_related = register.tag("get_n_related", do_get_n_related)


def do_get_related(parser, token):
    try:
        split = token.split_contents()
    except ValueError:
        raise template.TemplateSyntaxError("do_get_related_articles")
    return RelatedNode(szkolenie=split[1], variable=split[2])


do_get_related = register.tag("get_related", do_get_related)


class RelatedNode(template.Node):
    def __init__(self, n=None, szkolenie=None, variable=None):
        self.n = n
        self.szkolenie = szkolenie
        self.variable = variable

    def render(self, context):
        szkolenie = Variable(self.szkolenie).resolve(context)
        tags = [x["id"] for x in szkolenie.tagi_technologia.all().values("id")]

        related = (
            Szkolenie.objects.filter(tagi_technologia__in=tags, aktywne=True)
            .select_related()
            .exclude(id=szkolenie.id)
            .distinct()
        )

        if self.n:
            related = related[: self.n]
        context[self.variable] = related
        return ""


@register.simple_tag
def url_for_tech(technologia_slug, language):
    try:
        return TagTechnologia.objects.get(
            slug=technologia_slug, language=language
        ).get_absolute_url()
    except:
        if language == settings.DEFAULT_LANGUAGE:
            return reverse("index_by_technologia_pl", args=[technologia_slug])
        else:
            return reverse(
                "index_by_technologia",
                kwargs={"technologia_slug": technologia_slug, "language": language},
            )


@register.filter
def vat(decimal):
    return str(pretty_number(decimal * 100)) + "%"


@register.tag("get_wykladowcy")
def do_get_wykladowcy(parser, token):
    try:
        split = token.split_contents()
    except ValueError:
        raise template.TemplateSyntaxError("do_get_wykladowcy")
    return WykladowcyNode(variable=split[1], n=split[2])


class WykladowcyNode(template.Node):
    def __init__(self, variable, n):
        self.n = int(n)
        self.variable = variable

    def render(self, context):
        wykladowcy = Prowadzacy.objects.filter(
            pokazywac=True, nie_pokazuj_w_kolumnie=False
        ).order_by("?")[: self.n]
        context[self.variable] = wykladowcy
        return ""


@register.simple_tag(takes_context=True)
def tab_slug(context, value, skip_prefix=False):
    """
    FIXME 2013-10-07: Powinniśmy automatycznie generować tabom slug na podstawie title
    a zmienna slug zmienić na ID i używać do adresowania tabów (różne wersje językowe)
    To wymaga dość sporego przejrzenia obecnej mechaniki, więc szczególnie się
    niestety nie opłaca, bo nie daje specjalnie nic w zamian (poza ładniejszym kodem)
    """
    try:
        slug = next(item for item in context["taby"] if item["id"] == value)["slug"]
        if not skip_prefix:
            slug = "tab-" + slug
        return slug
    except StopIteration:
        return StopIteration('nie ma pozycji "{0}" w podanym słowniku'.format(value))


@register.simple_tag(takes_context=True, name="language_icon")
def language_icon_tag(context, object_or_language):
    return language_icon(object_or_language, context["language"])


@register.simple_tag(takes_context=False)
def notification_reports_tag(row, locations, trainings):
    response_locations = []
    for l in locations:
        response_locations.append({"location": l, "users_count": row.get(str(l.pk), 0)})

    return {
        "training": trainings[row["training"]],
        "locations": response_locations,
    }


@register.simple_tag(takes_context=False)
def terms_by_location(terms):
    """
    Tag grupuje terminy wedlug lokalizacji.
    """

    data = OrderedDict()

    try:
        terms = terms.order_by("lokalizacja__numer", "termin")
    except:
        pass

    for t in terms:
        if t.lokalizacja.pk in data:
            data[t.lokalizacja.pk]["terms"].append(t)
        else:
            data[t.lokalizacja.pk] = {
                "location": t.lokalizacja,
                "terms": [t],
            }
    return list(data.values())


@register.simple_tag(takes_context=False)
def training_has_terms(miasta):
    """
    Sprawdzamy, czy szkolenie ma "realne" terminy, na które można się zapisać.
    """

    if miasta:
        return any(m["terminy"] for m in miasta if m["terminy"])
    return False


def _uwagi_i_wolne_miejsca(termin, kurs_gwarantowany=False):
    return {
        "uwagi": termin.tekst_uwagi(),
        "wolne_miejsca": termin.tekst_wolne_miejsca(kurs_gwarantowany=kurs_gwarantowany),
    }


@register.simple_tag(takes_context=False)
def uwagi_i_wolne_miejsca(termin):
    """
    Zwracamy informację o długości kursu i ew. informację o wolnych miejscach.
    """

    return _uwagi_i_wolne_miejsca(termin, kurs_gwarantowany=True)


@register.simple_tag(takes_context=False)
def czy_zastosowac_uproszczony_widok_kursow(miasta):
    """
    Zwracamy informację o tym, czy zastosować uproszczony widok terminów
    Kursu. Dzieje się tak gdy:
     - jakieś miasto nie ma żadnego terminu (tylko Tekst o terminach)
     - jest mniej jak 5 terminów
     - wszystkie terminy są jednego typu (np. tylko Zaoczne)
     - różnica pomiędzy ilością terminów różnego trybu w danym mieście jest
     większa/równa N
    """

    N = 4

    # jakieś miasto nie ma żadnego terminu (tylko Tekst o terminach)
    terms = []
    for m in miasta:
        if m["terminy"]:
            terms += list(m["terminy"])
        else:
            return True

    # jest mniej jak 5 terminów
    if len(terms) < 5:
        return True

    # wszystkie terminy są jednego typu (np. tylko Zaoczne)
    map_tryb = {"1": 1, "2": 1, "3": 3}
    if len(list(set([map_tryb[str(t.tryb)] for t in terms]))) == 1:
        return True

    # przrywamy sprawdzanie, gdy jest więcej jak 12 terminów
    if len(terms) >= 12:
        return False

    # jest przynajmniej jednej termin wieczorowy
    # if any([t.tryb for t in terms if t.tryb == 2]):
    #     return True

    # różnica pomiędzy ilością terminów różnego trybu w danym mieście jest
    # większa/równa N
    for m in miasta:
        by_tryb = {"1": 0, "2": 0, "3": 0}

        for t in m["terminy"]:
            by_tryb[str(t.tryb)] += 1

        delta = (by_tryb["1"] + by_tryb["2"]) - by_tryb["3"]

        if abs(delta) >= N:
            return True

    # - jest min. 1 miasto, które ma tylko jeden typ terminów (np. tylko
    # zaoczne)
    # for m in miasta:
    #     if len(list(set([t.tryb for t in m['terminy']]))) == 1:
    #         return True
    return False


@register.simple_tag(takes_context=False)
def tabela_kursow(szkolenie, miasta):
    """
    Zwracamy tablice zawierającą opisy najczęściej pojawiających się trybów
    oraz listę terminów posortowaną per miasto i tryb.
    """

    result = {
        "header": {
            "D": szkolenie.get_tryb_opis(1),
            "Z": szkolenie.get_tryb_opis(3),
        },
        "data": [],
    }

    # Pobieramy wszystkie opisy dotyczace trybu, z ktorych najczesciej
    # wystepujacy dodamy do naglowka.
    # header = {
    #     '1': [],
    #     '3': [],
    # }
    # for m in miasta:
    #     for t in m['terminy']:
    #         header[str(t.tryb)].append(_uwagi_i_wolne_miejsca(t)['uwagi'])
    #
    # if header['1']:
    #     result['header']['D'] = max(set(header['1']), key=header['1'].count)
    # if header['3']:
    #     result['header']['Z'] = max(set(header['3']), key=header['3'].count)

    # Budujemy liste terminów per miasta z podziałem na tryb
    for m in miasta:
        D = [t for t in m["terminy"] if t.tryb in (1, 2)]
        Z = [t for t in m["terminy"] if t.tryb == 3]

        max_result_count = max([len(D), len(Z)])

        terms = []
        for i in range(max_result_count):
            terms.append(
                {
                    "D": D[i] if i < len(D) else None,
                    "Z": Z[i] if i < len(Z) else None,
                }
            )

        result["data"].append(
            {
                "location": m["miasto"],
                "terms": terms,
            }
        )
    return result


@register.simple_tag(takes_context=False)
def tabela_kursow_ilosc_terminow(tabela_kursow_dane):
    """
    Zwracamy liczbe wierszy z terminami w header.
    """

    counter = 0

    if not tabela_kursow_dane:
        return counter

    for row in tabela_kursow_dane:
        counter += len(row["terms"])
    return counter


@register.simple_tag(takes_context=False)
def czy_zastosowac_uproszczony_widok_kursow_tt(miasto):
    """
    Zwracamy informację o tym, czy zastosować uproszczony widok terminów
    Kursu (nowa wersja z tagu). Dzieje się tak gdy:
     - miasto nie ma żadnego terminu (tylko Tekst o terminach)
    """

    if not miasto["terminy"]:
        return True
    return False


@register.simple_tag(takes_context=False)
def tabela_kursow_tt(szkolenie, miasto):
    """
    Zwracamy tablice zawierającą opisy najczęściej pojawiających się trybów
    oraz listę terminów posortowaną per tryb.
    """

    return {
        "header": {
            "D": szkolenie.get_tryb_opis(1),
            "Z": szkolenie.get_tryb_opis(3),
        },
        "data": {
            "D": [t for t in miasto["terminy"] if t.tryb in (1, 2)],
            "Z": [t for t in miasto["terminy"] if t.tryb == 3],
        },
    }


@register.simple_tag(takes_context=False)
def ocena_szkolenia(szkolenie, always_default=False):
    """
    Funkcja zwraca ocenę szkolenia/kursu na podstawie ankiet.
    """

    N = 20
    min_avg = Decimal(4.0)

    base_translation = szkolenie.base_translation
    uczestnik_qs = Uczestnik.objects.filter(termin__odbylo_sie=True, status=3)

    if szkolenie.language == "en" and base_translation:
        # Dla szkolen EN bierzemy pod uwage szkolenia bazowe (czyli
        # odpowiedniki w jezyku polskim).
        uczestnik_qs = uczestnik_qs.filter(
            Q(termin__szkolenie=szkolenie) | Q(termin__szkolenie=base_translation)
        )
    else:
        uczestnik_qs = uczestnik_qs.filter(
            termin__szkolenie=szkolenie,
        )

    uczestnik_qs = uczestnik_qs.aggregate(
        indywidualny=Count(
            Case(
                When(
                    uczestnik_wieloosobowy_ilosc_osob__isnull=True,
                    then=1,
                )
            )
        ),
        wieloosobowy=Sum(
            Case(
                When(
                    uczestnik_wieloosobowy_ilosc_osob__gt=0,
                    then="uczestnik_wieloosobowy_ilosc_osob",
                )
            )
        ),
    )

    liczba_uczestnikow = uczestnik_qs.get("indywidualny") or 0
    liczba_uczestnikow += uczestnik_qs.get("wieloosobowy") or 0
    liczba_uczestnikow += szkolenie.archiwalna_liczba_przeszkolonych_lub_zero()

    if szkolenie.language == "en" and base_translation:
        # Dla Szkolenia EN dodajemy archiwalna liczbe uczestnikow ze Szkolenia
        #  PL
        liczba_uczestnikow += (
            base_translation.archiwalna_liczba_przeszkolonych_lub_zero()
        )

    if liczba_uczestnikow >= N:
        ratings = Wybor.objects.filter(
            pytanie_id__in=Podpytanie.objects.filter(
                pytanie__dotyczy_prowadzacych=True
            ).values_list("id", flat=True),
            rodzaj_pytania=ContentType.objects.get_by_natural_key(
                "ankiety", "podpytanie"
            ),
        )

        if szkolenie.language == "en" and base_translation:
            # Dla szkolen EN bierzemy pod uwage szkolenia bazowe (czyli
            # odpowiedniki w jezyku polskim).
            ratings = ratings.filter(
                Q(ankieta__termin_szkolenia__szkolenie=szkolenie)
                | Q(ankieta__termin_szkolenia__szkolenie=base_translation)
            )
        else:
            ratings = ratings.filter(ankieta__termin_szkolenia__szkolenie=szkolenie)

        ratings = ratings.select_related("odpowiedz").values_list(
            "odpowiedz__nazwa", flat=True
        )

        # Pozbywamy sie wszystkich nie-liczbowych odpowiedzi i castujemy na INT
        # (nie mozemy uzyc `map`).

        ratings_cleaned = []

        for rating in ratings:
            try:
                rating = int(rating)
                assert rating in range(1, 6)
            except:
                continue
            else:
                ratings_cleaned.append(rating)

        if ratings_cleaned:
            # Liczymy średnią
            avg = Decimal(sum(ratings_cleaned, 0.0) / len(ratings_cleaned)).quantize(
                Decimal(".1"), rounding=ROUND_HALF_UP
            )
            if avg >= min_avg:
                return {
                    "avg": avg,
                    "count": liczba_uczestnikow,
                    "css": str(avg).replace(".", "-").replace(",", "-"),
                }

    # W przypadku osadzania oceny w tekście poprzez tag [[ocena]] zawsze
    # zwracamy jakąś wartość. Jesli warunki nie są osiągnięte zwracamyz
    # zawsze ocenę 4.5
    if always_default:
        return {"avg": Decimal(4.5), "count": liczba_uczestnikow, "css": "4-5"}
    return None


@register.simple_tag(takes_context=False)
def ocena_tech_taga(techtag):
    """
    Funkcja zwraca ocenę techtaga na podstawie ankiet.
    """

    N = 50
    min_avg = Decimal(4.0)

    base_translation = techtag.base_translation
    if techtag.language == "en" and base_translation:
        # Dla taga EN bierzemy pod uwage szkolenia należące do taga bazowego.
        szkolenia = Szkolenie.objects.filter(
            tagi_technologia__in=[techtag, base_translation]
        )
    else:
        szkolenia = Szkolenie.objects.filter(tagi_technologia__in=[techtag])

    szkolenia = szkolenia.values_list("pk", flat=True)

    uczestnik_qs = Uczestnik.objects.filter(
        termin__szkolenie__pk__in=szkolenia, termin__odbylo_sie=True, status=3
    ).aggregate(
        indywidualny=Count(
            Case(
                When(
                    uczestnik_wieloosobowy_ilosc_osob__isnull=True,
                    then=1,
                )
            )
        ),
        wieloosobowy=Sum(
            Case(
                When(
                    uczestnik_wieloosobowy_ilosc_osob__gt=0,
                    then="uczestnik_wieloosobowy_ilosc_osob",
                )
            )
        ),
    )

    liczba_uczestnikow = uczestnik_qs.get("indywidualny") or 0
    liczba_uczestnikow += uczestnik_qs.get("wieloosobowy") or 0
    liczba_uczestnikow += techtag.archiwalna_liczba_przeszkolonych_lub_zero()

    if techtag.language == "en" and base_translation:
        # Dla taga EN dodajemy archiwalna liczbe uczestnikow z taga PL
        liczba_uczestnikow += (
            base_translation.archiwalna_liczba_przeszkolonych_lub_zero()
        )

    result = {"avg": None, "count": None, "css": None}

    if liczba_uczestnikow >= N:
        result["count"] = liczba_uczestnikow

        ratings = (
            Wybor.objects.filter(
                ankieta__termin_szkolenia__szkolenie__pk__in=szkolenia,
                pytanie_id__in=Podpytanie.objects.filter(
                    pytanie__dotyczy_prowadzacych=True
                ).values_list("id", flat=True),
                rodzaj_pytania=ContentType.objects.get_by_natural_key(
                    "ankiety", "podpytanie"
                ),
            )
            .select_related("odpowiedz")
            .values_list("odpowiedz__nazwa", flat=True)
        )

        # Pozbywamy sie wszystkich nie-liczbowych odpowiedzi i castujemy na INT
        # (nie mozemy uzyc `map`).
        ratings_cleaned = []

        for rating in ratings:
            try:
                rating = int(rating)
                assert rating in range(1, 6)
            except:
                continue
            else:
                ratings_cleaned.append(rating)

        if ratings_cleaned:
            # Liczymy średnią
            avg = Decimal(sum(ratings_cleaned, 0.0) / len(ratings_cleaned)).quantize(
                Decimal(".1"), rounding=ROUND_HALF_UP
            )
            if avg >= min_avg:
                result["avg"] = avg
                result["css"] = str(avg).replace(".", "-").replace(",", "-")
    return result


@register.simple_tag(takes_context=False)
def index_by_technologia_top(
    techtag, top_highlight, stars, tech_trenerzy, pl=None, en=None
):
    """
    Top tech-tagu może być wyświetlany w kilku wariantach.
    """

    data = {
        "tech": techtag,
        "stars": stars,
        "tech_trenerzy": tech_trenerzy,
        "top_highlight": top_highlight,
        "pl": pl,
        "en": en,
    }

    # Wariant D - brak highlight
    if not top_highlight:
        return render_to_string("www/index_by_technologia/top_D.html", data)

    N = 700
    text = top_highlight.highlight.tresc.lower()
    img = top_highlight.highlight.obrazek
    stripped_text = strip_tags(text)

    # Wariant A.
    # przypadek "maksymalny" - gdy:
    #   - albo tekst jest duży na co najmniej X,
    #   - albo do tekstu istnieje gfx "uwiarygodnienie"
    if len(stripped_text) > N or "<img" in text:
        return render_to_string("www/index_by_technologia/top_A.html", data)

    # Wariant C
    # przypadek "minimalny" - nie ma ani trenera ani zdjęcia
    if not tech_trenerzy and not img:
        return render_to_string("www/index_by_technologia/top_C.html", data)

    # Wariant B1/B2.
    # przypadek "średni" - gdy:
    #   - mamy trenera lub zdjęcie, ale (tekst jest niedługi oraz nie ma
    #     uwiarygadniaczy)
    if tech_trenerzy:
        return render_to_string("www/index_by_technologia/top_B1.html", data)
    else:
        return render_to_string("www/index_by_technologia/top_B2.html", data)


@register.simple_tag(takes_context=False)
def get_captcha_contact_form():
    return CaptchaContactForm()


@register.simple_tag(takes_context=False)
def css_included(content):
    """
    Zwracamy informację o długości kursu i ew. informację o wolnych miejscach.
    """

    groups = (
        re.findall(r"\[\[include:([^\]]*)\]\]", content, flags=re.I)
        if content
        else None
    )

    if not groups:
        return ""

    css_content = ""

    for group in groups:
        try:
            language, slug = get_lang_for_slug(group)
            assert language and slug
            fp = MyFlatPage.objects.get(slug=slug, language=language)
        except Exception:
            continue
        else:
            css_content += fp.css_content if fp.css_content else ""

    return mark_safe(css_content)


@register.simple_tag(takes_context=False)
def trainers_included(content, language):
    """
    Zwracamy informację o trenerach dodanych do treści strony.
    """

    mo = (
        re.findall(
            r"\[\[(staticpage_lista_trenerow_pl|staticpage_lista_trenerow_en):([^\]]*)\]\]",
            content,
            flags=re.I,
        )
        if content
        else None
    )

    if not mo:
        return ""

    try:
        ids = mo[0][1]
    except:
        return ""

    return staticpage_lista_trenerow(
        ids, language, "www/partial/staticpage_lista_trenerow_mobile.html"
    )
