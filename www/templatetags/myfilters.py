import re
import sys
from datetime import date
from decimal import Decimal

import textile
from django import template
from django.template import Node
from django.conf import settings
from django.contrib.admin.templatetags.admin_list import _boolean_icon
from django.db.models import Prefetch
from django.template.defaultfilters import date as _date, floatformat, stringfilter
from django.template.loader import render_to_string
from django.utils import translation
from django.utils.encoding import force_text, smart_str
from django.utils.formats import number_format
from django.utils.safestring import mark_safe
from django.utils.translation import pgettext_lazy, ugettext_lazy as _

from i18n.models import WersjaJezykowa
from i18n.utils import pretty_number

from ..models import (
    Lokalizacja,
    MyFlatPage,
    Prowadzacy,
    Szkolenie,
    SzkolenieWariant,
    TagTechnologia,
    TekstOTerminach,
    TerminSzkolenia,
)
from ..utils import language_icon

register = template.Library()


def get_lang_for_slug(full_slug):
    m = re.match(r"(..)/(.+)", full_slug)
    if m:
        return m.group(1), m.group(2)
    else:
        return settings.DEFAULT_LANGUAGE, full_slug


def myincluder(mo):
    try:
        language, slug = get_lang_for_slug(mo.group(2))
        fp = MyFlatPage.objects.get(slug=slug, language=language)
    except MyFlatPage.DoesNotExist:
        return "<h3>No such MyFlatPage to include: " + mo.group(2) + ".</h3>"
    return fp.lead + fp.content




def najblizszy_terminator(mo):
    try:
        language, slug = get_lang_for_slug(mo.group(2))
        szkolenie = Szkolenie.objects.prefetch_related(
            Prefetch(
                "warianty_szkolenia",
                queryset=SzkolenieWariant.objects.select_related("wariant"),
            )
        ).get(slug=slug, language=language)
    except Szkolenie.DoesNotExist:
        return "invalid szkolenie in najblizsze_terminy"

    szkolenia = [szkolenie] + [e.wariant for e in szkolenie.warianty_szkolenia.all()]
    szkolenia_cnt = len(szkolenia)
    miasta = Lokalizacja.objects.filter(reklamowana=True)

    result = ""

    for szkolenie in szkolenia:
        s = ""
        for miasto in miasta:
            s1 = (
                '<tr valign="top"><td style="padding-right: 5px;"><b>'
                + miasto.fullname
                + ":</b></td><td>"
            )
            terminy = szkolenie.terminy().filter(lokalizacja=miasto)
            tot = TekstOTerminach.objects.filter(
                lokalizacja=miasto, szkolenie=szkolenie
            )
            if terminy:
                s1 += (
                    "<br/>".join(
                        [
                            "- %s - %s%s %s"
                            % (
                                t.termin,
                                t.get_tryb_display(),
                                t.uwagi_i_wolne_miejsca(),
                                (
                                    ""
                                    if language == "pl" and t.language == "pl"
                                    else language_icon(t, language)
                                ),
                            )
                            for t in terminy
                        ]
                    )
                    + "</td></tr>"
                )
            elif tot:
                s1 += "- " + tot[0].tekst + "</td></tr>"
            else:
                s1 = ""
            s += s1
        if szkolenia_cnt > 1:
            result += (
                (
                    '<h3 style="margin :10px">{} / {}</h3>'.format(
                        szkolenie.nazwa, szkolenie.info_dla_wariantu()
                    )
                    + "<table>"
                    + s
                    + "</table>"
                )
                if s != ""
                else ""
            )
        else:
            result += ("<table>" + s + "</table>") if s != "" else ""
    return result


def najblizszy_terminator_en(mo):
    try:
        language, slug = get_lang_for_slug(mo.group(2))
        szkolenie = Szkolenie.objects.get(slug=slug, language=language)
    except Szkolenie.DoesNotExist:
        return "invalid szkolenie in najblizsze_terminy"
    s = ""
    for miasto in Lokalizacja.objects.filter(reklamowana=True):
        s1 = (
            '<tr valign="top"><td style="padding-right: 5px;"><b>'
            + miasto.fullname
            + ":</b></td><td>"
        )
        terminy = szkolenie.terminy().filter(lokalizacja=miasto)
        if terminy:
            s1 += (
                "<br/>".join(
                    ["%s - %s" % (t.termin, t.get_tryb_display_en()) for t in terminy]
                )
                + "</td></tr>"
            )
        else:
            s1 = ""
        s += s1
    return ("<table>" + s + "</table>") if s != "" else ""


@register.filter
def terminy_ciagle(terminy):
    """
    Zwrócić True, jeśli wszystkie terminy są ciągłe.
    Uwaga: przy braku terminów zwraca True.
    """
    return all(termin.ciagly() for termin in terminy)


@register.filter
def termin_short(termin):
    return termin.strftime("%d.%m")


@register.filter
def termin_full(t, warianty=False):
    start = t.termin
    stop = t.termin_do()

    if not stop or warianty:
        return _date(start, "j E Y")

    if start.year > date.today().year:
        return "{0} - {1}".format(_date(start, "j E Y"), _date(stop, "j E Y"))

    # https://app.asana.com/0/4723313594922/1183411881421105
    # if start.year != stop.year:
    #     return "{0} - {1}".format(_date(start, 'j E Y'), _date(stop, 'j E Y'))
    return "{0} - {1}".format(_date(start, "j E"), _date(stop, "j E Y"))


@register.filter
def termin_full_mobile(t, warianty=False):
    start = t.termin
    stop = t.termin_do()

    if not stop or warianty:
        return _date(start, "d.m.Y")

    if start.year > date.today().year:
        return "{0} - {1}".format(_date(start, "d.m.Y"), _date(stop, "d.m.Y"))

    # https://app.asana.com/0/4723313594922/1183411881421105
    # if start.year != stop.year:
    #     return "{0} - {1}".format(_date(start, 'd.m.Y'), _date(stop, 'd.m.Y'))
    return "{0} - {1}".format(_date(start, "d.m"), _date(stop, "d.m.Y"))


@register.filter
def liczba_terminow_kursu(miasta):
    """
    Zwracamy liczbe terminow lub informacji o terminie dla danego kursu.
    """

    count = 0

    if miasta:
        for m in miasta:
            if m["terminy"]:
                count += len(m["terminy"])
            elif m["tot"]:
                count += 1
    return count


@register.filter
def harmonogram(szkolenie):
    szkolenia = [szkolenie] + [
        e.wariant for e in szkolenie.warianty_szkolenia.select_related("wariant")
    ]
    szkolenia_cnt = len(szkolenia)

    # Pseudo-gettext
    DATEFORMAT = "%d.%m.%Y"
    result = ""

    for szkolenie in szkolenia:
        s = ""
        for tryb, opis in (
            (1, _("Grupy dzienne")),
            (3, _("Grupy zaoczne")),
            (2, _("Grupy wieczorowe")),
        ):
            terminy = szkolenie.terminy_trwajace().filter(tryb=tryb).select_related()
            if terminy:
                s += "<h3>%s</h3>" % opis
                s += "<ul>"
                for t in terminy:
                    harmonogram = t.harmonogram
                    if re.search("\S", t.harmonogram) is None:
                        opisy_zjazdow = []
                        for od, do in t.zjazdy():
                            if (
                                od.day == do.day
                                and od.month == do.month
                                and od.year == do.year
                            ):
                                opisy_zjazdow.append(
                                    "%02d.%02d.%02d" % (do.day, do.month, do.year)
                                )
                            elif od.month == do.month and od.year == do.year:
                                opisy_zjazdow.append(
                                    "%02d-%02d.%02d.%02d"
                                    % (od.day, do.day, do.month, do.year)
                                )
                            else:
                                opisy_zjazdow.append(
                                    "%02d.%02d.%02d-%02d.%02d.%02d"
                                    % (
                                        od.day,
                                        od.month,
                                        od.year,
                                        do.day,
                                        do.month,
                                        do.year,
                                    )
                                )
                        harmonogram = _("bloki") + ": " + ", ".join(opisy_zjazdow)
                    s += "<li>"
                    s += _("od") + " <strong>%s</strong> (%s)" % (
                        t.termin.strftime(DATEFORMAT),
                        t.lokalizacja.shortname,
                    )

                    current_language = translation.get_language()
                    if current_language != t.szkolenie.language:
                        s += " " + language_icon(t.szkolenie, current_language)
                    s += "<ul><li>%s</li><li>%s: %s</li></ul>" % (
                        harmonogram,
                        _("zakończenie"),
                        t.termin_zakonczenia.strftime(DATEFORMAT)
                        if t.termin_zakonczenia
                        else "",
                    )
                    s += "</li>"
                s += "</ul>"
        if szkolenia_cnt > 1 and s:
            result += (
                '<h3 style="margin:0;padding:0">{} / {}</h3>'.format(
                    szkolenie.nazwa, szkolenie.info_dla_wariantu()
                )
                + s
            )
        else:
            result += s

    return mark_safe(result)


def harmonogramer(mo):
    try:
        language, slug = get_lang_for_slug(mo.group(2))
        szkolenie = Szkolenie.objects.get(slug=slug, language=language)
    except Szkolenie.DoesNotExist:
        return "invalid szkolenie in harmonogram"
    return harmonogram(szkolenie)


def zobacz_tez_szkolenia(mo):
    try:
        language, slug = get_lang_for_slug(mo.group(2))
        szkolenie = Szkolenie.objects.get(slug=slug, language=language)
    except Szkolenie.DoesNotExist:
        return "invalid szkolenie in zobacz_tez_szkolenia"
    tags = [x["id"] for x in szkolenie.tagi_technologia.all().values("id")]
    related = (
        Szkolenie.objects.filter(
            tagi_technologia__in=tags, aktywne=True, tag_dlugosc__slug="szkolenie"
        )
        .exclude(id=szkolenie.id)
        .distinct()
    )
    t = template.Template(
        """
<ul>
{% for s in related %}
<li><a href="{{ s.get_absolute_url }}">{{ s.nazwa }}</a></li>{% endfor %}
</ul>
"""
    )
    return t.render(template.Context({"related": related}))


def sciezki_linki(mo):
    try:
        language, slug = get_lang_for_slug(mo.group(2))
        szkolenie = Szkolenie.objects.get(slug=slug, language=language)
    except Szkolenie.DoesNotExist:
        return "invalid szkolenie in sciezki_linki"
    return render_to_string(
        "www/sciezki_linki.html", {"sciezki": szkolenie.sciezki.all()}
    )


def sciezki_tresci(mo):
    try:
        language, slug = get_lang_for_slug(mo.group(2))
        szkolenie = Szkolenie.objects.get(slug=slug, language=language)
    except Szkolenie.DoesNotExist:
        return "invalid szkolenie in sciezki_tresci"
    return render_to_string(
        "www/sciezki_tresci.html", {"sciezki": szkolenie.sciezki.all()}
    )


def ocena_szkolenia_tag(mo):
    from www.templatetags.mytags import ocena_szkolenia

    try:
        language, slug = get_lang_for_slug(mo.group(2))
        szkolenie = Szkolenie.objects.get(slug=slug, language=language)
    except Szkolenie.DoesNotExist:
        return "invalid szkolenie in ocena"
    result = ocena_szkolenia(szkolenie, always_default=True)
    return """
        <div class="rate-include">
            {avg}<span>/5</span>
        </div>
        <div class="stars-cointaner-include">
            <div class="stars-include stars-{css}">Stars</div>
        </div>

        <div class="students-wrap">
            <div class="students">
                {count}
            </div>
            {message}
        </div>
    """.format(
        avg=str(result["avg"]),
        css=result["css"],
        count=result["count"],
        message=_("kursantów uczestniczyło w kursie"),
    )


def frontpage_lista_tagow(mo):
    try:
        language = mo.group(2)
        tagi = (
            TagTechnologia.objects.filter(
                language=language, widoczny_publicznie=True, widoczny_na_frontpage=True
            )
            .select_related("domena")
            .order_by("frontpage_ordering")
        )
    except IndexError:
        return "invalid language in frontpage_lista_tagow"
    return render_to_string(
        "www/partial/frontpage_lista_tagow.html", {"lista_tagow": tagi}
    )


def program_kursu_tag(mo):
    try:
        language, slug = get_lang_for_slug(mo.group(2))
        szkolenie = Szkolenie.objects.get(slug=slug, language=language)
    except Szkolenie.DoesNotExist:
        return "invalid szkolenie in program_kursu"
    return (
        my_textile(szkolenie.program) if szkolenie.program else "Program niedostępny."
    )


def frontpage_lista_trenerow(mo, language):
    try:
        limit = int(mo.group(2))
        trenerzy = (
            Prowadzacy.objects.exclude(fotka="")
            .filter(pokazywac=True)
            .order_by("ordering")[:limit]
        )
    except (IndexError, ValueError):
        return "invalid limit in frontpage_lista_trenerow"
    return render_to_string(
        "www/partial/frontpage_lista_trenerow.html",
        {
            "lista_trenerow": trenerzy,
            "language": language,
        },
    )


def staticpage_lista_trenerow(
    mo, language, template="www/partial/staticpage_lista_trenerow.html"
):
    try:
        str_ids = mo.group(2) if hasattr(mo, "group") else mo
        ids = [int(pk.strip()) for pk in str_ids.split(",")]
        if not ids:
            raise Exception("No ids")
    except:
        return "could not parse an ID staticpage_lista_trenerow"

    qs = Prowadzacy.objects.exclude(fotka="").filter(pokazywac=True).in_bulk(ids)

    trenerzy = []
    for i in ids:
        if i in qs:
            trenerzy.append(qs[i])

    return render_to_string(
        template,
        {
            "lista_trenerow": trenerzy,
            "language": language,
        },
    )


def szybki_kontakt(mo):
    return render_to_string(
        "www/partial/contact_form.html",
        {
            "recaptcha_sitekey": settings.NORECAPTCHA_INVISIBLE_SITE_KEY,
        },
    )


def szkolenie_kontynuacja(mo):
    try:
        parts = mo.group(2).split(";")
        headers = parts[1:]
        language, slug = get_lang_for_slug(parts[0])
        szkolenie = Szkolenie.objects.select_related().get(slug=slug, language=language)
    except Szkolenie.DoesNotExist:
        return "invalid szkolenie in szkolenie_kontynuacja"

    return render_to_string(
        "www/partial/szkolenie_kontynuacja.html",
        {
            "szkolenia_alternatywne": szkolenie.szkolenia_alternatywne.select_related().all(),
            "szkolenie_kontynuacja": szkolenie.kontynuacja,
            "language": language,
            "headers": headers,
        },
    )


def terminy_kursu(mo):
    try:
        language, slug = get_lang_for_slug(mo.group(2))
        szkolenie = Szkolenie.objects.select_related().get(slug=slug, language=language)
    except Szkolenie.DoesNotExist:
        return "invalid szkolenie in terminy_kursu"

    return render_to_string(
        "www/partial/kurs_terminy.html",
        {
            "szkolenie": szkolenie,
            "language": language,
        },
    )


def terminy_top(mo):
    """
    Funkcja zwraca informację o najbliższych terminach szkolenia w formie tekstowej.

    Algorytm:
    - jeśli szkolenie nie ma żadnych przyszłych terminów → zwraca "Dostępne na zamówienie dla grup."
    - jeśli ma tylko jeden przyszły termin → zwraca "Start DD.MM"
    - jeśli ma dokładnie dwa przyszłe terminy → zwraca "Start DD.MM lub DD.MM"
    - jeśli ma więcej niż dwa przyszłe terminy → zwraca dwa najbliższe terminy bez duplikatów
    """
    import datetime

    try:
        # Sprawdzamy czy parametr to slug czy id
        param = mo.group(2)
        if param.isdigit():
            # Jeśli to id
            szkolenie = Szkolenie.objects.get(id=int(param))
        else:
            # Jeśli to slug
            language, slug = get_lang_for_slug(param)
            szkolenie = Szkolenie.objects.get(slug=slug, language=language)
    except Szkolenie.DoesNotExist:
        return "invalid szkolenie in terminy_top"

    # Pobieramy wszystkie przyszłe terminy bezpośrednio, bez używania metody terminy()
    dzisiaj = datetime.date.today()
    terminy = TerminSzkolenia.objects.filter(
        szkolenie=szkolenie,
        zamkniete=False,
        termin__gte=dzisiaj
    ).order_by('termin')

    # Jeśli nie ma terminów
    if not terminy.exists():
        return "Dostępne na zamówienie dla grup."

    # Usuwamy duplikaty dat (ten sam dzień, różne lokalizacje)
    unikalne_daty = set()
    unikalne_terminy = []

    for termin in terminy:
        data_terminu = termin.termin.strftime("%d.%m")
        if data_terminu not in unikalne_daty:
            unikalne_daty.add(data_terminu)
            unikalne_terminy.append(termin)

    # Jeśli jest tylko jeden termin
    if len(unikalne_terminy) == 1:
        return f"Start {unikalne_terminy[0].termin.strftime('%d.%m')}"

    # Jeśli są dokładnie dwa terminy
    elif len(unikalne_terminy) == 2:
        return f"Start {unikalne_terminy[0].termin.strftime('%d.%m')} lub {unikalne_terminy[1].termin.strftime('%d.%m')}"

    # Jeśli jest więcej niż dwa terminy, bierzemy dwa najbliższe
    else:
        return f"Start {unikalne_terminy[0].termin.strftime('%d.%m')} lub {unikalne_terminy[1].termin.strftime('%d.%m')}"


def terminy_top_detailed(mo):
    """
    Funkcja zwraca szczegółową informację o najbliższych terminach szkolenia w formie tekstowej.

    Algorytm podobny do terminy_top, ale z dodatkową informacją o trybie szkolenia.
    Format: "Start od DD.MM [tryb_opis] lub od DD.MM [tryb_opis]"
    """
    import datetime

    try:
        # Sprawdzamy czy parametr to slug czy id
        param = mo.group(2)
        if param.isdigit():
            # Jeśli to id
            szkolenie = Szkolenie.objects.get(id=int(param))
        else:
            # Jeśli to slug
            language, slug = get_lang_for_slug(param)
            szkolenie = Szkolenie.objects.get(slug=slug, language=language)
    except Szkolenie.DoesNotExist:
        return "invalid szkolenie in terminy_top_detailed"

    # Pobieramy wszystkie przyszłe terminy bezpośrednio
    dzisiaj = datetime.date.today()
    terminy = TerminSzkolenia.objects.filter(
        szkolenie=szkolenie,
        zamkniete=False,
        termin__gte=dzisiaj
    ).order_by('termin')

    # Jeśli nie ma terminów
    if not terminy.exists():
        return "Dostępne na zamówienie dla grup."

    # Usuwamy duplikaty dat (ten sam dzień, różne lokalizacje)
    unikalne_daty = set()
    unikalne_terminy = []

    for termin in terminy:
        data_terminu = termin.termin.strftime("%d.%m")
        if data_terminu not in unikalne_daty:
            unikalne_daty.add(data_terminu)
            unikalne_terminy.append(termin)

    # Funkcja pomocnicza do generowania opisu terminu
    def termin_opis(termin):
        data = termin.termin.strftime("%d.%m")
        tryb_opis = szkolenie.get_tryb_opis(termin.tryb)
        return f"{data} {tryb_opis}"

    # Jeśli jest tylko jeden termin
    if len(unikalne_terminy) == 1:
        return f"Start od {termin_opis(unikalne_terminy[0])}"

    # Jeśli są dokładnie dwa terminy lub więcej (bierzemy dwa pierwsze)
    else:
        return f"Start od {termin_opis(unikalne_terminy[0])} lub od {termin_opis(unikalne_terminy[1])}"


def terminy_przy_zapisz_sie(mo):
    """
    Funkcja zwraca informację o konkretnym terminie szkolenia w formie tekstowej.

    Format: [[terminy_przy_zapisz_sie:<id/slug>;<indeks>;<godzina>]]

    Gdzie:
    - id/slug - identyfikator szkolenia
    - indeks - 0 dla pierwszego terminu, 1 dla drugiego terminu
    - godzina - opcjonalny parametr z godziną rozpoczęcia

    Zwraca:
    - "Obecnie brak terminu." - jeśli nie ma terminu o podanym indeksie
    - "Start DD.MM.YYYY" - jeśli jest termin, ale nie podano godziny
    - "Start DD.MM.YYYY, godzina HH:MM." - jeśli jest termin i podano godzinę
    """
    import datetime

    # Parsujemy parametry
    params = mo.group(2).split(';')
    if len(params) < 2:
        return "invalid parameters in terminy_przy_zapisz_sie - expected at least 2 parameters"

    szkolenie_param = params[0]
    try:
        indeks = int(params[1])
    except ValueError:
        return "invalid index parameter in terminy_przy_zapisz_sie - expected integer"

    godzina = params[2] if len(params) > 2 and params[2] else None

    # Pobieramy szkolenie
    try:
        if szkolenie_param.isdigit():
            # Jeśli to id
            szkolenie = Szkolenie.objects.get(id=int(szkolenie_param))
        else:
            # Jeśli to slug
            language, slug = get_lang_for_slug(szkolenie_param)
            szkolenie = Szkolenie.objects.get(slug=slug, language=language)
    except Szkolenie.DoesNotExist:
        return "invalid szkolenie in terminy_przy_zapisz_sie"

    # Pobieramy wszystkie przyszłe terminy bezpośrednio
    dzisiaj = datetime.date.today()
    terminy = TerminSzkolenia.objects.filter(
        szkolenie=szkolenie,
        zamkniete=False,
        termin__gte=dzisiaj
    ).order_by('termin')

    # Jeśli nie ma terminów
    if not terminy.exists():
        return "Obecnie brak terminu."

    # Usuwamy duplikaty dat (ten sam dzień, różne lokalizacje)
    unikalne_daty = set()
    unikalne_terminy = []

    for termin in terminy:
        data_terminu = termin.termin.strftime("%d.%m.%Y")
        if data_terminu not in unikalne_daty:
            unikalne_daty.add(data_terminu)
            unikalne_terminy.append(termin)

    # Sprawdzamy, czy istnieje termin o podanym indeksie
    if indeks >= len(unikalne_terminy):
        return "Obecnie brak terminu."

    # Generujemy odpowiedź
    termin = unikalne_terminy[indeks]
    data = termin.termin.strftime("%d.%m.%Y")

    if godzina:
        return f"Start {data}, godzina {godzina}."
    else:
        return f"Start {data}"


def parse_harmonogram_params(params):
    """
    Parsuje i waliduje parametry dla funkcji harmonogram_2025.

    Args:
        params (list): Lista parametrów z szablonu

    Returns:
        tuple: (szkolenie_param, tryb, przyszle_czy_w_toku) lub (None, None, None, error_message)
    """
    if len(params) < 2:
        return None, None, None, "invalid parameters in harmonogram_2025 - expected at least 2 parameters"

    szkolenie_param = params[0]
    try:
        tryb = int(params[1])
        if tryb not in [1, 2, 3]:
            return None, None, None, "invalid tryb parameter in harmonogram_2025 - expected 1, 2 or 3"
    except ValueError:
        return None, None, None, "invalid tryb parameter in harmonogram_2025 - expected integer"

    # Parametr przyszle_czy_w_toku jest opcjonalny, domyślnie 0 (przyszłe)
    przyszle_czy_w_toku = int(params[2]) if len(params) > 2 and params[2] else 0

    return szkolenie_param, tryb, przyszle_czy_w_toku, None


def get_szkolenie_by_param(szkolenie_param):
    """
    Pobiera obiekt szkolenia na podstawie parametru (ID lub slug).

    Args:
        szkolenie_param (str): ID lub slug szkolenia

    Returns:
        tuple: (szkolenie, None) lub (None, error_message)
    """
    try:
        if szkolenie_param.isdigit():
            # Jeśli to id
            # ID jest unikalne, więc nie potrzebujemy języka
            szkolenie_id = int(szkolenie_param)
            szkolenie = Szkolenie.objects.get(id=szkolenie_id)
        else:
            # Jeśli to slug
            language, slug = get_lang_for_slug(szkolenie_param)
            szkolenie = Szkolenie.objects.get(slug=slug, language=language)
        return szkolenie, None
    except Szkolenie.DoesNotExist:
        return None, "invalid szkolenie in harmonogram_2025"


def get_terminy(szkolenie, tryb, przyszle_czy_w_toku):
    """
    Pobiera terminy dla danego szkolenia, trybu i parametru przyszle_czy_w_toku.

    Args:
        szkolenie (Szkolenie): Obiekt szkolenia
        tryb (int): Tryb szkolenia (1, 2 lub 3)
        przyszle_czy_w_toku (int): 0 dla przyszłych, 1 dla w toku

    Returns:
        QuerySet: Terminy szkolenia
    """
    import datetime
    from django.conf import settings

    # Używamy bieżącej daty, chyba że jesteśmy w trybie testowym
    if settings.DEBUG and hasattr(settings, 'TEST_DATE') and settings.TEST_DATE:
        dzisiaj = settings.TEST_DATE
    else:
        dzisiaj = datetime.date.today()

    # Pobieramy wszystkie terminy dla danego szkolenia i trybu
    base_query = TerminSzkolenia.objects.filter(
        szkolenie=szkolenie,
        zamkniete=False,
        tryb=tryb
    )

    if przyszle_czy_w_toku == 0:  # Przyszłe terminy
        return base_query.filter(
            termin__gte=dzisiaj
        ).order_by('termin')
    else:  # Terminy w toku
        return base_query.filter(
            termin__lt=dzisiaj,
            termin_zakonczenia__gte=dzisiaj
        ).order_by('termin')


def format_date_with_weekday(date):
    """
    Formatuje datę z nazwą dnia tygodnia.

    Args:
        date (datetime.date): Data do sformatowania

    Returns:
        str: Sformatowana data z nazwą dnia tygodnia
    """
    from django.utils.dates import WEEKDAYS
    weekday_name = WEEKDAYS[date.weekday()].lower()
    return f"{date.strftime('%d.%m.%Y')} ({weekday_name})"


def get_all_dates_for_termin(termin):
    """
    Pobiera wszystkie daty dla danego terminu.

    Args:
        termin (TerminSzkolenia): Termin szkolenia

    Returns:
        list: Lista dat (datetime.date)
    """
    import datetime

    all_dates = []

    if termin.daty_szczegolowo:
        # Jeśli są zdefiniowane daty szczegółowe, używamy ich
        for date_str in termin.daty_szczegolowo.split(','):
            if date_str.strip():
                try:
                    date_obj = datetime.datetime.strptime(date_str.strip(), '%Y-%m-%d').date()
                    all_dates.append(date_obj)
                except ValueError:
                    # Ignorujemy niepoprawne daty
                    pass
        # Sortujemy daty
        all_dates.sort()
    else:
        # Jeśli nie ma zdefiniowanych dat szczegółowych, generujemy je na podstawie termin i termin_zakonczenia lub czas_dni szkolenia
        if termin.termin_zakonczenia:
            termin_do = termin.termin_zakonczenia
        elif termin.szkolenie.czas_dni and termin.szkolenie.czas_dni > 1:
            termin_do = termin.termin + datetime.timedelta(days=termin.szkolenie.czas_dni - 1)
        else:
            termin_do = termin.termin

        # Generujemy listę dat
        current_date = termin.termin
        while current_date <= termin_do:
            all_dates.append(current_date)
            current_date += datetime.timedelta(days=1)

    return all_dates


def format_tryb_wieczorowy(all_dates):
    """
    Formatuje daty dla trybu wieczorowego.

    Args:
        all_dates (list): Lista dat (datetime.date)

    Returns:
        str: Sformatowany tekst z datami
    """
    if not all_dates:
        return None

    dates = []
    # Pierwsza data z nazwą dnia tygodnia
    first_date = all_dates[0]
    dates.append(format_date_with_weekday(first_date))

    # Pozostałe daty w formacie DD.MM
    for date in all_dates[1:-1]:  # Pomijamy pierwszą i ostatnią datę
        dates.append(date.strftime('%d.%m'))

    # Ostatnia data w pełnym formacie (jeśli jest inna niż pierwsza)
    if len(all_dates) > 1:
        last_date = all_dates[-1]
        dates.append(last_date.strftime('%d.%m.%Y'))

    return ", ".join(dates) + "."


def format_tryb_weekendowy(all_dates):
    """
    Formatuje daty dla trybu weekendowego/zaocznego.

    Args:
        all_dates (list): Lista dat (datetime.date)

    Returns:
        list: Lista bloków dat
    """
    if not all_dates:
        return []

    blocks = []
    i = 0
    while i < len(all_dates):
        # Sprawdzamy, czy mamy parę dat (weekend)
        if i + 1 < len(all_dates) and (all_dates[i+1] - all_dates[i]).days <= 1:
            # Mamy parę dat (weekend lub dni ciągłe)
            dates = [format_date_with_weekday(all_dates[i]), format_date_with_weekday(all_dates[i+1])]
            blocks.append(", ".join(dates))
            i += 2
        else:
            # Pojedyncza data
            blocks.append(format_date_with_weekday(all_dates[i]))
            i += 1

    return blocks


def format_tryb_dzienny(all_dates):
    """
    Formatuje daty dla trybu dziennego.

    Args:
        all_dates (list): Lista dat (datetime.date)

    Returns:
        list: Lista bloków dat
    """
    if not all_dates:
        return []

    blocks = []
    current_block = []
    for i, date in enumerate(all_dates):
        current_block.append(date)
        # Jeśli to ostatnia data lub następna data nie jest kolejnym dniem
        if i == len(all_dates) - 1 or (all_dates[i+1] - date).days > 1:
            # Formatujemy daty w bloku
            dates = [format_date_with_weekday(d) for d in current_block]
            blocks.append(", ".join(dates))
            current_block = []

    return blocks


def harmonogram_2025(mo):
    """
    Funkcja zwraca szczegółowy harmonogram terminów szkolenia w formie tekstowej.

    Format: [[harmonogram_2025:<id/slug>;<tryb>;<przyszle_czy_w_toku>]]

    Gdzie:
    - id/slug - identyfikator szkolenia
    - tryb - 1 dla dziennego, 2 dla wieczorowego, 3 dla weekendowego/zaocznego
    - przyszle_czy_w_toku - opcjonalny parametr, 0 (domyślnie) dla przyszłych, 1 dla w toku

    Zwraca:
    - "-" - jeśli nie ma terminów dla podanych parametrów
    - Szczegółowy harmonogram terminów w zależności od trybu
    """
    import datetime
    import calendar
    from django.utils.dates import WEEKDAYS, WEEKDAYS_ABBR

    # Parsujemy parametry
    params = mo.group(2).split(';')
    szkolenie_param, tryb, przyszle_czy_w_toku, error = parse_harmonogram_params(params)

    if error:  # Jeśli jest błąd
        return error

    # Pobieramy szkolenie
    szkolenie, error = get_szkolenie_by_param(szkolenie_param)
    if error:
        return error

    # Pobieramy terminy
    terminy = get_terminy(szkolenie, tryb, przyszle_czy_w_toku)

    # Jeśli nie ma terminów
    if not terminy.exists():
        return "-"

    # Generujemy odpowiedź w zależności od trybu
    result = []

    for termin in terminy:
        # Pobieramy wszystkie daty dla terminu
        all_dates = get_all_dates_for_termin(termin)

        # Pobieramy lokalizację
        if termin.lokalizacja.fullname == "Zdalnie":
            lokalizacja = "Online"
        else:
            lokalizacja = termin.lokalizacja.fullname

        if tryb == 2:  # Tryb wieczorowy
            formatted_result = format_tryb_wieczorowy(all_dates)
            if formatted_result:
                result.append(f"{lokalizacja}: {formatted_result}")
        else:  # Tryb dzienny (1) lub weekendowy/zaoczny (3)
            if not all_dates:
                continue

            if tryb == 3:  # Tryb weekendowy/zaoczny
                blocks = format_tryb_weekendowy(all_dates)
            else:  # Tryb dzienny (1)
                blocks = format_tryb_dzienny(all_dates)

            if tryb == 1:  # Tryb dzienny - z line breaks między blokami, ale nie wewnątrz bloków
                result.append(f"{lokalizacja}: " + ",<br/>".join(blocks) + ".")
            else:  # Inne tryby - z line breaks
                result.append(f"{lokalizacja}: " + ",<br/>".join(blocks) + ".")

    # Łączymy wszystkie terminy
    if len(result) > 1:
        return "<br/><br/>".join(result)
    else:
        return result[0]

def parse_jesli_gwarantowany_params(params):
    """
    Parsuje i waliduje parametry dla funkcji jesli_gwarantowany.

    Args:
        params (list): Lista parametrów z szablonu

    Returns:
        tuple: (szkolenie_param, indeks, custom_text, None) lub (None, None, None, error_message)
    """
    if len(params) < 2:
        return None, None, None, "invalid parameters in jesli_gwarantowany - expected at least 2 parameters"

    szkolenie_param = params[0]
    try:
        indeks = int(params[1])
    except ValueError:
        return None, None, None, "invalid index parameter in jesli_gwarantowany - expected integer"

    # Tekst do wyświetlenia (opcjonalny)
    custom_text = params[2] if len(params) > 2 and params[2] else "termin gwarantowany"

    return szkolenie_param, indeks, custom_text, None


def get_unikalne_terminy(szkolenie):
    """
    Pobiera unikalne terminy dla danego szkolenia.

    Args:
        szkolenie (Szkolenie): Obiekt szkolenia

    Returns:
        list: Lista unikalnych terminów
    """
    import datetime

    # Pobieramy wszystkie przyszłe terminy
    dzisiaj = datetime.date.today()
    terminy = TerminSzkolenia.objects.filter(
        szkolenie=szkolenie,
        zamkniete=False,
        termin__gte=dzisiaj
    ).order_by('termin')

    # Usuwamy duplikaty dat (ten sam dzień, różne lokalizacje)
    unikalne_daty = set()
    unikalne_terminy = []

    for termin in terminy:
        data_terminu = termin.termin.strftime("%d.%m.%Y")
        if data_terminu not in unikalne_daty:
            unikalne_daty.add(data_terminu)
            unikalne_terminy.append(termin)

    return unikalne_terminy


def jesli_gwarantowany(mo):
    """
    Funkcja sprawdza, czy dany termin jest gwarantowany, i jeśli tak, to wyświetla odpowiedni tekst HTML.

    Format: [[jesli_gwarantowany:<id_szkolenia>;<indeks>;<tekst>]]

    Gdzie:
    - id_szkolenia - identyfikator szkolenia (ID lub slug)
    - indeks - indeks terminu (0 dla pierwszego terminu, 1 dla drugiego terminu, itd.)
    - tekst - opcjonalny parametr, tekst HTML do wyświetlenia, jeśli termin jest gwarantowany

    Zwraca:
    - Pusty string, jeśli termin nie jest gwarantowany
    - Domyślny tekst HTML lub tekst podany jako parametr, jeśli termin jest gwarantowany
    """
    # Parsujemy parametry
    params = mo.group(2).split(';')
    szkolenie_param, indeks, custom_text, error = parse_jesli_gwarantowany_params(params)

    if error:  # Jeśli jest błąd
        return error

    # Domyślny tekst HTML
    html = f'<p style="color: red; margin: 0px; margin-bottom: 10px; font-size: smaller;">{custom_text}</p>'

    # Pobieramy szkolenie
    szkolenie, error = get_szkolenie_by_param(szkolenie_param)
    if error:
        return ""

    # Pobieramy unikalne terminy
    unikalne_terminy = get_unikalne_terminy(szkolenie)

    # Sprawdzamy, czy istnieje termin o podanym indeksie
    if indeks >= len(unikalne_terminy):
        return ""

    # Pobieramy termin o podanym indeksie
    termin = unikalne_terminy[indeks]

    # Sprawdzamy, czy termin jest gwarantowany
    if termin.gwarantowany:
        return html
    else:
        return ""


def mydispatcher(mo):
    if mo.group(1) in ("include", "includes", "myinclude"):
        return myincluder(mo)
    elif mo.group(1) in ("comment", "mycomment"):
        return ""
    elif mo.group(1) in ("najblizsze_terminy",):
        return najblizszy_terminator(mo)
    elif mo.group(1) in ("najblizsze_terminy_en",):
        return najblizszy_terminator_en(mo)
    elif mo.group(1) in ("harmonogram",):
        return harmonogramer(mo)
    elif mo.group(1) in ("zobacz_tez_szkolenia",):
        return zobacz_tez_szkolenia(mo)
    elif mo.group(1) in ("sciezki_linki",):
        return sciezki_linki(mo)
    elif mo.group(1) in ("sciezki_tresci",):
        return sciezki_tresci(mo)
    elif mo.group(1) in ("ocena",):
        return ocena_szkolenia_tag(mo)
    elif mo.group(1) in ("program_kursu",):
        return program_kursu_tag(mo)
    elif mo.group(1) in ("frontpage_lista_tagow",):
        return frontpage_lista_tagow(mo)
    elif mo.group(1) in ("frontpage_lista_trenerow_pl",):
        return frontpage_lista_trenerow(mo, language="pl")
    elif mo.group(1) in ("frontpage_lista_trenerow_en",):
        return frontpage_lista_trenerow(mo, language="en")
    elif mo.group(1) in ("staticpage_lista_trenerow_pl",):
        return staticpage_lista_trenerow(mo, language="pl")
    elif mo.group(1) in ("szkolenie_kontynuacja",):
        return szkolenie_kontynuacja(mo)
    elif mo.group(1) in ("terminy_kursu",):
        return terminy_kursu(mo)
    elif mo.group(1) in ("terminy_top",):
        return terminy_top(mo)
    elif mo.group(1) in ("terminy_top_detailed",):
        return terminy_top_detailed(mo)
    elif mo.group(1) in ("terminy_przy_zapisz_sie",):
        return terminy_przy_zapisz_sie(mo)
    elif mo.group(1) in ("harmonogram_2025",):
        return harmonogram_2025(mo)
    elif mo.group(1) in ("jesli_gwarantowany",):
        return jesli_gwarantowany(mo)
    elif mo.group(1) in ("szybki_kontakt",):
        return szybki_kontakt(mo)
    else:
        return "<h3>Unknown myincludes command: " + mo.group(1) + ".</h3>"


def myincludes(content):
    return re.sub(r"\[\[([a-z0-9_]+):([^\]]*)\]\]", mydispatcher, content)


myincludes = stringfilter(myincludes)

register.filter("myincludes", myincludes)


def my_textile(value):
    try:
        import textile
    except ImportError:
        if settings.DEBUG:
            raise template.TemplateSyntaxError(
                "Error in {% my_textile %} filter: The Python textile library isn't installed."
            )
        return force_text(value)
    else:
        value = re.sub(r"(?m)^\#\*", "**", value)
        return mark_safe(force_text(textile.textile(smart_str(value))))


textile.is_safe = True

register.filter("my_textile", my_textile)


def pretty_lang_version(value):
    return {"pl": "Poland / polski", "en": "English / International"}.get(value, value)


register.filter("pretty_lang_version", pretty_lang_version)


@register.filter
def domain_for_language(value):
    return settings.DOMENY_DLA_JEZYKOW.get(value, "www.alx.pl")


def pdfignore(value):
    return re.sub(r"(?is)<!--\s*pdfignore\s*-->.*?<!--\s*endpdfignore\s*-->", "", value)


register.filter("pdfignore", pdfignore)

# Ten filtr jest zaimplementowany w Django, tylko najwyrazniej nie zarejestrowany. Rejestrujemy go zatem we wlasnym
# zakresie.
register.filter(_boolean_icon)


@register.filter
def vat_info(value, pokaz_brutto=False):
    """
    Zwraca info słowne o stawce VAT. Dopisywane po cenie.

    Jeśli podano opcję pokaz_brutto, to zamiast info o stawce VAT jest wyświetlana cena brutto.
    """
    wersja_jezykowa = WersjaJezykowa.biezaca()

    if wersja_jezykowa.stawka_vat is None:
        return ""

    if wersja_jezykowa.stawka_vat == Decimal("0.0000"):
        return _("(zwolnione z VAT)")
    else:
        if pokaz_brutto:
            return _("({0} {1} brutto)").format(
                number_format(pretty_number(value.cena_brutto())), value.waluta.symbol
            )
        else:
            return _("netto (VAT %s%%)") % (
                pretty_number(wersja_jezykowa.stawka_vat * Decimal("100")),
            )


@register.filter
def cena_info(value, pokaz_brutto=False):
    """
    Wyświetla info o cenie obiektu (cena, waluta, info o stawce VAT).
    """
    return pgettext_lazy("cena_info", "{0} {1} {2}").format(
        number_format(value.cena), value.waluta.symbol, vat_info(value, pokaz_brutto)
    )


@register.filter
def cena_info_short(value, przed_promocja=False):
    """
    Wyświetla info o cenie obiektu (cena netto, waluta).
    """

    cena = value.cena_przed_promocja if przed_promocja else value.cena

    if value.cena_miesieczna and value.tag_dlugosc.slug == "kurs-zawodowy":
        return pgettext_lazy(
            "cena_info_short_mc", "{0} <small>{1}/mies.</small>"
        ).format(number_format(floatformat(value.cena_rata(cena))), value.waluta.symbol)
    return pgettext_lazy("cena_info_short", "{0} <small>{1}</small>").format(
        number_format(cena), value.waluta.symbol
    )


@register.filter
def cena_info_short_with_suffix(value, przed_promocja=False):
    """
    Wyświetla info o cenie obiektu (cena netto, waluta).
    """

    cena = value.cena_przed_promocja if przed_promocja else value.cena

    dopisek = ""
    if value.waluta.symbol == "EUR":
        dopisek = "<small>in English:</small><br style='line-height: 0.8;'/>"

    if value.cena_miesieczna and value.tag_dlugosc.slug == "kurs-zawodowy":
        return pgettext_lazy(
            "cena_info_short_mc", "<div style='margin-top: 6px; line-height: 0.5;'>{0}{1} <small>{2}/mies.</small></div>"
        ).format(dopisek, number_format(floatformat(value.cena_rata(cena))), value.waluta.symbol)

    return pgettext_lazy("cena_info_short", "<div style='margin-top: 6px; line-height: 0.5;'>{0}{1} <small>{2}</small><div>").format(
        dopisek, number_format(cena), value.waluta.symbol
    )


@register.filter
def wysokosc_raty(value):
    """
    Wyświetla info o cenie raty (cena netto, waluta).
    """

    return number_format(floatformat(value.cena_rata(value.cena)))


@register.filter
def przypisane_szkolenia(value, szkolenie=None):
    """
    Zwraca (unikalną) listę szkoleń przypisanych do danego zbioru obiektów.

    Używane np. do uzyskiwania listy szkoleń z tłumaczeniami dla terminów, żeby wyświetlić ceny w wielu językach.

    Jeśli podano parametr szkolenie, szkolenie to jest dodawane do zwracanej listy (użyteczne, gdy nie ma terminów).
    """

    def klucz_sortowania(szkolenie):
        # Na stronie polskiej chcemy wyświetlać najpierw cenę polską.
        if translation.get_language() == "pl" and szkolenie.language == "pl":
            return -1000
        return 0

    szkolenia = set(obiekt.szkolenie for obiekt in value)
    if szkolenie:
        szkolenia.add(szkolenie)
    return sorted(szkolenia, key=klucz_sortowania)


@register.filter
def firstie(value, value2=None):
    """
    zamiast {% firstof %} do użycia w blocktrans
    """
    z = value or value2
    if not z:
        z = ""
    return z


@register.filter
def remove_text(value, text_to_remove):
    return value.replace(text_to_remove, "")


@register.filter(name="starts_with_w")
def starts_with_w(value):
    """Check if the given string starts with 'W' or 'w'."""
    return value.lower().startswith("w-")


@register.tag
def lineless(parser, token):
    nodelist = parser.parse(("endlineless",))
    parser.delete_first_token()
    return LinelessNode(nodelist)


class LinelessNode(Node):
    def __init__(self, nodelist):
        self.nodelist = nodelist

    def render(self, context):
        input_str = self.nodelist.render(context)
        output_str = ""
        for line in input_str.splitlines():
            if line.strip():
                output_str = "\n".join((output_str, line))
        return output_str


@register.filter(name='does_not_start_with')
def does_not_start_with(value, arg):
    """
    Custom filter to check if a string does not start with a specified substring.
    """
    return not value.lower().startswith(arg.lower())



@register.filter(name='ensure_style_tags')
def ensure_style_tags(value):
    if not value:
        return value
    # Check if the content is already wrapped with <style> tags
    if not re.search(r'<style[^>]*>.*?</style>', value, re.DOTALL):
        value = f'<style>{value}</style>'
    return value
