import os
import time
from functools import wraps

import selenium
from django.conf import settings
from django.core.management import call_command
from django.test import LiveServerTestCase, TestCase, TransactionTestCase
from django.urls import reverse
from selenium.common.exceptions import (
    NoSuchElementException,
    StaleElementReferenceException,
    TimeoutException,
)
from selenium.webdriver.support.wait import WebDriverWait

from www.testfactories import UserFactory

# Domyślny timeout dla testów czekających wykonanie JS
TIMEOUT = 20

# <PERSON><PERSON><PERSON><PERSON>a częstotliwość sprawdzania stanu dla testów czekających wykonanie JS
POLL_FREQUENCY = 0.5

WebDriver = selenium.webdriver.Chrome

WEBDRIVER_RETRIES = 30
WEBDRIVER_DELAY = 2.7  # seconds

chrome_options = selenium.webdriver.ChromeOptions()
chrome_options.add_argument("headless")
chrome_options.add_argument("lang={}".format(settings.LANGUAGE_CODE))
chrome_options.add_argument("--headless")
chrome_options.add_argument("--start-maximized")
chrome_options.add_argument("disable-infobars")
chrome_options.add_argument("--disable-extensions")
chrome_options.add_argument("--no-sandbox")
chrome_options.add_argument("--disable-dev-shm-usage")
chrome_options.add_argument("window-size=2600x2600")

chrome_service_args = getattr(settings, "SELENIUM_CHROME_SERVICE_ARGS", [])


class Wait(WebDriverWait):

    """
    Subclass of WebDriverWait with predetermined timeout and poll
    frequency. Also deals with a wider variety of exceptions.
    """

    def __init__(self, driver):
        super().__init__(driver, TIMEOUT, POLL_FREQUENCY)

    def until(self, method, message=""):
        """
        Calls the method provided with the driver as an
        argument until the return value is not False.
        """
        end_time = time.time() + self._timeout
        while True:
            try:
                value = method(self._driver)
                if value:
                    return value
            except NoSuchElementException:
                pass
            except StaleElementReferenceException:
                pass
            time.sleep(self._poll)
            if time.time() > end_time:
                break
        raise TimeoutException(message)

    def until_not(self, method, message=""):
        """
        Calls the method provided with the driver as an argument
        until the return value is False.
        """
        end_time = time.time() + self._timeout
        while True:
            try:
                value = method(self._driver)
                if not value:
                    return value
            except NoSuchElementException:
                return True
            except StaleElementReferenceException:
                pass
            time.sleep(self._poll)
            if time.time() > end_time:
                break
        raise TimeoutException(message)


def skroc_queryset(queryset_or_list):
    """
    Skróć queryseta lub listę do jednego, losowego, obiektu,
    jeśli jest ustawione ustawienie SZYBKIE_TESTY.
    """

    return queryset_or_list


def screenshot_on_error(test):
    """
    Dekorator @screenshot_on_error_test, który zapisze screenshot ostatniego ekranu,
    który się pojawił w teście zanim ten wyleciał.
    Działa pod warunkiem, że pod settings.TEST_SCREENSHOT_WRAP_DIRECTORY, jest
    podana ścieżka do katalogu gdzie ma zapisać.
    """
    # ściągnięte z http://wiki.ddenis.com/index.php?title=Selenium_testing_of_a_django_project
    @wraps(test)
    def inner(*args, **kwargs):
        if hasattr(settings, "TEST_SCREENSHOT_WRAP_DIRECTORY"):
            try:
                test(*args, **kwargs)
            except:
                testObject = args[0]  # self in the test
                name = "{0}.png".format(testObject._testMethodName)
                path = os.path.join(settings.TEST_SCREENSHOT_WRAP_DIRECTORY, name)
                try:
                    testObject.selenium.get_screenshot_as_file(path)
                except Exception:
                    pass
                raise

    return inner


class ALXTestCase(TestCase):
    fixtures = ["www.json", "i18n.json"]


class ALXTransactionTestCase(TransactionTestCase):
    fixtures = ["www.json", "i18n.json"]


class ALXLiveServerTestCase(LiveServerTestCase):
    fixtures = ["www.json", "i18n.json"]

    @classmethod
    def setUpClass(cls):
        super().setUpClass()

        # Workaround for "Connection reset by peer" random error.
        for attempt in range(WEBDRIVER_RETRIES):
            try:
                cls.selenium = WebDriver(
                    options=chrome_options, service_args=chrome_service_args
                )
            except Exception:
                time.sleep(WEBDRIVER_DELAY)
            else:
                break

        cls.selenium.implicitly_wait(10)
        cls.selenium.set_page_load_timeout(60)
        cls.selenium.set_window_position(0, 0)
        cls.selenium.maximize_window()

    @classmethod
    def tearDownClass(cls):
        cls.selenium.quit()
        super().tearDownClass()

    def tearDown(self):
        super().tearDown()
        self.selenium.delete_all_cookies()

    def _fixture_teardown(self):
        for db_name in self._databases_names(include_mirrors=False):
            call_command(
                "flush",
                verbosity=0,
                interactive=False,
                database=db_name,
                reset_sequences=False,
                allow_cascade=True,
                inhibit_post_migrate=False,
            )

    def admin_login(self, user=None):
        if user is None:
            user = UserFactory.create(
                password="test.pw",
                is_superuser=True,
                is_staff=True,
                first_name="Jan",
                last_name="Pawłowski",
            )

        # Czyścimy sesje, aby pokazała się nam strona logowania.
        self.selenium.delete_all_cookies()

        dane_do_wypelnienia = {
            "id_username": user.username,
            "id_password": "test.pw",
        }
        self.selenium.get(self.live_server_url + reverse("admin:index"))
        self.fill_fields_by_id(**dane_do_wypelnienia)
        self.click_by_xpath("//div[@class='submit-row']//input")
        return user

    def click_by_xpath(self, xpath):
        element = self.wait_for_element_by_xpath(xpath)
        element.click()
        return element

    def screenshot(self):
        if hasattr(settings, "TEST_SCREENSHOT_DIRECTORY"):
            if not hasattr(self, "_screenshot_number"):
                self._screenshot_number = 1
            name = "%s_%d.png" % (self._testMethodName, self._screenshot_number)
            path = os.path.join(settings.TEST_SCREENSHOT_DIRECTORY, name)
            self.selenium.get_screenshot_as_file(path)
            self._screenshot_number += 1

    def wait_for_element_by_xpath(self, xpath):
        element_is_present = lambda driver: driver.find_element_by_xpath(xpath)
        msg = "An element '%s' should be on the page" % xpath
        element = Wait(self.selenium).until(element_is_present, msg)
        self.screenshot()
        return element

    def wait_for_visible_element_by_xpath(self, xpath):
        element_is_visible = lambda driver: driver.find_element_by_xpath(
            xpath
        ).is_displayed()
        msg = "The element '%s' should be visible" % xpath
        Wait(self.selenium).until(element_is_visible, msg)
        element = self.selenium.find_element_by_xpath(xpath)
        self.screenshot()
        return element

    def wait_for_hidden_element_by_xpath(self, xpath):
        element_is_hidden = lambda driver: not driver.find_element_by_xpath(
            xpath
        ).is_displayed()
        msg = "The element '%s' should be hidden" % xpath
        Wait(self.selenium).until(element_is_hidden, msg)
        element = self.selenium.find_element_by_xpath(xpath)
        self.screenshot()
        return element

    def wait_for_element_by_id(self, id):
        element_is_present = lambda driver: driver.find_element_by_id(id)
        msg = "An element '%s' should be on the page" % id
        element = Wait(self.selenium).until(element_is_present, msg)
        self.screenshot()
        return element

    def fill_field_by_name(self, name, value):
        field = self.wait_for_element_by_xpath(
            "//*[(@type != 'hidden' or not(@type)) and @name = '" + name + "']"
        )
        field.send_keys(value)
        return field

    def fill_fields_by_name(self, **kwargs):
        for name, value in list(kwargs.items()):
            self.fill_field_by_name(name, value)

    def fill_field_by_id(self, name, value):
        field = self.wait_for_element_by_xpath("//*[@id='" + name + "']")
        field.send_keys(value)
        return field

    def fill_fields_by_id(self, **kwargs):
        for name, value in list(kwargs.items()):
            self.fill_field_by_id(name, value)
