from datetime import date, timed<PERSON>ta
from decimal import Decimal
from django.test import TestCase
from www.templatetags.myfilters import myincludes
from www.models import Szkolenie, TerminSzkolenia, Lokalizacja, TagZawod, TagDlugosc, Panstwo
from i18n.models import Waluta


class JesliGwarantowanyTestCase(TestCase):
    def setUp(self):
        # Tworzymy potrzebne obiekty
        self.panstwo = Panstwo.objects.create(nazwa="Polska")
        self.waluta = Waluta.objects.create(nazwa="Polski złoty", symbol="PLN", kurs_kupna=Decimal("1.00"), zaliczka=Decimal("406.50"))
        self.tag_zawod = TagZawod.objects.create(nazwa="Programista", language="pl")
        self.tag_dlugosc = TagDlugosc.objects.create(nazwa="Szkolenie", slug="szkolenie")

        # Tworzymy testowe szkolenie
        self.szkolenie = Szkolenie.objects.create(
            nazwa="Test Szkolenie",
            slug="test-szkolenie",
            language="pl",
            aktywne=True,
            tag_zawod=self.tag_zawod,
            tag_dlugosc=self.tag_dlugosc,
            waluta=self.waluta,
            kod="TST",
            czas_dni=1,
            cena_bazowa=Decimal("100.00"),
            program="Program testowy",
            opis="Opis testowy",
            cel="Cel testowy"
        )

        # Tworzymy lokalizacje
        self.lokalizacja1 = Lokalizacja.objects.create(
            shortname="WAW",
            fullname="Warszawa",
            numer=1,
            panstwo=self.panstwo
        )

        # Dzisiejsza data
        self.today = date.today()

        # Tworzymy termin gwarantowany
        self.termin_gwarantowany = TerminSzkolenia.objects.create(
            szkolenie=self.szkolenie,
            termin=self.today + timedelta(days=10),
            lokalizacja=self.lokalizacja1,
            zamkniete=False,
            tryb=1,
            gwarantowany=True
        )

        # Tworzymy termin niegwarantowany
        self.termin_niegwarantowany = TerminSzkolenia.objects.create(
            szkolenie=self.szkolenie,
            termin=self.today + timedelta(days=20),
            lokalizacja=self.lokalizacja1,
            zamkniete=False,
            tryb=1,
            gwarantowany=False
        )

    def test_termin_gwarantowany_domyslny_tekst(self):
        """Test dla terminu gwarantowanego z domyślnym tekstem"""
        result = myincludes(f"[[jesli_gwarantowany:{self.szkolenie.id};0]]")
        expected = '<p style="color: red; margin: 0px; margin-bottom: 10px; font-size: smaller;">termin gwarantowany</p>'
        self.assertEqual(result, expected)

    def test_termin_gwarantowany_wlasny_tekst(self):
        """Test dla terminu gwarantowanego z własnym tekstem"""
        custom_text ='GWARANTOWANY'
        expected = '<p style="color: red; margin: 0px; margin-bottom: 10px; font-size: smaller;">GWARANTOWANY</p>'
        result = myincludes(f"[[jesli_gwarantowany:{self.szkolenie.id};0;{custom_text}]]")
        self.assertEqual(result, expected)

    def test_termin_niegwarantowany(self):
        """Test dla terminu niegwarantowanego"""
        result = myincludes(f"[[jesli_gwarantowany:{self.szkolenie.id};1]]")
        self.assertEqual(result, "")

    def test_termin_innego_szkolenia(self):
        """Test dla terminu innego szkolenia"""
        # Tworzymy inne szkolenie
        inne_szkolenie = Szkolenie.objects.create(
            nazwa="Inne Szkolenie",
            slug="inne-szkolenie",
            language="pl",
            aktywne=True,
            tag_zawod=self.tag_zawod,
            tag_dlugosc=self.tag_dlugosc,
            waluta=self.waluta,
            kod="INS",
            czas_dni=1,
            cena_bazowa=Decimal("100.00"),
            program="Program testowy",
            opis="Opis testowy",
            cel="Cel testowy"
        )

        # Tworzymy termin gwarantowany dla innego szkolenia
        termin_innego_szkolenia = TerminSzkolenia.objects.create(
            szkolenie=inne_szkolenie,
            termin=self.today + timedelta(days=10),
            lokalizacja=self.lokalizacja1,
            zamkniete=False,
            tryb=1,
            gwarantowany=True
        )

        # Sprawdzamy, czy funkcja zwróci pusty string dla terminu innego szkolenia
        result = myincludes(f"[[jesli_gwarantowany:{self.szkolenie.id};0]]")
        self.assertEqual(result, '<p style="color: red; margin: 0px; margin-bottom: 10px; font-size: smaller;">termin gwarantowany</p>')

        # Sprawdzamy, czy funkcja zwróci pusty string dla terminu innego szkolenia
        result = myincludes(f"[[jesli_gwarantowany:{inne_szkolenie.id};0]]")
        self.assertEqual(result, '<p style="color: red; margin: 0px; margin-bottom: 10px; font-size: smaller;">termin gwarantowany</p>')

    def test_niepoprawne_parametry(self):
        """Test dla niepoprawnych parametrów"""
        # Brak indeksu terminu
        result = myincludes(f"[[jesli_gwarantowany:{self.szkolenie.id}]]")
        self.assertIn("invalid parameters", result)

        # Niepoprawny indeks terminu
        result = myincludes(f"[[jesli_gwarantowany:{self.szkolenie.id};abc]]")
        self.assertIn("invalid index parameter", result)

        # Indeks poza zakresem
        result = myincludes(f"[[jesli_gwarantowany:{self.szkolenie.id};99]]")
        self.assertEqual(result, "")

        # Niepoprawne ID szkolenia
        result = myincludes("[[jesli_gwarantowany:9999;0]]")
        self.assertEqual(result, "")
