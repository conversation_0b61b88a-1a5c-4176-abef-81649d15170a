from django.test import TestCase
from mock import MagicMock, patch

from common import postivo

from ..testfactories import FakturaWysylkaFactory


class PostivoAPITestCase(TestCase):
    @patch("common.postivo._make_dispatch")
    def test_dispatch_ok(self, mock_dispatch):
        mock_dispatch.return_value = MagicMock(result="ERR", result_description="Błąd")

        self.assertEqual(postivo.dispatch({}, b"abc"), (None, "Błąd"))

    @patch("common.postivo._make_dispatch")
    def test_dispatch_error(self, mock_dispatch):
        mock_dispatch.return_value = MagicMock(
            result="OK", shipments=[MagicMock(id="T06030563", status_code="1")]
        )

        faktura = FakturaWysylkaFactory.create()

        self.assertEqual(
            postivo.dispatch(
                faktura.get_recipient_data(), b"abc", faktura.callback_url
            ),
            ({"id": "T06030563", "status": "1"}, None),
        )
