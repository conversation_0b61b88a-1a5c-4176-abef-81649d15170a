from datetime import date, timedelta
from decimal import Decimal
from django.test import TestCase
from www.templatetags.myfilters import myincludes
from www.models import Szkolenie, TerminSzkolenia, Lokalizacja, TagZawod, TagDlugosc, Panstwo
from i18n.models import Waluta


class TerminyPrzyZapiszSieTestCase(TestCase):
    def setUp(self):
        # Tworzymy potrzebne obiekty
        self.panstwo = Panstwo.objects.create(nazwa="Polska")
        self.waluta = Waluta.objects.create(nazwa="Polski złoty", symbol="PLN", kurs_kupna=Decimal("1.00"), zaliczka=Decimal("406.50"))
        self.tag_zawod = TagZawod.objects.create(nazwa="Programista", language="pl")
        self.tag_dlugosc = TagDlugosc.objects.create(nazwa="Szkolenie", slug="szkolenie")
        
        # Tworzymy testowe szkolenie
        self.szkolenie = Szkolenie.objects.create(
            nazwa="Test Szkolenie",
            slug="test-szkolenie",
            language="pl",
            aktywne=True,
            tag_zawod=self.tag_zawod,
            tag_dlugosc=self.tag_dlugosc,
            waluta=self.waluta,
            kod="TST",
            czas_dni=1,
            cena_bazowa=Decimal("100.00"),
            program="Program testowy",
            opis="Opis testowy",
            cel="Cel testowy"
        )
        
        # Tworzymy lokalizacje
        self.lokalizacja1 = Lokalizacja.objects.create(
            shortname="WAW",
            fullname="Warszawa",
            numer=1,
            panstwo=self.panstwo
        )
        
        self.lokalizacja2 = Lokalizacja.objects.create(
            shortname="KRK",
            fullname="Kraków",
            numer=2,
            panstwo=self.panstwo
        )
        
        self.lokalizacja3 = Lokalizacja.objects.create(
            shortname="ZDL",
            fullname="Zdalnie",
            numer=3,
            panstwo=self.panstwo
        )
        
        # Dzisiejsza data
        self.today = date.today()
    
    def test_brak_terminow(self):
        """Test gdy nie ma żadnych terminów"""
        # Indeks 0
        result = myincludes(f"[[terminy_przy_zapisz_sie:{self.szkolenie.slug};0]]")
        self.assertEqual(result, "Obecnie brak terminu.")
        
        # Indeks 1
        result = myincludes(f"[[terminy_przy_zapisz_sie:{self.szkolenie.slug};1]]")
        self.assertEqual(result, "Obecnie brak terminu.")
    
    def test_jeden_termin(self):
        """Test gdy jest tylko jeden termin"""
        termin_date = self.today + timedelta(days=10)
        TerminSzkolenia.objects.create(
            szkolenie=self.szkolenie,
            termin=termin_date,
            lokalizacja=self.lokalizacja1,
            zamkniete=False,
            tryb=1
        )
        
        # Indeks 0 - bez godziny
        result = myincludes(f"[[terminy_przy_zapisz_sie:{self.szkolenie.slug};0]]")
        expected = f"Start {termin_date.strftime('%d.%m.%Y')}"
        self.assertEqual(result, expected)
        
        # Indeks 0 - z godziną
        result = myincludes(f"[[terminy_przy_zapisz_sie:{self.szkolenie.slug};0;09:00]]")
        expected = f"Start {termin_date.strftime('%d.%m.%Y')}, godzina 09:00."
        self.assertEqual(result, expected)
        
        # Indeks 1 - nie ma drugiego terminu
        result = myincludes(f"[[terminy_przy_zapisz_sie:{self.szkolenie.slug};1]]")
        self.assertEqual(result, "Obecnie brak terminu.")
    
    def test_dwa_terminy(self):
        """Test gdy są dokładnie dwa terminy"""
        termin_date1 = self.today + timedelta(days=10)
        termin_date2 = self.today + timedelta(days=20)
        
        TerminSzkolenia.objects.create(
            szkolenie=self.szkolenie,
            termin=termin_date1,
            lokalizacja=self.lokalizacja1,
            zamkniete=False,
            tryb=1
        )
        
        TerminSzkolenia.objects.create(
            szkolenie=self.szkolenie,
            termin=termin_date2,
            lokalizacja=self.lokalizacja2,
            zamkniete=False,
            tryb=1
        )
        
        # Indeks 0 - bez godziny
        result = myincludes(f"[[terminy_przy_zapisz_sie:{self.szkolenie.slug};0]]")
        expected = f"Start {termin_date1.strftime('%d.%m.%Y')}"
        self.assertEqual(result, expected)
        
        # Indeks 0 - z godziną
        result = myincludes(f"[[terminy_przy_zapisz_sie:{self.szkolenie.slug};0;09:00]]")
        expected = f"Start {termin_date1.strftime('%d.%m.%Y')}, godzina 09:00."
        self.assertEqual(result, expected)
        
        # Indeks 1 - bez godziny
        result = myincludes(f"[[terminy_przy_zapisz_sie:{self.szkolenie.slug};1]]")
        expected = f"Start {termin_date2.strftime('%d.%m.%Y')}"
        self.assertEqual(result, expected)
        
        # Indeks 1 - z godziną
        result = myincludes(f"[[terminy_przy_zapisz_sie:{self.szkolenie.slug};1;18:00]]")
        expected = f"Start {termin_date2.strftime('%d.%m.%Y')}, godzina 18:00."
        self.assertEqual(result, expected)
    
    def test_wiele_terminow(self):
        """Test gdy jest więcej niż dwa terminy"""
        termin_date1 = self.today + timedelta(days=10)
        termin_date2 = self.today + timedelta(days=20)
        termin_date3 = self.today + timedelta(days=30)
        
        TerminSzkolenia.objects.create(
            szkolenie=self.szkolenie,
            termin=termin_date1,
            lokalizacja=self.lokalizacja1,
            zamkniete=False,
            tryb=1
        )
        
        TerminSzkolenia.objects.create(
            szkolenie=self.szkolenie,
            termin=termin_date2,
            lokalizacja=self.lokalizacja2,
            zamkniete=False,
            tryb=1
        )
        
        TerminSzkolenia.objects.create(
            szkolenie=self.szkolenie,
            termin=termin_date3,
            lokalizacja=self.lokalizacja3,
            zamkniete=False,
            tryb=1
        )
        
        # Indeks 0 - pierwszy termin
        result = myincludes(f"[[terminy_przy_zapisz_sie:{self.szkolenie.slug};0;09:00]]")
        expected = f"Start {termin_date1.strftime('%d.%m.%Y')}, godzina 09:00."
        self.assertEqual(result, expected)
        
        # Indeks 1 - drugi termin
        result = myincludes(f"[[terminy_przy_zapisz_sie:{self.szkolenie.slug};1;09:00]]")
        expected = f"Start {termin_date2.strftime('%d.%m.%Y')}, godzina 09:00."
        self.assertEqual(result, expected)
        
        # Indeks 2 - trzeci termin (nie powinien być dostępny w tej funkcji)
        result = myincludes(f"[[terminy_przy_zapisz_sie:{self.szkolenie.slug};2]]")
        expected = f"Start {termin_date3.strftime('%d.%m.%Y')}"
        self.assertEqual(result, expected)
    
    def test_duplikaty_dat(self):
        """Test gdy są duplikaty dat (ten sam dzień, różne lokalizacje)"""
        termin_date1 = self.today + timedelta(days=10)
        termin_date2 = self.today + timedelta(days=10)  # Ten sam dzień co termin1
        termin_date3 = self.today + timedelta(days=20)
        
        TerminSzkolenia.objects.create(
            szkolenie=self.szkolenie,
            termin=termin_date1,
            lokalizacja=self.lokalizacja1,
            zamkniete=False,
            tryb=1
        )
        
        TerminSzkolenia.objects.create(
            szkolenie=self.szkolenie,
            termin=termin_date2,
            lokalizacja=self.lokalizacja3,  # Inna lokalizacja
            zamkniete=False,
            tryb=1
        )
        
        TerminSzkolenia.objects.create(
            szkolenie=self.szkolenie,
            termin=termin_date3,
            lokalizacja=self.lokalizacja2,
            zamkniete=False,
            tryb=1
        )
        
        # Indeks 0 - pierwszy termin
        result = myincludes(f"[[terminy_przy_zapisz_sie:{self.szkolenie.slug};0]]")
        expected = f"Start {termin_date1.strftime('%d.%m.%Y')}"
        self.assertEqual(result, expected)
        
        # Indeks 1 - drugi termin (powinien być termin_date3, bo termin_date2 jest duplikatem)
        result = myincludes(f"[[terminy_przy_zapisz_sie:{self.szkolenie.slug};1]]")
        expected = f"Start {termin_date3.strftime('%d.%m.%Y')}"
        self.assertEqual(result, expected)
    
    def test_id_zamiast_sluga(self):
        """Test gdy używamy ID zamiast sluga"""
        termin_date = self.today + timedelta(days=10)
        TerminSzkolenia.objects.create(
            szkolenie=self.szkolenie,
            termin=termin_date,
            lokalizacja=self.lokalizacja1,
            zamkniete=False,
            tryb=1
        )
        
        result = myincludes(f"[[terminy_przy_zapisz_sie:{self.szkolenie.id};0]]")
        expected = f"Start {termin_date.strftime('%d.%m.%Y')}"
        self.assertEqual(result, expected)
    
    def test_niepoprawne_parametry(self):
        """Test dla niepoprawnych parametrów"""
        # Brak indeksu
        result = myincludes(f"[[terminy_przy_zapisz_sie:{self.szkolenie.slug}]]")
        self.assertIn("invalid parameters", result)
        
        # Niepoprawny indeks
        result = myincludes(f"[[terminy_przy_zapisz_sie:{self.szkolenie.slug};abc]]")
        self.assertIn("invalid index parameter", result)
        
        # Niepoprawne ID szkolenia
        result = myincludes("[[terminy_przy_zapisz_sie:9999;0]]")
        self.assertIn("invalid szkolenie", result)
