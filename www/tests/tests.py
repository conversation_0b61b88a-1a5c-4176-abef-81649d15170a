import datetime
import os
import random
import re
import string
import time
import unittest
import uuid
import warnings
from decimal import ROUND_UP, Decimal
from io import BytesIO

from django.conf import settings
from django.core import mail, signing
from django.core.cache import cache
from django.core.exceptions import ValidationError
from django.core.files.uploadedfile import SimpleUploadedFile
from django.db.models import Q
from django.test.utils import override_settings
from django.urls import reverse
from django.utils.formats import number_format
from mock import patch
from selenium.common.exceptions import NoSuchElementException, TimeoutException
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support.color import Color
from selenium.webdriver.support.expected_conditions import element_to_be_clickable
from selenium.webdriver.support.ui import Select
from selenium.webdriver.support.wait import WebDriverWait

import www.tasks as tasks
from captcha.models import Captcha
from i18n.models import Waluta
from i18n.utils import pretty_number
from newsletter.models import Odbiorca
from www.models import (
    AutoresponderLog,
    CalendarUpdate,
    ContinuationUnsubscribed,
    DzienSzkolenia,
    FakturaWysylka,
    HighlightPlacement,
    MyFlatPage,
    Panstwo,
    Szkolenie,
    TagDlugosc,
    TagTechnologia,
    TerminSzkolenia,
    Uczestnik,
    UczestnikPlik,
    UserNotification,
    Zgloszenie,
)
from www.testfactories import (
    AutoryzacjaFactory,
    ContentGroupFactory,
    ContinuationUnsubscribedFactory,
    DiscountCodeFactory,
    DzienSzkoleniaFactory,
    FakturaWysylkaFactory,
    GraduateFactory,
    GraduateStoryFactory,
    GrupaZaszeregowaniaFactory,
    HighlightFactory,
    KursFactory,
    LokalizacjaFactory,
    MyFlatPageFactory,
    OdbiorcaFactory,
    PotencjalnyChetnyFactory,
    ProwadzacyFactory,
    SalaFactory,
    SiteModuleFactory,
    SprzetFactory,
    SzkolenieFactory,
    SzkolenieWariantFactory,
    TagTechnologiaFactory,
    TagTechnologiaProwadzacyFactory,
    TagZawodFactory,
    TerminSzkoleniaEnFactory,
    TerminSzkoleniaFactory,
    TerminSzkoleniaLogFactory,
    TerminZamknietySzkoleniaEnFactory,
    TerminZamknietySzkoleniaFactory,
    UczestnikFactory,
    UczestnikPlikFactory,
    UserCoursesNotificationFactory,
    UserNotificationFactory,
    UserNotificationLogFactory,
    WalutaFactory,
    ZgloszenieFactory,
)
from www.testhelpers import ALXLiveServerTestCase, ALXTestCase

from ..forms import (
    DzienSzkoleniaAdminInlineForm,
    SzkolenieAdminForm,
    TerminSzkoleniaAdminForm,
)
from ..utils import email_signing, generate_pdf


class PDFTestCase(ALXTestCase):
    def test_generowanie_pdfow_dziala(self):
        """Sprawdza, czy generowanie PDFów daje status 200."""
        with warnings.catch_warnings():
            warnings.filterwarnings("ignore", category=DeprecationWarning)
            mozliwosci = [
                (t, l) for t in TagDlugosc.objects.all() for l in ["pl", "en"]
            ]
            for tag_dlugosc, lang in mozliwosci:
                szkolenie = SzkolenieFactory(tag_dlugosc=tag_dlugosc, language=lang)
                pdf_url = reverse(
                    "detail_pdf",
                    kwargs={"slug": szkolenie.slug, "language": szkolenie.language},
                )
                response = self.client.get(pdf_url)
                self.assertEqual(response.status_code, 200)


class StaticPageTestCase(ALXTestCase):
    def test_historie_absolwentow(self):
        response = self.client.get(reverse("graduates", kwargs={"language": "pl"}))
        self.assertEqual(response.status_code, 200)

    def test_historia_absolwenta(self):
        GraduateStoryFactory.create(slug="abc")
        response = self.client.get(
            reverse("graduate_story", kwargs={"language": "pl", "slug": "abc"})
        )
        self.assertEqual(response.status_code, 200)


class TagTechnologiaTestCase(ALXTestCase):
    def test_lista_szkolen_dziala(self):
        """
        Sprawdza, czy wyświetlanie listy szkoleń daje status 200.
        """
        tagi_technologia = TagTechnologiaFactory.create_batch(
            2, language="pl", opis="Jakiś opis"
        )
        tagi_technologia.extend(
            TagTechnologiaFactory.create_batch(2, language="en", opis="Description")
        )
        for tag_technologia in tagi_technologia:
            # Poniżej produkcja szkoleń i kursów z terminami i bez, żeby się co miało wyświetlić.
            for tag_dlugosc in TagDlugosc.objects.all():
                SzkolenieFactory(
                    language=tag_technologia.language, tag_dlugosc=tag_dlugosc
                )
                TerminSzkoleniaFactory(
                    szkolenie__language=tag_technologia.language,
                    szkolenie__tag_dlugosc=tag_dlugosc,
                    szkolenie__tagi_technologia=[
                        tag_technologia,
                    ],
                )

            if tag_technologia.language == "pl":
                nazwa_widoku = "index_by_technologia_pl"
            else:
                nazwa_widoku = "index_by_technologia"
            url_taga = reverse(
                nazwa_widoku,
                kwargs={
                    "language": tag_technologia.language,
                    "technologia_slug": tag_technologia.slug,
                },
            )
            response = self.client.get(url_taga)
            self.assertEqual(response.status_code, 200)

    def test_lista_trenerow_tech_taga(self):
        tag = TagTechnologiaFactory.create()

        url_taga = reverse(
            "index_by_technologia_pl",
            kwargs={"language": "pl", "technologia_slug": tag.slug},
        )
        response = self.client.get(url_taga)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context["tech_trenerzy"]), 0)

        prowadzacy = TagTechnologiaProwadzacyFactory.create_batch(
            10, tagtechnologia=tag, prowadzacy__pokazywac=True
        )
        prowadzacy[0].prowadzacy.pokazywac = False
        prowadzacy[0].prowadzacy.save()
        prowadzacy[1].prowadzacy.pokazywac = False
        prowadzacy[1].prowadzacy.save()

        # Dodajemy dane niezwiazane z naszym tagiem
        TagTechnologiaProwadzacyFactory.create_batch(20, prowadzacy__pokazywac=True)

        url_taga = reverse(
            "index_by_technologia_pl",
            kwargs={"language": "pl", "technologia_slug": tag.slug},
        )
        response = self.client.get(url_taga)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context["tech_trenerzy"]), 8)


@override_settings(
    DOMENY_DLA_JEZYKOW={"pl": "www.alx.dev", "en": "www.alx-training.co.dev"}
)
class SzkolenieTestCase(ALXTestCase):
    def test_strona_domyslnego_szkolenia_dziala(self):
        """
        Sprawdza, czy wejscie na strone szkolenia daje status 200.
        """
        szkolenie = SzkolenieFactory()
        szkolenie_url = reverse(
            "detail_pl", kwargs={"slug": szkolenie.slug, "language": szkolenie.language}
        )
        response = self.client.get(szkolenie_url)
        self.assertEqual(response.status_code, 200)

    def test_lista_najblizszych_szkolen_dziala(self):
        """
        Sprawdza, czy lista najbliższych szkoleń zwraca status 200.
        """
        TerminSzkoleniaFactory.create_batch(2)
        TerminSzkoleniaEnFactory.create_batch(2)
        urle_do_sprawdzenia = [
            reverse("najblizsze_szkolenia"),
            reverse("najblizsze_szkolenia_en"),
        ]
        for url in urle_do_sprawdzenia:
            response = self.client.get(url)
            self.assertEqual(response.status_code, 200)

    def test_strony_szkolen_dzialaja(self):
        """
        Sprawdza, czy strony ze szczegółami szkoleń (z wyłączeniem flat page’y)
        zwracają status 200.
        """
        tagi_technologia = TagTechnologiaFactory.create_batch(2)
        lista_szkolen = SzkolenieFactory.create_batch(
            5,
            aktywne=True,
            strona_z_opisem_slug=None,
            tagi_technologia=tagi_technologia,
        )
        lista_szkolen.extend(
            SzkolenieFactory.create_batch(
                5,
                aktywne=True,
                strona_z_opisem_slug="",
                tagi_technologia=tagi_technologia,
            )
        )
        for szkolenie in lista_szkolen:
            url_name = "detail_{0}".format(szkolenie.language)
            response = self.client.get(
                reverse(url_name, kwargs={"slug": szkolenie.slug})
            )
            self.assertEqual(response.status_code, 200)

    # Poniższe testy deskryptorów cen obejmują tylko cenę i cenę autoryzacji.

    def test_przeliczanie_walut_dziala(self):
        """
        Sprawdza, czy w przypadku tłumaczeń bez podanych cen ceny są generowane
        poprawnie w oparciu o obiekt bazowy.
        """
        funt = Waluta.o_nazwie("funt")
        szkolenie_base = SzkolenieFactory()
        szkolenie = SzkolenieFactory(language="en", base_translation=szkolenie_base)
        ceny_kursy_i_mnozniki = [
            (1234, 765, Decimal("5.34"), Decimal("2.24")),
            (3456, 234, Decimal("6.27"), Decimal("1.73")),
            (8295, 184, Decimal("3.5432"), Decimal("1.22")),
        ]
        for cena, cena_autoryzacji, kurs, mnoznik_ceny in ceny_kursy_i_mnozniki:
            funt.kurs_kupna = kurs
            funt.mnoznik_ceny = mnoznik_ceny
            funt.save()

            szkolenie.waluta = funt
            szkolenie.cena_bazowa = None
            szkolenie.cena_autoryzacji_bazowa = None
            szkolenie.save()

            szkolenie.base_translation.cena_autoryzacji_bazowa = cena_autoryzacji
            szkolenie.base_translation.cena_bazowa = cena
            szkolenie.base_translation.save()

            mnoznik_laczny = funt.mnoznik_ceny / funt.kurs_kupna

            # Cenę szkolenia zaokrąglamy w górę z dokładnością do 10
            self.assertEqual(
                szkolenie.cena,
                (szkolenie.base_translation.cena * mnoznik_laczny).quantize(
                    Decimal("1E1"), rounding=ROUND_UP
                ),
            )
            # Cenę autoryzacji zaokrąglamy w górę z dokładnością do 1.
            self.assertEqual(
                szkolenie.cena_autoryzacji,
                (szkolenie.base_translation.cena_autoryzacji * mnoznik_laczny).quantize(
                    Decimal("1"), rounding=ROUND_UP
                ),
            )

    def test_ustawianie_cen(self):
        """
        Sprawdza, czy ceny mogą być ustawiane za pomocą atrybutów „cena*”
        bez przyrostka „bazowa”.
        """
        szkolenie = Szkolenie(language="en")
        szkolenie.cena_bazowa = 123
        szkolenie.cena_autoryzacji_bazowa = 456
        szkolenie.cena = 789
        szkolenie.cena_autoryzacji = 54
        self.assertEqual(szkolenie.cena, 789)
        self.assertEqual(szkolenie.cena_autoryzacji, 54)
        self.assertEqual(szkolenie.cena_bazowa, 789)
        self.assertEqual(szkolenie.cena_autoryzacji_bazowa, 54)

    def test_pobieranie_cen_z_tlumaczenia(self):
        """
        Sprawdza, że w przypadku podania cen dla tłumaczenia nie jest wykonywany
        fallback do obiektu bazowego.
        """
        szkolenie_base = SzkolenieFactory()
        szkolenie = SzkolenieFactory(language="en", base_translation=szkolenie_base)
        szkolenie.cena = None
        szkolenie.cena_autoryzacji = None
        szkolenie.cena = nowa_cena = szkolenie.cena + 500
        szkolenie.cena_autoryzacji = nowa_cena_autoryzacji = (
            szkolenie.cena_autoryzacji or 0
        ) + 500
        self.assertEqual(szkolenie.cena, nowa_cena)
        self.assertEqual(szkolenie.cena_autoryzacji, nowa_cena_autoryzacji)

    def test_dokladnosc_cen_dla_szkolen_bazowych(self):
        """
        Sprawdza, że ceny szkoleń bazowych moją odpowiednio zaokrągloną
        precyzję liczb dziesiętnych.
        """
        szkolenie = SzkolenieFactory(base_translation=None)
        for atrybut in [
            "cena",
            "cena_autoryzacji",
            "cena_przed_promocja",
            "cena_w_grupie_2_os",
            "cena_indywidualnie",
        ]:
            setattr(szkolenie, atrybut, Decimal("123.45"))
            self.assertEqual(
                getattr(szkolenie, atrybut).compare_total(Decimal("123.45")), 0
            )

            setattr(szkolenie, atrybut, Decimal("123.00"))
            self.assertEqual(
                getattr(szkolenie, atrybut).compare_total(Decimal("123")), 0
            )

    def test_synchronizacja_min_grupy_dziala(self):
        """
        Sprawdza, czy w przypadku braku min_grupy jej wartość jest pobierana
        z obiektu bazowego.
        """
        szkolenie_base = SzkolenieFactory()
        szkolenie = SzkolenieFactory(language="en", base_translation=szkolenie_base)
        szkolenie.min_grupa = None
        self.assertIsNotNone(szkolenie.base_translation.min_grupa)
        self.assertIsNone(szkolenie.min_grupa_bazowa)
        self.assertEqual(szkolenie.min_grupa, szkolenie.base_translation.min_grupa)

    def test_ustawianie_min_grupy_dziala(self):
        """
        Sprawdza, czy min_grupa może być ustawiona bez przyrostka „bazowa”.
        """
        szkolenie = Szkolenie()
        nowa_min_grupa = (szkolenie.min_grupa or 0) + 123
        szkolenie.min_grupa = nowa_min_grupa
        self.assertEqual(szkolenie.min_grupa, nowa_min_grupa)
        self.assertEqual(szkolenie.min_grupa_bazowa, nowa_min_grupa)

    def test_pobieranie_min_grupy_z_tlumaczenia(self):
        """
        Sprawdza, czy w przypadku podania min_grupy dla tłumaczenia jej wartość
        nie jest pobierana z obiektu bazowego.
        """
        szkolenie_base = SzkolenieFactory()
        szkolenie = SzkolenieFactory(language="en", base_translation=szkolenie_base)
        szkolenie.min_grupa = 123
        szkolenie.base_translation.min_grupa = 456
        self.assertEqual(szkolenie.min_grupa, 123)

    def test_pobieranie_grupy_tresci_z_tagow_technologia(self):
        """
        Sprawdza, czy grupy treści są pobierane z tagów technologia.
        """
        szkolenie = SzkolenieFactory()
        szkolenie.tagi_technologia.clear()
        self.assertIsNone(szkolenie.content_group)

        tagi_technologia_do_dodania = TagTechnologiaFactory.create_batch(2)
        self.assertEqual(len(tagi_technologia_do_dodania), 2)
        for tag_technologia in tagi_technologia_do_dodania:
            tag_technologia.content_group = ContentGroupFactory()
            tag_technologia.save()
            szkolenie.tagi_technologia.add(tag_technologia)
            # Mało dokładne – sprawdzamy tylko, czy coś jest zwracane przez atrybut content_group.
            self.assertTrue(szkolenie.content_group)

    def _training_instance_as_dict(self, instance):
        data = instance.__dict__.copy()

        data["waluta"] = data["waluta_id"]
        data["tag_zawod"] = data["tag_zawod_id"]
        data["tag_dlugosc"] = data["tag_dlugosc_id"]
        data["tagi_technologia"] = [e.pk for e in instance.tagi_technologia.all()]
        data["materialy_plik"] = ""
        return data

    def test_form_materialy_plik(self):
        """
        Testujemy upload materiałów i ich poprawny zapis/usuwanie. Jest to
        ważne, gdyż ponowny upload dla tego samego obiektu nie może zmienić
        nazwy pliku (jedynie jego content), oraz ma być możliwość fizycznego
        usuwania plików z dysku.
        """

        class FakeSzkolenieAdminForm(SzkolenieAdminForm):
            def __init__(self, *args, **kwargs):
                super().__init__(*args, **kwargs)
                del self.fields["teksty_o_terminach"]
                del self.fields["waluta"]

        training = SzkolenieFactory.create(slug=uuid.uuid4().hex)
        fname = "{0}.pdf".format(training.slug)

        self.assertEqual(training.materialy_plik, None)

        post_data = self._training_instance_as_dict(training)

        form = FakeSzkolenieAdminForm(post_data, instance=training)

        self.assertTrue(form.is_valid())

        form.save()

        # Robimy upload pliku

        f = SimpleUploadedFile(fname, b"tekst 1")

        post_data["materialy_plik"] = f
        files_data = {"materialy_plik": f}

        form = FakeSzkolenieAdminForm(post_data, files_data, instance=training)

        self.assertTrue(form.is_valid())

        form.save()

        # Powinien pojawić się plik w uploads oraz w obiekcie
        fpath = os.path.join(settings.MEDIA_ROOT, "materialy", fname)

        self.assertTrue(
            os.path.exists(os.path.join(settings.MEDIA_ROOT, "materialy", fname))
        )

        training = SzkolenieFactory._meta.model.objects.get(pk=training.pk)
        self.assertTrue(training.materialy_plik.path)

        # Sprawdzamy zawartośc pliku.

        fcontent = open(fpath, "r").read()
        self.assertEqual(fcontent, "tekst 1")

        # Robimy ponownie upload - nazwa pliku nie powinna się zmienić, ale
        # zawartość już tak.

        post_data = self._training_instance_as_dict(training)

        f = SimpleUploadedFile(fname, b"tekst 2")

        post_data["materialy_plik"] = f
        files_data = {"materialy_plik": f}

        form = FakeSzkolenieAdminForm(post_data, files_data, instance=training)

        self.assertTrue(form.is_valid())

        form.save()

        # Powinien pojawić się plik w uploads oraz w obiekcie
        fpath = os.path.join(settings.MEDIA_ROOT, "materialy", fname)

        self.assertTrue(
            os.path.exists(os.path.join(settings.MEDIA_ROOT, "materialy", fname))
        )

        training = SzkolenieFactory._meta.model.objects.get(pk=training.pk)
        self.assertTrue(training.materialy_plik.path)

        # Sprawdzamy zawartośc pliku.

        fcontent = open(fpath, "r").read()
        self.assertEqual(fcontent, "tekst 2")

        # Usuwamy plik
        post_data = self._training_instance_as_dict(training)

        post_data["materialy_plik-clear"] = "on"

        form = FakeSzkolenieAdminForm(post_data, instance=training)

        self.assertTrue(form.is_valid())

        form.save()

        # Plik powinien zostać usunięty
        self.assertFalse(
            os.path.exists(os.path.join(settings.MEDIA_ROOT, "materialy", fname))
        )

        training = SzkolenieFactory._meta.model.objects.get(pk=training.pk)
        self.assertFalse(training.materialy_plik)

    def test_slug(self):
        """
        Pole slug powinno zawierac tylko znaki przyjazne adresom URL.
        """

        training = SzkolenieFactory.create(nazwa="Test łół")

        # Bledny slug
        post_data = {"slug": "ółążśźćń"}

        form = SzkolenieAdminForm(post_data, instance=training)

        self.assertIn("slug", form.errors)

        # Poprawny slug
        post_data = {"slug": "a-s-d_b-"}

        form = SzkolenieAdminForm(post_data, instance=training)
        self.assertNotIn("slug", form.errors)

        # Pusty slug - poprawny
        post_data = {"slug": ""}

        form = SzkolenieAdminForm(post_data, instance=training)
        self.assertNotIn("slug", form.errors)

    def test_tags_lanuage(self):
        """
        Tagi (zawód i technologia), powinny być w językach w jakich jest
        szkolenie.
        """

        # Przypisujemy tag_zawod w innym języku
        training = SzkolenieFactory.create()
        post_data = self._training_instance_as_dict(training)
        post_data["tag_zawod"] = TagZawodFactory(language="en").pk

        form = SzkolenieAdminForm(post_data, instance=training)
        form.is_valid()

        self.assertEqual(
            form.errors["tag_zawod"],
            ["Język taga musi być zgodny z językiem szkolenia."],
        )

        # Przypisujemy tagi_technologia w innym języku
        training = SzkolenieFactory.create()
        post_data = self._training_instance_as_dict(training)
        post_data["tagi_technologia"] = [
            TagTechnologiaFactory(language="pl").pk,
            TagTechnologiaFactory(language="en").pk,
        ]

        form = SzkolenieAdminForm(post_data, instance=training)
        form.is_valid()

        self.assertEqual(
            form.errors["tagi_technologia"],
            ["Język wszystkich tagów musi być zgodny z językiem szkolenia."],
        )

        # Wszystko zgodne z językiem szkolenia
        training = SzkolenieFactory.create()
        post_data = self._training_instance_as_dict(training)
        post_data.update(
            {
                "tag_zawod": TagZawodFactory(language="pl").pk,
                "tagi_technologia": [
                    TagTechnologiaFactory(language="pl").pk,
                    TagTechnologiaFactory(language="pl").pk,
                ],
            }
        )

        form = SzkolenieAdminForm(post_data, instance=training)
        form.is_valid()

        self.assertNotIn("tag_zawod", form.errors)
        self.assertNotIn("tagi_technologia", form.errors)

    def test_url_with_domain(self):
        """
        Testujemy metodę modelu `Szkolenie.url_with_domain`
        """

        # Język polski
        e = SzkolenieFactory.create(language="pl")

        self.assertIn("www.alx.dev/", e.url_with_domain())

        self.assertIn(e.slug, e.url_with_domain())

        # Język angielski
        e = SzkolenieFactory.create(language="en")

        self.assertIn("www.alx-training.co.dev/", e.url_with_domain())

        self.assertIn(e.slug, e.url_with_domain())

    def test_url_with_domain_suggest_schedule(self):
        """
        Testujemy metodę modelu `Szkolenie.url_with_domain_suggest_schedule`
        """

        # Język polski
        e = SzkolenieFactory.create(language="pl")

        self.assertIn("www.alx.dev/", e.url_with_domain_suggest_schedule())

        self.assertIn(e.slug, e.url_with_domain_suggest_schedule())

        # Język angielski
        e = SzkolenieFactory.create(language="en")

        self.assertIn("www.alx-training.co.dev/", e.url_with_domain_suggest_schedule())

        self.assertIn(e.slug, e.url_with_domain_suggest_schedule())

    def test_url_registration(self):
        """
        Testujemy metodę modelu `Szkolenie.url_registration`
        """

        # Język polski
        e = SzkolenieFactory.create(language="pl", slug="abc")

        self.assertEqual("www.alx.dev/zgloszenie/abc/", e.url_registration())

        # Język angielski
        e = SzkolenieFactory.create(language="en", slug="abc")

        self.assertEqual("www.alx-training.co.dev/en/order/abc/", e.url_registration())

    def test_fields_inherited_between_translations(self):
        base = SzkolenieFactory.create(language="en")
        base.zawiera_obiady = True
        base.czas_dni = 2
        base.czas_godziny = 33
        base.save()

        t = SzkolenieFactory.create(base_translation=base, czas_dni=None)
        t.slug = uuid.uuid4().hex
        t.pk = None
        t.save()

        # Te pola powinny byc dziedziczone
        self.assertEqual(t.zawiera_obiady, True)
        self.assertEqual(t.czas_dni, 2)
        self.assertEqual(t.czas_godziny, 33)

        # Edycja - brak dziedziczenia
        base.czas_dni = 22
        base.czas_godziny = 333
        base.save()

        t.czas_godziny = 44
        t.save()

        t = SzkolenieFactory._meta.model.objects.get(pk=t.pk)

        self.assertEqual(t.zawiera_obiady, True)
        self.assertEqual(t.czas_dni, 2)
        self.assertEqual(t.czas_godziny, 44)

        # Jesli wartosc juz istnieje - nie nadpisuj jej
        t = SzkolenieFactory.create(
            czas_godziny=55, base_translation=base, czas_dni=None
        )
        t.pk = None
        t.slug = uuid.uuid4().hex
        t.save()

        self.assertEqual(t.zawiera_obiady, True)
        self.assertEqual(t.czas_dni, 22)
        self.assertEqual(t.czas_godziny, 55)

    def test_maksymalna_liczba_osob_dla_grup_otwartych(self):
        e = KursFactory.create()
        self.assertEqual(e.maksymalna_liczba_osob_dla_grup_otwartych(), 14)

        e = SzkolenieFactory.create(
            szkolenie_nadrzedne=KursFactory.create(),
            grupa_zaszeregowania=GrupaZaszeregowaniaFactory.create(nazwa="5:test"),
        )
        self.assertEqual(e.maksymalna_liczba_osob_dla_grup_otwartych(), 14)

        e = SzkolenieFactory.create()
        self.assertEqual(e.maksymalna_liczba_osob_dla_grup_otwartych(), 8)

        for i in [1, 2, 3, 4]:
            e = SzkolenieFactory.create(
                grupa_zaszeregowania=GrupaZaszeregowaniaFactory.create(
                    nazwa="{0}:test".format(i)
                )
            )
            self.assertEqual(e.maksymalna_liczba_osob_dla_grup_otwartych(), 14)

        e = SzkolenieFactory.create(
            grupa_zaszeregowania=GrupaZaszeregowaniaFactory.create(nazwa="5:test")
        )
        self.assertEqual(e.maksymalna_liczba_osob_dla_grup_otwartych(), 10)

        e = SzkolenieFactory.create(
            grupa_zaszeregowania=GrupaZaszeregowaniaFactory.create(nazwa="6:test")
        )
        self.assertEqual(e.maksymalna_liczba_osob_dla_grup_otwartych(), 8)

    def test_szkolenie_nadrzedne(self):
        # Testujemy poprawność nadrzędności szkoleń
        s1 = SzkolenieFactory.create()

        form = SzkolenieAdminForm({"szkolenie_nadrzedne": s1.pk})

        form.is_valid()

        self.assertEqual(
            form.errors["podzbior_dni"],
            ["Jeśli wybrano szkolenie nadrzędne musisz podać podzbiór dni."],
        )

        form = SzkolenieAdminForm(
            {"szkolenie_nadrzedne": s1.pk, "podzbior_dni": "1,2,3"}
        )

        form.is_valid()

        self.assertFalse("podzbior_dni" in form.errors)
        self.assertFalse("szkolenie_nadrzedne" in form.errors)

        # Próbujemy dziedziczyć ze szkolenie, które już jest podzbiorem

        s2 = SzkolenieFactory.create()
        s3 = SzkolenieFactory.create(szkolenie_nadrzedne=s2)

        form = SzkolenieAdminForm(
            {"szkolenie_nadrzedne": s3.pk, "podzbior_dni": "1,2,3"}
        )

        form.is_valid()

        self.assertEqual(
            form.errors["szkolenie_nadrzedne"],
            [
                "Wybierz poprawną wartość. Podana nie jest jednym z dostępnych "
                "wyborów."
            ],
        )

        # Probujemy uczynić szkolenie pozbiorem, mimo, że jest już nadrzędnym
        # dla innych

        s4 = SzkolenieFactory.create()
        SzkolenieFactory.create(szkolenie_nadrzedne=s4)
        s5 = SzkolenieFactory.create()

        post_data = self._training_instance_as_dict(s4)
        post_data["szkolenie_nadrzedne"] = s5.pk
        post_data["podzbior_dni"] = "1,2,3"

        form = SzkolenieAdminForm(post_data, instance=s4)

        form.is_valid()

        self.assertEqual(
            form.errors["szkolenie_nadrzedne"],
            [
                "To szkolenie jest już nadrzędne dla innych i nie możesz "
                "wybrać żadnego szkolenia nadrzędnego."
            ],
        )

        # Probujemy wybrac "samego sibie"
        post_data = self._training_instance_as_dict(s4)
        post_data["szkolenie_nadrzedne"] = s4.pk
        post_data["podzbior_dni"] = "1,2,3"

        form = SzkolenieAdminForm(post_data, instance=s4)

        form.is_valid()

        self.assertEqual(
            form.errors["szkolenie_nadrzedne"],
            [
                "Wybierz poprawną wartość. Podana nie jest jednym z dostępnych "
                "wyborów."
            ],
        )

    def test_wiele_rat(self):
        s1 = SzkolenieFactory.create()

        post_data = self._training_instance_as_dict(s1)
        post_data["wiele_rat"] = True

        form = SzkolenieAdminForm(post_data, instance=s1)

        form.is_valid()

        self.assertEqual(
            form.errors["wiele_rat"],
            ["Ta wartość może być zaznaczona tylko dla Kursów Zawodowych."],
        )

        # Jak zmienimy na kurs-zawodowy to bedzie ok
        post_data["tag_dlugosc"] = TagDlugosc.objects.get(slug="kurs-zawodowy").pk

        form = SzkolenieAdminForm(post_data, instance=s1)

        form.is_valid()

        self.assertNotIn("wiele_rat", form.errors)


class CaptchaMixin(object):
    """Mixin udostępniający możliwość wpisania poprawnej captchy."""

    def pobierz_captche(self):
        """
        Zwraca poprawną captchę dla aktualnie wypełnianego formularza,
        jako listę wartości formularza.
        """
        try:
            captcha = Captcha.objects.order_by("-id")[0]
        except IndexError:
            captcha = Captcha()
            captcha.save()
        return [captcha.id, captcha.text]


@override_settings(
    CENA_NETTO_DRUKOWANEGO_CERTYFIKATU={
        "pl": 37,
        "en": 19,
    }
)
class FormularzZgloszeniowyTestCase(ALXTestCase, CaptchaMixin):
    longMessage = True

    # Słownik z przykładowymi, neutralnymi danymi, który będziemy używać
    # jako bazę dla testowanych przypadków.
    przykladowe_dane = {
        "prywatny": 0,
        "imie_nazwisko": "Jan Onufry Zagłoba",
        "email": "<EMAIL>",
        "telefon": "123 456 789",
        "adres": "Wytwórnia miodów pitnych",
        "miejscowosc_kod": "Zbaraż",
        "faktura_firma": "Chorągiew księcia Jeremiego",
        "faktura_adres": "Zamek książęcy",
        "faktura_miejscowosc_kod": "Zbaraż",
        "faktura_nip": "123-456-32-18",
        "faktura_vat_id": "GB999 9999 73",
        "czy_grupa": "1",  # 1 oznacza pojedynczą osobę, 0 oznacza grupę…
    }

    def terminy_do_testu(
        self,
        ile=1,
        jezyki=[
            "pl",
        ],
        **kwargs,
    ):
        terminy = TerminSzkoleniaFactory.create_batch(ile, **kwargs)
        if "en" in jezyki:
            terminy_en = TerminSzkoleniaEnFactory.create_batch(ile, **kwargs)
            terminy.extend(terminy_en)
        return terminy

    def test_strona_z_formularzem_zgloszeniowym_dziala(self):
        """
        Sprawdza, czy dla widocznych szkoleń strona z formularzem
        zgłoszeniowym zwraca status 200.
        """
        terminy = []
        szkolenia = []
        for tag_dlugosc in TagDlugosc.objects.all():
            t = self.terminy_do_testu(
                jezyki=["pl", "en"], szkolenie__tag_dlugosc=tag_dlugosc
            )
            terminy.extend(t)
            szkolenia.append(SzkolenieFactory(tag_dlugosc=tag_dlugosc))
            szkolenia.append(SzkolenieFactory(tag_dlugosc=tag_dlugosc, language="en"))
        szkolenia_z_terminami = [termin.szkolenie for termin in terminy]
        szkolenia.extend(szkolenia_z_terminami)

        for szkolenie in szkolenia:
            url_zgloszenia = reverse(
                "zgloszenie",
                kwargs={"slug": szkolenie.slug, "language": szkolenie.language},
            )
            response = self.client.get(url_zgloszenia)
            self.assertEqual(response.status_code, 200)

    def pobierz_przykladowe_dane(self):
        """
        Zwraca słownik z przykładowymi danymi do wypełnienia formularza
        zgłoszeniowego. Nie zawiera konkretnego terminu szkolenia.
        """
        return self.przykladowe_dane

    def pobierz_termin(self, q_filter=None):
        """
        Zwraca termin, który może być wypełniony w formularzu zgłoszeniowym.
        Termin jest filtrowany za pomocą opcjonalnego obiektu Q.
        """
        if not q_filter:
            q_filter = Q()
        return TerminSzkolenia.objects.filter(
            termin__gte=datetime.date.today(), zamkniete=False
        ).filter(q_filter)[0]

    def dane_formularza_dla_terminu(self, termin, **kwargs):
        """
        Zwraca słownik z danymi potrzebnymi do wysłania formularza
        zgłoszeniowego na konkretny termin. Przykładowe dane
        są nadpisywane przez argumenty podane jako słowa kluczowe.
        """
        dane = dict(
            self.pobierz_przykladowe_dane(),
            captcha=self.pobierz_captche(),
            termin=str(termin.id),
        )
        if termin.szkolenie.tag_dlugosc.slug == "szkolenie":
            dane["za_kurs_zaplace"] = 5
        else:
            dane["za_kurs_zaplace"] = 1

        if termin.szkolenie.language == "pl":
            dane["akceptuje_regulamin"] = "1"

        # Pola „chce_obiady” i „chce_autoryzacje” są wymagane nawet,
        # jeśli uczestnik ich nie chce. Ustawiamy domyślne wartości.
        if termin.obiady == "obiady-opcjonalne":
            dane["chce_obiady"] = "0"
        if termin.autoryzacja():
            dane["chce_autoryzacje"] = "0"

        return dict(dane, **kwargs)

    def znajdz_uczestnika(self, dane_formularza):
        """
        Znajduje uczestnika, który wypełnił formularz zadanymi danymi.
        W przypadku, gdy uczestnik nie istnieje, zwraca None.
        """
        dane_do_przeszukania = ["imie_nazwisko", "email", "termin"]
        try:
            return Uczestnik.objects.filter(
                **dict(
                    (klucz, dane_formularza[klucz]) for klucz in dane_do_przeszukania
                )
            ).get()
        except Uczestnik.DoesNotExist:
            return None

    def wyslij_formularz(self, termin=None, follow=False, **kwargs):
        """
        Wysyła formularz zgłoszeniowy i zwraca otrzymaną odpowiedź oraz ewentualnie
        zapisanego uczestnika. W przypadku niepodania terminu, pobierany jest przykładowy termin.
        Formularz zawiera przykładowe dane nadpisane przez argumenty podane jako słowa kluczowe.
        """
        if not termin:
            termin = TerminSzkoleniaFactory()
        url_zgloszenia = reverse(
            "zgloszenie",
            kwargs={
                "slug": termin.szkolenie.slug,
                "language": termin.szkolenie.language,
            },
        )
        dane_formularza = self.dane_formularza_dla_terminu(termin, **kwargs)

        response = self.client.post(url_zgloszenia, dane_formularza, follow=follow)
        return response, self.znajdz_uczestnika(dane_formularza)

    def test_kod_rabatowy_30days_sie_zapisuje(self):
        termin = TerminSzkoleniaFactory(
            szkolenie=KursFactory.create(),
            termin=datetime.date.today() + datetime.timedelta(days=30),
        )

        response, uczestnik = self.wyslij_formularz(termin)

        self.assertTrue(uczestnik.discount_code)
        self.assertEqual(uczestnik.discount_code.discount, 3)
        self.assertEqual(uczestnik.discount_code.limit, 1)
        self.assertEqual(uczestnik.discount_code.source, "30days")

    def test_kod_rabatowy_30days_sie_nie_zapisuje_gdy_mniej_jak_30_dni(self):
        termin = TerminSzkoleniaFactory(
            szkolenie=KursFactory.create(),
            termin=datetime.date.today() + datetime.timedelta(days=29),
        )

        response, uczestnik = self.wyslij_formularz(termin)

        self.assertFalse(uczestnik.discount_code)

    def test_kod_rabatowy_30days_sie_nie_zapisuje_gdy_szkolenie(self):
        termin = TerminSzkoleniaFactory(
            szkolenie=SzkolenieFactory.create(),
            termin=datetime.date.today() + datetime.timedelta(days=30),
        )

        response, uczestnik = self.wyslij_formularz(termin)

        self.assertFalse(uczestnik.discount_code)

    def test_kod_rabatowy_30days_sie_nie_zapisuje_gdy_raty(self):
        termin = TerminSzkoleniaFactory(
            szkolenie=KursFactory.create(),
            termin=datetime.date.today() + datetime.timedelta(days=31),
        )

        response, uczestnik = self.wyslij_formularz(
            termin, za_kurs_zaplace=4, uwagi_klienta="test"
        )

        self.assertFalse(uczestnik.discount_code)

    def test_kod_rabatowy_30days_sie_nie_zapisuje_gdy_inny_kod(self):
        discount_code = DiscountCodeFactory.create(code="AbC", discount=45)

        termin = TerminSzkoleniaFactory(
            szkolenie=KursFactory.create(),
            termin=datetime.date.today() + datetime.timedelta(days=31),
        )
        response, uczestnik = self.wyslij_formularz(termin, discount_coupon="abc")

        self.assertTrue(uczestnik.discount_code)
        self.assertEqual(uczestnik.discount_code, discount_code)
        self.assertEqual(uczestnik.discount_code.discount, 45)

    def test_kod_rabatowy_sie_zapisuje(self):
        discount_code = DiscountCodeFactory.create(code="AbC")

        termin = TerminSzkoleniaFactory()
        response, uczestnik = self.wyslij_formularz(termin, discount_coupon="abc")

        self.assertTrue(uczestnik)
        self.assertEqual(uczestnik.discount_code, discount_code)

    def test_kod_rabatowy_wielorazowy_sie_zapisuje(self):
        discount_code = DiscountCodeFactory.create(code="AbC", limit=10)
        UczestnikFactory.create_batch(3, discount_code=discount_code)

        termin = TerminSzkoleniaFactory()
        response, uczestnik = self.wyslij_formularz(termin, discount_coupon="abc")

        self.assertTrue(uczestnik)
        self.assertEqual(uczestnik.discount_code, discount_code)

    def test_kod_rabatowy_sie_nie_zapisuje_gdy_raty(self):
        DiscountCodeFactory.create(code="AbC")

        termin = TerminSzkoleniaFactory()
        response, uczestnik = self.wyslij_formularz(
            termin,
            discount_coupon="abc",
            za_kurs_zaplace=10,
            raty_panstwo="inny",
            raty_nazwa_dokumentu="ABC",
            raty_numer_dokumentu="XYZ",
        )

        self.assertFalse(uczestnik)

        form = response.context["form"]
        self.assertIn("discount_coupon", form.errors)

    def test_kod_rabatowy_nie_istnieje(self):
        termin = TerminSzkoleniaFactory()
        response, uczestnik = self.wyslij_formularz(termin, discount_coupon="abc")

        self.assertFalse(uczestnik)

        form = response.context["form"]
        self.assertIn("discount_coupon", form.errors)

    def test_kod_rabatowy_zostal_wykorzystany(self):
        discount_code = DiscountCodeFactory.create(code="abc", limit=1)
        UczestnikFactory.create(discount_code=discount_code)

        termin = TerminSzkoleniaFactory()
        response, uczestnik = self.wyslij_formularz(termin, discount_coupon="abc")

        self.assertFalse(uczestnik)

        form = response.context["form"]
        self.assertIn("discount_coupon", form.errors)

    def test_kod_rabatowy_jest_niekatywny(self):
        DiscountCodeFactory.create(code="abc", limit=1, is_active=False)

        termin = TerminSzkoleniaFactory()
        response, uczestnik = self.wyslij_formularz(termin, discount_coupon="abc")

        self.assertFalse(uczestnik)

        form = response.context["form"]
        self.assertIn("discount_coupon", form.errors)

    def test_kod_rabatowy_przeterminowany(self):
        DiscountCodeFactory.create(code="abc", available_to=datetime.date(2012, 1, 1))

        termin = TerminSzkoleniaFactory()
        response, uczestnik = self.wyslij_formularz(termin, discount_coupon="abc")

        self.assertFalse(uczestnik)

        form = response.context["form"]
        self.assertIn("discount_coupon", form.errors)

    def test_email_do_celow_ksiegowych_sie_zapisuje(self):
        termin = TerminSzkoleniaFactory()
        response, uczestnik = self.wyslij_formularz(
            termin, email_ksiegowosc="<EMAIL>"
        )
        self.assertTrue(uczestnik)
        self.assertEqual(uczestnik.email_ksiegowosc, "<EMAIL>")

    def test_wiek_i_dodatkowe_pytanie_ukryte(self):
        termin = TerminSzkoleniaFactory(
            szkolenie__dodatkowe_pytanie_o_wiek="hidden",
            szkolenie__dodatkowe_pytanie_do_uczestnika_widocznosc="hidden",
        )
        response, uczestnik = self.wyslij_formularz(
            termin,
            wiek_uczestnika="16",
            odpowiedz_na_dodatkowe_pytanie="abc",
        )
        self.assertTrue(uczestnik)
        self.assertIsNone(uczestnik.wiek_uczestnika)
        self.assertEqual(uczestnik.odpowiedz_na_dodatkowe_pytanie, "")

    def test_wiek_i_dodatkowe_pytanie_sa_opcjonalne(self):
        termin = TerminSzkoleniaFactory(
            szkolenie__dodatkowe_pytanie_do_uczestnika="???",
            szkolenie__dodatkowe_pytanie_o_wiek="optional",
            szkolenie__dodatkowe_pytanie_do_uczestnika_widocznosc="optional",
        )
        response, uczestnik = self.wyslij_formularz(termin)
        self.assertTrue(uczestnik)
        self.assertIsNone(uczestnik.wiek_uczestnika)
        self.assertEqual(uczestnik.odpowiedz_na_dodatkowe_pytanie, "")

    def test_wiek_i_dodatkowe_pytanie_sa_opcjonalne_i_przeslane(self):
        termin = TerminSzkoleniaFactory(
            szkolenie__dodatkowe_pytanie_do_uczestnika="???",
            szkolenie__dodatkowe_pytanie_o_wiek="optional",
            szkolenie__dodatkowe_pytanie_do_uczestnika_widocznosc="optional",
        )
        response, uczestnik = self.wyslij_formularz(
            termin,
            wiek_uczestnika="16",
            odpowiedz_na_dodatkowe_pytanie="abc",
        )
        self.assertTrue(uczestnik)
        self.assertEqual(uczestnik.wiek_uczestnika, 16)
        self.assertEqual(uczestnik.odpowiedz_na_dodatkowe_pytanie, "abc")

    def test_wiek_i_dodatkowe_pytanie_sa_wymagane(self):
        termin = TerminSzkoleniaFactory(
            szkolenie__dodatkowe_pytanie_do_uczestnika="???",
            szkolenie__dodatkowe_pytanie_o_wiek="required",
            szkolenie__dodatkowe_pytanie_do_uczestnika_widocznosc="required",
        )
        response, uczestnik = self.wyslij_formularz(termin)

        self.assertFalse(uczestnik)

        form = response.context["form"]
        self.assertIn("wiek_uczestnika", form.errors)
        self.assertIn("odpowiedz_na_dodatkowe_pytanie", form.errors)

        response, uczestnik = self.wyslij_formularz(
            termin,
            wiek_uczestnika="88",
            odpowiedz_na_dodatkowe_pytanie="ółź",
        )
        self.assertTrue(uczestnik)
        self.assertEqual(uczestnik.wiek_uczestnika, 88)
        self.assertEqual(uczestnik.odpowiedz_na_dodatkowe_pytanie, "ółź")

    def test_cena_obiadow_jest_doliczana_dla_pojedynczych_osob(self):
        terminy = self.terminy_do_testu(
            jezyki=["pl", "en"], obiady="obiady-opcjonalne", cena_obiadu=20
        )
        for termin in terminy:
            response, uczestnik = self.wyslij_formularz(
                termin,
                czas_dni__gt=1,
                chce_obiady="1",
                czy_grupa="1",
                uczestnik_wieloosobowy_ilosc_osob="",
            )
            self.assertTrue(uczestnik)
            oczekiwana_cena = (
                termin.szkolenie.cena + len(list(termin.daty())) * termin.cena_obiadu
            )
            self.assertEqual(uczestnik.kwota_do_zaplaty, oczekiwana_cena)

    def test_cena_obiadow_jest_doliczana_dla_zgloszen_wieloosobowych(self):
        terminy = self.terminy_do_testu(
            jezyki=["pl", "en"],
            obiady="obiady-opcjonalne",
            cena_obiadu=20,
            szkolenie__czas_dni=4,
        )
        for termin in terminy:
            response, uczestnik = self.wyslij_formularz(
                termin,
                chce_obiady="1",
                uczestnik_wieloosobowy_ilosc_osob="5",
                czy_grupa="0",
            )
            self.assertTrue(uczestnik)
            oczekiwana_cena = 5 * (
                termin.szkolenie.cena + len(list(termin.daty())) * termin.cena_obiadu
            )
            self.assertEqual(uczestnik.kwota_do_zaplaty, oczekiwana_cena)

    def test_bledny_wybor_gdy_brak_firmy(self):
        termin = TerminSzkoleniaFactory(
            szkolenie__mozliwa_rejestracja_jako_firma=False,
        )
        response, uczestnik = self.wyslij_formularz(termin, prywatny="0")

        self.assertFalse(uczestnik)

        form = response.context["form"]
        self.assertIn("prywatny", form.errors)

    def test_kod_rabatowy_jest_uwzgledniany_w_cenie_dla_szkolenia(self):
        DiscountCodeFactory.create(code="AbC", discount=Decimal(10))

        termin = TerminSzkoleniaFactory(szkolenie__cena_bazowa=Decimal(100))
        response, uczestnik = self.wyslij_formularz(termin, discount_coupon="abc")

        self.assertTrue(uczestnik)
        self.assertEqual(uczestnik.kwota_do_zaplaty, Decimal(90))

    def test_kod_rabatowy_jest_uwzgledniany_w_cenie_dla_kursu_gdy_prywatny(self):
        tag_dlugosc = TagDlugosc.objects.get(slug="kurs-zawodowy")
        kurs = SzkolenieFactory.create(
            tag_dlugosc=tag_dlugosc, cena_bazowa=Decimal(100)
        )

        DiscountCodeFactory.create(code="AbC", discount=Decimal(10))

        termin = TerminSzkoleniaFactory(szkolenie=kurs)
        response, uczestnik = self.wyslij_formularz(
            termin, discount_coupon="abc", prywatny="1"
        )

        self.assertTrue(uczestnik)
        self.assertEqual(uczestnik.kwota_do_zaplaty, Decimal("73.17"))

    def test_cena_autoryzacji_jest_doliczana_dla_pojedynczych_osob(self):
        autoryzacja = AutoryzacjaFactory.create()
        terminy = self.terminy_do_testu(
            jezyki=["pl", "en"],
            autoryzacja_aktywna=True,
            szkolenie__autoryzacja=autoryzacja,
            szkolenie__cena_autoryzacji_bazowa=5,
        )
        for termin in terminy:
            response, uczestnik = self.wyslij_formularz(
                termin,
                chce_autoryzacje="1",
                czy_grupa="1",
            )
            self.assertTrue(uczestnik)
            oczekiwana_cena = termin.szkolenie.cena + termin.szkolenie.cena_autoryzacji
            self.assertEqual(uczestnik.kwota_do_zaplaty, oczekiwana_cena)

    def test_cena_autoryzacji_jest_doliczana_dla_zgloszen_wieloosobowych(self):
        autoryzacja = AutoryzacjaFactory.create()
        terminy = self.terminy_do_testu(
            jezyki=["pl", "en"],
            autoryzacja_aktywna=True,
            szkolenie__autoryzacja=autoryzacja,
            szkolenie__cena_autoryzacji_bazowa=5,
        )
        for termin in terminy:
            response, uczestnik = self.wyslij_formularz(
                termin,
                chce_autoryzacje="1",
                czy_grupa="0",
                uczestnik_wieloosobowy_ilosc_osob="5",
            )
            self.assertTrue(uczestnik)
            oczekiwana_cena = 5 * (
                termin.szkolenie.cena + termin.szkolenie.cena_autoryzacji
            )
            self.assertEqual(uczestnik.kwota_do_zaplaty, oczekiwana_cena)

    def test_obiekt_zgloszenia_jest_zapisany(self):
        termin = TerminSzkoleniaFactory()
        response, uczestnik = self.wyslij_formularz(termin)
        self.assertTrue(uczestnik.zgloszenie_form)
        self.assertEqual(
            uczestnik.zgloszenie_form.imie_nazwisko, uczestnik.imie_nazwisko
        )

    def test_waluta_jest_ustawiana(self):
        """
        Sprawdza, czy waluta szkolenia jest przypisywana do uczestnika.
        """
        for termin in [TerminSzkoleniaFactory(), TerminSzkoleniaEnFactory()]:
            response, uczestnik = self.wyslij_formularz(termin)
            self.assertTrue(uczestnik)
            self.assertEqual(uczestnik.waluta, termin.szkolenie.waluta)

    def test_klient_prywatny_polski_ma_niezerowy_vat(self):
        """
        Sprawdza, czy dla klienta prywatnego, polskiego, ustawiany jest VAT.
        """
        termin = TerminSzkoleniaFactory()
        response, uczestnik = self.wyslij_formularz(termin, prywatny="1")
        self.assertEqual(uczestnik.stawka_vat, Decimal("0.23"))

    def test_klient_prywatny_polski_z_akredytacja_ma_zerowy_vat(self):
        """
        Sprawdza, czy dla klienta prywatnego, polskiego, w lokliazacji
        z akredytacją MEN ustawiany jest zerowy VAT.
        """
        szkolenie = SzkolenieFactory()
        lokalizacja = LokalizacjaFactory()
        szkolenie.akredytacje.add(lokalizacja)
        termin = TerminSzkoleniaFactory(
            szkolenie=szkolenie,
            lokalizacja=lokalizacja,
        )
        response, uczestnik = self.wyslij_formularz(termin, prywatny="1")
        self.assertEqual(uczestnik.stawka_vat, 0)

    def test_klient_prywatny_angielski_ma_zerowy_vat(self):
        """
        Sprawdza, czy dla klienta prywatnego, angielskiego ustawiany jest zerowy VAT.
        """
        termin = TerminSzkoleniaEnFactory()
        response, uczestnik = self.wyslij_formularz(termin, prywatny="1")
        self.assertTrue(uczestnik)
        self.assertEqual(uczestnik.stawka_vat, 0)

    def test_klient_firmowy_polski_prywatny_ma_niezerowy_vat(self):
        """
        Sprawdza, czy klient firmowy niebędący podmiotem publicznym, zapisany przez
        polski formularz, ma niezerowy VAT.
        """
        termin = TerminSzkoleniaFactory()
        response, uczestnik = self.wyslij_formularz(
            termin,
            prywatny="0",
            podmiot_publiczny="",
            faktura_nip="123-456-32-18",
            faktura_vat_id="",
        )
        self.assertEqual(uczestnik.stawka_vat, Decimal("0.23"))

    def test_klient_firmowy_polski_publiczny_ma_zerowy_vat(self):
        """
        Sprawdza, czy klient firmowy będący podmiotem publicznym,
        zapisany przez polski formularz, ma zerowy VAT.
        """
        termin = TerminSzkoleniaFactory()
        response, uczestnik = self.wyslij_formularz(
            termin,
            prywatny="0",
            podmiot_publiczny="1",
            faktura_nip="123-456-32-18",
            faktura_vat_id="",
        )
        self.assertEqual(uczestnik.stawka_vat, 0)

    def test_klient_firmowy_angielski_ma_zerowy_vat(self):
        """
        Sprawdza, czy klient zagraniczny na angielskiej stronie ma zerowy VAT.
        """
        termin = TerminSzkoleniaEnFactory()
        response, uczestnik = self.wyslij_formularz(
            termin,
            prywatny="0",
            faktura_nip="",
            faktura_vat_id="GB999 9999 73",
        )
        self.assertTrue(uczestnik)
        self.assertEqual(uczestnik.stawka_vat, 0)

    def test_zgloszenie_referer_dla_newslettera(self):
        """
        Sprawdza czy po wysłaniu formularza zgłoszeniowego z zapisaniem
        na newsletter pojawił się odbiorca z refererem i danymi.
        """
        termin = TerminSzkoleniaFactory()
        dane = self.dane_formularza_dla_terminu(termin)
        response, uczestnik = self.wyslij_formularz(
            termin,
            chce_zapisac_sie_na_newsletter=True,
        )
        odbiorca = Odbiorca.objects.get(email=dane["email"])
        self.assertTrue(odbiorca, "nie utworzył się odbiorca newslettera")
        self.assertIn("http", odbiorca.referer, "nie utworzył się referer odbiorcy")

    def test_przypisanie_uczestnika_przy_zapisaniu_na_newsletter(self):
        """
        Sprawdzić, czy do odbiorcy newslettera jest przypisywany
        uczestnik.
        """
        response, uczestnik = self.wyslij_formularz(
            chce_zapisac_sie_na_newsletter="1",
        )
        odbiorca = Odbiorca.objects.get(email=uczestnik.email)
        self.assertEqual(odbiorca.uczestnik, uczestnik)

    def test_przypisanie_danych_przy_zapisaniu_na_newsletter(self):
        """
        Sprawdzić, czy do odbiorcy newslettera trafiają telefon i firma.
        """
        response, uczestnik = self.wyslij_formularz(
            chce_zapisac_sie_na_newsletter="1",
            telefon="123 456 789",
            email="<EMAIL>",
            prywatny="0",
            faktura_firma="Chorągiew księcia Jeremiego",
            faktura_adres="Zamek książęcy",
            faktura_miejscowosc_kod="Zbaraż",
            faktura_nip="123-456-32-18",
            faktura_vat_id="GB999 9999 73",
        )
        odbiorca = Odbiorca.objects.get(email=uczestnik.email)
        self.assertEqual(odbiorca.firma, "Chorągiew księcia Jeremiego")
        self.assertEqual(odbiorca.telefon, "123 456 789")

    def test_weryfikacja_formatu_numeru_nip(self):
        """
        Sprawdza, czy format NIP jest poprawny.
        """

        # Bledny NIP
        termin = TerminSzkoleniaFactory()
        response, uczestnik = self.wyslij_formularz(
            termin,
            prywatny="0",
            podmiot_publiczny="",
            faktura_nip="123-456-32 18",
            faktura_vat_id="",
        )

        self.assertFalse(uczestnik)

        form = response.context["form"]
        self.assertIn("faktura_nip", form.errors)

        # Poprawny NIP
        termin = TerminSzkoleniaFactory()
        response, uczestnik = self.wyslij_formularz(
            termin,
            prywatny="0",
            podmiot_publiczny="",
            faktura_nip="123-456-32-18",
            faktura_vat_id="",
        )

        self.assertTrue(uczestnik)
        self.assertEqual(uczestnik.faktura_nip, "123-456-32-18")

    def test_zgloszenie_na_kurs_potencjalnego_chetnego(self):
        """
        Sprawdź, czy potencjalny chetny może zapisać się na szkolenie i czy
        zostanie wysłana odpowiednia informacja.
        """

        mail.outbox = []

        tag_kurs = TagDlugosc.objects.get(slug="kurs-zawodowy")
        tag_szkolenie = TagDlugosc.objects.get(slug="szkolenie")
        terminy = self.terminy_do_testu(
            jezyki=["pl", "en"], szkolenie__tag_dlugosc=tag_kurs
        )
        terminy_sz = self.terminy_do_testu(
            jezyki=["pl", "en"], szkolenie__tag_dlugosc=tag_szkolenie
        )
        terminy.extend(terminy_sz)

        for termin in terminy:
            url_zgloszenia = reverse(
                "zgloszenie",
                kwargs={
                    "slug": termin.szkolenie.slug,
                    "language": termin.szkolenie.language,
                },
            )
            potecjalny_chetny = PotencjalnyChetnyFactory(
                imie_nazwisko="Jan Onufry Zagłoba",
                firma="FirmaJanaZagloby",
                szkolenie=termin.szkolenie,
            )
            platnosc = 1 if termin.szkolenie.tag_dlugosc.slug == "kurs-zawodowy" else 5
            form_data = {
                "termin": termin.id,
                "za_kurs_zaplace": platnosc,
                "chce_obiady": 0,
                "akceptuje_regulamin": 1,
                "imie_nazwisko": "Jan Onufry Zagłoba",
                "czy_grupa": 1,
                "prywatny": 1,
                "email": "<EMAIL>",
                "telefon": "123 456 789",
                "adres": "Wytwórnia miodów pitnych",
                "miejscowosc_kod": "Zbaraż",
                "captcha": self.pobierz_captche(),
            }
            response = self.client.post(url_zgloszenia, form_data)
            zgloszenie = Zgloszenie.objects.order_by("-id")[0]
            url_podziekowania = reverse(
                "dziekujemy_za_zgloszenie",
                kwargs={
                    "token": zgloszenie.token,
                    "language": termin.szkolenie.language,
                },
            )

            self.assertRedirects(response, url_podziekowania)
            self.assertIn(potecjalny_chetny.firma, mail.outbox[0].body)

    def test_informacje_o_ratach_w_podsumowaniu_zgloszenia(self):
        tag_kurs = TagDlugosc.objects.get(slug="kurs-zawodowy")
        tag_szkolenie = TagDlugosc.objects.get(slug="szkolenie")
        terminy = self.terminy_do_testu(
            jezyki=["pl"], szkolenie__tag_dlugosc=tag_kurs, szkolenie__wiele_rat=True
        )
        terminy_sz = self.terminy_do_testu(
            jezyki=["pl"],
            szkolenie__tag_dlugosc=tag_szkolenie,
            szkolenie__wiele_rat=True,
        )
        terminy.extend(terminy_sz)

        for termin in terminy:
            url_zgloszenia = reverse(
                "zgloszenie",
                kwargs={
                    "slug": termin.szkolenie.slug,
                    "language": termin.szkolenie.language,
                },
            )
            platnosc = 10 if termin.szkolenie.tag_dlugosc.slug == "kurs-zawodowy" else 5
            form_data = {
                "termin": termin.id,
                "za_kurs_zaplace": platnosc,
                "chce_obiady": 0,
                "akceptuje_regulamin": 1,
                "imie_nazwisko": "Jan Onufry Zagłoba",
                "czy_grupa": 1,
                "prywatny": 1,
                "email": "<EMAIL>",
                "telefon": "123 456 789",
                "adres": "Wytwórnia miodów pitnych",
                "miejscowosc_kod": "Zbaraż",
                "captcha": self.pobierz_captche(),
            }

            if platnosc == 10:
                form_data["raty_panstwo"] = "polska"
                form_data["raty_numer_dokumentu"] = "aaa111111"
                form_data["raty_pesel"] = "***********"

            response = self.client.post(url_zgloszenia, form_data, follow=True)

            if platnosc == 10:
                self.assertIn(
                    "Numer dowodu osobistego", response.content.decode("utf-8")
                )
                self.assertIn("PESEL", response.content.decode("utf-8"))
            else:
                self.assertNotIn(
                    "Numer dowodu osobistego", response.content.decode("utf-8")
                )
                self.assertNotIn("PESEL", response.content.decode("utf-8"))

    def test_zgloszenie_dziekujemy_jest_dobry_tekst_o_placeniu(self):
        """
        Sprawdza czy w podziękowaniu pojawia się tekst o formie płatności. Niby sprawdza tylko
        tłumaczenie na angielską wersję, ale jak jest angielska, to jest w ogóle. Też ważne.
        """
        for tag_dlugosc_slug in ["szkolenie", "kurs-zawodowy"]:
            tag_dlugosc = TagDlugosc.objects.get(slug=tag_dlugosc_slug)
            szkolenie = SzkolenieFactory(
                language="en", tag_dlugosc=tag_dlugosc, aktywne=True
            )
            termin = TerminSzkoleniaFactory(szkolenie=szkolenie)
            zgloszenie = ZgloszenieFactory(cena=Decimal("123.56"), termin=termin)
            UczestnikFactory.create(zgloszenie_form=zgloszenie)
            url_podziekowania = reverse(
                "dziekujemy_za_zgloszenie",
                kwargs={"token": zgloszenie.token, "language": "en"},
            )
            response = self.client.get(url_podziekowania)
            msg = "{0}: {1}".format(szkolenie.id, szkolenie.nazwa)
            if szkolenie.tag_dlugosc.slug == "kurs-zawodowy":
                expect = "For the course I will pay:"
            else:
                expect = "For the training I will pay:"
            self.assertContains(response, expect, 1, 200, msg)

    def test_zgloszenie_dziekujemy_aktywne(self):
        zgloszenie = ZgloszenieFactory()
        UczestnikFactory.create(zgloszenie_form=zgloszenie)
        url_podziekowania = reverse(
            "dziekujemy_za_zgloszenie",
            kwargs={"token": zgloszenie.token, "language": "pl"},
        )
        response = self.client.get(url_podziekowania)

        self.assertEqual(response.status_code, 200)

    def test_zgloszenie_dziekujemy_wciaz_aktywne(self):
        zgloszenie = ZgloszenieFactory()
        zgloszenie.time -= datetime.timedelta(days=60)
        zgloszenie.save()

        uczestnik = UczestnikFactory(zgloszenie_form=zgloszenie)
        uczestnik.termin.termin -= datetime.timedelta(days=10)
        uczestnik.termin.save()

        url_podziekowania = reverse(
            "dziekujemy_za_zgloszenie",
            kwargs={"token": zgloszenie.token, "language": "pl"},
        )
        response = self.client.get(url_podziekowania)

        self.assertEqual(response.status_code, 200)

    def test_zgloszenie_dziekujemy_wygasa_po_3_mc_od_zgloszenia(self):
        zgloszenie = ZgloszenieFactory()
        zgloszenie.time -= datetime.timedelta(days=91)
        zgloszenie.save()

        url_podziekowania = reverse(
            "dziekujemy_za_zgloszenie",
            kwargs={"token": zgloszenie.token, "language": "pl"},
        )
        response = self.client.get(url_podziekowania)

        self.assertEqual(response.status_code, 404)

    def test_zgloszenie_dziekujemy_wygasa_po_2_tyg_od_startu_szkolenia(self):
        zgloszenie = ZgloszenieFactory()

        uczestnik = UczestnikFactory(zgloszenie_form=zgloszenie)
        uczestnik.termin.termin -= datetime.timedelta(days=30)
        uczestnik.termin.save()

        url_podziekowania = reverse(
            "dziekujemy_za_zgloszenie",
            kwargs={"token": zgloszenie.token, "language": "pl"},
        )
        response = self.client.get(url_podziekowania)

        self.assertEqual(response.status_code, 404)

    def test_zgloszenie_czy_papierowa_faktura(self):
        """
        Sprawdza, czy opcja wyboru formy faktury działa prawidłowo.
        """

        termin_klass = {
            "pl": TerminSzkoleniaFactory,
            "en": TerminSzkoleniaEnFactory,
        }

        expect_dict = {
            "pl": "Zgadzam się na otrzymanie mailem faktury w wersji elektronicznej, nie potrzebuję papierowej.",
            "en": "I opt to receive the invoice via e-mail (not in print).",
        }
        for (lang, chce_fakture) in [
            ("pl", True),
            ("pl", False),
            ("en", True),
            ("en", False),
        ]:

            termin = termin_klass[lang].create(
                szkolenie__aktywne=True, zamkniete=False, szkolenie__language=lang
            )
            szkolenie = termin.szkolenie

            url_zgloszenia = reverse(
                "zgloszenie", kwargs={"slug": szkolenie.slug, "language": lang}
            )
            platnosc = 1 if szkolenie.tag_dlugosc.slug == "kurs-zawodowy" else 5
            form_data = {
                "termin": termin.id,
                "za_kurs_zaplace": platnosc,
                "chce_obiady": 0,
                "akceptuje_regulamin": 1,
                "imie_nazwisko": "Jan Onufry Zagłoba",
                "czy_grupa": 1,
                "prywatny": 0,
                "faktura_firma": "faktura_firma",
                "faktura_adres": "faktura_firma",
                "faktura_miejscowosc_kod": "faktura_firma",
                "faktura_nip": "123-456-32-18",
                "faktura_vat_id": "faktura_firma",
                "chce_fakture": chce_fakture,
                "email": "<EMAIL>",
                "telefon": "123 456 789",
                "adres": "Wytwórnia miodów pitnych",
                "miejscowosc_kod": "Zbaraż",
                "captcha": self.pobierz_captche(),
            }
            response = self.client.post(url_zgloszenia, form_data)
            zgloszenie = Zgloszenie.objects.order_by("-id")[0]
            url_podziekowania = reverse(
                "dziekujemy_za_zgloszenie",
                kwargs={"token": zgloszenie.token, "language": szkolenie.language},
            )

            self.assertRedirects(response, url_podziekowania)

            response = self.client.get(url_podziekowania)
            msg = "{0}: {1}".format(szkolenie.id, szkolenie.nazwa)

            expect = expect_dict[lang]
            if chce_fakture:
                self.assertNotIn(expect, response, msg)
            else:
                self.assertContains(response, expect, 1, 200, msg)

    def test_zgloszenie_czy_drukowany_certyfikat(self):
        """
        Sprawdza, czy opcja wyboru formy certyfikatu działa prawidłowo.
        """

        termin_klass = {
            "pl": TerminSzkoleniaFactory,
            "en": TerminSzkoleniaEnFactory,
        }

        zamawiam = {
            "pl": "Zamawiam certyfikat ukończenia szkolenia w wersji elektronicznej oraz papierowej (dopłata 37,00 PLN netto za osobę).",
            "en": "I want to order a certificate of completion in electronic and print versions (additional fee 19.00 EUR net per person; option available in the EU and UK).",
        }
        nie_zamawiam = {
            "pl": "Wybieram certyfikat ukończenia szkolenia w wersji elektronicznej, nie potrzebuję papierowego.",
            "en": "I want to order a certificate of completion in electronic version only.",
        }
        for (lang, certyfikat) in [
            ("pl", True),
            ("pl", False),
            ("en", True),
            ("en", False),
        ]:
            termin = termin_klass[lang].create(
                szkolenie__aktywne=True, zamkniete=False, szkolenie__language=lang
            )
            szkolenie = termin.szkolenie

            url_zgloszenia = reverse(
                "zgloszenie", kwargs={"slug": szkolenie.slug, "language": lang}
            )
            platnosc = 1 if szkolenie.tag_dlugosc.slug == "kurs-zawodowy" else 5
            form_data = {
                "termin": termin.id,
                "za_kurs_zaplace": platnosc,
                "chce_obiady": 0,
                "akceptuje_regulamin": 1,
                "imie_nazwisko": "Jan Onufry Zagłoba",
                "czy_grupa": 1,
                "prywatny": 0,
                "faktura_firma": "faktura_firma",
                "faktura_adres": "faktura_firma",
                "faktura_miejscowosc_kod": "faktura_firma",
                "faktura_nip": "123-456-32-18",
                "faktura_vat_id": "faktura_firma",
                "chce_fakture": False,
                "drukowany_certyfikat": certyfikat,
                "email": "<EMAIL>",
                "telefon": "123 456 789",
                "adres": "Wytwórnia miodów pitnych",
                "miejscowosc_kod": "Zbaraż",
                "captcha": self.pobierz_captche(),
            }
            response = self.client.post(url_zgloszenia, form_data)
            zgloszenie = Zgloszenie.objects.order_by("-id")[0]
            url_podziekowania = reverse(
                "dziekujemy_za_zgloszenie",
                kwargs={"token": zgloszenie.token, "language": szkolenie.language},
            )

            self.assertRedirects(response, url_podziekowania)

            response = self.client.get(url_podziekowania)
            msg = "{0}: {1}".format(szkolenie.id, szkolenie.nazwa)

            if certyfikat:
                expect = zamawiam[lang]
            else:
                expect = nie_zamawiam[lang]

            self.assertContains(response, expect, 1, 200, msg)

    def test_zgloszenie_prywatny_a_grupa(self):
        """
        Sprawdza, czy opcja wyboru formy faktury działa prawidłowo.
        """
        polski_text = "{0}. {1}, {2}.".format(
            "Osoba prywatna nie może zgłosić grupy",
            "Jeżeli chcesz zapisać na szkolenie kilka prywatnych osób",
            "każda z nich musi musi przysłać osobny formularz",
        )
        angielski_text = "{0}. {1}, {2}.".format(
            "A private person cannot register a group",
            "If several private persons wish to register for a training",
            "each must fill in and send a separate training registration form",
        )
        expect_dict = {"pl": polski_text, "en": angielski_text}
        mozliwosci = [
            (lang, czy_grupa, ilosc_osob)
            for lang in ["en", "pl"]
            for czy_grupa in [0, 1]
            for ilosc_osob in [0, 5]
        ]
        for (lang, czy_grupa, ilosc_osob) in mozliwosci:
            if lang == "pl":
                termin = TerminSzkoleniaFactory(
                    szkolenie__aktywne=True, zamkniete=False, szkolenie__language=lang
                )
            else:
                termin = TerminSzkoleniaEnFactory(
                    szkolenie__aktywne=True, zamkniete=False, szkolenie__language=lang
                )
            szkolenie = termin.szkolenie

            url_zgloszenia = reverse(
                "zgloszenie", kwargs={"slug": szkolenie.slug, "language": lang}
            )
            platnosc = 1 if szkolenie.tag_dlugosc.slug == "kurs-zawodowy" else 5
            form_data = {
                "termin": termin.id,
                "za_kurs_zaplace": platnosc,
                "chce_obiady": 0,
                "akceptuje_regulamin": 1,
                "imie_nazwisko": "Jan Onufry Zagłoba",
                "czy_grupa": czy_grupa,
                "prywatny": 1,
                "uczestnik_wieloosobowy_ilosc_osob": ilosc_osob,
                "chce_fakture": 0,
                "email": "<EMAIL>",
                "telefon": "123 456 789",
                "adres": "Wytwórnia miodów pitnych",
                "miejscowosc_kod": "Zbaraż",
                "captcha": self.pobierz_captche(),
            }
            response = self.client.post(url_zgloszenia, form_data)
            # msg = "{0}: {1}".format(szkolenie.id, szkolenie.nazwa)
            if czy_grupa == 0 or ilosc_osob > 1:
                self.assertContains(response, expect_dict[lang])
            else:
                self.assertNotContains(response, expect_dict[lang])

    def test_tytul_maila(self):
        mail.outbox = []
        termin = TerminSzkoleniaEnFactory()

        self.wyslij_formularz(termin)

        self.assertEqual(
            "[Action required] ALX - order confirmation for Jan Onufry Zagłoba",
            mail.outbox[1].subject,
        )

        mail.outbox = []
        termin = TerminSzkoleniaFactory()

        self.wyslij_formularz(termin)

        self.assertEqual(
            "[Wymagana reakcja] ALX - potwierdzenie zgłoszenia dla Jan Onufry Zagłoba",
            mail.outbox[1].subject,
        )

        mail.outbox = []
        self.wyslij_formularz(
            termin,
            prywatny="0",
            imie_nazwisko="Józef Bąk3\nJacek Placek",
            czy_grupa=1,
            uczestnik_wieloosobowy_ilosc_osob=2,
            faktura_firma="Moja firma3",
        )

        self.assertEqual(
            "[Wymagana reakcja] ALX - potwierdzenie zgłoszenia dla Moja firma3",
            mail.outbox[1].subject,
        )

    def test_nazwa_snz_w_mailu(self):
        mail.outbox = []
        termin = TerminSzkoleniaFactory(szkolenie__kod="SnZ", snz_opis="Abc Abc")

        self.wyslij_formularz(termin)

        self.assertIn("SnZ: Abc Abc", mail.outbox[0].body)

    @override_settings(
        DOMENY_DLA_JEZYKOW={
            "pl": "example.pl",
            "en": "example.com",
        }
    )
    def test_zgloszenie_domeny_w_mailach(self):
        """
        Sprawdza czy z angieskiego formularza dostajemy domeny
        polską gdzie trzeba i angielską gdzie trzeba.
        """
        # FIXME co najmniej opis testu jest w połowie bez sensu, ten test kiedyś był inny.

        domena_en = settings.DOMENY_DLA_JEZYKOW["en"]
        domena_pl = settings.DOMENY_DLA_JEZYKOW["pl"]

        mail.outbox = []
        termin = TerminSzkoleniaEnFactory()

        response, uczestnik = self.wyslij_formularz(termin)
        mesg = mail.outbox[0].body

        error_msg = "niewłaściwa domena - powinna być dla wersji językowej pl"
        self.assertIn(domena_pl + "/admin/www/uczestnik/", mesg, error_msg)
        self.assertNotIn(domena_en + "/admin/www/uczestnik/", mesg, error_msg)
        self.assertIn(domena_pl + "/admin/www/terminszkolenia/", mesg, error_msg)
        self.assertNotIn(domena_en + "/admin/www/terminszkolenia/", mesg, error_msg)

        mail.outbox = []

        # a tutaj teraz sprawdzamy czy jak są widziani wcześniej
        # i nieodbyci, to też jest dobrze
        data_od_dzisiaj = datetime.date.today() + datetime.timedelta(days=5)
        termin_od_dzisiaj = TerminSzkoleniaFactory(
            termin=data_od_dzisiaj,
            szkolenie__aktywne=True,
            zamkniete=False,
            szkolenie__language="pl",
            szkolenie__cena_bazowa=5.00,
        )
        szkolenie0 = termin_od_dzisiaj.szkolenie
        data_kiedys = datetime.date.today() + datetime.timedelta(days=-30)
        termin_kiedys = TerminSzkoleniaFactory(
            termin=data_kiedys, szkolenie=szkolenie0, odbylo_sie=False
        )
        waluta = WalutaFactory()
        uczestnik = Uczestnik(
            termin=termin_kiedys, waluta=waluta, imie_nazwisko="Jakiś Unknown", status=5
        )
        uczestnik.save()

        dane = {
            "akceptuje_regulamin": 1,
            "imie_nazwisko": "Znowu Zagłoba?",
            "czy_grupa": 1,
            "prywatny": 0,
            "chce_obiady": 0,
            "telefon": "666 456 789",
            "adres": "Wytwórnia miodów pitnych",
            "miejscowosc_kod": "Zbaraż",
            "captcha": self.pobierz_captche(),
        }

        mail.outbox = []
        response, uczestnik_b = self.wyslij_formularz(termin_od_dzisiaj, **dane)

        mesg = mail.outbox[0].body

        error_msg = "u'niewłaściwa domena - powinna być dla wersji językowej pl'"
        self.assertIn(domena_pl + "/admin/www/uczestnik/", mesg, error_msg)
        self.assertNotIn(domena_en + "/admin/www/uczestnik/", mesg, error_msg)
        self.assertIn(domena_pl + "/admin/www/terminszkolenia/", mesg, error_msg)
        self.assertNotIn(domena_en + "/admin/www/terminszkolenia/", mesg, error_msg)

        mail.outbox = []
        tasks.poinformuj_o_potencjalnie_zainteresowanych(termin_od_dzisiaj.id)
        mesg = mail.outbox[0].body

        error_msg = "u'niewłaściwa domena - powinna być dla wersji językowej pl'"
        self.assertIn(domena_pl + "/admin/www/uczestnik/", mesg, error_msg)
        self.assertNotIn(domena_en + "/admin/www/uczestnik/", mesg, error_msg)
        self.assertIn(domena_pl + "/admin/www/terminszkolenia/", mesg, error_msg)
        self.assertNotIn(domena_en + "/admin/www/terminszkolenia/", mesg, error_msg)

    def test_platnosc_z_gory_na_ponad_tydzien_przed_szkoleniem(self):
        """
        Sprawdza, czy przy płatności z góry na > 7 dni przed
        szkoleniem termin zapłaty jest 7 dni po zapisie.
        """
        termin_data = datetime.date.today() + datetime.timedelta(days=20)
        # Tag długość wybieramy tylko dla ustalenia uwagi, gdyż forma zapłaty
        # zależy od tego, czy mamy do czynienia z kursem, czy ze szkoleniem.
        tag_dlugosc = TagDlugosc.objects.get(slug="kurs-zawodowy")
        szkolenie = SzkolenieFactory(tag_dlugosc=tag_dlugosc)
        termin = TerminSzkoleniaFactory(termin=termin_data, szkolenie=szkolenie)
        response, uczestnik = self.wyslij_formularz(
            termin,
            za_kurs_zaplace=1,
        )
        self.assertTrue(uczestnik)
        self.assertEqual(
            uczestnik.termin_zaplaty, datetime.date.today() + datetime.timedelta(days=7)
        )

    def test_platnosc_z_gory_mniej_niz_tydzien_przed_szkoleniem(self):
        """Sprawdza, czy przy płatności z góry na < 7 dni przed szkoleniem termin zapłaty jest w dniu szkolenia."""
        termin_data = datetime.date.today() + datetime.timedelta(days=3)
        # Tag długość wybieramy tylko dla ustalenia uwagi, gdyż forma zapłaty
        # zależy od tego, czy mamy do czynienia z kursem, czy ze szkoleniem.
        tag_dlugosc = TagDlugosc.objects.get(slug="kurs-zawodowy")
        szkolenie = SzkolenieFactory(tag_dlugosc=tag_dlugosc)
        termin = TerminSzkoleniaFactory(termin=termin_data, szkolenie=szkolenie)
        response, uczestnik = self.wyslij_formularz(
            termin,
            za_kurs_zaplace=1,
        )
        self.assertTrue(uczestnik)
        self.assertEqual(uczestnik.termin_zaplaty, termin.termin)

    def test_zgloszenie_smieciowego_terminu(self):
        """
        Sprawdza reakcję serwera na śmieciowy termin szkolenia przy zgłoszeniu - akcja robota.
        """
        expect_dict = {
            "pl": '<label class="required" for="id_termin">Termin</label><small>oraz miasto i tryb zajęć</small></td><td class="input"><ul class="errorlist"><li>Wybierz poprawną wartość.',
            "en": '<label class="required" for="id_termin">Date</label><small>and city and mode of training</small></td><td class="input"><ul class="errorlist"><li>Select a valid choice.',
        }
        termin_data = datetime.date.today() + datetime.timedelta(days=3)
        for lang in ["en", "pl"]:
            szkolenie = SzkolenieFactory(language=lang)
            if lang == "pl":
                termin = TerminSzkoleniaFactory(termin=termin_data, szkolenie=szkolenie)
            else:
                termin = TerminSzkoleniaEnFactory(
                    termin=termin_data, szkolenie=szkolenie
                )
            platnosc = 1 if szkolenie.tag_dlugosc.slug == "kurs-zawodowy" else 5
            url_zgloszenia = reverse(
                "zgloszenie", kwargs={"slug": szkolenie.slug, "language": lang}
            )

            # Przypadek śmieciowego terminu:
            form_data = {
                "adres": "Wytwórnia miodów pitnych",
                "chce_fakture": 0,
                "czy_grupa": 1,
                "email": "<EMAIL>",
                "imie_nazwisko": "Jan Onufry Zagłoba",
                "miejscowosc_kod": "Zbaraż",
                "osoba_do_kontaktu": "x",
                "prywatny": 1,
                "telefon": "123 456 789",
                "termin": "hmm ...",
                "za_kurs_zaplace": platnosc,
                "uwagi_klienta": "x",
                "akceptuje_regulamin": 1,
                "captcha": self.pobierz_captche(),
            }
            msg = "{0}: {1}".format(szkolenie.id, szkolenie.nazwa)
            expect = expect_dict[lang]

            response = self.client.post(url_zgloszenia, form_data)
            self.assertContains(response, expect, status_code=200, msg_prefix=msg)

            # To samo dla zakmnietego terminu
            form_data["termin"] = TerminSzkoleniaFactory(
                termin=termin_data, szkolenie=szkolenie, zamkniete=True
            )
            response = self.client.post(url_zgloszenia, form_data)
            self.assertContains(response, expect, status_code=200, msg_prefix=msg)

            # To samo dla terminu innego szkolenia
            form_data["termin"] = TerminSzkoleniaFactory(termin=termin_data)
            response = self.client.post(url_zgloszenia, form_data)
            self.assertContains(response, expect, status_code=200, msg_prefix=msg)

            # To samo dla terminu, który nie istnieje
            form_data["termin"] = TerminSzkoleniaFactory.create().pk + 99
            response = self.client.post(url_zgloszenia, form_data)
            self.assertContains(response, expect, status_code=200, msg_prefix=msg)

            # Przypadek poprawnego terminu:
            form_data["termin"] = termin.id
            form_data["captcha"] = self.pobierz_captche()
            response = self.client.post(url_zgloszenia, form_data)
            self.assertNotContains(response, expect, 302, msg)

    def test_faktura_kraj(self):
        """
        Sprawdza, czy został poprawnie ustawiony język w polu `faktura_kraj`.
        """

        # Wielka brytania
        termin = TerminSzkoleniaEnFactory()
        response, uczestnik = self.wyslij_formularz(termin, prywatny="1")
        self.assertEqual(uczestnik.faktura_kraj, "GB")

        # Polska
        termin = TerminSzkoleniaFactory()
        response, uczestnik = self.wyslij_formularz(termin, prywatny="1")
        self.assertEqual(uczestnik.faktura_kraj, "PL")

    def test_info_about_subscription(self):
        """
        Sprawdza informację (mail do Biura) o subskrypcji użytkownika.
        """

        mail.outbox = []

        # Powinna byc informacja "Użytkownik zapisany na powiadomienie: nie"
        termin = TerminSzkoleniaFactory()

        self.wyslij_formularz(termin)

        mesg = mail.outbox[0].body

        self.assertIn("Użytkownik zapisany na powiadomienie: nie", mesg)

        mail.outbox = []

        # Dodajemy zapis na notyfikację, ale w innym mieście niż termin.
        termin = TerminSzkoleniaFactory()

        email = "some.email.{0}@domain.com".format(time.time())
        course = UserCoursesNotificationFactory(
            training=termin.szkolenie,
            locations=[LokalizacjaFactory.create(), LokalizacjaFactory.create()],
        )

        course.user.email = email
        course.user.save()

        self.wyslij_formularz(termin, email=email)

        mesg = mail.outbox[0].body

        self.assertIn("Użytkownik zapisany na powiadomienie: nie", mesg)

        mail.outbox = []

        # Teraz dodajemy użytkownika do konkretnego szkolenia i lokalizacji.
        termin = TerminSzkoleniaFactory()

        email = "some.email.{0}@domain2.com".format(time.time())
        course = UserCoursesNotificationFactory(
            training=termin.szkolenie,
            locations=[
                LokalizacjaFactory.create(),
                LokalizacjaFactory.create(),
                termin.lokalizacja,
            ],
        )

        course.user.email = email
        course.user.save()

        self.wyslij_formularz(termin, email=email)

        mesg = mail.outbox[0].body

        self.assertIn(
            "Użytkownik zapisany na powiadomienie: tak (źródło: strona www)", mesg
        )

        mail.outbox = []

    def test_raty(self):
        """
        Sprawdzamy walidacje pól do rat po stronie backendu.
        """

        tag_dlugosc = TagDlugosc.objects.get(slug="kurs-zawodowy")
        szkolenie = SzkolenieFactory.create(tag_dlugosc=tag_dlugosc, wiele_rat=True)
        termin = TerminSzkoleniaFactory(szkolenie=szkolenie)

        response, uczestnik = self.wyslij_formularz(
            termin, prywatny="1", za_kurs_zaplace="10"
        )
        errors = response.context["form"].errors

        # Wymagane Panstwo
        self.assertEqual(errors["raty_panstwo"], ["To pole jest wymagane."])

        response, uczestnik = self.wyslij_formularz(
            termin, prywatny="1", za_kurs_zaplace="10", raty_panstwo="polska"
        )
        errors = response.context["form"].errors

        # Wymagany PESEL i nr dowodu
        self.assertEqual(errors["raty_pesel"], ["To pole jest wymagane."])
        self.assertEqual(
            errors["raty_numer_dokumentu"],
            ["Wprowadź numer dowodu w formacie XXXYYYYYY."],
        )

        response, uczestnik = self.wyslij_formularz(
            termin, prywatny="1", za_kurs_zaplace="10", raty_panstwo="inny"
        )
        errors = response.context["form"].errors

        # Wymagana nazwa dokumentu i numer
        self.assertEqual(errors["raty_nazwa_dokumentu"], ["To pole jest wymagane."])
        self.assertEqual(errors["raty_numer_dokumentu"], ["To pole jest wymagane."])

        # Zapisujemy dla Polski
        response, uczestnik = self.wyslij_formularz(
            termin,
            email="<EMAIL>",
            prywatny="1",
            za_kurs_zaplace="10",
            raty_panstwo="polska",
            raty_pesel="***********",
            raty_numer_dokumentu="AFZ123123",
        )

        self.assertEqual(uczestnik.raty_panstwo, "polska")
        self.assertEqual(uczestnik.raty_pesel, "***********")
        self.assertEqual(uczestnik.raty_numer_dokumentu, "AFZ123123")

        # Zapisujemy dla innego Kraju
        response, uczestnik = self.wyslij_formularz(
            termin,
            email="<EMAIL>",
            prywatny="1",
            za_kurs_zaplace="10",
            raty_panstwo="inny",
            raty_nazwa_dokumentu="ABC",
            raty_numer_dokumentu="XYZ",
        )

        self.assertEqual(uczestnik.raty_panstwo, "inny")
        self.assertEqual(uczestnik.raty_nazwa_dokumentu, "ABC")
        self.assertEqual(uczestnik.raty_numer_dokumentu, "XYZ")

    def test_mozliwosc_wyslania_plikow(self):
        tag_dlugosc = TagDlugosc.objects.get(slug="kurs-zawodowy")
        szkolenie = SzkolenieFactory.create(tag_dlugosc=tag_dlugosc, wiele_rat=True)
        termin = TerminSzkoleniaFactory(szkolenie=szkolenie)

        response, uczestnik = self.wyslij_formularz(
            termin,
            follow=True,
            email="<EMAIL>",
            prywatny="1",
            za_kurs_zaplace="10",
            raty_panstwo="inny",
            raty_nazwa_dokumentu="ABC",
            raty_numer_dokumentu="XYZ",
        )

        self.assertIn('class="dropzone"', response.content.decode("utf-8"))

        response, uczestnik = self.wyslij_formularz(
            termin,
            follow=True,
            email="<EMAIL>",
            prywatny="1",
            za_kurs_zaplace="1",
        )

        self.assertNotIn('class="dropzone"', response.content.decode("utf-8"))

    def test_wyslij_formularz_gdy_szkolenie_nieaktywne(self):
        szkolenie = SzkolenieFactory.create(aktywne=False)
        termin = TerminSzkoleniaFactory(szkolenie=szkolenie)

        url_zgloszenia = reverse(
            "zgloszenie",
            kwargs={
                "slug": termin.szkolenie.slug,
                "language": termin.szkolenie.language,
            },
        )

        response = self.client.get(url_zgloszenia, follow=True)
        self.assertEqual(response.status_code, 404)

    def test_raty_sa_dostepne_tylko_dla_prywatnych(self):
        """
        Sprawdzamy, czy firma może placić w ratach (nie może :-)
        """

        tag_dlugosc = TagDlugosc.objects.get(slug="kurs-zawodowy")
        szkolenie = SzkolenieFactory.create(tag_dlugosc=tag_dlugosc, wiele_rat=True)
        termin = TerminSzkoleniaFactory(szkolenie=szkolenie)

        # Firma
        for rata in (3, 10):
            response, uczestnik = self.wyslij_formularz(
                termin, prywatny="0", za_kurs_zaplace=str(rata)
            )
            errors = response.context["form"].errors

            # Wymagane Panstwo
            self.assertEqual(
                errors["za_kurs_zaplace"],
                ["Raty są dostępny tylko dla osób prywatnych."],
            )

        # Osoba prywatna - brak bledow
        for rata in (3, 10):
            response, uczestnik = self.wyslij_formularz(
                termin, prywatny="1", za_kurs_zaplace=str(rata)
            )
            errors = response.context["form"].errors

            # Wymagane Panstwo
            self.assertNotIn("za_kurs_zaplace", errors)

    def test_temat_maila_do_biura(self):
        mail.outbox = []
        termin = TerminSzkoleniaFactory()

        # Prywatny uczestnik
        response, uczestnik = self.wyslij_formularz(
            termin, prywatny="1", imie_nazwisko="Józef Bąk"
        )
        subject = mail.outbox[0].subject
        self.assertIn(" Józef Bąk", subject)

        # Firmowy (jedna osoba)
        mail.outbox = []
        response, uczestnik = self.wyslij_formularz(
            termin,
            prywatny="0",
            imie_nazwisko="Józef Bąk2",
            czy_grupa=1,
            faktura_firma="Moja firma2",
        )
        subject = mail.outbox[0].subject
        self.assertIn(" [F1] Moja firma2", subject)

        # Firmowy (wiele osób)
        mail.outbox = []
        response, uczestnik = self.wyslij_formularz(
            termin,
            prywatny="0",
            imie_nazwisko="Józef Bąk3\nJacek Placek",
            czy_grupa=1,
            uczestnik_wieloosobowy_ilosc_osob=2,
            faktura_firma="Moja firma3",
        )
        subject = mail.outbox[0].subject
        self.assertIn(" [F2] Moja firma3", subject)

    def tekst_o_kodzie_rabatowym_w_mailu_do_biura_gdy_brak_kodu(self):
        self.wyslij_formularz()
        self.assertNotIn("kodu rabatowego:", mail.outbox[0].body)

    def tekst_o_kodzie_rabatowym_w_mailu_do_biura_gdy_kod_ale_bez_absolwenta(self):
        DiscountCodeFactory.create(code="AbC")

        self.wyslij_formularz(discount_coupon="abc")

        self.assertIn("kodu rabatowego: AbC (7%).", mail.outbox[0].body)

    @override_settings(
        DOMENY_DLA_JEZYKOW={
            "pl": "example.pl",
            "en": "example.com",
        }
    )
    def tekst_o_kodzie_rabatowym_w_mailu_do_biura_gdy_kod_ale_bez_uczestnika(self):
        termin = TerminSzkoleniaFactory.create()

        discount_code = DiscountCodeFactory.create(code="AbC")
        GraduateFactory.create(
            discount_code=discount_code, participant=None, term=termin
        )

        self.wyslij_formularz(termin, discount_coupon="abc")

        self.assertIn(
            "kodu rabatowego: AbC (7% wygenerowany dla: -brak danych-, "
            "termin: http://example.pl/admin/www/terminszkolenia/{}/).".format(
                termin.pk
            ),
            mail.outbox[0].body,
        )

    @override_settings(
        DOMENY_DLA_JEZYKOW={
            "pl": "example.pl",
            "en": "example.com",
        }
    )
    def tekst_o_kodzie_rabatowym_w_mailu_do_biura_gdy_wszystkie_dane(self):
        termin = TerminSzkoleniaFactory.create()

        discount_code = DiscountCodeFactory.create(code="AbC")
        GraduateFactory.create(
            discount_code=discount_code,
            participant=UczestnikFactory.create(
                imie_nazwisko="Jan Kowalski", termin__szkolenie__kod="123"
            ),
            term=termin,
        )

        self.wyslij_formularz(termin, discount_coupon="abc")

        self.assertIn(
            "kodu rabatowego: AbC (7% wygenerowany dla: Jan Kowalski (123), "
            "termin: http://example.pl/admin/www/terminszkolenia/{}/).".format(
                termin.pk
            ),
            mail.outbox[0].body,
        )

    def test_wariant_szkolenia(self):
        termin = TerminSzkoleniaFactory(szkolenie__aktywne=False)
        response, uczestnik = self.wyslij_formularz(termin, prywatny="1")
        self.assertFalse(uczestnik)

        SzkolenieWariantFactory.create(wariant=termin.szkolenie)

        response, uczestnik = self.wyslij_formularz(termin, prywatny="1")
        self.assertTrue(uczestnik)

    def test_wariant_szkolenia_gdy_nieaktywne(self):
        termin = TerminSzkoleniaFactory(szkolenie__aktywne=False)

        SzkolenieWariantFactory.create(
            wariant=termin.szkolenie, szkolenie__aktywne=False
        )

        response, uczestnik = self.wyslij_formularz(termin, prywatny="1")
        self.assertFalse(uczestnik)


class FormularzZgloszeniowyZamknietyTestCase(ALXTestCase, CaptchaMixin):
    longMessage = True

    # Słownik z przykładowymi, neutralnymi danymi, który będziemy używać
    # jako bazę dla testowanych przypadków.
    przykladowe_dane = {
        "uczestnik_wieloosobowy_ilosc_osob": 1,
        "imie_nazwisko": "Jan Onufry Zagłoba",
        "osoba_do_kontaktu": "pan.zagloba",
        "email_ksiegowosc": "<EMAIL>",
        "telefon": "123 456 789",
        "adres": "Wytwórnia miodów pitnych",
        "miejscowosc_kod": "Zbaraż",
        "faktura_firma": "Chorągiew księcia Jeremiego",
        "faktura_adres": "Zamek książęcy",
        "faktura_miejscowosc_kod": "Zbaraż",
        "faktura_nip": "123-456-32-18",
        "za_kurs_zaplace": 5,
        "chce_fakture": 0,
    }

    def test_strona_z_formularzem_zgloszeniowym_dziala(self):
        """
        Sprawdza, czy dla widocznych szkoleń strona z formularzem
        zgłoszeniowym zwraca status 200.
        """

        termin = TerminSzkoleniaFactory.create(
            zamkniete=True, prywatna_rejestracja=True
        )

        url_zgloszenia = reverse(
            "zamkniete_zgloszenie",
            kwargs={
                "slug": termin.szkolenie.slug,
                "language": termin.szkolenie.language,
                "secret_key": termin.secret_key.hex,
            },
        )
        response = self.client.get(url_zgloszenia)
        self.assertEqual(response.status_code, 200)

        # Gdy szkolenie otwarte

        termin.zamkniete = False
        termin.save()

        response = self.client.get(url_zgloszenia)
        self.assertEqual(response.status_code, 404)

    def dane_formularza_dla_terminu(self, **kwargs):
        """
        Zwraca słownik z danymi potrzebnymi do wysłania formularza
        zgłoszeniowego na konkretny termin. Przykładowe dane
        są nadpisywane przez argumenty podane jako słowa kluczowe.
        """
        dane = dict(
            self.przykladowe_dane,
            captcha=self.pobierz_captche(),
        )
        return dict(dane, **kwargs)

    def znajdz_uczestnika(self, dane_formularza):
        """
        Znajduje uczestnika, który wypełnił formularz zadanymi danymi.
        W przypadku, gdy uczestnik nie istnieje, zwraca None.
        """
        dane_do_przeszukania = ["email_ksiegowosc"]
        try:
            return Uczestnik.objects.filter(
                **dict(
                    (klucz, dane_formularza[klucz]) for klucz in dane_do_przeszukania
                )
            ).get()
        except Uczestnik.DoesNotExist:
            return None

    def wyslij_formularz(self, termin, **kwargs):
        """
        Wysyła formularz zgłoszeniowy i zwraca otrzymaną odpowiedź oraz ewentualnie
        zapisanego uczestnika. W przypadku niepodania terminu, pobierany jest przykładowy termin.
        Formularz zawiera przykładowe dane nadpisane przez argumenty podane jako słowa kluczowe.
        """

        url_zgloszenia = reverse(
            "zamkniete_zgloszenie",
            kwargs={
                "slug": termin.szkolenie.slug,
                "language": termin.szkolenie.language,
                "secret_key": termin.secret_key.hex,
            },
        )
        dane_formularza = self.dane_formularza_dla_terminu(**kwargs)

        response = self.client.post(url_zgloszenia, dane_formularza, follow=True)
        self.assertEqual(response.status_code, 200)
        return response, self.znajdz_uczestnika(dane_formularza)

    def test_zapisane_dane(self):
        termin = TerminZamknietySzkoleniaFactory(drukowany_certyfikat_w_cenie=True)
        response, uczestnik = self.wyslij_formularz(
            termin, email_ksiegowosc="<EMAIL>", imie_nazwisko=""
        )
        self.assertTrue(uczestnik)
        self.assertTrue(uczestnik.zgloszenie_form)
        self.assertEqual(uczestnik.email_ksiegowosc, "<EMAIL>")
        self.assertEqual(uczestnik.email, "<EMAIL>")

        self.assertFalse(uczestnik.chce_autoryzacje)
        self.assertTrue(uczestnik.drukowany_certyfikat)
        self.assertFalse(uczestnik.cena_obiadow)
        self.assertFalse(uczestnik.chce_autoryzacje)
        self.assertTrue(uczestnik.chce_obiady)
        self.assertEqual(uczestnik.termin, termin)
        self.assertEqual(uczestnik.faktura_kraj, "PL")

        self.assertEqual(uczestnik.imie_nazwisko, "Osób w grupie: 1")

        self.assertTrue(uczestnik.waluta)
        self.assertEqual(uczestnik.stawka_vat, Decimal("0.23"))

    def test_stawka_vat_gdy_podmiot_publiczny(self):
        termin = TerminZamknietySzkoleniaFactory()
        response, uczestnik = self.wyslij_formularz(
            termin,
            podmiot_publiczny=True,
        )
        self.assertEqual(uczestnik.stawka_vat, 0)

    def test_klient_angielski_ma_zerowy_vat(self):
        """
        Sprawdza, czy klient zagraniczny na angielskiej stronie ma zerowy VAT.
        """
        termin = TerminZamknietySzkoleniaEnFactory()
        response, uczestnik = self.wyslij_formularz(
            termin,
            faktura_vat_id="GB999 9999 73",
        )
        self.assertEqual(uczestnik.stawka_vat, 0)
        self.assertEqual(uczestnik.faktura_kraj, "GB")

    def test_cena_dla_grupy_wlasciwej(self):
        termin = TerminZamknietySzkoleniaFactory()
        response, uczestnik = self.wyslij_formularz(
            termin,
            uczestnik_wieloosobowy_ilosc_osob=5,
        )
        self.assertEqual(uczestnik.cena(), 800)

    def test_cena_dla_grupy_nadmiarowej(self):
        termin = TerminZamknietySzkoleniaFactory()
        response, uczestnik = self.wyslij_formularz(
            termin,
            uczestnik_wieloosobowy_ilosc_osob=8,
        )
        self.assertEqual(uczestnik.cena(), 860)

    def test_przypisanie_uczestnika_przy_zapisaniu_na_newsletter(self):
        """
        Sprawdzić, czy do odbiorcy newslettera jest przypisywany
        uczestnik.
        """

        termin = TerminZamknietySzkoleniaFactory()
        response, uczestnik = self.wyslij_formularz(
            termin,
            chce_zapisac_sie_na_newsletter="1",
        )
        odbiorca = Odbiorca.objects.get(email=uczestnik.email)
        self.assertEqual(odbiorca.uczestnik, uczestnik)

    def test_weryfikacja_formatu_numeru_nip(self):
        """
        Sprawdza, czy format NIP jest poprawny.
        """

        # Bledny NIP
        termin = TerminZamknietySzkoleniaFactory()
        response, uczestnik = self.wyslij_formularz(
            termin,
            faktura_nip="123-456-32 18",
        )

        self.assertFalse(uczestnik)

        form = response.context["form"]
        self.assertIn("faktura_nip", form.errors)

    def test_pole_uwagi_klienta_wymagane(self):
        termin = TerminZamknietySzkoleniaFactory()
        response, uczestnik = self.wyslij_formularz(
            termin,
            za_kurs_zaplace=7,
        )

        self.assertFalse(uczestnik)

        form = response.context["form"]
        self.assertIn("uwagi_klienta", form.errors)

    def test_pole_bylem_na_wymagane(self):
        termin = TerminZamknietySzkoleniaFactory()
        response, uczestnik = self.wyslij_formularz(
            termin,
            bylem_wczesniej=True,
        )

        self.assertFalse(uczestnik)

        form = response.context["form"]
        self.assertIn("bylem_na", form.errors)

    def test_maile_sa_wysylane(self):
        termin = TerminZamknietySzkoleniaFactory()
        response, uczestnik = self.wyslij_formularz(
            termin, uczestnik_wieloosobowy_ilosc_osob=2, faktura_firma="ABC"
        )

        self.assertEqual(len(mail.outbox), 2)

        self.assertIn("Zgłoszenie na", mail.outbox[0].subject)

        self.assertEqual(
            "[Wymagana reakcja] ALX - potwierdzenie zgłoszenia dla ABC",
            mail.outbox[1].subject,
        )

    def test_wyslij_formularz_gdy_szkolenie_otwarte(self):
        szkolenie = SzkolenieFactory.create()
        termin = TerminSzkoleniaFactory(szkolenie=szkolenie)

        url_zgloszenia = reverse(
            "zamkniete_zgloszenie",
            kwargs={
                "slug": termin.szkolenie.slug,
                "language": termin.szkolenie.language,
                "secret_key": termin.secret_key.hex,
            },
        )

        response = self.client.get(url_zgloszenia, follow=True)
        self.assertEqual(response.status_code, 404)


@override_settings(
    CENA_NETTO_DRUKOWANEGO_CERTYFIKATU={
        "pl": 37,
        "en": 19,
    }
)
class FormularzZgloszeniowyLiveServerTestCase(ALXLiveServerTestCase, CaptchaMixin):
    path = {
        "prywatny": '//input[@name="prywatny"][@value="1"]',
        "firma": '//input[@name="prywatny"][@value="0"]',
        "grupa": '//input[@id="id_czy_grupa_1"]',
        "osoba": '//input[@id="id_czy_grupa_0"]',
        "ilosc_osob": '//input[@id="id_uczestnik_wieloosobowy_ilosc_osob"]',
        "chce_obiady": '//input[@id="id_chce_obiady_0"]',
        "niechce_obiady": '//input[@id="id_chce_obiady_1"]',
        "ile_wege": '//input[@id="id_ile_obiadow_wegetarianskich"]',
        "czy_wege": '//input[@id="id_obiad_wegetarianski"]',
        "f_firma": '//input[@id="id_faktura_firma"]',
        "f_adres": '//input[@id="id_faktura_adres"]',
        "f_miejscowosc_kod": '//input[@id="id_faktura_miejscowosc_kod"]',
        "f_vat": {
            "pl": '//input[@id="id_faktura_nip"]',
            "en": '//input[@id="id_faktura_vat_id"]',
        },
        "publiczny": '//input[@id="id_podmiot_publiczny"]',
        "raty_no_3": '//input[@name="za_kurs_zaplace"][@value="3"]',
        "raty_no_10": '//input[@name="za_kurs_zaplace"][@value="10"]',
        "raty_panstwo": '//select[@name="raty_panstwo"]',
        "raty_nazwa_dokumentu": '//input[@name="raty_nazwa_dokumentu"]',
        "raty_numer_dokumentu": '//input[@name="raty_numer_dokumentu"]',
        "raty_pesel": '//input[@name="raty_pesel"]',
    }

    def termin_lang(self, szkolenie=None, lang="pl", **kwargs):
        if not szkolenie:
            szkolenie = SzkolenieFactory(language=lang, czas_dni="1")
        if lang == "en":
            termin = TerminSzkoleniaEnFactory(szkolenie=szkolenie, **kwargs)
        else:
            termin = TerminSzkoleniaFactory(szkolenie=szkolenie, **kwargs)
        return szkolenie, termin

    def test_zgloszenie_na_kurs_osoby_prywatnej(self):
        """
        Sprawdza, czy osoba prywatna może się zapisać na kurs
        i otrzymuje prawidłową cenę.
        """

        # self.selenium.set_window_size(1000, 1000)

        dane_do_wypelnienia = {
            "imie_nazwisko": "Jan Onufry Zagłoba",
            "email": "<EMAIL>",
            "telefon": "123 456 789",
            "adres": "Wytwórnia miodów pitnych",
            "miejscowosc_kod": "12-123 Zbaraż",
        }
        tag_dlugosc = TagDlugosc.objects.get(slug="kurs-zawodowy")
        kursy = SzkolenieFactory.create_batch(2, tag_dlugosc=tag_dlugosc)
        TerminSzkoleniaFactory(szkolenie=kursy[0])
        TerminSzkoleniaFactory(
            szkolenie=kursy[1], cena_obiadu=20, obiady="obiady-opcjonalne"
        )

        for no, kurs_z_terminami in enumerate(kursy, start=0):
            self.selenium.get(
                self.live_server_url
                + reverse(
                    "zgloszenie",
                    kwargs={
                        "slug": kurs_z_terminami.slug,
                        "language": kurs_z_terminami.language,
                    },
                )
            )

            wait = WebDriverWait(self.selenium, 10)
            wait.until(element_to_be_clickable(["id", "id_prywatny_1"]))
            self.selenium.find_element_by_id("id_prywatny_1").click()
            self.wait_for_hidden_element_by_xpath("//*[@name='faktura_firma']")
            if (
                "Obiady nie są wliczone w cenę"
                in self.selenium.find_element_by_id("tekst_o_obiadach").text
            ):
                self.selenium.find_element_by_css_selector(
                    '[name="chce_obiady"][value="0"]'
                ).click()
            self.selenium.find_element_by_id("id_akceptuje_regulamin").click()
            self.selenium.find_element_by_css_selector(
                '[name="za_kurs_zaplace"][value="1"]'
            ).click()

            dane_do_wypelnienia["captcha"] = self.pobierz_captche()[1]
            self.fill_fields_by_name(**dane_do_wypelnienia)
            if no:
                self.selenium.find_element_by_id("id_drukowany_certyfikat_1").click()
            # self.click_by_xpath("//*[@id='submit_button']")
            # self.selenium.find_element_by_id('submit_button').click()
            self.selenium.find_element_by_name("zgloszenie_form").submit()
            self.wait_for_visible_element_by_xpath(
                "//p[text()='Dziękujemy za wypełnienie formularza.']"
            )

            page_text = self.selenium.find_element_by_tag_name("body").text
            error_msg = "Zła cena: {0}: {1}".format(
                kurs_z_terminami.id, kurs_z_terminami.nazwa
            )
            # Różnice groszowe.
            kurs_cena = kurs_z_terminami.cena + ((37 * Decimal("1.23")) if no else 0)

            poprawne_ceny = [
                pretty_number(kurs_cena + i * Decimal("0.01")) for i in range(-1, 2)
            ]

            self.assertTrue(
                any(
                    "{0} {1} brutto".format(
                        number_format(cena), kurs_z_terminami.waluta.symbol
                    )
                    in page_text
                    for cena in poprawne_ceny
                ),
                error_msg,
            )

            uczestnik = Uczestnik.objects.filter(
                imie_nazwisko="Jan Onufry Zagłoba"
            ).order_by("-pk")[0]

            # Sprawdź, czy poprawnie został ustawiony Kraj na Polskę:
            self.assertEqual(uczestnik.faktura_kraj, "PL")

            # Sprawdź, czy poprawnie został ustawiony Info o drukowanym
            # certyfikacie
            self.assertEqual(uczestnik.drukowany_certyfikat, bool(no))

    def test_sprawdzenie_czy_sa_opcje_faktury(self):
        """
        Sprawdza czy są opcje do wybrania formy faktury i jaka opcja jest domyślnie
        """
        expect_dict = {
            ("pl", 2): "Wybór faktury elektronicznej oznacza rezygnację z faktury "
            "papierowej i wyrażenie zgody na przesłanie faktury "
            "e-mailem. Zmiana formy wysyłki faktury po wystawieniu "
            "faktury końcowej wiąże się z dopłatą 50 zł netto.",
            ("en", 2): "Choosing the electronic invoice is equivalent to receiving "
            "the invoice only via e-mail (not on paper). Changing the "
            "form of receiving the invoice after generating the invoice "
            "implies surcharge of 24.00 EUR net.",
            ("pl", 1): "papierowa",
            ("en", 1): "paper",
            ("pl", 0): "elektroniczna",
            ("en", 0): "electronic",
        }
        expected_text = ["", "", ""]

        terminy = [
            (
                "pl",
                TerminSzkoleniaFactory(
                    termin=datetime.date.today() + datetime.timedelta(10),
                    zamkniete=False,
                    szkolenie__aktywne=True,
                    szkolenie__language="pl",
                    lokalizacja__panstwo=Panstwo.objects.get(nazwa="Polska"),
                ),
            ),
            ("en", TerminSzkoleniaEnFactory()),
        ]
        for (lang, termin) in terminy:
            termin.clean()
            szkolenie = termin.szkolenie
            self.selenium.get(
                self.live_server_url
                + reverse(
                    "zgloszenie", kwargs={"slug": szkolenie.slug, "language": lang}
                )
            )

            expected_text[
                2
            ] = "//td[@class='label']/small[contains(text(),'{0}')]".format(
                expect_dict[(lang, 2)]
            )
            expected_text[
                1
            ] = "//label[@for='id_chce_fakture_1'][contains(text(),'{0}')]".format(
                expect_dict[(lang, 1)]
            )
            expected_text[
                0
            ] = "//label[@for='id_chce_fakture_0'][contains(text(),'{0}')]".format(
                expect_dict[(lang, 0)]
            )

            expected = "//label[@for='id_chce_fakture_0']/input[@checked]"
            not_expected = "//label[@for='id_chce_fakture_1']/input[@checked]"
            self.wait_for_visible_element_by_xpath(expected)
            try:
                self.wait_for_element_by_xpath(not_expected)
                msg = "Na stronie nie powinno być domyślnie zaznaczone{0}".format(
                    expect_dict[(lang, 1)]
                )
                self.fail(msg)
            except TimeoutException:
                pass
            for i in range(3):
                self.wait_for_element_by_xpath(expected_text[i])

    def test_sprawdzenie_zachowania_informacji_o_certyfikatach(self):
        """
        Sprawdza czy informacja o certyfikacie pojawia się prawidłowo
        """

        t1 = TerminSzkoleniaFactory.create()
        t2 = TerminSzkoleniaEnFactory.create()

        expected_text = {
            "pl": "UWAGA: Wybór drukowanego certyfikatu oznacza dopłatę do ceny szkolenia w wysokości 37,00 zł netto (cena zawiera przesyłkę pocztą we wzmocnionej kopercie za potwierdzeniem, na podany adres do korespondencji).",
            "en": "ATTENTION: Choosing paper certificate implies surcharge "
            "of 19.00 EUR net (option available in the EU and UK).",
        }

        for t in [t1, t2]:
            szkolenie = t.szkolenie

            self.selenium.get(
                self.live_server_url
                + reverse(
                    "zgloszenie",
                    kwargs={"slug": szkolenie.slug, "language": szkolenie.language},
                )
            )

            expected_element = "//td[@id='tekst_o_certyfikacie']"

            # Brak informacji o dopłacie
            el = self.wait_for_hidden_element_by_xpath(expected_element)
            self.assertEqual(el.text, "")

            # Zmieniamy, że chcemy drukowany certyfikat
            self.selenium.find_element_by_id("id_drukowany_certyfikat_1").click()

            el = self.wait_for_visible_element_by_xpath(expected_element)
            self.assertEqual(el.text, expected_text[szkolenie.language])

    def test_sprawdzenie_zachowania_informacji_obiadowej(self):
        """
        Sprawdza czy informacja o obiadach pojawia się prawidłowo
        """

        date_now = datetime.date.today()
        date_weekday = date_now + datetime.timedelta(days=11 - date_now.weekday())
        date_nieweekday = date_weekday + datetime.timedelta(days=-2)
        terminy_0_szkolenie_1 = TerminSzkoleniaFactory(
            termin=date_nieweekday, obiady="nie-ma-obiadow"
        )
        terminy_0_szkolenie_1b = TerminSzkoleniaFactory(
            termin=date_weekday, obiady="nie-ma-obiadow"
        )
        terminy_0_szkolenie_0 = TerminSzkoleniaFactory(
            obiady="nie-ma-obiadow", szkolenie__zawiera_obiady=False
        )
        terminy_1 = TerminSzkoleniaFactory(obiady="obiady-wliczone")
        terminy_0_1 = TerminSzkoleniaFactory(obiady="obiady-opcjonalne", cena_obiadu=20)
        terminy_ba = TerminSzkoleniaFactory(obiady="", szkolenie__zawiera_obiady=False)
        lista_terminow = [
            terminy_0_szkolenie_1,
            terminy_0_szkolenie_1b,
            terminy_0_szkolenie_0,
            terminy_1,
            terminy_0_1,
            terminy_ba,
        ]

        for t in lista_terminow:
            szkolenie = t.szkolenie

            self.selenium.get(
                self.live_server_url
                + reverse(
                    "zgloszenie", kwargs={"slug": szkolenie.slug, "language": "pl"}
                )
            )
            termin_select = Select(self.selenium.find_element_by_id("id_termin"))
            t_string = "{0} ({1}, {2})".format(
                t.termin.__str__(), t.lokalizacja.shortname, t.get_tryb_display()
            )
            termin_select.select_by_visible_text(t_string)

            expected_text = ""

            if t.obiady == "obiady-wliczone":
                expected_text = "Obiady są wliczone w cenę."
            elif t.obiady == "obiady-opcjonalne":
                expected_text = "Koszt obiadu"
            elif t.szkolenie.zawiera_obiady:
                if t.obiady == "nie-ma-obiadow":
                    if t.termin.weekday() == 5:
                        expected_text = "Termin weekendowy."
                    else:
                        expected_text = "Brak obiadów i brak możliwości ich dokupienia."
            else:
                expected_text = ""

            if expected_text != "":
                expected_element = (
                    "//td[@id='tekst_o_obiadach']/*[contains(text(),'{0}')]".format(
                        expected_text
                    )
                )
                try:
                    self.wait_for_visible_element_by_xpath(expected_element)
                except TimeoutException:
                    msg = 'Termin: {0}. Na stronie powinien być tekst: "{1}"'.format(
                        t_string, expected_text
                    )
                    self.fail(msg)
            else:
                expected_element = "//tr/td[@id='tekst_o_obiadach']/.."
                try:
                    self.wait_for_hidden_element_by_xpath(expected_element)
                except TimeoutException:
                    msg = "Termin: {0}. Na stronie nie powinno być tekstu o obiadach".format(
                        t_string
                    )
                    self.fail(msg)

    def test_osoba_grupa_input_textarea(self):
        """
        Sprawdza czy formularz poprawnie przełącza pola do wpisywania osoby lub grupy
        """
        for lang in ["en", "pl"]:
            imie_nazwisko_input = '//input[@id="id_imie_nazwisko"]'
            imie_nazwisko_textarea = '//textarea[@id="id_imie_nazwisko"]'
            szkolenie, termin = self.termin_lang(lang=lang)
            self.selenium.get(
                self.live_server_url
                + reverse(
                    "zgloszenie", kwargs={"slug": szkolenie.slug, "language": lang}
                )
            )
            self.wait_for_element_by_xpath(self.path["firma"]).click()
            self.wait_for_element_by_xpath(self.path["grupa"]).click()
            self.wait_for_visible_element_by_xpath(imie_nazwisko_textarea)
            self.wait_for_visible_element_by_xpath(self.path["ilosc_osob"])
            try:
                self.selenium.find_element_by_xpath(imie_nazwisko_input)
                msg = "Na stronie nie powinno być elementu {0}".format(
                    imie_nazwisko_input
                )
                self.fail(msg)
            except NoSuchElementException:
                pass
            self.wait_for_element_by_xpath(self.path["osoba"]).click()
            self.wait_for_hidden_element_by_xpath(self.path["ilosc_osob"])
            try:
                self.selenium.find_element_by_xpath(imie_nazwisko_textarea)
                msg = "Na stronie nie powinno być elementu {0}".format(
                    imie_nazwisko_textarea
                )
                self.fail(msg)
            except NoSuchElementException:
                pass
            self.wait_for_visible_element_by_xpath(imie_nazwisko_input)

    def test_czyszczenielubnie_wartosci_imie_nazwisko(self):
        """
        Sprawdza czy wartość pola imię nazwisko czyści się wtedy gdy przestawia się z grupy na osobę
        lub z grupy na prywatny. W innych przypadkach ma się nie czyścić.
        """
        kliki = ["firma", "prywatny", "osoba", "grupa"]
        mozliwosci = [(a, b) for a in kliki for b in kliki if a != b]
        mozliwosci.remove(("grupa", "firma"))
        for lang in ["en", "pl"]:
            szkolenie, termin = self.termin_lang(lang=lang)
            self.selenium.get(
                self.live_server_url
                + reverse(
                    "zgloszenie", kwargs={"slug": szkolenie.slug, "language": lang}
                )
            )
            for (a, b) in mozliwosci:
                if a in ["osoba", "grupa"]:
                    self.wait_for_element_by_xpath(self.path["firma"]).click()
                self.wait_for_element_by_xpath(self.path[a]).click()
                imie_nazwisko = self.wait_for_element_by_id("id_imie_nazwisko")
                imie_nazwisko.send_keys("Jasio Podróżniczek")
                if b in ["osoba", "grupa"]:
                    self.wait_for_element_by_xpath(self.path["firma"]).click()
                self.wait_for_element_by_xpath(self.path[b]).click()
                imie_nazwisko = self.wait_for_element_by_id("id_imie_nazwisko")
                imie_nazwisko_text = imie_nazwisko.get_attribute("value")
                msg = "Przełączenie z {a} na {b}".format(a=a, b=b)
                if a == "grupa" and b != "grupa":
                    self.assertEqual(imie_nazwisko_text, "", msg)
                else:
                    self.assertEqual(imie_nazwisko_text, "Jasio Podróżniczek", msg)
                imie_nazwisko.clear()

    def test_osoba_grupa_obiady_opcjonalne(self):
        """
        Sprawdza czy formularz poprawnie przełącza pola dla obiadów osoby lub grupy
        """
        for lang in ["en", "pl"]:
            szkolenie, termin = self.termin_lang(
                lang=lang, obiady="obiady-opcjonalne", cena_obiadu=20
            )
            self.selenium.get(
                self.live_server_url
                + reverse(
                    "zgloszenie", kwargs={"slug": szkolenie.slug, "language": lang}
                )
            )
            # Grupa:
            self.selenium.find_element_by_xpath(self.path["firma"]).click()
            self.wait_for_element_by_xpath(self.path["grupa"]).click()
            self.wait_for_element_by_xpath(self.path["chce_obiady"]).click()
            self.wait_for_element_by_xpath(self.path["ilosc_osob"]).send_keys("2")
            self.wait_for_visible_element_by_xpath(self.path["ile_wege"])
            self.wait_for_hidden_element_by_xpath(self.path["czy_wege"])
            # A teraz klikamy, że nie chcemy obiadów:
            self.selenium.find_element_by_xpath(self.path["niechce_obiady"]).click()
            self.wait_for_hidden_element_by_xpath(self.path["ile_wege"])
            self.wait_for_hidden_element_by_xpath(self.path["czy_wege"])
            # Osoba:
            self.selenium.find_element_by_xpath(self.path["osoba"]).click()
            self.selenium.find_element_by_xpath(self.path["chce_obiady"]).click()
            self.wait_for_hidden_element_by_xpath(self.path["ile_wege"])
            self.wait_for_visible_element_by_xpath(self.path["czy_wege"])
            # A teraz klikamy, że nie chcemy obiadów:
            self.selenium.find_element_by_xpath(self.path["niechce_obiady"]).click()
            self.wait_for_hidden_element_by_xpath(self.path["ile_wege"])
            self.wait_for_hidden_element_by_xpath(self.path["czy_wege"])

            # Zmieniamy kolejność klikania:
            # Klikamy, że chcemy obiady:
            self.selenium.find_element_by_xpath(self.path["chce_obiady"]).click()
            # Grupa:
            self.wait_for_element_by_xpath(self.path["grupa"]).click()
            self.wait_for_element_by_xpath(self.path["ilosc_osob"]).send_keys("2")
            self.wait_for_visible_element_by_xpath(self.path["ile_wege"])
            self.wait_for_hidden_element_by_xpath(self.path["czy_wege"])
            # Osoba:
            self.selenium.find_element_by_xpath(self.path["osoba"]).click()
            self.wait_for_hidden_element_by_xpath(self.path["ile_wege"])
            self.wait_for_visible_element_by_xpath(self.path["czy_wege"])

    def test_osoba_grupa_obiady_wliczone(self):
        """
        Sprawdza czy formularz poprawnie przełącza pola dla obiadów osoby lub grupy
        """
        for lang in ["en", "pl"]:
            szkolenie, termin = self.termin_lang(lang=lang, obiady="obiady-wliczone")
            self.selenium.get(
                self.live_server_url
                + reverse(
                    "zgloszenie", kwargs={"slug": szkolenie.slug, "language": lang}
                )
            )
            self.wait_for_hidden_element_by_xpath(self.path["chce_obiady"])
            self.selenium.find_element_by_xpath(self.path["firma"]).click()
            self.wait_for_element_by_xpath(self.path["grupa"]).click()
            self.wait_for_element_by_xpath(self.path["ilosc_osob"]).send_keys("2")
            self.wait_for_visible_element_by_xpath(self.path["ile_wege"])
            self.wait_for_hidden_element_by_xpath(self.path["czy_wege"])
            ilosc_osob = self.selenium.find_element_by_xpath(
                self.path["ilosc_osob"]
            ).get_attribute("value")
            self.assertEqual(ilosc_osob, "2")
            self.selenium.find_element_by_xpath(self.path["osoba"]).click()
            self.wait_for_hidden_element_by_xpath(self.path["ile_wege"])
            self.wait_for_visible_element_by_xpath(self.path["czy_wege"])
            # Czy się czyści ilość osób?
            ilosc_osob = self.wait_for_element_by_xpath(
                self.path["ilosc_osob"]
            ).get_attribute("value")
            self.assertEqual(ilosc_osob, "")

    def test_prywatny_firma_przelaczanie(self):
        """
        Sprawdza czy formularz poprawnie przełącza pola dla dla osoby prywatnej lub firmy
        """
        for lang in ["en", "pl"]:
            szkolenie, termin = self.termin_lang(lang=lang, obiady="obiady-wliczone")
            self.selenium.get(
                self.live_server_url
                + reverse(
                    "zgloszenie", kwargs={"slug": szkolenie.slug, "language": lang}
                )
            )
            self.wait_for_hidden_element_by_xpath(self.path["chce_obiady"])
            self.selenium.find_element_by_xpath(self.path["firma"]).click()
            self.wait_for_visible_element_by_xpath(self.path["grupa"])
            self.wait_for_visible_element_by_xpath(self.path["osoba"])
            self.wait_for_visible_element_by_xpath(self.path["f_firma"])
            self.wait_for_visible_element_by_xpath(self.path["f_adres"])
            self.wait_for_visible_element_by_xpath(self.path["f_miejscowosc_kod"])
            self.wait_for_visible_element_by_xpath(self.path["f_vat"][lang])
            self.selenium.find_element_by_xpath(self.path["prywatny"]).click()
            self.wait_for_hidden_element_by_xpath(self.path["grupa"])
            self.wait_for_hidden_element_by_xpath(self.path["osoba"])
            self.wait_for_hidden_element_by_xpath(self.path["f_firma"])
            self.wait_for_hidden_element_by_xpath(self.path["f_adres"])
            self.wait_for_hidden_element_by_xpath(self.path["f_miejscowosc_kod"])
            self.wait_for_hidden_element_by_xpath(self.path["f_vat"][lang])

    def test_prywatny_firma_przelaczanie_gdy_brak_firmy(self):
        """
        Sprawdza czy formularz poprawnie przełącza pola dla dla osoby prywatnej lub firmy
        """
        for lang in ["en", "pl"]:
            szkolenie, termin = self.termin_lang(lang=lang, obiady="obiady-wliczone")
            szkolenie.mozliwa_rejestracja_jako_firma = False
            szkolenie.save()

            self.selenium.get(
                self.live_server_url
                + reverse(
                    "zgloszenie", kwargs={"slug": szkolenie.slug, "language": lang}
                )
            )
            self.wait_for_hidden_element_by_xpath(self.path["chce_obiady"])
            try:
                self.selenium.find_element_by_xpath(self.path["firma"]).click()
            except NoSuchElementException:
                pass
                # Jest ok
            else:
                self.fail("Firma powinna byc ukryta!")

            self.wait_for_hidden_element_by_xpath(self.path["grupa"])
            self.wait_for_hidden_element_by_xpath(self.path["osoba"])
            self.wait_for_hidden_element_by_xpath(self.path["f_firma"])
            self.wait_for_hidden_element_by_xpath(self.path["f_adres"])
            self.wait_for_hidden_element_by_xpath(self.path["f_miejscowosc_kod"])
            self.wait_for_hidden_element_by_xpath(self.path["f_vat"][lang])

            self.selenium.find_element_by_xpath(self.path["prywatny"]).click()

            self.wait_for_hidden_element_by_xpath(self.path["grupa"])
            self.wait_for_hidden_element_by_xpath(self.path["osoba"])
            self.wait_for_hidden_element_by_xpath(self.path["f_firma"])
            self.wait_for_hidden_element_by_xpath(self.path["f_adres"])
            self.wait_for_hidden_element_by_xpath(self.path["f_miejscowosc_kod"])
            self.wait_for_hidden_element_by_xpath(self.path["f_vat"][lang])

    def test_za_kurs_zaplace(self):
        # Kurs zawosowy moze miec opcje 5 rat na żądanie.
        tag_dlugosc = TagDlugosc.objects.get(slug="kurs-zawodowy")
        kurs = SzkolenieFactory.create(tag_dlugosc=tag_dlugosc, wiele_rat=True)

        szkolenie, termin = self.termin_lang(lang="pl", szkolenie=kurs)

        self.selenium.get(
            self.live_server_url
            + reverse("zgloszenie", kwargs={"slug": szkolenie.slug, "language": "pl"})
        )

        self.assertTrue(
            self.selenium.find_element_by_xpath(
                '//input[@name="za_kurs_zaplace"][@value="10"]'
            )
        )

        # Wylaczamy wiele_wart
        termin.szkolenie.wiele_rat = False
        termin.szkolenie.save()

        self.selenium.get(
            self.live_server_url
            + reverse("zgloszenie", kwargs={"slug": szkolenie.slug, "language": "pl"})
        )

        try:
            self.selenium.find_element_by_xpath(
                '//input[@name="za_kurs_zaplace"][@value="10"]'
            )
        except NoSuchElementException:
            pass
            # Jest ok
        else:
            self.fail("Pltanosc ratalana powinna byc niedostepna!")

    def test_ukryj_raty_gdy_wybrano_firme(self):
        # Raty mają być niedostepne dla firm.
        tag_dlugosc = TagDlugosc.objects.get(slug="kurs-zawodowy")
        kurs = SzkolenieFactory.create(tag_dlugosc=tag_dlugosc, wiele_rat=True)

        szkolenie, termin = self.termin_lang(lang="pl", szkolenie=kurs)

        self.selenium.get(
            self.live_server_url
            + reverse("zgloszenie", kwargs={"slug": szkolenie.slug, "language": "pl"})
        )

        # Raty są dostępne
        for rata in (3, 10):
            self.assertTrue(
                self.selenium.find_element_by_xpath(
                    '//input[@name="za_kurs_zaplace"][@value="{0}"]'.format(rata)
                )
            )

        # Wybieramy firmę
        self.selenium.find_element_by_xpath(
            '//input[@name="prywatny"][@value="0"]'
        ).click()

        for rata in (3, 10):
            try:
                self.wait_for_hidden_element_by_xpath(
                    '//input[@name="za_kurs_zaplace"][@value="{0}"]'.format(rata)
                )
            except TimeoutException:
                self.fail("Pltanosc ratalana powinna byc niedostepna!")

        # Przełączamy na osobe prywatną
        self.selenium.find_element_by_xpath(
            '//input[@name="prywatny"][@value="1"]'
        ).click()

        # Raty znów dostępne
        for rata in (3, 10):
            self.assertTrue(
                self.selenium.find_element_by_xpath(
                    '//input[@name="za_kurs_zaplace"][@value="{0}"]'.format(rata)
                )
            )

    def test_pokaz_kraj_gdy_wybrano_platnosc_ratalna(self):
        tag_dlugosc = TagDlugosc.objects.get(slug="kurs-zawodowy")
        kurs = SzkolenieFactory.create(tag_dlugosc=tag_dlugosc, wiele_rat=True)

        szkolenie, termin = self.termin_lang(lang="pl", szkolenie=kurs)

        self.selenium.get(
            self.live_server_url
            + reverse("zgloszenie", kwargs={"slug": szkolenie.slug, "language": "pl"})
        )

        self.selenium.find_element_by_xpath(self.path["prywatny"]).click()
        self.selenium.find_element_by_xpath(self.path["raty_no_3"]).click()

        # Pole panstwo powinno byc teraz widoczne
        self.assertTrue(
            self.wait_for_visible_element_by_xpath(self.path["raty_panstwo"])
        )

    def test_dane_do_rat_gdy_kraj_to_polska(self):
        tag_dlugosc = TagDlugosc.objects.get(slug="kurs-zawodowy")
        kurs = SzkolenieFactory.create(tag_dlugosc=tag_dlugosc, wiele_rat=True)

        szkolenie, termin = self.termin_lang(lang="pl", szkolenie=kurs)

        self.selenium.get(
            self.live_server_url
            + reverse("zgloszenie", kwargs={"slug": szkolenie.slug, "language": "pl"})
        )

        self.selenium.find_element_by_xpath(self.path["prywatny"]).click()
        self.selenium.find_element_by_xpath(self.path["raty_no_3"]).click()

        panstwo_select = Select(
            self.wait_for_visible_element_by_xpath(self.path["raty_panstwo"])
        )
        panstwo_select.select_by_visible_text("Polska")

        self.assertTrue(self.wait_for_visible_element_by_xpath(self.path["raty_pesel"]))

        self.assertTrue(
            self.wait_for_visible_element_by_xpath(self.path["raty_numer_dokumentu"])
        )

    def test_dane_do_rat_gdy_kraj_inny_niz_polska(self):
        tag_dlugosc = TagDlugosc.objects.get(slug="kurs-zawodowy")
        kurs = SzkolenieFactory.create(tag_dlugosc=tag_dlugosc, wiele_rat=True)

        szkolenie, termin = self.termin_lang(lang="pl", szkolenie=kurs)

        self.selenium.get(
            self.live_server_url
            + reverse("zgloszenie", kwargs={"slug": szkolenie.slug, "language": "pl"})
        )

        self.selenium.find_element_by_xpath(self.path["prywatny"]).click()
        self.selenium.find_element_by_xpath(self.path["raty_no_3"]).click()

        panstwo_select = Select(
            self.wait_for_visible_element_by_xpath(self.path["raty_panstwo"])
        )
        panstwo_select.select_by_visible_text("inny")

        self.assertTrue(
            self.wait_for_visible_element_by_xpath(self.path["raty_nazwa_dokumentu"])
        )

        self.assertTrue(
            self.wait_for_visible_element_by_xpath(self.path["raty_numer_dokumentu"])
        )

    def test_ustaw_termin_jako_domyslny_przeslany_w_url(self):
        szkolenie = SzkolenieFactory.create()
        terminy = TerminSzkoleniaFactory.create_batch(3, szkolenie=szkolenie)

        # Wybieramy poszczegolny termin
        for termin in terminy:
            self.selenium.get(
                self.live_server_url
                + reverse(
                    "zgloszenie", kwargs={"slug": szkolenie.slug, "language": "pl"}
                )
                + "?t={0}".format(termin.pk)
            )

            select = Select(self.selenium.find_element_by_id("id_termin"))
            value = select.first_selected_option.get_attribute("value")

            self.assertEqual(int(value), termin.pk)

        # Teraz wpisujemy "smieci"
        self.selenium.get(
            self.live_server_url
            + reverse("zgloszenie", kwargs={"slug": szkolenie.slug, "language": "pl"})
            + "?t=ółążźć"
        )

        select = Select(self.selenium.find_element_by_id("id_termin"))
        value = select.first_selected_option.get_attribute("value")

        self.assertIn(int(value), [t.pk for t in terminy])


class ContextProcessorsTestCase(ALXTestCase):
    def test_dziwny_user_agent_nie_wywala_strony(self):
        """
        Sprawdza, czy user agent z dziwnymi znakami nie powoduje error 500.
        Test regresyjny dla procesora kontekstu chrome_xp.
        """
        response = self.client.get(
            "/", HTTP_USER_AGENT=b"Sogou web spider/4.0P\xd3\x02\xbfA"
        )
        self.assertEqual(response.status_code, 200)


@override_settings(
    DOMENY_DLA_JEZYKOW={
        "pl": "pl-domena",
        "en": "en-domena",
    }
)
class LinkRejestracyjnyTestCase(ALXTestCase):
    def setUp(self):
        super().setUp()
        os.environ["NORECAPTCHA_TESTING"] = "True"
        self.szkolenie = SzkolenieFactory.create(
            language="pl", nazwa="ABCX", slug="abc"
        )

    def test_send_registration_link_pl(self):
        response = self.client.post(
            "/pl/abc/send_registration_link/",
            {"email": "<EMAIL>", "g-recaptcha-response": "PASSED"},
            follow=True,
        )
        self.assertEqual(response.status_code, 200)

        # autoresponder
        self.assertEqual(len(mail.outbox), 1)

        body = mail.outbox[0].body
        subject = mail.outbox[0].subject

        self.assertEqual(subject, "ALX - link rejestracyjny na szkolenie: ABCX")
        self.assertIn("http://pl-domena/zgloszenie/abc/", body)
        self.assertIn("ABCX", body)
        self.assertIn("http://pl-domena/szkolenia/abc/", body)

        self.assertEqual(mail.outbox[0].to[0], "<EMAIL>")

        self.assertEqual(AutoresponderLog.objects.all().count(), 1)

    def test_send_registration_link_en(self):
        self.szkolenie.language = "en"
        self.szkolenie.save()

        response = self.client.post(
            "/en/abc/send_registration_link/",
            {"email": "<EMAIL>", "g-recaptcha-response": "PASSED"},
            follow=True,
        )
        self.assertEqual(response.status_code, 200)

        # autoresponder
        self.assertEqual(len(mail.outbox), 1)

        body = mail.outbox[0].body
        subject = mail.outbox[0].subject

        self.assertEqual(subject, "ALX - registration link for the training: ABCX")
        self.assertIn("http://en-domena/en/order/abc/", body)
        self.assertIn("ABCX", body)
        self.assertIn("http://en-domena/en/courses/abc/", body)

    def test_szkolenie_does_not_exist(self):
        response = self.client.post("/pl/abc2/send_registration_link/", {}, follow=True)
        self.assertEqual(response.status_code, 404)

    def test_no_post(self):
        response = self.client.get("/pl/abc/send_registration_link/", follow=True)
        self.assertEqual(response.status_code, 405)

    def test_no_captcha(self):
        response = self.client.post(
            "/pl/abc/send_registration_link/",
            {
                "email": "<EMAIL>",
            },
            follow=True,
        )
        self.assertEqual(response.status_code, 400)

    def test_worng_email(self):
        response = self.client.post(
            "/pl/abc/send_registration_link/",
            {"email": "email@email", "g-recaptcha-response": "PASSED"},
            follow=True,
        )
        self.assertEqual(response.status_code, 400)


@override_settings(
    ALLOWED_MAIL_CONTACT_FORM_TO_ADDRESSES=["<EMAIL>"],
    MAIL_CONTACT_FORM_TO_ADDRESS="<EMAIL>",
)
class FormularzKontaktowyTestCase(ALXTestCase):
    def setUp(self):
        super().setUp()
        os.environ["NORECAPTCHA_TESTING"] = "True"

    def test_post_contact_form(self):
        response = self.client.post(
            "/pl/post_contact_form/",
            {
                "email_telefon": "<EMAIL>",
                "tresc": "abc",
                "g-recaptcha-response": "PASSED",
            },
            follow=True,
        )
        self.assertEqual(response.status_code, 200)

        # biuro + autoresponder
        self.assertEqual(len(mail.outbox), 2)

        mail.outbox = []

        # bez autorespondera
        response = self.client.post(
            "/pl/post_contact_form/",
            {
                "email_telefon": "*********",
                "tresc": "abc",
                "g-recaptcha-response": "PASSED",
            },
            follow=True,
        )
        self.assertEqual(response.status_code, 200)

        # biuro
        self.assertEqual(len(mail.outbox), 1)

    def test_no_captcha(self):
        response = self.client.post(
            "/pl/post_contact_form/",
            {
                "email_telefon": "<EMAIL>",
                "tresc": "abc",
            },
            follow=True,
        )
        self.assertEqual(response.status_code, 404)

    def test_post_contact_form_when_sendto_field(self):
        response = self.client.post(
            "/pl/post_contact_form/",
            {
                "email_telefon": "<EMAIL>",
                "tresc": "abc",
                "g-recaptcha-response": "PASSED",
                "sendto": "<EMAIL>",
            },
            follow=True,
        )
        self.assertEqual(response.status_code, 200)

        self.assertEqual(mail.outbox[0].to[0], "<EMAIL>")

        mail.outbox = []

        response = self.client.post(
            "/pl/post_contact_form/",
            {
                "email_telefon": "<EMAIL>",
                "tresc": "abc",
                "g-recaptcha-response": "PASSED",
                "sendto": "<EMAIL>",
            },
            follow=True,
        )
        self.assertEqual(response.status_code, 200)

        self.assertEqual(mail.outbox[0].to[0], "<EMAIL>")


class FormularzKontaktowyLiveServerTestCase(ALXLiveServerTestCase):
    def setUp(self):
        super().setUp()

        tag_dlugosc = TagDlugosc.objects.get(slug="szkolenie")
        szkolenie = SzkolenieFactory.create(tag_dlugosc=tag_dlugosc, language="pl")

        self.szkolenie_slug = szkolenie.slug
        self.selenium.get(self.live_server_url + "/szkolenia/" + szkolenie.slug)

        os.environ["NORECAPTCHA_TESTING"] = "True"

    def open_inner(self):
        zsp = self.wait_for_visible_element_by_xpath(
            "//div[@class='lp-rightSlidePanel-left']"
        )
        hov = ActionChains(self.selenium).move_to_element(zsp)
        hov.perform()

    def get_fill_and_send_zdp(self, form_data):
        self.open_inner()

        self.wait_for_visible_element_by_xpath("//input[@type='submit'][1]")
        self.fill_fields_by_name(**form_data)
        self.click_by_xpath("//div[@id='qcf-inner-contact']//button[@type='submit'][1]")

    @unittest.skip("skipped")
    def test_zdp_jest_widoczne(self):
        self.open_inner()

        element = self.wait_for_element_by_xpath("//div[@id='qcf-inner-contact']/p[1]")
        self.assertEqual(
            element.text,
            "Tu możesz zadać niezobowiązujące i szybkie pytanie na temat szkolenia",
        )

    @unittest.skip("skipped")
    def test_zdp_niepoprawnie_wypelnione_pola_zmieniaja_kolor(self):
        """
        Jeżeli wymagane pola w fomularzu nie zostaną wypełnione, to tło zmienia kolor na
        czerwony. Walidacja robiona jest na poziomie JS.
        """

        error_color = Color.from_string("#ffe4e4").rgba
        form_data = {"email_telefon": "", "tresc": ""}

        self.get_fill_and_send_zdp(form_data)

        element = self.wait_for_element_by_xpath("//input[@name='email_telefon'][1]")
        self.assertEqual(element.value_of_css_property("background-color"), error_color)

        element = self.wait_for_element_by_xpath("//textarea[@name='tresc'][1]")

        self.assertEqual(element.value_of_css_property("background-color"), error_color)

    @unittest.skip("skipped")
    def test_zdp_po_wypelneniu_formularza_telefonem_pokazuje_sie_newsletter(self):
        """
        Po podaniu numeru telefonu w formularzu nie pokazuje się zaproszenie na
        newsletter, a jedynie podziękowanie. Wysyłany jest mail do automatu.
        """
        form_data = {"email_telefon": "601100200", "tresc": "Treść"}

        self.get_fill_and_send_zdp(form_data)
        self.open_inner()
        element = self.wait_for_visible_element_by_xpath(
            "//div[@id='qcf-inner-thanks']/h3[1]"
        )
        self.assertTrue(element.is_displayed())

    @unittest.skip("skipped")
    def test_zdp_po_wypelneniu_formularza_mailem_pokazuje_sie_newsletter(self):
        """
        Po podaniu maila w formularzu pokazuje się zaproszenie na newsletter.
        Po zapisaniu się na newsletter użytkownik powinien zostać dopisany do listy.
        Wybór potwierdzający zapisanie się jest domyślny. Przy zapisaniu do bazy
        zapisywany jest również referer strony z jakiej nastąpił zapis.
        """
        form_data = {"email_telefon": "<EMAIL>", "tresc": "Treść"}
        self.get_fill_and_send_zdp(form_data)
        element = self.wait_for_visible_element_by_xpath(
            "//div[@id='qcf-inner-newsletter']/h3[1]"
        )
        self.assertTrue(element.is_displayed())

        element = self.wait_for_visible_element_by_xpath(
            "//div[@id='qcf-inner-newsletter']//button[1]"
        )
        element.click()

        self.open_inner()

        element = self.wait_for_visible_element_by_xpath(
            "//div[@id='qcf-inner-thanks']/p"
        )
        self.assertEqual(element.text, "Twoja wiadomość została przesłana")

        odbiorca = Odbiorca.objects.filter(email=form_data["email_telefon"]).get()
        self.assertIn("/szkolenia/" + self.szkolenie_slug, odbiorca.referer)
        self.assertEqual(odbiorca.zrodlo_kontaktu, "box-pytanie")

    @unittest.skip("skipped")
    def test_zdp_po_odhaczeniu_opcji_newsletter_nie(self):
        """
        Po wysłaniu ZDP, gdy pojawi się pytanie czy użytkownik chce newsletter i użytkownik wybierze "Nie, dziękuję.",
        nie powinien w bazie tworzyć się odbiorca i nie powinien produkować się dodatkowy e-mail do użytkownika.
        """
        mail.outbox = []
        klient_mail = "test{0}@alx.pl".format(random.random())
        form_data = {"email_telefon": klient_mail, "tresc": "Treść"}

        self.get_fill_and_send_zdp(form_data)
        element = self.wait_for_visible_element_by_xpath(
            '//input[@id="radio-newsletter_subscribe-n"]'
        )
        element.click()
        element = self.wait_for_visible_element_by_xpath(
            "//div[@id='qcf-inner-newsletter']//button[1]"
        )
        element.click()

        self.open_inner()

        element = self.wait_for_visible_element_by_xpath(
            "//div[@id='qcf-inner-thanks']/p"
        )
        self.assertEqual(element.text, "Twoja wiadomość została przesłana")

        odbiorca = Odbiorca.objects.filter(email=klient_mail)
        self.assertEqual(len(odbiorca), 0)
        # biuro + autoresponder
        self.assertEqual(len(mail.outbox), 2)


class FormularzZaproponujTerminLiveServerTestCase(ALXLiveServerTestCase, CaptchaMixin):
    def setUp(self):
        super().setUp()

        szkolenie = SzkolenieFactory()
        self.szkolenie_slug = szkolenie.slug
        self.selenium.get(self.live_server_url + "/zaproponuj_termin/" + szkolenie.slug)

    def test_zaproponuj_termin_po_formularzu_zapis_na_newsletter(self):
        """
        Po wypełnieniu i wysłaniu formularza pojawia się strona z
        podziękowaniem i pytamiem o newsletter.
        """
        polikrates_email = "polikrates{0}@sredni.pl".format(random.random())
        form_data = {
            "imie_nazwisko": "Polikrates Średni",
            "email": polikrates_email,
            "kontakt": "601345876",
            "captcha": self.pobierz_captche()[1],
        }
        self.fill_fields_by_name(**form_data)
        self.click_by_xpath(
            "//form[@class='formularz_zgloszeniowy form']//input[@type='submit']"
        )
        self.selenium.find_element_by_id("radio-newsletter_subscribe-y").click()
        self.wait_for_visible_element_by_xpath(
            "//form[contains(@class, 'form-newsletter')]"
        )
        self.click_by_xpath(
            "//form[contains(@class, 'form-newsletter')]//input[@value='Zapisz']"
        )
        self.wait_for_hidden_element_by_xpath(
            "//form[contains(@class, 'form-newsletter')]//input[@value='Zapisz']"
        )

        odbiorca = Odbiorca.objects.get(email=form_data["email"])
        self.assertIn("/zaproponuj_termin/" + self.szkolenie_slug, odbiorca.referer)
        self.assertEqual("termin", odbiorca.zrodlo_kontaktu)


@override_settings(
    task_eager_propagates=True,
    task_always_eager=True,
    BROKER_BACKEND="memory",
)
class FormularzZaproponujTerminTestCase(ALXTestCase, CaptchaMixin):
    def test_przeslij_propozycje_terminu(self):
        """
        Wyślij request POST na url formularza pl/en i sprawdź, czy
        zostały wysłane maile.
        """
        for lang in ["pl", "en"]:
            mail.outbox = []
            szkolenie = SzkolenieFactory(language=lang)
            form_data = {
                "imie_nazwisko": "Polikrates Średni",
                "email": "<EMAIL>",
                "captcha": self.pobierz_captche(),
            }
            url_zgloszenia = reverse(
                "zaproponuj_termin",
                kwargs={"slug": szkolenie.slug, "language": szkolenie.language},
            )
            url_podziekowania = reverse(
                "dziekujemy_za_propozycje_terminu",
                kwargs={"language": szkolenie.language},
            )
            response = self.client.post(url_zgloszenia, form_data)
            self.assertRedirects(response, url_podziekowania)
            # biuro + autoresponder
            self.assertEqual(len(mail.outbox), 2)
            self.assertIn("jezyk => %s" % lang, mail.outbox[0].body)

    def test_propozycja_terminu_dla_nieaktywnego_szkolenia(self):
        szkolenie = SzkolenieFactory(aktywne=False)

        url_zgloszenia = reverse(
            "zaproponuj_termin",
            kwargs={"slug": szkolenie.slug, "language": szkolenie.language},
        )

        response = self.client.get(url_zgloszenia)
        self.assertEqual(response.status_code, 404)

    def test_propozycja_terminu_dla_wariantu_szkolenia(self):
        szkolenie = SzkolenieWariantFactory.create().wariant

        url_zgloszenia = reverse(
            "zaproponuj_termin",
            kwargs={"slug": szkolenie.slug, "language": szkolenie.language},
        )

        response = self.client.get(url_zgloszenia)
        self.assertEqual(response.status_code, 200)


@unittest.skip("powiadomienie wylaczone")
class PowiadamiaczkaOZainteresowanychTestCase(ALXTestCase):
    def test_potencjalnie_zainteresowani_to_nie_zrezygnowani(self):
        from www.tasks import (  # tu import bo inaczej sa konflikty
            poinformuj_o_potencjalnie_zainteresowanych,
        )

        data_od_dzisiaj = datetime.date.today()
        termin_kiedys = data_od_dzisiaj + datetime.timedelta(days=-50)
        termin_przyszly = data_od_dzisiaj + datetime.timedelta(days=5)
        termin = TerminSzkoleniaFactory(termin=termin_przyszly)
        szkolenie = termin.szkolenie
        termin_kiedys = TerminSzkoleniaFactory(
            termin=termin_kiedys, szkolenie=szkolenie, odbylo_sie=False
        )
        waluta = WalutaFactory()

        UczestnikFactory(
            termin=termin_kiedys,
            waluta=waluta,
            imie_nazwisko="Jan Zrezygnowany",
            status=4,
        )

        # Tworzymy też uczestnika potencjalnie zainteresowanego, żeby mieć
        # pewność, że jakikolwiek mail się wyśle.
        UczestnikFactory(
            termin=termin_kiedys, waluta=waluta, imie_nazwisko="Jan Chętny", status=5
        )

        mail.outbox = []
        poinformuj_o_potencjalnie_zainteresowanych(termin.id)
        mesg = mail.outbox[0].body

        error_msg = "W mailu nie powinien pojawić się zrezygnowany."
        self.assertNotIn(", zrezygnował", mesg, error_msg)
        self.assertNotIn("Zrezygnowany", mesg, error_msg)


class PrzelaczanieJezykaALXInternationalLiveServerTestCase(ALXLiveServerTestCase):
    def setUp(self):
        super().setUp()
        self.selenium.get(self.live_server_url + "/en/")

    def test_klikniecie_alx_international_w_stopce_pokazuje_alx_poland(self):
        """Po kliknięciu w Link ALX International w stopce, na stronie ma pojawić się ALX Poland"""
        alx_poland_xpath = "//ul[@id='international_site']/li/a"
        alx_international_link = self.wait_for_visible_element_by_xpath(
            "//a[@id='international_toggle']"
        )

        self.assertEqual(alx_international_link.text, "ALX International")
        element = self.wait_for_element_by_xpath(alx_poland_xpath)
        self.assertFalse(element.is_displayed())
        alx_international_link.click()
        element = self.wait_for_visible_element_by_xpath(alx_poland_xpath)
        self.assertTrue(element.is_displayed())
        self.assertEqual(element.text, "ALX Poland")


class TerminSzkoleniaTestCase(ALXTestCase):
    def _term_instance_as_dict(self, instance):
        data = instance.__dict__.copy()

        data["szkolenie"] = data["szkolenie_id"]
        data["lokalizacja"] = data["lokalizacja_id"]
        data["sala"] = data["sala_id"]
        return data

    def test_wszyscy_prowadzacy(self):
        termin = TerminSzkoleniaFactory.create()
        self.assertEqual(len(list(set(termin.wszyscy_prowadzacy))), 1)

        DzienSzkoleniaFactory.create_batch(
            2, terminszkolenia=termin, prowadzacy=termin.prowadzacy
        )
        DzienSzkoleniaFactory.create_batch(3, terminszkolenia=termin)

        self.assertEqual(len(list(set(termin.wszyscy_prowadzacy))), 4)

    def test_wyslane_do_zainteresowanych_z_innych_miast(self):
        termin = TerminSzkoleniaFactory()
        self.assertFalse(termin.wyslane_do_zainteresowanych_z_innych_miast())

        TerminSzkoleniaLogFactory.create(term=termin, log_type="xyz")
        self.assertFalse(termin.wyslane_do_zainteresowanych_z_innych_miast())

        TerminSzkoleniaLogFactory.create(
            term=termin, log_type="potencjalnie_zainteresowani_z_innych_miast"
        )
        self.assertTrue(termin.wyslane_do_zainteresowanych_z_innych_miast())

    def test_termin_domyslnie_nie_jest_reklamowany(self):
        termin = TerminSzkoleniaFactory()
        self.assertFalse(termin.czy_reklamowac)

    def test_termin_potwierdzony_jest_reklamowany(self):
        termin = TerminSzkoleniaFactory(odbylo_sie=True)
        termin.clean()
        self.assertTrue(termin.czy_reklamowac)

    def test_szkolenie_nie_posiada_akredytacji_w_lokalizacji(self):
        lokalizacja = LokalizacjaFactory()
        szkolenie = SzkolenieFactory()
        szkolenie.akredytacje.add(lokalizacja)
        termin = TerminSzkolenia(szkolenie=szkolenie)
        self.assertFalse(termin.is_posiada_akredytacje())

    def test_szkolenie_posiada_akredytacje_w_lokalizacji(self):
        lokalizacja = LokalizacjaFactory()
        szkolenie = SzkolenieFactory()
        szkolenie.akredytacje.add(lokalizacja)
        termin = TerminSzkolenia(lokalizacja=lokalizacja, szkolenie=szkolenie)
        self.assertTrue(termin.is_posiada_akredytacje())

    def test_sprawdzanie_ciaglosci_dla_nieciaglego_terminu(self):
        """
        Sprawdza, czy termin nieciągły wie, że jest nieciągły.
        """
        szkolenie = SzkolenieFactory(czas_godziny=None, czas_dni=None)
        termin = TerminSzkoleniaFactory(
            termin=datetime.date(2000, 1, 1),
            szkolenie=szkolenie,
            daty_szczegolowo="2000-01-01,2000-02-05",
        )
        self.assertFalse(termin.ciagly())

    def test_sprawdzanie_ciaglosci_dla_ciaglego_terminu(self):
        """Sprawdza, czy termin ciągły wie, że jest ciągły."""
        szkolenie = SzkolenieFactory(czas_godziny=None, czas_dni=None)
        termin = TerminSzkoleniaFactory(
            termin=datetime.date(2000, 1, 1),
            szkolenie=szkolenie,
            daty_szczegolowo="2000-01-01,2000-01-02",
        )
        self.assertTrue(termin.ciagly())

    def test_nie_da_sie_zrobic_polskiego_terminu_za_granica(self):
        szkolenie = SzkolenieFactory(language="pl")
        panstwo_niepolskie = Panstwo.objects.exclude(nazwa="Polska").order_by("?")[0]
        lokalizacja_zagraniczna = LokalizacjaFactory(panstwo=panstwo_niepolskie)
        termin = TerminSzkoleniaFactory(
            termin=datetime.date(2000, 1, 1),
            daty_szczegolowo="2000-01-01,2000-01-02",
            szkolenie=szkolenie,
            lokalizacja=lokalizacja_zagraniczna,
        )
        bledy = ["Nie można zrobić polskiego szkolenia za granicą"]
        self.assertRaisesRegex(ValidationError, str(bledy), termin.clean)

    def test_da_sie_zrobic_angielski_termin_wszedzie(self):
        szkolenie = SzkolenieFactory(language="en", czas_dni=2)
        lokalizacja_polska = LokalizacjaFactory()
        panstwo_niepolskie = Panstwo.objects.exclude(nazwa="Polska")
        lokalizacje = [LokalizacjaFactory(panstwo=p_n) for p_n in panstwo_niepolskie]
        lokalizacje.append(lokalizacja_polska)
        for lokalizacja in lokalizacje:
            termin = TerminSzkoleniaFactory(
                termin=datetime.date(2000, 1, 1),
                daty_szczegolowo="2000-01-01,2000-01-02",
                szkolenie=szkolenie,
                lokalizacja=lokalizacja,
            )
        termin.clean()

    def test_da_sie_zrobic_polski_termin_w_polsce(self):
        szkolenie = SzkolenieFactory(language="pl", czas_dni=2)
        lokalizacja_polska = LokalizacjaFactory()
        termin = TerminSzkoleniaFactory(
            termin=datetime.date(2000, 1, 1),
            daty_szczegolowo="2000-01-01,2000-01-02",
            szkolenie=szkolenie,
            lokalizacja=lokalizacja_polska,
        )
        termin.clean()

    def test_bledna_liczba_dni_szkolenia(self):
        szkolenie = SzkolenieFactory(language="pl", czas_dni=3)
        termin = TerminSzkoleniaFactory(
            termin=datetime.date(2000, 1, 1),
            daty_szczegolowo="2000-01-01,2000-01-02",
            szkolenie=szkolenie,
        )

        try:
            termin.clean()
        except ValidationError as err:
            self.assertEqual(
                err.message,
                "Liczba podanych dat szczegółowych (2) nie zgadza się z "
                "liczbą wymaganą dla szkolenia (3).",
            )
        else:
            self.fail("no exception")

    def test_bledna_liczba_dni_kursu(self):
        szkolenie = KursFactory(czas_dni_kurs=5)
        termin = TerminSzkoleniaFactory(
            termin=datetime.date(2000, 1, 1),
            daty_szczegolowo="2000-01-01,2000-01-02",
            szkolenie=szkolenie,
        )

        try:
            termin.clean()
        except ValidationError as err:
            self.assertEqual(
                err.message,
                "Liczba podanych dat szczegółowych (2) nie zgadza się z "
                "liczbą wymaganą dla kursu (5).",
            )
        else:
            self.fail("no exception")

    def test_bledna_liczba_dni_kursu_wartosc_nadpisana_w_terminie(self):
        szkolenie = KursFactory(czas_dni_kurs=5)
        termin = TerminSzkoleniaFactory(
            termin=datetime.date(2000, 1, 1),
            daty_szczegolowo="2000-01-01,2000-01-02",
            szkolenie=szkolenie,
            czas_dni_kurs=3,
        )

        try:
            termin.clean()
        except ValidationError as err:
            self.assertEqual(
                err.message,
                "Liczba podanych dat szczegółowych (2) nie zgadza się z "
                "liczbą wymaganą dla kursu (3).",
            )
        else:
            self.fail("no exception")

    def test_poprawna_liczba_dni_kursu_wartosc_nadpisana_w_terminie(self):
        szkolenie = KursFactory(czas_dni_kurs=5)
        termin = TerminSzkoleniaFactory(
            termin=datetime.date(2000, 1, 1),
            daty_szczegolowo="2000-01-01,2000-01-02",
            szkolenie=szkolenie,
            czas_dni_kurs=2,
        )
        termin.clean()

    def test_liczba_dni_kursu_brak_walidacji(self):
        szkolenie = KursFactory()
        termin = TerminSzkoleniaFactory(
            termin=datetime.date(2000, 1, 1),
            daty_szczegolowo="2000-01-01,2000-01-02",
            szkolenie=szkolenie,
        )
        termin.clean()

    def test_get_program_szkolenia(self):
        e = TerminSzkoleniaFactory()

        self.assertFalse(e.program_szkolenia)
        self.assertEqual(e.get_program_szkolenia(), "")

        # Usuwamy program szkolenia z obiektu szkolenia
        e.szkolenie.program_szkolenia = "program szkolenia"
        e.save()

        self.assertEqual(e.get_program_szkolenia(), "program szkolenia")

        # Nadpisujemy `program_szkolenia` w obikecie terminu.
        e.program_szkolenia = "Super program"
        e.save()

        self.assertEqual(e.get_program_szkolenia(), "Super program")

    def test_get_szablon_certyfikatu(self):
        e = TerminSzkoleniaFactory()

        self.assertFalse(e.szablon_certyfikatu)
        self.assertFalse(e.szkolenie.szablon_certyfikatu)
        # Certyfikat z pliku
        self.assertTrue(e.get_szablon_certyfikatu())

        # Nadpisujemy `szablon_certyfikatu` w obikecie szkolenia.
        e.szkolenie.szablon_certyfikatu = "{{ uczestnik }}"
        e.save()

        self.assertEqual(e.get_szablon_certyfikatu(), "{{ uczestnik }}")

        # Nadpisujemy `szablon_certyfikatu` w obikecie terminu.
        e.szablon_certyfikatu = "{{ uczestnik }} 1"
        e.save()

        self.assertEqual(e.get_szablon_certyfikatu(), "{{ uczestnik }} 1")

    def test_allow_to_generate_certificates(self):
        e = TerminSzkoleniaFactory()

        # Termin w przyszłości - False, odbył się - False
        self.assertFalse(e.allow_to_generate_certificates())
        self.assertFalse(e.allow_to_generate_certificates(print_only=True))

        # Termin w przeszłości, ale nie odbył się - False
        e.termin -= datetime.timedelta(days=365)
        e.save()
        self.assertFalse(e.allow_to_generate_certificates())
        self.assertFalse(e.allow_to_generate_certificates(print_only=True))

        # Termin w przeszłości, odbył się, nie ma programu szkolenia - True
        e.odbylo_sie = True
        e.szkolenie.program_szkolenia = ""
        e.save()
        self.assertTrue(e.allow_to_generate_certificates())
        self.assertTrue(e.allow_to_generate_certificates(print_only=True))

        # Termin w przeszłości, odbył się, jest programu szkolenia - True
        e.szkolenie.program_szkolenia = "1"
        e.save()
        self.assertTrue(e.allow_to_generate_certificates())
        self.assertTrue(e.allow_to_generate_certificates(print_only=True))

        # Termin w przyszłości, odbył się, jest programu szkolenia - True
        e.termin += datetime.timedelta(days=600)
        e.save()
        self.assertTrue(e.allow_to_generate_certificates())
        self.assertTrue(e.allow_to_generate_certificates(print_only=True))

        # Certyfikaty zostały wysłane
        e.notyfikacje_o_certyfikatach = datetime.datetime.now()
        e.save()
        self.assertFalse(e.allow_to_generate_certificates())
        self.assertTrue(e.allow_to_generate_certificates(print_only=True))

    def test_date_range(self):
        e = TerminSzkoleniaFactory(termin=datetime.date(2014, 11, 11))

        # Pojedyncza data
        self.assertEqual(len(e.date_range()), 1)

        e = TerminSzkoleniaFactory(
            termin=datetime.date(2014, 11, 11),
            daty_szczegolowo="2014-11-11,2014-11-12,2014-11-13,2014-11-16",
        )

        # Wiele data
        self.assertEqual(len(e.date_range()), 2)

    def test_termin_szkolenia_admin_form(self):
        today = datetime.date.today()

        ProwadzacyFactory.create_batch(9)
        prowadzacy = ProwadzacyFactory.create()

        instance = TerminSzkoleniaFactory.create(termin=today, prowadzacy=prowadzacy)
        form = TerminSzkoleniaAdminForm(instance=instance)

        self.assertEqual(form.fields["prowadzacy"].queryset.count(), 10)

        # Ustawiamy prowadzacego jako nieaktywnego
        prowadzacy.pracowal_do = today
        prowadzacy.save()

        form = TerminSzkoleniaAdminForm(instance=instance)

        self.assertEqual(form.fields["prowadzacy"].queryset.count(), 10)

        instance = TerminSzkoleniaFactory.create(
            termin=today + datetime.timedelta(days=1), prowadzacy=prowadzacy
        )
        form = TerminSzkoleniaAdminForm(instance=instance)

        self.assertEqual(form.fields["prowadzacy"].queryset.count(), 9)

        # Jako nowy termin
        form = TerminSzkoleniaAdminForm()
        self.assertEqual(form.fields["prowadzacy"].queryset.count(), 9)

    def test_dzien_szkolenia_admin_inline_form_ograniczenie_prowadzacych(self):
        today = datetime.date.today()

        ProwadzacyFactory.create_batch(9)
        prowadzacy = ProwadzacyFactory.create()

        termin = TerminSzkoleniaFactory.create(termin=today, prowadzacy=prowadzacy)
        instance = DzienSzkoleniaFactory(terminszkolenia=termin, prowadzacy=prowadzacy)
        form = DzienSzkoleniaAdminInlineForm(instance=instance)

        self.assertEqual(form.fields["prowadzacy"].queryset.count(), 10)

        # Ustawiamy prowadzacego jako nieaktywnego
        prowadzacy.pracowal_do = today
        prowadzacy.save()

        form = DzienSzkoleniaAdminInlineForm(instance=instance)

        self.assertEqual(form.fields["prowadzacy"].queryset.count(), 10)

        termin.termin = today + datetime.timedelta(days=1)
        termin.save()

        form = DzienSzkoleniaAdminInlineForm(instance=instance)

        self.assertEqual(form.fields["prowadzacy"].queryset.count(), 9)

        # Jako nowy dodatkowy prowadzacy
        form = DzienSzkoleniaAdminInlineForm()

        self.assertEqual(form.fields["prowadzacy"].queryset.count(), 9)

    def test_dzien_szkolenia_admin_inline_form_walidacja_daty(self):
        today = datetime.date.today()
        termin = TerminSzkolenia(
            termin=today, szkolenie=SzkolenieFactory.create(czas_dni=2)
        )

        DzienSzkoleniaAdminInlineForm.termin_instance = termin

        # Wysyłamy poprawnę datę szkolenia, ale nie zrobiliśmy clean terminu,
        # a dopiero tam są wyliczane daty szczegółowo.
        form = DzienSzkoleniaAdminInlineForm(data={"data": today.isoformat()})
        form.is_valid()
        self.assertEqual(
            form.errors["data"], ['Data nie występuje w liście dat "Daty szczegolowo".']
        )

        # Robimy clean
        termin.clean()
        form = DzienSzkoleniaAdminInlineForm(data={"data": today.isoformat()})
        form.is_valid()
        self.assertNotIn("data", form.errors)

    def test_dzien_szkolenia_admin_inline_form_walidacja_sali(self):
        today = datetime.date.today()
        termin = TerminSzkolenia(
            termin=today, szkolenie=SzkolenieFactory.create(czas_dni=2)
        )

        DzienSzkoleniaAdminInlineForm.termin_instance = termin

        # Wysyłamy dowolną sale, ale termin jeszcze nie ma lokalizacji,
        # więc nie mamy jak walidować.
        form = DzienSzkoleniaAdminInlineForm(data={"sala": SalaFactory.create().pk})
        form.is_valid()
        self.assertNotIn("sala", form.errors)

        # Dodajemy lokalziację
        termin.lokalizacja = LokalizacjaFactory.create()
        form = DzienSzkoleniaAdminInlineForm(data={"sala": SalaFactory.create().pk})
        form.is_valid()
        self.assertEqual(
            form.errors["sala"],
            ["Lokalizacja sali nie pokrywa się z lokalizacją szkolenia."],
        )

        # Poprawna lokalizacja
        termin.lokalizacja = LokalizacjaFactory.create()
        form = DzienSzkoleniaAdminInlineForm(
            data={"sala": SalaFactory.create(lokalizacja=termin.lokalizacja).pk}
        )
        form.is_valid()
        self.assertNotIn("sala", form.errors)

    def test_termin_nadrzedny(self):
        # Testujemy poprawność nadrzędności terminów

        # Brak reguły nadrzędności w Szkoleniach
        t1 = TerminSzkoleniaFactory.create()
        t2 = TerminSzkoleniaFactory.create()

        post_data = self._term_instance_as_dict(t2)
        post_data["termin_nadrzedny"] = t1.pk

        form = TerminSzkoleniaAdminForm(post_data, instance=t2)
        form.is_valid()

        self.assertEqual(
            form.errors["termin_nadrzedny"], ["Brak reguły nadrzędności w Szkoleniach."]
        )

        # Doadjemy regułę, ale błędna lokalizacja
        s1 = SzkolenieFactory.create()
        s2 = SzkolenieFactory.create(szkolenie_nadrzedne=s1, podzbior_dni="1,2")

        t1 = TerminSzkoleniaFactory.create(szkolenie=s1)
        t2 = TerminSzkoleniaFactory.create(szkolenie=s2)

        post_data = self._term_instance_as_dict(t2)
        post_data["termin_nadrzedny"] = t1.pk

        form = TerminSzkoleniaAdminForm(post_data, instance=t2)
        form.is_valid()

        self.assertEqual(
            form.errors["termin_nadrzedny"], ["Ten termin jest w innej lokalizacji."]
        )

        # Błędna liczba dni
        location = LokalizacjaFactory.create()
        s1 = SzkolenieFactory.create()
        s2 = SzkolenieFactory.create(szkolenie_nadrzedne=s1, podzbior_dni="1,2")

        t1 = TerminSzkoleniaFactory.create(
            szkolenie=s1, lokalizacja=location, daty_szczegolowo="2015-01-01,2015-01-02"
        )
        t2 = TerminSzkoleniaFactory.create(
            szkolenie=s2, lokalizacja=location, daty_szczegolowo="2015-01-01"
        )

        post_data = self._term_instance_as_dict(t2)
        post_data["termin_nadrzedny"] = t1.pk

        form = TerminSzkoleniaAdminForm(post_data, instance=t2)
        form.is_valid()

        self.assertEqual(
            form.errors["termin_nadrzedny"],
            ["Podzbiór dni jest inny niż liczba dni tego terminu."],
        )

        # Taki dzien nie istnieje w terminie nadrzędnym
        location = LokalizacjaFactory.create()
        s1 = SzkolenieFactory.create()
        s2 = SzkolenieFactory.create(szkolenie_nadrzedne=s1, podzbior_dni="1,3")

        t1 = TerminSzkoleniaFactory.create(
            szkolenie=s1, lokalizacja=location, daty_szczegolowo="2015-01-01,2015-01-02"
        )
        t2 = TerminSzkoleniaFactory.create(
            szkolenie=s2, lokalizacja=location, daty_szczegolowo="2015-01-01,2015-01-02"
        )

        post_data = self._term_instance_as_dict(t2)
        post_data["termin_nadrzedny"] = t1.pk

        form = TerminSzkoleniaAdminForm(post_data, instance=t2)
        form.is_valid()

        self.assertEqual(
            form.errors["termin_nadrzedny"],
            ["W terminie nadrzędnym nie istnieje dzień 3 szkolenia."],
        )

        # Rozne sale
        location = LokalizacjaFactory.create()
        s1 = SzkolenieFactory.create()
        s2 = SzkolenieFactory.create(szkolenie_nadrzedne=s1, podzbior_dni="1,3")

        r1 = SalaFactory.create(lokalizacja=location)
        r2 = SalaFactory.create(lokalizacja=location)

        t1 = TerminSzkoleniaFactory.create(
            szkolenie=s1,
            sala=r1,
            lokalizacja=location,
            daty_szczegolowo="2015-01-01,2015-01-02,2015-01-03,2015-01-04",
        )
        t2 = TerminSzkoleniaFactory.create(
            szkolenie=s2,
            sala=r2,
            lokalizacja=location,
            daty_szczegolowo="2015-01-01,2015-01-03",
        )

        post_data = self._term_instance_as_dict(t2)
        post_data["termin_nadrzedny"] = t1.pk

        form = TerminSzkoleniaAdminForm(post_data, instance=t2)
        form.is_valid()

        self.assertEqual(
            form.errors["termin_nadrzedny"],
            ["Sala terminu nadrzędnego jest inna niż wybrana."],
        )

        # Wszystko OK
        location = LokalizacjaFactory.create()
        s1 = SzkolenieFactory.create()
        s2 = SzkolenieFactory.create(szkolenie_nadrzedne=s1, podzbior_dni="1,3")

        t1 = TerminSzkoleniaFactory.create(
            szkolenie=s1,
            lokalizacja=location,
            daty_szczegolowo="2015-01-01,2015-01-02,2015-01-03,2015-01-04",
        )
        t2 = TerminSzkoleniaFactory.create(
            szkolenie=s2, lokalizacja=location, daty_szczegolowo="2015-01-01,2015-01-03"
        )

        post_data = self._term_instance_as_dict(t2)
        post_data["termin_nadrzedny"] = t1.pk

        form = TerminSzkoleniaAdminForm(post_data, instance=t2)
        form.is_valid()

        self.assertNotIn("termin_nadrzedny", form.errors)

    def test_utworz_terminy_podrzedne(self):
        date = datetime.date(2016, 2, 1)

        # Tworzymy regule dziedziczenia
        s1 = SzkolenieFactory.create(czas_dni=3, zawiera_obiady=False)
        s2 = SzkolenieFactory.create(
            szkolenie_nadrzedne=s1, podzbior_dni="2,3", czas_dni=2, zawiera_obiady=True
        )
        s3 = SzkolenieFactory.create(
            szkolenie_nadrzedne=s1, podzbior_dni="1", zawiera_obiady=False
        )
        location = LokalizacjaFactory.create(domyslna_cena_obiadu=100, calendar="1")

        termin = TerminSzkoleniaFactory.create(
            szkolenie=s1,
            lokalizacja=location,
            termin=date,
            daty_szczegolowo=",".join(
                [
                    date.strftime("%Y-%m-%d"),
                    (date + datetime.timedelta(days=1)).strftime("%Y-%m-%d"),
                    (date + datetime.timedelta(days=2)).strftime("%Y-%m-%d"),
                ]
            ),
            obiady="",
        )
        termin.clean()
        termin.save()

        # Tworzymy terminy podrzędne
        termin.utworz_terminy_podrzedne()

        # Powinny być dwa dodatkowe terminy
        self.assertTrue(
            TerminSzkolenia.objects.filter(
                lokalizacja=location,
                szkolenie=s2,
                termin=date + datetime.timedelta(days=1),
            ).count()
        )

        self.assertTrue(
            TerminSzkolenia.objects.filter(
                lokalizacja=location, szkolenie=s3, termin=date
            ).count()
        )

        # Sprawdzamy poprawnosc generowania pola z obiadami
        t1 = TerminSzkolenia.objects.get(szkolenie=s1)
        self.assertEqual(t1.obiady, "obiady-opcjonalne")

        t2 = TerminSzkolenia.objects.get(szkolenie=s2)
        self.assertEqual(t2.obiady, "obiady-wliczone")

        t3 = TerminSzkolenia.objects.get(szkolenie=s3)
        self.assertEqual(t3.obiady, "obiady-opcjonalne")

        # Sprawdzamy, czy zostały wyołane kalendarze
        # Powinno zostać dodane 2 wpisy, po jednym dla każdej lokalizacji
        # nowego terminu podrzędengo.
        self.assertEqual(CalendarUpdate.objects.all().count(), 2)

    def test_utworz_terminy_podrzedne_i_szczeglowe_dni_szkolenia(self):
        date = datetime.date(2016, 2, 1)

        # Tworzymy regule dziedziczenia
        s1 = SzkolenieFactory.create(czas_dni=6)
        s2 = SzkolenieFactory.create(
            szkolenie_nadrzedne=s1, podzbior_dni="2,3", czas_dni=2
        )
        s3 = SzkolenieFactory.create(szkolenie_nadrzedne=s1, podzbior_dni="1")
        s4 = SzkolenieFactory.create(
            szkolenie_nadrzedne=s1, podzbior_dni="4,5", czas_dni=2
        )
        location = LokalizacjaFactory.create()

        termin = TerminSzkoleniaFactory.create(
            szkolenie=s1,
            lokalizacja=location,
            termin=date,
            daty_szczegolowo=",".join(
                [
                    date.strftime("%Y-%m-%d"),
                    (date + datetime.timedelta(days=1)).strftime("%Y-%m-%d"),
                    (date + datetime.timedelta(days=2)).strftime("%Y-%m-%d"),
                    (date + datetime.timedelta(days=3)).strftime("%Y-%m-%d"),
                    (date + datetime.timedelta(days=4)).strftime("%Y-%m-%d"),
                    (date + datetime.timedelta(days=5)).strftime("%Y-%m-%d"),
                ]
            ),
        )
        termin.clean()
        termin.save()

        # tworzymy dni szczegolowe
        DzienSzkoleniaFactory.create(data=date, terminszkolenia=termin)
        DzienSzkoleniaFactory.create(
            data=date + datetime.timedelta(days=3), terminszkolenia=termin
        )
        DzienSzkoleniaFactory.create(
            data=date + datetime.timedelta(days=4),
            terminszkolenia=termin,
            sprzet=SprzetFactory.create_batch(3),
        )
        DzienSzkoleniaFactory.create(
            data=date + datetime.timedelta(days=5), terminszkolenia=termin
        )

        # Tworzymy terminy podrzędne
        termin.utworz_terminy_podrzedne()

        # Powinny być trzy dodatkowe terminy
        t1 = TerminSzkolenia.objects.filter(szkolenie=s2).get()
        t2 = TerminSzkolenia.objects.filter(szkolenie=s3).get()
        t3 = TerminSzkolenia.objects.filter(szkolenie=s4).get()

        # Termin t2, nie powinien mieć żadnych przeciążonych dni
        self.assertEqual(DzienSzkolenia.objects.filter(terminszkolenia=t1).count(), 0)

        # Termin t2, powinien mieć przeciążony tylko jeden dzień (`date`)
        self.assertEqual(DzienSzkolenia.objects.filter(terminszkolenia=t2).count(), 1)

        ds2 = DzienSzkolenia.objects.get(terminszkolenia=t2)
        self.assertEqual(ds2.sprzet.all().count(), 1)

        # Termin t3, powinien mieć przeciążone dwa dni (`date` + 3 i 4)
        self.assertEqual(DzienSzkolenia.objects.filter(terminszkolenia=t3).count(), 2)

        ds3 = DzienSzkolenia.objects.get(
            terminszkolenia=t3, data=date + datetime.timedelta(days=3)
        )
        self.assertEqual(ds3.sprzet.all().count(), 1)
        ds4 = DzienSzkolenia.objects.get(
            terminszkolenia=t3, data=date + datetime.timedelta(days=4)
        )
        self.assertEqual(ds4.sprzet.all().count(), 3)

    def test_obiady(self):
        # Obiady nie moga byc przypisane do kursu i trybu wieczornego

        # Szkolenie - tryb wieczorowy
        t = TerminSzkoleniaFactory.create(tryb=2)

        post_data = self._term_instance_as_dict(t)
        post_data["obiady"] = "obiady-wliczone"

        form = TerminSzkoleniaAdminForm(post_data, instance=t)
        form.is_valid()

        self.assertEqual(
            form.errors["obiady"],
            ["Obiady nie mogą być przypisane do trybu wieczorowego."],
        )

        # Kurs zawodowy - tryb wieoczorowy
        t = TerminSzkoleniaFactory.create(szkolenie=KursFactory.create(), tryb=2)

        post_data = self._term_instance_as_dict(t)
        post_data["obiady"] = "obiady-wliczone"

        form = TerminSzkoleniaAdminForm(post_data, instance=t)
        form.is_valid()

        self.assertEqual(
            form.errors["obiady"],
            ["Obiady nie mogą być przypisane do trybu wieczorowego."],
        )

        # Kurs zawodowy i tryb nie-wieczorowy
        t = TerminSzkoleniaFactory.create(szkolenie=KursFactory.create(), tryb=1)

        post_data = self._term_instance_as_dict(t)
        post_data["obiady"] = "obiady-wliczone"

        form = TerminSzkoleniaAdminForm(post_data, instance=t)
        form.is_valid()

        self.assertNotIn("obiady", form.errors)

    def test_nested_objects_with_invalid_dates(self):
        # Testusjemy zwracanie podrzednych terminow z blednymi datami.

        termin = TerminSzkoleniaFactory.create(
            termin=datetime.date(2015, 1, 1),
            daty_szczegolowo="2015-01-01,2015-01-02,2015-01-03",
        )

        t1 = TerminSzkoleniaFactory.create(
            termin=datetime.date(2015, 1, 1),
            szkolenie__podzbior_dni="1",
            termin_nadrzedny=termin,
            daty_szczegolowo="2015-01-01",
        )

        t2 = TerminSzkoleniaFactory.create(
            termin=datetime.date(2015, 1, 2),
            szkolenie__podzbior_dni="2,3",
            termin_nadrzedny=termin,
            daty_szczegolowo="2015-01-02,2015-01-03",
        )

        errors = termin.nested_objects_with_invalid_dates()
        self.assertEqual(errors, [])

        # Zmieniamy date nadrzednemu terminowi
        termin.termin = datetime.date(2015, 2, 1)
        termin.daty_szczegolowo = "2015-02-01,2015-02-02,2015-02-03"
        termin.save()

        errors = termin.nested_objects_with_invalid_dates()
        self.assertEqual(len(errors), 2)
        self.assertIn(t1, errors)
        self.assertIn(t2, errors)

        # Powracamy do pierowtnych ustawien
        termin.termin = datetime.date(2015, 1, 1)
        termin.daty_szczegolowo = "2015-01-01,2015-01-02,2015-01-03"
        termin.save()

        # Zmieniamy date pierwszemu terminowi
        t1.termin = datetime.date(2015, 1, 2)
        t1.daty_szczegolowo = "2015-01-02"
        t1.save()

        errors = termin.nested_objects_with_invalid_dates()
        self.assertEqual(len(errors), 1)
        self.assertIn(t1, errors)

        # Zmieniamy date szkolenia drugiemu terminowi
        t2.szkolenie.podzbior_dni = "4"
        t2.szkolenie.save()

        errors = termin.nested_objects_with_invalid_dates()
        self.assertEqual(len(errors), 2)
        self.assertIn(t1, errors)
        self.assertIn(t2, errors)

    def test_prowadzacy_per_dni(self):
        # Brak głównego prowadzącego i przeciążonych dni
        termin = TerminSzkoleniaFactory.create(
            termin=datetime.date(2015, 1, 1),
            daty_szczegolowo="2015-01-01,2015-01-02,2015-01-03",
            prowadzacy=None,
        )

        result = termin.prowadzacy_per_dni()
        self.assertEqual(len(result), 0)

        # Przypadek, gdy nie ma przeciążonych dni
        termin = TerminSzkoleniaFactory.create(
            termin=datetime.date(2015, 1, 1),
            daty_szczegolowo="2015-01-01,2015-01-02,2015-01-03",
        )

        result = termin.prowadzacy_per_dni()
        self.assertEqual(len(result), 1)

        self.assertEqual(
            sorted(result[termin.prowadzacy.pk]["dni"]),
            [
                datetime.date(2015, 1, 1),
                datetime.date(2015, 1, 2),
                datetime.date(2015, 1, 3),
            ],
        )

        # 1 przeciążony dzień, ale ten sam prowadzący
        termin = TerminSzkoleniaFactory.create(
            termin=datetime.date(2015, 1, 1),
            daty_szczegolowo="2015-01-01,2015-01-02,2015-01-03",
        )
        DzienSzkoleniaFactory.create(
            terminszkolenia=termin,
            data=datetime.date(2015, 1, 2),
            prowadzacy=termin.prowadzacy,
        )

        result = termin.prowadzacy_per_dni()
        self.assertEqual(len(result), 1)

        self.assertEqual(
            sorted(result[termin.prowadzacy.pk]["dni"]),
            [
                datetime.date(2015, 1, 1),
                datetime.date(2015, 1, 2),
                datetime.date(2015, 1, 3),
            ],
        )

        # 1 przeciążony dzień i inny prowadzący
        termin = TerminSzkoleniaFactory.create(
            termin=datetime.date(2015, 1, 1),
            daty_szczegolowo="2015-01-01,2015-01-02,2015-01-03",
        )
        d1 = DzienSzkoleniaFactory.create(
            terminszkolenia=termin, data=datetime.date(2015, 1, 2)
        )

        result = termin.prowadzacy_per_dni()
        self.assertEqual(len(result), 2)

        self.assertEqual(
            sorted(result[termin.prowadzacy.pk]["dni"]),
            [datetime.date(2015, 1, 1), datetime.date(2015, 1, 3)],
        )

        self.assertEqual(
            sorted(result[d1.prowadzacy.pk]["dni"]), [datetime.date(2015, 1, 2)]
        )

        # Wszystkie dni przeciążone
        termin = TerminSzkoleniaFactory.create(
            termin=datetime.date(2015, 1, 1),
            daty_szczegolowo="2015-01-01,2015-01-02,2015-01-03",
        )
        d1 = DzienSzkoleniaFactory.create(
            terminszkolenia=termin, data=datetime.date(2015, 1, 1)
        )
        d2 = DzienSzkoleniaFactory.create(
            terminszkolenia=termin, data=datetime.date(2015, 1, 2)
        )
        d3 = DzienSzkoleniaFactory.create(
            terminszkolenia=termin, data=datetime.date(2015, 1, 3)
        )

        result = termin.prowadzacy_per_dni()
        self.assertEqual(len(result), 4)

        self.assertEqual(sorted(result[termin.prowadzacy.pk]["dni"]), [])

        self.assertEqual(
            sorted(result[d1.prowadzacy.pk]["dni"]), [datetime.date(2015, 1, 1)]
        )
        self.assertEqual(
            sorted(result[d2.prowadzacy.pk]["dni"]), [datetime.date(2015, 1, 2)]
        )
        self.assertEqual(
            sorted(result[d3.prowadzacy.pk]["dni"]), [datetime.date(2015, 1, 3)]
        )

    def test_sale_per_dni(self):
        # Brak głównej sali i przeciążonych dni
        termin = TerminSzkoleniaFactory.create(
            termin=datetime.date(2015, 1, 1),
            daty_szczegolowo="2015-01-01,2015-01-02,2015-01-03",
            sala=None,
        )

        result = termin.sale_per_dni()
        self.assertEqual(len(result), 0)

        # Przypadek, gdy nie ma przeciążonych dni
        termin = TerminSzkoleniaFactory.create(
            termin=datetime.date(2015, 1, 1),
            daty_szczegolowo="2015-01-01,2015-01-02,2015-01-03",
            sala=SalaFactory.create(),
        )

        result = termin.sale_per_dni()
        self.assertEqual(len(result), 1)

        self.assertEqual(
            sorted(result[termin.sala.pk]["dni"]),
            [
                datetime.date(2015, 1, 1),
                datetime.date(2015, 1, 2),
                datetime.date(2015, 1, 3),
            ],
        )

        # 1 przeciążony dzień, ale ta sama sala
        termin = TerminSzkoleniaFactory.create(
            termin=datetime.date(2015, 1, 1),
            daty_szczegolowo="2015-01-01,2015-01-02,2015-01-03",
            sala=SalaFactory.create(),
        )
        DzienSzkoleniaFactory.create(
            terminszkolenia=termin, data=datetime.date(2015, 1, 2), sala=termin.sala
        )

        result = termin.sale_per_dni()
        self.assertEqual(len(result), 1)

        self.assertEqual(
            sorted(result[termin.sala.pk]["dni"]),
            [
                datetime.date(2015, 1, 1),
                datetime.date(2015, 1, 2),
                datetime.date(2015, 1, 3),
            ],
        )

        # 1 przeciążony dzień i inna sala
        termin = TerminSzkoleniaFactory.create(
            termin=datetime.date(2015, 1, 1),
            daty_szczegolowo="2015-01-01,2015-01-02,2015-01-03",
            sala=SalaFactory.create(),
        )
        d1 = DzienSzkoleniaFactory.create(
            terminszkolenia=termin, data=datetime.date(2015, 1, 2)
        )

        result = termin.sale_per_dni()
        self.assertEqual(len(result), 2)

        self.assertEqual(
            sorted(result[termin.sala.pk]["dni"]),
            [datetime.date(2015, 1, 1), datetime.date(2015, 1, 3)],
        )

        self.assertEqual(sorted(result[d1.sala.pk]["dni"]), [datetime.date(2015, 1, 2)])

        # Wszystkie dni przeciążone
        termin = TerminSzkoleniaFactory.create(
            termin=datetime.date(2015, 1, 1),
            daty_szczegolowo="2015-01-01,2015-01-02,2015-01-03",
            sala=SalaFactory.create(),
        )
        d1 = DzienSzkoleniaFactory.create(
            terminszkolenia=termin, data=datetime.date(2015, 1, 1)
        )
        d2 = DzienSzkoleniaFactory.create(
            terminszkolenia=termin, data=datetime.date(2015, 1, 2)
        )
        d3 = DzienSzkoleniaFactory.create(
            terminszkolenia=termin, data=datetime.date(2015, 1, 3)
        )

        result = termin.sale_per_dni()
        self.assertEqual(len(result), 4)

        self.assertEqual(sorted(result[termin.sala.pk]["dni"]), [])

        self.assertEqual(sorted(result[d1.sala.pk]["dni"]), [datetime.date(2015, 1, 1)])
        self.assertEqual(sorted(result[d2.sala.pk]["dni"]), [datetime.date(2015, 1, 2)])
        self.assertEqual(sorted(result[d3.sala.pk]["dni"]), [datetime.date(2015, 1, 3)])

    def test_sprzet_per_dni(self):
        # Brak głównego sprzętu i przeciążonych dni
        termin = TerminSzkoleniaFactory.create(
            termin=datetime.date(2015, 1, 1),
            daty_szczegolowo="2015-01-01,2015-01-02,2015-01-03",
            sprzet=[],
        )

        result = termin.sprzet_per_dni()
        self.assertEqual(len(result), 0)

        sprzet = SprzetFactory.create_batch(2)

        # Przypadek, gdy nie ma przeciążonych dni
        termin = TerminSzkoleniaFactory.create(
            termin=datetime.date(2015, 1, 1),
            daty_szczegolowo="2015-01-01,2015-01-02,2015-01-03",
            sprzet=sprzet,
        )

        result = termin.sprzet_per_dni()
        self.assertEqual(len(result), 2)

        for s in sprzet:
            self.assertEqual(
                sorted(result[s.pk]["dni"]),
                [
                    datetime.date(2015, 1, 1),
                    datetime.date(2015, 1, 2),
                    datetime.date(2015, 1, 3),
                ],
            )

        # 1 przeciążony dzień, ale ten sam sprzęt
        termin = TerminSzkoleniaFactory.create(
            termin=datetime.date(2015, 1, 1),
            daty_szczegolowo="2015-01-01,2015-01-02,2015-01-03",
            sprzet=sprzet,
        )
        DzienSzkoleniaFactory.create(
            terminszkolenia=termin, data=datetime.date(2015, 1, 2), sprzet=sprzet
        )

        result = termin.sprzet_per_dni()
        self.assertEqual(len(result), 2)

        for s in sprzet:
            self.assertEqual(
                sorted(result[s.pk]["dni"]),
                [
                    datetime.date(2015, 1, 1),
                    datetime.date(2015, 1, 2),
                    datetime.date(2015, 1, 3),
                ],
            )

        # 1 przeciążony dzień i inny sprzęt
        termin = TerminSzkoleniaFactory.create(
            termin=datetime.date(2015, 1, 1),
            daty_szczegolowo="2015-01-01,2015-01-02,2015-01-03",
            sprzet=sprzet,
        )
        d1 = DzienSzkoleniaFactory.create(
            terminszkolenia=termin, data=datetime.date(2015, 1, 2)
        )

        result = termin.sprzet_per_dni()
        self.assertEqual(len(result), 3)

        for s in sprzet:
            self.assertEqual(
                sorted(result[s.pk]["dni"]),
                [datetime.date(2015, 1, 1), datetime.date(2015, 1, 3)],
            )

        self.assertEqual(
            sorted(result[d1.sprzet.all()[0].pk]["dni"]), [datetime.date(2015, 1, 2)]
        )

        # Wszystkie dni przeciążone
        termin = TerminSzkoleniaFactory.create(
            termin=datetime.date(2015, 1, 1),
            daty_szczegolowo="2015-01-01,2015-01-02,2015-01-03",
            sprzet=sprzet,
        )
        d1 = DzienSzkoleniaFactory.create(
            terminszkolenia=termin,
            data=datetime.date(2015, 1, 1),
            sprzet=[sprzet[0], SprzetFactory.create()],
        )
        d2 = DzienSzkoleniaFactory.create(
            terminszkolenia=termin, data=datetime.date(2015, 1, 2)
        )
        d3 = DzienSzkoleniaFactory.create(
            terminszkolenia=termin,
            data=datetime.date(2015, 1, 3),
            sprzet=[sprzet[0], SprzetFactory.create()],
        )

        result = termin.sprzet_per_dni()
        self.assertEqual(len(result), 5)

        self.assertEqual(
            sorted(result[sprzet[0].pk]["dni"]),
            [datetime.date(2015, 1, 1), datetime.date(2015, 1, 3)],
        )

        self.assertEqual(sorted(result[sprzet[1].pk]["dni"]), [])

        self.assertEqual(
            sorted(result[d1.sprzet.all()[0].pk]["dni"]),
            [datetime.date(2015, 1, 1), datetime.date(2015, 1, 3)],
        )
        self.assertEqual(
            sorted(result[d1.sprzet.all()[1].pk]["dni"]), [datetime.date(2015, 1, 1)]
        )
        self.assertEqual(
            sorted(result[d2.sprzet.all()[0].pk]["dni"]), [datetime.date(2015, 1, 2)]
        )
        self.assertEqual(
            sorted(result[d3.sprzet.all()[0].pk]["dni"]),
            [datetime.date(2015, 1, 1), datetime.date(2015, 1, 3)],
        )
        self.assertEqual(
            sorted(result[d3.sprzet.all()[1].pk]["dni"]), [datetime.date(2015, 1, 3)]
        )

    def test_get_kalendarze_dane_flat(self):
        # 2 przeciążone dni i inna sala oraz prowadzący
        termin = TerminSzkoleniaFactory.create(
            termin=datetime.date(2015, 1, 1),
            daty_szczegolowo="2015-01-01,2015-01-02,2015-01-03,2015-01-04",
            sala=SalaFactory.create(),
        )
        d1 = DzienSzkoleniaFactory.create(
            terminszkolenia=termin, data=datetime.date(2015, 1, 2)
        )
        d2 = DzienSzkoleniaFactory.create(
            terminszkolenia=termin,
            data=datetime.date(2015, 1, 4),
            sala=d1.sala,
            prowadzacy=d1.prowadzacy,
        )

        result = termin.get_kalendarze_dane_flat()

        self.assertEqual(len(result["lokalizacje"]), 1)
        self.assertEqual(result["lokalizacje"][0]["id"], str(termin.lokalizacja.pk))

        # To już mamy przetestowan w testach powyżej, sprawdzamy tylko, czy
        # ilość się zgadza.
        self.assertEqual(len(result["prowadzacy"]), 2)
        self.assertEqual(len(result["sale"]), 2)

        # Sprawdzamy, czy zmienił się hash (nic nie zmieniamy, więc nie
        # powinien)
        result2 = termin.get_kalendarze_dane_flat()
        self.assertDictEqual(result, result2)

        # Teraz zmieniamy dzien d2 - powinien sie zmienic hash sal i
        # prowadzących - lokalizacja zostaje taka sama.
        d2.data = datetime.date(2015, 1, 3)
        d2.save()
        result3 = termin.get_kalendarze_dane_flat()

        self.assertDictEqual(result2["lokalizacje"][0], result3["lokalizacje"][0])

        self.assertNotEqual(
            result2["prowadzacy"][0]["hash"], result3["prowadzacy"][0]["hash"]
        )
        self.assertNotEqual(
            result2["prowadzacy"][1]["hash"], result3["prowadzacy"][1]["hash"]
        )

        self.assertNotEqual(result2["sale"][0]["hash"], result3["sale"][0]["hash"])
        self.assertNotEqual(result2["sale"][1]["hash"], result3["sale"][1]["hash"])

        # Tylko sprawdzamy, czy serializacja przebiegła pomyślnie
        self.assertTrue(termin.get_kalendarze_dane_flat(as_json=True))

    def test_original_state(self):
        termin = TerminSzkoleniaFactory.create(
            odbylo_sie=False,
            sala=SalaFactory.create(),
            prowadzacy=ProwadzacyFactory.create(),
            lokalizacja=LokalizacjaFactory.create(),
        )

        termin = TerminSzkolenia.objects.get(pk=termin.pk)

        self.assertFalse(termin._original_state["kalendarze_dane_flat"])

        termin.kalendarze_dane_flat = termin.get_kalendarze_dane_flat(as_json=True)
        termin.save()

        # Oryginalny obiekt dalej powinien byc bez danych
        self.assertFalse(termin._original_state["kalendarze_dane_flat"])

        # Pobieramy obiekt ponownie
        termin = TerminSzkolenia.objects.get(pk=termin.pk)
        self.assertTrue(termin._original_state["kalendarze_dane_flat"])

    @unittest.skip("Wylaczona strona")
    def test_last_minute(self):
        """
        Badamy tylko, czy jest resp 200. Ten test bedzie miał więcej sensu na
        django-1.7 gdzie jest "czysta" baza.
        """

        response = self.client.get(reverse("last_minute"))
        self.assertEqual(response.status_code, 200)


###################
# Certyfikaty
###################

# todo wlaczyc pozniej!
@unittest.skip("Przebudowa certyfikatow ...")
class CertificateViewTestCase(ALXTestCase):
    certificate = None

    def setUp(self):
        super().setUp()

        random_name = "Imię i nazwisko {0}".format(
            "".join([random.choice(string.ascii_uppercase) for _ in range(25)])
        )
        self.certificate = GraduateFactory(
            name=random_name,
            template="""
                <h1>Jakiś tam tekst</h1> ... <p>{0}</p>
            """.format(
                random_name
            ),
        )

        # Dodaj przykładowy PDF
        pdf = generate_pdf(
            "www/certificates/certificate_pdf.html",
            {
                "content": self.certificate.template,
            },
        )
        self.certificate.pdf.save(
            "{0}.pdf".format(self.certificate.key.hex), pdf, save=True
        )

    def test_share_via_email(self):
        resp = self.client.post(
            reverse(
                "certificate",
                kwargs={
                    "slug": self.certificate.slug,
                    "key": self.certificate.key.hex,
                    "language": "pl",
                },
            ),
            data={
                "email": "<EMAIL>",
            },
            follow=True,
        )

        self.assertContains(resp, "Wiadomość została wysłana.")

        self.assertEqual(len(mail.outbox), 1)

        subject = mail.outbox[0].subject
        body = mail.outbox[0].body

        self.assertEqual(subject, "Zobacz mój certyfikat")
        self.assertIn(self.certificate.name, body)

        public_url = reverse(
            "public_certificate",
            kwargs={
                "slug": self.certificate.slug,
                "public_key": self.certificate.public_key.hex,
                "language": "pl",
            },
        )
        self.assertIn(public_url, body)

    def test_public_view(self):
        resp = self.client.get(
            reverse(
                "public_certificate",
                kwargs={
                    "slug": self.certificate.slug,
                    "public_key": self.certificate.public_key.hex,
                    "language": "pl",
                },
            )
        )

        # Sprawdź elemenety szablonu (np. czy występują personalia uczestnika).
        self.assertContains(resp, self.certificate.name)


###################
# Faktura wysyłka
###################


class FakturaWysylkaViewTestCase(ALXTestCase):
    def test_callback_url_ok(self):
        postivo = FakturaWysylkaFactory.create(
            dispatch_id="T00001224", dispatched_at=datetime.datetime.now(), status=1
        )

        self.assertIsNone(postivo.status_date)

        url = (
            reverse(
                "postivo_callback",
                kwargs={
                    "key": postivo.key.hex,
                },
            )
            + "?dispatch_id=T00001224&status_code=3&date=2011-01-21+13%3A22%3A14"
        )

        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

        postivo = FakturaWysylka.objects.get(pk=postivo.pk)

        self.assertEqual(postivo.status, "3")
        self.assertEqual(
            postivo.status_date, datetime.datetime(2011, 1, 21, 13, 22, 14)
        )

    def test_callback_url_not_dispatched(self):
        postivo = FakturaWysylkaFactory.create(dispatch_id="T00001224", status=1)

        url = (
            reverse(
                "postivo_callback",
                kwargs={
                    "key": postivo.key.hex,
                },
            )
            + "?dispatch_id=T00001224&status_code=3&date=2011-01-21+13%3A22%3A14"
        )

        response = self.client.get(url)
        self.assertEqual(response.status_code, 404)

    def test_callback_url_worng_key(self):
        FakturaWysylkaFactory.create(
            dispatch_id="T00001224", dispatched_at=datetime.datetime.now(), status=1
        )

        url = (
            reverse(
                "postivo_callback",
                kwargs={
                    "key": str(uuid.uuid4().hex),
                },
            )
            + "?dispatch_id=T00001224&status_code=3&date=2011-01-21+13%3A22%3A14"
        )

        response = self.client.get(url)
        self.assertEqual(response.status_code, 404)

    def test_callback_url_worng_dispatch_id(self):
        postivo = FakturaWysylkaFactory.create(
            dispatch_id="abc", dispatched_at=datetime.datetime.now(), status=1
        )

        url = (
            reverse(
                "postivo_callback",
                kwargs={
                    "key": postivo.key.hex,
                },
            )
            + "?dispatch_id=T00001224&status_code=3&date=2011-01-21+13%3A22%3A14"
        )

        response = self.client.get(url)
        self.assertEqual(response.status_code, 404)

    def test_callback_url_cant_parse_date(self):
        postivo = FakturaWysylkaFactory.create(
            dispatch_id="T00001224", dispatched_at=datetime.datetime.now(), status=1
        )

        url = (
            reverse(
                "postivo_callback",
                kwargs={
                    "key": postivo.key.hex,
                },
            )
            + "?dispatch_id=T00001224&status_code=3&date=101101-2113%3A22%3A14"
        )

        response = self.client.get(url)
        self.assertEqual(response.status_code, 404)


###################
# Powiadomienia
###################


@override_settings(MAIL_TO_NOTIFICATION_ALERT="")
class UserNotificationViewTestCase(ALXLiveServerTestCase):
    def _test_personal_data_form(self, user_pk, language="pl"):
        # Ponieważ formularz jest na kilku podstonach warto go wynieść do
        # osobnej funkcji.

        # Teraz sprawdzamy formularz opcjonalnych danych osobowych
        self.selenium.find_element_by_id("id_phone_number").send_keys("123 123 123")
        self.selenium.find_element_by_id("id_full_name").send_keys("Józef")
        self.selenium.find_element_by_id("personal-data-form-submit").click()

        message = self.wait_for_element_by_xpath('//div[@id="messages"]').text

        if language == "pl":
            self.assertEqual(message, "Zmiany zostały zapisane.")
        else:
            self.assertEqual(message, "Your data has been successfully saved.")

        # Sprawdzamy, czy dane rzeczywiście zostały zapisane
        user = UserNotificationFactory._meta.model.objects.get(pk=user_pk)

        self.assertEqual(user.full_name, "Józef")
        self.assertEqual(user.company_name, "")
        self.assertEqual(user.phone_number, "*********")

    def test_initial_subscribe_pl(self):
        # Zapisujemy się pierwszy raz do subskrypcji - wersja PL
        training = SzkolenieFactory(language="pl", header_kwd="test")

        url = reverse("detail_pl", kwargs={"slug": training.slug, "language": "pl"})

        def do_subscription(mails_count):
            self.selenium.get(self.live_server_url + url)

            # Uruchamiamy modal
            self.selenium.find_element_by_id("id_notifications-modal-run").click()
            self.wait_for_element_by_id("subscription-container")

            self.wait_for_element_by_id("id_email")
            self.wait_for_element_by_id("id_locations_0")

            # Wpisujemy adres email oraz wybieramy miejscowości
            self.selenium.find_element_by_id("id_email").send_keys("<EMAIL>")
            self.selenium.find_element_by_id("id_tos").click()

            self.selenium.find_element_by_id("id_locations_0").click()
            self.selenium.find_element_by_id("id_locations_1").click()

            # Zapisz formularz
            with patch(
                "www.tasks.user_notifications_alert.delay"
            ) as user_notifications_alert:

                self.selenium.find_element_by_id(
                    "subscription-ajax-form-submit"
                ).click()
                message = self.wait_for_element_by_xpath('//p[@id="thank-you"]').text
                self.assertEqual(message, "Dziękujemy.")

                # Użytkownik nie jest aktywny - nie powinny być wysyłane alerty
                # do Biura.
                self.assertEqual(user_notifications_alert.call_count, 0)

            # Sprawdzamy maile. Pierwsza rejestracja, więc w mailu musi być
            # link do aktywacji konta.
            self.assertEqual(len(mail.outbox), mails_count)

            self.assertIn(
                "[Wymagana reakcja] Potwierdź chęć otrzymywania powiadomień "
                "z ALX (www.alx.pl)",
                mail.outbox[mails_count - 1].subject,
            )

            user = UserNotificationFactory._meta.model.objects.all()[0]

            self.assertIn(
                reverse(
                    "activate_subscription",
                    kwargs={
                        "key": user.key.hex,
                        "language": "pl",
                    },
                ),
                mail.outbox[mails_count - 1].body,
            )

            # Użytkownik powinien miec uzupełniony adres IP i przeglądarkę
            self.assertTrue(user.remote_addr)
            self.assertTrue(user.user_agent)

        # Pierwszy raz
        do_subscription(1)

        # Drugi raz - użytkownik dalej nie jest potwierdzony, więc mail będzie
        # ten sam.
        do_subscription(2)

    def test_initial_subscribe_en(self):
        # Zapisujemy się pierwszy raz do subskrypcji - wersja EN
        training = SzkolenieFactory(language="en", header_kwd="test")

        url = reverse("detail_en", kwargs={"slug": training.slug, "language": "en"})

        def do_subscription(mails_count):
            self.selenium.get(self.live_server_url + url)

            # Uruchamiamy modal
            self.selenium.find_element_by_id("id_notifications-modal-run").click()
            self.wait_for_element_by_id("subscription-container")

            self.wait_for_element_by_id("id_email")
            self.selenium.find_element_by_id("id_locations_0").click()
            self.selenium.find_element_by_id("id_email").send_keys("<EMAIL>")
            self.selenium.find_element_by_id("id_tos").click()

            with patch(
                "www.tasks.user_notifications_alert.delay"
            ) as user_notifications_alert:

                self.selenium.find_element_by_id(
                    "subscription-ajax-form-submit"
                ).click()
                message = self.wait_for_element_by_xpath('//p[@id="thank-you"]').text
                self.assertEqual(message, "Thank you.")

                # Użytkownik nie jest aktywny - nie powinny być wysyłane alerty
                # do Biura.
                self.assertEqual(user_notifications_alert.call_count, 0)

            # Sprawdzamy maile. Pierwsza rejestracja, więc w mailu musi być
            # link do aktywacji konta
            self.assertEqual(len(mail.outbox), mails_count)

            self.assertIn(
                "[Action required] Confirm subscription to notifications " "from ALX",
                mail.outbox[mails_count - 1].subject,
            )

            user = UserNotificationFactory._meta.model.objects.all()[0]

            self.assertIn(
                reverse(
                    "activate_subscription",
                    kwargs={
                        "key": user.key.hex,
                        "language": "en",
                    },
                ),
                mail.outbox[mails_count - 1].body,
            )

            # Użytkownik powinien mieć uzupełniony adres IP i przeglądarkę
            self.assertTrue(user.remote_addr)
            self.assertTrue(user.user_agent)

        # Pierwszy raz
        do_subscription(1)

        # Drugi raz - użytkownik dalej nie jest potwierdzony, więc mail będzie
        # ten sam.
        do_subscription(2)

    def _test_activate_subscription(self, status):
        # Tworzymy dane poczatkowe
        user = UserNotificationFactory(status=status)

        # Użytkonik jest nieaktywny i brak daty aktywacji
        self.assertEqual(user.status, status)
        self.assertFalse(user.activation_at)

        # Aktywuj subskrypcję
        url = reverse(
            "activate_subscription",
            kwargs={
                "key": user.key.hex,
                "language": "pl",
            },
        )

        with patch(
            "www.tasks.user_notifications_alert.delay"
        ) as user_notifications_alert:

            self.selenium.get(self.live_server_url + url)

            # Powinna zostać wywołana akcja celery wysyłająca alerty z
            # powiadomieniami dla Biura.
            self.assertEqual(user_notifications_alert.call_count, 1)

        # Sprawdź elemenety szablonu (np. czy występują komunikaty).
        page_text = self.selenium.find_element_by_tag_name("body").text

        self.assertIn("Twoja subskrypcja została aktywowana.", page_text)

        # Powinny być obecne kody trackujące GA
        self.assertIn('data-ga-category="contact"', self.selenium.page_source)

        self.assertIn('data-ga-action="notification"', self.selenium.page_source)

        # Teraz użytkownik powinien mieć status 1 (aktywny)
        user = UserNotificationFactory._meta.model.objects.get(pk=user.pk)
        self.assertEqual(user.status, 1)

        # Powinna być data aktywacji
        self.assertTrue(user.activation_at)

        # Spróbuj jeszcze raz wejść na link - powinniśmy otrzymać ten sam
        # komunika, ale już bez zmiany danych w obiekcie.

        with patch(
            "www.tasks.user_notifications_alert.delay"
        ) as user_notifications_alert:

            self.selenium.get(self.live_server_url + url)

            # Teraz już nie powinny być wysyłane alerty do Biura.
            self.assertEqual(user_notifications_alert.call_count, 0)

        page_text = self.selenium.find_element_by_tag_name("body").text

        self.assertIn("Twoja subskrypcja została aktywowana.", page_text)

        # Nie powinny być już obecne kody trackujące GA
        self.assertNotIn('data-ga-category="contact"', self.selenium.page_source)

        self.assertNotIn('data-ga-action="notification"', self.selenium.page_source)

        # Status i daty aktywacji nie powinny się zmieniać
        user2 = UserNotificationFactory._meta.model.objects.get(pk=user.pk)

        self.assertEqual(user2.status, user.status)
        self.assertEqual(user2.activation_at, user.activation_at)

        # Teraz sprawdzamy formularz opcjonalnych danych osobowych
        self._test_personal_data_form(user.pk)

    def test_activate_subscription_with_status_0(self):
        self._test_activate_subscription(0)

    def test_activate_subscription_with_status_minus1(self):
        self._test_activate_subscription(-1)

    def test_activate_subscription_with_status_2(self):
        self._test_activate_subscription(2)

    def test_manage_subscription(self):
        # Tworzymy dane poczatkowe
        user = UserNotificationFactory(status=1)
        UserCoursesNotificationFactory(
            user=user,
            locations=[
                LokalizacjaFactory._meta.model.objects.filter(reklamowana=True)[0]
            ],
        )

        # Przejdź do zarządzania
        url = reverse(
            "manage_subscriptions",
            kwargs={
                "key": user.key.hex,
                "language": "pl",
            },
        )

        self.selenium.get(self.live_server_url + url)

        # Zmień liczbę lokalizacji
        self.selenium.find_element_by_id("id_form-0-locations_0").click()
        self.selenium.find_element_by_id("id_form-0-locations_1").click()
        self.selenium.find_element_by_id("id_form-0-locations_2").click()
        self.selenium.find_element_by_id("id_form-0-locations_3").click()

        # Zapisz formularz
        with patch(
            "www.tasks.user_notifications_alert.delay"
        ) as user_notifications_alert:

            self.selenium.find_element_by_id("manage-subscriptions-submit").click()
            self.wait_for_element_by_id("messages")

            # Powinna zostać wywołana akcja celery wysyłająca alerty z
            # powiadomieniami dla Biura.
            self.assertEqual(user_notifications_alert.call_count, 1)

        messages = self.selenium.find_element_by_id("messages").text
        self.assertIn("Zmiany zostały zapisane", messages)

        # Sprawdź liczbę lokalizacji
        obj = UserCoursesNotificationFactory._meta.model.objects.get(user=user)
        self.assertTrue(obj.locations.all().count() > 2)

        # Dla języka EN powinien być tylko Londyn do wyboru.
        url = reverse(
            "manage_subscriptions",
            kwargs={
                "key": user.key.hex,
                "language": "en",
            },
        )
        self.selenium.get(self.live_server_url + url)
        li_locations = self.selenium.find_elements_by_xpath(
            "//input[contains(@id,'id_form-0-locations_')]"
        )
        self.assertEqual(len(li_locations), 1)

        # Usuwamy lokalizację
        url = reverse(
            "manage_subscriptions",
            kwargs={
                "key": user.key.hex,
                "language": "pl",
            },
        )

        self.selenium.get(self.live_server_url + url)

        self.selenium.find_element_by_id("id_form-0-DELETE").click()

        # Zapisz formularz
        with patch(
            "www.tasks.user_notifications_alert.delay"
        ) as user_notifications_alert:

            self.selenium.find_element_by_id("manage-subscriptions-submit").click()
            self.wait_for_element_by_id("messages")

            # Powinna zostać wywołana akcja celery wysyłająca alerty z
            # powiadomieniami dla Biura.
            self.assertEqual(user_notifications_alert.call_count, 1)

        messages = self.selenium.find_element_by_id("messages").text
        self.assertIn("Zmiany zostały zapisane", messages)

        # Sprawdź liczbę lokalizacji (powinna być 0)
        self.assertRaises(
            UserCoursesNotificationFactory._meta.model.DoesNotExist,
            UserCoursesNotificationFactory._meta.model.objects.get,
            user=user,
        )

        # Teraz sprawdzamy formularz opcjonalnych danych osobowych
        self._test_personal_data_form(user.pk)

    def test_cancel_subscription(self):
        # Tworzymy dane poczatkowe
        user = UserNotificationFactory()
        UserCoursesNotificationFactory(
            user=user,
            training=SzkolenieFactory(),
            locations=[LokalizacjaFactory(), LokalizacjaFactory()],
        )
        UserNotificationLogFactory(
            user=user,
        )

        # Użytkonik jest aktywny
        self.assertEqual(user.status, 1)

        # Istnieją obiekty powiązane
        self.assertEqual(
            UserCoursesNotificationFactory._meta.model.objects.filter(
                user=user
            ).count(),
            1,
        )

        self.assertEqual(
            UserNotificationLogFactory._meta.model.objects.filter(user=user).count(), 1
        )

        # Usuń się z subskrypcji

        url = reverse(
            "cancel_subscription",
            kwargs={
                "key": user.key.hex,
                "language": "pl",
            },
        )

        self.selenium.get(self.live_server_url + url)

        # Sprawdź elemenety szablonu (np. czy występują komunikaty).
        page_text = self.selenium.find_element_by_tag_name("body").text

        self.assertIn("Zostałeś wypisany.", page_text)

        self.assertEqual(len(mail.outbox), 1)
        self.assertEqual(
            mail.outbox[0].subject, "[Powiadomienie Wypis] - {}".format(user.email)
        )
        self.assertIn(user.email, mail.outbox[0].body)
        self.assertIn(str(user.pk), mail.outbox[0].body)

        mail.outbox = []

        # Teraz użytkownik powinien mieć status 2 (usunięty)
        user = UserNotificationFactory._meta.model.objects.get(pk=user.pk)
        self.assertEqual(user.status, 2)

        # Nie istnieją obiekty powiązane
        self.assertEqual(
            UserCoursesNotificationFactory._meta.model.objects.filter(
                user=user
            ).count(),
            0,
        )

        self.assertEqual(
            UserNotificationLogFactory._meta.model.objects.filter(user=user).count(), 0
        )

        # Spróbuj jeszcze raz wejść na link - powinniśmy dostać ten sam
        # komunikat, ale już bez zmiany danych.
        self.selenium.get(self.live_server_url + url)
        page_text = self.selenium.find_element_by_tag_name("body").text

        self.assertIn("Zostałeś wypisany.", page_text)

        user2 = UserNotificationFactory._meta.model.objects.get(pk=user.pk)

        self.assertEqual(user2.updated_at, user.updated_at)

        self.assertEqual(len(mail.outbox), 0)


###################
# Kontynuacje
###################


class ContinuationViewTestCase(ALXLiveServerTestCase):
    def test_cancel_continuation_when_invalid_token(self):
        url = reverse(
            "cancel_continuation",
            kwargs={
                "token": "abc",
                "language": "pl",
            },
        )

        self.selenium.get(self.live_server_url + url)

        page_text = self.selenium.find_element_by_tag_name("body").text

        self.assertIn("Niepoprawny token.", page_text)

        self.assertEqual(ContinuationUnsubscribed.objects.all().count(), 0)

    def test_cancel_continuation_when_invalid_salt(self):
        url = reverse(
            "cancel_continuation",
            kwargs={
                "token": signing.dumps({"email": "<EMAIL>"}, salt="abc"),
                "language": "pl",
            },
        )

        self.selenium.get(self.live_server_url + url)

        page_text = self.selenium.find_element_by_tag_name("body").text

        self.assertIn("Niepoprawny token.", page_text)

        self.assertEqual(ContinuationUnsubscribed.objects.all().count(), 0)

    def test_cancel_continuation_when_already_exist(self):
        ContinuationUnsubscribedFactory.create(email="<EMAIL>")

        url = reverse(
            "cancel_continuation",
            kwargs={
                "token": signing.dumps(
                    {"email": "<EMAIL>"}, salt="signing.continuation_training"
                ),
                "language": "pl",
            },
        )

        self.selenium.get(self.live_server_url + url)

        page_text = self.selenium.find_element_by_tag_name("body").text

        self.assertIn("Zostałeś wypisany.", page_text)

        self.assertEqual(ContinuationUnsubscribed.objects.all().count(), 1)

        self.assertEqual(len(mail.outbox), 0)

    def test_cancel_continuation_when_ok(self):
        url = reverse(
            "cancel_continuation",
            kwargs={
                "token": signing.dumps(
                    {"email": "<EMAIL>"}, salt="signing.continuation_training"
                ),
                "language": "pl",
            },
        )

        self.selenium.get(self.live_server_url + url)

        page_text = self.selenium.find_element_by_tag_name("body").text

        self.assertIn("Zostałeś wypisany.", page_text)

        self.assertEqual(ContinuationUnsubscribed.objects.all().count(), 1)

        self.assertTrue(ContinuationUnsubscribed.objects.get(email="<EMAIL>"))

        self.assertEqual(len(mail.outbox), 1)
        self.assertIn("[Kontynuacje Wypis] - <EMAIL>", mail.outbox[0].subject)
        self.assertIn("<EMAIL>", mail.outbox[0].body)

    def test_cancel_continuation_when_en(self):
        url = reverse(
            "cancel_continuation",
            kwargs={
                "token": signing.dumps(
                    {"email": "<EMAIL>"}, salt="signing.continuation_training"
                ),
                "language": "en",
            },
        )

        self.selenium.get(self.live_server_url + url)

        page_text = self.selenium.find_element_by_tag_name("body").text

        self.assertIn("Your subscription has been terminated.", page_text)
        self.assertEqual(len(mail.outbox), 1)


###################
# Subskrypcje
###################


@override_settings(
    DOMENY_DLA_JEZYKOW={
        "pl": "example.pl",
        "en": "example.com",
    }
)
class CancelAllSubscriptionsViewTestCase(ALXTestCase):
    def setUp(self):
        os.environ["NORECAPTCHA_TESTING"] = "True"
        super().setUp()
        cache.clear()

    def test_cancel_when_no_objects(self):
        url = reverse(
            "cancel_all_subscriptions",
            kwargs={
                "language": "pl",
            },
        )

        response = self.client.post(
            url,
            {"email": "<EMAIL>", "g-recaptcha-response": "PASSED"},
            follow=True,
        )
        self.assertEqual(response.status_code, 200)

        self.assertIn(
            "Rozpoczęlismy sprawdzanie, czy twój adres email znajduje ",
            response.content.decode("utf-8"),
        )

        self.assertEqual(len(mail.outbox), 0)

    def test_cancel_when_action_allowed(self):
        url = reverse(
            "cancel_all_subscriptions",
            kwargs={
                "language": "pl",
            },
        )

        response = self.client.post(
            url,
            {"email": "<EMAIL>", "g-recaptcha-response": "PASSED"},
            follow=True,
        )
        self.assertEqual(response.status_code, 200)

        self.assertIn(
            "Rozpoczęlismy sprawdzanie, czy twój adres email znajduje ",
            response.content.decode("utf-8"),
        )

        OdbiorcaFactory.create(email="<EMAIL>")

        response = self.client.post(
            url,
            {"email": "<EMAIL>", "g-recaptcha-response": "PASSED"},
            follow=True,
        )
        self.assertEqual(response.status_code, 200)

        self.assertIn(
            "Musisz odczekać 30 minut pomiędzy kolejnymi " "próbami.",
            response.content.decode("utf-8"),
        )

        self.assertEqual(len(mail.outbox), 0)

    def test_cancel_when_en(self):
        OdbiorcaFactory.create(email="<EMAIL>")

        url = reverse(
            "cancel_all_subscriptions",
            kwargs={
                "language": "en",
            },
        )

        response = self.client.post(
            url,
            {"email": "<EMAIL>", "g-recaptcha-response": "PASSED"},
            follow=True,
        )
        self.assertEqual(response.status_code, 200)

        self.assertIn(
            "We are checking if your email address is on our mailing lists. If ",
            response.content.decode("utf-8"),
        )

        self.assertEqual(len(mail.outbox), 1)
        self.assertIn("Unsubscribe from notifications", mail.outbox[0].subject)
        self.assertIn(
            "http://example.com/en/mailing/unsubscribeme/", mail.outbox[0].body
        )

    def test_cancel_when_newsletter(self):
        OdbiorcaFactory.create(email="<EMAIL>")

        url = reverse(
            "cancel_all_subscriptions",
            kwargs={
                "language": "pl",
            },
        )

        response = self.client.post(
            url,
            {"email": "<EMAIL>", "g-recaptcha-response": "PASSED"},
            follow=True,
        )
        self.assertEqual(response.status_code, 200)

        self.assertIn(
            "Rozpoczęlismy sprawdzanie, czy twój adres email znajduje ",
            response.content.decode("utf-8"),
        )

        self.assertEqual(len(mail.outbox), 1)
        self.assertIn("Rezygnacja z powiadomień", mail.outbox[0].subject)
        self.assertIn("http://example.pl/mailing/wypiszmnie/", mail.outbox[0].body)

    def test_cancel_when_newsletter_already_unsubscribed(self):
        OdbiorcaFactory.create(email="<EMAIL>", status="opt-out")

        url = reverse(
            "cancel_all_subscriptions",
            kwargs={
                "language": "pl",
            },
        )

        response = self.client.post(
            url,
            {"email": "<EMAIL>", "g-recaptcha-response": "PASSED"},
            follow=True,
        )
        self.assertEqual(response.status_code, 200)

        self.assertEqual(len(mail.outbox), 0)

    def test_cancel_when_continuation(self):
        UczestnikFactory.create(email="<EMAIL>")

        url = reverse(
            "cancel_all_subscriptions",
            kwargs={
                "language": "pl",
            },
        )

        response = self.client.post(
            url,
            {"email": "<EMAIL>", "g-recaptcha-response": "PASSED"},
            follow=True,
        )
        self.assertEqual(response.status_code, 200)

        self.assertIn(
            "Rozpoczęlismy sprawdzanie, czy twój adres email znajduje ",
            response.content.decode("utf-8"),
        )

        self.assertEqual(len(mail.outbox), 1)
        self.assertIn("Rezygnacja z powiadomień", mail.outbox[0].subject)
        self.assertIn("http://example.pl/mailing/wypiszmnie/", mail.outbox[0].body)

    def test_cancel_when_continuation_already_unsubscribed(self):
        UczestnikFactory.create(email="<EMAIL>")
        ContinuationUnsubscribedFactory.create(email="<EMAIL>")

        url = reverse(
            "cancel_all_subscriptions",
            kwargs={
                "language": "pl",
            },
        )

        response = self.client.post(
            url,
            {"email": "<EMAIL>", "g-recaptcha-response": "PASSED"},
            follow=True,
        )
        self.assertEqual(response.status_code, 200)

        self.assertEqual(len(mail.outbox), 0)

    def test_cancel_when_user_notification(self):
        UserNotificationFactory.create(email="<EMAIL>")

        url = reverse(
            "cancel_all_subscriptions",
            kwargs={
                "language": "pl",
            },
        )

        response = self.client.post(
            url,
            {"email": "<EMAIL>", "g-recaptcha-response": "PASSED"},
            follow=True,
        )
        self.assertEqual(response.status_code, 200)

        self.assertIn(
            "Rozpoczęlismy sprawdzanie, czy twój adres email znajduje ",
            response.content.decode("utf-8"),
        )

        self.assertEqual(len(mail.outbox), 1)
        self.assertIn("Rezygnacja z powiadomień", mail.outbox[0].subject)
        self.assertIn("http://example.pl/mailing/wypiszmnie/", mail.outbox[0].body)

    def test_cancel_when_user_notification_already_unsubscribed(self):
        UserNotificationFactory.create(email="<EMAIL>", status=2)

        url = reverse(
            "cancel_all_subscriptions",
            kwargs={
                "language": "pl",
            },
        )

        response = self.client.post(
            url,
            {"email": "<EMAIL>", "g-recaptcha-response": "PASSED"},
            follow=True,
        )
        self.assertEqual(response.status_code, 200)

        self.assertEqual(len(mail.outbox), 0)


@override_settings(
    DOMENY_DLA_JEZYKOW={
        "pl": "example.pl",
        "en": "example.com",
    }
)
class DoCancelAllSubscriptionsViewTestCase(ALXLiveServerTestCase):
    def test_cancel_when_invalid_token(self):
        url = reverse(
            "do_cancel_all_subscriptions",
            kwargs={
                "token": "abc",
                "language": "pl",
            },
        )

        self.selenium.get(self.live_server_url + url)

        page_text = self.selenium.find_element_by_tag_name("body").text

        self.assertIn("Niepoprawny token.", page_text)

        self.assertEqual(len(mail.outbox), 0)

    def test_cancel_when_invalid_salt(self):
        url = reverse(
            "do_cancel_all_subscriptions",
            kwargs={
                "token": signing.dumps({"email": "<EMAIL>"}, salt="abc"),
                "language": "pl",
            },
        )

        self.selenium.get(self.live_server_url + url)

        page_text = self.selenium.find_element_by_tag_name("body").text

        self.assertIn("Niepoprawny token.", page_text)

        self.assertEqual(len(mail.outbox), 0)

    def test_cancel_when_nothing_to_do(self):
        url = reverse(
            "do_cancel_all_subscriptions",
            kwargs={
                "token": signing.dumps(
                    {"email": "<EMAIL>"},
                    salt="signing.cancel_all_subscriptions",
                ),
                "language": "pl",
            },
        )

        self.selenium.get(self.live_server_url + url)

        page_text = self.selenium.find_element_by_tag_name("body").text

        self.assertIn(
            "Zostałeś poprawnie wypisany z powiadomień w serwisie.", page_text
        )

        self.assertEqual(len(mail.outbox), 0)

    def test_cancel_newsletter(self):
        odbiorca = OdbiorcaFactory.create(email="<EMAIL>")

        url = reverse(
            "do_cancel_all_subscriptions",
            kwargs={
                "token": signing.dumps(
                    {"email": "<EMAIL>"},
                    salt="signing.cancel_all_subscriptions",
                ),
                "language": "pl",
            },
        )

        self.selenium.get(self.live_server_url + url)

        page_text = self.selenium.find_element_by_tag_name("body").text

        self.assertIn(
            "Zostałeś poprawnie wypisany z powiadomień w serwisie.", page_text
        )

        odbiorca = Odbiorca.objects.get(pk=odbiorca.pk)
        self.assertEqual(odbiorca.status, "opt-out")

        self.assertEqual(len(mail.outbox), 1)
        self.assertIn("[Newsletter Wypis] - <EMAIL>", mail.outbox[0].subject)
        self.assertIn("<EMAIL>", mail.outbox[0].body)
        self.assertIn(
            "http://example.pl/admin/newsletter/odbiorca/{}/".format(odbiorca.pk),
            mail.outbox[0].body,
        )

    def test_cancel_newsletter_when_already_unsubscribed(self):
        OdbiorcaFactory.create(email="<EMAIL>", status="opt-out")

        url = reverse(
            "do_cancel_all_subscriptions",
            kwargs={
                "token": signing.dumps(
                    {"email": "<EMAIL>"},
                    salt="signing.cancel_all_subscriptions",
                ),
                "language": "pl",
            },
        )

        self.selenium.get(self.live_server_url + url)

        page_text = self.selenium.find_element_by_tag_name("body").text

        self.assertIn(
            "Zostałeś poprawnie wypisany z powiadomień w serwisie.", page_text
        )

        self.assertEqual(len(mail.outbox), 0)

    def test_cancel_continuation(self):
        UczestnikFactory.create(email="<EMAIL>")

        url = reverse(
            "do_cancel_all_subscriptions",
            kwargs={
                "token": signing.dumps(
                    {"email": "<EMAIL>"},
                    salt="signing.cancel_all_subscriptions",
                ),
                "language": "pl",
            },
        )

        self.selenium.get(self.live_server_url + url)

        page_text = self.selenium.find_element_by_tag_name("body").text

        self.assertIn(
            "Zostałeś poprawnie wypisany z powiadomień w serwisie.", page_text
        )

        continuation = ContinuationUnsubscribed.objects.get(email="<EMAIL>")
        self.assertTrue(continuation)

        self.assertEqual(len(mail.outbox), 1)
        self.assertIn("[Kontynuacje Wypis] - <EMAIL>", mail.outbox[0].subject)
        self.assertIn("<EMAIL>", mail.outbox[0].body)
        self.assertIn(
            "http://example.pl/admin/www/continuationunsubscribed/{}/".format(
                continuation.pk
            ),
            mail.outbox[0].body,
        )

    def test_cancel_continuation_when_already_unsubscribed(self):
        UczestnikFactory.create(email="<EMAIL>")
        ContinuationUnsubscribedFactory.create(email="<EMAIL>")

        url = reverse(
            "do_cancel_all_subscriptions",
            kwargs={
                "token": signing.dumps(
                    {"email": "<EMAIL>"},
                    salt="signing.cancel_all_subscriptions",
                ),
                "language": "pl",
            },
        )

        self.selenium.get(self.live_server_url + url)

        page_text = self.selenium.find_element_by_tag_name("body").text

        self.assertIn(
            "Zostałeś poprawnie wypisany z powiadomień w serwisie.", page_text
        )

        self.assertEqual(len(mail.outbox), 0)

    def test_cancel_user_notification(self):
        user_notification = UserNotificationFactory.create(email="<EMAIL>")

        url = reverse(
            "do_cancel_all_subscriptions",
            kwargs={
                "token": signing.dumps(
                    {"email": "<EMAIL>"},
                    salt="signing.cancel_all_subscriptions",
                ),
                "language": "pl",
            },
        )

        self.selenium.get(self.live_server_url + url)

        page_text = self.selenium.find_element_by_tag_name("body").text

        self.assertIn(
            "Zostałeś poprawnie wypisany z powiadomień w serwisie.", page_text
        )

        user_notification = UserNotification.objects.get(pk=user_notification.pk)
        self.assertEqual(user_notification.status, 2)

        self.assertEqual(len(mail.outbox), 1)
        self.assertIn("[Powiadomienie Wypis] - <EMAIL>", mail.outbox[0].subject)
        self.assertIn("<EMAIL>", mail.outbox[0].body)
        self.assertIn(
            "http://example.pl/admin/www/usernotification/{}/".format(
                user_notification.pk
            ),
            mail.outbox[0].body,
        )

    def test_cancel_user_notification_when_already_unsubscribed(self):
        UserNotificationFactory.create(email="<EMAIL>", status=2)

        url = reverse(
            "do_cancel_all_subscriptions",
            kwargs={
                "token": signing.dumps(
                    {"email": "<EMAIL>"},
                    salt="signing.cancel_all_subscriptions",
                ),
                "language": "pl",
            },
        )

        self.selenium.get(self.live_server_url + url)

        page_text = self.selenium.find_element_by_tag_name("body").text

        self.assertIn(
            "Zostałeś poprawnie wypisany z powiadomień w serwisie.", page_text
        )

        self.assertEqual(len(mail.outbox), 0)

    def test_cancel_many(self):
        UczestnikFactory.create(email="<EMAIL>")
        UserNotificationFactory.create(email="<EMAIL>")
        OdbiorcaFactory.create(email="<EMAIL>")

        url = reverse(
            "do_cancel_all_subscriptions",
            kwargs={
                "token": signing.dumps(
                    {"email": "<EMAIL>"},
                    salt="signing.cancel_all_subscriptions",
                ),
                "language": "pl",
            },
        )

        self.selenium.get(self.live_server_url + url)

        self.assertEqual(len(mail.outbox), 3)

    def test_cancel_when_en(self):
        OdbiorcaFactory.create(email="<EMAIL>")

        url = reverse(
            "do_cancel_all_subscriptions",
            kwargs={
                "token": signing.dumps(
                    {"email": "<EMAIL>"},
                    salt="signing.cancel_all_subscriptions",
                ),
                "language": "en",
            },
        )

        self.selenium.get(self.live_server_url + url)

        page_text = self.selenium.find_element_by_tag_name("body").text

        self.assertIn("Your all subscriptions have been terminated.", page_text)
        self.assertEqual(len(mail.outbox), 1)


#########
# ETag
#########


class ETagTestCase(ALXTestCase):
    def test_szkolenie_manager(self):
        """
        Testujemy zapytanie SQL zwracające sumę MD5 dla szkolenia.
        """

        # Szkolenie nie istnieje, powinniśmy dostać `None`
        etag = Szkolenie.objects.get_etag("nie-istnieje", "en")
        self.assertEqual(etag, None)

        # Szkolenie istnieje, powinniśmy dostać ciąg MD5
        training = SzkolenieFactory.create()
        etag = Szkolenie.objects.get_etag(training.slug, training.language)
        self.assertTrue(etag)
        self.assertTrue(re.compile("^[a-fA-F0-9]{32}$").search(etag))  # MD5

        # Symulujemy 10 żądań - ETag nie powinien się zmieniać
        for i in range(10):
            self.assertEqual(
                Szkolenie.objects.get_etag(training.slug, training.language), etag
            )

        # Dokonujemy modyfikacji obiektu szkolenia - ETag pownien się zmienić
        training.kod = "KOD"
        training.save()

        etag2 = Szkolenie.objects.get_etag(training.slug, training.language)
        self.assertNotEqual(etag2, etag)

        # Dodajemy 2 terminy do szkolenia - ETag pownien się zmienić
        t1 = TerminSzkoleniaFactory.create(szkolenie=training)
        t2 = TerminSzkoleniaFactory.create(szkolenie=training)

        etag3 = Szkolenie.objects.get_etag(training.slug, training.language)
        self.assertNotEqual(etag3, etag2)

        # Zmieniamy coś w treści terminu - ETag pownien się zmienić
        t1.ile_dni = 23
        t1.save()

        etag4 = Szkolenie.objects.get_etag(training.slug, training.language)
        self.assertNotEqual(etag4, etag3)

        # Usuwamy jeden termin  - ETag pownien się zmienić
        t2.delete()

        etag5 = Szkolenie.objects.get_etag(training.slug, training.language)
        self.assertNotEqual(etag5, etag4)

        # Dodajemy obiekt w SiteModule - ETag pownien się zmienić
        SiteModuleFactory.create()

        etag6 = Szkolenie.objects.get_etag(training.slug, training.language)
        self.assertNotEqual(etag6, etag5)

        # Dodajemy obiekt SiteModule, ale w języku EN - ETag nie pownien się
        # zmienić
        SiteModuleFactory.create(language="en")

        etag7 = Szkolenie.objects.get_etag(training.slug, training.language)
        self.assertEqual(etag7, etag6)

        # Dodajemy stronę statyczną - ETag pownien się zmienić
        page = MyFlatPageFactory.create()

        etag8 = Szkolenie.objects.get_etag(training.slug, training.language)
        self.assertNotEqual(etag8, etag7)

        # Edytujemy stronę statyczną - ETag pownien się zmienić
        page.header_title = "new title"
        page.save()

        etag9 = Szkolenie.objects.get_etag(training.slug, training.language)
        self.assertNotEqual(etag9, etag8)

        # Dodajemy nowe szkolenie do tego samego techtaga - ETag pownien
        # się zmienić
        tmp_training = SzkolenieFactory.create(
            tagi_technologia=training.tagi_technologia.all()
        )

        etag10 = Szkolenie.objects.get_etag(training.slug, training.language)
        self.assertNotEqual(etag10, etag9)

        # Edytujemy szkolenie przypisane do tego samego techtaga - ETag pownien
        # się zmienić
        tmp_training.kod = "KOD"
        tmp_training.save()

        etag11 = Szkolenie.objects.get_etag(training.slug, training.language)
        self.assertNotEqual(etag11, etag10)

        # Dodajemy szkolenie przypisane do innych techtagów - ETag nie pownien
        # się zmienić
        SzkolenieFactory.create()

        etag12 = Szkolenie.objects.get_etag(training.slug, training.language)
        self.assertEqual(etag12, etag11)

    def test_szkolenie_view(self):
        """
        Testujemy kod odpowiedzi widoku szkolenia.
        """

        training = SzkolenieFactory.create()

        url = reverse(
            "detail_pl", kwargs={"slug": training.slug, "language": training.language}
        )

        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

        # Nagłówek ma zawierać ETag
        self.assertIn("etag", response._headers)

        etag = response._headers["etag"][1]

        self.assertTrue(etag)

        # Wysyłamy żądanie z nagłowkiem If-None-Match, powinniśmy teraz dostać
        # kod odpowiedzi 304

        response = self.client.get(url, HTTP_IF_NONE_MATCH=etag)
        self.assertEqual(response.status_code, 304)

        # Symulujemy 10 żądań, każde powinno zwrócić 304
        for i in range(10):
            response = self.client.get(url, HTTP_IF_NONE_MATCH=etag)
            self.assertEqual(response.status_code, 304)

        # Teraz ETag powinien się zmienić, więc i kod żądania również (na 200)
        TerminSzkoleniaFactory.create(szkolenie=training)

        response = self.client.get(url, HTTP_IF_NONE_MATCH=etag)
        self.assertEqual(response.status_code, 200)

        # Szkolenie nie istnieje, zawsze powinien być zwrócony kod 404
        etag = response._headers["etag"][1]
        url = reverse("detail_pl", kwargs={"slug": "XYZ", "language": "pl"})

        response = self.client.get(url, HTTP_IF_NONE_MATCH=etag)
        self.assertEqual(response.status_code, 404)

    def test_tag_technologia_manager(self):
        """
        Testujemy zapytanie SQL zwracające sumę MD5 dla tagu technologia.
        """

        # Tag nie istnieje, powinniśmy dostać `None`
        etag = TagTechnologia.objects.get_etag("nie-istnieje", "en")
        self.assertEqual(etag, None)

        # Tag istnieje, powinniśmy dostać ciąg MD5
        tag = TagTechnologiaFactory.create()
        etag = TagTechnologia.objects.get_etag(tag.slug, tag.language)
        self.assertTrue(etag)
        self.assertTrue(re.compile("^[a-fA-F0-9]{32}$").search(etag))  # MD5

        # Symulujemy 10 żądań - ETag nie powinien się zmieniać
        for i in range(10):
            self.assertEqual(
                TagTechnologia.objects.get_etag(tag.slug, tag.language), etag
            )

        # Dokonujemy modyfikacji obiektu tagu - ETag pownien się zmienić
        tag.ordering = 349673
        tag.save()

        etag2 = TagTechnologia.objects.get_etag(tag.slug, tag.language)
        self.assertNotEqual(etag2, etag)

        # Przypisujemy taga do dwóch szkoleń - ETag pownien się zmienić
        t1 = SzkolenieFactory.create(tagi_technologia=[tag])
        t2 = SzkolenieFactory.create(tagi_technologia=[tag])

        etag3 = TagTechnologia.objects.get_etag(tag.slug, tag.language)
        self.assertNotEqual(etag3, etag2)

        # Zmieniamy coś w treści szkolenia - ETag pownien się zmienić
        t1.nazwa = "test"
        t1.save()

        etag4 = TagTechnologia.objects.get_etag(tag.slug, tag.language)
        self.assertNotEqual(etag4, etag3)

        # Usuwamy jedno szkolenie  - ETag pownien się zmienić
        t2.delete()

        etag5 = TagTechnologia.objects.get_etag(tag.slug, tag.language)
        self.assertNotEqual(etag5, etag4)

        # Dodajemy obiekt w SiteModule - ETag pownien się zmienić
        SiteModuleFactory.create()

        etag6 = TagTechnologia.objects.get_etag(tag.slug, tag.language)
        self.assertNotEqual(etag6, etag5)

        # Dodajemy obiekt SiteModule, ale w języku EN - ETag nie pownien się
        # zmienić
        SiteModuleFactory.create(language="en")

        etag7 = TagTechnologia.objects.get_etag(tag.slug, tag.language)
        self.assertEqual(etag7, etag6)

        # Dodajemy highlight - ETag pownien się zmienić
        highlight = HighlightFactory.create()

        HighlightPlacement.objects.create(
            page=tag,
            highlight=highlight,
        )

        etag8 = TagTechnologia.objects.get_etag(tag.slug, tag.language)
        self.assertNotEqual(etag8, etag7)

        # Dodajemy highlight do innego Tech taga - ETag nie pownien się zmienić
        HighlightPlacement.objects.create(
            page=TagTechnologiaFactory.create(),
            highlight=HighlightFactory.create(),
        )

        etag9 = TagTechnologia.objects.get_etag(tag.slug, tag.language)
        self.assertEqual(etag9, etag8)

        # Edytujemy przypisany highlight - ETag pownien się zmienić
        highlight.tytul = "!"
        highlight.save()

        etag10 = TagTechnologia.objects.get_etag(tag.slug, tag.language)
        self.assertNotEqual(etag10, etag9)

        # Dopisujemy do szkolenia termin - ETag pownien się zmienić
        term = TerminSzkoleniaFactory(szkolenie=t1)

        etag11 = TagTechnologia.objects.get_etag(tag.slug, tag.language)
        self.assertNotEqual(etag11, etag10)

        # Modyfikujemy termin - ETag pownien się zmienić
        term.ile_dni = 10
        term.save()

        etag12 = TagTechnologia.objects.get_etag(tag.slug, tag.language)
        self.assertNotEqual(etag12, etag11)

        # Usuwamy termin - ETag pownien się zmienić
        term.delete()

        etag13 = TagTechnologia.objects.get_etag(tag.slug, tag.language)
        self.assertNotEqual(etag13, etag12)

        # Dodajemy termin do skzolenia nie związanego z tagiem - ETag nie
        # pownien się zmienić
        TerminSzkoleniaFactory()
        TerminSzkoleniaFactory()

        etag14 = TagTechnologia.objects.get_etag(tag.slug, tag.language)
        self.assertEqual(etag14, etag13)

        # Dodajemy stronę statyczną - ETag pownien się zmienić
        page = MyFlatPageFactory.create()

        etag15 = TagTechnologia.objects.get_etag(tag.slug, tag.language)
        self.assertNotEqual(etag15, etag14)

        # Edytujemy stronę statyczną - ETag pownien się zmienić
        page.header_title = "new title"
        page.save()

        etag16 = TagTechnologia.objects.get_etag(tag.slug, tag.language)
        self.assertNotEqual(etag16, etag15)

        # Zmieniamy coś w szkoleniu EN, które jest na bazie naszego szkolenia
        t3en = SzkolenieFactory.create(language="en", base_translation=t1)
        etag17 = TagTechnologia.objects.get_etag(tag.slug, tag.language)
        self.assertNotEqual(etag17, etag16)

        # Dodajemy EN termin - etag powinien sie zmienić
        TerminSzkoleniaFactory(szkolenie=t3en)
        etag18 = TagTechnologia.objects.get_etag(tag.slug, tag.language)
        self.assertNotEqual(etag18, etag17)

        # Dodajemy losowe terminy EN - etag nie powinien się zmienić
        TerminSzkoleniaEnFactory.create()
        TerminSzkoleniaEnFactory.create()

        etag19 = TagTechnologia.objects.get_etag(tag.slug, tag.language)
        self.assertEqual(etag19, etag18)

        # Dodajemy trenerow - etag powinien sie zmienic
        prowadzacy = TagTechnologiaProwadzacyFactory.create_batch(5, tagtechnologia=tag)

        etag20 = TagTechnologia.objects.get_etag(tag.slug, tag.language)
        self.assertNotEqual(etag20, etag19)

        # Zmieniamy coś w prowadzacym - etag powinien sie zmienic
        prowadzacy[0].prowadzacy.ordering = 1
        prowadzacy[0].prowadzacy.save()

        etag21 = TagTechnologia.objects.get_etag(tag.slug, tag.language)
        self.assertNotEqual(etag21, etag20)

        # Ddodajemy niepowiazanego prowadzacego - etag nie powinien sie zmienic
        TagTechnologiaProwadzacyFactory.create_batch(3)

        etag22 = TagTechnologia.objects.get_etag(tag.slug, tag.language)
        self.assertEqual(etag22, etag21)

    def test_tag_technologia_view(self):
        """
        Testujemy kod odpowiedzi widoku taga technologii.
        """

        tag = TagTechnologiaFactory.create()

        url = reverse(
            "index_by_technologia_pl",
            kwargs={"technologia_slug": tag.slug, "language": tag.language},
        )

        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

        # Nagłówek ma zawierać ETag
        self.assertIn("etag", response._headers)

        etag = response._headers["etag"][1]

        self.assertTrue(etag)

        # Wysyłamy żądanie z nagłowkiem If-None-Match, powinniśmy teraz dostać
        # kod odpowiedzi 304

        response = self.client.get(url, HTTP_IF_NONE_MATCH=etag)
        self.assertEqual(response.status_code, 304)

        # Symulujemy 10 żądań, każde powinno zwrócić 304
        for i in range(10):
            response = self.client.get(url, HTTP_IF_NONE_MATCH=etag)
            self.assertEqual(response.status_code, 304)

        # Teraz ETag powinien się zmienić, więc i kod żądania również (na 200)
        SzkolenieFactory.create(tagi_technologia=[tag])

        response = self.client.get(url, HTTP_IF_NONE_MATCH=etag)
        self.assertEqual(response.status_code, 200)

        # Tag nie istnieje, zawsze powinien być zwrócony kod 404
        etag = response._headers["etag"][1]
        url = reverse(
            "index_by_technologia_pl",
            kwargs={"technologia_slug": "XYZ", "language": tag.language},
        )

        response = self.client.get(url, HTTP_IF_NONE_MATCH=etag)
        self.assertEqual(response.status_code, 404)

    def test_my_flat_page_manager(self):
        """
        Testujemy zapytanie SQL zwracające sumę MD5 dla strony statycznej.
        """

        # Strona nie istnieje, powinniśmy dostać `None`
        etag = MyFlatPage.objects.get_etag("nie-istnieje", "en")
        self.assertEqual(etag, None)

        # Strona istnieje, powinniśmy dostać ciąg MD5
        page = MyFlatPageFactory.create(szkolenie=None)
        etag = MyFlatPage.objects.get_etag(page.slug, page.language)
        self.assertTrue(etag)
        self.assertTrue(re.compile("^[a-fA-F0-9]{32}$").search(etag))  # MD5

        # Symulujemy 10 żądań - ETag nie powinien się zmieniać
        for i in range(10):
            self.assertEqual(
                MyFlatPage.objects.get_etag(page.slug, page.language), etag
            )

        # Dokonujemy modyfikacji obiektu strony - ETag pownien się zmienić
        page.szkolenie = SzkolenieFactory.create()
        page.save()

        etag2 = MyFlatPage.objects.get_etag(page.slug, page.language)
        self.assertNotEqual(etag2, etag)

        # Dodajemy 2 terminy do szkolenia - ETag pownien się zmienić
        t1 = TerminSzkoleniaFactory.create(szkolenie=page.szkolenie, prowadzacy=None)
        t2 = TerminSzkoleniaFactory.create(szkolenie=page.szkolenie, prowadzacy=None)

        etag3 = MyFlatPage.objects.get_etag(page.slug, page.language)
        self.assertNotEqual(etag3, etag2)

        # Zmieniamy coś w treści terminu - ETag pownien się zmienić
        t1.ile_dni = 23
        t1.save()

        etag4 = MyFlatPage.objects.get_etag(page.slug, page.language)
        self.assertNotEqual(etag4, etag3)

        # Usuwamy jeden termin  - ETag pownien się zmienić
        t2.delete()

        etag5 = MyFlatPage.objects.get_etag(page.slug, page.language)
        self.assertNotEqual(etag5, etag4)

        # Dodajemy obiekt w SiteModule - ETag pownien się zmienić
        SiteModuleFactory.create()

        etag6 = MyFlatPage.objects.get_etag(page.slug, page.language)
        self.assertNotEqual(etag6, etag5)

        # Dodajemy obiekt SiteModule, ale w języku EN - ETag nie pownien się
        # zmienić
        SiteModuleFactory.create(language="en")

        etag7 = MyFlatPage.objects.get_etag(page.slug, page.language)
        self.assertEqual(etag7, etag6)

        # Dodajemy stronę statyczną - ETag pownien się zmienić
        p = MyFlatPageFactory.create(language=page.language)

        etag8 = MyFlatPage.objects.get_etag(page.slug, page.language)
        self.assertNotEqual(etag8, etag7)

        # Dodajemy stronę statyczną, ale w języku EN - ETag nie pownien się
        # zmienić
        MyFlatPageFactory.create(language="en")

        etag9 = MyFlatPage.objects.get_etag(page.slug, page.language)
        self.assertEqual(etag9, etag8)

        # Edytujemy stronę statyczną - ETag pownien się zmienić
        p.header_title = "some title"
        p.save()

        etag10 = MyFlatPage.objects.get_etag(page.slug, page.language)
        self.assertNotEqual(etag10, etag9)

        # Dodajemy szkolenie EN, które jest na bazie naszego szkolenia -
        # etag nie powinien się zmienić.
        szkolenie_en = SzkolenieFactory.create(
            language="en", base_translation=page.szkolenie
        )
        etag11 = MyFlatPage.objects.get_etag(page.slug, page.language)
        self.assertEqual(etag11, etag10)

        # Dodajemy EN termin - etag powinien sie zmienić
        TerminSzkoleniaFactory(szkolenie=szkolenie_en, prowadzacy=None)
        etag12 = MyFlatPage.objects.get_etag(page.slug, page.language)
        self.assertNotEqual(etag12, etag11)

        # Dodajemy losowe terminy EN - etag nie powinien się zmienić
        TerminSzkoleniaEnFactory.create(prowadzacy=None)
        TerminSzkoleniaEnFactory.create(prowadzacy=None)

        etag13 = MyFlatPage.objects.get_etag(page.slug, page.language)
        self.assertEqual(etag13, etag12)

        # Zmieniamy coś w trenerach  - etag powinien sie zmienić
        ProwadzacyFactory.create()

        etag14 = MyFlatPage.objects.get_etag(page.slug, page.language)
        self.assertNotEqual(etag14, etag13)

    def test_my_flat_page_view(self):
        """
        Testujemy kod odpowiedzi widoku strony statycznej.
        """

        page = MyFlatPageFactory.create()

        url = reverse(
            "my_flat_page",
            kwargs={
                "slug": page.slug,
                "language": page.language,
            },
        )

        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

        # Nagłówek ma zawierać ETag
        self.assertIn("etag", response._headers)

        etag = response._headers["etag"][1]

        self.assertTrue(etag)

        # Wysyłamy żądanie z nagłowkiem If-None-Match, powinniśmy teraz dostać
        # kod odpowiedzi 304

        response = self.client.get(url, HTTP_IF_NONE_MATCH=etag)
        self.assertEqual(response.status_code, 304)

        # Symulujemy 10 żądań, każde powinno zwrócić 304
        for i in range(10):
            response = self.client.get(url, HTTP_IF_NONE_MATCH=etag)
            self.assertEqual(response.status_code, 304)

        # Teraz ETag powinien się zmienić, więc i kod żądania również (na 200)
        TerminSzkoleniaFactory.create(szkolenie=page.szkolenie)

        response = self.client.get(url, HTTP_IF_NONE_MATCH=etag)
        self.assertEqual(response.status_code, 200)

        # Tag nie istnieje, zawsze powinien być zwrócony kod 404
        etag = response._headers["etag"][1]
        url = reverse(
            "my_flat_page",
            kwargs={
                "slug": "XYZ",
                "language": page.language,
            },
        )

        response = self.client.get(url, HTTP_IF_NONE_MATCH=etag)
        self.assertEqual(response.status_code, 404)


#######
# RODO
#######


class RODOTestCase(ALXTestCase):
    def test_no_key(self):
        url = reverse("rodo", args=("newsletter",))

        response = self.client.get(url)
        self.assertEqual(response.status_code, 404)

        response = self.client.get(url + "?key=")
        self.assertEqual(response.status_code, 404)

    def test_invalid_key(self):
        url = reverse("rodo", args=("newsletter",))

        response = self.client.get(url + "?key=«§·²³€≠©ßęŋðæ’”µ…’ćź„ń‘")
        self.assertEqual(response.status_code, 200)

    def test_check_content(self):
        key = email_signing("<EMAIL>")

        url = reverse("rodo", args=("newsletter",))
        response = self.client.get(url + "?key=" + key)
        self.assertContains(
            response,
            "Dziękujemy za wyrażenie zgody na otrzymywanie newslettera.",
            status_code=200,
        )

        url = reverse("rodo", args=("notification",))
        response = self.client.get(url + "?key=" + key)
        self.assertContains(
            response,
            "Dziękujemy za wyrażenie zgody na otrzymywanie powiadomień o "
            "terminach szkoleń.",
            status_code=200,
        )

        url = reverse("rodo", args=("notification-and-newsletter",))
        response = self.client.get(url + "?key=" + key)
        self.assertContains(
            response,
            "Dziękujemy za wyrażenie zgody na otrzymywanie powiadomień o "
            "terminach szkoleń i newslettera.",
            status_code=200,
        )

    def test_newsletter_with_entries(self):
        url = reverse("rodo", args=("newsletter",))
        key = email_signing("<EMAIL>")

        newsletter = OdbiorcaFactory.create(email="<EMAIL>", lang="pl")
        newsletter.tos_agreement = None
        newsletter.save()

        notification = UserNotificationFactory.create(email="<EMAIL>")
        notification.tos_agreement = None
        notification.save()

        response = self.client.get(url + "?key=" + key)
        self.assertEqual(response.status_code, 200)

        newsletter = Odbiorca.objects.get(pk=newsletter.pk)
        self.assertTrue(newsletter.tos_agreement)

        notification = UserNotification.objects.get(pk=notification.pk)
        self.assertIsNone(notification.tos_agreement)

    def test_notification_with_entries(self):
        url = reverse("rodo", args=("notification",))
        key = email_signing("<EMAIL>")

        newsletter = OdbiorcaFactory.create(email="<EMAIL>", lang="pl")
        newsletter.tos_agreement = None
        newsletter.save()

        notification = UserNotificationFactory.create(email="<EMAIL>")
        notification.tos_agreement = None
        notification.save()

        response = self.client.get(url + "?key=" + key)
        self.assertEqual(response.status_code, 200)

        newsletter = Odbiorca.objects.get(pk=newsletter.pk)
        self.assertIsNone(newsletter.tos_agreement)

        notification = UserNotification.objects.get(pk=notification.pk)
        self.assertTrue(notification.tos_agreement)

    def test_notification_and_newsletter_with_entries(self):
        url = reverse("rodo", args=("notification-and-newsletter",))
        key = email_signing("<EMAIL>")

        newsletter = OdbiorcaFactory.create(email="<EMAIL>", lang="pl")
        newsletter.tos_agreement = None
        newsletter.save()

        notification = UserNotificationFactory.create(email="<EMAIL>")
        notification.tos_agreement = None
        notification.save()

        response = self.client.get(url + "?key=" + key)
        self.assertEqual(response.status_code, 200)

        newsletter = Odbiorca.objects.get(pk=newsletter.pk)
        self.assertTrue(newsletter.tos_agreement)

        notification = UserNotification.objects.get(pk=notification.pk)
        self.assertTrue(notification.tos_agreement)

    def test_with_authorized_entries(self):
        url = reverse("rodo", args=("notification-and-newsletter",))
        key = email_signing("<EMAIL>")

        newsletter = OdbiorcaFactory.create(email="<EMAIL>")
        newsletter_tos_agreement = newsletter.tos_agreement
        self.assertTrue(newsletter_tos_agreement)

        notification = UserNotificationFactory.create(email="<EMAIL>")
        notification_tos_agreement = notification.tos_agreement
        self.assertTrue(notification_tos_agreement)

        response = self.client.get(url + "?key=" + key)
        self.assertEqual(response.status_code, 200)

        notification = UserNotification.objects.get(pk=notification.pk)
        self.assertEqual(notification.tos_agreement, notification_tos_agreement)

        newsletter = Odbiorca.objects.get(pk=newsletter.pk)
        self.assertEqual(newsletter.tos_agreement, newsletter_tos_agreement)


#######
# Stats
#######


class StatsLiveServerTestCase(ALXLiveServerTestCase):
    def test_stats_are_working(self):
        UczestnikFactory.create(termin__odbylo_sie=True, status=1)
        UserCoursesNotificationFactory.create(
            locations=[
                LokalizacjaFactory.create(reklamowana=True),
                LokalizacjaFactory.create(),
            ]
        )
        OdbiorcaFactory.create()
        UczestnikFactory.create(
            termin__odbylo_sie=True,
            status=1,
            termin__szkolenie=KursFactory.create(),
            termin__lokalizacja=LokalizacjaFactory.create(
                ulica_i_numer="jasna", shortname="warszawa"
            ),
        )

        self.admin_login()

        for no in range(1, 8):
            self.selenium.get(
                self.live_server_url + reverse("stats") + "?wykres={}".format(no)
            )
            # Jak to jest w body, to znaczy, że JS "strawil" dane i je wyswietli
            self.assertIn(
                "new google.visualization.DataTable", self.selenium.page_source
            )


class UczestnikPlikTestCase(ALXTestCase):
    image_data = (
        b"\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00"
        b"\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\x01sRGB"
        b"\x00\xae\xce\x1c\xe9\x00\x00\x00\tpHYs\x00\x00\x0b\x13\x00"
        b"\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\x07tIME\x07\xdb"
        b"\x0c\x17\x020;\xd1\xda\xcf\xd2\x00\x00\x00\x0cIDAT\x08\xd7c"
        b"\xf8\xff\xff?\x00\x05\xfe\x02\xfe\xdc\xccY\xe7\x00\x00\x00"
        b"\x00IEND\xaeB`\x82"
    )

    def test_file_is_not_an_image(self):
        zgloszenie = ZgloszenieFactory.create()
        uczestnik = UczestnikFactory.create(
            zgloszenie_form=zgloszenie,
            prywatny=True,
            za_kurs_zaplace=10,
            termin=TerminSzkoleniaFactory.create(szkolenie=KursFactory.create()),
        )

        url_podziekowania = reverse(
            "dziekujemy_za_zgloszenie",
            kwargs={
                "token": zgloszenie.token,
                "language": uczestnik.termin.szkolenie.language,
            },
        )

        img = BytesIO(b"mybinarydata")
        img.name = "myimage.jpg"

        response = self.client.post(
            url_podziekowania,
            {"file": img},
            **{"HTTP_X_REQUESTED_WITH": "XMLHttpRequest"},
        )

        self.assertContains(
            response, "Prześlij poprawny plik graficzny.", status_code=400
        )

    def test_empty_form(self):
        zgloszenie = ZgloszenieFactory.create()
        uczestnik = UczestnikFactory.create(
            zgloszenie_form=zgloszenie,
            prywatny=True,
            za_kurs_zaplace=10,
            termin=TerminSzkoleniaFactory.create(szkolenie=KursFactory.create()),
        )

        url_podziekowania = reverse(
            "dziekujemy_za_zgloszenie",
            kwargs={
                "token": zgloszenie.token,
                "language": uczestnik.termin.szkolenie.language,
            },
        )

        response = self.client.post(
            url_podziekowania, {}, **{"HTTP_X_REQUESTED_WITH": "XMLHttpRequest"}
        )

        self.assertContains(response, "To pole jest wymagane.", status_code=400)

    def test_more_than_4_files(self):
        zgloszenie = ZgloszenieFactory.create()
        uczestnik = UczestnikFactory.create(
            zgloszenie_form=zgloszenie,
            prywatny=True,
            za_kurs_zaplace=10,
            termin=TerminSzkoleniaFactory.create(szkolenie=KursFactory.create()),
        )

        UczestnikPlikFactory.create_batch(4, participant=uczestnik)

        url_podziekowania = reverse(
            "dziekujemy_za_zgloszenie",
            kwargs={
                "token": zgloszenie.token,
                "language": uczestnik.termin.szkolenie.language,
            },
        )

        img = BytesIO(self.image_data)
        img.name = "myimage.jpg"

        response = self.client.post(
            url_podziekowania,
            {"file": img},
            **{"HTTP_X_REQUESTED_WITH": "XMLHttpRequest"},
        )

        self.assertContains(response, "Limit dodanych ", status_code=400)

    def test_file_saved(self):
        zgloszenie = ZgloszenieFactory.create()
        uczestnik = UczestnikFactory.create(
            zgloszenie_form=zgloszenie,
            prywatny=True,
            za_kurs_zaplace=10,
            termin=TerminSzkoleniaFactory.create(szkolenie=KursFactory.create()),
        )

        url_podziekowania = reverse(
            "dziekujemy_za_zgloszenie",
            kwargs={
                "token": zgloszenie.token,
                "language": uczestnik.termin.szkolenie.language,
            },
        )

        img = BytesIO(self.image_data)
        img.name = "myimage.jpg"

        response = self.client.post(
            url_podziekowania,
            {"file": img},
            **{"HTTP_X_REQUESTED_WITH": "XMLHttpRequest"},
        )

        self.assertEqual(response.status_code, 200)

        plik = UczestnikPlik.objects.get(participant=uczestnik)
        self.assertTrue(plik.file.url)

        # usuniecie pliku, usuwa fizycznie plik
        plik.delete()

        with self.assertRaises(ValueError):
            self.assertFalse(plik.file.url)
