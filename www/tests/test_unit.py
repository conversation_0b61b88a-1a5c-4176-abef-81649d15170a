import datetime
import math
import os
import random
import unittest
from decimal import Decimal
from io import BytesIO

import factory
from dateutil.parser import parse
from django.conf import settings
from django.core import mail, signing
from django.core.exceptions import ValidationError
from django.db import IntegrityError, models
from django.template.base import TemplateSyntaxError
from django.test import TestCase
from django.test.utils import override_settings
from django.urls import reverse
from django.utils.translation import activate
from freezegun import freeze_time
from mock import MagicMock, patch

import www.tasks
from alx_auth.factories import UserFactory
from common import my_slugify
from optout.testfactories import OptOutFactory
from www.management.commands import (
    continuation_training,
    grupa_rusza,
    hunt_the_untrained,
    installment_notification,
    office_notifications,
    podsumowanie_terminow,
    resend_activation_email,
    sixteen_days_before_start,
    unsubscribe_system_users,
    unsubscribe_trained_users,
    user_notifications,
)
from www.models import (
    AutoresponderLog,
    CertificateGroup,
    CertificateNo,
    ContinuationLog,
    DiscountCode,
    FakturaKorekta,
    FakturaWysylka,
    Graduate,
    Leave,
    Lokalizacja,
    Prowadzacy,
    TerminSzkolenia,
    TerminSzkoleniaLog,
    Uczestnik,
    UczestnikNotification,
    UserCoursesNotification,
    UserNotification,
    UserNotificationLog,
    parse_miejscowosc_kod,
)
from www.tasks import (
    poinformuj_trenerow_o_potwierdzeniu_terminu,
    poinformuj_zapisanych_o_potwierdzeniu_terminu,
    poinformuj_biuro_o_potwierdzeniu_terminu,
    to_sentence,
)
from www.templatetags import myfilters, mytags
from www.testfactories import (
    AnkietaFactory,
    AssetFactory,
    AutoresponderLogFactory,
    CertificateNoFactory,
    ContinuationLogFactory,
    ContinuationUnsubscribedFactory,
    DiscountCodeFactory,
    DzienSzkoleniaFactory,
    FakturaKorektaFactory,
    FakturaWysylkaFactory,
    GraduateFactory,
    GrupaZaszeregowaniaFactory,
    HighlightPlacementFactory,
    KursFactory,
    LeaveFactory,
    LokalizacjaFactory,
    MyFlatPageFactory,
    OdbiorcaFactory,
    OdpowiedzFactory,
    PodpytanieFactory,
    ProwadzacyFactory,
    PytanieFactory,
    SalaFactory,
    SzkolenieFactory,
    SzkolenieWariantFactory,
    TagTechnologiaFactory,
    TekstOTerminachFactory,
    TerminSzkoleniaEnFactory,
    TerminSzkoleniaFactory,
    TerminSzkoleniaLogFactory,
    TerminSzkoleniaMailFactory,
    TerminSzkoleniaMailUczestnikFactory,
    UczestnikFactory,
    UczestnikNotificationFactory,
    UserCoursesNotificationFactory,
    UserNotificationFactory,
    UserNotificationLogFactory,
    WalutaFactory,
    WyborFactory,
    ZgloszenieFactory,
)
from www.testhelpers import ALXTestCase, ALXTransactionTestCase

from .. import api, utils, validators
from ..forms import (
    FakturaWysylkaAddAdminForm,
    SubscriptionForm,
    UserNotificationPersonalDataForm,
    UserNotificationReportAdminForm,
)
from .patches import APIZliczaczPatch


class ProwadzacyTestCase(ALXTestCase):
    def test_imie_nazwisko(self):
        p = ProwadzacyFactory.create(imie="A", nazwisko="B")
        self.assertEqual(p.imie_naziwsko(), "A B")

        p = ProwadzacyFactory.create(imie="A", nazwisko="B", tytul_naukowy="inż.")
        self.assertEqual(p.imie_naziwsko(), "inż. A B")

    def test_tytul_imie(self):
        p = ProwadzacyFactory.create(imie="A")
        self.assertEqual(p.tytul_imie(), "A")

        p = ProwadzacyFactory.create(imie="A", tytul_naukowy="inż.")
        self.assertEqual(p.tytul_imie(), "A (inż.)")

    def test_manager(self):
        today = datetime.date.today()
        ProwadzacyFactory.create_batch(9)

        # Dodajemy prowadzacego
        prowadzacy = ProwadzacyFactory.create()

        self.assertEqual(Prowadzacy.objects.active().count(), 10)

        # Ustawiamy prowadzacego jako niekatywnego
        prowadzacy.pracowal_do = today
        prowadzacy.save()

        self.assertEqual(Prowadzacy.objects.active().count(), 9)

        self.assertEqual(Prowadzacy.objects.active(date=today).count(), 10)

        self.assertEqual(
            Prowadzacy.objects.active(date=today + datetime.timedelta(days=1)).count(),
            9,
        )


class SalaTestCase(ALXTestCase):
    def test_get_ulica_i_numer(self):
        sala = SalaFactory.create(lokalizacja__ulica_i_numer="XYZ")

        self.assertEqual(sala.get_ulica_i_numer(), "XYZ")

        sala = SalaFactory.create(lokalizacja__ulica_i_numer="XYZ", ulica_i_numer="ABC")

        self.assertEqual(sala.get_ulica_i_numer(), "ABC")

        sala = SalaFactory.create(
            lokalizacja__ulica_i_numer="",
        )

        self.assertEqual(sala.get_ulica_i_numer(), "")

    def test_instrukcja_dotarcia(self):
        sala = SalaFactory.create(lokalizacja__instrukcja_dotarcia="XYZ")

        self.assertEqual(sala.get_instrukcja_dotarcia(), "XYZ")

        sala = SalaFactory.create(
            lokalizacja__instrukcja_dotarcia="XYZ", instrukcja_dotarcia="ABC"
        )

        self.assertEqual(sala.get_instrukcja_dotarcia(), "ABC")

        sala = SalaFactory.create(
            lokalizacja__instrukcja_dotarcia="",
        )

        self.assertEqual(sala.get_instrukcja_dotarcia(), "")

    def test_get_ulica_i_numer_i_instrukcja_dotarcia(self):
        sala = SalaFactory.create(
            ulica_i_numer="XYZ", lokalizacja__instrukcja_dotarcia="ABC"
        )

        self.assertEqual(sala.get_ulica_i_numer(), "XYZ")
        self.assertEqual(sala.get_instrukcja_dotarcia(), "")

        sala = SalaFactory.create(ulica_i_numer="XYZ", instrukcja_dotarcia="123")

        self.assertEqual(sala.get_ulica_i_numer(), "XYZ")
        self.assertEqual(sala.get_instrukcja_dotarcia(), "123")


class SzkolenieTestCase(ALXTestCase):
    def test_krotka_nazwa_lub_nazwa(self):
        szkolenie = SzkolenieFactory.create(nazwa="abc")
        self.assertEqual(szkolenie.krotka_nazwa_lub_nazwa, "abc")

        szkolenie = SzkolenieFactory.create(nazwa="abc", krotka_nazwa="123")
        self.assertEqual(szkolenie.krotka_nazwa_lub_nazwa, "123")

    def test_jest_wariantem(self):
        szkolenie = SzkolenieFactory.create(aktywne=False)
        self.assertIsNone(szkolenie.jest_wariantem())

        szkolenie_glowne = SzkolenieWariantFactory.create(wariant=szkolenie).szkolenie

        self.assertEqual(szkolenie.jest_wariantem(), szkolenie_glowne)

        szkolenie_glowne.aktywne = False
        szkolenie_glowne.save()

        self.assertIsNone(szkolenie.jest_wariantem())
        self.assertEqual(szkolenie.jest_wariantem(aktywne=False), szkolenie_glowne)

        szkolenie.aktywne = True
        szkolenie.save()

        self.assertIsNone(szkolenie.jest_wariantem(aktywne=False))


class TerminSzkoleniaTestCase(ALXTestCase):
    def test_get_for_continuation_gdy_brak(self):
        self.assertFalse(TerminSzkolenia.objects.get_for_continuation())

    def test_get_for_continuation_gdy_terminy_bez_flagi(self):
        TerminSzkoleniaFactory.create_batch(2)
        self.assertFalse(TerminSzkolenia.objects.get_for_continuation())

    def test_get_for_continuation_gdy_terminy_bez_szkolenia_z_kontynuacja(self):
        TerminSzkoleniaFactory.create_batch(2, szkolenie__maile_o_kontynuacji=True)
        self.assertFalse(TerminSzkolenia.objects.get_for_continuation())

    def test_get_for_continuation_gdy_terminy_nie_odbyly_sie(self):
        TerminSzkoleniaFactory.create_batch(
            2,
            szkolenie=SzkolenieFactory.create(
                maile_o_kontynuacji=True, kontynuacja=SzkolenieFactory.create()
            ),
            odbylo_sie=False,
        )
        self.assertFalse(TerminSzkolenia.objects.get_for_continuation())

    def test_get_for_continuation_gdy_szkolenie_nieaktywne(self):
        TerminSzkoleniaFactory.create_batch(
            2,
            szkolenie=SzkolenieFactory.create(
                maile_o_kontynuacji=True,
                kontynuacja=SzkolenieFactory.create(aktywne=False),
            ),
            odbylo_sie=True,
            termin=datetime.date.today() - datetime.timedelta(days=2),
        )
        self.assertFalse(TerminSzkolenia.objects.get_for_continuation())

    def test_get_for_continuation_gdy_terminy_zbyt_stare(self):
        TerminSzkoleniaFactory.create_batch(
            2,
            szkolenie=SzkolenieFactory.create(
                maile_o_kontynuacji=True,
                kontynuacja=SzkolenieFactory.create(),
            ),
            odbylo_sie=True,
            termin=datetime.date(2009, 1, 1),
        )
        TerminSzkoleniaFactory.create_batch(
            2,
            szkolenie=SzkolenieFactory.create(
                maile_o_kontynuacji=True,
                kontynuacja=SzkolenieFactory.create(),
            ),
            odbylo_sie=True,
            termin_zakonczenia=datetime.date(2009, 1, 1),
        )
        self.assertFalse(TerminSzkolenia.objects.get_for_continuation())

    def test_get_for_continuation_gdy_nie_podana_data_zakonczenia(self):
        # ---- not related
        TerminSzkoleniaFactory.create_batch(3)
        TerminSzkoleniaFactory.create_batch(
            3,
            szkolenie=SzkolenieFactory.create(
                maile_o_kontynuacji=True,
                kontynuacja=SzkolenieFactory.create(),
            ),
            odbylo_sie=True,
            termin_zakonczenia=datetime.date.today(),
        )
        TerminSzkoleniaFactory.create_batch(
            3,
            szkolenie=SzkolenieFactory.create(
                maile_o_kontynuacji=True,
                kontynuacja=SzkolenieFactory.create(),
            ),
            odbylo_sie=True,
            termin_zakonczenia=None,
            termin=datetime.date.today(),
        )
        # --------------

        TerminSzkoleniaFactory.create(
            szkolenie=SzkolenieFactory.create(
                maile_o_kontynuacji=True,
                kontynuacja=SzkolenieFactory.create(),
            ),
            odbylo_sie=True,
            termin=datetime.date.today() - datetime.timedelta(days=2),
        )
        TerminSzkoleniaFactory.create(
            szkolenie=SzkolenieFactory.create(
                maile_o_kontynuacji=True,
                kontynuacja=SzkolenieFactory.create(),
            ),
            odbylo_sie=True,
            termin=datetime.date.today() - datetime.timedelta(days=2),
            termin_zakonczenia=datetime.date.today() - datetime.timedelta(days=2),
        )
        self.assertEqual(TerminSzkolenia.objects.get_for_continuation().count(), 2)

    def test_get_for_continuation_gdy_podana_data_zakonczenia(self):
        TerminSzkoleniaFactory.create_batch(
            2,
            szkolenie=SzkolenieFactory.create(
                maile_o_kontynuacji=True,
                kontynuacja=SzkolenieFactory.create(),
            ),
            odbylo_sie=True,
            termin_zakonczenia=datetime.date(2018, 5, 5),
        )  # not related

        TerminSzkoleniaFactory.create_batch(
            3,
            szkolenie=SzkolenieFactory.create(
                maile_o_kontynuacji=True,
                kontynuacja=SzkolenieFactory.create(),
            ),
            odbylo_sie=True,
            termin_zakonczenia=datetime.date(2018, 4, 4),
        )

        self.assertEqual(
            TerminSzkolenia.objects.get_for_continuation(
                termin_zakonczenia=datetime.date(2018, 4, 4)
            ).count(),
            3,
        )

    def test_samodzielny_termin_gdy_prawda(self):
        TerminSzkoleniaFactory.create_batch(
            3, termin_nadrzedny=TerminSzkoleniaFactory.create()
        )

        termin = TerminSzkoleniaFactory.create()
        self.assertTrue(termin.samodzielny_termin())

    def test_samodzielny_termin_gdy_falsz(self):
        termin_nadrzedny = TerminSzkoleniaFactory.create()
        terminy_podrzedne = TerminSzkoleniaFactory.create_batch(
            3, termin_nadrzedny=termin_nadrzedny
        )

        self.assertFalse(terminy_podrzedne[0].samodzielny_termin())
        self.assertFalse(termin_nadrzedny.samodzielny_termin())

    def test_samodzielny_termin_liczba_zapytan(self):
        termin = TerminSzkoleniaFactory.create()

        with self.assertNumQueries(1):
            self.assertTrue(termin.samodzielny_termin())

        termin = TerminSzkoleniaFactory.create()
        termin = TerminSzkolenia.objects.annotate(
            liczba_terminow_podrzednych=models.Count("nadrzedne")
        ).get(pk=termin.pk)

        with self.assertNumQueries(0):
            self.assertTrue(termin.samodzielny_termin())

    def test_ilosc_uczestnikow_z_formalnosciami(self):
        termin = TerminSzkoleniaFactory.create()

        UczestnikFactory.create_batch(2, termin=termin, status=-1)
        UczestnikFactory.create_batch(2, termin=termin, status=1)
        UczestnikFactory.create_batch(3, termin=termin, status=0)
        UczestnikFactory.create_batch(3, termin=termin, status=2)

        self.assertEqual(termin.ilosc_uczestnikow_z_formalnosciami(), 4)


@override_settings(GENERATE_INVOICE_THROUGH_API_ZLICZACZ=True)
class UczestnikTestCase(ALXTestCase):
    def test_wyslac_mail_o_potwierdzeniu_terminu(self):
        uczestnik = UczestnikFactory.create(status=1)
        self.assertTrue(uczestnik.wyslac_mail_o_potwierdzeniu_terminu())

        uczestnik = UczestnikFactory.create(
            mail_o_potwierdzeniu_terminu=datetime.datetime.now(), status=1
        )
        self.assertFalse(uczestnik.wyslac_mail_o_potwierdzeniu_terminu())

        uczestnik = UczestnikFactory.create(
            termin__termin=datetime.date.today() - datetime.timedelta(days=2), status=1
        )
        self.assertFalse(uczestnik.wyslac_mail_o_potwierdzeniu_terminu())

        uczestnik = UczestnikFactory.create(status=2)
        self.assertFalse(uczestnik.wyslac_mail_o_potwierdzeniu_terminu())

    def test_aktywny_newsletter_gdy_brak(self):
        uczestnik = UczestnikFactory.create()
        self.assertFalse(uczestnik.aktywny_newsletter)

    def test_aktywny_newsletter_gdy_brak_rodo(self):
        odbiorca = OdbiorcaFactory.create(email="<EMAIL>")
        odbiorca.tos_agreement = None
        odbiorca.save()

        uczestnik = UczestnikFactory.create(email="<EMAIL>")
        self.assertFalse(uczestnik.aktywny_newsletter)

    def test_aktywny_newsletter_gdy_brak_adresu_email(self):
        uczestnik = UczestnikFactory.create(email="")
        self.assertFalse(uczestnik.aktywny_newsletter)

    def test_aktywny_newsletter_gdy_brak_potwierdzenia(self):
        OdbiorcaFactory.create(email="<EMAIL>", status="opt-out")

        uczestnik = UczestnikFactory.create(email="<EMAIL>")
        self.assertFalse(uczestnik.aktywny_newsletter)

    def test_aktywny_newsletter(self):
        OdbiorcaFactory.create(email="<EMAIL>")

        uczestnik = UczestnikFactory.create(email="<EMAIL>")
        self.assertTrue(uczestnik.aktywny_newsletter)

    def test_can_send_first_continuation_email_gdy_brak_maila(self):
        uczestnik = UczestnikFactory.create(
            email="", termin__szkolenie__kontynuacja=SzkolenieFactory.create()
        )

        self.assertFalse(uczestnik.can_send_first_continuation_email())

    def test_can_send_first_continuation_email_gdy_optout(self):
        OptOutFactory.create(email="<EMAIL>")
        uczestnik = UczestnikFactory.create(
            email="<EMAIL>",
            termin__szkolenie__kontynuacja=SzkolenieFactory.create(),
        )

        self.assertFalse(uczestnik.can_send_first_continuation_email())

    def test_can_send_first_continuation_email_gdy_brak_newslettera(self):
        uczestnik = UczestnikFactory.create(
            email="<EMAIL>",
            termin__szkolenie__kontynuacja=SzkolenieFactory.create(),
        )

        self.assertTrue(uczestnik.can_send_first_continuation_email())

    def test_can_send_first_continuation_email_gdy_mail_juz_wyslany(self):
        kontynuacja = SzkolenieFactory.create()

        uczestnik = UczestnikFactory.create(
            email="<EMAIL>", termin__szkolenie__kontynuacja=kontynuacja
        )
        ContinuationLogFactory.create(email="<EMAIL>", training=kontynuacja)

        self.assertFalse(uczestnik.can_send_first_continuation_email())

    def test_can_send_first_continuation_email_gdy_juz_uczestniczyl_w_kontynuacji(self):
        kontynuacja = SzkolenieFactory.create()

        uczestnik = UczestnikFactory.create(
            email="<EMAIL>", termin__szkolenie__kontynuacja=kontynuacja
        )
        UczestnikFactory.create(email="<EMAIL>", termin__szkolenie=kontynuacja)

        self.assertFalse(uczestnik.can_send_first_continuation_email())

    def test_can_send_first_continuation_email_gdy_wypisany(self):
        kontynuacja = SzkolenieFactory.create()

        uczestnik = UczestnikFactory.create(
            email="<EMAIL>", termin__szkolenie__kontynuacja=kontynuacja
        )
        ContinuationUnsubscribedFactory.create(email="<EMAIL>")

        self.assertFalse(uczestnik.can_send_first_continuation_email())

    def test_can_send_first_continuation_email(self):
        # ------ not related
        UczestnikFactory.create(
            email="<EMAIL>", termin__szkolenie=SzkolenieFactory.create()
        )
        ContinuationLogFactory.create_batch(2, email="<EMAIL>")
        # ---------------------

        uczestnik = UczestnikFactory.create(
            email="<EMAIL>",
            termin__szkolenie__kontynuacja=SzkolenieFactory.create(),
        )
        OdbiorcaFactory.create(email="<EMAIL>")

        self.assertTrue(uczestnik.can_send_first_continuation_email())

    def test_can_send_second_continuation_email_gdy_brak_maila(self):
        uczestnik = UczestnikFactory.create(
            email="", termin__szkolenie__kontynuacja=SzkolenieFactory.create()
        )

        self.assertFalse(uczestnik.can_send_second_continuation_email())

    def test_can_send_second_continuation_email_gdy_optout(self):
        OptOutFactory.create(email="<EMAIL>")
        uczestnik = UczestnikFactory.create(
            email="<EMAIL>",
            termin__szkolenie__kontynuacja=SzkolenieFactory.create(),
        )

        self.assertFalse(uczestnik.can_send_second_continuation_email())

    def test_can_send_second_continuation_email_gdy_brak_newslettera(self):
        uczestnik = UczestnikFactory.create(
            email="<EMAIL>",
            termin__szkolenie__kontynuacja=SzkolenieFactory.create(),
        )

        self.assertTrue(uczestnik.can_send_second_continuation_email())

    def test_can_send_second_continuation_email_gdy_mail_juz_wyslany(self):
        kontynuacja = SzkolenieFactory.create()

        uczestnik = UczestnikFactory.create(
            email="<EMAIL>", termin__szkolenie__kontynuacja=kontynuacja
        )
        ContinuationLogFactory.create(
            email="<EMAIL>",
            training=kontynuacja,
            notification_type="second_notification",
        )

        self.assertFalse(uczestnik.can_send_second_continuation_email())

    def test_can_send_second_continuation_email_gdy_wypisany(self):
        kontynuacja = SzkolenieFactory.create()

        uczestnik = UczestnikFactory.create(
            email="<EMAIL>", termin__szkolenie__kontynuacja=kontynuacja
        )
        ContinuationUnsubscribedFactory.create(email="<EMAIL>")

        self.assertFalse(uczestnik.can_send_second_continuation_email())

    def test_can_send_second_continuation_email_gdy_juz_uczestniczyl_w_kontynuacji(
        self,
    ):
        kontynuacja = SzkolenieFactory.create()

        uczestnik = UczestnikFactory.create(
            email="<EMAIL>", termin__szkolenie__kontynuacja=kontynuacja
        )
        UczestnikFactory.create(email="<EMAIL>", termin__szkolenie=kontynuacja)

        self.assertFalse(uczestnik.can_send_second_continuation_email())

    def test_can_send_second_continuation_email(self):
        # ------------- not related
        UczestnikFactory.create(
            email="<EMAIL>", termin__szkolenie=SzkolenieFactory.create()
        )
        ContinuationLogFactory.create_batch(2, email="<EMAIL>")
        # -------------

        uczestnik = UczestnikFactory.create(
            email="<EMAIL>",
            termin__szkolenie__kontynuacja=SzkolenieFactory.create(),
        )

        self.assertTrue(uczestnik.can_send_second_continuation_email())

    def test_get_nazwa_gdy_grupa(self):
        uczestnik = UczestnikFactory(
            uczestnik_wieloosobowy_ilosc_osob=2, imie_nazwisko="123"
        )

        uczestnik.faktura_firma = "Abc"
        uczestnik.save()

        self.assertEqual(uczestnik.get_nazwa(), "Abc")

    def test_get_nazwa_gdy_nie_grupa(self):
        uczestnik = UczestnikFactory(imie_nazwisko="123")

        uczestnik.faktura_firma = "Abc"
        uczestnik.save()

        self.assertEqual(uczestnik.get_nazwa(), "123")

    def test_get_email_ksiegowosc(self):
        uczestnik = UczestnikFactory(
            email="<EMAIL>",
            email_ksiegowosc="<EMAIL>",
        )

        self.assertEqual(uczestnik.get_email_ksiegowosc(), "<EMAIL>")

        uczestnik.email_ksiegowosc = ""
        uczestnik.save()

        self.assertEqual(uczestnik.get_email_ksiegowosc(), "<EMAIL>")

    def test_imie_nazwisko_1(self):
        uczestnik = UczestnikFactory(
            imie_nazwisko="Jan Kowalski\r\nJan Nowak \r\n Anna Maria Wesołowska "
        )
        self.assertEqual(
            uczestnik.imie_nazwisko_oneline(),
            "Jan Kowalski, Jan Nowak, Anna Maria Wesołowska",
        )

    def test_imie_nazwisko_2(self):
        uczestnik = UczestnikFactory(
            imie_nazwisko="Jan Kowalski, Jan Nowak, Anna Maria Wesołowska"
        )
        self.assertEqual(
            uczestnik.imie_nazwisko_oneline(),
            "Jan Kowalski, Jan Nowak, Anna Maria Wesołowska",
        )

    def test_imie_nazwisko_3(self):
        uczestnik = UczestnikFactory(imie_nazwisko="Jan Kowalski")
        self.assertEqual(uczestnik.imie_nazwisko_oneline(), "Jan Kowalski")

    def test_imie_nazwisko_4(self):
        uczestnik = UczestnikFactory(imie_nazwisko="")
        self.assertEqual(uczestnik.imie_nazwisko_oneline(), "")

    def test_termin_platnosci_do_faktury_14_dni(self):
        uczestnik = UczestnikFactory(termin__termin=parse("2014-02-14").date())
        data = uczestnik.termin_platnosci_do_faktury(parse("2014-02-01").date())
        self.assertEqual(data, parse("2014-02-08").date())

    def test_termin_platnosci_do_faktury_7_dni(self):
        uczestnik = UczestnikFactory(termin__termin=parse("2014-02-14").date())
        data = uczestnik.termin_platnosci_do_faktury(parse("2014-02-07").date())
        self.assertEqual(data, parse("2014-02-11").date())

    def test_termin_platnosci_do_faktury_5_dni(self):
        uczestnik = UczestnikFactory(termin__termin=parse("2014-02-14").date())
        data = uczestnik.termin_platnosci_do_faktury(parse("2014-02-09").date())
        self.assertEqual(data, parse("2014-02-11").date())

    def test_termin_platnosci_do_faktury_4_dni(self):
        uczestnik = UczestnikFactory(termin__termin=parse("2014-02-13").date())
        data = uczestnik.termin_platnosci_do_faktury(parse("2014-02-09").date())
        self.assertEqual(data, parse("2014-02-10").date())

    def test_termin_platnosci_do_faktury_3_dni(self):
        uczestnik = UczestnikFactory(termin__termin=parse("2014-02-13").date())
        data = uczestnik.termin_platnosci_do_faktury(parse("2014-02-10").date())
        self.assertEqual(data, parse("2014-02-10").date())

    def test_termin_platnosci_do_faktury_1_dzien(self):
        uczestnik = UczestnikFactory(termin__termin=parse("2014-02-14").date())
        data = uczestnik.termin_platnosci_do_faktury(parse("2014-02-13").date())
        self.assertEqual(data, parse("2014-02-13").date())

    def test_tytul_do_faktury_jedna_osoba_szkolenie(self):
        uczestnik = UczestnikFactory(termin__daty_szczegolowo="2014-02-14,2014-02-15")
        self.assertEqual(
            uczestnik.tytul_do_faktury(),
            "Szkolenie: {} w terminie od 14.02.2014. Uczestnik: {}.".format(
                uczestnik.termin.szkolenie.nazwa, uczestnik.imie_nazwisko
            ),
        )

    def test_tytul_do_faktury_jedna_osoba_kurs(self):
        uczestnik = UczestnikFactory(
            termin__szkolenie=KursFactory(),
            termin__termin=parse("2014-05-14").date(),
            termin__daty_szczegolowo="2014-05-14",
        )
        self.assertEqual(
            uczestnik.tytul_do_faktury(),
            "Kurs: {} w terminie od 14.05.2014. Uczestnik: {}.".format(
                uczestnik.termin.szkolenie.nazwa, uczestnik.imie_nazwisko
            ),
        )

    def test_tytul_do_faktury_wiele_osob_szkolenie(self):
        uczestnik = UczestnikFactory(
            uczestnik_wieloosobowy_ilosc_osob=5,
            termin__termin=parse("2014-05-14").date(),
        )
        self.assertEqual(
            uczestnik.tytul_do_faktury(),
            "Szkolenie: {} w terminie od 14.05.2014. Uczestnicy: {}.".format(
                uczestnik.termin.szkolenie.nazwa, uczestnik.imie_nazwisko_oneline()
            ),
        )

    def test_tytul_do_faktury_wiele_osob_kurs(self):
        uczestnik = UczestnikFactory(
            termin__szkolenie=KursFactory(),
            termin__termin=parse("2014-05-14").date(),
            uczestnik_wieloosobowy_ilosc_osob=5,
            termin__daty_szczegolowo="2014-05-14",
        )
        self.assertEqual(
            uczestnik.tytul_do_faktury(),
            "Kurs: {} w terminie od 14.05.2014. Uczestnicy: {}.".format(
                uczestnik.termin.szkolenie.nazwa, uczestnik.imie_nazwisko_oneline()
            ),
        )

    def test_tytul_do_faktury_raty_jedna_osoba_szkolenie(self):
        uczestnik = UczestnikFactory(
            termin__daty_szczegolowo="2014-02-14,2014-02-15,2014-02-16"
        )
        self.assertEqual(
            uczestnik.tytul_do_faktury_raty(),
            "Szkolenie: {} w terminie od 14.02.2014. Uczestnik: {}.".format(
                uczestnik.termin.szkolenie.nazwa, uczestnik.imie_nazwisko
            ),
        )

    def test_tytul_do_faktury_raty_jedna_osoba_kurs(self):
        uczestnik = UczestnikFactory(
            termin__szkolenie=KursFactory(),
            termin__termin=parse("2014-05-14").date(),
            termin__daty_szczegolowo="2014-05-14",
        )
        self.assertEqual(
            uczestnik.tytul_do_faktury_raty(),
            "Kurs: {} w terminie od 14.05.2014. Uczestnik: {}.".format(
                uczestnik.termin.szkolenie.nazwa, uczestnik.imie_nazwisko
            ),
        )

    def test_tytul_do_faktury_raty_wiele_osob_szkolenie(self):
        uczestnik = UczestnikFactory(
            uczestnik_wieloosobowy_ilosc_osob=5,
            termin__termin=parse("2014-05-14").date(),
        )
        self.assertEqual(
            uczestnik.tytul_do_faktury_raty(),
            "Szkolenie: {} w terminie od 14.05.2014. Uczestnicy: {}.".format(
                uczestnik.termin.szkolenie.nazwa, uczestnik.imie_nazwisko_oneline()
            ),
        )

    def test_tytul_do_faktury_raty_wiele_osob_kurs(self):
        uczestnik = UczestnikFactory(
            termin__szkolenie=KursFactory(),
            termin__termin=parse("2014-05-14").date(),
            uczestnik_wieloosobowy_ilosc_osob=5,
            termin__daty_szczegolowo="2014-05-14,2014-05-15",
        )
        self.assertEqual(
            uczestnik.tytul_do_faktury_raty(),
            "Kurs: {} w terminie od 14.05.2014. Uczestnicy: {}.".format(
                uczestnik.termin.szkolenie.nazwa, uczestnik.imie_nazwisko_oneline()
            ),
        )

    def test_tytul_do_faktury_zaliczkowej_pierwsza_rata(self):
        uczestnik = UczestnikFactory(
            termin__szkolenie=KursFactory(),
            termin__termin=parse("2014-05-14").date(),
            termin__daty_szczegolowo="2014-05-14",
            zaliczka_zaplacone=parse("2014-05-14").date(),
            termin__odbylo_sie=True,
            za_kurs_zaplace=10,
        )
        self.assertEqual(
            uczestnik.tytul_do_faktury_zaliczkowej(),
            "Kurs: {} w terminie od 14.05.2014. Uczestnik: {}. "
            "- I rata".format(
                uczestnik.termin.szkolenie.nazwa, uczestnik.imie_nazwisko
            ),
        )

    def test_tytul_do_faktury_zaliczkowej_oplata_wstepna(self):
        uczestnik = UczestnikFactory(
            termin__szkolenie=KursFactory(),
            termin__termin=parse("2014-05-14").date(),
            termin__daty_szczegolowo="2014-05-14",
            za_kurs_zaplace=3,
        )

        self.assertEqual(
            uczestnik.tytul_do_faktury_zaliczkowej(),
            "Kurs: {} w terminie od 14.05.2014. Uczestnik: {}. - Opłata wstępna".format(
                uczestnik.termin.szkolenie.nazwa, uczestnik.imie_nazwisko
            ),
        )

    def test_tytul_do_faktury_szkolenie_dla_modziezy(self):
        uczestnik = UczestnikFactory(
            termin__szkolenie=KursFactory(kod="K-PROG-INTRO-M"),
            termin__termin=parse("2014-05-14").date(),
            termin__daty_szczegolowo="2014-05-14",
            za_kurs_zaplace=3,
        )

        self.assertEqual(
            uczestnik.tytul_do_faktury_zaliczkowej(),
            "Kurs: Nauka programowania i Język Java (dla młodzieży) w "
            "terminie od 14.05.2014. Uczestnik: {}. - Opłata wstępna".format(
                uczestnik.imie_nazwisko
            ),
        )

    # def test_zaplacono_pierwsza_rate(self):
    #     uczestnik = UczestnikFactory(za_kurs_zaplace=3)
    #
    #     self.assertTrue(uczestnik.zaplacono_pierwsza_rate())
    #
    #     uczestnik.za_kurs_zaplace = -1
    #     uczestnik.save()
    #
    #     self.assertFalse(uczestnik.zaplacono_pierwsza_rate())

    def test_faktura_zaliczkowa(self):
        uczestnik = UczestnikFactory(za_kurs_zaplace=3)

        self.assertTrue(uczestnik.faktura_zaliczkowa)

        uczestnik.za_kurs_zaplace = 1
        uczestnik.save()

        self.assertTrue(uczestnik.faktura_zaliczkowa)

        uczestnik.termin.odbylo_sie = True
        uczestnik.termin.save()

        self.assertFalse(uczestnik.faktura_zaliczkowa)

    def test_faktura_zaliczkowa_gdy_zaliczka_zaplacone(self):
        uczestnik = UczestnikFactory(
            za_kurs_zaplace=3, zaliczka_zaplacone=datetime.date.today()
        )

        uczestnik.termin.odbylo_sie = True
        uczestnik.termin.save()

        self.assertTrue(uczestnik.faktura_zaliczkowa)

        uczestnik.rata1_zaplacone = datetime.date.today()
        uczestnik.save()

        self.assertFalse(uczestnik.faktura_zaliczkowa)

    def test_zaliczka_kwota(self):
        uczestnik = UczestnikFactory(
            za_kurs_zaplace=3, zaliczka_kwota=Decimal("900.00")
        )

        self.assertEqual(str(uczestnik.cena_zaliczka()), "900.00")
        self.assertEqual(
            uczestnik.cena_brutto_zaliczka() - uczestnik.cena_zaliczka(),
            uczestnik.kwota_vat_zaliczka(),
        )

    def test_cena_rata(self):
        uczestnik = UczestnikFactory(
            za_kurs_zaplace=3,
        )

        kwota = Decimal(123)

        self.assertEqual(str(uczestnik.cena_rata(kwota)), "123.00")
        self.assertEqual(
            uczestnik.cena_brutto_rata(kwota) - uczestnik.cena_rata(kwota),
            uczestnik.kwota_vat_rata(kwota),
        )

    def test_termin_platnosci_do_faktury_zaliczkowej(self):
        uczestnik = UczestnikFactory(termin__termin=parse("2014-05-14").date())

        self.assertEqual(
            uczestnik.termin_platnosci_do_faktury_zaliczkowej(),
            parse("2014-05-15").date(),
        )

    def test_generowanie_danych_do_zaliczek(self):
        for za_kurs_zaplace in [1, 5, 6, 7]:
            e = UczestnikFactory(za_kurs_zaplace=za_kurs_zaplace)
            self.assertTrue(e.zaliczka_kwota)
            self.assertEqual(e.zaliczka_kwota, e.kwota_do_zaplaty)
            self.assertTrue(e.zaliczka_termin)
            self.assertEqual(e.zaliczka_termin, e.termin_zaplaty)
            self.assertFalse(e.zaliczka_zaplacone)

        e = UczestnikFactory(za_kurs_zaplace=1, zaplacone=datetime.date.today())
        self.assertTrue(e.zaliczka_zaplacone, e.zaplacone)

    def test_dane_do_faktury_firma_nazwa(self):
        uczestnik = UczestnikFactory()
        uczestnik.faktura_firma = "Firma#1"
        uczestnik.faktura_adres = "Adres"
        uczestnik.faktura_miejscowosc_kod = "33-389 Miasto"
        uczestnik.faktura_nip = "111"
        uczestnik.faktura_vat_id = "111"
        uczestnik.clean()
        dane = uczestnik.dane_do_faktury()
        self.assertEqual(dane["klient"], uczestnik.faktura_firma)
        self.assertEqual(dane["klient_nazwa"], uczestnik.faktura_firma)

    def test_dane_do_faktury_prywatny_nazwa(self):
        uczestnik = UczestnikFactory(prywatny=True)
        dane = uczestnik.dane_do_faktury()
        self.assertEqual(dane["klient"], uczestnik.imie_nazwisko)
        self.assertEqual(dane["klient_nazwa"], uczestnik.imie_nazwisko)

    def test_dane_do_faktury_prywatny_nazwa_gdy_osoba_kontaktowa(self):
        uczestnik = UczestnikFactory(prywatny=True, osoba_do_kontaktu="ABC")
        dane = uczestnik.dane_do_faktury()
        self.assertEqual(dane["klient"], "ABC")
        self.assertEqual(dane["klient_nazwa"], "ABC")

    def test_dane_do_faktury_prywatny_email(self):
        uczestnik = UczestnikFactory(prywatny=True, email="<EMAIL>")
        dane = uczestnik.dane_do_faktury()
        self.assertEqual(dane["klient_email"], "<EMAIL>")

    def test_dane_do_faktury_prywatny_email_gdy_osoba_do_rozliczen(self):
        uczestnik = UczestnikFactory(prywatny=True, email_ksiegowosc="<EMAIL>")
        dane = uczestnik.dane_do_faktury()
        self.assertEqual(dane["klient_email"], "<EMAIL>")

    def test_dane_do_faktury_firma_email(self):
        uczestnik = UczestnikFactory(prywatny=False, email="<EMAIL>")
        dane = uczestnik.dane_do_faktury()
        self.assertEqual(dane["klient_email"], "<EMAIL>")

    def test_dane_do_faktury_firma_email_gdy_osoba_do_rozliczen(self):
        uczestnik = UczestnikFactory(prywatny=False, email_ksiegowosc="<EMAIL>")
        dane = uczestnik.dane_do_faktury()
        self.assertEqual(dane["klient_email"], "<EMAIL>")

    def test_object_has_required_data_for_invoice(self):
        """
        Sprawdza, czy uczestnik zawiera poprawne dane do faktury pro-forma.
        """

        # Uczestnik prywatny
        u = UczestnikFactory(prywatny=True)
        self.assertFalse(u.object_has_required_data_for_invoice())

        u.imie_nazwisko = "Imię i nazwisko"
        u.adres = "adres"
        u.miejscowosc_kod = "00-987"
        u.email = "<EMAIL>"
        u.imie_nazwisko_zostalo_sprawdzone = True
        u.save()
        self.assertTrue(u.object_has_required_data_for_invoice())

        # Uczestnik-firma
        u = UczestnikFactory(prywatny=False)
        self.assertFalse(u.object_has_required_data_for_invoice())

        u.faktura_firma = "Firma"
        u.faktura_adres = "adres"
        u.faktura_miejscowosc_kod = "00-987"
        u.faktura_nip = "111-111-111-111"
        u.email = "<EMAIL>"
        u.imie_nazwisko_zostalo_sprawdzone = True
        u.save()
        self.assertTrue(u.object_has_required_data_for_invoice())

    def test_can_send_proforma_email(self):
        uczestnik = UczestnikFactory()
        self.assertFalse(uczestnik.can_send_proforma_email())

        uczestnik = UczestnikFactory(zliczacz_proforma_no="123")
        self.assertTrue(uczestnik.can_send_proforma_email())

        uczestnik = UczestnikFactory(zliczacz_proforma_no="123", email="")
        self.assertFalse(uczestnik.can_send_proforma_email())

        uczestnik = UczestnikFactory(zliczacz_proforma_no="123", nr_faktury="123")
        self.assertFalse(uczestnik.can_send_proforma_email())

        uczestnik = UczestnikFactory(
            zliczacz_proforma_no="123", zliczacz_faktura_no="123"
        )
        self.assertFalse(uczestnik.can_send_proforma_email())

        uczestnik = UczestnikFactory(
            zliczacz_proforma_no="123", proforma_wyslana=datetime.datetime.now()
        )
        self.assertFalse(uczestnik.can_send_proforma_email())

    def test_can_send_fvat_email(self):
        uczestnik = UczestnikFactory()
        self.assertFalse(uczestnik.can_send_fvat_email())

        uczestnik = UczestnikFactory(zliczacz_faktura_no="123")
        self.assertTrue(uczestnik.can_send_fvat_email())

        uczestnik = UczestnikFactory(zliczacz_faktura_no="123", email="")
        self.assertFalse(uczestnik.can_send_fvat_email())

        uczestnik = UczestnikFactory(
            zliczacz_faktura_no="123", faktura_wyslana=datetime.datetime.now()
        )
        self.assertFalse(uczestnik.can_send_fvat_email())

        uczestnik = UczestnikFactory(
            zliczacz_faktura_no="123", korekta_wyslana=datetime.datetime.now()
        )
        self.assertFalse(uczestnik.can_send_fvat_email())

    def test_can_send_invoice_note_email(self):
        uczestnik = UczestnikFactory()
        self.assertFalse(uczestnik.can_send_invoice_note_email())

        uczestnik = UczestnikFactory()
        FakturaKorektaFactory.create(uczestnik=uczestnik)
        self.assertTrue(uczestnik.can_send_invoice_note_email())

        uczestnik = UczestnikFactory(email="")
        FakturaKorektaFactory.create(uczestnik=uczestnik)
        self.assertFalse(uczestnik.can_send_invoice_note_email())

        uczestnik = UczestnikFactory(korekta_wyslana=datetime.datetime.now())
        FakturaKorektaFactory.create(uczestnik=uczestnik)
        self.assertFalse(uczestnik.can_send_invoice_note_email())

    def test_can_send_invoice_installments_email(self):
        uczestnik = UczestnikFactory()
        self.assertFalse(uczestnik.can_send_invoice_installments_email())

        uczestnik = UczestnikFactory(zliczacz_faktura_raty_no="123")
        self.assertTrue(uczestnik.can_send_invoice_installments_email())

        uczestnik = UczestnikFactory(zliczacz_faktura_raty_no="123", email="")
        self.assertFalse(uczestnik.can_send_invoice_installments_email())

        uczestnik = UczestnikFactory(
            zliczacz_faktura_raty_no="123", faktura_raty_wyslana=datetime.datetime.now()
        )
        self.assertFalse(uczestnik.can_send_invoice_installments_email())

    def test_allowed_to_get_invoice_by_admin_fvat(self):
        uczestnik = UczestnikFactory()
        self.assertFalse(uczestnik.allowed_to_get_invoice_by_admin(proforma=False))

        uczestnik.prywatny = True
        uczestnik.imie_nazwisko_zostalo_sprawdzone = True
        uczestnik.adres = "Adres"
        uczestnik.miejscowosc_kod = "02-987 Warszawa"
        uczestnik.wystaw_proforme_automatycznie = True
        uczestnik.status = 3
        uczestnik.save()

        # zaliczkowa
        self.assertTrue(uczestnik.allowed_to_get_invoice_by_admin(proforma=False))

        uczestnik.status = -1
        uczestnik.save()

        self.assertTrue(uczestnik.allowed_to_get_invoice_by_admin(proforma=False))

        uczestnik.zaliczka_kwota = None
        uczestnik.zaliczka_termin = None
        uczestnik.save()

        self.assertFalse(uczestnik.allowed_to_get_invoice_by_admin(proforma=False))

        # normalna
        uczestnik.termin.odbylo_sie = True
        uczestnik.status = 3
        uczestnik.nr_proformy = "123"
        uczestnik.save()
        self.assertTrue(uczestnik.allowed_to_get_invoice_by_admin(proforma=False))

        uczestnik.nr_faktury = "123"
        uczestnik.save()
        self.assertFalse(uczestnik.allowed_to_get_invoice_by_admin(proforma=False))

    def test_allowed_to_get_invoice_installments_by_admin(self):
        uczestnik = UczestnikFactory(za_kurs_zaplace=3)
        self.assertFalse(uczestnik.allowed_to_get_invoice_installments_by_admin())

        uczestnik.prywatny = True
        uczestnik.imie_nazwisko_zostalo_sprawdzone = True
        uczestnik.adres = "Adres"
        uczestnik.miejscowosc_kod = "02-987 Warszawa"
        uczestnik.wystaw_proforme_automatycznie = True
        uczestnik.status = 3
        uczestnik.termin.odbylo_sie = True
        uczestnik.save()

        self.assertTrue(uczestnik.allowed_to_get_invoice_installments_by_admin())

        uczestnik.za_kurs_zaplace = 2
        uczestnik.save()
        self.assertTrue(uczestnik.allowed_to_get_invoice_installments_by_admin())

        uczestnik.za_kurs_zaplace = 1
        uczestnik.save()
        self.assertFalse(uczestnik.allowed_to_get_invoice_installments_by_admin())

        uczestnik.za_kurs_zaplace = 3
        uczestnik.nr_faktury_raty = "123"
        uczestnik.save()
        self.assertFalse(uczestnik.allowed_to_get_invoice_installments_by_admin())

    def test_allowed_to_get_invoice_by_admin_proforma(self):
        uczestnik = UczestnikFactory()
        self.assertFalse(uczestnik.allowed_to_get_invoice_by_admin(proforma=True))

        uczestnik.prywatny = True
        uczestnik.imie_nazwisko_zostalo_sprawdzone = True
        uczestnik.adres = "Adres"
        uczestnik.miejscowosc_kod = "02-987 Warszawa"
        uczestnik.wystaw_proforme_automatycznie = True
        uczestnik.status = 3
        uczestnik.save()
        self.assertTrue(uczestnik.allowed_to_get_invoice_by_admin(proforma=True))

        uczestnik.status = -1
        uczestnik.save()

        self.assertTrue(uczestnik.allowed_to_get_invoice_by_admin(proforma=True))

        uczestnik.status = 4
        uczestnik.save()

        self.assertFalse(uczestnik.allowed_to_get_invoice_by_admin(proforma=True))

        uczestnik.status = 3
        uczestnik.nr_proformy = "123"
        uczestnik.save()
        self.assertFalse(uczestnik.allowed_to_get_invoice_by_admin(proforma=True))

        uczestnik.nr_proformy = ""
        uczestnik.nr_faktury = "123"
        uczestnik.save()
        self.assertFalse(uczestnik.allowed_to_get_invoice_by_admin(proforma=True))

    def test_allowed_to_get_invoice_by_admin_zaliczkowa(self):
        uczestnik = UczestnikFactory(za_kurs_zaplace=10)
        self.assertFalse(uczestnik.allowed_to_get_invoice_by_admin(proforma=True))
        self.assertFalse(uczestnik.allowed_to_get_invoice_by_admin(proforma=False))

        uczestnik.prywatny = True
        uczestnik.imie_nazwisko_zostalo_sprawdzone = True
        uczestnik.adres = "Adres"
        uczestnik.miejscowosc_kod = "02-987 Warszawa"
        uczestnik.wystaw_proforme_automatycznie = True
        uczestnik.status = 3
        uczestnik.save()
        self.assertTrue(uczestnik.allowed_to_get_invoice_by_admin(proforma=True))
        self.assertTrue(uczestnik.allowed_to_get_invoice_by_admin(proforma=False))

        uczestnik.status = -1
        uczestnik.save()

        self.assertTrue(uczestnik.allowed_to_get_invoice_by_admin(proforma=True))
        self.assertTrue(uczestnik.allowed_to_get_invoice_by_admin(proforma=False))

        uczestnik.status = 4
        uczestnik.save()

        self.assertFalse(uczestnik.allowed_to_get_invoice_by_admin(proforma=True))
        self.assertFalse(uczestnik.allowed_to_get_invoice_by_admin(proforma=False))

    def test_get_invoice_note(self):
        uczestnik = UczestnikFactory()
        self.assertIsNone(uczestnik.get_invoice_note())

        FakturaKorektaFactory.create()

        self.assertIsNone(uczestnik.get_invoice_note())

        FakturaKorektaFactory.create(uczestnik=UczestnikFactory.create())
        self.assertIsNone(uczestnik.get_invoice_note())

        obj = FakturaKorektaFactory.create(uczestnik=uczestnik)
        self.assertTrue(uczestnik.get_invoice_note())
        self.assertEqual(uczestnik.get_invoice_note(), obj)

    def test_allowed_to_get_invoice_note_by_admin(self):
        uczestnik = UczestnikFactory()
        self.assertFalse(uczestnik.allowed_to_get_invoice_note_by_admin())

        uczestnik.zliczacz_faktura_no = 1
        uczestnik.save()
        self.assertFalse(uczestnik.allowed_to_get_invoice_note_by_admin())

        uczestnik.termin.odbylo_sie = False
        uczestnik.termin.save()
        self.assertTrue(uczestnik.allowed_to_get_invoice_note_by_admin())

        FakturaKorektaFactory.create(uczestnik=uczestnik)
        self.assertFalse(uczestnik.allowed_to_get_invoice_note_by_admin())

    def test_can_send_agreement_email(self):
        uczestnik = UczestnikFactory(raty_panstwo="polska", prywatny=True)
        self.assertFalse(uczestnik.can_send_agreement_email())

        uczestnik.za_kurs_zaplace = 10
        uczestnik.save()
        self.assertTrue(uczestnik.can_send_agreement_email())

        uczestnik.umowa_wyslana = datetime.datetime.now()
        uczestnik.save()
        self.assertFalse(uczestnik.can_send_agreement_email())

        uczestnik.umowa_wyslana = None
        uczestnik.email = ""
        uczestnik.save()
        self.assertFalse(uczestnik.can_send_agreement_email())


class UczestnikFakturaTestCase(ALXTestCase):
    def get_termin(self):
        termin = TerminSzkoleniaFactory()
        termin.clean()
        return termin

    def test_data_faktury_moze_byc_pusta(self):
        uczestnik = UczestnikFactory()
        self.assertFalse(uczestnik.faktura_po_szkoleniu_data)
        self.assertTrue(uczestnik.pk)

    def get_ostatni_dzien_szkolenia_kursu(self, termin):
        return termin.daty_szczegolowo.split(",")[-1]

    def test_przy_wyborze_platnosci_14_dni_po_ustaw_fakture_na_ostatni_dzien(self):
        uczestnik = UczestnikFactory(za_kurs_zaplace=6, termin=self.get_termin())
        self.assertTrue(uczestnik.faktura_po_szkoleniu)
        self.assertEqual(
            uczestnik.faktura_po_szkoleniu_data.__str__(),
            self.get_ostatni_dzien_szkolenia_kursu(uczestnik.termin),
        )
        self.assertEqual(uczestnik.faktura_po_szkoleniu_data.__class__, datetime.date)

    def test_przy_5_rat_ustaw_fakture_na_date_1_raty(self):
        uczestnik = UczestnikFactory(za_kurs_zaplace=10, termin=self.get_termin())
        self.assertTrue(uczestnik.faktura_po_szkoleniu)
        self.assertEqual(uczestnik.faktura_po_szkoleniu_data, uczestnik.rata1_termin)
        self.assertEqual(uczestnik.faktura_po_szkoleniu_data.__class__, datetime.date)

    def test_przy_5_rat_nic_nie_rob_z_faktura_jesli_ustawiona(self):
        faktura_data = datetime.date.today()
        uczestnik = UczestnikFactory(
            za_kurs_zaplace=10,
            termin=self.get_termin(),
            faktura_po_szkoleniu_data=faktura_data,
        )
        self.assertTrue(uczestnik.faktura_po_szkoleniu)
        self.assertEqual(uczestnik.faktura_po_szkoleniu_data, faktura_data)

    def test_przy_od_razu_place_nic_nie_rob_z_faktura(self):
        uczestnik = UczestnikFactory(za_kurs_zaplace=1, termin=self.get_termin())
        self.assertFalse(uczestnik.faktura_po_szkoleniu)
        self.assertFalse(uczestnik.faktura_po_szkoleniu_data)

    def test_blad_gdy_brak_danych_do_faktury(self):
        uczestnik = UczestnikFactory()
        try:
            uczestnik.clean
        except ValidationError:
            self.fail("uczestnik.clean raised ValidationError unexpectedly!")

    def test_brak_bledu_gdy_sa_danye_do_faktury_lub_osoba_prywatna(self):
        uczestnik = UczestnikFactory(prywatny=True)

        uczestnik.clean()

        uczestnik.prywatny = False

        uczestnik.faktura_firma = "1"
        uczestnik.faktura_adres = "1"
        uczestnik.faktura_miejscowosc_kod = "1"
        uczestnik.faktura_nip = "1"
        uczestnik.faktura_vat_id = "1"
        uczestnik.imie_nazwisko_zostalo_sprawdzone = True

        uczestnik.clean()


@unittest.skip("Walidacja rat jeszcze nie jest włączona.")
class UczestnikRatyTestCase(ALXTestCase):
    pola_z_ratami = [
        "zaliczka",
        "rata1",
        "rata2",
        "rata3",
        "rata4",
    ]
    standardowa_kwota = math.factorial(len(pola_z_ratami)) * 100
    standardowy_termin_zaplaty = datetime.date.today() + datetime.timedelta(days=50)

    def test_brak_rat_jest_spojny(self):
        u = UczestnikFactory(
            kwota_do_zaplaty=self.standardowa_kwota,
            termin_zaplaty=self.standardowy_termin_zaplaty,
        )
        u.clean()

    def test_raty_ciagle_sa_spojne(self):
        for i in range(1, len(self.pola_z_ratami) + 1):
            u = UczestnikFactory(
                kwota_do_zaplaty=self.standardowa_kwota,
                termin_zaplaty=self.standardowy_termin_zaplaty,
            )
            rata = self.standardowa_kwota / i
            for j in range(i):
                setattr(u, self.pola_z_ratami[j] + "_kwota", rata)
                setattr(
                    u,
                    self.pola_z_ratami[j] + "_termin",
                    self.standardowy_termin_zaplaty
                    + datetime.timedelta(days=j + 1 - i),
                )
            u.clean()

    def test_raty_nieciagle_sa_niespojne(self):
        for i in range(1, len(self.pola_z_ratami)):
            # Najpierw sprawdzamy przypadek spójnych rat.
            u = UczestnikFactory(
                kwota_do_zaplaty=self.standardowa_kwota,
                termin_zaplaty=self.standardowy_termin_zaplaty,
            )
            rata = self.standardowa_kwota / i
            for j in range(i):
                setattr(u, self.pola_z_ratami[j] + "_kwota", rata)
                setattr(
                    u,
                    self.pola_z_ratami[j] + "_termin",
                    self.standardowy_termin_zaplaty
                    + datetime.timedelta(days=j + 1 - i),
                )
            u.clean()
            for sufiks in {"_kwota", "_termin"}:
                setattr(
                    u,
                    self.pola_z_ratami[j + 1] + sufiks,
                    getattr(u, self.pola_z_ratami[j] + sufiks),
                )
                setattr(
                    u,
                    self.pola_z_ratami[j] + sufiks,
                    None,
                )
            bledy = ["Harmonogram rat jest nieciągły."]
            self.assertRaisesRegex(
                ValidationError,
                str(bledy),
                u.clean,
            )

    def test_kwoty_rat_musza_sie_sumowac(self):
        i = len(self.pola_z_ratami)
        u = UczestnikFactory(
            kwota_do_zaplaty=self.standardowa_kwota,
            termin_zaplaty=self.standardowy_termin_zaplaty,
        )
        rata = self.standardowa_kwota / i
        for j in range(i):
            setattr(u, self.pola_z_ratami[j] + "_kwota", rata)
            setattr(
                u,
                self.pola_z_ratami[j] + "_termin",
                self.standardowy_termin_zaplaty + datetime.timedelta(days=j + 1 - i),
            )
        u.clean()
        u.kwota_do_zaplaty *= 2
        bledy = ["Suma rat nie jest równa kwocie do zapłaty."]
        self.assertRaisesRegex(
            ValidationError,
            str(bledy),
            u.clean,
        )

    def test_dane_rat_musza_byc_kompletne(self):
        """
        Podanie kwoty wymaga podania terminu (i vice versa).
        """
        i = len(self.pola_z_ratami)
        # Najpierw generujemy poprawny przypadek.
        u = UczestnikFactory(
            kwota_do_zaplaty=self.standardowa_kwota,
            termin_zaplaty=self.standardowy_termin_zaplaty,
        )
        rata = self.standardowa_kwota / i
        for j in range(i):
            setattr(u, self.pola_z_ratami[j] + "_kwota", rata)
            setattr(
                u,
                self.pola_z_ratami[j] + "_termin",
                self.standardowy_termin_zaplaty + datetime.timedelta(days=j + 1 - i),
            )
        u.clean()

        jakies_pole = self.pola_z_ratami[i / 2]
        for sufiks in {"_kwota", "_termin"}:
            tmp = getattr(u, jakies_pole + sufiks)
            setattr(u, jakies_pole + sufiks, None)
            bledy = ["Dane w harmonogramie rat nie są kompletne."]
            self.assertRaisesRegex(
                ValidationError,
                str(bledy),
                u.clean,
            )
            setattr(u, jakies_pole + sufiks, tmp)
            u.clean()


class GrupaZaszeregowaniaTestCase(ALXTestCase):
    def test_pobierz_poprawny_number_grupy_z_nazwy(self):
        grupa = GrupaZaszeregowaniaFactory(nazwa="123: Nazwa")
        self.assertEqual(grupa.get_number(), 123)

    def test_pobierz_niepoprawny_number_grupy_z_nazwy(self):
        grupa = GrupaZaszeregowaniaFactory(nazwa="x: Nazwa")
        self.assertEqual(grupa.get_number(), 0)
        grupa = GrupaZaszeregowaniaFactory(nazwa="")
        self.assertEqual(grupa.get_number(), 0)


@override_settings(
    MINIMALNA_CENA_NETTO_SZKOLENIA_DLA_DARMOWEJ_FAKTURY={
        "pl": 813,
        "en": 500,
    },
    CENA_NETTO_DRUKOWANEGO_CERTYFIKATU={
        "pl": 19,
        "en": 10,
    },
)
class ZgloszenieTestCase(ALXTestCase):
    def test_zgloszenie_waliduje_sie(self):
        zgloszenie = ZgloszenieFactory(cena=1234)
        try:
            zgloszenie.full_clean()
        except ValidationError:
            self.fail("Zgloszenie nie waliduje sie")

    def test_is_doplata_do_faktury_wystepuje_pl(self):
        zgloszenie = ZgloszenieFactory(cena=813, chce_fakture=True)
        self.assertTrue(zgloszenie.is_doplata_do_faktury())

    def test_is_doplata_do_faktury_nie_wystepuje_pl(self):
        zgloszenie = ZgloszenieFactory(cena=822, chce_fakture=True)
        self.assertFalse(zgloszenie.is_doplata_do_faktury())

    def test_is_doplata_do_faktury_wystepuje_en(self):
        zgloszenie = ZgloszenieFactory(
            cena=500, chce_fakture=True, termin__szkolenie__language="en"
        )
        self.assertTrue(zgloszenie.is_doplata_do_faktury())

    def test_is_doplata_do_faktury_nie_wystepuje_en(self):
        zgloszenie = ZgloszenieFactory(
            cena=522, chce_fakture=True, termin__szkolenie__language="en"
        )
        self.assertFalse(zgloszenie.is_doplata_do_faktury())

    def test_set_cena_prywatny_jedna_osoba_pokrywamy_vat(self):
        kurs = KursFactory(cena_bazowa=500)
        termin = TerminSzkoleniaFactory(szkolenie=kurs)
        zgloszenie = ZgloszenieFactory(cena=0, termin=termin, prywatny=True)
        zgloszenie.set_cena()
        self.assertEqual(zgloszenie.cena, 406.50)

    def test_set_cena_firma_dwie_osoby(self):
        kurs = KursFactory(cena_bazowa=500)
        termin = TerminSzkoleniaFactory(szkolenie=kurs)
        zgloszenie = ZgloszenieFactory(
            cena=0, termin=termin, prywatny=False, uczestnik_wieloosobowy_ilosc_osob=2
        )
        zgloszenie.set_cena()
        self.assertEqual(zgloszenie.cena, 1000)

    def test_set_cena_chece_drukowany_certyfikat(self):
        kurs = KursFactory(cena_bazowa=500)
        termin = TerminSzkoleniaFactory(szkolenie=kurs)
        zgloszenie = ZgloszenieFactory(
            cena=0, termin=termin, prywatny=True, drukowany_certyfikat=True
        )
        zgloszenie.set_cena()
        self.assertEqual(zgloszenie.cena, 425.50)

    def test_set_cena_trzy_osoby_chece_drukowany_certyfikat(self):
        kurs = KursFactory(cena_bazowa=500)
        termin = TerminSzkoleniaFactory(szkolenie=kurs)
        zgloszenie = ZgloszenieFactory(
            cena=0,
            termin=termin,
            prywatny=False,
            drukowany_certyfikat=True,
            uczestnik_wieloosobowy_ilosc_osob=3,
        )
        zgloszenie.set_cena()
        self.assertEqual(zgloszenie.cena, 1557)


@unittest.skip("powiadomienie wylaczone")
@override_settings(GENERATE_INVOICE_THROUGH_API_ZLICZACZ=False)
class PoinforumjPotencjalnieZainteresowanychUnitTestCase(ALXTestCase):
    def setup_factories(
        self, termin_data=datetime.date.today() - datetime.timedelta(100)
    ):
        try:
            self.lokalizacja
        except AttributeError:
            self.lokalizacja = LokalizacjaFactory(shortname="Jasiów")
        try:
            self.grupa
        except AttributeError:
            self.grupa = GrupaZaszeregowaniaFactory(nazwa="1: Szydełkowanie Basic")
        try:
            self.szkolenie
        except AttributeError:
            self.szkolenie = SzkolenieFactory(grupa_zaszeregowania=self.grupa)
        try:
            self.termin
        except AttributeError:
            self.termin = TerminSzkoleniaFactory(
                lokalizacja=self.lokalizacja, szkolenie=self.szkolenie
            )
        try:
            self.termin_stary
        except AttributeError:
            self.termin_stary = TerminSzkoleniaFactory(
                lokalizacja=self.lokalizacja,
                szkolenie=self.szkolenie,
                termin=termin_data,
                odbylo_sie=False,
            )
        try:
            self.uczestnik
        except AttributeError:
            self.uczestnik = UczestnikFactory(termin=self.termin_stary)

    def test_start_terminu_wysyla_maila(self):
        self.setup_factories(
            termin_data=datetime.date.today() - datetime.timedelta(100)
        )
        self.termin.odbylo_sie = True
        self.termin.save()
        self.termin.run_celery_jobs_on_conditions(UserFactory.create().pk)
        self.assertIn("Zawiadomić potencjalnie zainteresowanych", mail.outbox[0].body)

    def test_start_terminu_nie_wysyla_do_starszych_niz_180(self):
        self.setup_factories(
            termin_data=datetime.date.today() - datetime.timedelta(200)
        )
        self.termin.odbylo_sie = True
        self.termin.save()
        self.termin.run_celery_jobs_on_conditions(UserFactory.create().pk)

        # 1 mail jest dla potencjalnie chetych (notyfikacje)
        self.assertEqual(len(mail.outbox), 1)

    def test_start_terminu_wysyla_do_mlodszych_niz_270_gz_7(self):
        self.grupa = GrupaZaszeregowaniaFactory(nazwa="7: Szydełkowanie")
        self.setup_factories(
            termin_data=datetime.date.today() - datetime.timedelta(269)
        )
        self.termin.odbylo_sie = True
        self.termin.save()
        self.termin.run_celery_jobs_on_conditions(UserFactory.create().pk)
        self.assertIn("Zawiadomić potencjalnie zainteresowanych", mail.outbox[0].body)

    def test_start_terminu_nie_wysyla_do_starszych_niz_270_gz_7(self):
        self.grupa = GrupaZaszeregowaniaFactory(nazwa="7: Szydełkowanie")
        self.setup_factories(
            termin_data=datetime.date.today() - datetime.timedelta(271)
        )
        self.termin.odbylo_sie = True
        self.termin.save()
        self.termin.run_celery_jobs_on_conditions(UserFactory.create().pk)

        # 1 mail jest dla potencjalnie chetych (notyfikacje)
        self.assertEqual(len(mail.outbox), 1)

    def test_start_terminu_nie_wysyla_maila_dla_termin_zamkniety(self):
        self.lokalizacja = LokalizacjaFactory(shortname="Jasiów")
        self.grupa = GrupaZaszeregowaniaFactory(nazwa="1: Szydełkowanie Basic")
        self.szkolenie = SzkolenieFactory(grupa_zaszeregowania=self.grupa)
        self.termin = TerminSzkoleniaFactory(
            lokalizacja=self.lokalizacja, zamkniete=True, szkolenie=self.szkolenie
        )
        self.setup_factories()
        self.termin.odbylo_sie = True
        self.termin.save()
        self.termin.run_celery_jobs_on_conditions(UserFactory.create().pk)
        self.assertEqual(len(mail.outbox), 0)

    @unittest.skip("Not implemented")
    def test_nie_wysyla_maila_bez_uczestnikow(self):
        self.setup_factories()
        termin_potencjalnie_przeszkolony = TerminSzkoleniaFactory(
            termin=datetime.date.today() - datetime.timedelta(50),
            lokalizacja=self.lokalizacja,
            zamkniete=False,
            odbylo_sie=True,
            szkolenie=self.szkolenie,
        )
        UczestnikFactory(
            termin=termin_potencjalnie_przeszkolony,
            imie_nazwisko=self.uczestnik.imie_nazwisko,
        )
        # self.termin.odbylo_sie = True
        self.termin.save()
        self.termin.run_celery_jobs_on_conditions(UserFactory.create().pk)
        self.assertEqual(len(mail.outbox), 0)

    def test_start_terminu_nie_wysyla_dla_egzaminu(self):
        self.szkolenie = SzkolenieFactory(wysylaj_powiadomienia_proformy=False)
        self.setup_factories(termin_data=datetime.date.today() - datetime.timedelta(50))
        self.termin.odbylo_sie = True
        self.termin.save()
        self.termin.run_celery_jobs_on_conditions(UserFactory.create().pk)
        self.assertEqual(len(mail.outbox), 0)


@override_settings(GENERATE_INVOICE_THROUGH_API_ZLICZACZ=False)
class PoinformujUczestnikowUnitTestCase(ALXTestCase):
    def test_kazdy_dostaje_maila(self):
        termin = TerminSzkoleniaFactory()
        UczestnikFactory.create_batch(2, termin=termin)
        poinformuj_zapisanych_o_potwierdzeniu_terminu.delay(termin.id)

        # Pamiętamy, że na końcu idze jeszcze mail z podsumowaniem do biura.
        self.assertEqual(len(mail.outbox), 2 + 1)

    def test_zapisuje_date_wysylki(self):
        termin = TerminSzkoleniaFactory()
        uczestnicy = UczestnikFactory.create_batch(2, termin=termin)

        self.assertIsNone(uczestnicy[0].mail_o_potwierdzeniu_terminu)
        self.assertIsNone(uczestnicy[1].mail_o_potwierdzeniu_terminu)

        poinformuj_zapisanych_o_potwierdzeniu_terminu.delay(termin.id)

        uczestnicy[0] = Uczestnik.objects.get(pk=uczestnicy[0].pk)
        uczestnicy[1] = Uczestnik.objects.get(pk=uczestnicy[1].pk)

        self.assertIsNotNone(uczestnicy[0].mail_o_potwierdzeniu_terminu)
        self.assertIsNotNone(uczestnicy[1].mail_o_potwierdzeniu_terminu)

    def test_dwukrotne_uruchomienie_nie_wysyla_maili(self):
        termin = TerminSzkoleniaFactory()
        UczestnikFactory.create_batch(2, termin=termin)

        poinformuj_zapisanych_o_potwierdzeniu_terminu.delay(termin.id)

        self.assertEqual(len(mail.outbox), 2 + 1)

        mail.outbox = []

        poinformuj_zapisanych_o_potwierdzeniu_terminu.delay(termin.id)

        self.assertEqual(len(mail.outbox), 0)

    def test_wysylka_do_niektorych(self):
        termin = TerminSzkoleniaFactory()
        uczestnicy = UczestnikFactory.create_batch(2, termin=termin)

        uczestnicy[0].mail_o_potwierdzeniu_terminu = datetime.datetime.now()
        uczestnicy[0].save()

        uczestnicy[1].email = "<EMAIL>"
        uczestnicy[1].save()

        poinformuj_zapisanych_o_potwierdzeniu_terminu.delay(termin.id)

        self.assertEqual(len(mail.outbox), 1 + 1)

        self.assertEqual(mail.outbox[0].to, ["<EMAIL>"])

    def test_wskazanie_uczestnika(self):
        termin = TerminSzkoleniaFactory()
        uczestnicy = UczestnikFactory.create_batch(4, termin=termin)

        poinformuj_zapisanych_o_potwierdzeniu_terminu.delay(termin.id, uczestnicy[0].pk)

        self.assertEqual(len(mail.outbox), 1 + 1)

        self.assertEqual(mail.outbox[0].to, [uczestnicy[0].email])

    def test_to_sentence_0(self):
        lista = []
        lista_string = ""
        self.assertEqual(to_sentence(lista, "i"), lista_string)

    def test_to_sentence_1(self):
        lista = ["raz"]
        lista_string = "raz"
        self.assertEqual(to_sentence(lista, "i"), lista_string)

    def test_to_sentence_2(self):
        lista = ["raz", "dwa"]
        lista_string = "raz i dwa"
        self.assertEqual(to_sentence(lista, "i"), lista_string)

    def test_to_sentence_3(self):
        lista = ["raz", "dwa", "trzy"]
        lista_string = "raz, dwa i trzy"
        self.assertEqual(to_sentence(lista, "i"), lista_string)

    def test_to_sentence_4(self):
        lista = ["raz", "dwa", "trzy", "cztery"]
        lista_string = "raz, dwa, trzy i cztery"
        self.assertEqual(to_sentence(lista, "i"), lista_string)

    def test_uruchomienie_terminu_wysyla_maila(self):
        termin = TerminSzkoleniaFactory()
        UczestnikFactory(termin=termin)
        self.assertEqual(len(mail.outbox), 0)
        termin.odbylo_sie = True
        termin.save()

        termin.run_celery_jobs_on_conditions(UserFactory.create().pk)

        # Pamiętamy, że na końcu idzie jeszcze 1 mail z podsumowaniem do biura.
        self.assertEqual(len(mail.outbox), 1 + 1)

    def test_mail_na_rozne_adresy(self):
        termin = TerminSzkoleniaFactory()
        uczestnik = UczestnikFactory.create(termin=termin, email="<EMAIL>")
        poinformuj_zapisanych_o_potwierdzeniu_terminu(termin.id)

        self.assertEqual(mail.outbox[0].to, ["<EMAIL>"])

        mail.outbox = []
        uczestnik.email_ksiegowosc = "<EMAIL>"
        uczestnik.save()

        poinformuj_zapisanych_o_potwierdzeniu_terminu(termin.id)

        self.assertEqual(mail.outbox[0].to, ["<EMAIL>"])

        mail.outbox = []
        uczestnik.email_ksiegowosc = "<EMAIL>"
        uczestnik.save()
        poinformuj_zapisanych_o_potwierdzeniu_terminu(termin.id)

        self.assertCountEqual(mail.outbox[0].to, ["<EMAIL>", "<EMAIL>"])

    def test_uruchomienie_terminu_nie_wysyla_maila_termin_w_przeszlosci(self):
        termin = TerminSzkoleniaFactory(
            termin=datetime.date.today() - datetime.timedelta(5),
        )
        UczestnikFactory(termin=termin)
        self.assertEqual(len(mail.outbox), 0)
        termin.odbylo_sie = True
        termin.save()
        self.assertEqual(len(mail.outbox), 0)

    def test_uruchomienie_terminu_egzaminu_nie_wysyla_maila(self):
        szkolenie = SzkolenieFactory(wysylaj_powiadomienia_proformy=False)
        termin = TerminSzkoleniaFactory(szkolenie=szkolenie)
        UczestnikFactory(termin=termin)
        self.assertEqual(len(mail.outbox), 0)
        termin.odbylo_sie = True
        termin.save()

        termin.run_celery_jobs_on_conditions(UserFactory.create().pk)
        self.assertEqual(len(mail.outbox), 0)


@override_settings(GENERATE_INVOICE_THROUGH_API_ZLICZACZ=False)
class PoinformujZapisanychOPotwierdzeniuSzkoleniaMailTestCase(
    APIZliczaczPatch, ALXTestCase
):
    def setup_factories(
        self,
        daty_szczegolowo="2000-01-01,2000-01-02",
        tryb=1,
        nr_faktury="",
        language="pl",
        run_task=True,
        sala_wirtualna=False,
        **kwargs,
    ):
        self.lokalizacja = LokalizacjaFactory(
            fullname="Jasiów",
            fullname_miejscownik="Jasiowie",
            ulica_i_numer="ul. Dziwna 7",
        )
        self.szkolenie = SzkolenieFactory(language=language)
        self.termin = TerminSzkoleniaFactory(
            lokalizacja=self.lokalizacja,
            szkolenie=self.szkolenie,
            tryb=tryb,
            sala=SalaFactory.create(
                lokalizacja=self.lokalizacja, sala_wirtualna=sala_wirtualna
            ),
            termin=parse(daty_szczegolowo.split(",")[0]).date(),
            daty_szczegolowo=daty_szczegolowo,
            **kwargs,
        )

        self.uczestnik = UczestnikFactory(
            termin=self.termin,
            adres="adres",
            miejscowosc_kod="77-798",
            prywatny=True,
            wystaw_proforme_automatycznie=True,
            za_kurs_zaplace=5,
            imie_nazwisko_zostalo_sprawdzone=True,
            nr_faktury=nr_faktury,
        )

        if run_task:
            with freeze_time(
                (self.termin.termin - datetime.timedelta(days=2)).isoformat()
            ):
                poinformuj_zapisanych_o_potwierdzeniu_terminu.delay(self.termin.id)

            self.mail_message = mail.outbox[0]

    def test_wiadomosc_zawiera_instrukcje_dotarcia(self):
        self.setup_factories(run_task=False)
        mail.outbox = []

        self.termin.lokalizacja.instrukcja_dotarcia = "dom czwarty; za chata sołtysa"
        self.termin.lokalizacja.save()

        with freeze_time((self.termin.termin - datetime.timedelta(days=2)).isoformat()):
            poinformuj_zapisanych_o_potwierdzeniu_terminu.delay(self.termin.id)

        result_string = (
            "Lokalizacja w której odbędzie się szkolenie to "
            + "ul. Dziwna 7 w Jasiowie (dom czwarty; za chata sołtysa)."
        )
        self.assertIn(result_string, mail.outbox[0].body)

    def test_wiadomosc_zawiera_instrukcje_dotarcia_z_obiektu_sali(self):
        self.setup_factories(run_task=False)
        self.termin.sala.instrukcja_dotarcia = "ABC"
        self.termin.sala.save()

        mail.outbox = []

        with freeze_time((self.termin.termin - datetime.timedelta(days=2)).isoformat()):
            poinformuj_zapisanych_o_potwierdzeniu_terminu.delay(self.termin.id)

        result_string = (
            "Lokalizacja w której odbędzie się szkolenie to "
            + "ul. Dziwna 7 w Jasiowie (ABC)."
        )
        self.assertIn(result_string, mail.outbox[0].body)

    def test_wiadomosc_nie_zawiera_instrukcje_dotarcia_gdy_ulica_i_numer_w_sali(self):
        self.setup_factories(run_task=False)
        self.termin.lokalizacja.instrukcja_dotarcia = "dom czwarty; za chata sołtysa"
        self.termin.lokalizacja.save()
        self.termin.sala.ulica_i_numer = "ABC"
        self.termin.sala.save()

        mail.outbox = []

        with freeze_time((self.termin.termin - datetime.timedelta(days=2)).isoformat()):
            poinformuj_zapisanych_o_potwierdzeniu_terminu.delay(self.termin.id)

        result_string = "Lokalizacja w której odbędzie się szkolenie to " + "ABC"
        self.assertIn(result_string, mail.outbox[0].body)

    def test_wiadomosc_zawiera_instrukcje_dotarcia_gdy_ulica_i_numer_w_sali_i_instrukcje_dotarcia(
        self,
    ):
        self.setup_factories(run_task=False)
        self.termin.lokalizacja.instrukcja_dotarcia = "dom czwarty; za chata sołtysa"
        self.termin.lokalizacja.save()
        self.termin.sala.ulica_i_numer = "ABC"
        self.termin.sala.instrukcja_dotarcia = "123"
        self.termin.sala.save()

        mail.outbox = []

        with freeze_time((self.termin.termin - datetime.timedelta(days=2)).isoformat()):
            poinformuj_zapisanych_o_potwierdzeniu_terminu.delay(self.termin.id)

        result_string = (
            "Lokalizacja w której odbędzie się szkolenie to " + "ABC w Jasiowie (123)."
        )
        self.assertIn(result_string, mail.outbox[0].body)

    def test_wiadomosc_zawiera_poprawny_subject(self):
        self.setup_factories()
        result_string = "Potwierdzenie szkolenia: {} ({} {})".format(
            self.termin.szkolenie.nazwa,
            self.termin.termin,
            self.termin.lokalizacja.fullname,
        )
        self.assertEqual(self.mail_message.subject, result_string)

    def test_wiadomosc_zawiera_nazwe_i_tryb_szkolenia_dzienny(self):
        self.setup_factories(daty_szczegolowo="2000-01-03,2000-01-04")
        result_string = "na szkolenie {} otrzymaliśmy wystarczającą".format(
            self.szkolenie.nazwa
        )
        self.assertIn(result_string, self.mail_message.body)

    def test_wiadomosc_zawiera_nazwe_i_tryb_szkolenia_wieczorowy(self):
        self.setup_factories(daty_szczegolowo="2000-01-03,2000-01-04")
        result_string = "na szkolenie {} otrzymaliśmy wystarczającą".format(
            self.szkolenie.nazwa
        )
        self.assertIn(result_string, self.mail_message.body)

    def test_wiadomosc_zawiera_nazwe_i_tryb_szkolenia_weekendowy(self):
        self.setup_factories(daty_szczegolowo="2000-01-01,2000-01-02")
        result_string = "na szkolenie {} (tryb weekendowy) otrzymaliśmy".format(
            self.szkolenie.nazwa
        )
        self.assertIn(result_string, self.mail_message.body)

    def test_wiadomosc_zawiera_godzine_dzienny(self):
        self.setup_factories()
        result_string = "Szkolenie realizowane będzie w godzinach 9:00 - 17:00"
        self.assertIn(result_string, self.mail_message.body)

    def test_wiadomosc_zawiera_godzine_wieczorowy(self):
        self.setup_factories(daty_szczegolowo="2000-01-01,2000-01-02", tryb=2)
        result_string = "Szkolenie realizowane będzie w godzinach 18:00 - 21:00"
        self.assertIn(result_string, self.mail_message.body)

    def test_wiadomosc_zawiera_godzine_zaoczny(self):
        self.setup_factories(daty_szczegolowo="2000-01-01,2000-01-02")
        result_string = "Szkolenie realizowane będzie w godzinach 9:00 - 17:00"
        self.assertIn(result_string, self.mail_message.body)

    def test_wiadomosc_zawiera_niestandardowe_godziny(self):
        self.setup_factories(niestandardowe_godziny="13:30 - 18:45")
        result_string = "w godzinach 13:30 - 18:45"
        self.assertIn(result_string, self.mail_message.body)

    def test_wiadomosc_zawiera_lokalizacje(self):
        self.setup_factories("2000-01-01,2000-01-02")
        result_string = (
            "Lokalizacja w której odbędzie się szkolenie to "
            + "ul. Dziwna 7 w Jasiowie."
        )
        self.assertIn(result_string, self.mail_message.body)

    def test_wiadomosc_zawiera_lokalizacje_zdalna(self):
        self.setup_factories("2000-01-01,2000-01-02", sala_wirtualna=True)
        result_string = (
            "Szkolenie realizowane będzie w formie zdalnej "
            "(dostęp do sali wirtualnej otrzymają Państwo bliżej terminu "
            "rozpoczęcia zajęć) w godzinach"
        )
        self.assertIn(result_string, self.mail_message.body)

    def test_wiadomosc_zawiera_lokalizacje_z_obiektu_sali(self):
        self.setup_factories("2000-01-01,2000-01-02", run_task=False)
        mail.outbox = []

        self.termin.sala.ulica_i_numer = "ul. Mniej Dziwna 12"
        self.termin.sala.save()

        with freeze_time((self.termin.termin - datetime.timedelta(days=2)).isoformat()):
            poinformuj_zapisanych_o_potwierdzeniu_terminu.delay(self.termin.id)

        result_string = (
            "Lokalizacja w której odbędzie się szkolenie to "
            + "ul. Mniej Dziwna 12 w Jasiowie."
        )
        self.assertIn(result_string, mail.outbox[0].body)

    def test_wiadomosc_zawiera_date_i_lokalizacje_2_dni(self):
        self.setup_factories("2000-01-01,2000-01-02")
        result_string = "Na zajęcia zapraszam w dniach: 1-2.01."
        self.assertIn(result_string, self.mail_message.body)

    def test_wiadomosc_zawiera_date_i_lokalizacje_4_dni(self):
        self.setup_factories("2000-01-01,2000-01-02,2000-01-03,2000-01-04")
        result_string = "Na zajęcia zapraszam w dniach: 1-4.01."
        self.assertIn(result_string, self.mail_message.body)

    def test_wiadomosc_zawiera_date_i_lokalizacje_2_zjazdy(self):
        self.setup_factories("2000-01-01,2000-01-02,2000-01-10,2000-01-11")
        result_string = "Na zajęcia zapraszam w dniach: 1-2.01 oraz 10-11.01."
        self.assertIn(result_string, self.mail_message.body)

    def test_wiadomosc_zawiera_date_i_lokalizacje_3_zjazdy(self):
        daty_szczegolowo = ",".join(
            [
                "2000-01-01,2000-01-02",
                "2000-01-10,2000-01-11",
                "2000-01-20,2000-01-21,2000-01-22",
            ]
        )
        self.setup_factories(daty_szczegolowo=daty_szczegolowo)
        result_string = (
            "Na zajęcia zapraszam w dniach: 1-2.01, " "10-11.01 oraz 20-22.01."
        )
        self.assertIn(result_string, self.mail_message.body)

    def test_jobs_state_status(self):
        """
        Sprawdza, czy metoda `save` modelu poprawnie ustawia wartość
        `jobs_state` podczas zapisu instancji.
        """

        self.setup_factories()

        self.assertFalse(self.termin.jobs_state)
        self.assertFalse(self.termin.internal_jobs_state)

        # Spełnij wymagania terminu do wykonania zadań jednorazowych/
        self.termin.odbylo_sie = True
        self.termin.termin = datetime.date.today()
        self.termin.save()

        self.assertTrue(self.termin.jobs_state)
        self.assertTrue(self.termin.internal_jobs_state)

        # Zmień wartość `odbylo_sie`. Wartość pola `jobs_state` nie powinna się
        # zmienić.
        jobs_state = self.termin.jobs_state
        internal_jobs_state = self.termin.internal_jobs_state

        self.termin.odbylo_sie = False
        self.termin.save()
        self.termin.odbylo_sie = True
        self.termin.save()

        self.assertEqual(jobs_state, self.termin.jobs_state)
        self.assertEqual(internal_jobs_state, self.termin.internal_jobs_state)

    def test_no_proforma_in_email_attachments(self):
        """
        Wysyłane wiadomości email, nie powinny zawierać w załączniku faktury
        pro-forma.
        """

        self.setup_factories()

        self.assertNotIn(
            "W załączniku przesyłam fakturę Pro-Forma.", self.mail_message.body
        )
        self.assertEqual(len(mail.outbox[0].attachments), 0)

    @override_settings(GENERATE_INVOICE_THROUGH_API_ZLICZACZ=True)
    @patch("requests.post")
    @patch("requests.get")
    def test_proforma_in_email_attachments(self, mock_get, mock_post):
        """
        Wysyłane wiadomości email, powinny zawierać w załączniku fakturę
        pro-forma.
        """

        mock_get.side_effect = self.mock_requests_get
        mock_post.side_effect = self.mock_requests_post

        self.setup_factories()

        # Użytkownik muis spełnić wszystkie kryteria do faktury pro-forma
        self.assertTrue(self.uczestnik.object_has_required_data_for_invoice())
        self.assertTrue(self.uczestnik.allowed_to_get_invoice())

        # Informacja o fakturze i faktura w mailu
        self.assertIn(
            "W załączniku przesyłam fakturę Pro-Forma.", self.mail_message.body
        )
        self.assertEqual(len(mail.outbox[0].attachments), 1)

        # Użytkownik powinien mieć zapisane ID faktury, termin płatności oraz
        # numer faktury
        e = Uczestnik.objects.get(pk=self.uczestnik.pk)
        self.assertEqual(e.zliczacz_proforma_no, "99999")
        self.assertEqual(e.nr_proformy, "1/2/3")
        self.assertTrue(e.termin_zaplaty)

        # Powinien wyjść email do biura z podsumowaniem
        self.assertEqual(
            mail.outbox[1].subject, "Podsumowanie wysyłki faktur Pro-forma"
        )

        body = mail.outbox[1].body
        self.assertIn(self.uczestnik.imie_nazwisko_oneline(), body)
        self.assertIn("/admin/www/uczestnik/{0}/".format(self.uczestnik.pk), body)
        self.assertIn("automatyczna faktura: tak", body)
        self.assertIn("faktura wysłana: tak", body)
        self.assertIn("błędy API: brak", body)
        self.assertIn(self.termin.szkolenie.nazwa, body)

    @override_settings(GENERATE_INVOICE_THROUGH_API_ZLICZACZ=True)
    @patch("requests.post")
    @patch("requests.get")
    def test_proforma_failed(self, mock_get, mock_post):
        """
        Wysyłane wiadomości email, nie powinny zawierać w załączniku faktury
        pro-forma, gdyż symulujemy błąd jej generowania.
        Ponadto do biura powinien zostać wyslany email z listą powstałych
        błędów.
        """

        mock_get.side_effect = self.mock_requests_get
        mock_post.side_effect = self.mock_requests_post

        self.response_post_status_code = 400
        self.response_post_text = "{'details': 'error'}"

        self.setup_factories()

        # Użytkownik muis spełnić wszystkie kryteria do faktury pro-forma
        self.assertTrue(self.uczestnik.object_has_required_data_for_invoice())
        self.assertTrue(self.uczestnik.allowed_to_get_invoice())

        # Informacja o fakturze i faktura w mailu
        self.assertNotIn(
            "W załączniku przesyłam fakturę Pro-Forma.", self.mail_message.body
        )
        self.assertEqual(len(mail.outbox[0].attachments), 0)

        # Użytkownik nie powinien mieć zapisane ID faktury, numeru faktury
        e = Uczestnik.objects.get(pk=self.uczestnik.pk)
        self.assertEqual(e.zliczacz_proforma_no, None)
        self.assertFalse(e.nr_proformy)

        # Powinien wyjść email do biura z podsumowaniem i błędem generowania
        # faktury
        self.assertEqual(
            mail.outbox[1].subject, "Podsumowanie wysyłki faktur Pro-forma"
        )

        body = mail.outbox[1].body
        self.assertIn("{'details': 'error'}", body)
        self.assertIn("faktura wysłana: nie", body)

    @override_settings(GENERATE_INVOICE_THROUGH_API_ZLICZACZ=True)
    @patch("requests.post")
    @patch("requests.get")
    def test_proforma_failed_fvat_already_created(self, mock_get, mock_post):
        """
        Wysyłane wiadomości email, nie powinny zawierać w załączniku faktury
        pro-forma, gdyż została już wystawiona wcześniej faktura VAT.
        """

        self.setup_factories(nr_faktury="abc")

        # Użytkownik muis spełnić wszystkie kryteria do faktury pro-forma
        self.assertTrue(self.uczestnik.object_has_required_data_for_invoice())
        self.assertTrue(self.uczestnik.allowed_to_get_invoice())

        # Informacja o fakturze i faktura w mailu
        self.assertNotIn(
            "W załączniku przesyłam fakturę Pro-Forma.", self.mail_message.body
        )
        self.assertEqual(len(mail.outbox[0].attachments), 0)

        # Powinien wyjść email do biura z podsumowaniem i błędem generowania
        # faktury
        self.assertEqual(
            mail.outbox[1].subject, "Podsumowanie wysyłki faktur Pro-forma"
        )

        body = mail.outbox[1].body
        self.assertIn("Faktura już wystawiona.", body)
        self.assertIn("faktura wysłana: nie", body)


@override_settings(GENERATE_INVOICE_THROUGH_API_ZLICZACZ=False)
class PoinformujZapisanychOPotwierdzeniuKursuMailTestCase(ALXTestCase):
    def setup_factories(
        self, tryb=1, za_kurs_zaplace=1, sala_wirtualna=False, run_task=True
    ):
        daty_szczegolowo = ",".join(
            [
                "2000-01-01,2000-01-02",
                "2000-01-10,2000-01-11",
                "2000-01-20,2000-01-21,2000-01-22",
            ]
        )
        self.lokalizacja = LokalizacjaFactory(
            fullname="Jasiów",
            fullname_miejscownik="Jasiowie",
            ulica_i_numer="ul. Dziwna 7",
        )
        self.szkolenie = KursFactory(language="pl")
        self.termin = TerminSzkoleniaFactory(
            lokalizacja=self.lokalizacja,
            szkolenie=self.szkolenie,
            tryb=tryb,
            sala=SalaFactory.create(
                lokalizacja=self.lokalizacja, sala_wirtualna=sala_wirtualna
            ),
            daty_szczegolowo=daty_szczegolowo,
        )
        self.uczestnik = UczestnikFactory(
            termin=self.termin,
            email="jasiu@jasiu@.com",
            za_kurs_zaplace=za_kurs_zaplace,
        )
        if run_task:
            poinformuj_zapisanych_o_potwierdzeniu_terminu.delay(self.termin.id)
            self.mail_message = mail.outbox[0]

    def test_wiadomosc_zawiera_poprawny_subject(self):
        self.setup_factories()
        result_string = "Potwierdzenie kursu: {} ({} {})".format(
            self.termin.szkolenie.nazwa,
            self.termin.termin,
            self.termin.lokalizacja.fullname,
        )
        self.assertEqual(self.mail_message.subject, result_string)

    def test_wiadomosc_zawiera_nazwe_i_tryb_kursu_dzienny(self):
        self.setup_factories()
        result_string = "na kurs {} (tryb dzienny)".format(self.szkolenie.nazwa)
        self.assertIn(result_string, self.mail_message.body)

    def test_wiadomosc_zawiera_harmonogram_kursu(self):
        self.setup_factories()
        result_string = "Poniżej przesyłam harmonogram kursu:"
        self.assertIn(result_string, self.mail_message.body)

    def test_wiadomosc_zawiera_nazwe_i_tryb_kursu_wieczorowy(self):
        self.setup_factories(tryb=2)
        result_string = "na kurs {} (tryb wieczorowy)".format(self.szkolenie.nazwa)
        self.assertIn(result_string, self.mail_message.body)

    def test_wiadomosc_zawiera_nazwe_i_tryb_kursu_zaoczny(self):
        self.setup_factories(tryb=3)
        result_string = "na kurs {} (tryb zaoczny)".format(self.szkolenie.nazwa)
        self.assertIn(result_string, self.mail_message.body)

    def test_wiadomosc_zawiera_date_i_lokalizacje_3_zjazdy(self):
        self.setup_factories()
        result_strings = [
            "1. spotkanie: 1-2 stycznia 2000,",
            "2. spotkanie: 10-11 stycznia 2000,",
            "3. spotkanie: 20-22 stycznia 2000 (zakończenie kursu)",
        ]
        for result_string in result_strings:
            self.assertIn(result_string, self.mail_message.body)

    def test_wiadomosc_zawiera_adnotke_o_ratach(self):
        self.setup_factories(tryb=1, za_kurs_zaplace=10)
        result_string = (
            "W związku z wyborem płatności ratalnej proszę o przesłanie "
            + "obustronnej kopii dowodu osobistego celem przygotowania umowy."
        )
        self.assertIn(result_string, self.mail_message.body)

    def test_wiadomosc_nie_zawiera_adnotki_o_ratach(self):
        self.setup_factories(tryb=1, za_kurs_zaplace=1)
        result_string = (
            "W związku z wyborem płatności ratalnej proszę o przesłanie "
            + "obustronnej kopii dowodu osobistego celem przygotowania umowy."
        )
        self.assertNotIn(result_string, self.mail_message.body)

    def test_wiadomosc_nie_wysylana_jak_nie_ma_maila(self):
        """
        Ma być 2 Maile z setup_factories i 2 tutaj (czyli powtóra + nowy).
        Na pustego maila nie wysyłamy.
        """
        self.setup_factories()
        UczestnikFactory(termin=self.termin, email="")
        UczestnikFactory(termin=self.termin, email="<EMAIL>")
        poinformuj_zapisanych_o_potwierdzeniu_terminu(self.termin.id)

        # Pamiętamy, że na końcu idze jeszcze mail z podsumowaniem do biura.
        self.assertEqual(len(mail.outbox), 3 + 1)

    def test_wiadomosc_wysylana_na_wlasciwy_adres(self):
        self.setup_factories()
        self.assertEqual([self.uczestnik.email], self.mail_message.to)

    @unittest.skip("Not implemented")
    def test_wiadomosc_wysylana_kopia_na_wlasciwy_adres(self):
        self.setup_factories()
        self.assertEqual("mail-monitoring?", self.mail_message.bcc)

    def test_wiadomosc_zawiera_lokalizacje(self):
        self.setup_factories()
        result_string = (
            "Lokalizacja w której odbędzie się kurs to " + "ul. Dziwna 7 w Jasiowie."
        )
        self.assertIn(result_string, self.mail_message.body)

    def test_wiadomosc_zawiera_lokalizacje_zdalna(self):
        self.setup_factories(sala_wirtualna=True)
        result_string = (
            "Kurs realizowany będzie w formie zdalnej "
            "(dostęp do sali wirtualnej otrzymają Państwo bliżej terminu "
            "rozpoczęcia zajęć) w godzinach"
        )
        self.assertIn(result_string, self.mail_message.body)

    def test_wiadomosc_zawiera_lokalizacje_z_obiektu_sali(self):
        self.setup_factories(run_task=False)
        mail.outbox = []

        self.termin.sala.ulica_i_numer = "ul. Mniej Dziwna 12"
        self.termin.sala.save()

        with freeze_time((self.termin.termin - datetime.timedelta(days=2)).isoformat()):
            poinformuj_zapisanych_o_potwierdzeniu_terminu.delay(self.termin.id)

        result_string = (
            "Lokalizacja w której odbędzie się kurs to "
            + "ul. Mniej Dziwna 12 w Jasiowie."
        )
        self.assertIn(result_string, mail.outbox[0].body)


@override_settings(
    GENERATE_INVOICE_THROUGH_API_ZLICZACZ=False, MAIL_OBSLUGA="<EMAIL>"
)
class PoinformujBiuroOPotwierdzeniuTerminuMailTestCase(ALXTestCase):
    def setup_factories(
        self,
        tryb=1,
        za_kurs_zaplace=1,
        add_system_user=True,
        dodatkowi_prowadzacy=None,
        uczestnik_status=1,
        **kwargs,
    ):
        self.user = UserFactory.create(
            first_name="Admin", last_name="Adminowski", email="<EMAIL>"
        )
        daty_szczegolowo = ",".join(
            [
                "2000-01-01,2000-01-02",
                "2000-01-10,2000-01-11",
                "2000-01-20,2000-01-21,2000-01-22",
            ]
        )
        lokalizacja = LokalizacjaFactory(
            fullname="Jasiów",
            fullname_miejscownik="Jasiowie",
            ulica_i_numer="ul. Dziwna 7",
        )
        self.szkolenie = KursFactory(language="pl")
        self.termin = TerminSzkoleniaFactory(
            lokalizacja=lokalizacja,
            szkolenie=self.szkolenie,
            tryb=tryb,
            daty_szczegolowo=daty_szczegolowo,
            obiady="nie-ma-obiadow",
            sala=SalaFactory.create(lokalizacja=lokalizacja, nazwa="Sala numer 5"),
            **kwargs,
        )
        if dodatkowi_prowadzacy:
            for no, prowadzacy in enumerate(dodatkowi_prowadzacy):
                DzienSzkoleniaFactory.create(
                    data=self.termin.termin + datetime.timedelta(days=no),
                    terminszkolenia=self.termin,
                    prowadzacy=prowadzacy,
                    sala=self.termin.sala,
                )
        if add_system_user:
            self.termin.prowadzacy.user = UserFactory.create(email="<EMAIL>")
            self.termin.prowadzacy.save()
        UczestnikFactory.create(
            imie_nazwisko="Jarek A",
            termin=self.termin,
            email="<EMAIL>",
            za_kurs_zaplace=za_kurs_zaplace,
            prywatny=True,
            status=uczestnik_status,
        )
        u = UczestnikFactory.create(
            imie_nazwisko="Marek C",
            uczestnik_wieloosobowy_ilosc_osob=5,
            termin=self.termin,
            email="<EMAIL>",
            za_kurs_zaplace=za_kurs_zaplace,
            status=uczestnik_status,
        )
        Uczestnik.objects.filter(pk=u.pk).update(faktura_firma="Firma ABC")
        u = UczestnikFactory.create(
            imie_nazwisko="Michał Zabłocki",
            termin=self.termin,
            email="<EMAIL>",
            za_kurs_zaplace=za_kurs_zaplace,
            status=uczestnik_status,
        )
        Uczestnik.objects.filter(pk=u.pk).update(faktura_firma="Firma XYZ")
        poinformuj_biuro_o_potwierdzeniu_terminu(self.termin.id, self.user.pk)
        self.mail_message = mail.outbox[0]

    @override_settings(MAIL_KSIEGOWOSC="<EMAIL>")
    def test_trenerzy_z_uod(self):
        p0 = ProwadzacyFactory.create(umowa="uod")
        p1 = ProwadzacyFactory.create(user=UserFactory.create())
        p2 = ProwadzacyFactory.create(user=UserFactory.create(), umowa="b2b")
        p3 = ProwadzacyFactory.create(user=UserFactory.create(email=""), umowa="uod")
        p4 = ProwadzacyFactory.create(umowa="uod")

        self.setup_factories(dodatkowi_prowadzacy=[p1, p2, p3, p4], prowadzacy=p0)
        self.assertEqual(len(mail.outbox), 1)

        self.assertEquals(self.mail_message.to, ["<EMAIL>"])
        self.assertEqual(
            self.mail_message.subject, "Termin: {}. Trenerzy z UoD".format(self.termin.pk)
        )

        self.assertIn(str(p0), self.mail_message.body)
        self.assertIn(str(p3), self.mail_message.body)
        self.assertIn(str(p4), self.mail_message.body)

        self.assertNotIn(str(p1), self.mail_message.body)
        self.assertNotIn(str(p2), self.mail_message.body)


@override_settings(
    GENERATE_INVOICE_THROUGH_API_ZLICZACZ=False, MAIL_OBSLUGA="<EMAIL>"
)
class PoinformujTrenerowOPotwierdzeniuKursuMailTestCase(ALXTestCase):
    def setup_factories(
        self,
        tryb=1,
        za_kurs_zaplace=1,
        add_system_user=True,
        dodatkowi_prowadzacy=None,
        uczestnik_status=1,
        **kwargs,
    ):
        self.user = UserFactory.create(
            first_name="Admin", last_name="Adminowski", email="<EMAIL>"
        )
        daty_szczegolowo = ",".join(
            [
                "2000-01-01,2000-01-02",
                "2000-01-10,2000-01-11",
                "2000-01-20,2000-01-21,2000-01-22",
            ]
        )
        lokalizacja = LokalizacjaFactory(
            fullname="Jasiów",
            fullname_miejscownik="Jasiowie",
            ulica_i_numer="ul. Dziwna 7",
        )
        self.szkolenie = KursFactory(language="pl")
        self.termin = TerminSzkoleniaFactory(
            lokalizacja=lokalizacja,
            szkolenie=self.szkolenie,
            tryb=tryb,
            daty_szczegolowo=daty_szczegolowo,
            obiady="nie-ma-obiadow",
            sala=SalaFactory.create(lokalizacja=lokalizacja, nazwa="Sala numer 5"),
            **kwargs,
        )
        if dodatkowi_prowadzacy:
            for no, prowadzacy in enumerate(dodatkowi_prowadzacy):
                DzienSzkoleniaFactory.create(
                    data=self.termin.termin + datetime.timedelta(days=no),
                    terminszkolenia=self.termin,
                    prowadzacy=prowadzacy,
                    sala=self.termin.sala,
                )
        if add_system_user:
            self.termin.prowadzacy.user = UserFactory.create(email="<EMAIL>")
            self.termin.prowadzacy.save()
        UczestnikFactory.create(
            imie_nazwisko="Jarek A",
            termin=self.termin,
            email="<EMAIL>",
            za_kurs_zaplace=za_kurs_zaplace,
            prywatny=True,
            status=uczestnik_status,
        )
        u = UczestnikFactory.create(
            imie_nazwisko="Marek C",
            uczestnik_wieloosobowy_ilosc_osob=5,
            termin=self.termin,
            email="<EMAIL>",
            za_kurs_zaplace=za_kurs_zaplace,
            status=uczestnik_status,
        )
        Uczestnik.objects.filter(pk=u.pk).update(faktura_firma="Firma ABC")
        u = UczestnikFactory.create(
            imie_nazwisko="Michał Zabłocki",
            termin=self.termin,
            email="<EMAIL>",
            za_kurs_zaplace=za_kurs_zaplace,
            status=uczestnik_status,
        )
        Uczestnik.objects.filter(pk=u.pk).update(faktura_firma="Firma XYZ")
        poinformuj_trenerow_o_potwierdzeniu_terminu(self.termin.id, self.user.pk)
        self.mail_message = mail.outbox[0]

    def test_wiadomosc_zawiera_poprawny_subject(self):
        self.setup_factories()
        result_string = "Potwierdzenie kursu: {} ({} {})".format(
            self.termin.szkolenie.nazwa,
            self.termin.termin,
            self.termin.lokalizacja.fullname,
        )
        self.assertEqual(self.mail_message.subject, result_string)

    def test_wiadomosc_zawiera_nazwe_sali(self):
        self.setup_factories()
        result_string = " (Sala numer 5)"
        self.assertIn(result_string, self.mail_message.body)

    def test_wiadomosc_zawiera_obiady(self):
        self.setup_factories()
        result_string = "Obiady: nie ma obiadów"
        self.assertIn(result_string, self.mail_message.body)

    def test_wiadomosc_zawiera_nazwe_i_tryb_kursu_dzienny(self):
        self.setup_factories()
        result_string = "kurs: {} (tryb dzienny)".format(self.szkolenie.nazwa)
        self.assertIn(result_string, self.mail_message.body)

    def test_wiadomosc_zawiera_nazwe_i_tryb_kursu_wieczorowy(self):
        self.setup_factories(tryb=2)
        result_string = "kurs: {} (tryb wieczorowy)".format(self.szkolenie.nazwa)
        self.assertIn(result_string, self.mail_message.body)

    def test_wiadomosc_zawiera_nazwe_i_tryb_kursu_zaoczny(self):
        self.setup_factories(tryb=3)
        result_string = "kurs: {} (tryb zaoczny)".format(self.szkolenie.nazwa)
        self.assertIn(result_string, self.mail_message.body)

    def test_wiadomosc_zawiera_date_i_lokalizacje_3_zjazdy(self):
        self.setup_factories()
        result_strings = [
            "1. spotkanie: 1-2 stycznia 2000",
            "2. spotkanie: 10-11 stycznia 2000",
            "3. spotkanie: 20-22 stycznia 2000 (zakończenie kursu)",
        ]
        for result_string in result_strings:
            self.assertIn(result_string, self.mail_message.body)

    def test_wiadomosc_zawiera_godzine_dzienny(self):
        self.setup_factories()
        result_string = "Godziny: 9:00 - 17:00"
        self.assertIn(result_string, self.mail_message.body)

    def test_wiadomosc_zawiera_niestandardowe_godziny(self):
        self.setup_factories(niestandardowe_godziny="13:30 - 18:45")
        result_string = "Godziny: 13:30 - 18:45"
        self.assertIn(result_string, self.mail_message.body)

    def test_wiadomosc_zawiera_uwagi_dla_trenera(self):
        self.setup_factories(uwagi_dla_trenera="Hej co robisz? ÓŁŻĆ")
        result_string = "Uwagi niestandardowe: Hej co robisz? ÓŁŻĆ"
        self.assertIn(result_string, self.mail_message.body)

    def test_zawiera_uczestnikow(self):
        self.setup_factories()

        result_string = ". Jarek A"
        self.assertIn(result_string, self.mail_message.body)
        result_string = ". Firma ABC - Ilość osób: 5"
        self.assertIn(result_string, self.mail_message.body)
        result_string = ". Firma XYZ - Michał Zabłocki"
        self.assertIn(result_string, self.mail_message.body)

    def test_brak_maila_gdy_nie_ma_powiazania_z_uzytkownikiem_systemowym(self):
        self.assertRaises(IndexError, self.setup_factories, add_system_user=False)

    def test_wiadomosc_zawiera_poprawny_adres_from(self):
        self.setup_factories()
        self.assertEqual(
            self.mail_message.from_email, "Admin Adminowski <<EMAIL>>"
        )

    def test_wiadomosc_wysylana_do_trenera(self):
        self.setup_factories()
        self.assertEqual(self.mail_message.to, ["<EMAIL>"])

    def test_maile_do_dodatkowych_trenerow(self):
        p1 = ProwadzacyFactory.create(user=UserFactory.create())
        p2 = ProwadzacyFactory.create(user=UserFactory.create())
        p3 = ProwadzacyFactory.create(user=UserFactory.create(email=""))
        p4 = ProwadzacyFactory.create()

        self.setup_factories(dodatkowi_prowadzacy=[p1, p2, p3, p4])
        self.assertEqual(len(mail.outbox), 3)

    def test_brak_potwierdzonych_uczestnikow(self):
        self.assertRaises(IndexError, self.setup_factories, uczestnik_status=-1)


@override_settings(GENERATE_INVOICE_THROUGH_API_ZLICZACZ=False)
class PoinformujTrenerowOPotwierdzeniuSzkoleniaMailTestCase(ALXTestCase):
    def setup_factories(
        self,
        daty_szczegolowo="2000-01-01,2000-01-02",
        tryb=1,
        sala=None,
        uczestnik_status=1,
    ):
        self.lokalizacja = LokalizacjaFactory(
            fullname="Jasiów",
            fullname_miejscownik="Jasiowie",
            ulica_i_numer="ul. Dziwna 7",
        )
        self.user = UserFactory.create(
            first_name="Admin", last_name="Adminowski", email="<EMAIL>"
        )
        self.szkolenie = SzkolenieFactory(language="pl")
        self.termin = TerminSzkoleniaFactory(
            lokalizacja=self.lokalizacja,
            szkolenie=self.szkolenie,
            tryb=tryb,
            sala=sala or SalaFactory.create(lokalizacja=self.lokalizacja),
            termin=parse(daty_szczegolowo.split(",")[0]).date(),
            daty_szczegolowo=daty_szczegolowo,
        )
        UczestnikFactory.create(
            imie_nazwisko="Jarek A",
            termin=self.termin,
            email="<EMAIL>",
            prywatny=True,
            status=uczestnik_status,
        )
        self.termin.prowadzacy.user = UserFactory.create()
        self.termin.prowadzacy.save()
        poinformuj_trenerow_o_potwierdzeniu_terminu(self.termin.id, self.user.pk)
        self.mail_message = mail.outbox[0]

    def test_wiadomosc_zawiera_poprawny_subject(self):
        self.setup_factories()
        result_string = "Potwierdzenie szkolenia: {} ({} {})".format(
            self.termin.szkolenie.nazwa,
            self.termin.termin,
            self.termin.lokalizacja.fullname,
        )
        self.assertEqual(self.mail_message.subject, result_string)

    def test_wiadomosc_zawiera_nazwe_i_tryb_szkolenia_dzienny(self):
        self.setup_factories(daty_szczegolowo="2000-01-03,2000-01-04")
        result_string = "szkolenie: {}".format(self.szkolenie.nazwa)
        self.assertIn(result_string, self.mail_message.body)

    def test_wiadomosc_zawiera_nazwe_i_tryb_szkolenia_wieczorowy(self):
        self.setup_factories(daty_szczegolowo="2000-01-03,2000-01-04")
        result_string = "szkolenie: {}".format(self.szkolenie.nazwa)
        self.assertIn(result_string, self.mail_message.body)

    def test_wiadomosc_zawiera_nazwe_i_tryb_szkolenia_weekendowy(self):
        self.setup_factories(daty_szczegolowo="2000-01-01,2000-01-02")
        result_string = "szkolenie: {} (tryb weekendowy)".format(self.szkolenie.nazwa)
        self.assertIn(result_string, self.mail_message.body)

    def test_wiadomosc_zawiera_date_i_lokalizacje_3_zjazdy(self):
        daty_szczegolowo = ",".join(
            [
                "2000-01-01,2000-01-02",
                "2000-01-10,2000-01-11",
                "2000-01-20,2000-01-21,2000-01-22",
            ]
        )
        self.setup_factories(daty_szczegolowo=daty_szczegolowo)
        result_string = "zajęcia w dniach 1-2.01, 10-11.01 oraz 20-22.01"
        self.assertIn(result_string, self.mail_message.body)

    def test_zawiera_konflikty(self):
        daty_szczegolowo = ",".join(
            [
                "2000-01-01,2000-01-02",
                "2000-01-10,2000-01-11",
                "2000-01-20,2000-01-21,2000-01-22",
            ]
        )

        sala = SalaFactory.create()

        termin1 = TerminSzkoleniaFactory(
            tryb=2,
            sala=sala,
            termin=parse("2000-01-01").date(),
            daty_szczegolowo="2000-01-01,2000-01-03,2000-01-11,2000-01-25",
            termin_zakonczenia="2000-01-25",
            odbylo_sie=True,
        )
        termin2 = TerminSzkoleniaFactory(
            tryb=2,
            sala=SalaFactory.create(),
            termin=parse("2000-01-01").date(),
            daty_szczegolowo="2000-01-01",
            termin_zakonczenia="2000-01-01",
            odbylo_sie=True,
        )
        termin3 = TerminSzkoleniaFactory(
            tryb=2,
            sala=sala,
            termin=parse("2000-01-01").date(),
            daty_szczegolowo="2000-01-01",
            termin_zakonczenia="2000-01-01",
            odbylo_sie=False,
        )
        termin4 = TerminSzkoleniaFactory(
            tryb=2,
            sala=sala,
            termin=parse("2000-01-22").date(),
            daty_szczegolowo="2000-01-22",
            termin_zakonczenia="2000-01-22",
            odbylo_sie=True,
        )
        termin5 = TerminSzkoleniaFactory(
            tryb=1,
            sala=sala,
            termin=parse("2000-01-22").date(),
            daty_szczegolowo="2000-01-22",
            termin_zakonczenia="2000-01-22",
            odbylo_sie=True,
        )
        termin6 = TerminSzkoleniaFactory(
            tryb=1,
            sala=sala,
            termin=parse("2000-01-23").date(),
            daty_szczegolowo="2000-01-23",
            termin_zakonczenia="2000-01-23",
            odbylo_sie=True,
        )

        self.setup_factories(daty_szczegolowo=daty_szczegolowo, sala=sala)

        self.assertIn(
            "- {}, w dniach: 2000-01-01, 2000-01-11".format(termin1.szkolenie),
            self.mail_message.body,
        )
        self.assertNotIn(str(termin2.szkolenie), self.mail_message.body)
        self.assertNotIn(str(termin3.szkolenie), self.mail_message.body)
        self.assertIn(
            "- {}, w dniach: 2000-01-22".format(termin4.szkolenie),
            self.mail_message.body,
        )
        self.assertNotIn(str(termin5.szkolenie), self.mail_message.body)
        self.assertNotIn(str(termin6.szkolenie), self.mail_message.body)

    def test_brak_potwierdzonych_uczestnikow(self):
        self.assertRaises(IndexError, self.setup_factories, uczestnik_status=-1)


class ParseMiejscowoscKodTestCase(TestCase):
    def test_parse_miejscowosc_kod_1(self):
        data = parse_miejscowosc_kod("Warszawa, 00-000")
        self.assertEqual(data["miejscowosc"], "Warszawa")
        self.assertEqual(data["kod"], "00-000")

    def test_parse_miejscowosc_kod_2(self):
        data = parse_miejscowosc_kod("Poznań 12345")
        self.assertEqual(data["miejscowosc"], "Poznań")
        self.assertEqual(data["kod"], "12345")

    def test_parse_miejscowosc_kod_3(self):
        data = parse_miejscowosc_kod("Zielona Góra 01-123")
        self.assertEqual(data["miejscowosc"], "Zielona Góra")
        self.assertEqual(data["kod"], "01-123")


class GoogleCalendarIDValidatorTestCase(TestCase):
    def test_validator(self):
        # Przypadek - bez błędów, id jako email
        calendar = "<EMAIL>"
        validator = validators.GoogleCalendarIDValidator()
        self.assertFalse(validator(calendar))

        # Przypadek - błąd w id jako email
        calendar = "some. <EMAIL>"
        validator = validators.GoogleCalendarIDValidator()
        self.assertRaises(ValidationError, validator, calendar)

        # Przypadek - bez błędów, id jako URL
        calendar = (
            "http://www.google.com/calendar/feeds/some.id@group."
            "calendar.google.com/private-c/basic"
        )
        validator = validators.GoogleCalendarIDValidator()
        self.assertFalse(validator(calendar))

        # Przypadek - błąd w id jako URL
        calendar = (
            "http://www.google.com/calendar/feeds/some.idgroup."
            "calendar.google.com/private-c/basic"
        )
        validator = validators.GoogleCalendarIDValidator()
        self.assertRaises(ValidationError, validator, calendar)

        # Przypadek - błąd w składni URL
        calendar = (
            "http:/www.google.com/calendar/feeds/some.id@group."
            "calendar.google.com/private-c/basic"
        )
        validator = validators.GoogleCalendarIDValidator()
        self.assertRaises(ValidationError, validator, calendar)


class MultiDatesValidatorTestCase(TestCase):
    def test_validator(self):
        dates = ""
        validator = validators.MultiDatesValidator()
        self.assertFalse(validator(dates))

        dates = "2018-01-02"
        validator = validators.MultiDatesValidator()
        self.assertFalse(validator(dates))

        dates = "2018-01-02,2018-02-01,2018-03-01"
        validator = validators.MultiDatesValidator()
        self.assertFalse(validator(dates))

        dates = "2018-01-02,2018-02-01,2018-03-01,ół"
        validator = validators.MultiDatesValidator()
        self.assertRaises(ValidationError, validator, dates)

        dates = "abc"
        validator = validators.MultiDatesValidator()
        self.assertRaises(ValidationError, validator, dates)

        dates = "2018-02-01,01-02-2018"
        validator = validators.MultiDatesValidator()
        self.assertFalse(validator(dates))

        dates = "2018-01-01,01-01-2018"
        validator = validators.MultiDatesValidator()
        self.assertRaises(ValidationError, validator, dates)

        # Dzien wolny od pracy
        dates = "2018-12-25"
        validator = validators.MultiDatesValidator()
        self.assertRaises(ValidationError, validator, dates)

        dates = "2018-01-02,2019-01-05"
        validator = validators.MultiDatesValidator()
        self.assertRaises(ValidationError, validator, dates)


###########
# Urlopy
###########


class LeaveModelUnitTestCase(TestCase):
    def test_unicode(self):
        e = LeaveFactory()
        self.assertEqual(e.__str__(), e.dates)

    def test_save_method(self):
        e = LeaveFactory(dates="2019-01-03,2019-01-01,2017-01-01")

        e = Leave.objects.get(pk=e.pk)

        self.assertEqual(e.days, 3)
        self.assertEqual(e.start, datetime.date(2017, 1, 1))
        self.assertEqual(e.end, datetime.date(2019, 1, 3))


###################
# Absolwneci
###################


class GraduateModelUnitTestCase(ALXTestCase):
    def test_unicode(self):
        e = GraduateFactory()
        self.assertEqual(e.__str__(), "Imię i nazwisko ółążźćń ({0})".format(e.email))

    def test_discount_code_auto_creation(self):
        e = GraduateFactory()

        discount_code = e.discount_code
        self.assertEqual(discount_code.source, "graduate")
        self.assertEqual(discount_code.discount, Decimal(7))

    def test_slug(self):
        e = GraduateFactory()
        self.assertEqual(e.slug, "imie-i-nazwisko-olazzcn")

    def test_empty_slug(self):
        e = GraduateFactory(name="")
        self.assertEqual(e.slug, "no-name")

    def test_non_empty_key(self):
        e = GraduateFactory()
        self.assertTrue(e.key)

    def test_normalized_email(self):
        e = GraduateFactory(email="<EMAIL>")
        self.assertEqual(e.email, "<EMAIL>")

    def test_uniqueness_with_email(self):
        e = GraduateFactory.create()

        # Identyczny adres email w tym samym terminie
        self.assertRaises(
            IntegrityError, GraduateFactory.create, email=e.email, term=e.term
        )

    def test_uniqueness_with_discount_code(self):
        e = GraduateFactory.create()

        # Identyczny kod rabatowy
        self.assertRaises(
            IntegrityError,
            GraduateFactory.create,
            discount_code=e.discount_code,
        )

    def test_generating_unique_number(self):
        self.assertEqual(CertificateNo.objects.all().count(), 0)

        for i in range(10):
            e = GraduateFactory.create()
            self.assertTrue(e.number)

        self.assertEqual(CertificateNo.objects.all().count(), 1)
        obj = CertificateNo.objects.all()[0]
        self.assertEqual(obj.counter, 10)

    def test_saving_template(self):
        # Zapisujemy program szkolenia lokalnie w obiekcie absolwenta
        term = TerminSzkoleniaFactory.create(program_szkolenia="ABCDEFG")
        e = GraduateFactory.create(term=term)
        self.assertEqual(e.template, "ABCDEFG")
        self.assertFalse(e.template_in_pdf)

    def test_saving_template_in_pdf(self):
        term = TerminSzkoleniaFactory.create(szkolenie__program_szkolenia_w_pdf=True)
        e = GraduateFactory.create(term=term)
        self.assertTrue(e.template_in_pdf)

        term = TerminSzkoleniaFactory.create(
            szkolenie__program_szkolenia_w_pdf=True, program_szkolenia_w_pdf=False
        )
        e = GraduateFactory.create(term=term)
        self.assertFalse(e.template_in_pdf)

        term = TerminSzkoleniaFactory.create(
            szkolenie__program_szkolenia_w_pdf=False, program_szkolenia_w_pdf=True
        )
        e = GraduateFactory.create(term=term)
        self.assertTrue(e.template_in_pdf)

        term = TerminSzkoleniaFactory.create(
            szkolenie__program_szkolenia_w_pdf=True, program_szkolenia_w_pdf=None
        )
        e = GraduateFactory.create(term=term)
        self.assertTrue(e.template_in_pdf)

        term = TerminSzkoleniaFactory.create(
            szkolenie__program_szkolenia_w_pdf=False, program_szkolenia_w_pdf=None
        )
        e = GraduateFactory.create(term=term)
        self.assertFalse(e.template_in_pdf)


###################
# E-Certyfikaty
###################


class TextVariablesUnitTestCase(TestCase):
    def test_extract_all_variables_from_text(self):
        text = """
        jakiś tekst ... {var1} .. { var2     } .. {         VAR3}
        """

        variables = utils.extract_variables(text)

        self.assertIn("var1", variables)
        self.assertIn("var2", variables)
        self.assertIn("var3", variables)

        self.assertEqual(variables["var1"], "{var1}")
        self.assertEqual(variables["var2"], "{ var2     }")
        self.assertEqual(variables["var3"], "{         VAR3}")

    def test_replace_variables_in_text(self):
        text = """
        jakiś tekst ... {var1} .. { var2     } .. {         VAR3}
        """

        data = {
            "var1": "@",
            "var2": "#",
            "var3": "ółążśźćń",
        }

        content = utils.replace_variables(text, data)

        self.assertIn("@", content)
        self.assertIn("#", content)
        self.assertIn("ółążśźćń", content)

        # Mimo wiekszej ilości zmiennych niż tych w tekście funkcja zamienia
        # te co są bez zgłaszania wyjątku.
        data = {
            "var1": "@",
            "var2": "#",
            "var3": "ółążśźćń",
            "var4": "ółążśźćń",
        }

        content = utils.replace_variables(text, data)

    def test_empty_variable_block(self):
        text = """
        jakiś tekst ... {} .. {            }
        """

        variables = utils.extract_variables(text)

        self.assertFalse(variables)

    def test_fails_multiline_variable(self):
        text = """
        jakiś tekst ... { tag1
        } ..

        {

            tag2

            }
        """

        variables = utils.extract_variables(text)

        self.assertFalse(variables)

    def test_has_variable_helper(self):
        text = """
        jakiś tekst ... { TAG1   }"""

        self.assertTrue(utils.has_variable(text, "tag1"))
        self.assertFalse(utils.has_variable(text, "tag2"))

    def test_validator(self):
        # Przypadek - bez błędów.

        text = """
        jakiś tekst ... {var1} .. { var2     } .. {         VAR3}
        <p>kolejny poziom</p>
        <strong>{ HEJ! }</strong> {}
        """

        validator = validators.TextVariablesValidator(
            allowed_vars=["var1", "var2", "var3", "hej!"]
        )

        self.assertFalse(validator(text))

        # Przypadek - brakuje wszystkich wymaganych zmiennych.

        text = """
        jakiś tekst ... {
        """

        validator = validators.TextVariablesValidator(
            allowed_vars=["var1", "var2", "var3", "hej!"],
            required_vars=["var1", "var2", "var3", "hej!"],
        )

        self.assertRaises(ValidationError, validator, text)

        # Przypadek - brakuje którejś z wymaganych zmiennych.

        text = """
        jakiś tekst ... {var1} .. { var2     } .. {         VAR3}
        <p>kolejny poziom</p>
        """

        validator = validators.TextVariablesValidator(
            allowed_vars=["var1", "var2", "var3", "hej!"],
            required_vars=["var1", "var2", "var3", "hej!"],
        )

        self.assertRaises(ValidationError, validator, text)

        # Przypadek - niedozwolona zmienna.

        text = """
        jakiś tekst ... {var1} .. { var2     } .. {         VAR3}
        <p>kolejny poziom</p>
        <strong>{ HEJ! }</strong> {oj}
        """

        validator = validators.TextVariablesValidator(
            allowed_vars=["var1", "var2", "var3", "hej!"],
            required_vars=["var1", "var2", "var3", "hej!"],
        )

        self.assertRaises(ValidationError, validator, text)


class DjangoTemplateValidatorTestCase(TestCase):
    def test_validator(self):
        # Przypadek - bez błędów.

        text = """
        jakiś tekst ... {{ var1 }} .. {{ var2     }} .. {{         VAR3 }}
        <p>kolejny poziom</p>
        """

        validator = validators.DjangoTemplateValidator()

        self.assertFalse(validator(text))

        # Przypadek - błąd w składni IF

        text = """
        jakiś tekst ... {% if %}
        """

        validator = validators.DjangoTemplateValidator()

        self.assertRaises(ValidationError, validator, text)


class CertificateTemplateTestCase(ALXTestCase):
    def tearDown(self):
        # Jest jakiś bug z @override_settings(LANGUAGE_CODE='en'), który
        # nie zmienia kontekstu języka po wykonaniu metody.
        activate("pl")

    def test_render_template(self):
        # Poprawny render
        e = TerminSzkoleniaFactory(termin=datetime.date(2014, 11, 11))
        e.szkolenie.language = "pl"
        e.szkolenie.save()

        content = utils.render_template(e.get_szablon_certyfikatu(), {})

        self.assertTrue(content)

        # Błąd składni
        self.assertRaises(TemplateSyntaxError, utils.render_template, "{% if %}", {})

    def test_liczba_dni_pl(self):
        # Sprawdzamy, czy poprawnie jest odmieniana liczba dni w języku
        # polskim

        # Jedna data
        e = TerminSzkoleniaFactory(termin=datetime.date(2014, 11, 11))
        e.szkolenie.language = "pl"
        e.szkolenie.save()

        content = utils.render_template(
            e.get_szablon_certyfikatu(),
            {
                "term_days": len(list(e.daty())),
                "language": e.szkolenie.language,
            },
        )

        self.assertIn("dniu", content)

        # Wiele dat

        e = TerminSzkoleniaFactory(
            termin=datetime.date(2014, 11, 11),
            daty_szczegolowo="2014-11-11,2014-11-12,2014-11-13,2014-11-16",
        )
        e.szkolenie.language = "pl"
        e.szkolenie.save()

        content = utils.render_template(
            e.get_szablon_certyfikatu(),
            {
                "term_days": len(list(e.daty())),
                "language": e.szkolenie.language,
            },
        )

        self.assertIn("dniach", content)

    def test_date_format_pl(self):
        # Sprawdzamy, czy poprawnie jest formatowana data dla języka PL
        #
        # Możliwe przypadki:
        #   - 11 październik 2014
        #   - 11-16 październik 2014
        #   - 11 październik 2014 - 1 grudnia 2014

        # Jedna data
        e = TerminSzkoleniaFactory(termin=datetime.date(2014, 10, 11))
        e.szkolenie.language = "pl"
        e.szkolenie.save()

        content = utils.render_template(
            e.get_szablon_certyfikatu(),
            {
                "term_days": len(list(e.daty())),
                "term_date": e.date_range(),
                "language": e.szkolenie.language,
            },
        )

        self.assertIn("11 października 2014", content)

        # Wiele dat - ten sam miesiąc
        e = TerminSzkoleniaFactory(
            termin=datetime.date(2014, 10, 11),
            daty_szczegolowo="2014-10-11,2014-10-12,2014-10-13,2014-10-16",
        )
        e.szkolenie.language = "pl"
        e.szkolenie.save()

        content = utils.render_template(
            e.get_szablon_certyfikatu(),
            {
                "term_days": len(list(e.daty())),
                "term_date": e.date_range(),
                "language": e.szkolenie.language,
            },
        )

        self.assertIn("11 - 16 października 2014", content)

        # Wiele dat - różne miesiące
        e = TerminSzkoleniaFactory(
            termin=datetime.date(2014, 10, 11),
            daty_szczegolowo="2014-10-11,2014-10-12,2014-10-13,2014-12-16",
        )
        e.szkolenie.language = "pl"
        e.szkolenie.save()

        content = utils.render_template(
            e.get_szablon_certyfikatu(),
            {
                "term_days": len(list(e.daty())),
                "term_date": e.date_range(),
                "language": e.szkolenie.language,
            },
        )

        self.assertIn("11 października 2014 - 16 grudnia 2014", content)

        # Wiele dat - ten sam miesiąc, różny rok
        e = TerminSzkoleniaFactory(
            termin=datetime.date(2014, 10, 11),
            daty_szczegolowo="2014-10-11,2014-10-12,2014-10-13,2015-10-16",
        )
        e.szkolenie.language = "pl"
        e.szkolenie.save()

        content = utils.render_template(
            e.get_szablon_certyfikatu(),
            {
                "term_days": len(list(e.daty())),
                "term_date": e.date_range(),
                "language": e.szkolenie.language,
            },
        )

        self.assertIn("11 października 2014 - 16 października 2015", content)

    @override_settings(LANGUAGE_CODE="en")
    def test_date_format_en(self):
        # Sprawdzamy, czy poprawnie jest formatowana data dla języka EN
        #
        # Możliwe przypadki:
        #   - 11 October 2014
        #   - 11-16 October 2014
        #   - 11 October 2014 - 1 December 2014

        activate("en")

        # Jedna data
        e = TerminSzkoleniaFactory(termin=datetime.date(2014, 10, 11))
        e.szkolenie.language = "en"
        e.szkolenie.save()

        content = utils.render_template(
            e.get_szablon_certyfikatu(),
            {
                "term_days": len(list(e.daty())),
                "term_date": e.date_range(),
                "language": e.szkolenie.language,
            },
        )

        self.assertIn("11 October 2014", content)

        # Wiele dat - ten sam miesiąc
        e = TerminSzkoleniaFactory(
            termin=datetime.date(2014, 10, 11),
            daty_szczegolowo="2014-10-11,2014-10-12,2014-10-13,2014-10-16",
        )
        e.szkolenie.language = "en"
        e.szkolenie.save()

        content = utils.render_template(
            e.get_szablon_certyfikatu(),
            {
                "term_days": len(list(e.daty())),
                "term_date": e.date_range(),
                "language": e.szkolenie.language,
            },
        )

        self.assertIn("11 - 16 October 2014", content)

        # Wiele dat - różne miesiące
        e = TerminSzkoleniaFactory(
            termin=datetime.date(2014, 10, 11),
            daty_szczegolowo="2014-10-11,2014-10-12,2014-10-13,2014-12-16",
        )
        e.szkolenie.language = "en"
        e.szkolenie.save()

        content = utils.render_template(
            e.get_szablon_certyfikatu(),
            {
                "term_days": len(list(e.daty())),
                "term_date": e.date_range(),
                "language": e.szkolenie.language,
            },
        )

        self.assertIn("11 October 2014 - 16 December 2014", content)

        # Wiele dat - ten sam miesiąc, różny rok
        e = TerminSzkoleniaFactory(
            termin=datetime.date(2014, 10, 11),
            daty_szczegolowo="2014-10-11,2014-10-12,2014-10-13,2015-10-16",
        )
        e.szkolenie.language = "en"
        e.szkolenie.save()

        content = utils.render_template(
            e.get_szablon_certyfikatu(),
            {
                "term_days": len(list(e.daty())),
                "term_date": e.date_range(),
                "language": e.szkolenie.language,
            },
        )

        self.assertIn("11 October 2014 - 16 October 2015", content)


class CertificateNoModelUnitTestCase(TestCase):
    def test_number(self):
        date = datetime.date(2016, 1, 1)
        obj = CertificateNoFactory.create(date=date, counter=1)

        self.assertEqual(obj.number, "ALX001001A")

        date = datetime.date(2017, 5, 20)
        obj = CertificateNoFactory.create(date=date, counter=123)

        self.assertEqual(obj.number, "ALX140123B")


@override_settings(
    DOMENY_DLA_JEZYKOW={
        "pl": "pl-domena",
        "en": "en-domena",
    }
)
class CertificateCeleryUnitTestCase(ALXTestCase):
    def _create_certificate(self, language="pl"):
        # Stwórz uczestnika
        graduate = GraduateFactory(name="Imię nazwisko")

        graduate.term.szkolenie.language = language
        graduate.term.szkolenie.save()

        # Uruchom zadanie tworzące obiekt Certificate, PDF i wysyłające email.
        www.tasks.create_certificate.delay(graduate.pk)

        # Sprawdź, czy odpowiednie flagi zostały ustawione
        obj = Graduate.objects.filter(pk=graduate.pk).order_by("-pk")[0]

        self.assertEqual(obj.mail_sent_counter, 1)
        self.assertTrue(obj.mail_sent_at)

        # Sprawdź, czy plik PDF został wygenerowany
        self.assertTrue(obj.pdf)

        self.assertEqual(len(mail.outbox), 1)

        # Sprawdź, czy odpowiednia domena została ustawiona
        self.assertIn("{0}-domena".format(language), mail.outbox[0].body)

        # Sprawdź, czy odpowiedni link został wygenrowany
        url = reverse(
            "certificate",
            kwargs={
                "slug": "imie-nazwisko",
                "key": obj.key.hex,
                "language": language,
            },
        )
        self.assertIn(url, mail.outbox[0].body)
        self.assertEqual(mail.outbox[0].to, [obj.email])

    def test_create_certificate_pl(self):
        self._create_certificate()

    def test_create_certificate_en(self):
        self._create_certificate(language="en")

    def test_update_certificate(self):
        # Stwórz uczestnika
        graduate = GraduateFactory(name="Imię nazwisko")

        # Uruchom zadanie wysyłające jeszcze raz email do uczestnika
        # oraz generujące PDF.
        www.tasks.update_certificate.delay(graduate.pk)

        self.assertEqual(len(mail.outbox), 1)
        self.assertEqual(mail.outbox[0].to, [graduate.email])


###################
# Autoresponder
###################


@override_settings(
    DOMENY_DLA_JEZYKOW={"pl": "www.alx.dev", "en": "www.alx-training.co.dev"},
    MAIL_FROM_ADDRESS_AUTOREPLAY="<EMAIL>",
    MAIL_FROM_ADDRESS_AUTOREPLAY_EN="<EMAIL>",
)
class AutoresponderLogTestCase(ALXTestCase):
    def test_allow_to_send_autoreplay_more_than_15_emails(self):
        AutoresponderLogFactory.create_batch(20)
        self.assertFalse(
            AutoresponderLog.objects.allow_to_send_autoreplay("<EMAIL>")
        )

    def test_allow_to_send_autoreplay_15_emails(self):
        AutoresponderLogFactory.create_batch(15)
        self.assertFalse(
            AutoresponderLog.objects.allow_to_send_autoreplay("<EMAIL>")
        )

    def test_allow_to_send_autoreplay_the_same_emails(self):
        AutoresponderLogFactory.create_batch(5, receiver_email="<EMAIL>")
        self.assertFalse(
            AutoresponderLog.objects.allow_to_send_autoreplay("<EMAIL>")
        )

    def test_allow_to_send_autoreplay_with_true(self):
        AutoresponderLogFactory.create_batch(4, receiver_email="<EMAIL>")
        self.assertTrue(
            AutoresponderLog.objects.allow_to_send_autoreplay("<EMAIL>")
        )

        AutoresponderLog.objects.all()

        self.assertTrue(
            AutoresponderLog.objects.allow_to_send_autoreplay("<EMAIL>")
        )

        AutoresponderLogFactory.create_batch(9)

        self.assertTrue(
            AutoresponderLog.objects.allow_to_send_autoreplay("<EMAIL>")
        )

        AutoresponderLog.objects.all().delete()
        logs = AutoresponderLogFactory.create_batch(15)
        logs[0].email_sent_at -= datetime.timedelta(minutes=62)
        logs[0].save()

        self.assertTrue(
            AutoresponderLog.objects.allow_to_send_autoreplay("<EMAIL>")
        )

    def test_autoresponder_task_pl(self):
        www.tasks.autoresponder.delay(
            email="<EMAIL>",
            topic="formularz_kontaktowy",
            language="pl",
            email_data={"imie_nazwisko": "ół ZX", "tresc": "abc ²"},
        )

        self.assertEqual(len(mail.outbox), 1)

        body = mail.outbox[0].body
        subject = mail.outbox[0].subject

        self.assertEqual(subject, "Dostaliśmy twój formularz")
        self.assertIn("Witaj ół ZX", body)
        self.assertIn("abc ²", body)
        self.assertEqual(mail.outbox[0].from_email, "<EMAIL>")

        self.assertEqual(AutoresponderLog.objects.all().count(), 1)

    def test_autoresponder_task_en(self):
        www.tasks.autoresponder.delay(
            email="<EMAIL>",
            topic="propozycja_terminu",
            language="en",
            email_data={"imie_nazwisko": "ół ZX", "szkolenie": "Nazwa szkolenia"},
        )

        self.assertEqual(len(mail.outbox), 1)

        body = mail.outbox[0].body
        subject = mail.outbox[0].subject

        self.assertEqual(subject, "We received your request")
        self.assertIn("Hello ół ZX", body)
        self.assertEqual(mail.outbox[0].from_email, "<EMAIL>")

        self.assertEqual(AutoresponderLog.objects.all().count(), 1)

    @patch("www.tasks.logger")
    def test_autoresponder_limit_exceeded(self, mock_logger):
        AutoresponderLogFactory.create_batch(15)

        www.tasks.autoresponder.delay(
            email="<EMAIL>",
            topic="formularz_kontaktowy",
            language="pl",
            email_data={"imie_nazwisko": "ół ZX"},
        )

        mock_logger.error.assert_called_with(
            "Wykorzystany limit autoodpowiedzi dla: <EMAIL>. Topic: formularz_kontaktowy"
        )
        self.assertEqual(len(mail.outbox), 0)

    def test_autoresponder_custom_subject(self):
        www.tasks.autoresponder.delay(
            email="<EMAIL>",
            topic="link_rejestracyjny",
            language="en",
            email_data={"imie_nazwisko": "ół ZX"},
            msg_subject="ÓŁ ABC",
        )

        self.assertEqual(len(mail.outbox), 1)

        self.assertEqual(mail.outbox[0].subject, "ÓŁ ABC")


###########################
# Powiadomienia użytkownika
############################


@override_settings(
    MAIL_FROM_ADDRESS="<EMAIL>", MAIL_FROM_ADDRESS_EN="<EMAIL>"
)
class UczestnikNotificationTestCase(ALXTestCase):
    def test_nothing_to_do(self):
        cmd = installment_notification.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 0)
        self.assertEqual(UczestnikNotification.objects.count(), 0)

    def test_send_email_for_payment_3(self):
        day_after_tomorrow = datetime.date.today() + datetime.timedelta(days=2)

        for no, field in enumerate(["rata1", "rata2"]):
            mail.outbox = []
            UczestnikNotification.objects.all().delete()
            Uczestnik.objects.all().delete()

            UczestnikFactory.create(
                status=-1,
                za_kurs_zaplace=3,
                zaliczka_kwota=10,
                **{
                    "{}_termin".format(field): day_after_tomorrow,
                    "{}_zaplacone".format(field): None,
                    "{}_kwota".format(field): 100,
                },
            )
            UczestnikFactory.create(
                status=1,
                za_kurs_zaplace=3,
                termin__odbylo_sie=False,
                zaliczka_kwota=10,
                **{
                    "{}_termin".format(field): day_after_tomorrow,
                    "{}_zaplacone".format(field): None,
                    "{}_kwota".format(field): 100,
                },
            )
            UczestnikFactory.create(
                status=1,
                za_kurs_zaplace=3,
                termin__odbylo_sie=True,
                email="",
                zaliczka_kwota=10,
                **{
                    "{}_termin".format(field): day_after_tomorrow,
                    "{}_zaplacone".format(field): None,
                    "{}_kwota".format(field): 100,
                },
            )
            UczestnikFactory.create(
                status=1,
                za_kurs_zaplace=3,
                termin__odbylo_sie=True,
                zaliczka_kwota=10,
                **{
                    "{}_termin".format(field): day_after_tomorrow
                    + datetime.timedelta(days=1),
                    "{}_zaplacone".format(field): None,
                    "{}_kwota".format(field): 100,
                },
            )
            participant = UczestnikFactory.create(
                status=1,
                za_kurs_zaplace=3,
                termin__odbylo_sie=True,
                zaliczka_kwota=10,
                **{
                    "{}_termin".format(field): day_after_tomorrow,
                    "{}_zaplacone".format(field): None,
                    "{}_kwota".format(field): 100,
                },
            )
            cmd = installment_notification.Command()
            cmd.handle()

            self.assertEqual(len(mail.outbox), 1)

            body = mail.outbox[0].body
            subject = mail.outbox[0].subject

            self.assertEqual(subject, "Przypomnienie o terminie płatności")
            self.assertIn("raty nr {} za kurs".format(no + 1), body)
            self.assertIn("123.00 zł", body)
            self.assertIn(
                "{}, {}, {}".format(
                    participant.get_nazwa()[:32],
                    participant.termin.termin.isoformat(),
                    participant.termin.szkolenie.kod,
                ),
                body,
            )
            self.assertEqual(mail.outbox[0].from_email, "<EMAIL>")
            self.assertEqual(mail.outbox[0].to, [participant.email])

            log = UczestnikNotification.objects.get()
            self.assertEqual(log.email, participant.email)
            self.assertEqual(log.participant, participant)

    def test_send_email_for_payment_5(self):
        day_after_tomorrow = datetime.date.today() + datetime.timedelta(days=2)

        for no, field in enumerate(["zaliczka", "rata1", "rata2", "rata3", "rata4"]):
            mail.outbox = []
            UczestnikNotification.objects.all().delete()
            Uczestnik.objects.all().delete()

            UczestnikFactory.create(
                status=-1,
                za_kurs_zaplace=10,
                **{
                    "{}_termin".format(field): day_after_tomorrow,
                    "{}_zaplacone".format(field): None,
                    "{}_kwota".format(field): 100,
                    "zaliczka_kwota": 100,
                },
            )
            UczestnikFactory.create(
                status=3,
                za_kurs_zaplace=10,
                termin__odbylo_sie=False,
                **{
                    "{}_termin".format(field): day_after_tomorrow,
                    "{}_zaplacone".format(field): None,
                    "{}_kwota".format(field): 100,
                    "zaliczka_kwota": 100,
                },
            )
            UczestnikFactory.create(
                status=3,
                za_kurs_zaplace=10,
                termin__odbylo_sie=True,
                email="",
                **{
                    "{}_termin".format(field): day_after_tomorrow,
                    "{}_zaplacone".format(field): None,
                    "{}_kwota".format(field): 100,
                    "zaliczka_kwota": 100,
                },
            )
            UczestnikFactory.create(
                status=3,
                za_kurs_zaplace=10,
                termin__odbylo_sie=True,
                **{
                    "{}_termin".format(field): day_after_tomorrow
                    - datetime.timedelta(days=1),
                    "{}_zaplacone".format(field): None,
                    "{}_kwota".format(field): 100,
                    "zaliczka_kwota": 100,
                },
            )

            participant = UczestnikFactory.create(
                status=3,
                za_kurs_zaplace=10,
                termin__odbylo_sie=True,
                **{
                    "{}_termin".format(field): day_after_tomorrow,
                    "{}_zaplacone".format(field): None,
                    "{}_kwota".format(field): 100,
                    "zaliczka_kwota": 100,
                },
            )
            cmd = installment_notification.Command()
            cmd.handle()

            self.assertEqual(len(mail.outbox), 1)

            body = mail.outbox[0].body
            subject = mail.outbox[0].subject

            self.assertEqual(subject, "Przypomnienie o terminie płatności")
            self.assertIn("raty nr {} za kurs".format(no + 1), body)
            self.assertIn("123.00 zł", body)
            self.assertIn(
                "{}, {}, {}".format(
                    participant.get_nazwa()[:32],
                    participant.termin.termin.isoformat(),
                    participant.termin.szkolenie.kod,
                ),
                body,
            )
            self.assertEqual(mail.outbox[0].from_email, "<EMAIL>")
            self.assertEqual(mail.outbox[0].to, [participant.email])

            log = UczestnikNotification.objects.get()
            self.assertEqual(log.email, participant.email)
            self.assertEqual(log.participant, participant)

    def test_log_already_exist(self):
        day_after_tomorrow = datetime.date.today() + datetime.timedelta(days=2)

        participant = UczestnikFactory.create(
            status=1,
            za_kurs_zaplace=3,
            termin__odbylo_sie=True,
            zaliczka_kwota=10,
            **{
                "rata1_termin": day_after_tomorrow,
                "rata1_zaplacone": None,
                "rata1_kwota": 100,
            },
        )
        UczestnikNotificationFactory.create(
            participant=participant, notification_type="rata_1_niezaplacona"
        )
        cmd = installment_notification.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 0)

    def test_send_many_mails(self):
        day_after_tomorrow = datetime.date.today() + datetime.timedelta(days=2)

        UczestnikFactory.create_batch(
            4,
            status=3,
            za_kurs_zaplace=7,
            termin__odbylo_sie=True,
            zaliczka_kwota=10,
            **{
                "rata1_termin": day_after_tomorrow,
                "rata1_zaplacone": None,
                "rata1_kwota": 100,
            },
        )
        UczestnikFactory.create_batch(
            2,
            status=3,
            za_kurs_zaplace=3,
            termin__odbylo_sie=True,
            zaliczka_kwota=10,
            **{
                "rata1_termin": day_after_tomorrow,
                "rata1_zaplacone": None,
                "rata1_kwota": 100,
            },
        )
        UczestnikFactory.create_batch(
            3,
            status=1,
            za_kurs_zaplace=10,
            termin__odbylo_sie=True,
            zaliczka_kwota=10,
            **{
                "rata4_termin": day_after_tomorrow,
                "rata4_zaplacone": None,
                "rata4_kwota": 100,
            },
        )
        cmd = installment_notification.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 5)
        self.assertEqual(UczestnikNotification.objects.count(), 5)

    @patch("www.management.commands.installment_notification.logger")
    def test_smtp_error(self, mock_logger):
        day_after_tomorrow = datetime.date.today() + datetime.timedelta(days=2)

        participant = UczestnikFactory.create(
            status=1,
            za_kurs_zaplace=3,
            termin__odbylo_sie=True,
            zaliczka_kwota=10,
            **{
                "rata1_termin": day_after_tomorrow,
                "rata1_zaplacone": None,
                "rata1_kwota": 100,
            },
        )

        # Robimy patch na klasę `EmailMessage` w celu sprawdzenia poprawności
        # logowania błędu o nieudanym wysłaniu maila.
        with patch(
            "django.core.mail.EmailMessage.send",
            MagicMock(side_effect=Exception("Fail!")),
        ):

            cmd = installment_notification.Command()
            cmd.handle()

            mock_logger.exception.assert_called_with(
                "Błąd przy wysyłce maila 'przypomnienie o racie' dla użytkownika ID: "
                "{0} ({1})".format(participant.pk, participant.email)
            )


###################
# API zliczacz
###################


class APIZliczaczUnitTestCase(APIZliczaczPatch, ALXTestCase):
    @patch("requests.post")
    def test_generate_invoice(self, mock_post):
        """
        Test tworzenia faktury dla uczestnika i zwracania jej ID.
        """

        mock_post.side_effect = self.mock_requests_post

        e = UczestnikFactory()
        e.faktura_firma = "Firma#1"
        e.faktura_adres = "Adres"
        e.faktura_miejscowosc_kod = "33-389 Miasto"
        e.faktura_nip = "111"
        e.save()

        # Oczekujemy pozytywnego wyniku
        idata, errors = api.generate_invoice(e)

        self.assertFalse(errors)
        self.assertEqual(idata["faktura_id"], 99999)

        # Oczekujemy błędnego wyniku
        self.response_post_status_code = 400
        self.response_post_text = "{'details': 'error'}"

        idata, errors = api.generate_invoice(e)

        self.assertFalse(idata)
        self.assertEqual(errors, "{'details': 'error'}")

    @patch("requests.post")
    def test_generate_invoice_description(self, mock_post):
        """
        Test tworzenia faktury dla uczestnika w zależności od stawki VAT.
        """

        mock_post.side_effect = self.mock_requests_post

        e = UczestnikFactory()
        e.faktura_firma = "Firma#1"
        e.faktura_adres = "Adres"
        e.faktura_miejscowosc_kod = "33-389 Miasto"
        e.faktura_nip = "111"
        e.stawka_vat = 0
        e.podmiot_publiczny = True
        e.save()

        # Oczekujemy pozytywnego wyniku
        idata, errors = api.generate_invoice(e)

        # Podmiot publiczny
        self.assertFalse(errors)
        self.assertEqual(
            idata["parsed_data"]["faktura_uwagi"],
            "usługa zwolniona z VAT na podstawie art. 43 ust. 1 pkt 29 "
            "lit. c ustawy o VAT",
        )

        # Akredytacja
        e.podmiot_publiczny = False
        e.termin.is_posiada_akredytacje = lambda: True
        e.save()

        # Oczekujemy pozytywnego wyniku
        idata, errors = api.generate_invoice(e)

        self.assertFalse(errors)
        self.assertEqual(
            idata["parsed_data"]["faktura_uwagi"],
            "usługa zwolniona z VAT na podstawie art. 43 ust. 1 pkt 29 "
            "lit. b ustawy o VAT",
        )

    @patch("requests.post")
    def test_generate_invoice_installments_5_installments(self, mock_post):
        """
        Test tworzenia faktury dla uczestnika i zwracania jej ID.
        """

        mock_post.side_effect = self.mock_requests_post

        e = UczestnikFactory(za_kurs_zaplace=10)
        e.faktura_firma = "Firma#1"
        e.faktura_adres = "Adres"
        e.faktura_miejscowosc_kod = "33-389 Miasto"
        e.faktura_nip = "111"
        e.save()

        # Oczekujemy pozytywnego wyniku
        idata, errors = api.generate_invoice_installments(e)

        self.assertFalse(errors)
        self.assertEqual(idata["faktura_id"], 99999)
        self.assertEqual(len(idata["parsed_data"]["pozycje_faktury"]), 4)

        self.assertTrue(
            idata["parsed_data"]["pozycje_faktury"][0]["pozycja_opis"].endswith(
                " - II rata"
            )
        )
        self.assertTrue(
            idata["parsed_data"]["pozycje_faktury"][1]["pozycja_opis"].endswith(
                " - III rata"
            )
        )
        self.assertTrue(
            idata["parsed_data"]["pozycje_faktury"][2]["pozycja_opis"].endswith(
                " - IV rata"
            )
        )
        self.assertTrue(
            idata["parsed_data"]["pozycje_faktury"][3]["pozycja_opis"].endswith(
                " - V rata"
            )
        )

        # Oczekujemy błędnego wyniku
        self.response_post_status_code = 400
        self.response_post_text = "{'details': 'error'}"

        idata, errors = api.generate_invoice(e)

        self.assertFalse(idata)
        self.assertEqual(errors, "{'details': 'error'}")

    @patch("requests.post")
    def test_generate_invoice_installments_1_installment(self, mock_post):
        """
        Test tworzenia faktury dla uczestnika i zwracania jej ID.
        """

        mock_post.side_effect = self.mock_requests_post

        e = UczestnikFactory(za_kurs_zaplace=2)
        e.faktura_firma = "Firma#1"
        e.faktura_adres = "Adres"
        e.faktura_miejscowosc_kod = "33-389 Miasto"
        e.faktura_nip = "111"
        e.save()

        # Oczekujemy pozytywnego wyniku
        idata, errors = api.generate_invoice_installments(e)

        self.assertFalse(errors)
        self.assertEqual(idata["faktura_id"], 99999)
        self.assertEqual(len(idata["parsed_data"]["pozycje_faktury"]), 1)

        self.assertFalse(
            idata["parsed_data"]["pozycje_faktury"][0]["pozycja_opis"].endswith(
                " - I rata"
            )
        )

        # Oczekujemy błędnego wyniku
        self.response_post_status_code = 400
        self.response_post_text = "{'details': 'error'}"

        idata, errors = api.generate_invoice(e)

        self.assertFalse(idata)
        self.assertEqual(errors, "{'details': 'error'}")

    @patch("requests.post")
    def test_generate_invoice_installments_2_installments(self, mock_post):
        """
        Test tworzenia faktury dla uczestnika i zwracania jej ID.
        """

        mock_post.side_effect = self.mock_requests_post

        e = UczestnikFactory(za_kurs_zaplace=3)
        e.faktura_firma = "Firma#1"
        e.faktura_adres = "Adres"
        e.faktura_miejscowosc_kod = "33-389 Miasto"
        e.faktura_nip = "111"
        e.save()

        # Oczekujemy pozytywnego wyniku
        idata, errors = api.generate_invoice_installments(e)

        self.assertFalse(errors)
        self.assertEqual(idata["faktura_id"], 99999)
        self.assertEqual(len(idata["parsed_data"]["pozycje_faktury"]), 2)

        self.assertTrue(
            idata["parsed_data"]["pozycje_faktury"][0]["pozycja_opis"].endswith(
                " - I rata"
            )
        )
        self.assertTrue(
            idata["parsed_data"]["pozycje_faktury"][1]["pozycja_opis"].endswith(
                " - II rata"
            )
        )

        # Oczekujemy błędnego wyniku
        self.response_post_status_code = 400
        self.response_post_text = "{'details': 'error'}"

        idata, errors = api.generate_invoice(e)

        self.assertFalse(idata)
        self.assertEqual(errors, "{'details': 'error'}")

    @patch("requests.post")
    def test_generate_invoice_installments_description(self, mock_post):
        """
        Test tworzenia faktury dla uczestnika w zależności od stawki VAT.
        """

        mock_post.side_effect = self.mock_requests_post

        today = datetime.date.today()

        e = UczestnikFactory(za_kurs_zaplace=10)
        e.faktura_firma = "Firma#1"
        e.faktura_adres = "Adres"
        e.faktura_miejscowosc_kod = "33-389 Miasto"
        e.faktura_nip = "111"
        e.stawka_vat = 0
        e.podmiot_publiczny = True
        e.rata1_termin = today
        e.rata1_zaplacone = today
        e.rata2_termin = today + datetime.timedelta(days=1)
        e.rata3_termin = today + datetime.timedelta(days=2)
        e.rata4_termin = today + datetime.timedelta(days=3)
        e.save()

        # Oczekujemy pozytywnego wyniku
        idata, errors = api.generate_invoice_installments(e)

        self.assertEqual(
            idata["parsed_data"]["faktura_sposob_platnosci"],
            "II rata zapłacono {}, III rata do zapłaty w terminie do {}, "
            "IV rata do zapłaty w terminie do {}, V rata do zapłaty w "
            "terminie do {}".format(
                e.rata1_zaplacone.isoformat(),
                e.rata2_termin.isoformat(),
                e.rata3_termin.isoformat(),
                e.rata4_termin.isoformat(),
            ),
        )

        # Podmiot publiczny
        self.assertFalse(errors)
        self.assertEqual(
            idata["parsed_data"]["faktura_uwagi"],
            "usługa zwolniona z VAT na podstawie art. 43 ust. 1 pkt 29 "
            "lit. c ustawy o VAT",
        )

        # Akredytacja
        e.podmiot_publiczny = False
        e.termin.is_posiada_akredytacje = lambda: True
        e.save()

        # Oczekujemy pozytywnego wyniku
        idata, errors = api.generate_invoice(e)

        self.assertFalse(errors)
        self.assertEqual(
            idata["parsed_data"]["faktura_uwagi"],
            "usługa zwolniona z VAT na podstawie art. 43 ust. 1 pkt 29 "
            "lit. b ustawy o VAT",
        )

    @patch("requests.post")
    def test_generate_invoice_zaliczkowa(self, mock_post):
        mock_post.side_effect = self.mock_requests_post

        e = UczestnikFactory()
        e.faktura_firma = "Firma#1"
        e.faktura_adres = "Adres"
        e.faktura_miejscowosc_kod = "33-389 Miasto"
        e.faktura_nip = "111"
        e.stawka_vat = 0
        e.podmiot_publiczny = True
        e.save()

        # Oczekujemy pozytywnego wyniku
        idata, errors = api.generate_invoice(e)

        self.assertFalse(errors)

        # Zaliczki zostały usuniete - 08-11-2018
        self.assertFalse(idata["parsed_data"]["pozycje_faktury"][0]["pozycja_zaliczka"])

    @patch("requests.get")
    def test_get_invoice_pdf(self, mock_get):
        """
        Test pobierania faktury dla uczestnika i zwracania jej contentu.
        """

        mock_get.side_effect = self.mock_requests_get

        # Oczekujemy pozytywnego wyniku
        content, errors = api.get_invoice_pdf(1)

        self.assertFalse(errors)
        self.assertEqual(content.getvalue(), "Jakiś tekst ...".encode("utf-8"))

        # Oczekujemy błędnego wyniku
        self.response_get_status_code = 400
        self.response_get_text = "{'details': 'error'}"

        content, errors = api.get_invoice_pdf(1)

        self.assertFalse(content)
        self.assertEqual(errors, "{'details': 'error'}")

    @override_settings(GENERATE_INVOICE_THROUGH_API_ZLICZACZ=True)
    @patch("requests.patch")
    def test_change_invoice_status(self, mock_patch):
        """
        Zmiana statusu faktury.
        """

        mock_patch.side_effect = self.mock_requests_post

        self.response_post_status_code = 200
        self.response_post_text = "{'status_faktury': 4}"

        result, errors = api.update_invoice_status(
            1234, "paid", datetime.date.today().isoformat()
        )
        self.assertTrue(result)

    @override_settings(GENERATE_INVOICE_THROUGH_API_ZLICZACZ=True)
    @patch("requests.patch")
    def test_change_invoice_status_failed(self, mock_patch):
        """
        Błąd podczas zmiany statusu faktury.
        """

        mock_patch.side_effect = self.mock_requests_post

        self.response_post_status_code = 400
        self.response_post_text = "{'details': 'error'}"

        result, errors = api.update_invoice_status(1234, "paid")
        self.assertFalse(result)
        self.assertEqual(errors, "{'details': 'error'}")


###################
# Wysyłka faktur
###################


@override_settings(
    DOMENY_DLA_JEZYKOW={"pl": "www.alx.dev", "en": "www.alx-training.co.dev"},
    FORCE_SSL=True,
)
class FakturaWysylkaTestCase(ALXTestCase):
    @staticmethod
    def get_api_data():
        return {
            "faktura_ostatnia_zmiana": "2016-11-16T19:58:08.582000",
            "faktura_sprzedawca": 37,
            "faktura_uwagi": None,
            "faktura_id": 7,
            "faktura_miejsce_wystawienia": "Warszawa",
            "faktura_kod_pocztowy_sprzedawcy": "02-298",
            "faktura_numer": "1/1/2",
            "faktura_data_sprzedazy": "2016-11-16",
            "faktura_adres_nabywcy": "ul. Złota 18a/5",
            "typ_faktury": 1,
            "pozycje_faktury": [
                dict(
                    [
                        ("pozycja_opis", "Opis pozycji"),
                        ("pozycja_cena_jednostkowa_netto", "486.18"),
                        ("pozycja_wartosc_netto", "486.18"),
                        ("pozycja_kwota_vat", "111.82"),
                        ("pozycja_wartosc_brutto", "598.00"),
                        ("pozycja_liczba_jednostek", "1.00"),
                        ("stawka_vat", 8),
                        ("pozycja_lp", 1),
                        ("pozycja_zaliczka", False),
                    ]
                ),
            ],
            "faktura_data_zaplaty": "2016-11-16",
            "faktura_nip_nabywcy": "100-200-301",
            "faktura_data_wystawienia": "2016-11-16",
            "faktura_nazwa_sprzedawcy": "Nazwa sprzedawcy",
            "faktura_miasto_nabywcy": "Kraków",
            "faktura_kod_pocztowy_nabywcy": "53-312",
            "faktura_nabywca": [
                ("klient", "Piotr Nowak"),
                ("klient_adres", "ul. Złota 18a/5"),
                ("klient_adres_kopertowy", "adres"),
                ("klient_nip", "100-200-301"),
                ("klient_aktywny", True),
                ("klient_email", "<EMAIL>"),
                ("klient_faktura_co_miesiac", False),
                ("klient_miasto", "Kraków"),
                ("klient_konto", ""),
                ("klient_kod_pocztowy", "53-312"),
                ("klient_nazwa", "Piotr Nowak"),
                ("klient_szkoleniowy", True),
                ("klient_wystawca", False),
                ("klient_wysylac_maile", False),
                ("klient_komentarz", "Stworzony przez API"),
            ],
            "faktura_termin_platnosci": "2016-11-16",
            "faktura_wystawiona": True,
            "faktura_numer_konta": "123456789",
            "faktura_nazwa_nabywcy": "Piotr Nowak",
            "status_faktury": 4,
            "faktura_adres_sprzedawcy": "ul. Złota 343",
            "faktura_miasto_sprzedawcy": "Kraków",
            "faktura_typ_daty": 2,
            "faktura_nip_sprzedawcy": "1-1-1-1-1-1",
        }

    def test_create_from_zliczacz(self):
        obj = FakturaWysylka.objects.create_from_zliczacz(self.get_api_data())

        self.assertEqual(obj.faktura_id, 7)
        self.assertEqual(obj.faktura_numer, "1/1/2")
        self.assertEqual(obj.faktura_uwagi, "")
        self.assertEqual(obj.recipient_name, "Piotr Nowak")
        self.assertEqual(obj.recipient_address, "ul. Złota 18a/5")
        self.assertEqual(obj.recipient_post_code, "53-312")
        self.assertEqual(obj.recipient_city, "Kraków")
        self.assertIsNone(obj.uczestnik)

    def test_create_from_zliczacz_with_uczestnik(self):
        uczestnik = UczestnikFactory.create(nr_faktury="1/1/2", zliczacz_faktura_no=7)

        obj = FakturaWysylka.objects.create_from_zliczacz(self.get_api_data())
        self.assertEqual(obj.uczestnik, uczestnik)

    def test_osoba_do_kontaktu(self):
        UczestnikFactory.create(
            nr_faktury="1/1/2", zliczacz_faktura_no=7, osoba_do_kontaktu="Witold W."
        )

        obj = FakturaWysylka.objects.create_from_zliczacz(self.get_api_data())
        self.assertEqual(obj.recipient_name, "Witold W., Piotr Nowak")

    def test_callback_url(self):
        obj = FakturaWysylka.objects.create_from_zliczacz(self.get_api_data())

        self.assertEqual(
            obj.callback_url,
            "https://www.alx.dev/postivo/callback/{}/".format(obj.key.hex),
        )

    def test_has_required_data(self):
        obj = FakturaWysylka.objects.create_from_zliczacz(self.get_api_data())

        self.assertTrue(obj.has_required_data())

        obj.recipient_name = ""
        obj.save()

        self.assertFalse(obj.has_required_data())

    def test_form_brak_zliczacz_id(self):
        form_data = {}

        form = FakturaWysylkaAddAdminForm(form_data)

        self.assertFalse(form.is_valid())
        self.assertEqual(form.errors["zliczacz_id"], ["To pole jest wymagane."])

    @patch("www.forms.check_invoice_data")
    def test_form_faktura_juz_istnieje(self, mock_api):
        mock_api.return_value = {"faktura_id": 123}, None
        form_data = {"zliczacz_id": 123}

        FakturaWysylkaFactory.create(faktura_id=123)

        form = FakturaWysylkaAddAdminForm(form_data)

        self.assertFalse(form.is_valid())
        self.assertEqual(form.errors["zliczacz_id"], ["Ta faktura została już dodana."])

    @patch("www.forms.check_invoice_data")
    def test_form_ok(self, mock_api):
        mock_api.return_value = {"faktura_id": 123}, None
        form_data = {"zliczacz_id": 123}

        form = FakturaWysylkaAddAdminForm(form_data)
        self.assertTrue(form.is_valid())


###################
# Korekty
###################


class FakturaKorektaTestCase(ALXTestCase):
    @staticmethod
    def get_api_data():
        return {
            "faktura_ostatnia_zmiana": "2016-11-16T19:58:08.582000",
            "faktura_sprzedawca": 37,
            "faktura_uwagi": "",
            "faktura_id": 7,
            "faktura_miejsce_wystawienia": "Warszawa",
            "faktura_kod_pocztowy_sprzedawcy": "02-298",
            "faktura_numer": "1/1/2",
            "faktura_data_sprzedazy": "2016-11-16",
            "faktura_adres_nabywcy": "ul. Złota 18a/5",
            "typ_faktury": 1,
            "pozycje_faktury": [
                dict(
                    [
                        ("pozycja_opis", "Opis pozycji"),
                        ("pozycja_cena_jednostkowa_netto", "486.18"),
                        ("pozycja_wartosc_netto", "486.18"),
                        ("pozycja_kwota_vat", "111.82"),
                        ("pozycja_wartosc_brutto", "598.00"),
                        ("pozycja_liczba_jednostek", "1.00"),
                        ("stawka_vat", 8),
                        ("pozycja_lp", 1),
                        ("pozycja_zaliczka", False),
                    ]
                ),
            ],
            "faktura_data_zaplaty": "2016-11-16",
            "faktura_nip_nabywcy": "100-200-301",
            "faktura_data_wystawienia": "2016-11-16",
            "faktura_nazwa_sprzedawcy": "Nazwa sprzedawcy",
            "faktura_miasto_nabywcy": "Kraków",
            "faktura_kod_pocztowy_nabywcy": "53-312",
            "faktura_nabywca": [
                ("klient", "Piotr Nowak"),
                ("klient_adres", "ul. Złota 18a/5"),
                ("klient_adres_kopertowy", "adres"),
                ("klient_nip", "100-200-301"),
                ("klient_aktywny", True),
                ("klient_email", "<EMAIL>"),
                ("klient_faktura_co_miesiac", False),
                ("klient_miasto", "Kraków"),
                ("klient_konto", ""),
                ("klient_kod_pocztowy", "53-312"),
                ("klient_nazwa", "Piotr Nowak"),
                ("klient_szkoleniowy", True),
                ("klient_wystawca", False),
                ("klient_wysylac_maile", False),
                ("klient_komentarz", "Stworzony przez API"),
            ],
            "faktura_termin_platnosci": "2016-11-16",
            "faktura_wystawiona": True,
            "faktura_numer_konta": "123456789",
            "faktura_nazwa_nabywcy": "Piotr Nowak",
            "status_faktury": 4,
            "faktura_adres_sprzedawcy": "ul. Złota 343",
            "faktura_miasto_sprzedawcy": "Kraków",
            "faktura_typ_daty": 2,
            "faktura_nip_sprzedawcy": "1-1-1-1-1-1",
        }

    def test_get_faktura_numer_korekty(self):
        obj = FakturaKorekta.objects.create_from_zliczacz(self.get_api_data())
        self.assertTrue(obj.faktura_numer_korekty)
        self.assertEqual(obj.faktura_numer_korekty, obj.get_faktura_numer_korekty())

        dt = datetime.datetime(2016, 1, 1).date()

        obj.faktura_data_wystawienia_korekty = dt

        self.assertEqual(obj.get_faktura_numer_korekty(), "1/01/16/K")

    def test_get_faktura_kolejny_numer_korekty(self):
        dt = datetime.date.today()

        no = FakturaKorekta.objects.get_faktura_kolejny_numer_korekty(
            month=dt.month, year=dt.year
        )
        self.assertEqual(no, 1)

        for i in range(10):
            FakturaKorekta.objects.create_from_zliczacz(self.get_api_data())

        no = FakturaKorekta.objects.get_faktura_kolejny_numer_korekty(
            month=dt.month, year=dt.year
        )
        self.assertEqual(no, 11)

        # Bierzemy fakture z numerem 10 i przepisujemy do innego miesiaca
        obj = FakturaKorekta.objects.get(faktura_kolejny_numer_korekty=10)
        obj.faktura_data_wystawienia_korekty = datetime.datetime(2015, 1, 1).date()
        obj.save()
        self.assertEqual(obj.faktura_numer_korekty, "10/01/15/K")

        # W takim razie kolejny numer faktury w tym mc bedzie znowu 10
        no = FakturaKorekta.objects.get_faktura_kolejny_numer_korekty(
            month=dt.month, year=dt.year
        )
        self.assertEqual(no, 10)

        # Kolejny numer faktury w 1-2015 bedzie 11
        no = FakturaKorekta.objects.get_faktura_kolejny_numer_korekty(
            month=datetime.datetime(2015, 1, 1).date().month,
            year=datetime.datetime(2015, 1, 1).date().year,
        )
        self.assertEqual(no, 11)

        obj = FakturaKorekta.objects.get(faktura_kolejny_numer_korekty=9)
        obj.faktura_kolejny_numer_korekty = 130
        obj.save()

        no = FakturaKorekta.objects.get_faktura_kolejny_numer_korekty(
            month=dt.month, year=dt.year
        )
        self.assertEqual(no, 131)

    def test_create_from_zliczacz(self):
        obj = FakturaKorekta.objects.create_from_zliczacz(
            self.get_api_data(), make_defaults=True
        )

        self.assertEqual(obj.fakturakorektapozycjaoryginalna_set.count(), 1)
        self.assertEqual(obj.fakturakorektapozycjakorygujaca_set.count(), 1)

        pos = obj.fakturakorektapozycjakorygujaca_set.all()[0]

        self.assertEqual(pos.pozycja_cena_jednostkowa_netto, 0)
        self.assertEqual(pos.pozycja_wartosc_netto, 0)
        self.assertEqual(pos.pozycja_kwota_vat, 0)
        self.assertEqual(pos.pozycja_wartosc_brutto, 0)

        obj = FakturaKorekta.objects.create_from_zliczacz(
            self.get_api_data(), make_defaults=False
        )

        self.assertEqual(obj.fakturakorektapozycjaoryginalna_set.count(), 1)
        self.assertEqual(obj.fakturakorektapozycjakorygujaca_set.count(), 0)

    def test_set_cena_bilans(self):
        obj = FakturaKorekta.objects.create_from_zliczacz(
            self.get_api_data(), make_defaults=True
        )
        self.assertEqual(obj.cena_bilans, -598)

        pos = obj.fakturakorektapozycjakorygujaca_set.all()[0]
        pos.pozycja_wartosc_brutto = 600
        pos.save()

        obj.set_cena_bilans()
        self.assertEqual(obj.cena_bilans, 2)

        obj.status_faktury = 2
        obj.set_cena_bilans()

        self.assertEqual(obj.cena_bilans, 600)

        pos.pozycja_wartosc_brutto = 0
        pos.save()
        obj.set_cena_bilans()

        self.assertEqual(obj.cena_bilans, 0)

    def test_pole_uwagi(self):
        data = self.get_api_data()
        data["uczestnik"] = UczestnikFactory.create(uwagi="abc")
        obj = FakturaKorekta.objects.create_from_zliczacz(data)

        uczestnik = Uczestnik.objects.get(pk=data["uczestnik"].pk)
        self.assertEqual(
            uczestnik.uwagi,
            "abc\n\n" "Korekta numer: {}".format(obj.faktura_numer_korekty),
        )


###################
# Powiadomienia
###################


class UserNotificationBaseTestCase(ALXTestCase):
    def _drop_objects(self):
        UserNotificationLog.objects.all().delete()
        UserNotification.objects.all().delete()


class UserNotificationTransactionTestCase(UserNotificationBaseTestCase):
    def test_user_notification_uniqueness(self):
        # Identyczny adres email
        UserNotificationFactory.create(email="<EMAIL>")
        self.assertRaises(
            IntegrityError, UserNotificationFactory.create, email="<EMAIL>"
        )

    def test_user_notification_uniqueness_camel_case(self):
        # Camel case w adresie email
        UserNotificationFactory.create(email="<EMAIL>")
        self.assertRaises(
            IntegrityError, UserNotificationFactory.create, email="<EMAIL>"
        )

    def test_user_notification_email_normalized(self):
        # Adres email musi byc zawsze normalizowany do malych liter
        e = UserNotificationFactory.create(email="<EMAIL>")
        self.assertEqual(e.email, "<EMAIL>")

    def test_user_courses_notification_uniqueness(self):
        # Identyczny użytkownik oraz szkolenie

        user = UserNotificationFactory()
        training = SzkolenieFactory()

        UserCoursesNotificationFactory.create(user=user, training=training)
        self.assertRaises(
            IntegrityError,
            UserCoursesNotificationFactory.create,
            user=user,
            training=training,
        )

    def test_user_notification_log_uniqueness(self):
        # Identyczny użytkownik, termin, lokalizacja i status

        user = UserNotificationFactory()
        term = TerminSzkoleniaFactory()
        location = LokalizacjaFactory()

        UserNotificationLogFactory.create(
            user=user, term=term, location=location, notification_type="term_created"
        )

        self.assertRaises(
            IntegrityError,
            UserNotificationLogFactory.create,
            user=user,
            term=term,
            location=location,
            notification_type="term_created",
        )

    def test_user_notification_log_queries_count(self):
        # Nadpisana metoda `get_query_set` powinna automatycznie pobierać dane
        # z relacji.

        user = UserNotificationFactory()
        term = TerminSzkoleniaFactory()
        location = LokalizacjaFactory()

        log = UserNotificationLogFactory.create(
            user=user, term=term, location=location, notification_type="term_created"
        )

        with self.assertNumQueries(1):
            obj = UserNotificationLog.objects.get(pk=log.pk)

            # Odnieś się do danych z relacji
            obj.term
            obj.location


class UserNotificationTestCase(UserNotificationBaseTestCase):
    def test_is_system_source_is_system(self):
        user = UserNotificationFactory(source="system")
        self.assertTrue(user.is_system())

    def test_is_system_source_is_www(self):
        user = UserNotificationFactory(source="www")
        self.assertFalse(user.is_system())

    def test_is_system_source_is_staff(self):
        user = UserNotificationFactory(source="staff")
        self.assertFalse(user.is_system())

    def test_manager_potencjalnie_zainteresowani_z_innych_miast(self):
        # Tworzymy dane poczatkowe
        user1 = UserNotificationFactory()
        user2 = UserNotificationFactory()
        user3 = UserNotificationFactory()
        s = SzkolenieFactory()

        locations = list(LokalizacjaFactory.create_batch(5))
        term = TerminSzkoleniaFactory(szkolenie=s, lokalizacja=locations[0])

        users = UserNotification.objects.potencjalnie_zainteresowani_z_innych_miast(
            term
        )
        self.assertFalse(users)

        UserCoursesNotificationFactory(
            user=user1,
            locations=locations,
            training=s,
        )
        UserCoursesNotificationFactory(
            user=user2,
            locations=locations[1:],
            training=s,
        )
        UserCoursesNotificationFactory(
            user=user3,
            locations=locations[3:],
            training=s,
        )
        UserCoursesNotificationFactory(user=user3, locations=locations[3:])
        UserCoursesNotificationFactory(user=user3, locations=[locations[0]])
        UserCoursesNotificationFactory(user=user3, locations=locations)
        UserCoursesNotificationFactory(
            locations=[locations[0]],
            training=s,
        )
        UserCoursesNotificationFactory.create_batch(5)
        UserCoursesNotificationFactory.create_batch(5, locations=[locations[0]])
        UserCoursesNotificationFactory.create_batch(5, locations=locations)
        UserCoursesNotificationFactory.create_batch(5, locations=locations[2:])

        users = UserNotification.objects.potencjalnie_zainteresowani_z_innych_miast(
            term
        )
        self.assertEqual(len(users), 2)
        self.assertTrue(user2 in users and user3 in users)

        UserCoursesNotificationFactory.create_batch(15, training=s)
        users = UserNotification.objects.potencjalnie_zainteresowani_z_innych_miast(
            term
        )
        self.assertEqual(len(users), 17)

        UserNotificationLogFactory.create(term=term, user=user3)
        UserNotificationLogFactory.create_batch(10, user=user2)

        users = UserNotification.objects.potencjalnie_zainteresowani_z_innych_miast(
            term
        )
        self.assertEqual(len(users), 16)

    def test_manager_get_for_notifications(self):
        """
        Funkcja testuje metodę managera TerminSzkoleniaManager -
        `get_for_notifications`, która zwraca terminy filtrowane pod kątem
        danego użytkownika.
        """

        now = datetime.datetime.now()
        two_hours_earlier = now - datetime.timedelta(hours=2)
        tommorow = (now + datetime.timedelta(hours=24)).date()

        # Tworzymy dane poczatkowe
        user = UserNotificationFactory()
        t1 = SzkolenieFactory()
        t2 = SzkolenieFactory()

        locations = []
        for i in range(5):
            locations.append(LokalizacjaFactory())
        locations_ids = [l.pk for l in locations]

        term = TerminSzkoleniaFactory(szkolenie=t1, lokalizacja=locations[0])

        # Stworzyliśmy użytkownika, który zapisał się na powiadomienia
        # dotyczące szkoleń `t1` oraz `t2` w lokalizacjach `locations`.

        UserCoursesNotificationFactory(
            user=user,
            locations=locations,
            training=t1,
        )
        UserCoursesNotificationFactory(
            user=user,
            locations=locations,
            training=t2,
        )

        objects = TerminSzkolenia.objects

        # Pobieramy dane dla:
        #  - szkolenia `t1`
        #  - lokalizacji `locations_ids`
        #  - daty utworzenia min. 2 godziny temu - `two_hours_earlier`
        #  - terminu rozpoczęcia min. jutro - `tommorow`
        #  - oraz ostatniej wysyłki do uzytkownika - `last_action_date`

        # Załóżmy, że użytkownik ostatnią wysyłkę miał 24 godziny temu.
        last_action_date = now - datetime.timedelta(hours=24)

        kwargs = {
            "training_id": t1.pk,
            "locations_ids": locations_ids,
            "created_at": two_hours_earlier,
            "term_date": tommorow,
            "last_action_date": last_action_date,
        }

        objs = objects.get_for_notifications(**kwargs)

        # Powinniśmy dostać 0 wyników, gdyż, termin został dodany mniej jak
        # 2 godziny temu.
        self.assertEqual(objs.count(), 0)

        # Zmieńmy datę utworzenia terminu szkolenia, aby odpowiadała naszym
        # kryteriom (np. 5 godzin wstecz).
        term.created_at = now - datetime.timedelta(hours=5)
        term.save()

        objs = objects.get_for_notifications(**kwargs)
        # Powinniśmy dostać 1 wynik, gdyż, termin został dodany później jak
        # 2 godziny temu oraz ostatnia notyfikacja użytkownika jest
        # starsza jak data utworzenia.
        self.assertEqual(objs.count(), 1)
        self.assertEqual(objs[0].pk, term.pk)

        # # Teraz ustawmy datę utworzenia szkolenia starszą niż data
        # # ostatniego powiadomienia - w tym wypadku powinniśmy dostać 0 wyników,
        # # zakładamy zatem, że użytkownik dostał notyfikację wcześniej.
        # term.created_at = now - datetime.timedelta(hours=25)
        # term.save()
        #
        # objs = objects.get_for_notifications(**kwargs)
        # self.assertEqual(objs.count(), 0)

        # Teraz ustawmy datę uruchomienia terminu na młodszą niż ostatnie
        # powiadomienie użytkownika (np. 1h temu). Powinniśmy dostać obiekt
        # terminu.
        term.jobs_state = now - datetime.timedelta(hours=1)
        term.save()

        objs = objects.get_for_notifications(**kwargs)

        self.assertEqual(objs.count(), 1)
        self.assertEqual(objs[0].pk, term.pk)

        # Teraz załóżmy, że termin odbywa się dzisiaj - dla dzisiejszych
        # terminów nie ma sensu wysyłać powiadomień.
        term.termin = now.date()
        term.save()

        objs = objects.get_for_notifications(**kwargs)
        self.assertEqual(objs.count(), 0)

        # Powróćmy do poprzedniej daty startu, ale oznaczmy szkolenie jako
        # zamknięte - dla zamknietych szkoleń nie wysyłamy powiadomień.
        term.termin = (now + datetime.timedelta(hours=72)).date()
        term.zamkniete = True
        term.save()

        objs = objects.get_for_notifications(**kwargs)
        self.assertEqual(objs.count(), 0)

        # Teraz symulujemy jakiś stary termin, który nie ma daty utworzenia
        # - takie terminy również omijamy - warunek: created_at__isnull=False
        term.zamkniete = False
        term.created_at = None
        term.save()

        objs = objects.get_for_notifications(**kwargs)
        self.assertEqual(objs.count(), 0)

        # Powracamy do warunków początkowych - powinniśmy dostać jeden obiket
        # terminu szkolenia.
        term.created_at = now - datetime.timedelta(hours=25)
        term.save()

        objs = objects.get_for_notifications(**kwargs)

        self.assertEqual(objs.count(), 1)
        self.assertEqual(objs[0].pk, term.pk)

        # Zmieńmy lokalizację szkolenia (która nie jest w zainteresowaniach
        # użytkownika). Powinniśmy dostać zero obiektów.
        term.lokalizacja = LokalizacjaFactory()
        term.save()

        objs = objects.get_for_notifications(**kwargs)
        self.assertEqual(objs.count(), 0)

    def test_manager_ready_for_notifications(self):
        """
        Funkcja testuje metodę managera UserNotificationManager -
        `ready_for_notifications`, która zwraca użytkowników nadających się
        do notyfikacji w danym czasie.
        """

        now = datetime.datetime.now()

        # Tworzymy dane początkowe
        user = UserNotificationFactory()

        self.assertFalse(user.last_email_sent_at)
        self.assertFalse(user.activation_at)
        self.assertFalse(user.activation_email_sent_at)

        objects = UserNotification.objects

        # Pobieramy użytkowników, których czas ostatniej akcji, jest
        # późniejszy niż 24 godziny - powinniśmy dostać 0 rekordów.
        objs = objects.ready_for_notifications(sending_interval=24)
        self.assertEqual(objs.count(), 0)

        # Zmieńmy użytkownikowi datę utworzenia na starszą niż 24 godziny -
        # powinnismy dostac jeden rekord.
        user.created_at = now - datetime.timedelta(hours=25)
        user.save()

        objs = objects.ready_for_notifications(sending_interval=24)
        self.assertEqual(objs.count(), 1)
        self.assertEqual(objs[0].pk, user.pk)

        # Powróćmy do starej daty, a ustawmy datę ostatniej wysyłki na
        # późniejszą niż 24 godziny. Powinniśmy dostać 1 rekord.
        user.created_at = now
        user.last_email_sent_at = now - datetime.timedelta(hours=25)
        user.save()

        objs = objects.ready_for_notifications(sending_interval=24)
        self.assertEqual(objs.count(), 1)
        self.assertEqual(objs[0].pk, user.pk)

        # Ustawiamy `send_notifications` na False. Powinniśmy dostać 0
        # rekordów.
        user.send_notifications = False
        user.save()

        objs = objects.ready_for_notifications(sending_interval=24)
        self.assertEqual(objs.count(), 0)

        # Teraz data ostatniej wysyłki jest mniejsza niż 24 godziny.
        # Powinniśmy dostać 0 rekordów.
        user.ready_for_notifications = True
        user.last_email_sent_at = now - datetime.timedelta(hours=20)
        user.save()

        objs = objects.ready_for_notifications(sending_interval=24)
        self.assertEqual(objs.count(), 0)

    def test_manager_get_created_by_system(self):
        """
        Funkcja testuje metodę managera UserNotificationManager -
        `get_created_by_system`, która zwraca użytkowników stworzonych
        przez automat.
        """

        # Tworzymy dane początkowe
        user = UserNotificationFactory.create(source="system")
        UserCoursesNotificationFactory.create(user=user, source="system")

        objects = UserNotification.objects

        # Pobieramy użytkowników - 1 wynik.
        objs = objects.get_created_by_system()
        self.assertEqual(objs.count(), 1)

        # Dodajemy drugą preferencję - 1 wynik
        UserCoursesNotificationFactory.create(user=user, source="system")

        objs = objects.get_created_by_system()
        self.assertEqual(objs.count(), 1)

        # Dodajemy trzecią preferencję, która nie pasuje statusem -
        # brak wyników
        UserCoursesNotificationFactory.create(user=user, source="www")

        objs = objects.get_created_by_system()
        self.assertEqual(objs.count(), 0)

        # Tworzymy 12 użytkowników, którzy pasują i 8, którzy nie - rezultat 12

        for i in range(10):
            user = UserNotificationFactory.create(source="system")

            for i in range(8):
                UserCoursesNotificationFactory.create(user=user, source="system")

        for i in range(2):
            UserNotificationFactory.create(source="system")

        for i in range(4):
            user = UserNotificationFactory.create(source="system")

            for i in range(3):
                UserCoursesNotificationFactory.create(user=user, source="system")
            for i in range(3):
                UserCoursesNotificationFactory.create(user=user, source="www")

        for i in range(4):
            user = UserNotificationFactory.create(source="www")

            for i in range(3):
                UserCoursesNotificationFactory.create(user=user, source="staff")

        objs = objects.get_created_by_system()
        self.assertEqual(objs.count(), 12)

    def test_manager_get_active(self):
        """
        Funkcja testuje metodę managera UserNotificationManager - `get_active`,
        która zwraca aktywnych użytkowników.
        """

        # Tworzymy dane początkowe
        UserNotificationFactory.create(source="system")

        objects = UserNotification.objects

        # Pobieramy użytkowników - 1 wynik.
        objs = objects.get_active()
        self.assertEqual(objs.count(), 1)

        # Dodajemy drugiego uczestnika, ale wypisanego - dalej dostajemy 1
        # obiekt
        UserNotificationFactory.create(status=2)

        objs = objects.get_active()
        self.assertEqual(objs.count(), 1)

        # Dodajemy 10 aktywnych użytkowników
        UserNotificationFactory.create_batch(10)

        objs = objects.get_active()
        self.assertEqual(objs.count(), 11)


class UserCoursesNotificationManagerTestCase(UserNotificationBaseTestCase):
    def test_manager_reports_query(self):
        """
        Funkcja testuje metodę managera UserCoursesNotificationManager -
        `reports_query`, która zwraca raport z zapisow na poszczegolne
        szkolenia.
        """

        # Brak zapisow - nic nie zwracamy

        objects = UserCoursesNotification.objects

        qs = list(objects.reports_query([]))

        self.assertEqual(qs, [])

        # Dodajemy dwóch aktywnych użytkowników:
        #  - jeden dopisany do jednego skzolenia i jednej lokalizacji
        #  - drugi dopisany do 2 szkoleń i 2 lokalizacji
        # szkolenia i jednej lokalizacji.

        l1 = LokalizacjaFactory.create()
        l2 = LokalizacjaFactory.create()

        c1 = UserCoursesNotificationFactory.create(locations=[l1])
        c2 = UserCoursesNotificationFactory.create(locations=[l2])
        c3 = UserCoursesNotificationFactory.create(
            training=c1.training, locations=[l1, l2]
        )

        qs = list(objects.reports_query([l1, l2]))

        # Powinniśmy dostać dwa rekordy
        self.assertEqual(len(qs), 2)

        # W pierwszym szkolenie z c1.training
        self.assertEqual(int(qs[0]["training"]), c1.training.pk)

        # Liczba zapisanych - 2
        self.assertEqual(qs[0]["total_users"], 2)

        # Liczba zapisanych w lokalizacjach - 3
        self.assertEqual(qs[0]["total_users_in_locations"], 3)

        # Liczba zapisanych w lokalizacji l1 - 2 i l1 - 1
        self.assertEqual(qs[0][str(l1.pk)], 2)
        self.assertEqual(qs[0][str(l2.pk)], 1)

        # W przypadku drugiego rekordu, wszystkie statystyki to 1 i 0
        self.assertEqual(int(qs[1]["training"]), c2.training.pk)
        self.assertEqual(qs[1]["total_users"], 1)
        self.assertEqual(qs[1]["total_users_in_locations"], 1)
        self.assertEqual(qs[1][str(l1.pk)], 0)
        self.assertEqual(qs[1][str(l2.pk)], 1)

        # Teraz użytkownika c1.user i c3.user oznaczamy jako nieaktywnych i
        # filtrujemy tylko po aktywnych.
        c1.user.status = 0
        c1.user.save()

        c3.user.status = 2
        c3.user.save()

        qs = list(objects.reports_query([l1, l2], user__status=1))

        # Powinniśmy dostać jeden rekord
        self.assertEqual(len(qs), 1)

        self.assertEqual(int(qs[0]["training"]), c2.training.pk)
        self.assertEqual(qs[0]["total_users"], 1)
        self.assertEqual(qs[0]["total_users_in_locations"], 1)
        self.assertEqual(qs[0][str(l1.pk)], 0)
        self.assertEqual(qs[0][str(l2.pk)], 1)

        # Czyścimy bazę
        self._drop_objects()

        # Dodajemy dwóch aktywnych użytkowników zapisanych na te same
        # szkolenia i do tych samych miast, ale jeden niekatywny.

        l1 = LokalizacjaFactory.create()
        l2 = LokalizacjaFactory.create()

        c1 = UserCoursesNotificationFactory.create(locations=[l1, l2])
        c2 = UserCoursesNotificationFactory.create(
            training=c1.training, locations=[l1, l2]
        )

        c2.user.status = 0
        c2.user.save()

        qs = list(objects.reports_query([l1, l2], user__status=1))

        # Powinniśmy dostać jeden rekord
        self.assertEqual(len(qs), 1)

        self.assertEqual(int(qs[0]["training"]), c1.training.pk)
        self.assertEqual(qs[0]["total_users"], 1)
        self.assertEqual(qs[0]["total_users_in_locations"], 2)
        self.assertEqual(qs[0][str(l1.pk)], 1)
        self.assertEqual(qs[0][str(l2.pk)], 1)

    def test_manager_stats_query(self):
        """
        Funkcja testuje metodę managera UserCoursesNotificationManager -
        `stats_query`, która zwraca statystyki zapisów na dane miasto
        pogrupowane po miesiącach.
        """

        # Brak zapisow - nic nie zwracamy

        objects = UserCoursesNotification.objects

        qs = list(objects.stats_query([]))

        self.assertEqual(qs, [])

        # Dodajemy dwóch aktywnych użytkowników:
        #  - jeden dopisany do jednego skzolenia i jednej lokalizacji
        #  - drugi dopisany do 2 szkoleń i 2 lokalizacji
        # szkolenia i jednej lokalizacji.

        created_at = datetime.datetime.now()

        l1 = LokalizacjaFactory.create()
        l2 = LokalizacjaFactory.create()

        c1 = UserCoursesNotificationFactory.create(locations=[l1])
        c2 = UserCoursesNotificationFactory.create(locations=[l2])
        c3 = UserCoursesNotificationFactory.create(
            training=c1.training, locations=[l1, l2]
        )

        for c in [c1, c2, c3]:
            c.created_at = created_at
            c.save()

        qs = list(objects.stats_query([l1, l2]))

        # Powinniśmy dostać jeden rekord
        self.assertEqual(len(qs), 1)

        # Data
        self.assertEqual(
            qs[0]["month"], datetime.datetime(created_at.year, created_at.month, 1)
        )

        # Liczba zapisanych w lokalizacji l1 - 2 i l1 - 2
        self.assertEqual(qs[0][str(l1.pk)], 2)
        self.assertEqual(qs[0][str(l2.pk)], 2)

        # Teraz użytkownika c1.user i c3.user oznaczamy jako nieaktywnych i
        # filtrujemy tylko po aktywnych.
        c1.user.status = 0
        c1.user.save()

        c3.user.status = 2
        c3.user.save()

        qs = list(objects.stats_query([l1, l2], user__status=1))

        # Powinniśmy dostać jeden rekord
        self.assertEqual(len(qs), 1)

        # Data
        self.assertEqual(
            qs[0]["month"], datetime.datetime(created_at.year, created_at.month, 1)
        )

        # Liczba zapisanych w lokalizacji l1 - 0 i l1 - 1
        self.assertEqual(qs[0][str(l1.pk)], 0)
        self.assertEqual(qs[0][str(l2.pk)], 1)

        # Czyścimy bazę
        self._drop_objects()

        # Teraz dodajemy obiekty w różnych datach
        l1 = LokalizacjaFactory.create()
        l2 = LokalizacjaFactory.create()

        c1 = UserCoursesNotificationFactory.create(locations=[l1])
        c2 = UserCoursesNotificationFactory.create(locations=[l1, l2])
        c3 = UserCoursesNotificationFactory.create(
            training=c1.training, locations=[l1, l2]
        )

        c1.created_at = datetime.datetime(2015, 1, 23)
        c1.save()

        c2.created_at = datetime.datetime(2015, 1, 11)
        c2.save()

        c3.created_at = datetime.datetime(2015, 3, 3)
        c3.save()

        qs = list(objects.stats_query([l1, l2], user__status=1))

        # Powinniśmy dostać dwa rekordy
        self.assertEqual(len(qs), 2)

        # Daty
        self.assertEqual(qs[0]["month"], datetime.datetime(2015, 1, 1))
        self.assertEqual(qs[1]["month"], datetime.datetime(2015, 3, 1))

        # Liczba zapisanych w lokalizacjach
        self.assertEqual(qs[0][str(l1.pk)], 2)
        self.assertEqual(qs[0][str(l2.pk)], 1)

        self.assertEqual(qs[1][str(l1.pk)], 1)
        self.assertEqual(qs[1][str(l2.pk)], 1)


@override_settings(
    USER_NOTIFICATION_SENDING_INTERVAL_HOURS=48,
    DOMENY_DLA_JEZYKOW={"pl": "www.alx.dev", "en": "www.alx-training.co.dev"},
)
class UserNotificationManagementTestCase(UserNotificationBaseTestCase):
    def _test_email_body(
        self,
        body,
        is_created,
        is_started,
        terms,
        language,
        user,
        has_kurs_zawodowy_general_info=False,
        before_start=False,
        has_kurs_zawodowy_specific_info=False,
        has_suggest_schedule_specific_info=False,
        has_suggest_schedule_general_info=False,
    ):
        # Testujemy zawartość email z powiadomieniem, sprawdzając nagłówki
        # oraz szczegóły terminów.

        assert language in ("pl", "en")

        if is_created:
            if language == "pl":
                self.assertIn("Terminy utworzone", body)
            elif language == "en":
                self.assertIn("New course dates added", body)

        if is_started:
            if language == "pl":
                self.assertIn("Terminy potwierdzone", body)
            elif language == "en":
                self.assertIn("Confirmed courses", body)

        if before_start:
            if language == "pl":
                self.assertIn(
                    "Terminy, którym do zebrania się grupy brakuje "
                    "jednego zapisu (Twojego!)",
                    body,
                )
            elif language == "en":
                self.assertIn(
                    "Course dates to be confirmed once one more person joins",
                    body,
                )

        if any([t.lokalizacja.zdalna for t in terms]):
            if language == "pl":
                self.assertIn(
                    "Nasze zajęcia zdalne wyglądają identycznie jak szkolenia realizowane w standardowym trybie",
                    body,
                )
            

        # http lub https
        protocol = settings.FORCE_SSL and "https://" or "http://"

        # Sprawdź zawartość terminów
        for term in terms:
            self.assertIn(term.szkolenie.nazwa, body)
            if language == "pl":
                term_url = reverse(
                    "detail_pl",
                    kwargs={
                        "slug": term.szkolenie.slug,
                        "language": language,
                    },
                )
                url = "{0}{1}{2}".format(
                    protocol, settings.DOMENY_DLA_JEZYKOW[language], term_url
                )

                self.assertIn(url, body)
                self.assertIn(term.lokalizacja.fullname_miejscownik, body)

                if term.is_kurs():
                    self.assertIn("(tryb: {0})".format(term.get_tryb_display()), body)
            else:
                term_url = reverse(
                    "detail_en",
                    kwargs={
                        "slug": term.szkolenie.slug,
                        "language": language,
                    },
                )
                url = "{0}{1}{2}".format(
                    protocol, settings.DOMENY_DLA_JEZYKOW[language], term_url
                )
                self.assertIn(url, body)
                self.assertIn(term.lokalizacja.shortname, body)

                if term.is_kurs():
                    self.assertIn(
                        "(mode: {0})".format(term.get_tryb_display_en()), body
                    )

        # Sprawdź informacje o kursie zawodowym.
        if has_kurs_zawodowy_general_info:
            if language == "pl":
                self.assertIn(
                    "Dokładne harmonogramy zajęć kursów znajdują się na "
                    'stronie kursu w zakładce "Tryby i harmonogramy".',
                    body,
                )
            else:
                self.assertIn(
                    "The exact schedules of the courses can be found",
                    body,
                )

        if has_kurs_zawodowy_specific_info:
            if language == "pl":
                term_url = "{0}#terminy".format(
                    reverse(
                        "detail_pl",
                        kwargs={
                            "slug": terms[0].szkolenie.slug,
                            "language": language,
                        },
                    )
                )
                self.assertIn(term_url, body)
            else:
                term_url = "{0}#schedule".format(
                    reverse(
                        "detail_en",
                        kwargs={
                            "slug": terms[0].szkolenie.slug,
                            "language": language,
                        },
                    )
                )
                self.assertIn(term_url, body)

        # Sprawdź informacje o możliwości propozycji terminu
        if has_suggest_schedule_general_info:
            if language == "pl":
                self.assertIn(
                    "Jeśli wyznaczony termin szkolenia Ci nie odpowiada, "
                    "zaproponuj nam własny termin używając formularza na "
                    "stronie danego szkolenia.",
                    body,
                )
            else:
                self.assertIn(
                    "If the date of the course does not suit you, please feel free to suggest your own date",
                    body,
                )

        if has_suggest_schedule_specific_info:
            term_url = reverse(
                "zaproponuj_termin",
                kwargs={
                    "slug": terms[0].szkolenie.slug,
                    "language": language,
                },
            )
            self.assertIn(term_url, body)

        # Sprawdź link do anulowania subskrypcji
        url = reverse(
            "cancel_subscription",
            kwargs={
                "key": user.key.hex,
                "language": language,
            },
        )
        self.assertIn(url, body)

        # Sprawdź link do zarządzania subskrypcją
        url = reverse(
            "manage_subscriptions",
            kwargs={
                "key": user.key.hex,
                "language": language,
            },
        )
        self.assertIn(url, body)

    def test_all_empty_scenarios(self):
        """
        Funkcja sprawdzamy wszystkie przyadki, w których nie będzie
        powiadomień.
        """

        now = datetime.datetime.now()

        # Tworzymy dane początkowe
        user = UserNotificationFactory()
        t1 = SzkolenieFactory()
        t2 = SzkolenieFactory()

        locations = []
        for i in range(5):
            locations.append(LokalizacjaFactory())

        term = TerminSzkoleniaFactory(szkolenie=t1, lokalizacja=locations[0])

        # Stworzyliśmy użytkownika, który zapisał się na powiadomienia
        # dotyczące szkoleń `t1` oraz `t2` w lokalizacjach `locations`.

        UserCoursesNotificationFactory(
            user=user,
            locations=locations,
            training=t1,
        )
        UserCoursesNotificationFactory(
            user=user,
            locations=locations,
            training=t2,
        )

        logs_objects = UserNotificationLog.objects

        # Liczba wysłanych maili oraz logów powinna wynieść 0
        cmd = user_notifications.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 0)
        self.assertEqual(logs_objects.count(), 0)

        # Zmieniamy datę utworzenia terminu na więcej ja 2 godziny temu,
        # ale powiadomienie dalej powinno być równe zero (gdyż ostatnia data
        # akcji użytkownika jest mniejsza jak dwa dni)
        term.created_at = now - datetime.timedelta(hours=3)
        term.save()

        cmd = user_notifications.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 0)
        self.assertEqual(logs_objects.count(), 0)

        # Zmieniamy datę ostatniej notyfikacji użytkownika, ale podmieniamy
        # lokalizację szkolenia, która nie jest w preferencjach użytkonika.
        user.created_at = now - datetime.timedelta(hours=72)
        user.save()

        term.lokalizacja = LokalizacjaFactory()
        term.save()

        cmd = user_notifications.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 0)
        self.assertEqual(logs_objects.count(), 0)

        # Powracamy do starej lokalizacji, ale dodajemy log, informujący o
        # wysłanym powiadomieniu do tego użytkownika.
        term.lokalizacja = locations[0]
        term.save()

        log = UserNotificationLogFactory(
            user=user,
            term=term,
            location=locations[0],
            notification_type="term_created",
        )

        cmd = user_notifications.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 0)
        self.assertEqual(logs_objects.count(), 1)

        # Usuwamy log, ale dodajemy użytkownika jako Uczestnika przyszłego
        # szkolenia.
        log.delete()

        UczestnikFactory.create(
            email=user.email,
            termin=TerminSzkoleniaFactory(szkolenie=t1),
        )

        cmd = user_notifications.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 0)
        self.assertEqual(logs_objects.count(), 0)

    def test_seven_days_earlier(self):
        """
        Sprawdzamy filtrowanie terminów na podstawie `termin_zakonczenia`
        lub daty zakończenia terminu.
        """

        now = datetime.datetime.now()

        # Tworzymy dane początkowe
        user = UserNotificationFactory()
        t1 = SzkolenieFactory()

        term = TerminSzkoleniaFactory(
            szkolenie=t1, lokalizacja=LokalizacjaFactory(), odbylo_sie=True
        )
        term2 = TerminSzkoleniaFactory(szkolenie=t1, lokalizacja=term.lokalizacja)
        term2.created_at = now - datetime.timedelta(hours=3)
        term2.save()

        UserCoursesNotificationFactory(
            user=user,
            locations=[term.lokalizacja],
            training=t1,
        )

        # Dodajemy użytkownika jako uczestnika przyszłego szkolenia
        UczestnikFactory.create(email=user.email, termin=term)

        # Zmieniamy datę ostatniej notyfikacji użytkownika, ale podmieniamy
        # lokalizację szkolenia, która nie jest w preferencjach użytkonika.
        user.created_at = now - datetime.timedelta(hours=72)
        user.save()

        # Zmieniamy datę utworzenia terminu na więcej ja 2 godziny temu oraz
        # ustawiamy datę zakończenia szkolenia w przyszłości.
        term.created_at = now - datetime.timedelta(hours=3)
        term.termin = now.date() - datetime.timedelta(days=13)
        term.termin_zakonczenia = now.date() + datetime.timedelta(days=3)
        term.save()

        cmd = user_notifications.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 0)

        # Ustawiamy datę zakończenia szkolenia na 4 dni temu.
        term.termin_zakonczenia = now.date() - datetime.timedelta(days=4)
        term.save()

        cmd = user_notifications.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 0)

        # Ustawiamy datę terminu na nie później jak 7 dni temu.
        term.termin = now.date() - datetime.timedelta(days=5)
        term.termin_zakonczenia = None
        term.save()

        cmd = user_notifications.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 0)

    def _test_all_term_created_scenarios(self, language):
        """
        Funkcja sprawdzamy wszystkie przyadki, w których wysyłane są
        powiadomienia o nowo utworzonych (`term_created`) terminach.
        """

        now = datetime.datetime.now()

        # Tworzymy dane początkowe
        user = UserNotificationFactory(language=language)
        t1 = SzkolenieFactory(language=language)
        t2 = SzkolenieFactory(language=language)

        locations = []
        for i in range(5):
            locations.append(LokalizacjaFactory())

        term = TerminSzkoleniaFactory(szkolenie=t1, lokalizacja=locations[0])

        # Stworzyliśmy użytkownika, który zapisał się na powiadomienia
        # dotyczące szkoleń `t1` oraz `t2` w lokalizacjach `locations`.

        UserCoursesNotificationFactory(
            user=user,
            locations=locations,
            training=t1,
        )
        UserCoursesNotificationFactory(
            user=user,
            locations=locations,
            training=t2,
        )

        logs_objects = UserNotificationLog.objects

        # Zmieniamy datę utworzenia terminu oraz ostatnią notyfiację
        # użytkownika.
        term.created_at = now - datetime.timedelta(hours=3)
        term.save()

        user.created_at = now - datetime.timedelta(hours=49)
        user.save()

        self.assertEqual(logs_objects.count(), 0)
        self.assertFalse(user.last_email_sent_at)

        # Liczba wysłanych maili oraz logów powinna wynieść 1
        cmd = user_notifications.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 1)
        self.assertEqual(logs_objects.count(), 1)

        # Użytkownik powinien mieć informację o ostatniej wysyłce
        self.assertTrue(UserNotification.objects.get(pk=user.pk).last_email_sent_at)

        # Sprawdźmy zawartość maila
        self._test_email_body(
            mail.outbox[0].body,
            is_created=True,
            is_started=False,
            terms=[term],
            language=user.language,
            user=user,
        )

        # Spróbujmy jeszcze raz wysłać - nic nie powinno się stać.
        mail.outbox = []

        cmd = user_notifications.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 0)
        self.assertEqual(logs_objects.count(), 1)

        # Teraz stwórzmy 10 użytkowników (z czego 8 nadających się do
        # wysyłki) oraz 10 terminów, z czego 5 pasujących do użytkowników.

        trainings = []
        locations = []

        for i in range(11):
            trainings.append(SzkolenieFactory())
            locations.append(LokalizacjaFactory())

            e = TerminSzkoleniaFactory(
                szkolenie=trainings[-1],
                lokalizacja=locations[-1],
            )
            if i <= 4:
                e.created_at = now - datetime.timedelta(hours=3)
                e.save()

        for i in range(11):
            e = UserNotificationFactory(language=language)
            UserCoursesNotificationFactory(
                user=e,
                locations=[locations[i], locations[-1]],
                training=trainings[i],
            )
            if i <= 7:
                e.created_at = now - datetime.timedelta(hours=72)
                e.save()

        # Powinniśmy dostać 5 wysłanych maili oraz 5 + 1 logów
        # (1 z poprzedniego testu)

        cmd = user_notifications.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 5)
        self.assertEqual(logs_objects.count(), 5 + 1)

    def test_all_term_created_scenarios_pl(self):
        self._test_all_term_created_scenarios(language="pl")

    def test_all_term_created_scenarios_en(self):
        self._test_all_term_created_scenarios(language="en")

    def _test_all_term_started_scenarios(self, language):
        """
        Funkcja sprawdzamy wszystkie przyadki, w których wysyłane są
        powiadomienia o nowo uruchomionych (`term_started`) terminach.
        """

        now = datetime.datetime.now()

        # Tworzymy dane początkowe
        user = UserNotificationFactory(language=language)
        t1 = SzkolenieFactory(language=language)
        t2 = SzkolenieFactory(language=language)

        locations = []
        for i in range(5):
            locations.append(LokalizacjaFactory())

        term = TerminSzkoleniaFactory(szkolenie=t1, lokalizacja=locations[0])

        # Stworzyliśmy użytkownika, który zapisał się na powiadomienia
        # dotyczące szkoleń `t1` oraz `t2` w lokalizacjach `locations`.

        UserCoursesNotificationFactory(
            user=user,
            locations=locations,
            training=t1,
        )
        UserCoursesNotificationFactory(
            user=user,
            locations=locations,
            training=t2,
        )

        logs_objects = UserNotificationLog.objects

        # Zmieniamy datę utworzenia terminu oraz ostatnią notyfiację
        # użytkownika.
        term.created_at = now - datetime.timedelta(hours=3)
        term.jobs_state = now - datetime.timedelta(hours=1)
        term.save()

        user.created_at = now - datetime.timedelta(hours=49)
        user.save()

        self.assertEqual(logs_objects.count(), 0)
        self.assertFalse(user.last_email_sent_at)

        # Liczba wysłanych maili oraz logów powinna wynieść 1
        cmd = user_notifications.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 1)
        self.assertEqual(logs_objects.count(), 1)

        # Użytkownik powinien mieć informację o ostatniej wysyłce
        self.assertTrue(UserNotification.objects.get(pk=user.pk).last_email_sent_at)

        # Sprawdźmy zawartość maila
        self._test_email_body(
            mail.outbox[0].body,
            is_created=False,
            is_started=True,
            terms=[term],
            language=user.language,
            user=user,
        )

        # Spróbujmy jeszcze raz wysłać - nic nie powinno się stać.
        mail.outbox = []

        cmd = user_notifications.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 0)
        self.assertEqual(logs_objects.count(), 1)

        # Teraz stwórzmy 10 użytkowników (z czego 8 nadających się do
        # wysyłki) oraz 10 terminów, z czego 5 pasujących do użytkowników.

        trainings = []
        locations = []

        for i in range(11):
            trainings.append(SzkolenieFactory())
            locations.append(LokalizacjaFactory())

            e = TerminSzkoleniaFactory(
                szkolenie=trainings[-1],
                lokalizacja=locations[-1],
            )
            if i <= 4:
                e.created_at = now - datetime.timedelta(hours=3)
                e.jobs_state = now - datetime.timedelta(hours=1)
                e.save()

        for i in range(11):
            e = UserNotificationFactory(language=language)
            UserCoursesNotificationFactory(
                user=e,
                locations=[locations[i], locations[-1]],
                training=trainings[i],
            )
            if i <= 7:
                e.created_at = now - datetime.timedelta(hours=72)
                e.save()

        # Powinniśmy dostać 5 wysłanych maili oraz 5 + 1 logów
        # (1 z poprzedniego testu)

        cmd = user_notifications.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 5)
        self.assertEqual(logs_objects.count(), 5 + 1)

    def test_all_term_started_scenarios_pl(self):
        self._test_all_term_started_scenarios(language="pl")

    def test_all_term_started_scenarios_en(self):
        self._test_all_term_started_scenarios(language="en")

    def _test_all_term_before_start_scenarios(self, language):
        """
        Funkcja sprawdzamy wszystkie przyadki, w których wysyłane są
        powiadomienia o terminach (`before_start`) który uzbierały odpowiednią
        liczbę osób.
        """

        now = datetime.datetime.now()

        # Tworzymy dane początkowe
        user = UserNotificationFactory(language=language)
        t1 = SzkolenieFactory(language=language, min_grupa_bazowa=5)
        t2 = SzkolenieFactory(language=language, min_grupa_bazowa=5)

        locations = []
        for i in range(5):
            locations.append(LokalizacjaFactory())

        term = TerminSzkoleniaFactory(szkolenie=t1, lokalizacja=locations[0])

        # Stworzyliśmy użytkownika, który zapisał się na powiadomienia
        # dotyczące szkoleń `t1` oraz `t2` w lokalizacjach `locations`.

        UserCoursesNotificationFactory(
            user=user,
            locations=locations,
            training=t1,
        )
        UserCoursesNotificationFactory(
            user=user,
            locations=locations,
            training=t2,
        )

        participants = []

        for i in range(5):
            participants.append(UczestnikFactory.create(termin=term))

        logs_objects = UserNotificationLog.objects

        # Zmieniamy datę utworzenia terminu oraz ostatnią notyfiację
        # użytkownika.
        term.created_at = now - datetime.timedelta(hours=3)
        term.save()

        user.created_at = now - datetime.timedelta(hours=49)
        user.save()

        self.assertEqual(logs_objects.count(), 0)
        self.assertFalse(user.last_email_sent_at)

        # Liczba wysłanych maili oraz logów powinna wynieść 1
        cmd = user_notifications.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 1)
        self.assertEqual(logs_objects.count(), 1)

        # Użytkownik powinien mieć informację o ostatniej wysyłce
        self.assertTrue(UserNotification.objects.get(pk=user.pk).last_email_sent_at)

        # Sprawdźmy zawartość maila
        self._test_email_body(
            mail.outbox[0].body,
            is_created=False,
            is_started=False,
            before_start=True,
            terms=[term],
            language=user.language,
            user=user,
        )

        # Spróbujmy jeszcze raz wysłać - nic nie powinno się stać.
        mail.outbox = []

        cmd = user_notifications.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 0)
        self.assertEqual(logs_objects.count(), 1)

        # Teraz wśród uczestników są nierentowni, więc nie powinno być maila z
        # before_start tylko created.
        participants[0].nierentowny = True
        participants[0].save()

        term.created_at = now - datetime.timedelta(hours=3)
        term.save()

        user.created_at = now - datetime.timedelta(hours=49)
        user.last_email_sent_at = None
        user.save()

        logs_objects.all().delete()

        mail.outbox = []

        cmd = user_notifications.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 1)

        # Sprawdźmy zawartość maila
        self._test_email_body(
            mail.outbox[0].body,
            is_created=True,
            is_started=False,
            terms=[term],
            language=user.language,
            user=user,
        )

    def test_all_term_before_start_scenarios_pl(self):
        self._test_all_term_before_start_scenarios(language="pl")

    def test_all_term_before_start_scenarios_en(self):
        self._test_all_term_before_start_scenarios(language="en")

    def _test_kurs_zawodowy_in_email(self, language):
        """
        Testujemy wystąpienia informacji związanych z kursami zawodowymi.
        """

        # Dodajemy 5 terminów tego samego szkolenia, będącego kursem
        # zawodowym - powinna być informacja z linkiem.

        now = datetime.datetime.now()

        # Tworzymy dane początkowe
        user = UserNotificationFactory(language=language)
        t1 = KursFactory(language=language)
        t2 = SzkolenieFactory(language=language)

        locations = []
        for i in range(5):
            locations.append(LokalizacjaFactory())

        terms = []

        for i in range(5):
            t = TerminSzkoleniaFactory(szkolenie=t1, lokalizacja=locations[i])
            t.created_at = now - datetime.timedelta(hours=3)
            t.save()
            terms.append(t)

        # Stworzyliśmy użytkownika, który zapisał się na powiadomienia
        # dotyczące szkoleń `t1` oraz `t2` w lokalizacjach `locations`.

        UserCoursesNotificationFactory(
            user=user,
            locations=locations,
            training=t1,
        )
        UserCoursesNotificationFactory(
            user=user,
            locations=locations,
            training=t2,
        )

        user.created_at = now - datetime.timedelta(hours=49)
        user.save()

        # Liczba wysłanych maili oraz logów powinna wynieść 1
        cmd = user_notifications.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 1)

        # Sprawdźmy zawartość maila
        self._test_email_body(
            mail.outbox[0].body,
            is_created=True,
            is_started=False,
            terms=terms,
            language=user.language,
            user=user,
            has_kurs_zawodowy_specific_info=True,
        )

        # Teraz dodajemy różne terminy z czego jeden jest kursem zawodowym.
        self._drop_objects()
        mail.outbox = []

        # Tworzymy dane początkowe
        user = UserNotificationFactory(language=language)
        t1 = KursFactory(language=language)
        t2 = SzkolenieFactory(language=language)

        locations = []
        for i in range(5):
            locations.append(LokalizacjaFactory())

        terms = []

        for i in range(5):
            t = TerminSzkoleniaFactory(szkolenie=t1, lokalizacja=locations[i])
            t.created_at = now - datetime.timedelta(hours=3)
            t.save()
            terms.append(t)

            t = TerminSzkoleniaFactory(szkolenie=t2, lokalizacja=locations[i])
            t.created_at = now - datetime.timedelta(hours=3)
            t.jobs_state = now - datetime.timedelta(days=1)
            t.save()
            terms.append(t)

        # Stworzyliśmy użytkownika, który zapisał się na powiadomienia
        # dotyczące szkoleń `t1` oraz `t2` w lokalizacjach `locations`.

        UserCoursesNotificationFactory(
            user=user,
            locations=locations,
            training=t1,
        )
        UserCoursesNotificationFactory(
            user=user,
            locations=locations,
            training=t2,
        )

        user.created_at = now - datetime.timedelta(hours=49)
        user.save()

        # Liczba wysłanych maili oraz logów powinna wynieść 1
        cmd = user_notifications.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 1)

        # Sprawdźmy zawartość maila
        self._test_email_body(
            mail.outbox[0].body,
            is_created=True,
            is_started=True,
            terms=terms,
            language=user.language,
            user=user,
            has_kurs_zawodowy_general_info=True,
        )

    def test_kurs_zawodowy_in_email_pl(self):
        self._test_kurs_zawodowy_in_email(language="pl")

    def test_kurs_zawodowy_in_email_en(self):
        self._test_kurs_zawodowy_in_email(language="en")

    def _test_suggest_schedule_in_email(self, language):
        """
        Testujemy wystąpienia informacji związanych z propozycją własnego
        terminu szkolenia.
        """

        # Dodajemy 5 terminów tego samego szkolenia, - powinna być informacja
        # z linkiem.

        now = datetime.datetime.now()

        # Tworzymy dane początkowe
        user = UserNotificationFactory(language=language)
        t1 = SzkolenieFactory(language=language)
        t2 = SzkolenieFactory(language=language)

        locations = []
        for i in range(5):
            locations.append(LokalizacjaFactory())

        terms = []

        for i in range(5):
            t = TerminSzkoleniaFactory(szkolenie=t1, lokalizacja=locations[i])
            t.created_at = now - datetime.timedelta(hours=3)
            t.save()
            terms.append(t)

        # Stworzyliśmy użytkownika, który zapisał się na powiadomienia
        # dotyczące szkoleń `t1` oraz `t2` w lokalizacjach `locations`.

        UserCoursesNotificationFactory(
            user=user,
            locations=locations,
            training=t1,
        )
        UserCoursesNotificationFactory(
            user=user,
            locations=locations,
            training=t2,
        )

        user.created_at = now - datetime.timedelta(hours=49)
        user.save()

        # Liczba wysłanych maili oraz logów powinna wynieść 1
        cmd = user_notifications.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 1)

        # Sprawdźmy zawartość maila
        self._test_email_body(
            mail.outbox[0].body,
            is_created=True,
            is_started=False,
            terms=terms,
            language=user.language,
            user=user,
            has_suggest_schedule_specific_info=True,
        )

        # Teraz dodajemy różne terminy.
        self._drop_objects()
        mail.outbox = []

        # Tworzymy dane początkowe
        user = UserNotificationFactory(language=language)
        t1 = SzkolenieFactory(language=language)
        t2 = SzkolenieFactory(language=language)

        locations = []
        for i in range(5):
            locations.append(LokalizacjaFactory())

        terms = []

        for i in range(5):
            t = TerminSzkoleniaFactory(szkolenie=t1, lokalizacja=locations[i])
            t.created_at = now - datetime.timedelta(hours=3)
            t.save()
            terms.append(t)

            t = TerminSzkoleniaFactory(szkolenie=t2, lokalizacja=locations[i])
            t.created_at = now - datetime.timedelta(hours=3)
            t.save()
            terms.append(t)

        # Stworzyliśmy użytkownika, który zapisał się na powiadomienia
        # dotyczące szkoleń `t1` oraz `t2` w lokalizacjach `locations`.

        UserCoursesNotificationFactory(
            user=user,
            locations=locations,
            training=t1,
        )
        UserCoursesNotificationFactory(
            user=user,
            locations=locations,
            training=t2,
        )

        user.created_at = now - datetime.timedelta(hours=49)
        user.save()

        # Liczba wysłanych maili oraz logów powinna wynieść 1
        cmd = user_notifications.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 1)

        # Sprawdźmy zawartość maila
        self._test_email_body(
            mail.outbox[0].body,
            is_created=True,
            is_started=False,
            terms=terms,
            language=user.language,
            user=user,
            has_suggest_schedule_general_info=True,
        )

    def test_suggest_schedule_in_email_pl(self):
        self._test_suggest_schedule_in_email(language="pl")

    def test_suggest_schedule_in_email_en(self):
        self._test_suggest_schedule_in_email(language="en")

    def test_all_combined_scenarios(self):
        """
        Funkcja testuje wysyłkę powiadomien łącząc wiele warunków (daty,
        dotychczas wysłane powiadomienia etc)
        """

        now = datetime.datetime.now()

        # Tworzymy dane początkowe
        user = UserNotificationFactory()
        user.created_at = now - datetime.timedelta(days=10)
        user.last_email_sent_at = now - datetime.timedelta(days=3)
        user.save()

        t1 = SzkolenieFactory()
        t2 = SzkolenieFactory()

        locations = [LokalizacjaFactory(shortname="zdalnie", fullname_miejscownik="online")]
        for i in range(4):
            locations.append(LokalizacjaFactory())

        # Stworzyliśmy użytkownika, który zapisał się na powiadomienia
        # dotyczące szkoleń `t1` oraz `t2` w lokalizacjach `locations`.

        UserCoursesNotificationFactory(
            user=user,
            locations=locations,
            training=t1,
        )
        UserCoursesNotificationFactory(
            user=user,
            locations=locations,
            training=t2,
        )

        # Dodajemy dwa terminy:
        #   - jeden - stworzony jakiś czas temu, ale uruchomiony niedawno
        #   - drugi - stworzony 5 godzin temu
        term1 = TerminSzkoleniaFactory(szkolenie=t1, lokalizacja=locations[0])
        term1.created_at = now - datetime.timedelta(days=10)
        term1.jobs_state = now - datetime.timedelta(days=1)
        term1.save()

        term2 = TerminSzkoleniaFactory(szkolenie=t2, lokalizacja=locations[1])
        term2.created_at = now - datetime.timedelta(hours=5)
        term2.save()

        logs_objects = UserNotificationLog.objects

        # Powinien zostać wysłany jeden email, a w nim jedno powiadomienie o
        # uruchomionym terminie i jeden o utworzonym.

        with override_settings(SALA_ZDALNA_IDS={"pl": locations[0].id}):
            cmd = user_notifications.Command()
            cmd.handle()

        self.assertEqual(len(mail.outbox), 1)
        self.assertEqual(logs_objects.count(), 2)

        # Sprawdźmy zawartość maila
        self._test_email_body(
            mail.outbox[0].body,
            is_created=True,
            is_started=True,
            terms=[term1, term2],
            language=user.language,
            user=user,
        )

        # Uruchamiamy zadanie jeszcze raz - nic nie powinno zostać wysłane
        mail.outbox = []

        with override_settings(SALA_ZDALNA_IDS={"pl": locations[0].id}):
            cmd = user_notifications.Command()
            cmd.handle()

        self.assertEqual(len(mail.outbox), 0)
        self.assertEqual(logs_objects.count(), 2)

        # Zmieniamy użytkownikowi datę ostatniej notyfikacji na fałszywą
        # (starszą), mimo to nie powinien nic dostać, gdyż logi stanowią
        # dodatkowe zabezpieczenie.
        mail.outbox = []

        user.last_email_sent_at = now - datetime.timedelta(days=3)
        user.save()

        with override_settings(SALA_ZDALNA_IDS={"pl": locations[0].id}):
            cmd = user_notifications.Command()
            cmd.handle()

        self.assertEqual(len(mail.outbox), 0)
        self.assertEqual(logs_objects.count(), 2)

    def test_sala_zdalna(self):
        now = datetime.datetime.now()

        # Tworzymy dane początkowe
        user = UserNotificationFactory(language="pl")
        user.created_at = now - datetime.timedelta(days=10)
        user.last_email_sent_at = now - datetime.timedelta(days=3)
        user.save()

        t1 = SzkolenieFactory()
        t2 = SzkolenieFactory()
        t3 = SzkolenieFactory()
        t4 = SzkolenieFactory()

        locations = []
        for i in range(5):
            locations.append(LokalizacjaFactory())

        zdalna = LokalizacjaFactory(shortname="zdalnie", fullname_miejscownik="online")

        # Stworzyliśmy użytkownika, który zapisał się na powiadomienia
        # dotyczące szkoleń `t1` oraz `t2` w lokalizacjach `locations`.

        UserCoursesNotificationFactory(
            user=user,
            locations=locations,
            training=t1,
        )
        UserCoursesNotificationFactory(
            user=user,
            locations=locations,
            training=t2,
        )
        UserCoursesNotificationFactory(
            user=user,
            locations=locations,
            training=t3,
        )
        UserCoursesNotificationFactory(
            user=user,
            locations=locations,
            training=t4,
        )

        # Dodajemy cztery terminy:
        #   - jeden - stworzony jakiś czas temu, ale uruchomiony niedawno
        #   - drugi - stworzony jakiś czas temu, ale uruchomiony niedawno i w lokalizacji zdalnej
        #   - trzci - stworzony 5 godzin temu
        #   - czwarty - stworzony 5 godzin temu i w lokalizacji zdalnej
        term1 = TerminSzkoleniaFactory(szkolenie=t1, lokalizacja=locations[0])
        term1.created_at = now - datetime.timedelta(days=10)
        term1.jobs_state = now - datetime.timedelta(days=1)
        term1.save()

        term2 = TerminSzkoleniaFactory(szkolenie=t2, lokalizacja=zdalna)
        term2.created_at = now - datetime.timedelta(days=10)
        term2.jobs_state = now - datetime.timedelta(days=1)
        term2.save()        

        term3 = TerminSzkoleniaFactory(szkolenie=t3, lokalizacja=locations[1])
        term3.created_at = now - datetime.timedelta(hours=5)
        term3.save()

        term4 = TerminSzkoleniaFactory(szkolenie=t4, lokalizacja=zdalna)
        term4.created_at = now - datetime.timedelta(hours=5)
        term4.save()        

        logs_objects = UserNotificationLog.objects

        # Powinien zostać wysłany jeden email, a w nim jedno powiadomienie o
        # uruchomionym terminie i jeden o utworzonym.

        with override_settings(SALA_ZDALNA_IDS={"pl": zdalna.id}):
            cmd = user_notifications.Command()
            cmd.handle()

        self.assertEqual(len(mail.outbox), 1)
        self.assertEqual(logs_objects.count(), 3)

        # Sprawdźmy zawartość maila
        self._test_email_body(
            mail.outbox[0].body,
            is_created=True,
            is_started=True,
            terms=[term1, term2, term3],
            language=user.language,
            user=user,
        )

        # Uruchamiamy zadanie jeszcze raz - nic nie powinno zostać wysłane
        mail.outbox = []

        with override_settings(SALA_ZDALNA_IDS={"pl": zdalna.id}):
            cmd = user_notifications.Command()
            cmd.handle()

        self.assertEqual(len(mail.outbox), 0)
        self.assertEqual(logs_objects.count(), 3)

        # Zmieniamy użytkownikowi datę ostatniej notyfikacji na fałszywą
        # (starszą), mimo to nie powinien nic dostać, gdyż logi stanowią
        # dodatkowe zabezpieczenie.
        mail.outbox = []

        user.last_email_sent_at = now - datetime.timedelta(days=3)
        user.save()

        with override_settings(SALA_ZDALNA_IDS={"pl": zdalna.id}):
            cmd = user_notifications.Command()
            cmd.handle()

        self.assertEqual(len(mail.outbox), 0)
        self.assertEqual(logs_objects.count(), 3)

    def test_email_opt_out(self):
        now = datetime.datetime.now()

        # Tworzymy dane początkowe
        user = UserNotificationFactory()
        user.created_at = now - datetime.timedelta(days=10)
        user.last_email_sent_at = now - datetime.timedelta(days=3)
        user.save()

        t1 = SzkolenieFactory()
        t2 = SzkolenieFactory()

        locations = []
        for i in range(5):
            locations.append(LokalizacjaFactory())

        # Stworzyliśmy użytkownika, który zapisał się na powiadomienia
        # dotyczące szkoleń `t1` oraz `t2` w lokalizacjach `locations`.

        UserCoursesNotificationFactory(
            user=user,
            locations=locations,
            training=t1,
        )
        UserCoursesNotificationFactory(
            user=user,
            locations=locations,
            training=t2,
        )

        # Dodajemy dwa terminy:
        #   - jeden - stworzony jakiś czas temu, ale uruchomiony niedawno
        #   - drugi - stworzony 5 godzin temu
        term1 = TerminSzkoleniaFactory(szkolenie=t1, lokalizacja=locations[0])
        term1.created_at = now - datetime.timedelta(days=10)
        term1.jobs_state = now - datetime.timedelta(days=1)
        term1.save()

        term2 = TerminSzkoleniaFactory(szkolenie=t2, lokalizacja=locations[1])
        term2.created_at = now - datetime.timedelta(hours=5)
        term2.save()

        logs_objects = UserNotificationLog.objects

        OptOutFactory.create(email=user.email)

        cmd = user_notifications.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 0)
        self.assertEqual(logs_objects.count(), 0)

    @patch("www.management.commands.user_notifications.logger")
    def test_errors_scenarios(self, mock_logger):
        """
        Funkcja generuje błędy zapisów i wysyłki maila.
        """

        now = datetime.datetime.now()

        # Tworzymy dane początkowe
        user = UserNotificationFactory()
        user.created_at = now - datetime.timedelta(days=3)
        user.save()

        t1 = SzkolenieFactory()

        locations = []
        for i in range(5):
            locations.append(LokalizacjaFactory())

        UserCoursesNotificationFactory(
            user=user,
            locations=locations,
            training=t1,
        )

        term = TerminSzkoleniaFactory(szkolenie=t1, lokalizacja=locations[0])
        term.created_at = now - datetime.timedelta(days=2)
        term.save()

        # Robimy patch na metodę `get_or_create` modelu `UserNotificationLog`
        # w celu sprawdzenia poprawności logowania błędu o nieudanym zapisie
        # logu.
        with patch(
            "www.models.UserNotificationLog.objects.get_or_create",
            MagicMock(side_effect=Exception()),
        ):

            cmd = user_notifications.Command()
            cmd.handle()

            mock_logger.warning.assert_called_with(
                "Błąd przy zapisie logu dla Terminu ID: {0} dla "
                "użytkownika ID: {1} ({2})".format(term.pk, user.pk, user.email)
            )

        # Wyzerujmy informacje o wysyłce dla użytkownika:
        user.last_email_sent_at = None
        user.save()
        UserNotificationLog.objects.all().delete()

        # Robimy patch na klasę `EmailMessage` w celu sprawdzenia poprawności
        # logowania błędu o nieudanym wysłaniu maila.
        with patch(
            "django.core.mail.EmailMessage.send",
            MagicMock(side_effect=Exception("Fail!")),
        ):

            cmd = user_notifications.Command()
            cmd.handle()

            mock_logger.exception.assert_called_with(
                "Błąd przy wysyłce powiadomienia dla użytkownika "
                "ID: {0} ({1})".format(user.pk, user.email)
            )


@override_settings(EXPIRED_NOTIFICATIONS_MAX_AGE=180)
class UnsubscribeSystemUsersManagementTestCase(ALXTestCase):
    def test_empty_scenarios(self):
        """
        Funkcja sprawdzamy wszystkie przyadki, w których nie będzie wypisów.
        """

        # Tworzymy dane początkowe

        for i in range(3):
            user = UserNotificationFactory(source="system")
            UserCoursesNotificationFactory(user=user, source="system")

        cmd = unsubscribe_system_users.Command()
        cmd.handle()

        # Nic nie powinno się zmienić

        self.assertEqual(UserCoursesNotification.objects.all().count(), 3)

        # Zmieniamy datę utworzenia na przeszła, ale też źródło -
        # nic nie powinno się zmienić.

        for c in UserCoursesNotification.objects.all():
            c.created_at -= datetime.timedelta(days=300)
            c.source = "www"
            c.save()

        cmd = unsubscribe_system_users.Command()
        cmd.handle()

        self.assertEqual(UserCoursesNotification.objects.all().count(), 3)

        # Nie powinien zostać wysłany żaden email
        self.assertEqual(len(mail.outbox), 0)

    def test_unsubscribe(self):
        """
        Funkcja sprawdzamy wszystkie przyadki, w których nastapiło wypisanie.
        """

        # Tworzymy dane początkowe

        users_without_courses = []
        users_with_one_course = []

        # 3 użytkowników z przeterminowanymi szkoleniami.
        for i in range(3):
            user = UserNotificationFactory(source="system")
            course = UserCoursesNotificationFactory(user=user, source="system")
            course.created_at -= datetime.timedelta(days=300)
            course.save()
            users_without_courses.append(user)

        # 3 użytkowników z nie-przeterminowanymi szkoleniami.
        for i in range(3):
            user = UserNotificationFactory(source="system")
            UserCoursesNotificationFactory(user=user, source="system")

        # 3 użytkowników z przeterminowanymi szkoleniami, ale mają też zapisy
        # robione poza systemem.
        for i in range(3):
            user = UserNotificationFactory(source="system")
            UserCoursesNotificationFactory(user=user, source="system")
            UserCoursesNotificationFactory(user=user, source="www")

        # 3 użytkowników, kazdy z nich ma 2 przterminiowane zapisy i
        # jeden nie.
        for i in range(3):
            user = UserNotificationFactory(source="system")
            course = UserCoursesNotificationFactory(user=user, source="system")
            course.created_at -= datetime.timedelta(days=300)
            course.save()
            course = UserCoursesNotificationFactory(user=user, source="system")
            course.created_at -= datetime.timedelta(days=300)
            course.save()
            UserCoursesNotificationFactory(user=user, source="system")
            users_with_one_course.append(user)

        # Powinno być 70 zapisów.
        self.assertEqual(UserCoursesNotification.objects.all().count(), 21)

        cmd = unsubscribe_system_users.Command()
        cmd.handle()

        # Powinno zostać 40 zapisów.
        self.assertEqual(UserCoursesNotification.objects.all().count(), 12)

        # Powinien zostać wysłany jeden email z raportem
        self.assertEqual(len(mail.outbox), 1)

        # Ci użytkownicy nie powinni mieć żadnej notyfikacji
        for user in users_without_courses:
            self.assertEqual(user.preferences.all().count(), 0)

            self.assertIn(user.email, mail.outbox[0].body)

        # Ci użytkownicy powinni mieć jedną notyfikację
        for user in users_with_one_course:
            self.assertEqual(user.preferences.all().count(), 1)

            self.assertIn(user.email, mail.outbox[0].body)


@override_settings(
    USER_NOTIFICATION_RESEND_ACTIVATION_EMAIL=48,
    USER_NOTIFICATION_MARK_AS_NEVER_ACTIVATED=24 * 5,
)
class ResendActivationEmailManagementTestCase(ALXTestCase):
    def test_empty_scenarios(self):
        """
        Funkcja sprawdzamy wszystkie przyadki, w których nie będzie wysyłek
        maili.
        """

        UserNotificationFactory.create_batch(2, status=0)

        cmd = resend_activation_email.Command()
        cmd.handle()

        # Nie powinny zostać wysłane żadne maile
        self.assertEqual(len(mail.outbox), 0)

        # Zmieniamy datę wysyłki maila, ale status jest różny od 0
        delta = datetime.datetime.now() - datetime.timedelta(days=15)

        for status in [-1, 1, 2]:
            mail.outbox = []

            UserNotificationFactory.create_batch(
                2, status=status, activation_email_sent_at=delta
            )

            cmd = resend_activation_email.Command()
            cmd.handle()

            # Nie powinny zostać wysłane żadne maile
            self.assertEqual(len(mail.outbox), 0)

        # Robimy to samo co wyżej, ale z `resend_activation_email_counter` == 1
        delta = datetime.datetime.now() - datetime.timedelta(days=15)

        for status in [-1, 1, 2]:
            mail.outbox = []

            UserNotificationFactory.create_batch(
                2,
                resend_activation_email_counter=1,
                status=status,
                activation_email_sent_at=delta,
            )

            cmd = resend_activation_email.Command()
            cmd.handle()

            # Nie powinny zostać wysłane żadne maile
            self.assertEqual(len(mail.outbox), 0)

        # Dodajemu użytkownika pasującego do pierwszego powiadomienia, ale
        # data wysłania ostatniego przypomnienia jest za mała.
        user = UserNotificationFactory(resend_activation_email_counter=0, status=0)
        user.activation_email_sent_at = datetime.datetime.now() - datetime.timedelta(
            days=1
        )
        user.save()

        cmd = resend_activation_email.Command()
        cmd.handle()

        # Nie powinny zostać wysłane żadne maile
        self.assertEqual(len(mail.outbox), 0)

        # Dodajemu użytkownika pasującego do drugiego powiadomienia, ale
        # data wysłania ostatniego przypomnienia jest za mała.
        user = UserNotificationFactory(resend_activation_email_counter=1, status=0)
        user.activation_email_sent_at = datetime.datetime.now() - datetime.timedelta(
            days=4
        )
        user.save()

        cmd = resend_activation_email.Command()
        cmd.handle()

        # Nie powinny zostać wysłane żadne maile
        self.assertEqual(len(mail.outbox), 0)

    def _test_first_reminder(self, language):
        """
        Testujemy wysyłanie pierwszego przypomnienia.
        """

        # Dodajemy 5 użytkowników, z czego 2 powinno dostać przypomnienia.

        users = []

        for i in range(3):
            UserNotificationFactory(language=language)

        for i in range(2):
            user = UserNotificationFactory(language=language, status=0)
            user.activation_email_sent_at = (
                datetime.datetime.now() - datetime.timedelta(days=3)
            )
            UserCoursesNotificationFactory(
                user=user,
                locations=[
                    LokalizacjaFactory.create(),
                    LokalizacjaFactory.create(),
                    LokalizacjaFactory.create(),
                ],
            )
            UserCoursesNotificationFactory(user=user)
            user.save()

            users.append(user)

        cmd = resend_activation_email.Command()
        cmd.handle()

        # Powinny zostać wysłane 2 maile
        self.assertEqual(len(mail.outbox), 2)

        # Sprawdź, czy zostały zapisane dane o wysyłce
        for user in users:
            u = UserNotification.objects.get(pk=user.pk)
            self.assertEqual(u.resend_activation_email_counter, 1)
            self.assertNotEqual(
                user.activation_email_sent_at, u.activation_email_sent_at
            )

        # Sprawdzamy zawartość maili
        for no, email in enumerate(mail.outbox):
            if language == "pl":
                subject = (
                    "[Wymagana reakcja] Potwierdź chęć otrzymywania "
                    "powiadomień z ALX (www.alx.pl)"
                )
            else:
                subject = (
                    "[Action required] Confirm subscription to "
                    "notifications from ALX"
                )

            self.assertEqual(subject, email.subject)

            preferences = users[no].preferences.all()
            body = email.body

            for preference in preferences:
                self.assertIn(preference.training.nazwa, body)
                for location in preference.locations.all():
                    if language == "pl":
                        location_name = location.fullname_miejscownik
                    else:
                        location_name = location.fullname

                    self.assertIn(location_name, body)

    def test_first_reminder_pl(self):
        self._test_first_reminder("pl")

    def test_first_reminder_en(self):
        self._test_first_reminder("en")

    def _test_second_reminder(self, language):
        """
        Testujemy wysyłanie drugiego przypomnienia i oznaczania użytkownika
        jako "nigdy niepotwierdzony".
        """

        # Dodajemy 5 użytkowników, z czego 2 powinno dostać przypomnienia.

        users = []

        for i in range(3):
            UserNotificationFactory(language=language)

        for i in range(2):
            user = UserNotificationFactory(
                language=language, status=0, resend_activation_email_counter=1
            )
            user.activation_email_sent_at = (
                datetime.datetime.now() - datetime.timedelta(days=6)
            )
            UserCoursesNotificationFactory(
                user=user,
                locations=[
                    LokalizacjaFactory.create(),
                    LokalizacjaFactory.create(),
                    LokalizacjaFactory.create(),
                ],
            )
            UserCoursesNotificationFactory(user=user)
            user.save()

            users.append(user)

        cmd = resend_activation_email.Command()
        cmd.handle()

        # Powinny zostać wysłane 2 maile
        self.assertEqual(len(mail.outbox), 2)

        # Sprawdź, czy zostały zapisane dane o wysyłce
        for user in users:
            u = UserNotification.objects.get(pk=user.pk)
            self.assertEqual(u.resend_activation_email_counter, 2)
            self.assertEqual(u.status, -1)
            self.assertNotEqual(
                user.activation_email_sent_at, u.activation_email_sent_at
            )

        # Sprawdzamy zawartość maili
        for no, email in enumerate(mail.outbox):
            if language == "pl":
                subject = (
                    "[Wymagana reakcja] Potwierdź chęć otrzymywania "
                    "powiadomień z ALX (www.alx.pl)"
                )
            else:
                subject = (
                    "[Action required] Confirm subscription to "
                    "notifications from ALX"
                )

            self.assertEqual(subject, email.subject)

            preferences = users[no].preferences.all()
            body = email.body

            for preference in preferences:
                self.assertIn(preference.training.nazwa, body)
                for location in preference.locations.all():
                    if language == "pl":
                        location_name = location.fullname_miejscownik
                    else:
                        location_name = location.fullname

                    self.assertIn(location_name, body)

    def test_second_reminder_pl(self):
        self._test_second_reminder("pl")

    def test_second_reminder_en(self):
        self._test_second_reminder("en")


class SixteenDaysBeforeStartManagementTestCase(ALXTestCase):
    def test_empty_scenarios(self):
        """
        Funkcja sprawdzamy wszystkie przyadki, w których nie będzie wysyłek
        maili.
        """

        today = datetime.date.today()

        cmd = sixteen_days_before_start.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 0)
        self.assertEqual(TerminSzkoleniaLog.objects.count(), 0)

        # Dodajemy termin, ale nie ma on uczestników
        termin = TerminSzkoleniaFactory.create(
            termin=today + datetime.timedelta(days=16)
        )
        cmd = sixteen_days_before_start.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 0)
        # Log powinien się dodać
        self.assertEqual(TerminSzkoleniaLog.objects.count(), 1)

        TerminSzkoleniaLog.objects.all().delete()

        # Termin jest ok, ale wszyscy uczestnicy zrezygnowali.
        UczestnikFactory.create_batch(4, termin=termin, status=4)

        cmd = sixteen_days_before_start.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 0)
        # Log powinien się dodać
        self.assertEqual(TerminSzkoleniaLog.objects.count(), 1)

        TerminSzkoleniaLog.objects.all().delete()

        # Termin jest za 15 dni
        UczestnikFactory.create_batch(4, termin=termin)
        termin.termin = today + datetime.timedelta(days=15)
        termin.save()
        cmd = sixteen_days_before_start.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 0)
        self.assertEqual(TerminSzkoleniaLog.objects.count(), 0)

        # Termin ma ustawiony odbylo_sie na False
        termin.termin = today + datetime.timedelta(days=16)
        termin.odbylo_sie = False
        termin.save()
        cmd = sixteen_days_before_start.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 0)
        self.assertEqual(TerminSzkoleniaLog.objects.count(), 0)

        # Uczestnicy nie mają adresów email
        termin.odbylo_sie = None
        termin.save()
        Uczestnik.objects.all().delete()
        UczestnikFactory.create_batch(4, termin=termin, email="")
        cmd = sixteen_days_before_start.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 0)
        self.assertEqual(TerminSzkoleniaLog.objects.count(), 1)

        TerminSzkoleniaLog.objects.all().delete()

        # Szkolenie jest zamknięte
        termin.szkolenie.language = "pl"
        termin.szkolenie.save()
        termin.zamkniete = True
        termin.save()
        Uczestnik.objects.all().delete()
        UczestnikFactory.create_batch(4, termin=termin)
        cmd = sixteen_days_before_start.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 0)
        self.assertEqual(TerminSzkoleniaLog.objects.count(), 0)

    def test_send_emails_szkolenie_pl(self):
        today = datetime.date.today()

        termin = TerminSzkoleniaFactory.create(
            termin=today + datetime.timedelta(days=16)
        )
        uczestnik = UczestnikFactory.create(
            termin=termin,
            status=2,
            uczestnik_wieloosobowy_ilosc_osob=2,
        )
        uczestnik.faktura_firma = "Super firma"
        uczestnik.save()
        UczestnikFactory.create(termin=termin, status=2, imie_nazwisko="Jan Kowalski")
        UczestnikFactory.create_batch(1, termin=termin, status=2, email="")
        UczestnikFactory.create_batch(3, termin=termin, status=4)

        cmd = sixteen_days_before_start.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 2)
        # Log powinien się dodać
        self.assertEqual(TerminSzkoleniaLog.objects.count(), 1)

        # Sprawdzamy treść
        self.assertEqual(
            mail.outbox[0].subject, "Przypomnienie o szkoleniu ALX dla Jan Kowalski"
        )
        self.assertEqual(
            mail.outbox[1].subject, "Przypomnienie o szkoleniu ALX dla Super firma"
        )

        body = mail.outbox[0].body

        self.assertIn(termin.szkolenie.nazwa, body)
        self.assertIn("jesteś na liście uczestników szkolenia", body)
        self.assertIn("tego szkolenia może", body)
        self.assertIn("bezkosztowo", body)
        self.assertIn("uregulowania kosztu szkolenia", body)
        self.assertIn("udziału w szkoleniu po", body)

        # Usuchamiamy zadanie jeszcze raz - nic nie powinno sie wysłać
        mail.outbox = []

        cmd = sixteen_days_before_start.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 0)
        self.assertEqual(TerminSzkoleniaLog.objects.count(), 1)

    @unittest.skip("obecnie tylko dla szkolen pl")
    def test_send_emails_szkolenie_en(self):
        today = datetime.date.today()

        termin = TerminSzkoleniaEnFactory.create(
            termin=today + datetime.timedelta(days=16)
        )
        uczestnik = UczestnikFactory.create(
            termin=termin,
            status=2,
            uczestnik_wieloosobowy_ilosc_osob=2,
        )
        uczestnik.faktura_firma = "Super firma"
        uczestnik.save()
        UczestnikFactory.create(termin=termin, status=2, imie_nazwisko="Jan Kowalski")
        UczestnikFactory.create_batch(1, termin=termin, status=2, email="")
        UczestnikFactory.create_batch(3, termin=termin, status=4)

        cmd = sixteen_days_before_start.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 2)
        # Log powinien się dodać
        self.assertEqual(TerminSzkoleniaLog.objects.count(), 1)

        # Sprawdzamy treść
        self.assertEqual(
            mail.outbox[0].subject,
            "Notification about upcoming training ALX for Jan Kowalski",
        )
        self.assertEqual(
            mail.outbox[1].subject,
            "Notification about upcoming training ALX for Super firma",
        )

        body = mail.outbox[0].body

        self.assertIn(termin.szkolenie.nazwa, body)
        self.assertIn("jesteś na liście uczestników szkolenia", body)
        self.assertIn("tego szkolenia może", body)
        self.assertIn("bezkosztowo", body)
        self.assertIn("uregulowania kosztu szkolenia", body)
        self.assertIn("udziału w szkoleniu po", body)

    def test_send_emails_kurs_pl(self):
        today = datetime.date.today()

        termin = TerminSzkoleniaFactory.create(
            termin=today + datetime.timedelta(days=16), szkolenie=KursFactory.create()
        )
        uczestnik = UczestnikFactory.create(
            termin=termin,
            status=2,
            uczestnik_wieloosobowy_ilosc_osob=2,
        )
        uczestnik.faktura_firma = "Super firma"
        uczestnik.save()
        UczestnikFactory.create(termin=termin, status=2, imie_nazwisko="Jan Kowalski")
        UczestnikFactory.create_batch(1, termin=termin, status=2, email="")
        UczestnikFactory.create_batch(3, termin=termin, status=4)

        cmd = sixteen_days_before_start.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 2)
        # Log powinien się dodać
        self.assertEqual(TerminSzkoleniaLog.objects.count(), 1)

        # Sprawdzamy treść
        self.assertEqual(
            mail.outbox[0].subject, "Przypomnienie o kursie ALX dla Jan Kowalski"
        )
        self.assertEqual(
            mail.outbox[1].subject, "Przypomnienie o kursie ALX dla Super firma"
        )

        body = mail.outbox[0].body

        self.assertIn(termin.szkolenie.nazwa, body)
        self.assertIn("jesteś na liście uczestników kursu", body)
        self.assertIn("tego kursu może", body)
        self.assertIn("bez konieczności zapłaty za cały kurs", body)
        self.assertIn("uregulowania kosztu kursu", body)
        self.assertIn("udziału w kursie po", body)

        # Usuchamiamy zadanie jeszcze raz - nic nie powinno sie wysłać
        mail.outbox = []

        cmd = sixteen_days_before_start.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 0)
        self.assertEqual(TerminSzkoleniaLog.objects.count(), 1)

    @unittest.skip("obecnie tylko dla kusow pl")
    def test_send_emails_kurs_en(self):
        today = datetime.date.today()

        termin = TerminSzkoleniaEnFactory.create(
            termin=today + datetime.timedelta(days=16),
            szkolenie=KursFactory.create(language="en"),
        )
        uczestnik = UczestnikFactory.create(
            termin=termin,
            status=2,
            uczestnik_wieloosobowy_ilosc_osob=2,
        )
        uczestnik.faktura_firma = "Super firma"
        uczestnik.save()
        UczestnikFactory.create(termin=termin, status=2, imie_nazwisko="Jan Kowalski")
        UczestnikFactory.create_batch(1, termin=termin, status=2, email="")
        UczestnikFactory.create_batch(3, termin=termin, status=4)

        cmd = sixteen_days_before_start.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 2)
        # Log powinien się dodać
        self.assertEqual(TerminSzkoleniaLog.objects.count(), 1)

        # Sprawdzamy treść
        self.assertEqual(
            mail.outbox[0].subject,
            "Notification about upcoming course ALX for Jan Kowalski",
        )
        self.assertEqual(
            mail.outbox[1].subject,
            "Notification about upcoming course ALX for Super firma",
        )

        body = mail.outbox[0].body

        self.assertIn(termin.szkolenie.nazwa, body)
        self.assertIn("jesteś na liście uczestników kursu", body)
        self.assertIn("tego kursu może", body)
        self.assertIn("bez konieczności zapłaty za cały kurs", body)
        self.assertIn("uregulowania kosztu kursu", body)
        self.assertIn("udziału w kursie po", body)


class UnsubscribeTrainedUsersManagementTestCase(ALXTestCase):
    def test_empty_scenarios(self):
        """
        Funkcja sprawdzamy wszystkie przyadki, w których nie będzie wypisów.
        """

        # Tworzymy dane początkowe

        for i in range(3):
            user = UserNotificationFactory()
            course = UserCoursesNotificationFactory(
                user=user,
            )
            course.created_at -= datetime.timedelta(days=30)
            course.save()

        cmd = unsubscribe_trained_users.Command()
        cmd.handle()

        # Nic nie powinno się zmienić

        self.assertEqual(UserCoursesNotification.objects.all().count(), 3)

        # Dodajemy użytkowników do terminów szkoleń, ale szkolenia się nie
        # odbyły. Nic nie powinno się zmienić.

        for course in UserCoursesNotification.objects.all():
            term = TerminSzkoleniaFactory.create(
                szkolenie=course.training,
                odbylo_sie=False,
                termin=datetime.date.today() - datetime.timedelta(days=5),
            )
            UczestnikFactory.create(termin=term, email=course.user.email, status=3)

        cmd = unsubscribe_trained_users.Command()
        cmd.handle()

        self.assertEqual(UserCoursesNotification.objects.all().count(), 3)

        # Dodajemy przeszkolenia dla użytkowników, ale dla innych szkoleń.
        # Nic nie powinno się zmienić.

        for course in UserCoursesNotification.objects.all():
            term = TerminSzkoleniaFactory.create(
                odbylo_sie=True,
                termin=datetime.date.today() - datetime.timedelta(days=5),
            )
            UczestnikFactory.create(termin=term, email=course.user.email, status=3)

        cmd = unsubscribe_trained_users.Command()
        cmd.handle()

        self.assertEqual(UserCoursesNotification.objects.all().count(), 3)

        # Dodajemy przeszkolenia dla użytkowników, ale dla dzisiejszej daty -
        # szkolenie jezszcze trwa, więc nic nie powinno się zmienić.

        for course in UserCoursesNotification.objects.all():
            term = TerminSzkoleniaFactory.create(
                szkolenie=course.training,
                odbylo_sie=True,
            )
            UczestnikFactory.create(termin=term, email=course.user.email, status=3)

        cmd = unsubscribe_trained_users.Command()
        cmd.handle()

        self.assertEqual(UserCoursesNotification.objects.all().count(), 3)

        # Dodajemy przeszkolenia dla użytkowników, ale ze statusem innym
        # niż 3. Nic nie powinno się zmienić.

        for course in UserCoursesNotification.objects.all():
            term = TerminSzkoleniaFactory.create(
                szkolenie=course.training,
                odbylo_sie=True,
                termin=datetime.date.today() - datetime.timedelta(days=5),
            )
            UczestnikFactory.create(termin=term, email=course.user.email, status=2)

        cmd = unsubscribe_trained_users.Command()
        cmd.handle()

        self.assertEqual(UserCoursesNotification.objects.all().count(), 3)

        # Nie powinien zostać wysłany żaden email
        self.assertEqual(len(mail.outbox), 0)

    def test_unsubscribe(self):
        """
        Funkcja sprawdzamy wszystkie przyadki, w których nastapiło wypisanie.
        """

        # Tworzymy dane początkowe

        users = []

        for i in range(10):
            user = UserNotificationFactory()
            course = UserCoursesNotificationFactory(
                user=user,
            )
            course.created_at -= datetime.timedelta(days=30)
            course.save()
            users.append([user, course])

        # Powinno być 10 preferencji.
        self.assertEqual(UserCoursesNotification.objects.all().count(), 10)

        # Dodajemy Termin, który się odbyły i do których należy pierwszy z
        # testowych użytkowników.

        data = users[0]

        term = TerminSzkoleniaFactory.create(
            szkolenie=data[1].training,
            odbylo_sie=True,
            termin=datetime.date.today() - datetime.timedelta(days=5),
        )
        UczestnikFactory.create(termin=term, email=data[0].email, status=3)

        cmd = unsubscribe_trained_users.Command()
        cmd.handle()

        # Powinno pozostać 9 preferencji.
        self.assertEqual(UserCoursesNotification.objects.all().count(), 9)

        # Powinien zostać wysłany jeden email z raportem
        self.assertEqual(len(mail.outbox), 1)
        self.assertIn(data[0].email, mail.outbox[0].body)

        mail.outbox = []

        # Dodajemy do tego szkolenia jeszcze dwóch użytkowników:
        for data in [users[1], users[2]]:
            term = TerminSzkoleniaFactory.create(
                szkolenie=data[1].training,
                odbylo_sie=True,
                termin=datetime.date.today() - datetime.timedelta(days=5),
            )
            UczestnikFactory.create(termin=term, email=data[0].email, status=3)

        cmd = unsubscribe_trained_users.Command()
        cmd.handle()

        # Powinien zostać wysłany jeden email z raportem
        self.assertEqual(len(mail.outbox), 1)
        self.assertIn(users[1][0].email, mail.outbox[0].body)
        self.assertIn(users[2][0].email, mail.outbox[0].body)

        mail.outbox = []

        # Powinno pozostać 7 preferencji.
        self.assertEqual(UserCoursesNotification.objects.all().count(), 7)

        # Teraz usuwamy wszystko, tworzymy jednego użytkownika
        # przypisanego do 10 szkoleń, z czego z 4 został pomyślnie
        # przeszkolony.

        UserNotification.objects.all().delete()

        courses = []
        user = UserNotificationFactory()
        for i in range(10):
            course = UserCoursesNotificationFactory(
                user=user,
            )
            course.created_at -= datetime.timedelta(days=30)
            course.save()
            courses.append(course)

        self.assertEqual(UserCoursesNotification.objects.all().count(), 10)

        # Przeszkolony pomyślnie z 4 pierwszych szkoleń.
        for course in courses[:4]:
            term = TerminSzkoleniaFactory.create(
                szkolenie=course.training,
                odbylo_sie=True,
                termin=datetime.date.today() - datetime.timedelta(days=5),
            )
            UczestnikFactory.create(termin=term, email=user.email, status=3)

        # Przeszkolony bez powodzenia z 2 kolejnych terminów
        for course in courses[5:7]:
            term = TerminSzkoleniaFactory.create(
                szkolenie=course.training,
                odbylo_sie=True,
                termin=datetime.date.today() + datetime.timedelta(days=5),
            )
            UczestnikFactory.create(termin=term, email=user.email, status=3)

        cmd = unsubscribe_trained_users.Command()
        cmd.handle()

        # Powinno pozostać 6 preferencji.
        self.assertEqual(UserCoursesNotification.objects.all().count(), 6)

        # Powinien zostać wysłany jeden email z raportem
        self.assertEqual(len(mail.outbox), 1)
        self.assertIn(user.email, mail.outbox[0].body)


@override_settings(MAIL_TO_NOTIFICATION_ALERT="")
class UserNotificationSubscriptionFormTestCase(UserNotificationBaseTestCase):
    def setUp(self):
        super().setUp()

        self.training = SzkolenieFactory()

        self.locations = [LokalizacjaFactory() for i in range(10)]
        self.locations_qs = Lokalizacja.objects.filter(
            pk__in=[i.pk for i in self.locations]
        )

    def test_save_new_user(self):
        # Zapisujemy nowego użytkownika po raz pierwszy

        form_data = {
            "email": "invalid-email",
            "tos": "1",
            "locations": [i.pk for i in self.locations[:6]],
        }

        form_kwargs = {
            "locations": self.locations_qs,
            "language": "pl",
        }

        form = SubscriptionForm(form_data, **form_kwargs)

        # Niepoprawny - błędny email
        self.assertFalse(form.is_valid())

        form_data["email"] = "<EMAIL>"

        form = SubscriptionForm(form_data, **form_kwargs)

        # Poprawny
        self.assertTrue(form.is_valid())

        # Robimy zapis
        obj, course, send_activation_email = form.save(self.training)

        self.assertTrue(send_activation_email)
        self.assertEqual(obj.language, "pl")
        self.assertEqual(obj.email, "<EMAIL>")
        self.assertEqual(obj.status, 0)
        self.assertTrue(obj.tos_agreement)
        self.assertTrue(obj.activation_email_sent_at)
        self.assertEqual(obj.resend_activation_email_counter, 0)

        # W bazie powinien być 1 użytkownik
        self.assertEqual(UserNotification.objects.all().count(), 1)

        # Użytkownik powinien być przypisany do 1 szkolenia
        self.assertEqual(UserCoursesNotification.objects.all().count(), 1)

        # Użytkownik powinien być przypisany do 6 lokalizacji
        e = UserCoursesNotification.objects.all()[0]

        self.assertEqual(e.locations.all().count(), 6)

    def test_tos_is_required(self):
        # Zapisujemy nowego użytkownika po raz pierwszy

        form_data = {
            "email": "<EMAIL>",
            "locations": [i.pk for i in self.locations[:6]],
        }

        form_kwargs = {
            "locations": self.locations_qs,
            "language": "pl",
        }

        form = SubscriptionForm(form_data, **form_kwargs)

        self.assertFalse(form.is_valid())

    def test_save_new_user_on_en_site(self):
        # Zapisujemy nowego użytkownika po raz pierwszy dla wersji EN, czyli
        # bez lokalizacji.

        form_data = {
            "email": "<EMAIL>",
            "tos": "1",
            "locations": [i.pk for i in self.locations[:1]],
        }

        form_kwargs = {
            "locations": self.locations_qs,
            "language": "en",
        }

        form = SubscriptionForm(form_data, **form_kwargs)

        # Poprawny
        self.assertTrue(form.is_valid())

        # Robimy zapis
        obj, course, send_activation_email = form.save(self.training)

        self.assertTrue(send_activation_email)
        self.assertEqual(obj.language, "en")
        self.assertEqual(obj.email, "<EMAIL>")
        self.assertEqual(obj.status, 0)
        self.assertTrue(obj.activation_email_sent_at)
        self.assertEqual(obj.resend_activation_email_counter, 0)

        # W bazie powinien być 1 użytkownik
        self.assertEqual(UserNotification.objects.all().count(), 1)

        # Użytkownik powinien być przypisany do 1 szkolenia
        self.assertEqual(UserCoursesNotification.objects.all().count(), 1)

        e = UserCoursesNotification.objects.all()[0]
        self.assertEqual(e.locations.all().count(), 1)

    def test_change_status_from_cancelled_to_new(self):
        # Sytuacja, kiedy użytkownik wypisał się z powiadomień, a potem dodaje
        # się jeszcze raz.

        user = UserNotificationFactory(email="<EMAIL>")
        user.set_as_cancelled()

        form_data = {
            "email": user.email,
            "tos": "1",
            "locations": [i.pk for i in self.locations[:2]],
        }

        form_kwargs = {
            "locations": self.locations_qs,
            "language": "pl",
        }

        form = SubscriptionForm(form_data, **form_kwargs)

        # Poprawny
        self.assertTrue(form.is_valid())

        # Robimy zapis
        obj, course, send_activation_email = form.save(self.training)

        self.assertTrue(send_activation_email)
        self.assertEqual(obj.language, "pl")
        self.assertEqual(obj.email, "<EMAIL>")
        self.assertEqual(obj.status, 0)
        self.assertTrue(obj.activation_email_sent_at)
        self.assertFalse(obj.last_email_sent_at)
        self.assertFalse(obj.activation_at)
        self.assertEqual(obj.resend_activation_email_counter, 0)

        # W bazie powinien być 1 użytkownik
        self.assertEqual(UserNotification.objects.all().count(), 1)

        # Użytkownik powinien być przypisany do 1 szkolenia
        self.assertEqual(UserCoursesNotification.objects.all().count(), 1)

        # Użytkownik powinien być przypisany do 2 lokalizacji
        e = UserCoursesNotification.objects.all()[0]

        self.assertEqual(e.locations.all().count(), 2)

    def test_change_status_from_never_activated_to_new(self):
        # Sytuacja, kiedy system oznaczył użytkownika jako "nigdy
        # niepotwierdzony", a potem dodaje się jeszcze raz.

        user = UserNotificationFactory(
            email="<EMAIL>", resend_activation_email_counter=2
        )
        user.set_as_never_activated()

        form_data = {
            "email": user.email,
            "tos": "1",
            "locations": [i.pk for i in self.locations[:2]],
        }

        form_kwargs = {
            "locations": self.locations_qs,
            "language": "pl",
        }

        form = SubscriptionForm(form_data, **form_kwargs)

        # Poprawny
        self.assertTrue(form.is_valid())

        # Robimy zapis
        obj, course, send_activation_email = form.save(self.training)

        self.assertTrue(send_activation_email)
        self.assertEqual(obj.language, "pl")
        self.assertEqual(obj.email, "<EMAIL>")
        self.assertEqual(obj.status, 0)
        self.assertTrue(obj.activation_email_sent_at)
        self.assertFalse(obj.last_email_sent_at)
        self.assertFalse(obj.activation_at)
        self.assertEqual(obj.resend_activation_email_counter, 0)

        # W bazie powinien być 1 użytkownik
        self.assertEqual(UserNotification.objects.all().count(), 1)

        # Użytkownik powinien być przypisany do 1 szkolenia
        self.assertEqual(UserCoursesNotification.objects.all().count(), 1)

        # Użytkownik powinien być przypisany do 2 lokalizacji
        e = UserCoursesNotification.objects.all()[0]

        self.assertEqual(e.locations.all().count(), 2)

    def test_resend_activation_email(self):
        # Sytuacja, kiedy użytkownik próbuje dopisać się do kolejngo szkolenia,
        # mimo iż jeszcze nie aktywował konta.

        user = UserNotificationFactory(email="<EMAIL>", status=0)
        user.activation_email_sent_at = datetime.datetime.now()
        user.save()

        form_data = {
            "email": user.email,
            "tos": "1",
            "locations": [i.pk for i in self.locations[:4]],
        }

        form_kwargs = {
            "locations": self.locations_qs,
            "language": "pl",
        }

        form = SubscriptionForm(form_data, **form_kwargs)

        # Poprawny
        self.assertTrue(form.is_valid())

        # Robimy zapis
        obj, course, send_activation_email = form.save(self.training)

        self.assertTrue(send_activation_email)
        self.assertEqual(obj.resend_activation_email_counter, 0)

        # # Nie minęła godzina od wysłania ostatniego maila aktywacyjnego.
        # self.assertFalse(send_activation_email)
        #
        # # Zmieniamy datę ostatniej wysyłki maila aktywacyjnego
        # delta = datetime.datetime.now() - datetime.timedelta(hours=2)
        # user.activation_email_sent_at = delta
        # user.save()
        #
        # form = SubscriptionForm(form_data, locations=self.locations_qs)
        #
        # # Poprawny
        # self.assertTrue(form.is_valid())
        #
        # # Robimy zapis
        # obj, course, send_activation_email = form.save(self.training, 'en')
        #
        # # Minęła godzina od wysłania ostatniego maila aktywacyjnego.
        # self.assertTrue(send_activation_email)

    def test_resend_activation_email_if_never_activated(self):
        # Sytuacja, kiedy użytkownik próbuje dopisać się do kolejngo
        # szkolenia, mimo, że system oznaczył go jako "nigdy niepotwierdzony".

        user = UserNotificationFactory(
            email="<EMAIL>", resend_activation_email_counter=2
        )
        user.set_as_never_activated()
        user.activation_email_sent_at = datetime.datetime.now()
        user.save()

        form_data = {
            "email": user.email,
            "tos": "1",
            "locations": [i.pk for i in self.locations[:4]],
        }

        form_kwargs = {
            "locations": self.locations_qs,
            "language": "pl",
        }

        form = SubscriptionForm(form_data, **form_kwargs)

        # Poprawny
        self.assertTrue(form.is_valid())

        # Robimy zapis
        obj, course, send_activation_email = form.save(self.training)

        self.assertTrue(send_activation_email)
        self.assertEqual(obj.status, 0)
        self.assertEqual(obj.resend_activation_email_counter, 0)

    def test_change_existing_settings(self):
        # Sytuacja, kiedy użytkownik dopisuje się do szkolenia, do którego
        # już jest dopisany.

        form_data = {
            "email": "<EMAIL>",
            "tos": "1",
            "locations": [i.pk for i in self.locations[:6]],
        }

        form_kwargs = {
            "locations": self.locations_qs,
            "language": "pl",
        }

        form = SubscriptionForm(form_data, **form_kwargs)

        # Poprawny
        self.assertTrue(form.is_valid())

        # Robimy zapis
        obj, course, send_activation_email = form.save(self.training)
        obj.status = 1
        obj.save()

        # W bazie powinien być 1 użytkownik
        self.assertEqual(UserNotification.objects.all().count(), 1)

        # Użytkownik powinien być przypisany do 1 szkolenia
        self.assertEqual(UserCoursesNotification.objects.all().count(), 1)

        # Użytkownik powinien być przypisany do 6 lokalizacji
        e = UserCoursesNotification.objects.all()[0]

        self.assertEqual(e.locations.all().count(), 6)

        # Zapisujemy się na to samo szkolenie, ale w innych lokalizacjach
        form_data = {
            "email": "<EMAIL>",
            "tos": "1",
            "locations": [i.pk for i in self.locations[7:9]],
        }

        form = SubscriptionForm(form_data, **form_kwargs)

        # Poprawny
        self.assertTrue(form.is_valid())
        form.save(self.training)

        # W bazie powinien być 1 użytkownik
        self.assertEqual(UserNotification.objects.all().count(), 1)

        # Użytkownik powinien być przypisany do 1 szkolenia
        self.assertEqual(UserCoursesNotification.objects.all().count(), 1)

        # Użytkownik powinien być przypisany do 2 lokalizacji
        e = UserCoursesNotification.objects.all()[0]

        self.assertEqual(e.locations.all().count(), 2)

        # Zapisujemy się na kolejne szkolenie
        form_data = {
            "email": "<EMAIL>",
            "tos": "1",
            "locations": [i.pk for i in self.locations[:6]],
        }

        form = SubscriptionForm(form_data, **form_kwargs)

        # Poprawny
        self.assertTrue(form.is_valid())
        form.save(SzkolenieFactory())

        # W bazie powinien być 1 użytkownik
        self.assertEqual(UserNotification.objects.all().count(), 1)

        # Użytkownik powinien być przypisany do 1 szkolenia
        self.assertEqual(UserCoursesNotification.objects.all().count(), 2)

        # Powinno być 8 lokalizacji
        locations = 0
        for e in UserCoursesNotification.objects.all():
            locations += e.locations.all().count()

        self.assertEqual(locations, 8)


class UserNotificationReportFiltersTestCase(UserNotificationBaseTestCase):
    def test_form(self):
        # Testujemy zachowanie formularza `UserNotificationReportAdminForm`
        # dla różnych zestawów danych

        # Wysyłamy puste dane
        form_data = {}

        form = UserNotificationReportAdminForm(
            form_data,
        )

        self.assertTrue(form.is_valid())

        self.assertEqual(form.get_params(), {})

        # Wysyłamy puste dane
        form_data = {
            "date_from": "",
            "date_to": "",
        }

        form = UserNotificationReportAdminForm(
            form_data,
        )

        self.assertTrue(form.is_valid())

        self.assertEqual(form.get_params(), {})

        # Tylko data OD
        today = datetime.date.today()

        form_data = {"date_from": today.isoformat()}

        form = UserNotificationReportAdminForm(
            form_data,
        )

        self.assertTrue(form.is_valid())

        self.assertEqual(form.get_params(), {"user__created_at__gte": today})

        # Tylko data DO
        form_data = {"date_to": today.isoformat()}

        form = UserNotificationReportAdminForm(
            form_data,
        )

        self.assertTrue(form.is_valid())

        self.assertEqual(form.get_params(), {"user__created_at__lte": today})

        # Data DO i DO
        form_data = {"date_from": today.isoformat(), "date_to": today.isoformat()}

        form = UserNotificationReportAdminForm(
            form_data,
        )

        self.assertTrue(form.is_valid())

        self.assertEqual(
            form.get_params(),
            {"user__created_at__gte": today, "user__created_at__lte": today},
        )

        # Data OD większa od daty DO
        form_data = {
            "date_from": (today + datetime.timedelta(days=1)).isoformat(),
            "date_to": today.isoformat(),
        }

        form = UserNotificationReportAdminForm(
            form_data,
        )

        self.assertFalse(form.is_valid())

        self.assertEqual(
            form.errors["date_from"], ["Data OD nie może być wieksz od daty DO."]
        )


class UserNotificationPersonalDataFormTestCase(UserNotificationBaseTestCase):
    def test_form(self):
        # Testujemy zachowanie formularza dla różnych danych

        # Wysyłamy puste dane
        instance = UserNotificationFactory.create()

        form_data = {}

        form = UserNotificationPersonalDataForm(
            form_data,
            instance=instance,
        )

        self.assertTrue(form.is_valid())

        # Próbujemy dodać HTML i unicode
        instance = UserNotificationFactory.create()

        form_data = {
            "full_name": "<strong>HTML</strong>",
            "phone_number": "<b>",
        }

        form = UserNotificationPersonalDataForm(
            form_data,
            instance=instance,
        )

        self.assertTrue(form.is_valid())

        form.save()

        # Sprawdzamy, czy dane zostały poprawnie zapisane
        user = UserNotification.objects.get(pk=instance.pk)

        self.assertEqual(user.full_name, "HTML")
        self.assertEqual(user.company_name, "")
        self.assertEqual(user.phone_number, "")

        # Próbujemy dodać HTML i unicode
        instance = UserNotificationFactory.create()

        form_data = {
            "full_name": "<strong>ółążśźćń</strong>",
            "phone_number": "+48 0(12) *********** kierunkowy 16",
        }

        form = UserNotificationPersonalDataForm(
            form_data,
            instance=instance,
        )

        self.assertTrue(form.is_valid())

        form.save()

        # Sprawdzamy, czy dane zostały poprawnie zapisane
        user = UserNotification.objects.get(pk=instance.pk)

        self.assertEqual(user.full_name, "ółążśźćń")
        self.assertEqual(user.company_name, "")
        self.assertEqual(user.phone_number, "4801212312312316")

        # Teraz wysyłamy pusty formularz - dane powinny zostać wyczyszczone
        form_data = {
            "full_name": "",
            "phone_number": "",
            "company_name": "",
        }

        form = UserNotificationPersonalDataForm(
            form_data,
            instance=instance,
        )

        self.assertTrue(form.is_valid())

        form.save()

        # Sprawdzamy, czy dane zostały poprawnie zapisane
        user = UserNotification.objects.get(pk=instance.pk)

        self.assertEqual(user.full_name, "")
        self.assertEqual(user.company_name, "")
        self.assertEqual(user.phone_number, "")


@override_settings(MAIL_TO_NOTIFICATION_ALERT="<EMAIL>")
class UserNotificationAlertTaskTestCase(ALXTestCase):
    def _test_email_content(
        self,
        email,
        user,
        training,
        location,
        already_subscribed=0,
        already_subscribed_unique=0,
        terms=None,
        min_group_reached=False,
    ):
        subject = email.subject
        body = email.body

        # Sprawdź tytuł maila
        self.assertEqual(subject, "[Powiadomienie Zapis] - {0}".format(training.nazwa))

        # W treści powinien być adres email użytkownika
        self.assertIn(user.email, body)

        # W treści powinien być link do admina filtrowanego po kodzie
        # szkolenia i loaklizacji
        url = "{0}?q={1}&preferences__locations__id__exact={2}".format(
            reverse("admin:www_usernotification_changelist"), training.kod, location.pk
        )
        self.assertIn(url, body)

        # Sprawdź ilość subskrypcji (wszystkich i unikalnych).
        self.assertIn(
            "Zapisanych na powiadomienia: {0}".format(already_subscribed), body
        )
        self.assertIn(
            "Zapisanych na powiadomienia (unikalni względem poniższych "
            "terminów): {0}".format(already_subscribed_unique),
            body,
        )

        if terms:
            # Sprawdzamy, czy są wyszczególnione wszystkie terminy.
            for term in terms:
                self.assertIn(
                    "- {0} ({1}): {2} / {3}".format(
                        location.shortname,
                        term.termin,
                        len(term.uczestnicy_niezrezygnowani()),
                        training.min_grupa,
                    ),
                    body,
                )
        else:
            self.assertIn("Brak.", body)

        # Sprawdź, czy wystepuje dopisek dla Biura
        min_group_reached_text = (
            'UWAGA: Rozważ "ręczne" skontaktowanie się '
            "z zapisanymi na powiadomienia, być może "
            "jest grupa do zebrania!"
        )

        if min_group_reached:
            self.assertIn(min_group_reached_text, body)
        else:
            self.assertNotIn(min_group_reached_text, body)

    def test_new_query(self):
        """
        Symulujemy sytuację, w której wysyłane są alerty podczas dodawania
        nowych notyfikacji lub aktywacji konta.
        """

        user = UserNotificationFactory.create()

        # Dodajemy notyfikację, ale nie stworzoną przez użytkownika
        # (status != www) - w tym przypadku brak alertów.
        locations = [LokalizacjaFactory.create(), LokalizacjaFactory.create()]
        UserCoursesNotificationFactory.create(
            user=user, source="system", locations=locations
        )

        www.tasks.user_notifications_alert.delay(
            user.id,
            new_query_list=www.tasks.notification_qs_to_dict(
                user.preferences.all().prefetch_related("locations")
            ),
        )

        self.assertEqual(len(mail.outbox), 0)

        # Teraz dodajemy notyfikację, która powinna być w alercie.
        course = UserCoursesNotificationFactory.create(user=user, locations=locations)

        www.tasks.user_notifications_alert.delay(
            user.id,
            new_query_list=www.tasks.notification_qs_to_dict(
                user.preferences.all().prefetch_related("locations")
            ),
        )

        self.assertEqual(len(mail.outbox), 1)

        for location in locations:
            self._test_email_content(
                email=mail.outbox[0],
                user=user,
                training=course.training,
                location=location,
                already_subscribed=1,
                already_subscribed_unique=1,
            )

        mail.outbox = []

        # Teraz dodajemy terminy do szkolenia `course.training` w lokalizacji
        # `location`.

        t1 = TerminSzkoleniaFactory.create(
            szkolenie=course.training, lokalizacja=locations[0]
        )

        t2 = TerminSzkoleniaFactory.create(
            szkolenie=course.training, lokalizacja=locations[0]
        )

        # Ten nie powinien być brany pod uwagę, gdyż jest w innej lokalizacji
        TerminSzkoleniaFactory.create(
            szkolenie=course.training, lokalizacja=LokalizacjaFactory.create()
        )

        www.tasks.user_notifications_alert.delay(
            user.id,
            new_query_list=www.tasks.notification_qs_to_dict(
                user.preferences.all().prefetch_related("locations")
            ),
        )

        self.assertEqual(len(mail.outbox), 1)

        self._test_email_content(
            email=mail.outbox[0],
            user=user,
            training=course.training,
            location=locations[0],
            already_subscribed=1,
            already_subscribed_unique=1,
            terms=[t1, t2],
        )

        self._test_email_content(
            email=mail.outbox[0],
            user=user,
            training=course.training,
            location=locations[1],
            already_subscribed=1,
            already_subscribed_unique=1,
        )

        mail.outbox = []

        # Teraz dodajemy kolejnego usera do notyfikacji, który jednocześnie
        # jest Uczestnikiem przyszłego szkolenia - w mailu liczba unikalnych
        # powinna wynosić 1 oraz powinien pojawić się napis
        # "UWAGA: Rozważ "ręczne" skontaktowanie ...".

        user2 = UserNotificationFactory.create()
        UserCoursesNotificationFactory.create(
            user=user2, training=course.training, locations=[locations[0]]
        )
        UczestnikFactory.create(
            email=user2.email,
            termin=t1,
        )

        www.tasks.user_notifications_alert.delay(
            user.id,
            new_query_list=www.tasks.notification_qs_to_dict(
                user.preferences.all().prefetch_related("locations")
            ),
        )

        self.assertEqual(len(mail.outbox), 1)

        self._test_email_content(
            email=mail.outbox[0],
            user=user,
            training=course.training,
            location=locations[0],
            already_subscribed=2,
            already_subscribed_unique=1,
            terms=[t1, t2],
            min_group_reached=True,
        )

        self._test_email_content(
            email=mail.outbox[0],
            user=user,
            training=course.training,
            location=locations[1],
            already_subscribed=1,
            already_subscribed_unique=1,
            min_group_reached=True,
        )

        mail.outbox = []

        # Teraz wysyłamy 4 maile (kazde dla unikalnego szkolenia)
        user = UserNotificationFactory.create()
        for i in range(4):
            UserCoursesNotificationFactory.create(
                user=user, locations=[LokalizacjaFactory.create() for j in range(4)]
            )

        www.tasks.user_notifications_alert.delay(
            user.id,
            new_query_list=www.tasks.notification_qs_to_dict(
                user.preferences.all().prefetch_related("locations")
            ),
        )

        self.assertEqual(len(mail.outbox), 4)

    def test_old_query(self):
        """
        Symulujemy sytuację, w której wysyłane są alerty podczas edycji
        powiadomień przez użytkownika.
        """

        user = UserNotificationFactory.create()

        # Dodajemy notyfikację, ale nie stworzoną przez użytkownika
        # (status != www) - w tym przypadku brak alertów.
        location = LokalizacjaFactory.create()
        UserCoursesNotificationFactory.create(
            user=user, source="system", locations=[location]
        )

        qs_list = www.tasks.notification_qs_to_dict(
            user.preferences.all().prefetch_related("locations")
        )

        www.tasks.user_notifications_alert.delay(user.id, old_query_list=qs_list)

        self.assertEqual(len(mail.outbox), 0)

        # Teraz dodajemy powiadomienie przypisane do 3 lokalizacji i nic z
        # nim nie robimy. Nie uległo zmianie, czyli nie powinno być alertu.

        l1 = LokalizacjaFactory.create()
        l2 = LokalizacjaFactory.create()
        l3 = LokalizacjaFactory.create()
        c1 = UserCoursesNotificationFactory.create(user=user, locations=[l1, l2, l3])

        qs_list = www.tasks.notification_qs_to_dict(
            user.preferences.all().prefetch_related("locations")
        )

        www.tasks.user_notifications_alert.delay(user.id, old_query_list=qs_list)

        self.assertEqual(len(mail.outbox), 0)

        # Usuwamy preferencję `c1` - nie powinno być alertu (o usuniętych
        # obiektach nie informujemy).

        c1.delete()

        www.tasks.user_notifications_alert.delay(user.id, old_query_list=qs_list)

        self.assertEqual(len(mail.outbox), 0)

        # Dodajemy powiadomienie przypisane do 3 lokalizacji. Następnie
        # pobieramy je i cachujemy, a potem zmieniamy (usuwamy jedną
        # lokalizację) - nie powinno być alertu.

        l1 = LokalizacjaFactory.create()
        l2 = LokalizacjaFactory.create()
        l3 = LokalizacjaFactory.create()
        UserCoursesNotificationFactory.create(user=user, locations=[l1, l2, l3])

        qs_list = www.tasks.notification_qs_to_dict(
            user.preferences.all().prefetch_related("locations")
        )

        l1.delete()

        www.tasks.user_notifications_alert.delay(user.id, old_query_list=qs_list)

        self.assertEqual(len(mail.outbox), 0)

        # Dodajemy dwie nowe lokalizacje do preferencji - powinien byc alert.

        l1 = LokalizacjaFactory.create()
        l2 = LokalizacjaFactory.create()
        l3 = LokalizacjaFactory.create()
        c2 = UserCoursesNotificationFactory.create(user=user, locations=[l1, l2, l3])

        qs_list = www.tasks.notification_qs_to_dict(
            user.preferences.all().prefetch_related("locations")
        )

        l4 = LokalizacjaFactory.create()
        l5 = LokalizacjaFactory.create()

        c2.locations.add(l4)
        c2.locations.add(l5)
        c2.save()

        www.tasks.user_notifications_alert.delay(user.id, old_query_list=qs_list)

        self.assertEqual(len(mail.outbox), 1)

        for location in [l4, l5]:
            self._test_email_content(
                email=mail.outbox[0],
                user=user,
                training=c2.training,
                location=location,
                already_subscribed=1,
                already_subscribed_unique=1,
            )

    def test_donotsendthis(self):
        """
        Użytkownicy, których adres email zawiera ciąg "donotsendthis" mają
        być pomijane.
        """

        user = UserNotificationFactory.create(email="<EMAIL>")

        UserCoursesNotificationFactory.create(
            user=user, locations=[LokalizacjaFactory.create()]
        )

        www.tasks.user_notifications_alert.delay(
            user.id, new_query_list=list(user.preferences.all().values())
        )

        self.assertEqual(len(mail.outbox), 0)


@override_settings(MAIL_TO_NOTIFICATION_ALERT="<EMAIL>")
class PoinformujOPotencjalnieChetnychTaskTestCase(ALXTestCase):
    def test_empty_mail_in_prefered_location(self):
        """
        Symulujemy sytuację, w której system nie znjaduje potecjalnie
        chętnych w danej loklizacji.
        """

        termin = TerminSzkoleniaFactory.create()

        www.tasks.poinformuj_o_potencjalnie_chetnych.delay(termin.pk)

        self.assertEqual(len(mail.outbox), 0)

        # Jest juz uczestnikeim
        termin = TerminSzkoleniaFactory.create()

        user = UserNotificationFactory.create()
        UserCoursesNotificationFactory.create(
            training=termin.szkolenie, user=user, locations=[termin.lokalizacja]
        )
        UczestnikFactory.create(email=user.email, termin=termin)

        www.tasks.poinformuj_o_potencjalnie_chetnych.delay(termin.pk)

        self.assertEqual(len(mail.outbox), 0)

    def test_empty_mail_in_other_locations(self):
        """
        Symulujemy sytuację, w której system nie znjaduje potecjalnie
        chętnych w pozostalych lokalziacjach.
        """

        termin = TerminSzkoleniaFactory.create()

        www.tasks.poinformuj_o_potencjalnie_chetnych.delay(termin.pk)

        self.assertEqual(len(mail.outbox), 0)

        # Jest juz uczestnikeim
        termin = TerminSzkoleniaFactory.create()

        user = UserNotificationFactory.create()
        UserCoursesNotificationFactory.create(training=termin.szkolenie, user=user)
        UczestnikFactory.create(email=user.email, termin=termin)

        www.tasks.poinformuj_o_potencjalnie_chetnych.delay(termin.pk)

        self.assertEqual(len(mail.outbox), 0)

    def test_user_in_prefered_location(self):
        # Znajdujemy dopasowanie na podstawie szkolenia i lokalizacji

        termin = TerminSzkoleniaFactory.create()
        UserCoursesNotificationFactory.create_batch(10, training=termin.szkolenie)

        user1 = UserNotificationFactory.create()
        UserCoursesNotificationFactory.create(
            training=termin.szkolenie,
            user=user1,
            locations=[
                termin.lokalizacja,
                LokalizacjaFactory.create(),
                LokalizacjaFactory.create(),
            ],
        )

        user2 = UserNotificationFactory.create()
        UserCoursesNotificationFactory.create(
            training=termin.szkolenie, user=user2, locations=[termin.lokalizacja]
        )

        www.tasks.poinformuj_o_potencjalnie_chetnych.delay(termin.pk)

        self.assertEqual(len(mail.outbox), 1)
        self.assertIn(user1.email, mail.outbox[0].body)
        self.assertIn(user2.email, mail.outbox[0].body)

    def test_user_in_other_locations(self):
        # Znajdujemy dopasowanie na podstawie szkolenia

        termin = TerminSzkoleniaFactory.create()

        user1 = UserNotificationFactory.create()
        UserCoursesNotificationFactory.create(
            training=termin.szkolenie,
            user=user1,
            locations=[LokalizacjaFactory.create(), LokalizacjaFactory.create()],
        )

        user2 = UserNotificationFactory.create()
        uc = UserCoursesNotificationFactory.create(
            training=termin.szkolenie, user=user2
        )

        www.tasks.poinformuj_o_potencjalnie_chetnych.delay(termin.pk)

        self.assertEqual(len(mail.outbox), 1)
        self.assertIn(user1.email, mail.outbox[0].body)
        self.assertIn(user2.email, mail.outbox[0].body)
        self.assertIn(
            "Lokalizacje: {0}".format(uc.locations.all()[0].shortname),
            mail.outbox[0].body,
        )

    def test_user_already_in_prefered_location(self):
        # Jesli user jest w preferowanej lokalizacji nie powinno go byc w
        # pozostalych.

        termin = TerminSzkoleniaFactory.create()

        user1 = UserNotificationFactory.create()
        UserCoursesNotificationFactory.create(
            training=termin.szkolenie,
            user=user1,
            locations=[
                LokalizacjaFactory.create(),
                LokalizacjaFactory.create(),
                termin.lokalizacja,
            ],
        )

        www.tasks.poinformuj_o_potencjalnie_chetnych.delay(termin.pk)

        self.assertEqual(len(mail.outbox), 1)
        self.assertIn("Brak dopasowań w pozostałych lokalizacjach", mail.outbox[0].body)


class PotencjalnieZainteresowaniZInnychMiastTaskTestCase(ALXTestCase):
    def test_no_users(self):
        """
        Symulujemy sytuację, w której system nie znjaduje potecjalnie
        zainteresowanych.
        """

        termin = TerminSzkoleniaFactory.create(odbylo_sie=True)
        www.tasks.potencjalnie_zainteresowani_z_innych_miast.delay(termin.pk)
        self.assertEqual(len(mail.outbox), 0)

    def test_mailing_pl(self):
        termin = TerminSzkoleniaFactory.create(
            odbylo_sie=True, potencjalnie_zainteresowani_mail="hejo!"
        )

        UserCoursesNotificationFactory.create_batch(10, training=termin.szkolenie)
        UserCoursesNotificationFactory.create_batch(20)
        www.tasks.potencjalnie_zainteresowani_z_innych_miast.delay(termin.pk)
        self.assertEqual(len(mail.outbox), 10)
        self.assertEqual(UserNotificationLog.objects.all().count(), 10)

        sent_mail = mail.outbox[0]

        self.assertEqual(sent_mail.subject, "Powiadomienie z firmy ALX (www.alx.pl)")
        self.assertIn("hejo!", sent_mail.body)

    def test_mailing_en(self):
        termin = TerminSzkoleniaEnFactory.create(odbylo_sie=True)

        UserCoursesNotificationFactory.create_batch(3, training=termin.szkolenie)
        www.tasks.potencjalnie_zainteresowani_z_innych_miast.delay(termin.pk)
        self.assertEqual(len(mail.outbox), 3)
        self.assertEqual(UserNotificationLog.objects.all().count(), 3)

        sent_mail = mail.outbox[0]

        self.assertEqual(
            sent_mail.subject, "Notification from ALX Training (www.alx.training)"
        )
        self.assertIn("Hello", sent_mail.body)


###################
# Nieprzeszkoleni
###################


@override_settings(UNTRAINED_USERS_MAX_TRAINING_AGE=180)
class UntrainedManagementTestCase(ALXTestCase):
    def _create_random_terms(
        self,
        count=3,
        term_finished=False,
        date=None,
        loaction=None,
        training=None,
        users_count=0,
        users_status=1,
        clone_objects=False,
        clone_objects_date=None,
        make_upper_cloned_emails=False,
    ):
        """
        Pomocnicza funnkcja tworzy przykładowe terminy wraz z danymi, które
        zastępują te aktualnie znajdujące się w bazie.
        """

        _30_days_ago = datetime.date.today() - datetime.timedelta(days=30)

        terms = []

        for i in range(count):
            terms.append(
                TerminSzkoleniaFactory(
                    termin=date or _30_days_ago,
                    lokalizacja=loaction or LokalizacjaFactory(reklamowana=True),
                    szkolenie=training or SzkolenieFactory(),
                    odbylo_sie=term_finished,
                )
            )

        if users_count:
            for no, term in enumerate(terms):
                for i in range(users_count):
                    UczestnikFactory(
                        termin=term,
                        email="email{0}.{1}@example.com".format(no, i),
                        waluta=WalutaFactory(),
                        status=users_status,
                    )

        if clone_objects and users_count:
            # Stwórz użytkowników przypisanych do innych terminów, ale:
            #   - z tym samym szkoleniem
            #   - tą samą lokalizacją
            #   - tym samym adresem email
            #   - taką samą datą lub `clone_objects_date`
            new_terms = []

            for term in terms:
                new_terms.append(
                    TerminSzkoleniaFactory(
                        termin=clone_objects_date or term.termin,
                        lokalizacja=term.lokalizacja,
                        szkolenie=term.szkolenie,
                        odbylo_sie=True,
                    )
                )

            for no, term in enumerate(new_terms):
                for i in range(users_count):
                    email = "email{0}.{1}@example.com".format(no, i)

                    if make_upper_cloned_emails:
                        email = email.upper()

                    UczestnikFactory(termin=term, email=email, waluta=WalutaFactory())
        return terms

    def test_empty_terms_queryset(self):
        """
        Symuljemy brak pasujących terminów. Nikt nie powinien zostać dodany
        do powiadomień.
        """

        cmd = hunt_the_untrained.Command()
        cmd.handle()

        self.assertEqual(UserNotification.objects.all().count(), 0)

    def test_empty_participants_queryset(self):
        """
        Symuljemy brak uczestników w istniejących terminach. Nikt nie
        powinien zostać dodany do powiadomień.
        """

        self._create_random_terms()

        cmd = hunt_the_untrained.Command()
        cmd.handle()

        self.assertEqual(UserNotification.objects.all().count(), 0)

    def test_invalid_email_address(self):
        """
        Symuljemy błędne adresy email uczestników. Nikt nie
        powinien zostać dodany do powiadomień.
        """

        terms = self._create_random_terms()

        for term in terms:
            UczestnikFactory(
                termin=term,
                email=random.choice(["", "inavlid"]),
                waluta=WalutaFactory(),
            )

        cmd = hunt_the_untrained.Command()
        cmd.handle()

        self.assertEqual(UserNotification.objects.all().count(), 0)

    def test_user_already_trained(self):
        """
        Symuljemy sytuację, w której wszyscy znalezieni użytkownicy
        zostali już przeszkoleni.
        """

        self._create_random_terms(users_count=3, clone_objects=True)

        cmd = hunt_the_untrained.Command()
        cmd.handle()

        self.assertEqual(UserNotification.objects.all().count(), 0)

    def test_user_with_status_4(self):
        """
        Symuljemy sytuację, w której wszyscy znalezieni użytkownicy
        mają status 4 (zrezygnował).
        """

        self._create_random_terms(users_count=3, users_status=4)

        cmd = hunt_the_untrained.Command()
        cmd.handle()

        self.assertEqual(UserNotification.objects.all().count(), 0)

    def test_user_already_in_notification_model(self):
        """
        Symuljemy sytuację, w której wszyscy znalezieni użytkownicy już są
        zapisani na powiadomienia.
        """

        terms = self._create_random_terms(users_count=3)

        # Stwórz obikety notyfikacji. Nieważne są ich status, ani rodzaj
        # powiadomień, jeśli użytkownik tam jest omijamy go.

        for term in terms:
            for user in term.uczestnik_set.all():
                UserNotificationFactory(email=user.email)

        self.assertEqual(UserNotification.objects.all().count(), 9)

        cmd = hunt_the_untrained.Command()
        cmd.handle()

        self.assertEqual(UserNotification.objects.all().count(), 9)

    @patch("www.management.commands.hunt_the_untrained.logger")
    def test_errors_while_saving(self, mock_logger):
        """
        Symuljemy błąd podczas zapisu, nikt nie powinien zostać zapisany, a
        błąd obsłużony.
        """

        today = datetime.date.today()

        self._create_random_terms(
            count=1,
            users_count=1,
            clone_objects=True,
            clone_objects_date=today - datetime.timedelta(days=60),
        )

        with patch(
            "www.models.UserNotification.objects.create",
            MagicMock(side_effect=Exception()),
        ):

            cmd = hunt_the_untrained.Command()
            cmd.handle()

            mock_logger.exception.assert_called_with(
                "Could not add user '<EMAIL>' to notification"
            )

        self.assertEqual(UserNotification.objects.all().count(), 0)

    def test_camel_case_in_email_address(self):
        """
        Symuljemy inaczej zapisane adresy email uczestników, niemniej jednak
        znormalizowane są takie same. Nikt nie powinien zostać dodany do
        powiadomień.
        """

        self._create_random_terms(
            users_count=3, clone_objects=True, make_upper_cloned_emails=True
        )

        cmd = hunt_the_untrained.Command()
        cmd.handle()

        self.assertEqual(UserNotification.objects.all().count(), 0)

    def test_user_already_trained_long_time_ago(self):
        """
        Symuljemy sytuację, w której wszyscy znalezieni użytkownicy
        byli przeszkoleni, ale w terminie pozniejszym niz ostatni nieudany -
        w takiej sytuacji zapisujemy ich na powiadomienia.
        """

        today = datetime.date.today()

        self._create_random_terms(
            users_count=3,
            clone_objects=True,
            clone_objects_date=today - datetime.timedelta(days=60),
        )

        cmd = hunt_the_untrained.Command()
        cmd.handle()

        qs = UserNotification.objects.all()

        self.assertEqual(qs.count(), 9)

        # Wszyscy użytkownicy w źródle `UserCoursesNotification.source`
        # powinni mieć ustawioną wartość "system".

        for e in qs:
            for course in e.preferences.all():
                self.assertEqual(course.source, "system")

    def test_user_in_several_terms(self):
        """
        Symuljemy sytuację, w której jeden użytkownik jest w kilku terminach,
        ale tylko część z nich obyła się później z nim lub bez niego.
        """

        # Tworzymy pierwszy termin, który się nie odbył
        term1 = self._create_random_terms(
            count=1,
            users_count=1,
        )[0]

        user = term1.uczestnik_set.all()[0]

        # Tworzymy drugi termin, który się nie odbył

        term2 = self._create_random_terms(
            count=1,
        )[0]
        UczestnikFactory(termin=term2, email=user.email, waluta=WalutaFactory())

        # Tworzymy trzeci termin, który się nie odbył
        term3 = self._create_random_terms(
            count=1,
        )[0]
        UczestnikFactory(termin=term3, email=user.email, waluta=WalutaFactory())

        # Zakładamy, że:
        #   - termin pierwszy odbył się w późniejszym czasie, ale bez
        #     naszego uczestnika
        #   - termin drugi odbył się w późniejszym czasie, z naszym
        #     użytkownikiem, ale w innej lokalizacji
        #   - termin trzeci odbył się w późniejszym czasie z naszym uczestnikem

        self._create_random_terms(
            count=1,
            training=term1.szkolenie,
            loaction=term1.lokalizacja,
            term_finished=True,
        )

        tmp_term2 = self._create_random_terms(
            count=1, training=term2.szkolenie, term_finished=True
        )[0]

        UczestnikFactory(termin=tmp_term2, email=user.email, waluta=WalutaFactory())

        tmp_term3 = self._create_random_terms(
            count=1,
            training=term3.szkolenie,
            loaction=term3.lokalizacja,
            term_finished=True,
        )[0]

        UczestnikFactory(termin=tmp_term3, email=user.email, waluta=WalutaFactory())

        # Uruchamiamy zadanie
        cmd = hunt_the_untrained.Command()
        cmd.handle()

        # Powinniśmy uzyskać:
        #   - jeden wpis w modelu `UserNotificationFactory`
        #   - jeden wpis w modelu `UserCoursesNotificationFactory`, który
        #     będzie przypisany do jednej lokalizacji

        self.assertEqual(UserNotification.objects.all().count(), 1)
        n_user = UserNotification.objects.all()[0]

        self.assertEqual(n_user.email, user.email)

        self.assertEqual(UserCoursesNotification.objects.filter(user=n_user).count(), 1)

        course = UserCoursesNotification.objects.get(user=n_user)

        self.assertEqual(course.locations.all().count(), 1)

    def test_user_in_several_locations(self):
        """
        Symuljemy sytuację, w której jeden użytkownik jest w jednym terminie i
        różnych lokalizacjach i żaden z terminów się nie obył.
        """

        # Tworzymy pierwszy termin, który się nie odbył
        term1 = self._create_random_terms(
            count=1,
            users_count=1,
        )[0]

        user = term1.uczestnik_set.all()[0]

        # Tworzymy drugi termin, który się nie odbył

        term2 = self._create_random_terms(count=1, training=term1.szkolenie)[0]
        UczestnikFactory(termin=term2, email=user.email, waluta=WalutaFactory())

        # Tworzymy trzeci termin, który się nie odbył
        term3 = self._create_random_terms(count=1, training=term1.szkolenie)[0]
        UczestnikFactory(termin=term3, email=user.email, waluta=WalutaFactory())

        cmd = hunt_the_untrained.Command()
        cmd.handle()

        # Powinniśmy uzyskać:
        #   - jeden wpis w modelu `UserNotificationFactory`
        #   - jeden wpis w modelu `UserCoursesNotificationFactory`, który
        #     będzie przypisany do trzech lokalizacji

        self.assertEqual(UserNotification.objects.all().count(), 1)
        n_user = UserNotification.objects.all()[0]

        self.assertEqual(n_user.email, user.email)

        self.assertEqual(UserCoursesNotification.objects.filter(user=n_user).count(), 1)

        course = UserCoursesNotification.objects.get(user=n_user)

        self.assertEqual(course.locations.all().count(), 3)

    def test_user_personal_data(self):
        """
        Testujemy zapisywanie prywatnych danych uczestnika.
        """

        # Tworzymy pierwszy termin, który się nie odbył
        term1 = self._create_random_terms(
            count=1,
            users_count=1,
        )[0]

        user = term1.uczestnik_set.all()[0]
        user.imie_nazwisko = "Imię nazwisko"
        user.telefon = "*********"
        user.faktura_firma = "Firma"
        user.save()

        cmd = hunt_the_untrained.Command()
        cmd.handle()

        n_user = UserNotificationFactory._meta.model.objects.all()[0]

        self.assertEqual(n_user.email, user.email)
        self.assertEqual(n_user.full_name, "Imię nazwisko")
        self.assertEqual(n_user.phone_number, "*********")
        self.assertEqual(n_user.company_name, "Firma")

    def test_user_personal_data__company(self):
        """
        Testujemy zapisywanie frmowych danych uczestnika.
        """

        # Tworzymy pierwszy termin, który się nie odbył
        term1 = self._create_random_terms(
            count=1,
            users_count=1,
        )[0]

        user = term1.uczestnik_set.all()[0]
        user.imie_nazwisko = "Imię nazwisko, Imię2 nazwisko2"
        user.uczestnik_wieloosobowy_ilosc_osob = 2
        user.telefon = "*********"
        user.faktura_firma = "Firma"
        user.save()

        cmd = hunt_the_untrained.Command()
        cmd.handle()

        n_user = UserNotificationFactory._meta.model.objects.all()[0]

        self.assertEqual(n_user.email, user.email)
        self.assertEqual(n_user.full_name, "Firma")
        self.assertEqual(n_user.phone_number, "*********")
        self.assertEqual(n_user.company_name, "Firma")

    def test_cancelled_term_in_the_future(self):
        """
        Testujemy zapisywanie Uczestników z anulowanych terminów z przyszłości.
        """

        term = TerminSzkoleniaFactory.create(
            termin=datetime.date.today() + datetime.timedelta(days=5),
            odbylo_sie=False,
            zamkniete=False,
            lokalizacja__reklamowana=True,
        )
        UczestnikFactory.create(termin=term)

        self.assertEqual(UserNotification.objects.all().count(), 0)

        cmd = hunt_the_untrained.Command()
        cmd.handle()

        self.assertEqual(UserNotification.objects.all().count(), 1)

    def test_cancelled_term_in_the_future_and_the_past(self):
        """
        Testujemy zapisywanie Uczestników z anulowanych terminów z
        przyszłości i przeszłości.
        """

        term1 = TerminSzkoleniaFactory.create(
            termin=datetime.date.today() + datetime.timedelta(days=5),
            odbylo_sie=False,
            zamkniete=False,
            lokalizacja__reklamowana=True,
        )
        UczestnikFactory.create(termin=term1)

        term2 = TerminSzkoleniaFactory.create(
            termin=datetime.date.today() - datetime.timedelta(days=5),
            odbylo_sie=False,
            lokalizacja__reklamowana=True,
        )
        UczestnikFactory.create_batch(2, termin=term2)

        # Tworzymy szum :-)
        UczestnikFactory.create_batch(10)

        self.assertEqual(UserNotification.objects.all().count(), 0)

        cmd = hunt_the_untrained.Command()
        cmd.handle()

        self.assertEqual(UserNotification.objects.all().count(), 3)

    def test_email_opt_out(self):
        term1 = self._create_random_terms(
            count=1,
            users_count=1,
        )[0]

        user = term1.uczestnik_set.all()[0]
        OptOutFactory.create(email=user.email)

        cmd = hunt_the_untrained.Command()
        cmd.handle()

        self.assertEqual(UserNotification.objects.all().count(), 0)


###########
# Assets
###########


class AssetTestCase(TestCase):
    def test_deleting_file(self):
        # Podczas usuwania plik pownien zostać trwale usunięty z dysku.

        asset = AssetFactory.create()
        fiel_path = asset.file.path

        self.assertTrue(fiel_path)
        self.assertTrue(os.path.exists(fiel_path))

        asset.delete()

        self.assertFalse(os.path.exists(fiel_path))


###############
# Utils i api
###############


class UtilsTestCase(APIZliczaczPatch, ALXTestCase):
    def test_parse_pl_address(self):
        street, house_number, flat_number = utils.parse_pl_address("ul. Żółta 13")
        self.assertEqual(street, "ul. Żółta")
        self.assertEqual(house_number, "13")
        self.assertEqual(flat_number, "")

        street, house_number, flat_number = utils.parse_pl_address(
            "ul. Aleja Jana Pawła 2 13lok.6ABC"
        )
        self.assertEqual(street, "ul. Aleja Jana Pawła 2")
        self.assertEqual(house_number, "13")
        self.assertEqual(flat_number, "6abc")

    def test_is_html(self):
        self.assertFalse(utils.is_html("ółążś¢€½²²²²\n\n\n\ndddd"))
        self.assertTrue(utils.is_html("<li>text</li>"))
        self.assertTrue(utils.is_html("<li    >text</li>"))

    def test_slugify_file_name(self):
        self.assertEqual(
            my_slugify.slugify_file_name("óÓłGS%#QWE888-8_8"), "oOlGS-QWE888-8_8"
        )

        self.assertEqual(my_slugify.slugify_file_name("123456789"), "123456789")

        self.assertEqual(my_slugify.slugify_file_name("-_-_-___--"), "-_-_-___--")

        self.assertEqual(my_slugify.slugify_file_name("$"), "-")

        self.assertEqual(my_slugify.slugify_file_name("a-b-c-d-e__"), "a-b-c-d-e__")

    def test_get_upload_path_for_assets(self):
        filename = "super file ółż.pdf"
        path = utils.get_upload_path_for_assets(None, filename)
        self.assertEqual(path, "super-file-olz.pdf")

        filename = ".jpg"
        path = utils.get_upload_path_for_assets(None, filename)
        self.assertEqual(path, "-jpg.ext")

        filename = "data.tar.bz2"
        path = utils.get_upload_path_for_assets(None, filename)
        self.assertEqual(path, "data.tar.bz2")

        filename = ""
        path = utils.get_upload_path_for_assets(None, filename)
        self.assertEqual(len(path.split(".")[0]), 32)

        filename = "myname12OL"
        path = utils.get_upload_path_for_assets(None, filename)
        self.assertEqual(path, "myname12OL.ext")

    @patch("www.utils.umowa_ratalna_word")
    def test_generate_umowa_ratalna_za_kurs_zaplace_3(self, word):
        word.side_efect = MagicMock()

        uczestnik = UczestnikFactory.create(
            za_kurs_zaplace=3, kwota_do_zaplaty=Decimal(1765)
        )
        utils.generate_umowa_ratalna(uczestnik)

        data = word.call_args[0][0]

        # Sprawdzamy dane rat
        self.assertEqual(data["zaliczka_status_platnosci"], "płatna do dnia")
        self.assertEqual(
            data["zaliczka_termin"], uczestnik.zaliczka_termin.strftime("%d.%m.%Y")
        )

        self.assertEqual(data["1_rata_cena"], "835.48")
        self.assertEqual(data["1_rata_status_platnosci"], "płatna do dnia")
        self.assertEqual(
            data["1_rata_termin"], uczestnik.rata1_termin.strftime("%d.%m.%Y")
        )

        self.assertEqual(data["2_rata_cena"], "835.48")
        self.assertEqual(data["2_rata_status_platnosci"], "płatna do dnia")
        self.assertEqual(
            data["2_rata_termin"], uczestnik.rata2_termin.strftime("%d.%m.%Y")
        )

        self.assertEqual(data["cena_brutto"], "2170.96")

        self.assertEqual(
            data["cena_brutto_slownie"],
            "dwa tysiące sto siedemdziesiąt złotych, dziewięćdziesiąt " "sześć groszy",
        )

        # Oznaczamy wszystkie raty jako oplacone
        uczestnik.zaliczka_zaplacone = datetime.date(2015, 11, 30)
        uczestnik.rata1_zaplacone = datetime.date(2015, 11, 30)
        uczestnik.rata2_zaplacone = datetime.date(2015, 11, 30)
        uczestnik.save()

        utils.generate_umowa_ratalna(uczestnik)

        data = word.call_args[0][0]

        # Sprawdzamy dane rat
        self.assertEqual(data["zaliczka_status_platnosci"], "zapłacona w dniu")
        self.assertEqual(data["zaliczka_termin"], "30.11.2015")

        self.assertEqual(data["1_rata_status_platnosci"], "zapłacona w dniu")
        self.assertEqual(data["1_rata_termin"], "30.11.2015")

        self.assertEqual(data["2_rata_status_platnosci"], "zapłacona w dniu")
        self.assertEqual(data["2_rata_termin"], "30.11.2015")

    @patch("www.utils.umowa_ratalna_word")
    def test_generate_umowa_ratalna_za_kurs_zaplace_10(self, word):
        word.side_efect = MagicMock()

        uczestnik = UczestnikFactory.create(
            za_kurs_zaplace=10, kwota_do_zaplaty=Decimal(1123)
        )
        utils.generate_umowa_ratalna(uczestnik)

        data = word.call_args[0][0]

        # Sprawdzamy dane rat
        self.assertEqual(data["1_rata_cena"], "276.26")
        self.assertEqual(data["1_rata_status_platnosci"], "płatna do dnia")
        self.assertEqual(
            data["1_rata_termin"], uczestnik.zaliczka_termin.strftime("%d.%m.%Y")
        )

        self.assertEqual(data["2_rata_cena"], "276.26")
        self.assertEqual(data["2_rata_status_platnosci"], "płatna do dnia")
        self.assertEqual(
            data["2_rata_termin"], uczestnik.rata1_termin.strftime("%d.%m.%Y")
        )

        self.assertEqual(data["3_rata_cena"], "276.26")
        self.assertEqual(data["3_rata_status_platnosci"], "płatna do dnia")
        self.assertEqual(
            data["3_rata_termin"], uczestnik.rata2_termin.strftime("%d.%m.%Y")
        )

        self.assertEqual(data["4_rata_cena"], "276.26")
        self.assertEqual(data["4_rata_status_platnosci"], "płatna do dnia")
        self.assertEqual(
            data["4_rata_termin"], uczestnik.rata3_termin.strftime("%d.%m.%Y")
        )

        self.assertEqual(data["5_rata_cena"], "276.26")
        self.assertEqual(data["5_rata_status_platnosci"], "płatna do dnia")
        self.assertEqual(
            data["5_rata_termin"], uczestnik.rata4_termin.strftime("%d.%m.%Y")
        )

        self.assertEqual(data["cena_brutto"], "1381.30")

        self.assertEqual(
            data["cena_brutto_slownie"],
            "tysiąc trzysta osiemdziesiąt jeden złotych, trzydzieści groszy",
        )

        # Oznaczamy wszystkie raty jako oplacone
        uczestnik.zaliczka_zaplacone = datetime.date(2015, 11, 30)
        uczestnik.rata1_zaplacone = datetime.date(2015, 11, 30)
        uczestnik.rata2_zaplacone = datetime.date(2015, 11, 30)
        uczestnik.rata3_zaplacone = datetime.date(2015, 11, 30)
        uczestnik.rata4_zaplacone = datetime.date(2015, 11, 30)
        uczestnik.save()

        utils.generate_umowa_ratalna(uczestnik)

        data = word.call_args[0][0]

        # Sprawdzamy dane rat
        self.assertEqual(data["1_rata_status_platnosci"], "zapłacona w dniu")
        self.assertEqual(data["1_rata_termin"], "30.11.2015")

        self.assertEqual(data["2_rata_status_platnosci"], "zapłacona w dniu")
        self.assertEqual(data["2_rata_termin"], "30.11.2015")

        self.assertEqual(data["3_rata_status_platnosci"], "zapłacona w dniu")
        self.assertEqual(data["3_rata_termin"], "30.11.2015")

        self.assertEqual(data["4_rata_status_platnosci"], "zapłacona w dniu")
        self.assertEqual(data["4_rata_termin"], "30.11.2015")

        self.assertEqual(data["5_rata_status_platnosci"], "zapłacona w dniu")
        self.assertEqual(data["5_rata_termin"], "30.11.2015")

    @override_settings(GENERATE_INVOICE_THROUGH_API_ZLICZACZ=True)
    @patch("requests.post")
    @patch("requests.get")
    def test_generate_invoice(self, mock_get, mock_post):
        mock_get.side_effect = self.mock_requests_get
        mock_post.side_effect = self.mock_requests_post

        uczestnik = UczestnikFactory.create(termin__termin=datetime.date(2015, 1, 1))

        result, errors = utils.generate_invoice(uczestnik, proforma=False)
        self.assertTrue(result)
        self.assertIsNone(errors)

        # Użytkownik powinien mieć zapisane ID faktury, termin płatności,
        # numer faktury oraz jej status
        e = Uczestnik.objects.get(pk=uczestnik.pk)
        self.assertEqual(e.zliczacz_faktura_no, "99999")
        self.assertEqual(e.nr_faktury, "1/2/3")
        self.assertEqual(e.termin_zaplaty, datetime.date.today())
        self.assertEqual(e.faktura_status, "ready")

    @override_settings(GENERATE_INVOICE_THROUGH_API_ZLICZACZ=True)
    @patch("requests.post")
    @patch("requests.get")
    def test_generate_invoice_paid_status(self, mock_get, mock_post):
        mock_get.side_effect = self.mock_requests_get

        self.response_post_json = {"status_faktury": 4}

        mock_post.side_effect = self.mock_requests_post

        uczestnik = UczestnikFactory.create(
            termin__termin=datetime.date(2015, 1, 1), termin__odbylo_sie=True
        )

        result, errors = utils.generate_invoice(uczestnik, proforma=False)
        self.assertTrue(result)
        self.assertIsNone(errors)

        # Użytkownik powinien mieć zapisane ID faktury, termin płatności,
        # numer faktury oraz jej status
        e = Uczestnik.objects.get(pk=uczestnik.pk)
        self.assertEqual(e.zliczacz_faktura_no, "99999")
        self.assertEqual(e.nr_faktury, "1/2/3")
        self.assertEqual(e.termin_zaplaty, datetime.date.today())
        self.assertEqual(e.faktura_status, "paid")

    @override_settings(GENERATE_INVOICE_THROUGH_API_ZLICZACZ=True)
    @patch("requests.post")
    @patch("requests.get")
    def test_generate_invoice_zaliczka_paid_status(self, mock_get, mock_post):
        mock_get.side_effect = self.mock_requests_get

        self.response_post_json = {"status_faktury": 4}

        mock_post.side_effect = self.mock_requests_post

        uczestnik = UczestnikFactory.create(termin__termin=datetime.date(2015, 1, 1))

        result, errors = utils.generate_invoice(uczestnik, proforma=False)
        self.assertTrue(result)
        self.assertIsNone(errors)

        # Użytkownik powinien mieć zapisane ID faktury, termin płatności,
        # numer faktury oraz jej status
        e = Uczestnik.objects.get(pk=uczestnik.pk)
        self.assertEqual(e.zliczacz_faktura_no, "99999")
        self.assertEqual(e.nr_faktury, "1/2/3")
        self.assertEqual(e.termin_zaplaty, datetime.date.today())
        self.assertEqual(e.faktura_status, "zaliczka_paid")

    @override_settings(
        GENERATE_INVOICE_THROUGH_API_ZLICZACZ=True,
        MAIL_PLATNOSCI_MONITOR="<EMAIL>",
    )
    @patch("requests.post")
    @patch("requests.get")
    def test_send_invoice(self, mock_get, mock_post):
        mock_get.side_effect = self.mock_requests_get
        mock_post.side_effect = self.mock_requests_post

        uczestnik = UczestnikFactory.create(
            termin__termin=datetime.date(2015, 1, 1), email_ksiegowosc="<EMAIL>"
        )

        result, errors = utils.generate_invoice(uczestnik, proforma=False)
        self.assertTrue(result)

        result, errors = utils.send_invoice(
            uczestnik, "Tytuł maila", "Treść maila", [], ["<EMAIL>"]
        )

        # Informacja o fakturze i faktura w mailu
        self.assertEqual("Tytuł maila", mail.outbox[0].subject)
        self.assertEqual("Treść maila", mail.outbox[0].body)
        self.assertEqual(len(mail.outbox[0].attachments), 1)

        self.assertEqual(mail.outbox[0].to, ["<EMAIL>"])
        self.assertEqual(mail.outbox[0].cc, ["<EMAIL>"])
        self.assertEqual(mail.outbox[0].bcc, ["<EMAIL>"])

    def test_send_agreement(self):
        uczestnik = UczestnikFactory.create(
            termin__termin=datetime.date(2015, 1, 1),
            za_kurs_zaplace=10,
            email_ksiegowosc="<EMAIL>",
        )

        utils.send_agreement(
            uczestnik, "Tytuł maila", "Treść maila", [], ["<EMAIL>"]
        )

        # Informacja o fakturze i faktura w mailu
        self.assertEqual("Tytuł maila", mail.outbox[0].subject)
        self.assertEqual("Treść maila", mail.outbox[0].body)
        self.assertEqual(len(mail.outbox[0].attachments), 1)

        self.assertEqual(mail.outbox[0].to, ["<EMAIL>"])
        self.assertEqual(mail.outbox[0].cc, ["<EMAIL>"])
        self.assertEqual(mail.outbox[0].bcc, ["<EMAIL>"])

    @override_settings(MAIL_PLATNOSCI_MONITOR="<EMAIL>")
    @patch("www.utils.generate_invoice_note_pdf")
    def test_send_invoice_note(self, mock_pdf):
        mock_pdf.return_value = BytesIO(b"test"), "filename"

        uczestnik = UczestnikFactory.create(email_ksiegowosc="<EMAIL>")
        FakturaKorektaFactory.create(uczestnik=uczestnik)

        __, __ = utils.send_invoice_note(
            uczestnik, "Tytuł maila", "Treść maila", [], ["<EMAIL>"]
        )

        # Informacja o fakturze i faktura w mailu
        self.assertEqual("Tytuł maila", mail.outbox[0].subject)
        self.assertEqual("Treść maila", mail.outbox[0].body)
        self.assertEqual(len(mail.outbox[0].attachments), 1)

        self.assertEqual(mail.outbox[0].to, ["<EMAIL>"])
        self.assertEqual(mail.outbox[0].cc, ["<EMAIL>"])
        self.assertEqual(mail.outbox[0].bcc, ["<EMAIL>"])

    @override_settings(GENERATE_INVOICE_THROUGH_API_ZLICZACZ=True)
    @patch("requests.post")
    @patch("requests.get")
    def test_generate_invoice_failed(self, mock_get, mock_post):
        """
        Błąd podczas generowania proforma.
        """

        mock_get.side_effect = self.mock_requests_get
        mock_post.side_effect = self.mock_requests_post

        self.response_post_status_code = 400
        self.response_post_text = "{'details': 'error'}"

        uczestnik = UczestnikFactory.create()

        result, errors = utils.generate_invoice(uczestnik)
        self.assertFalse(result)
        self.assertEqual(errors, "{'details': 'error'}")

        # Użytkownik nie powinien mieć zapisane ID faktury, numeru faktury
        e = Uczestnik.objects.get(pk=uczestnik.pk)
        self.assertEqual(e.zliczacz_proforma_no, None)
        self.assertFalse(e.nr_proformy)

    @override_settings(GENERATE_INVOICE_THROUGH_API_ZLICZACZ=True)
    @patch("requests.post")
    @patch("requests.get")
    def test_send_invoice_failed(self, mock_get, mock_post):
        """
        Błąd podczas wysyłania proforma.
        """

        mock_get.side_effect = self.mock_requests_get
        mock_post.side_effect = self.mock_requests_post

        self.response_get_status_code = 400
        self.response_get_text = "{'details': 'error'}"

        uczestnik = UczestnikFactory.create()

        result, errors = utils.send_invoice(uczestnik, "Tytuł", "Treść", [], [])
        self.assertFalse(result)
        self.assertEqual(errors, "{'details': 'error'}")
        self.assertEqual(len(mail.outbox), 0)

    @override_settings(GENERATE_INVOICE_THROUGH_API_ZLICZACZ=True)
    @patch("requests.get")
    def test_check_invoice_data(self, mock_get):
        self.response_get_json = {
            "count": 1,
            "results": [FakturaKorektaTestCase.get_api_data()],
        }
        mock_get.side_effect = self.mock_requests_get

        uczestnik = UczestnikFactory.create()

        result, errors = utils.check_invoice_data("123", uczestnik=uczestnik)
        self.assertFalse(errors)
        self.assertTrue(result)
        self.assertEqual(result["uczestnik"], uczestnik)

    @override_settings(GENERATE_INVOICE_THROUGH_API_ZLICZACZ=True)
    @patch("requests.get")
    def test_check_invoice_data_failed(self, mock_get):
        """
        Błąd podczas pobierania faktury.
        """

        mock_get.side_effect = self.mock_requests_get

        self.response_get_status_code = 400
        self.response_get_text = "{'details': 'error'}"

        result, errors = utils.check_invoice_data("123")
        self.assertFalse(result)
        self.assertEqual(errors, "{'details': 'error'}")

        self.response_get_status_code = 200
        self.response_get_text = None

        self.response_get_json = {"count": 0, "results": []}
        result, errors = utils.check_invoice_data("123")
        self.assertFalse(result)
        self.assertEqual(errors, "Faktura nie została znaleziona.")

        self.response_get_json = {"count": 2, "results": []}
        result, errors = utils.check_invoice_data("123")
        self.assertFalse(result)
        self.assertIn("Zbyt dużo dopasowań dla tego numeru faktury/ID.", errors)

    @override_settings(GENERATE_INVOICE_THROUGH_API_ZLICZACZ=True)
    @patch("requests.get")
    def test_generate_invoice_note_data(self, mock_get):
        self.response_get_json = {
            "count": 1,
            "results": [FakturaKorektaTestCase.get_api_data()],
        }
        mock_get.side_effect = self.mock_requests_get

        uczestnik = UczestnikFactory.create()

        result, errors = utils.generate_invoice_note_data("123", uczestnik=uczestnik)
        self.assertFalse(errors)
        self.assertTrue(result)
        self.assertEqual(result["uczestnik"], uczestnik)

    @override_settings(GENERATE_INVOICE_THROUGH_API_ZLICZACZ=True)
    @patch("requests.get")
    def test_generate_invoice_note_data_failed(self, mock_get):
        """
        Błąd podczas generowania faktury korygującej.
        """

        mock_get.side_effect = self.mock_requests_get

        self.response_get_status_code = 400
        self.response_get_text = "{'details': 'error'}"

        result, errors = utils.generate_invoice_note_data("123")
        self.assertFalse(result)
        self.assertEqual(errors, "{'details': 'error'}")

        self.response_get_status_code = 200
        self.response_get_text = None

        self.response_get_json = {"count": 0, "results": []}
        result, errors = utils.generate_invoice_note_data("123")
        self.assertFalse(result)
        self.assertEqual(errors, "Faktura nie została znaleziona.")

        self.response_get_json = {"count": 2, "results": []}
        result, errors = utils.generate_invoice_note_data("123")
        self.assertFalse(result)
        self.assertIn("Zbyt dużo dopasowań dla tego numeru faktury/ID.", errors)

        self.response_get_json = {"count": 1, "results": [{}]}
        mock_get.side_effect = self.mock_requests_get
        result, errors = utils.generate_invoice_note_data("123")
        self.assertFalse(result)
        self.assertEqual(errors, "Faktura nie ma żadnej pozycji.")

        self.response_get_json = {
            "count": 1,
            "results": [
                {
                    "pozycje_faktury": [{}],
                    "typ_faktury": 2,
                }
            ],
        }
        result, errors = utils.generate_invoice_note_data("123")
        self.assertFalse(result)
        self.assertEqual(errors, "Nie mozesz wystawić korekty do faktury " "Pro-Forma.")

    def test_two_working_days_before_start_when_monday(self):
        result = utils.two_working_days_before_start(datetime.date(2018, 4, 23))

        self.assertEqual(result[0], datetime.date(2018, 4, 25))

    def test_two_working_days_before_start_when_thursday(self):
        result = utils.two_working_days_before_start(datetime.date(2018, 4, 26))

        self.assertEqual(len(result), 3)

    def test_two_working_days_before_start_when_friday(self):
        result = utils.two_working_days_before_start(datetime.date(2018, 4, 27))

        self.assertEqual(result[0], datetime.date(2018, 5, 1))

    def test_email_signing(self):
        value = utils.email_signing("<EMAIL>")

        self.assertTrue(value)

        self.assertEqual(signing.loads(value), "<EMAIL>")

    def test_random_discount_code(self):
        value = utils.random_discount_code(length=6)

        self.assertEqual(len(value), 6)
        self.assertIsInstance(value, str)


#################
# Discount codes
#################


class DiscountCodeTestCase(ALXTestCase):
    def test_generate_code_when_no_objects(self):
        with self.assertNumQueries(1):
            code = DiscountCode.objects.generate_code()

        self.assertTrue(code)
        self.assertEqual(len(code), 6)

    def test_generate_code_when_objects(self):
        DiscountCodeFactory.create_batch(10)

        # no chance that there will be a next query
        with self.assertNumQueries(1):
            code = DiscountCode.objects.generate_code()

        self.assertTrue(code)

    @patch("www.models.random_discount_code")
    def test_generate_code_when_no_free_codes(self, rdc_patched):
        rdc_patched.return_value = "123456"

        DiscountCodeFactory.create(code="123456")

        with self.assertNumQueries(10):
            with self.assertRaisesMessage(
                Exception, "Could not generate a discount code."
            ):
                DiscountCode.objects.generate_code()

    def test_to_decimal(self):
        discount_code = DiscountCodeFactory.create(discount=Decimal("12.12"))

        self.assertEqual(discount_code.to_decimal(), Decimal("0.8788"))

    def test_active_when_no_codes(self):
        codes = DiscountCode.objects.active()
        self.assertEqual(codes.count(), 0)

    def test_active_when_date_in_the_future(self):
        DiscountCodeFactory.create()

        with freeze_time("2017-01-01"):
            codes = DiscountCode.objects.active()
            self.assertEqual(codes.count(), 0)

    def test_active_when_is_active_flag_is_false(self):
        DiscountCodeFactory.create(is_active=False)

        codes = DiscountCode.objects.active()
        self.assertEqual(codes.count(), 0)

    def test_active_when_date_in_the_future_but_no_date_from(self):
        DiscountCodeFactory.create_batch(1)
        DiscountCodeFactory.create_batch(2, available_from=None)

        with freeze_time("2017-01-01"):
            codes = DiscountCode.objects.active()
            self.assertEqual(codes.count(), 2)

    def test_active_when_date_in_the_future_but_no_date_to(self):
        DiscountCodeFactory.create_batch(1)
        DiscountCodeFactory.create_batch(2, available_to=None)

        year = datetime.datetime.now().year + 10

        with freeze_time("{}-01-01".format(year)):
            codes = DiscountCode.objects.active()
            self.assertEqual(codes.count(), 2)

    def test_active_when_no_dates(self):
        DiscountCodeFactory.create_batch(1)
        DiscountCodeFactory.create_batch(2, available_from=None, available_to=None)

        with freeze_time("2017-01-01"):
            codes = DiscountCode.objects.active()
            self.assertEqual(codes.count(), 2)

    def test_get_active_when_no_code(self):
        self.assertIsNone(DiscountCode.objects.get_active("abc"))

    def test_get_active_when_code_expired(self):
        DiscountCodeFactory.create(code="abc")

        with freeze_time("2017-01-01"):
            self.assertIsNone(DiscountCode.objects.get_active("abc"))

    def test_get_active_when_limit_used(self):
        discount_code = DiscountCodeFactory.create(code="abc", limit=2)
        UczestnikFactory.create_batch(2, discount_code=discount_code)

        self.assertIsNone(DiscountCode.objects.get_active("abc"))

    def test_get_active_when_limit_is_zero(self):
        DiscountCodeFactory.create(code="abc", limit=0)

        self.assertIsNone(DiscountCode.objects.get_active("abc"))

    def test_get_active_when_limit_is_null(self):
        discount_code = DiscountCodeFactory.create(code="abc", limit=None)
        UczestnikFactory.create_batch(2, discount_code=discount_code)

        result = DiscountCode.objects.get_active("abc")
        self.assertEqual(result, discount_code)
        self.assertEqual(result.used_count, 2)

    def test_get_active_when_limit_not_used(self):
        discount_code = DiscountCodeFactory.create(code="abc", limit=3)
        UczestnikFactory.create_batch(2, discount_code=discount_code)

        result = DiscountCode.objects.get_active("abc")
        self.assertEqual(result, discount_code)
        self.assertEqual(result.used_count, 2)

    def test_get_active_when_many_objects(self):
        # -- not related
        DiscountCodeFactory.create_batch(10)
        for i in range(20):
            UczestnikFactory.create(discount_code=DiscountCodeFactory.create())
        # ----------------

        discount_code1 = DiscountCodeFactory.create(code="abc", limit=3)
        UczestnikFactory.create(discount_code=discount_code1)

        discount_code2 = DiscountCodeFactory.create(code="abc2", limit=1)
        UczestnikFactory.create(discount_code=discount_code2)

        result = DiscountCode.objects.get_active("abc")
        self.assertEqual(result, discount_code1)
        self.assertEqual(result.used_count, 1)

        self.assertIsNone(DiscountCode.objects.get_active("abc2"))

    def test_get_graduate_if_graduate_does_not_exist(self):
        GraduateFactory.create_batch(3)  # not related

        discount_code = DiscountCodeFactory.create()
        self.assertIsNone(discount_code.get_graduate())

    def test_get_graduate_if_graduate_exists(self):
        discount_code = DiscountCodeFactory.create()
        graduate = GraduateFactory.create(discount_code=discount_code)
        GraduateFactory.create_batch(3, term=graduate.term)  # not related
        self.assertEqual(discount_code.get_graduate(), graduate)


#######################
# Office notifications
#######################


@override_settings(DOMENY_DLA_JEZYKOW={"pl": "www.alx.dev"})
class OfficeNotificationsManagementTestCase(ALXTestCase):
    def test_empty_scenarios(self):
        cmd = office_notifications.Command()
        cmd.handle()

        # Nie powinno byc zadnych logow, ani wyslanych maili
        self.assertEqual(TerminSzkoleniaLog.objects.all().count(), 0)
        self.assertEqual(len(mail.outbox), 0)

    def test_non_empty_scenarios(self):
        with freeze_time("2018-04-29"):
            TerminSzkoleniaFactory.create(termin=datetime.date.today(), odbylo_sie=True)

        with freeze_time("2018-04-26"):
            cmd = office_notifications.Command()
            cmd.handle()

        self.assertEqual(len(mail.outbox), 1)

    def test_non_empty_scenarios_with_use(self):
        with freeze_time("2018-04-29"):
            t1 = TerminSzkoleniaFactory.create(termin=datetime.date.today(), odbylo_sie=True)
            u1 = UczestnikFactory.create(termin=t1, zaliczka_zaplacone=None, status=1)
            u2 = UczestnikFactory.create(termin=t1, zaliczka_zaplacone=None, status=4)

            t2 = TerminSzkoleniaFactory.create(termin=datetime.date.today(), odbylo_sie=True)
            u3 = UczestnikFactory.create(termin=t2, zaliczka_zaplacone=datetime.date.today())
            u4 = UczestnikFactory.create(termin=t2, zaliczka_zaplacone=None)

        with freeze_time("2018-04-26"):
            cmd = office_notifications.Command()
            cmd.handle()

        self.assertEqual(len(mail.outbox), 1)        
        self.assertIn(str(u1), mail.outbox[0].body)        
        self.assertNotIn(str(u2), mail.outbox[0].body)        
        self.assertIn(str(u4), mail.outbox[0].body)        
        self.assertNotIn(str(u3), mail.outbox[0].body)                


##################
# Grupa rusza
##################


@override_settings(
    DOMENY_DLA_JEZYKOW={"pl": "www.alx.dev", "en": "www.alx-training.co.dev"}
)
class GrupaRuszaManagementTestCase(ALXTestCase):
    def test_empty_scenarios(self):
        """
        Symuljemy brak uczestników w istniejących terminach. Nikt nie
        powinien zostać dodany do powiadomień.
        """

        cmd = grupa_rusza.Command()
        cmd.handle()

        # Nie powinno byc zadnych logow, ani wyslanych maili
        self.assertEqual(TerminSzkoleniaLog.objects.all().count(), 0)
        self.assertEqual(len(mail.outbox), 0)

    def test_empty_scenarios_for_prawie_gotowa(self):
        """
        Symuljemy brak uczestników w istniejących terminach dla przypadku
        Prawie Gotowa.
        """

        # --- Za malo uczestnikow - powinno byc 4
        termin = TerminSzkoleniaFactory.create(
            szkolenie__min_grupa_bazowa=5,
            termin=datetime.date.today() + datetime.timedelta(days=10),
        )
        UczestnikFactory.create_batch(2, termin=termin)

        cmd = grupa_rusza.Command()
        cmd.handle()

        # Nie powinno byc zadnych logow, ani wyslanych maili
        self.assertEqual(len(mail.outbox), 0)

        # --- Juz jest w logach
        Uczestnik.objects.all().delete()
        TerminSzkolenia.objects.all().delete()
        termin = TerminSzkoleniaFactory.create(
            szkolenie__min_grupa_bazowa=5,
            termin=datetime.date.today() + datetime.timedelta(days=30),
        )
        UczestnikFactory.create_batch(4, termin=termin)
        TerminSzkoleniaLog.objects.all().delete()
        TerminSzkoleniaLogFactory.create(
            term=termin, log_type="grupa_rusza_prawie_zebrana"
        )

        cmd = grupa_rusza.Command()
        cmd.handle()

        # Nie powinno byc zadnych logow, ani wyslanych maili
        self.assertEqual(len(mail.outbox), 0)

    @patch("www.models.TerminSzkolenia.objects.get_for_grupa_rusza")
    def test_empty_scenarios_for_gotowa(self, mock_model):
        """
        Symuljemy brak uczestników w istniejących terminach dla przypadku
        Gotowa.
        """

        # --- Za malo uczestnikow - powinno byc min. 6
        termin = TerminSzkoleniaFactory.create(
            szkolenie__min_grupa_bazowa=5,
            termin=datetime.date.today() + datetime.timedelta(days=10),
        )
        UczestnikFactory.create_batch(5, termin=termin)

        cmd = grupa_rusza.Command()
        cmd.handle()

        # Nie powinno byc zadnych logow, ani wyslanych maili
        self.assertEqual(len(mail.outbox), 0)

        # --- Juz jest w logach (jako drugi mail)
        Uczestnik.objects.all().delete()
        TerminSzkolenia.objects.all().delete()
        termin = TerminSzkoleniaFactory.create(
            szkolenie__min_grupa_bazowa=5,
            termin=datetime.date.today() + datetime.timedelta(days=10),
        )
        UczestnikFactory.create_batch(7, termin=termin)
        TerminSzkoleniaLogFactory.create(
            term=termin, log_type="grupa_rusza_szybkie_potwierdzenie"
        )

        cmd = grupa_rusza.Command()
        cmd.handle()

        # Nie powinno byc zadnych logow, ani wyslanych maili
        self.assertEqual(len(mail.outbox), 0)

    def test_send_emails_gotowa(self):
        """
        Symuljemy wyslanie maila z informacją Gotowa.
        """

        termin = TerminSzkoleniaFactory.create(
            szkolenie__min_grupa_bazowa=5,
            termin=datetime.date.today() + datetime.timedelta(days=10),
        )
        UczestnikFactory.create_batch(6, termin=termin)
        UczestnikFactory.create_batch(2, termin=termin, nierentowny=True)

        termin2 = TerminSzkoleniaFactory.create(
            szkolenie__min_grupa_bazowa=5,
            termin=datetime.date.today() + datetime.timedelta(days=10),
        )
        UczestnikFactory.create_batch(6, termin=termin2)

        cmd = grupa_rusza.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 2)
        self.assertEqual(TerminSzkoleniaLog.objects.all().count(), 2)

        body = mail.outbox[0].body

        self.assertIn("8 / 6R", body)
        self.assertIn(str(termin.termin), body)
        self.assertIn(termin.szkolenie.kod, body)
        self.assertIn(
            "http://www.alx.dev/admin/www/terminszkolenia/{0}/".format(termin.pk), body
        )
        self.assertEqual(
            "Grupa zebrana przed terminem - szybkie potwierdzenie? "
            "({0} - {1})".format(termin.szkolenie.kod, termin.termin),
            mail.outbox[0].subject,
        )

        # Uruchamiamy zadanie jeszcze raz - zero maili
        mail.outbox = []

        cmd = grupa_rusza.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 0)

    def test_send_emails_gotowa_z_faktoryzacja(self):
        termin = TerminSzkoleniaFactory.create(
            szkolenie__min_grupa_bazowa=5,
            termin=datetime.date.today() + datetime.timedelta(days=10),
        )
        UczestnikFactory.create_batch(6, termin=termin)
        UczestnikFactory.create_batch(2, termin=termin, nierentowny=True)

        termin_podrzedny = TerminSzkoleniaFactory.create(
            szkolenie__nazwa="Szkolenie podrzędne",
            termin=datetime.date.today(),
            termin_nadrzedny=termin,
        )
        UczestnikFactory.create_batch(1, termin=termin_podrzedny)
        UczestnikFactory.create_batch(1, termin=termin_podrzedny, nierentowny=True)

        termin2 = TerminSzkoleniaFactory.create(
            szkolenie__min_grupa_bazowa=5,
            termin=datetime.date.today() + datetime.timedelta(days=10),
        )
        UczestnikFactory.create_batch(6, termin=termin2)

        cmd = grupa_rusza.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 2)
        self.assertEqual(TerminSzkoleniaLog.objects.all().count(), 2)

        body = mail.outbox[0].body

        self.assertIn("8 / 6R", body)
        self.assertIn(str(termin.termin), body)
        self.assertIn(termin.szkolenie.kod, body)
        self.assertIn("Szkolenie podrzędne: 2 / 1R", body)
        self.assertIn(
            "http://www.alx.dev/admin/www/terminszkolenia/{0}/".format(termin.pk), body
        )
        self.assertEqual(
            "Grupa zebrana przed terminem - szybkie potwierdzenie? "
            "({0} - {1})".format(termin.szkolenie.kod, termin.termin),
            mail.outbox[0].subject,
        )

        # Uruchamiamy zadanie jeszcze raz - zero maili
        mail.outbox = []

        cmd = grupa_rusza.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 0)

    def test_send_emails_gotowa_hybryda_zdalna(self):
        """
        Symuljemy wyslanie maila z informacją Gotowa + hybryda.
        """

        termin_zdalny = TerminSzkoleniaFactory.create(
            szkolenie__min_grupa_bazowa=5,
            lokalizacja__shortname="zdalnie",
            termin=datetime.date.today() + datetime.timedelta(days=10),
        )

        termin = TerminSzkoleniaFactory.create(
            szkolenie__min_grupa_bazowa=5,
            lokalizacja__shortname="Warszawa",
            termin=datetime.date.today() + datetime.timedelta(days=10),
            termin_zdalny=termin_zdalny,
        )
        UczestnikFactory.create_batch(6, termin=termin, imie_nazwisko="Imię Waw")
        UczestnikFactory.create_batch(
            2, termin=termin, nierentowny=True, imie_nazwisko="Imię Waw"
        )

        UczestnikFactory.create_batch(
            1, termin=termin_zdalny, imie_nazwisko="Imię zdalnie"
        )
        UczestnikFactory.create_batch(
            3, termin=termin_zdalny, nierentowny=True, imie_nazwisko="Imię zdalnie"
        )

        cmd = grupa_rusza.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 2)
        self.assertEqual(TerminSzkoleniaLog.objects.all().count(), 2)

        body = mail.outbox[1].body

        self.assertIn("rusza. Jest:", body)
        self.assertIn("Razem hybrydowo: 12 / 7R", body)

        self.assertIn("Warszawa</strong> - 8 / 6R", body)
        self.assertIn(str(termin.termin), body)
        self.assertIn(termin.szkolenie.kod, body)
        self.assertIn(
            "http://www.alx.dev/admin/www/terminszkolenia/{0}/".format(termin.pk), body
        )
        self.assertIn("Uczestnicy: Imię Waw, ", body)

        self.assertIn("zdalnie</strong> - 4 / 1R", body)
        self.assertIn(
            "http://www.alx.dev/admin/www/terminszkolenia/{0}/".format(
                termin_zdalny.pk
            ),
            body,
        )
        self.assertIn("Uczestnicy: Imię zdalnie, ", body)

        self.assertEqual(
            "Grupa zebrana przed terminem - szybkie potwierdzenie? "
            "({0} - {1})".format(termin.szkolenie.kod, termin.termin),
            mail.outbox[1].subject,
        )

        # Uruchamiamy zadanie jeszcze raz - zero maili
        mail.outbox = []

        cmd = grupa_rusza.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 0)

    def test_send_emails_gotowa_hybryda_zdalna_i_faktoryzacja(self):
        termin_zdalny = TerminSzkoleniaFactory.create(
            szkolenie__min_grupa_bazowa=5,
            lokalizacja__shortname="zdalnie",
            termin=datetime.date.today() + datetime.timedelta(days=10),
        )

        termin = TerminSzkoleniaFactory.create(
            szkolenie__min_grupa_bazowa=5,
            lokalizacja__shortname="Warszawa",
            termin=datetime.date.today() + datetime.timedelta(days=10),
            termin_zdalny=termin_zdalny,
        )
        UczestnikFactory.create_batch(6, termin=termin, imie_nazwisko="Imię Waw")
        UczestnikFactory.create_batch(
            2, termin=termin, nierentowny=True, imie_nazwisko="Imię Waw"
        )

        UczestnikFactory.create_batch(
            1, termin=termin_zdalny, imie_nazwisko="Imię zdalnie"
        )
        UczestnikFactory.create_batch(
            3, termin=termin_zdalny, nierentowny=True, imie_nazwisko="Imię zdalnie"
        )

        termin_podrzedny = TerminSzkoleniaFactory.create(
            szkolenie__nazwa="Szkolenie podrzędne",
            termin=datetime.date.today(),
            termin_nadrzedny=termin,
        )
        UczestnikFactory.create_batch(1, termin=termin_podrzedny)
        UczestnikFactory.create_batch(1, termin=termin_podrzedny, nierentowny=True)

        cmd = grupa_rusza.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 2)
        self.assertEqual(TerminSzkoleniaLog.objects.all().count(), 2)

        body = mail.outbox[1].body

        self.assertIn("rusza. Jest:", body)
        self.assertIn("Razem hybrydowo: 12 / 7R", body)

        self.assertIn("Warszawa</strong> - 8 / 6R", body)
        self.assertIn("Szkolenie podrzędne: 2 / 1R", body)
        self.assertIn(str(termin.termin), body)
        self.assertIn(termin.szkolenie.kod, body)
        self.assertIn(
            "http://www.alx.dev/admin/www/terminszkolenia/{0}/".format(termin.pk), body
        )
        self.assertIn("Uczestnicy: Imię Waw, ", body)

        self.assertIn("zdalnie</strong> - 4 / 1R", body)
        self.assertIn(
            "http://www.alx.dev/admin/www/terminszkolenia/{0}/".format(
                termin_zdalny.pk
            ),
            body,
        )
        self.assertIn("Uczestnicy: Imię zdalnie, ", body)

        self.assertEqual(
            "Grupa zebrana przed terminem - szybkie potwierdzenie? "
            "({0} - {1})".format(termin.szkolenie.kod, termin.termin),
            mail.outbox[1].subject,
        )

        # Uruchamiamy zadanie jeszcze raz - zero maili
        mail.outbox = []

        cmd = grupa_rusza.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 0)

    def test_send_emails_gotowa_hybryda_stacjonarna(self):
        """
        Symuljemy wyslanie maila z informacją Gotowa + hybryda.
        """

        termin_zdalny = TerminSzkoleniaFactory.create(
            szkolenie__min_grupa_bazowa=5,
            lokalizacja__shortname="zdalnie",
            termin=datetime.date.today() + datetime.timedelta(days=10),
        )

        termin = TerminSzkoleniaFactory.create(
            szkolenie__min_grupa_bazowa=5,
            lokalizacja__shortname="Warszawa",
            termin=datetime.date.today() + datetime.timedelta(days=10),
            termin_zdalny=termin_zdalny,
        )
        UczestnikFactory.create_batch(
            6, termin=termin_zdalny, imie_nazwisko="Imię zdalnie"
        )
        UczestnikFactory.create_batch(
            2, termin=termin_zdalny, nierentowny=True, imie_nazwisko="Imię zdalnie"
        )

        UczestnikFactory.create_batch(1, termin=termin, imie_nazwisko="Imię Waw")
        UczestnikFactory.create_batch(
            3, termin=termin, nierentowny=True, imie_nazwisko="Imię Waw"
        )

        cmd = grupa_rusza.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 2)
        self.assertEqual(TerminSzkoleniaLog.objects.all().count(), 2)

        body = mail.outbox[0].body

        self.assertIn("rusza. Jest:", body)
        self.assertIn("Razem hybrydowo: 12 / 7R", body)

        self.assertIn("zdalnie</strong> - 8 / 6R", body)
        self.assertIn(str(termin_zdalny.termin), body)
        self.assertIn(termin_zdalny.szkolenie.kod, body)
        self.assertIn(
            "http://www.alx.dev/admin/www/terminszkolenia/{0}/".format(
                termin_zdalny.pk
            ),
            body,
        )
        self.assertIn("Uczestnicy: Imię zdalnie, ", body)

        self.assertIn("Warszawa</strong> - 4 / 1R", body)
        self.assertIn(
            "http://www.alx.dev/admin/www/terminszkolenia/{0}/".format(termin.pk), body
        )
        self.assertIn("Uczestnicy: Imię Waw, ", body)

        self.assertEqual(
            "Grupa zebrana przed terminem - szybkie potwierdzenie? "
            "({0} - {1})".format(termin_zdalny.szkolenie.kod, termin_zdalny.termin),
            mail.outbox[0].subject,
        )

        # Uruchamiamy zadanie jeszcze raz - zero maili
        mail.outbox = []

        cmd = grupa_rusza.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 0)

    def test_send_emails_prawie_gotowa(self):
        """
        Symuljemy wyslanie maila z informacją Prawie Gotowa.
        """

        termin = TerminSzkoleniaFactory.create(
            szkolenie__min_grupa_bazowa=5,
            termin=datetime.date.today() + datetime.timedelta(days=10),
        )
        UczestnikFactory.create_batch(4, termin=termin)
        UczestnikFactory.create_batch(2, termin=termin, nierentowny=True)

        termin2 = TerminSzkoleniaFactory.create(
            szkolenie__min_grupa_bazowa=5,
            termin=datetime.date.today() + datetime.timedelta(days=10),
        )
        UczestnikFactory.create_batch(4, termin=termin2)

        cmd = grupa_rusza.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 2)
        self.assertEqual(TerminSzkoleniaLog.objects.all().count(), 2)

        body = mail.outbox[0].body

        self.assertIn("6 / 4R", body)
        self.assertIn(str(termin.termin), body)
        self.assertIn(termin.szkolenie.kod, body)
        self.assertIn(
            "http://www.alx.dev/admin/www/terminszkolenia/{0}/".format(termin.pk), body
        )
        self.assertEqual(
            "Grupa prawie zebrana ({0} - {1})".format(
                termin.szkolenie.kod, termin.termin
            ),
            mail.outbox[0].subject,
        )

        # Uruchamiamy zadanie jeszcze raz - zero maili
        mail.outbox = []

        cmd = grupa_rusza.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 0)

    def test_send_emails_prawie_gotowa_i_faktoryzacja(self):
        termin = TerminSzkoleniaFactory.create(
            szkolenie__min_grupa_bazowa=5,
            termin=datetime.date.today() + datetime.timedelta(days=10),
        )
        UczestnikFactory.create_batch(4, termin=termin)
        UczestnikFactory.create_batch(2, termin=termin, nierentowny=True)

        termin_podrzedny = TerminSzkoleniaFactory.create(
            szkolenie__nazwa="Szkolenie podrzędne",
            termin=datetime.date.today(),
            termin_nadrzedny=termin,
        )
        UczestnikFactory.create_batch(1, termin=termin_podrzedny)
        UczestnikFactory.create_batch(1, termin=termin_podrzedny, nierentowny=True)

        termin2 = TerminSzkoleniaFactory.create(
            szkolenie__min_grupa_bazowa=5,
            termin=datetime.date.today() + datetime.timedelta(days=10),
        )
        UczestnikFactory.create_batch(4, termin=termin2)

        cmd = grupa_rusza.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 2)
        self.assertEqual(TerminSzkoleniaLog.objects.all().count(), 2)

        body = mail.outbox[0].body

        self.assertIn("6 / 4R", body)
        self.assertIn(str(termin.termin), body)
        self.assertIn(termin.szkolenie.kod, body)
        self.assertIn("Szkolenie podrzędne: 2 / 1R", body)
        self.assertIn(
            "http://www.alx.dev/admin/www/terminszkolenia/{0}/".format(termin.pk), body
        )
        self.assertEqual(
            "Grupa prawie zebrana ({0} - {1})".format(
                termin.szkolenie.kod, termin.termin
            ),
            mail.outbox[0].subject,
        )

        # Uruchamiamy zadanie jeszcze raz - zero maili
        mail.outbox = []

        cmd = grupa_rusza.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 0)

    def test_send_emails_prawie_gotowa_hybryda_zdalna(self):
        """
        Symuljemy wyslanie maila z informacją Prawie Gotowa + hybryda.
        """

        termin_zdalny = TerminSzkoleniaFactory.create(
            szkolenie__min_grupa_bazowa=5,
            lokalizacja__shortname="zdalnie",
            termin=datetime.date.today() + datetime.timedelta(days=10),
        )

        termin = TerminSzkoleniaFactory.create(
            szkolenie__min_grupa_bazowa=5,
            lokalizacja__shortname="Warszawa",
            termin=datetime.date.today() + datetime.timedelta(days=10),
            termin_zdalny=termin_zdalny,
        )
        UczestnikFactory.create_batch(3, termin=termin, imie_nazwisko="Imię Waw")
        UczestnikFactory.create_batch(
            2, termin=termin, nierentowny=True, imie_nazwisko="Imię Waw"
        )

        UczestnikFactory.create_batch(
            1, termin=termin_zdalny, imie_nazwisko="Imię zdalnie"
        )
        UczestnikFactory.create_batch(
            3, termin=termin_zdalny, nierentowny=True, imie_nazwisko="Imię zdalnie"
        )

        cmd = grupa_rusza.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 2)
        self.assertEqual(TerminSzkoleniaLog.objects.all().count(), 2)

        body = mail.outbox[1].body

        self.assertIn("rusza. Jest:", body)
        self.assertIn("Razem hybrydowo: 9 / 4R", body)

        self.assertIn("Warszawa</strong> - 5 / 3R", body)
        self.assertIn(str(termin.termin), body)
        self.assertIn(termin.szkolenie.kod, body)
        self.assertIn(
            "http://www.alx.dev/admin/www/terminszkolenia/{0}/".format(termin.pk), body
        )
        self.assertIn("Uczestnicy: Imię Waw, ", body)

        self.assertIn("zdalnie</strong> - 4 / 1R", body)
        self.assertIn(
            "http://www.alx.dev/admin/www/terminszkolenia/{0}/".format(
                termin_zdalny.pk
            ),
            body,
        )
        self.assertIn("Uczestnicy: Imię zdalnie, ", body)

        self.assertEqual(
            "Grupa prawie zebrana ({0} - {1})".format(
                termin.szkolenie.kod, termin.termin
            ),
            mail.outbox[1].subject,
        )

        # Uruchamiamy zadanie jeszcze raz - zero maili
        mail.outbox = []

        cmd = grupa_rusza.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 0)

    def test_send_emails_prawie_gotowa_hybryda_zdalna_i_faktoryzacja(self):
        termin_zdalny = TerminSzkoleniaFactory.create(
            szkolenie__min_grupa_bazowa=5,
            lokalizacja__shortname="zdalnie",
            termin=datetime.date.today() + datetime.timedelta(days=10),
        )

        termin = TerminSzkoleniaFactory.create(
            szkolenie__min_grupa_bazowa=5,
            lokalizacja__shortname="Warszawa",
            termin=datetime.date.today() + datetime.timedelta(days=10),
            termin_zdalny=termin_zdalny,
        )
        UczestnikFactory.create_batch(3, termin=termin, imie_nazwisko="Imię Waw")
        UczestnikFactory.create_batch(
            2, termin=termin, nierentowny=True, imie_nazwisko="Imię Waw"
        )

        termin_podrzedny = TerminSzkoleniaFactory.create(
            szkolenie__nazwa="Szkolenie podrzędne",
            termin=datetime.date.today(),
            termin_nadrzedny=termin,
        )
        UczestnikFactory.create_batch(1, termin=termin_podrzedny)
        UczestnikFactory.create_batch(1, termin=termin_podrzedny, nierentowny=True)

        UczestnikFactory.create_batch(
            1, termin=termin_zdalny, imie_nazwisko="Imię zdalnie"
        )
        UczestnikFactory.create_batch(
            3, termin=termin_zdalny, nierentowny=True, imie_nazwisko="Imię zdalnie"
        )

        cmd = grupa_rusza.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 2)
        self.assertEqual(TerminSzkoleniaLog.objects.all().count(), 2)

        body = mail.outbox[1].body

        self.assertIn("rusza. Jest:", body)
        self.assertIn("Razem hybrydowo: 9 / 4R", body)

        self.assertIn("Warszawa</strong> - 5 / 3R", body)
        self.assertIn(str(termin.termin), body)
        self.assertIn(termin.szkolenie.kod, body)
        self.assertIn("Szkolenie podrzędne: 2 / 1R", body)
        self.assertIn(
            "http://www.alx.dev/admin/www/terminszkolenia/{0}/".format(termin.pk), body
        )
        self.assertIn("Uczestnicy: Imię Waw, ", body)

        self.assertIn("zdalnie</strong> - 4 / 1R", body)
        self.assertIn(
            "http://www.alx.dev/admin/www/terminszkolenia/{0}/".format(
                termin_zdalny.pk
            ),
            body,
        )
        self.assertIn("Uczestnicy: Imię zdalnie, ", body)

        self.assertEqual(
            "Grupa prawie zebrana ({0} - {1})".format(
                termin.szkolenie.kod, termin.termin
            ),
            mail.outbox[1].subject,
        )

        # Uruchamiamy zadanie jeszcze raz - zero maili
        mail.outbox = []

        cmd = grupa_rusza.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 0)

    def test_send_emails_prawie_gotowa_hybryda_stacjonarna(self):
        """
        Symuljemy wyslanie maila z informacją Prawie Gotowa + hybryda.
        """

        termin_zdalny = TerminSzkoleniaFactory.create(
            szkolenie__min_grupa_bazowa=5,
            lokalizacja__shortname="zdalnie",
            termin=datetime.date.today() + datetime.timedelta(days=10),
        )

        termin = TerminSzkoleniaFactory.create(
            szkolenie__min_grupa_bazowa=5,
            lokalizacja__shortname="Warszawa",
            termin=datetime.date.today() + datetime.timedelta(days=10),
            termin_zdalny=termin_zdalny,
        )
        UczestnikFactory.create_batch(
            3, termin=termin_zdalny, imie_nazwisko="Imię zdalnie"
        )
        UczestnikFactory.create_batch(
            1, termin=termin_zdalny, nierentowny=True, imie_nazwisko="Imię zdalnie"
        )

        UczestnikFactory.create_batch(1, termin=termin, imie_nazwisko="Imię Waw")
        UczestnikFactory.create_batch(
            3, termin=termin, nierentowny=True, imie_nazwisko="Imię Waw"
        )

        cmd = grupa_rusza.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 2)
        self.assertEqual(TerminSzkoleniaLog.objects.all().count(), 2)

        body = mail.outbox[0].body

        self.assertIn("rusza. Jest:", body)
        self.assertIn("Razem hybrydowo: 8 / 4R", body)

        self.assertIn("zdalnie</strong> - 4 / 3R", body)
        self.assertIn(str(termin_zdalny.termin), body)
        self.assertIn(termin_zdalny.szkolenie.kod, body)
        self.assertIn(
            "http://www.alx.dev/admin/www/terminszkolenia/{0}/".format(
                termin_zdalny.pk
            ),
            body,
        )
        self.assertIn("Uczestnicy: Imię zdalnie, ", body)

        self.assertIn("Warszawa</strong> - 4 / 1R", body)
        self.assertIn(
            "http://www.alx.dev/admin/www/terminszkolenia/{0}/".format(termin.pk), body
        )
        self.assertIn("Uczestnicy: Imię Waw, ", body)

        self.assertEqual(
            "Grupa prawie zebrana ({0} - {1})".format(
                termin_zdalny.szkolenie.kod, termin_zdalny.termin
            ),
            mail.outbox[0].subject,
        )

        # Uruchamiamy zadanie jeszcze raz - zero maili
        mail.outbox = []

        cmd = grupa_rusza.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 0)


@override_settings(
    DOMENY_DLA_JEZYKOW={"pl": "www.alx.dev", "en": "www.alx-training.co.dev"}
)
class PodsumowanieTerminowManagementTestCase(ALXTestCase):
    def test_empty_scenarios(self):
        cmd = podsumowanie_terminow.Command()
        cmd.handle()

        # Nie powinno byc wyslanych maili
        self.assertEqual(len(mail.outbox), 0)

    def test_old_entries(self):
        TerminSzkoleniaLogFactory.create(log_type="czy_reklamowac")
        with freeze_time(datetime.date.today() - datetime.timedelta(days=2)):
            TerminSzkoleniaLogFactory.create(log_type="odbylo_sie")
        cmd = podsumowanie_terminow.Command()
        cmd.handle()

        # Nie powinno byc wyslanych maili
        self.assertEqual(len(mail.outbox), 0)

    def test_mail(self):
        with freeze_time(datetime.date.today() - datetime.timedelta(days=1)):
            odbylo_sie = TerminSzkoleniaLogFactory.create(
                log_type="odbylo_sie", user=UserFactory.create()
            )
            czy_reklamowac = TerminSzkoleniaLogFactory.create_batch(
                2, log_type="czy_reklamowac", user=UserFactory.create()
            )
        cmd = podsumowanie_terminow.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 1)

        m = mail.outbox[0]

        self.assertEqual(m.subject, "Terminy wczoraj potwierdzone i zapromowane")
        self.assertIn(odbylo_sie.term.szkolenie.kod, m.body)
        self.assertIn(czy_reklamowac[0].term.szkolenie.kod, m.body)
        self.assertIn(czy_reklamowac[1].term.szkolenie.kod, m.body)

    def test_filtering_czy_reklamowac(self):
        with freeze_time(datetime.date.today() - datetime.timedelta(days=1)):
            odbylo_sie = TerminSzkoleniaLogFactory.create(log_type="odbylo_sie")
            TerminSzkoleniaLogFactory.create(
                log_type="czy_reklamowac", term=odbylo_sie.term
            )
        cmd = podsumowanie_terminow.Command()
        cmd.handle()

        self.assertEqual(len(mail.outbox), 1)

        m = mail.outbox[0]

        self.assertIn(odbylo_sie.term.szkolenie.kod, m.body)
        self.assertIn("Brak terminów.", m.body)


##################
# Templatefilters
##################


class TemplatefilterTestCase(ALXTestCase):
    def test_termin_full_ten_sam_rok(self):
        termin = TerminSzkoleniaFactory.create(
            termin=datetime.date(2000, 1, 1),
            termin_zakonczenia=datetime.date(2000, 1, 2),
        )
        result = myfilters.termin_full(termin)
        self.assertEqual(result, "1 stycznia - 2 stycznia 2000")

    def test_termin_full_rozne_lata(self):
        termin = TerminSzkoleniaFactory.create(
            termin=datetime.date(2000, 1, 21),
            termin_zakonczenia=datetime.date(2001, 2, 23),
        )
        result = myfilters.termin_full(termin)
        self.assertEqual(result, "21 stycznia - 23 lutego 2001")

    def test_termin_full_przyszly_rok(self):
        with freeze_time("2018-12-12"):
            termin = TerminSzkoleniaFactory.create(
                termin=datetime.date(2019, 1, 21),
                termin_zakonczenia=datetime.date(2019, 2, 23),
            )
            result = myfilters.termin_full(termin)
            self.assertEqual(result, "21 stycznia 2019 - 23 lutego 2019")

    def test_termin_full_brak_daty_koncowej(self):
        termin = TerminSzkoleniaFactory.create(termin=datetime.date(2000, 1, 1))
        result = myfilters.termin_full(termin)
        self.assertEqual(result, "1 stycznia 2000")

    def test_termin_full_mobile_ten_sam_rok(self):
        termin = TerminSzkoleniaFactory.create(
            termin=datetime.date(2000, 1, 1),
            termin_zakonczenia=datetime.date(2000, 1, 2),
        )
        result = myfilters.termin_full_mobile(termin)
        self.assertEqual(result, "01.01 - 02.01.2000")

    def test_termin_full_mobile_rozne_lata(self):
        termin = TerminSzkoleniaFactory.create(
            termin=datetime.date(2000, 1, 21),
            termin_zakonczenia=datetime.date(2001, 2, 23),
        )
        result = myfilters.termin_full_mobile(termin)
        self.assertEqual(result, "21.01 - 23.02.2001")

    def test_termin_full_mobile_przyszly_rok(self):
        with freeze_time("2018-12-12"):
            termin = TerminSzkoleniaFactory.create(
                termin=datetime.date(2019, 1, 21),
                termin_zakonczenia=datetime.date(2019, 2, 23),
            )
            result = myfilters.termin_full_mobile(termin)
            self.assertEqual(result, "21.01.2019 - 23.02.2019")

    def test_termin_full_mobile_brak_daty_koncowej(self):
        termin = TerminSzkoleniaFactory.create(termin=datetime.date(2000, 1, 1))
        result = myfilters.termin_full_mobile(termin)
        self.assertEqual(result, "01.01.2000")

    def test_frontpage_lista_trenerow(self):
        out = myfilters.myincludes("[[frontpage_lista_trenerow_pl:abc]]")
        self.assertEqual(out, "invalid limit in frontpage_lista_trenerow")

        ProwadzacyFactory.create_batch(10)

        out = myfilters.myincludes("[[frontpage_lista_trenerow_pl:5]]")
        self.assertEqual(out.count("#trener-"), 5)

        out = myfilters.myincludes("[[frontpage_lista_trenerow_en:5]]")
        self.assertEqual(out.count("#trener-"), 0)

    def test_staticpage_lista_trenerow(self):
        out = myfilters.myincludes("[[staticpage_lista_trenerow_pl:abc]]")
        self.assertEqual(out, "could not parse an ID staticpage_lista_trenerow")

        out = myfilters.myincludes("[[staticpage_lista_trenerow_pl:1,2,3,a]]")
        self.assertEqual(out, "could not parse an ID staticpage_lista_trenerow")

        prowadzacy = ProwadzacyFactory.create_batch(10)

        out = myfilters.myincludes(
            "[[staticpage_lista_trenerow_pl:{0},{1}]]".format(
                prowadzacy[0].pk, prowadzacy[1].pk
            )
        )
        self.assertEqual(out.count("trener-"), 2)

        out = myfilters.myincludes("[[staticpage_lista_trenerow_pl:0]]")
        self.assertEqual(out.count("trener-"), 0)

    def test_frontpage_lista_tagow(self):
        TagTechnologiaFactory.create_batch(10)

        out = myfilters.myincludes("[[frontpage_lista_tagow:pl]]")
        self.assertEqual(out.count('class="lp-singleTag"'), 10)

    def test_program_kursu(self):
        szkolenie = SzkolenieFactory.create(slug="abc", program="")
        out = myfilters.myincludes("[[program_kursu:pl/abc]]")
        self.assertIn("Program niedostępny.", out)

        szkolenie.program = "## 123"
        szkolenie.save()
        out = myfilters.myincludes("[[program_kursu:pl/abc]]")
        self.assertIn("123", out)

    def test_szkolenie_kontynuacja(self):
        out = myfilters.myincludes("[[szkolenie_kontynuacja:pl/szkolenie]]")
        self.assertEqual(out, "invalid szkolenie in szkolenie_kontynuacja")

        out = myfilters.myincludes("[[szkolenie_kontynuacja:abc]]")
        self.assertEqual(out, "invalid szkolenie in szkolenie_kontynuacja")

        szkolenie = SzkolenieFactory.create(slug="szkolenie")

        out = myfilters.myincludes("[[szkolenie_kontynuacja:pl/szkolenie]]")
        self.assertEqual(out.strip("\n"), "")

        szkolenie.kontynuacja = SzkolenieFactory.create(nazwa="ABC")
        szkolenie.save()

        out = myfilters.myincludes("[[szkolenie_kontynuacja:pl/szkolenie]]")
        self.assertIn("ABC", out)

    def test_szkolenie_kontynuacja_i_alternatywne(self):
        szkolenie = SzkolenieFactory.create(slug="szkolenie")

        out = myfilters.myincludes("[[szkolenie_kontynuacja:pl/szkolenie]]")
        self.assertEqual(out.strip("\n"), "")

        szkolenie.szkolenia_alternatywne.add(
            SzkolenieFactory.create(nazwa="ABC", krotka_nazwa="123")
        )
        szkolenie.szkolenia_alternatywne.add(SzkolenieFactory.create(nazwa="EFG"))
        szkolenie.save()

        out = myfilters.myincludes("[[szkolenie_kontynuacja:pl/szkolenie]]")
        self.assertIn("123", out)
        self.assertIn("EFG", out)

        szkolenie.kontynuacja = SzkolenieFactory.create()
        szkolenie.save()

        out = myfilters.myincludes("[[szkolenie_kontynuacja:pl/szkolenie]]")
        self.assertIn("Kurs (poziom 2)", out)
        self.assertIn("Krótkie szkolenia", out)

    def test_szkolenie_kontynuacja_i_alternatywne_wlasne_naglowki(self):
        szkolenie = SzkolenieFactory.create(slug="szkolenie")
        szkolenie.szkolenia_alternatywne.add(
            SzkolenieFactory.create(nazwa="ABC", krotka_nazwa="123")
        )
        szkolenie.szkolenia_alternatywne.add(SzkolenieFactory.create(nazwa="EFG"))
        szkolenie.save()

        out = myfilters.myincludes("[[szkolenie_kontynuacja:pl/szkolenie;Ó Ł;ĄĆ]]")
        self.assertIn("ĄĆ:", out)
        self.assertNotIn("Ó Ł:", out)

        szkolenie.kontynuacja = SzkolenieFactory.create()
        szkolenie.save()

        out = myfilters.myincludes("[[szkolenie_kontynuacja:pl/szkolenie;ÓŁ]]")
        self.assertIn("Krótkie szkolenia", out)
        self.assertIn("ÓŁ", out)

        out = myfilters.myincludes(
            "[[szkolenie_kontynuacja:pl/szkolenie;ÓŁ;ĄĆ (test)]]"
        )
        self.assertIn("ĄĆ (test)", out)
        self.assertIn("ÓŁ", out)

        out = myfilters.myincludes("[[szkolenie_kontynuacja:pl/szkolenie;;ĄĆ (test)]]")
        self.assertIn("ĄĆ (test)", out)
        self.assertIn("Kurs (poziom 2)", out)

    def test_terminy_kursu(self):
        out = myfilters.myincludes("[[terminy_kursu:pl/szkolenie]]")
        self.assertEqual(out, "invalid szkolenie in terminy_kursu")

        out = myfilters.myincludes("[[terminy_kursu:abc]]")
        self.assertEqual(out, "invalid szkolenie in terminy_kursu")

        SzkolenieFactory.create(slug="szkolenie")

        out = myfilters.myincludes("[[terminy_kursu:pl/szkolenie]]")
        self.assertIn("Dostępne na zamówienie", out)

    def test_szybki_kontakt(self):
        out = myfilters.myincludes("[[szybki_kontakt:form]]")
        self.assertIn("data-sitekey", out)
        self.assertIn("js/contact_form.js?", out)
        self.assertIn("//www.google.com/recaptcha/api.js", out)


##################
# Templatetags
##################


class TemplatetagTestCase(ALXTestCase):
    def test_uwagi_i_wolne_miejsca_bez_uwag_o_miejscach(self):
        termin = TerminSzkoleniaFactory.create(
            szkolenie=SzkolenieFactory.create(tryb_dzienny_opis="123"),
            dodatkowe_uwagi="Test 12",
        )

        result = mytags.uwagi_i_wolne_miejsca(termin)
        self.assertEqual(result["uwagi"], "Test 12")
        self.assertEqual(result["wolne_miejsca"], "")

    def test_uwagi_i_wolne_miejsca_opis_w_szkoleniu(self):
        termin = TerminSzkoleniaFactory.create(
            szkolenie=SzkolenieFactory.create(tryb_dzienny_opis="123")
        )

        result = mytags.uwagi_i_wolne_miejsca(termin)
        self.assertEqual(result["uwagi"], "123")
        self.assertEqual(result["wolne_miejsca"], "")

    def test_uwagi_i_wolne_miejsca_noappend(self):
        termin = TerminSzkoleniaFactory.create(
            dodatkowe_uwagi="Test 12[[noappend]]", czy_reklamowac=False
        )

        result = mytags.uwagi_i_wolne_miejsca(termin)
        self.assertEqual(result["uwagi"], "Test 12")
        self.assertEqual(result["wolne_miejsca"], "")

    def test_uwagi_i_wolne_miejsca_gdy_kurs(self):
        termin = TerminSzkoleniaFactory.create(
            szkolenie=KursFactory.create(), czy_reklamowac=True
        )

        result = mytags.uwagi_i_wolne_miejsca(termin)
        self.assertEqual(result["uwagi"], "")
        self.assertEqual(result["wolne_miejsca"], "zostało&nbsp;8&nbsp;miejsc")

    def test_uwagi_i_wolne_miejsca_gdy_szkolenie(self):
        termin = TerminSzkoleniaFactory.create(czy_reklamowac=True)

        result = mytags.uwagi_i_wolne_miejsca(termin)
        self.assertEqual(result["uwagi"], "")
        self.assertEqual(result["wolne_miejsca"], "zostało&nbsp;8&nbsp;miejsc")

        UczestnikFactory.create_batch(9, termin=termin)
        result = mytags.uwagi_i_wolne_miejsca(termin)
        self.assertEqual(result["wolne_miejsca"], "zostało&nbsp;6&nbsp;miejsc")

        UczestnikFactory.create_batch(3, termin=termin)
        result = mytags.uwagi_i_wolne_miejsca(termin)
        self.assertEqual(result["wolne_miejsca"], "ostatnie&nbsp;miejsca")

    def test_uwagi_i_wolne_miejsca_nie_pokazuj_wolnych_miejsc(self):
        termin = TerminSzkoleniaFactory.create(
            czy_reklamowac=True, nie_pokazuj_wolnych_miejsc=True
        )

        result = mytags.uwagi_i_wolne_miejsca(termin)
        self.assertEqual(result["uwagi"], "")
        self.assertEqual(result["wolne_miejsca"], "")

    def test_uwagi_i_wolne_miejsca_gdy_termin_podrzedny(self):
        termin = TerminSzkoleniaFactory.create(
            czy_reklamowac=True, termin_nadrzedny=TerminSzkoleniaFactory.create()
        )

        result = mytags.uwagi_i_wolne_miejsca(termin)
        self.assertEqual(result["uwagi"], "")
        self.assertEqual(result["wolne_miejsca"], "ostatnie&nbsp;miejsca")

    def test_uwagi_i_wolne_gdy_zmiana_sposobu_liczenia(self):
        termin = TerminSzkoleniaFactory.create(
            czy_reklamowac=True, szkolenie=KursFactory.create()
        )

        UczestnikFactory.create_batch(5, termin=termin, status=1)
        UczestnikFactory.create_batch(7, termin=termin, status=-1)
        UczestnikFactory.create_batch(4, termin=termin, status=0)
        result = mytags.uwagi_i_wolne_miejsca(termin)
        self.assertEqual(result["wolne_miejsca"], "zostały&nbsp;3&nbsp;miejsca")

    def test_czy_zastosowac_uproszczony_widok_kursow_tt_brak_terminow(self):
        kurs = KursFactory.create()
        miasto = LokalizacjaFactory.create(reklamowana=True)
        TekstOTerminachFactory.create(lokalizacja=miasto, szkolenie=kurs)

        self.assertTrue(
            mytags.czy_zastosowac_uproszczony_widok_kursow_tt(
                kurs.najblizsze_terminy_miastami()[0]
            )
        )

    def test_tabela_kursow_tt(self):
        kurs = KursFactory.create()
        lokalizacja = LokalizacjaFactory.create(reklamowana=True)

        TerminSzkoleniaFactory.create(szkolenie=kurs, lokalizacja=lokalizacja, tryb=1)

        TerminSzkoleniaFactory.create(szkolenie=kurs, lokalizacja=lokalizacja, tryb=1)

        TerminSzkoleniaFactory.create(szkolenie=kurs, lokalizacja=lokalizacja, tryb=1)

        TerminSzkoleniaFactory.create(szkolenie=kurs, lokalizacja=lokalizacja, tryb=3)

        result = mytags.tabela_kursow_tt(kurs, kurs.najblizsze_terminy_miastami()[0])
        self.assertEqual(result["header"]["D"], "")
        self.assertEqual(result["header"]["Z"], "")

        self.assertEqual(len(result["data"]["D"]), 3)
        self.assertEqual(len(result["data"]["Z"]), 1)

    def test_tabela_kursow_tt_naglowki_trybow(self):
        kurs = KursFactory.create(tryb_zaoczny_opis="ABC")
        lokalizacja = LokalizacjaFactory.create(reklamowana=True)

        TerminSzkoleniaFactory.create(
            szkolenie=kurs, lokalizacja=lokalizacja, dodatkowe_uwagi="Test 2", tryb=3
        )

        result = mytags.tabela_kursow_tt(kurs, kurs.najblizsze_terminy_miastami()[0])
        self.assertEqual(result["header"]["D"], "")
        self.assertEqual(result["header"]["Z"], "ABC")

    def test_czy_zastosowac_uproszczony_widok_kursow_brak_terminow(self):
        kurs = KursFactory.create()
        self.assertTrue(
            mytags.czy_zastosowac_uproszczony_widok_kursow(
                kurs.najblizsze_terminy_miastami()
            )
        )

    def test_czy_zastosowac_uproszczony_widok_kursow_malo_terminow(self):
        kurs = KursFactory.create()
        for i in range(2):
            lokalizacja = LokalizacjaFactory.create(reklamowana=True)
            TerminSzkoleniaFactory.create(
                szkolenie=kurs, tryb=1, lokalizacja=lokalizacja
            )
            TerminSzkoleniaFactory.create(
                szkolenie=kurs, tryb=3, lokalizacja=lokalizacja
            )
        self.assertTrue(
            mytags.czy_zastosowac_uproszczony_widok_kursow(
                kurs.najblizsze_terminy_miastami()
            )
        )

    def test_czy_zastosowac_uproszczony_widok_kursow_jeden_tryb(self):
        kurs = KursFactory.create()
        lokalizacja = LokalizacjaFactory.create(reklamowana=True)
        TerminSzkoleniaFactory.create_batch(
            10, szkolenie=kurs, tryb=1, lokalizacja=lokalizacja
        )
        self.assertTrue(
            mytags.czy_zastosowac_uproszczony_widok_kursow(
                kurs.najblizsze_terminy_miastami()
            )
        )

    def test_czy_zastosowac_uproszczony_widok_kursow_tryb_wieczorowy(self):
        kurs = KursFactory.create()
        for i in range(3):
            lokalizacja = LokalizacjaFactory.create(reklamowana=True)
            TerminSzkoleniaFactory.create(
                szkolenie=kurs, tryb=1, lokalizacja=lokalizacja
            )
            TerminSzkoleniaFactory.create(
                szkolenie=kurs, tryb=3, lokalizacja=lokalizacja
            )
            TerminSzkoleniaFactory.create(
                szkolenie=kurs, tryb=2, lokalizacja=lokalizacja
            )
        self.assertFalse(
            mytags.czy_zastosowac_uproszczony_widok_kursow(
                kurs.najblizsze_terminy_miastami()
            )
        )

    def test_czy_zastosowac_uproszczony_widok_kursow_roznica_w_miastach(self):
        kurs = KursFactory.create()
        lokalizacja = LokalizacjaFactory.create(reklamowana=True)
        TerminSzkoleniaFactory.create_batch(
            6, szkolenie=kurs, tryb=1, lokalizacja=lokalizacja
        )
        TerminSzkoleniaFactory.create_batch(
            2, szkolenie=kurs, tryb=3, lokalizacja=lokalizacja
        )
        self.assertTrue(
            mytags.czy_zastosowac_uproszczony_widok_kursow(
                kurs.najblizsze_terminy_miastami()
            )
        )

    def test_czy_zastosowac_uproszczony_widok_kursow(self):
        kurs = KursFactory.create()
        lokalizacja = LokalizacjaFactory.create(reklamowana=True)
        TerminSzkoleniaFactory.create_batch(
            5, szkolenie=kurs, tryb=1, lokalizacja=lokalizacja
        )
        TerminSzkoleniaFactory.create_batch(
            4, szkolenie=kurs, tryb=3, lokalizacja=lokalizacja
        )

        lokalizacja = LokalizacjaFactory.create(reklamowana=True)
        TerminSzkoleniaFactory.create_batch(
            2, szkolenie=kurs, tryb=3, lokalizacja=lokalizacja
        )

        self.assertFalse(
            mytags.czy_zastosowac_uproszczony_widok_kursow(
                kurs.najblizsze_terminy_miastami()
            )
        )

    def test_czy_zastosowac_uproszczony_widok_kursow_gdy_wiecej_jak_12_terminow(self):
        kurs = KursFactory.create()
        lokalizacja = LokalizacjaFactory.create(reklamowana=True)
        TerminSzkoleniaFactory.create_batch(
            4, szkolenie=kurs, tryb=1, lokalizacja=lokalizacja
        )
        TerminSzkoleniaFactory.create_batch(
            9, szkolenie=kurs, tryb=3, lokalizacja=lokalizacja
        )

        self.assertFalse(
            mytags.czy_zastosowac_uproszczony_widok_kursow(
                kurs.najblizsze_terminy_miastami()
            )
        )

    def test_tabela_kursow(self):
        kurs = KursFactory.create()
        lokalizacja = LokalizacjaFactory.create(reklamowana=True)

        TerminSzkoleniaFactory.create(
            szkolenie=kurs, lokalizacja=lokalizacja, dodatkowe_uwagi="Test 1", tryb=1
        )

        TerminSzkoleniaFactory.create(
            szkolenie=kurs, lokalizacja=lokalizacja, dodatkowe_uwagi="Test 2", tryb=1
        )

        TerminSzkoleniaFactory.create(
            szkolenie=kurs, lokalizacja=lokalizacja, dodatkowe_uwagi="Test 2", tryb=1
        )

        TerminSzkoleniaFactory.create(
            szkolenie=kurs, lokalizacja=lokalizacja, dodatkowe_uwagi="Test 4", tryb=3
        )

        result = mytags.tabela_kursow(kurs, kurs.najblizsze_terminy_miastami())
        self.assertEqual(result["header"]["D"], "")
        self.assertEqual(result["header"]["Z"], "")

        data = result["data"][0]

        self.assertEqual(data["location"], lokalizacja)
        self.assertEqual(len(data["terms"]), 3)

        for no, row in enumerate(data["terms"]):
            self.assertTrue(row["D"])
            self.assertEqual(bool(row["Z"]), not bool(no))

    def test_tabela_kursow_naglowki_trybow(self):
        kurs = KursFactory.create(tryb_zaoczny_opis="ABC")
        lokalizacja = LokalizacjaFactory.create(reklamowana=True)

        TerminSzkoleniaFactory.create(
            szkolenie=kurs, lokalizacja=lokalizacja, dodatkowe_uwagi="Test 2", tryb=3
        )

        result = mytags.tabela_kursow(kurs, kurs.najblizsze_terminy_miastami())
        self.assertEqual(result["header"]["D"], "")
        self.assertEqual(result["header"]["Z"], "ABC")

    def test_ocena_szkolenia_ponizej_20_przeszkolonych(self):
        termin = TerminSzkoleniaFactory.create(odbylo_sie=True)
        AnkietaFactory.create(termin_szkolenia=termin)
        UczestnikFactory.create_batch(19, status=3, termin=termin)

        self.assertIsNone(mytags.ocena_szkolenia(termin.szkolenie))

    def test_ocena_szkolenia_brak_pytan_o_prowadzacych(self):
        termin = TerminSzkoleniaFactory.create(odbylo_sie=True)
        ankiety = AnkietaFactory.create_batch(21, termin_szkolenia=termin)
        for ankieta in ankiety:
            UczestnikFactory.create(status=3, termin=termin)

        for ankieta in ankiety:
            WyborFactory.create_batch(15, ankieta=ankieta)

        self.assertIsNone(mytags.ocena_szkolenia(termin.szkolenie))

    def test_ocena_szkolenia_srednia_ponizej_4(self):
        termin = TerminSzkoleniaFactory.create(odbylo_sie=True)
        ankiety = AnkietaFactory.create_batch(21, termin_szkolenia=termin)
        for ankieta in ankiety:
            UczestnikFactory.create(status=3, termin=termin)

        p = PodpytanieFactory.create(pytanie__dotyczy_prowadzacych=True)

        for ankieta in ankiety:
            for i in range(15):
                WyborFactory.create(
                    pytanie=p,
                    ankieta=ankieta,
                    odpowiedz=OdpowiedzFactory.create(nazwa="1"),
                )

        self.assertIsNone(mytags.ocena_szkolenia(termin.szkolenie))

    def test_ocena_szkolenia_bledne_odpowiedzi(self):
        termin = TerminSzkoleniaFactory.create(odbylo_sie=True)
        ankiety = AnkietaFactory.create_batch(21, termin_szkolenia=termin)
        for ankieta in ankiety:
            UczestnikFactory.create(status=3, termin=termin)

        p = PodpytanieFactory.create(pytanie__dotyczy_prowadzacych=True)

        for ankieta in ankiety:
            for i in range(15):
                WyborFactory.create(
                    pytanie=p,
                    ankieta=ankieta,
                    odpowiedz=OdpowiedzFactory.create(nazwa="test"),
                )

        self.assertIsNone(mytags.ocena_szkolenia(termin.szkolenie))

    def test_ocena_szkolenia_i_archiwalna_liczba_przeszkolonych(self):
        termin = TerminSzkoleniaFactory.create(
            szkolenie__archiwalna_liczba_przeszkolonych=100, odbylo_sie=True
        )
        ankiety = AnkietaFactory.create_batch(21, termin_szkolenia=termin)
        for ankieta in ankiety:
            UczestnikFactory.create(status=3, termin=termin)

        p = PodpytanieFactory.create(pytanie__dotyczy_prowadzacych=True)

        for ankieta in ankiety:
            WyborFactory.create(
                pytanie=p, ankieta=ankieta, odpowiedz=OdpowiedzFactory.create(nazwa="4")
            )

        stars = mytags.ocena_szkolenia(termin.szkolenie)
        self.assertEqual(stars["count"], 121)
        self.assertEqual(stars["avg"], Decimal("4.0"))
        self.assertEqual(stars["css"], "4-0")

    def test_ocena_szkolenia_i_archiwalna_liczba_przeszkolonych_i_brak_ocen(self):
        termin = TerminSzkoleniaFactory.create(
            szkolenie__archiwalna_liczba_przeszkolonych=100, odbylo_sie=True
        )

        stars = mytags.ocena_szkolenia(termin.szkolenie)
        self.assertIsNone(stars)

    def test_ocena_szkolenia_z_always_default_i_brak_ocen(self):
        termin = TerminSzkoleniaFactory.create(odbylo_sie=True)

        stars = mytags.ocena_szkolenia(termin.szkolenie, always_default=True)
        self.assertEqual(stars["count"], 0)
        self.assertEqual(stars["avg"], Decimal("4.5"))
        self.assertEqual(stars["css"], "4-5")

    def test_ocena_szkolenia_z_always_default_i_srednia_ponizej_4(self):
        termin = TerminSzkoleniaFactory.create(odbylo_sie=True)
        ankiety = AnkietaFactory.create_batch(2, termin_szkolenia=termin)
        for ankieta in ankiety:
            UczestnikFactory.create(status=3, termin=termin)

        p = PodpytanieFactory.create(pytanie__dotyczy_prowadzacych=True)

        for ankieta in ankiety:
            WyborFactory.create(
                pytanie=p, ankieta=ankieta, odpowiedz=OdpowiedzFactory.create(nazwa="1")
            )

        stars = mytags.ocena_szkolenia(termin.szkolenie, always_default=True)
        self.assertEqual(stars["count"], 2)
        self.assertEqual(stars["avg"], Decimal("4.5"))
        self.assertEqual(stars["css"], "4-5")

    def test_ocena_szkolenia(self):
        termin = TerminSzkoleniaFactory.create(odbylo_sie=True)
        ankiety = AnkietaFactory.create_batch(21, termin_szkolenia=termin)
        UczestnikFactory.create_batch(10, status=3, termin=termin)
        UczestnikFactory.create(
            status=3, uczestnik_wieloosobowy_ilosc_osob=2, termin=termin
        )
        UczestnikFactory.create(
            status=3, uczestnik_wieloosobowy_ilosc_osob=9, termin=termin
        )

        # Nieprzeszkoleni - nie wliczamy!
        UczestnikFactory.create_batch(
            5, status=2, termin=termin, uczestnik_wieloosobowy_ilosc_osob=2
        )
        UczestnikFactory.create_batch(4, status=1, termin=termin)

        # Tworzymy troche danych nie zwiazanych z tym terminem
        # (aby przetestiowac poprawnosc agregacji).
        TerminSzkoleniaFactory.create_batch(10, odbylo_sie=True)
        UczestnikFactory.create_batch(10, status=3)
        UczestnikFactory.create(status=3, uczestnik_wieloosobowy_ilosc_osob=2)

        p1 = PodpytanieFactory.create(pytanie__dotyczy_prowadzacych=True)
        p2 = PodpytanieFactory.create(pytanie__dotyczy_prowadzacych=True)
        p3 = PodpytanieFactory.create(pytanie__dotyczy_prowadzacych=True)
        p4 = PodpytanieFactory.create(pytanie__dotyczy_prowadzacych=True)

        for ankieta in ankiety:
            WyborFactory.create(
                pytanie=p1,
                ankieta=ankieta,
                odpowiedz=OdpowiedzFactory.create(nazwa="4"),
            )
            WyborFactory.create(
                pytanie=p2,
                ankieta=ankieta,
                odpowiedz=OdpowiedzFactory.create(nazwa="5"),
            )
            WyborFactory.create(
                pytanie=p3,
                ankieta=ankieta,
                odpowiedz=OdpowiedzFactory.create(nazwa="4"),
            )
            WyborFactory.create(
                pytanie=p4,
                ankieta=ankieta,
                odpowiedz=OdpowiedzFactory.create(nazwa="test"),
            )
            WyborFactory.create(
                pytanie=PytanieFactory.create(),
                ankieta=ankieta,
                odpowiedz=OdpowiedzFactory.create(nazwa="test"),
            )
            WyborFactory.create(
                pytanie=PodpytanieFactory.create(pytanie__dotyczy_prowadzacych=False),
                ankieta=ankieta,
                odpowiedz=OdpowiedzFactory.create(nazwa="test"),
            )

        stars = mytags.ocena_szkolenia(termin.szkolenie)
        self.assertEqual(stars["count"], 21)
        self.assertEqual(stars["avg"], Decimal("4.3"))
        self.assertEqual(stars["css"], "4-3")

    def test_ocena_szkolenia_wersja_en(self):
        termin = TerminSzkoleniaFactory.create(
            odbylo_sie=True, szkolenie__language="en"
        )
        ankiety = AnkietaFactory.create_batch(21, termin_szkolenia=termin)
        UczestnikFactory.create_batch(10, status=3, termin=termin)
        UczestnikFactory.create_batch(
            10, status=3, uczestnik_wieloosobowy_ilosc_osob=10, termin=termin
        )

        p1 = PodpytanieFactory.create(pytanie__dotyczy_prowadzacych=True)
        p2 = PodpytanieFactory.create(pytanie__dotyczy_prowadzacych=True)

        for ankieta in ankiety:
            WyborFactory.create(
                pytanie=p1,
                ankieta=ankieta,
                odpowiedz=OdpowiedzFactory.create(nazwa="5"),
            )
            WyborFactory.create(
                pytanie=p2,
                ankieta=ankieta,
                odpowiedz=OdpowiedzFactory.create(nazwa="3"),
            )

        # Dodajemy kilka ankiet i uczestnikow w PL
        termin.szkolenie.base_translation = SzkolenieFactory.create(
            archiwalna_liczba_przeszkolonych=11
        )
        termin.szkolenie.save()

        t = TerminSzkoleniaFactory.create(
            odbylo_sie=True, szkolenie=termin.szkolenie.base_translation
        )

        ankiety = AnkietaFactory.create_batch(5, termin_szkolenia=t)
        UczestnikFactory.create_batch(50, status=3, termin=t)

        for ankieta in ankiety:
            WyborFactory.create(
                pytanie=p1,
                ankieta=ankieta,
                odpowiedz=OdpowiedzFactory.create(nazwa="5"),
            )
            WyborFactory.create(
                pytanie=p2,
                ankieta=ankieta,
                odpowiedz=OdpowiedzFactory.create(nazwa="5"),
            )

        stars = mytags.ocena_szkolenia(termin.szkolenie)
        self.assertEqual(stars["count"], 171)
        self.assertEqual(stars["avg"], Decimal("4.2"))
        self.assertEqual(stars["css"], "4-2")

    def test_ocena_szkolenia_z_always_default(self):
        termin = TerminSzkoleniaFactory.create(
            szkolenie__archiwalna_liczba_przeszkolonych=18, odbylo_sie=True
        )
        ankiety = AnkietaFactory.create_batch(2, termin_szkolenia=termin)
        for ankieta in ankiety:
            UczestnikFactory.create(status=3, termin=termin)

        p1 = PodpytanieFactory.create(pytanie__dotyczy_prowadzacych=True)
        p2 = PodpytanieFactory.create(pytanie__dotyczy_prowadzacych=True)
        p3 = PodpytanieFactory.create(pytanie__dotyczy_prowadzacych=True)
        p4 = PodpytanieFactory.create(pytanie__dotyczy_prowadzacych=True)

        for ankieta in ankiety:
            WyborFactory.create(
                pytanie=p1,
                ankieta=ankieta,
                odpowiedz=OdpowiedzFactory.create(nazwa="4"),
            )
            WyborFactory.create(
                pytanie=p2,
                ankieta=ankieta,
                odpowiedz=OdpowiedzFactory.create(nazwa="5"),
            )
            WyborFactory.create(
                pytanie=p3,
                ankieta=ankieta,
                odpowiedz=OdpowiedzFactory.create(nazwa="4"),
            )
            WyborFactory.create(
                pytanie=p4,
                ankieta=ankieta,
                odpowiedz=OdpowiedzFactory.create(nazwa="test"),
            )
            WyborFactory.create(
                pytanie=PytanieFactory.create(),
                ankieta=ankieta,
                odpowiedz=OdpowiedzFactory.create(nazwa="test"),
            )
            WyborFactory.create(
                pytanie=PodpytanieFactory.create(pytanie__dotyczy_prowadzacych=False),
                ankieta=ankieta,
                odpowiedz=OdpowiedzFactory.create(nazwa="test"),
            )

        stars = mytags.ocena_szkolenia(termin.szkolenie, always_default=True)
        self.assertEqual(stars["count"], 20)
        self.assertEqual(stars["avg"], Decimal("4.3"))
        self.assertEqual(stars["css"], "4-3")

    def test_ocena_tech_taga_ponizej_50_przeszkolonych(self):
        tagi = TagTechnologiaFactory.create_batch(10)
        lista_szkolen = SzkolenieFactory.create_batch(5, tagi_technologia=tagi) + list(
            SzkolenieFactory.create_batch(15, tagi_technologia=tagi[1:])
        )

        p = PodpytanieFactory.create(pytanie__dotyczy_prowadzacych=True)

        for szkolenie in lista_szkolen:
            termin = TerminSzkoleniaFactory.create(odbylo_sie=True, szkolenie=szkolenie)
            UczestnikFactory.create_batch(2, status=3, termin=termin)

            ankieta = AnkietaFactory.create()
            WyborFactory.create(
                pytanie=p, ankieta=ankieta, odpowiedz=OdpowiedzFactory.create(nazwa="5")
            )

        stars = mytags.ocena_tech_taga(tagi[0])
        self.assertEqual(stars["count"], None)
        self.assertEqual(stars["avg"], None)
        self.assertEqual(stars["css"], None)

    def test_ocena_tech_taga_ponizej_sredniej_4(self):
        tagi = TagTechnologiaFactory.create_batch(10)
        lista_szkolen = SzkolenieFactory.create_batch(5, tagi_technologia=tagi) + list(
            SzkolenieFactory.create_batch(15, tagi_technologia=tagi[1:])
        )

        p = PodpytanieFactory.create(pytanie__dotyczy_prowadzacych=True)

        for szkolenie in lista_szkolen:
            termin = TerminSzkoleniaFactory.create(odbylo_sie=True, szkolenie=szkolenie)
            UczestnikFactory.create_batch(10, status=3, termin=termin)

            ankieta = AnkietaFactory.create()
            WyborFactory.create(
                pytanie=p, ankieta=ankieta, odpowiedz=OdpowiedzFactory.create(nazwa="2")
            )

        stars = mytags.ocena_tech_taga(tagi[0])
        self.assertEqual(stars["count"], 50)
        self.assertEqual(stars["avg"], None)
        self.assertEqual(stars["css"], None)

    def test_ocena_tech_taga(self):
        tagi = TagTechnologiaFactory.create_batch(10)
        lista_szkolen = SzkolenieFactory.create_batch(5, tagi_technologia=tagi) + list(
            SzkolenieFactory.create_batch(15, tagi_technologia=tagi[1:])
        )

        p1 = PodpytanieFactory.create(pytanie__dotyczy_prowadzacych=True)
        p2 = PodpytanieFactory.create(pytanie__dotyczy_prowadzacych=True)
        p3 = PodpytanieFactory.create(pytanie__dotyczy_prowadzacych=True)
        p4 = PodpytanieFactory.create(pytanie__dotyczy_prowadzacych=True)

        for szkolenie in lista_szkolen:
            termin = TerminSzkoleniaFactory.create(odbylo_sie=True, szkolenie=szkolenie)
            UczestnikFactory.create_batch(10, status=3, termin=termin)
            UczestnikFactory.create_batch(2, status=1, termin=termin)

            ankiety = AnkietaFactory.create_batch(2, termin_szkolenia=termin)

            for ankieta in ankiety:
                WyborFactory.create(
                    pytanie=p1,
                    ankieta=ankieta,
                    odpowiedz=OdpowiedzFactory.create(nazwa="4"),
                )
                WyborFactory.create(
                    pytanie=p2,
                    ankieta=ankieta,
                    odpowiedz=OdpowiedzFactory.create(nazwa="5"),
                )
                WyborFactory.create(
                    pytanie=p3,
                    ankieta=ankieta,
                    odpowiedz=OdpowiedzFactory.create(nazwa="4"),
                )
                WyborFactory.create(
                    pytanie=p4,
                    ankieta=ankieta,
                    odpowiedz=OdpowiedzFactory.create(nazwa="test"),
                )
                WyborFactory.create(
                    pytanie=PytanieFactory.create(),
                    ankieta=ankieta,
                    odpowiedz=OdpowiedzFactory.create(nazwa="test"),
                )
                WyborFactory.create(
                    pytanie=PodpytanieFactory.create(
                        pytanie__dotyczy_prowadzacych=False
                    ),
                    ankieta=ankieta,
                    odpowiedz=OdpowiedzFactory.create(nazwa="test"),
                )

        tag = tagi[0]
        tag.archiwalna_liczba_przeszkolonych = 13
        tag.save()

        stars = mytags.ocena_tech_taga(tag)
        self.assertEqual(stars["count"], 63)
        self.assertEqual(stars["avg"], Decimal("4.3"))
        self.assertEqual(stars["css"], "4-3")

    def test_ocena_tech_taga_wersja_en(self):
        tag = TagTechnologiaFactory.create(
            language="en",
            archiwalna_liczba_przeszkolonych=13,
            base_translation=TagTechnologiaFactory.create(
                archiwalna_liczba_przeszkolonych=11
            ),
        )

        lista_szkolen = SzkolenieFactory.create_batch(
            5, tagi_technologia=[tag], language="en"
        )

        p1 = PodpytanieFactory.create(pytanie__dotyczy_prowadzacych=True)
        p2 = PodpytanieFactory.create(pytanie__dotyczy_prowadzacych=True)

        for szkolenie in lista_szkolen:
            termin = TerminSzkoleniaFactory.create(odbylo_sie=True, szkolenie=szkolenie)
            UczestnikFactory.create_batch(10, status=3, termin=termin)
            UczestnikFactory.create_batch(2, status=1, termin=termin)

            ankiety = AnkietaFactory.create_batch(2, termin_szkolenia=termin)

            for ankieta in ankiety:
                WyborFactory.create(
                    pytanie=p1,
                    ankieta=ankieta,
                    odpowiedz=OdpowiedzFactory.create(nazwa="4"),
                )
                WyborFactory.create(
                    pytanie=p2,
                    ankieta=ankieta,
                    odpowiedz=OdpowiedzFactory.create(nazwa="5"),
                )

        # Dodajemy kilka ankiet i uczestnikow w PL
        szkolenie = SzkolenieFactory.create(tagi_technologia=[tag.base_translation])

        termin = TerminSzkoleniaFactory.create(odbylo_sie=True, szkolenie=szkolenie)
        UczestnikFactory.create_batch(40, status=3, termin=termin)
        ankiety = AnkietaFactory.create_batch(2, termin_szkolenia=termin)

        for ankieta in ankiety:
            WyborFactory.create(
                pytanie=p1,
                ankieta=ankieta,
                odpowiedz=OdpowiedzFactory.create(nazwa="5"),
            )
            WyborFactory.create(
                pytanie=p2,
                ankieta=ankieta,
                odpowiedz=OdpowiedzFactory.create(nazwa="5"),
            )

        stars = mytags.ocena_tech_taga(tag)
        self.assertEqual(stars["count"], 114)
        self.assertEqual(stars["avg"], Decimal("4.6"))
        self.assertEqual(stars["css"], "4-6")

    def test_index_by_technologia_top_wariant_A_dlugosc_tekstu(self):
        tag = TagTechnologiaFactory.create()
        placement = HighlightPlacementFactory.create(page=tag)

        placement.highlight.tresc = "a" * 701
        placement.highlight.save()

        out = mytags.index_by_technologia_top(tag, placement, None, None)
        self.assertIn("lp-zbiorczyMax".format(tag.nazwa), out)

    def test_index_by_technologia_top_wariant_A_obrazke_w_tresci(self):
        tag = TagTechnologiaFactory.create()
        placement = HighlightPlacementFactory.create(page=tag)

        placement.highlight.tresc = '<IMG src="...">'
        placement.highlight.save()

        out = mytags.index_by_technologia_top(tag, placement, None, None)
        self.assertIn("lp-zbiorczyMax".format(tag.nazwa), out)

    def test_index_by_technologia_top_wariant_B(self):
        tag = TagTechnologiaFactory.create()
        placement = HighlightPlacementFactory.create(page=tag)

        placement.highlight.tresc = "ABC"
        placement.highlight.obrazek = "..."
        placement.highlight.save()

        out = mytags.index_by_technologia_top(tag, placement, None, None)
        self.assertIn("lp-zbiorczyMedium".format(tag.nazwa), out)

    def test_index_by_technologia_top_wariant_C(self):
        tag = TagTechnologiaFactory.create()
        placement = HighlightPlacementFactory.create(page=tag)

        placement.highlight.tresc = "ABC"
        placement.highlight.save()

        out = mytags.index_by_technologia_top(tag, placement, None, None)
        self.assertIn("lp-noCoach".format(tag.nazwa), out)

    def test_index_by_technologia_top_wariant_D(self):
        tag = TagTechnologiaFactory.create()

        out = mytags.index_by_technologia_top(tag, None, None, None)
        self.assertIn("Szkolenia: {0}".format(tag.nazwa), out)

    def test_css_included_gdy_brak_zagniezdzen(self):
        self.assertEqual(mytags.css_included("abc"), "")

    def test_css_included_gdy_zagniezdzenia(self):
        MyFlatPageFactory.create(slug="abc1", css_content="<style>1")
        MyFlatPageFactory.create(slug="abc2", css_content=" 2")
        MyFlatPageFactory.create(slug="abc3")

        self.assertEqual(
            mytags.css_included(
                """
        a
        b
        c [[include:pl/abc1]]


        [[include:pl/abc2]][[include:pl/abc3]]


        [[include:asdfasdf aasdf asdfas]]
        [[include:]]
        [[include:ół]]
        [[include:pl/abc2               ] ]
        """
            ),
            "<style>1 2",
        )

    def test_trainers_included(self):
        out = mytags.trainers_included("[[staticpage_lista_trenerow_pl:abc]]", "pl")
        self.assertEqual(out, "could not parse an ID staticpage_lista_trenerow")

        out = mytags.trainers_included("[[staticpage_lista_trenerow_pl:1,2,3,a]]", "pl")
        self.assertEqual(out, "could not parse an ID staticpage_lista_trenerow")

        prowadzacy = ProwadzacyFactory.create_batch(10)

        out = mytags.trainers_included(
            "[[staticpage_lista_trenerow_pl:{0},{1}]]".format(
                prowadzacy[0].pk, prowadzacy[1].pk
            ),
            "pl",
        )
        self.assertEqual(out.count("lp-singleDescription-mobile"), 2)

        out = mytags.trainers_included("[[staticpage_lista_trenerow_pl:0]]", "pl")
        self.assertEqual(out.count("lp-singleDescription-mobile"), 0)


###############
# Kontynuacje
###############


class ContinuationLogTestCase(ALXTestCase):
    def test_any_continuation_exists_gdy_brak(self):
        szkolenie = SzkolenieFactory.create()
        ContinuationLogFactory.create_batch(2, training=szkolenie)  # not related

        self.assertFalse(
            ContinuationLog.objects.any_continuation_exists(
                "<EMAIL>", training=szkolenie
            )
        )

    def test_any_continuation_exists_gdy_inny_email(self):
        szkolenie = SzkolenieFactory.create()
        ContinuationLogFactory.create_batch(2, training=szkolenie)  # not related
        ContinuationLogFactory.create(training=szkolenie, email="<EMAIL>")

        self.assertFalse(
            ContinuationLog.objects.any_continuation_exists(
                "<EMAIL>", training=szkolenie
            )
        )

    def test_any_continuation_exists_gdy_istanieje(self):
        szkolenie = SzkolenieFactory.create()
        ContinuationLogFactory.create_batch(2, training=szkolenie)  # not related
        ContinuationLogFactory.create(training=szkolenie, email="<EMAIL>")

        self.assertTrue(
            ContinuationLog.objects.any_continuation_exists(
                "<EMAIL>", training=szkolenie
            )
        )

    def test_allow_second_notification(self):
        szkolenie = SzkolenieFactory.create()
        ContinuationLogFactory.create_batch(2, training=szkolenie)  # not related

        self.assertTrue(
            ContinuationLog.objects.allow_second_notification(
                "<EMAIL>", training=szkolenie
            )
        )

    def test_allow_second_notification_gdy_istnieje_drugie(self):
        szkolenie = SzkolenieFactory.create()
        ContinuationLogFactory.create_batch(2, training=szkolenie)  # not related
        ContinuationLogFactory.create(
            email="<EMAIL>",
            training=szkolenie,
            notification_type="second_notification",
        )

        self.assertFalse(
            ContinuationLog.objects.allow_second_notification(
                "<EMAIL>", training=szkolenie
            )
        )

    def test_allow_second_notification_gdy_istnieje_pierwsze_krocej_jak_6tyg(self):
        szkolenie = SzkolenieFactory.create()
        ContinuationLogFactory.create_batch(2, training=szkolenie)  # not related
        ContinuationLogFactory.create(
            email="<EMAIL>",
            training=szkolenie,
            notification_type="first_notification",
        )

        self.assertFalse(
            ContinuationLog.objects.allow_second_notification(
                "<EMAIL>", training=szkolenie
            )
        )

    def test_allow_second_notification_gdy_istnieje_pierwsze_dluzej_jak_6tyg(self):
        szkolenie = SzkolenieFactory.create()
        ContinuationLogFactory.create_batch(2, training=szkolenie)  # not related
        log = ContinuationLogFactory.create(
            email="<EMAIL>",
            training=szkolenie,
            notification_type="first_notification",
        )
        log.created_at -= datetime.timedelta(days=50)
        log.save()

        self.assertTrue(
            ContinuationLog.objects.allow_second_notification(
                "<EMAIL>", training=szkolenie
            )
        )

    def test_allow_second_notification_gdy_istnieje_pierwsze_ale_inny_mail(self):
        szkolenie = SzkolenieFactory.create()
        log = ContinuationLogFactory.create(
            email="<EMAIL>",
            training=szkolenie,
            notification_type="first_notification",
        )
        log.created_at -= datetime.timedelta(days=50)
        log.save()

        self.assertTrue(
            ContinuationLog.objects.allow_second_notification(
                "<EMAIL>", training=szkolenie
            )
        )

    def test_allow_second_notification_gdy_istnieje_pierwsze_i_drugie(self):
        szkolenie = SzkolenieFactory.create()
        ContinuationLogFactory.create_batch(2, training=szkolenie)  # not related
        log = ContinuationLogFactory.create(
            email="<EMAIL>",
            training=szkolenie,
            notification_type="first_notification",
        )
        log.created_at -= datetime.timedelta(days=50)
        log.save()
        ContinuationLogFactory.create(
            email="<EMAIL>",
            training=szkolenie,
            notification_type="second_notification",
        )

        self.assertFalse(
            ContinuationLog.objects.allow_second_notification(
                "<EMAIL>", training=szkolenie
            )
        )


@override_settings(
    DOMENY_DLA_JEZYKOW={"pl": "www.alx.dev", "en": "www.alx-training.co.dev"},
    MAIL_FROM_ADDRESS_EN="<EMAIL>",
    MAIL_FROM_ADDRESS="<EMAIL>",
)
class ContinuationTrainingManagementTestCase(ALXTestCase):
    def test_send_email_pl_bez_terminow(self):
        kontynuacja = SzkolenieFactory.create()
        uczestnik = UczestnikFactory.create(
            termin__szkolenie__kontynuacja=kontynuacja, email="<EMAIL>"
        )

        cmd = continuation_training.Command()
        result = cmd.send_email(
            uczestnik, kontynuacja, kod_rabatowy=DiscountCodeFactory.create()
        )

        self.assertTrue(result)
        self.assertEqual(len(mail.outbox), 1)

        email = mail.outbox[0]

        self.assertEqual(
            "Chcesz kontynuować naukę? Masz rabat na kolejny kurs.", email.subject
        )
        self.assertIn(uczestnik.termin.szkolenie.nazwa, email.body)
        self.assertIn(kontynuacja.nazwa, email.body)
        self.assertNotIn("Terminy", email.body)
        self.assertEqual(email.from_email, "<EMAIL>")
        self.assertEqual(email.to, ["<EMAIL>"])
        self.assertEqual(DiscountCode.objects.all().count(), 1)

    def test_send_email_pl_z_terminami(self):
        kontynuacja = KursFactory.create()
        uczestnik = UczestnikFactory.create(termin__szkolenie__kontynuacja=kontynuacja)

        cmd = continuation_training.Command()
        result = cmd.send_email(
            uczestnik,
            kontynuacja,
            TerminSzkoleniaFactory.create_batch(2, szkolenie=kontynuacja),
            DiscountCodeFactory.create(),
        )

        self.assertTrue(result)
        self.assertEqual(len(mail.outbox), 1)

        self.assertIn("terminach i trybach", mail.outbox[0].body)

    def test_send_email_en_bez_terminow(self):
        szkolenie = SzkolenieFactory(
            kontynuacja=SzkolenieFactory.create(language="en"), language="en"
        )
        uczestnik = UczestnikFactory.create(
            termin__szkolenie=szkolenie, email="<EMAIL>"
        )

        cmd = continuation_training.Command()
        result = cmd.send_email(uczestnik, szkolenie.kontynuacja)

        self.assertTrue(result)
        self.assertEqual(len(mail.outbox), 1)

        email = mail.outbox[0]

        self.assertEqual(
            "Chcesz kontynuować naukę? Masz rabat na kolejny kurs.", email.subject
        )
        self.assertIn(szkolenie.nazwa, email.body)
        self.assertIn(szkolenie.kontynuacja.nazwa, email.body)
        self.assertEqual(email.from_email, "<EMAIL>")
        self.assertEqual(email.to, ["<EMAIL>"])

    def test_send_email_en_z_terminami(self):
        activate("en")

        szkolenie = SzkolenieFactory(
            kontynuacja=SzkolenieFactory.create(language="en"), language="en"
        )
        uczestnik = UczestnikFactory.create(termin__szkolenie=szkolenie)

        cmd = continuation_training.Command()
        result = cmd.send_email(
            uczestnik,
            szkolenie.kontynuacja,
            TerminSzkoleniaEnFactory.create_batch(2, szkolenie=szkolenie.kontynuacja),
            DiscountCodeFactory.create(),
        )

        self.assertTrue(result)
        self.assertEqual(len(mail.outbox), 1)

        self.assertIn("terminach i trybach", mail.outbox[0].body)

    def test_send_email_to_trener_gdy_brak_prowadzacego(self):
        termin = TerminSzkoleniaFactory.create(prowadzacy=None)

        cmd = continuation_training.Command()
        result = cmd.send_email_to_trener(termin)

        self.assertFalse(result)
        self.assertEqual(len(mail.outbox), 0)

    def test_send_email_to_trener_gdy_brak_uzytkownika_prowadzacego(self):
        termin = TerminSzkoleniaFactory.create(prowadzacy__user=None)

        cmd = continuation_training.Command()
        result = cmd.send_email_to_trener(termin)

        self.assertFalse(result)
        self.assertEqual(len(mail.outbox), 0)

    def test_send_email_to_trener(self):
        kontynuacja = SzkolenieFactory.create()
        termin = TerminSzkoleniaFactory.create(
            szkolenie__kontynuacja=kontynuacja,
            prowadzacy__user=UserFactory.create(email="<EMAIL>"),
        )

        cmd = continuation_training.Command()
        result = cmd.send_email_to_trener(termin)

        self.assertTrue(result)
        self.assertEqual(len(mail.outbox), 1)

        email = mail.outbox[0]

        self.assertEqual(
            "Wspomnij ludziom na sali o kontynuacji Twojego szkolenia.", email.subject
        )
        self.assertIn(kontynuacja.nazwa, email.body)
        self.assertEqual(email.from_email, "<EMAIL>")
        self.assertEqual(email.to, ["<EMAIL>"])

    def test_send_first_notification_gdy_brak_terminow(self):
        cmd = continuation_training.Command()
        cmd.send_first_notification()

        self.assertEqual(len(mail.outbox), 0)

        TerminSzkoleniaFactory.create(
            szkolenie=SzkolenieFactory.create(
                maile_o_kontynuacji=True,
                kontynuacja=SzkolenieFactory.create(),
            ),
            odbylo_sie=True,
            termin_zakonczenia=datetime.date.today(),
        )

        cmd = continuation_training.Command()
        cmd.send_first_notification()

        self.assertEqual(len(mail.outbox), 0)

    def test_send_first_notification_gdy_brak_uczestnikow_i_brak_prowadzacego(self):
        TerminSzkoleniaFactory.create(
            szkolenie=SzkolenieFactory.create(
                maile_o_kontynuacji=True,
                kontynuacja=SzkolenieFactory.create(),
            ),
            odbylo_sie=True,
            termin_zakonczenia=datetime.date.today() + datetime.timedelta(days=1),
        )

        cmd = continuation_training.Command()
        cmd.send_first_notification()

        self.assertEqual(len(mail.outbox), 0)

        self.assertEqual(TerminSzkoleniaLog.objects.all().count(), 0)

    def test_send_first_notification_gdy_brak_uczestnikow_ale_jest_prowadzacy(self):
        TerminSzkoleniaFactory.create(
            szkolenie=SzkolenieFactory.create(
                maile_o_kontynuacji=True,
                kontynuacja=SzkolenieFactory.create(),
            ),
            odbylo_sie=True,
            termin_zakonczenia=datetime.date.today() + datetime.timedelta(days=1),
            prowadzacy__user=UserFactory.create(email="<EMAIL>"),
        )

        cmd = continuation_training.Command()
        cmd.send_first_notification()

        self.assertEqual(len(mail.outbox), 1)

        self.assertEqual(
            "Wspomnij ludziom na sali o kontynuacji Twojego szkolenia.",
            mail.outbox[0].subject,
        )
        self.assertEqual(TerminSzkoleniaLog.objects.all().count(), 1)

        # uruchamiamy zadanie jeszcze raz
        mail.outbox = []

        cmd = continuation_training.Command()
        cmd.send_first_notification()

        self.assertEqual(len(mail.outbox), 0)

        self.assertEqual(TerminSzkoleniaLog.objects.all().count(), 1)

    def test_send_first_notification_gdy_uczestnicy_bez_newslettera(self):
        termin = TerminSzkoleniaFactory.create(
            szkolenie=SzkolenieFactory.create(
                maile_o_kontynuacji=True,
                kontynuacja=SzkolenieFactory.create(),
            ),
            odbylo_sie=True,
            termin_zakonczenia=datetime.date.today() + datetime.timedelta(days=1),
        )

        UczestnikFactory.create_batch(2, status=1, termin=termin)

        cmd = continuation_training.Command()
        cmd.send_first_notification()

        self.assertEqual(len(mail.outbox), 2)

    def test_send_first_notification_gdy_uczestnicy_wypisani(self):
        termin = TerminSzkoleniaFactory.create(
            szkolenie=SzkolenieFactory.create(
                maile_o_kontynuacji=True,
                kontynuacja=SzkolenieFactory.create(),
            ),
            odbylo_sie=True,
            termin_zakonczenia=datetime.date.today() + datetime.timedelta(days=1),
        )

        uczestnicy = UczestnikFactory.create_batch(2, status=1, termin=termin)
        for uczestnik in uczestnicy:
            ContinuationUnsubscribedFactory.create(email=uczestnik.email)

        cmd = continuation_training.Command()
        cmd.send_first_notification()

        self.assertEqual(len(mail.outbox), 0)

    def test_send_first_notification(self):
        kontynuacja = KursFactory.create()
        TerminSzkoleniaFactory.create_batch(2, szkolenie=kontynuacja)
        termin = TerminSzkoleniaFactory.create(
            szkolenie=SzkolenieFactory.create(
                maile_o_kontynuacji=True,
                kontynuacja=kontynuacja,
            ),
            odbylo_sie=True,
            termin_zakonczenia=datetime.date.today() + datetime.timedelta(days=1),
        )

        # ----- not related
        UczestnikFactory.create(status=1, termin=termin, email="<EMAIL>")
        ContinuationUnsubscribedFactory.create(email="<EMAIL>")
        UczestnikFactory.create_batch(3, status=0, termin=termin)
        for uczestnik in UczestnikFactory.create_batch(3, status=3, termin=termin):
            ContinuationLogFactory.create(
                training=termin.szkolenie.kontynuacja,
                email=uczestnik.email,
                discount_code=None,
            )
        # ----------------------

        UczestnikFactory.create_batch(2, status=3, termin=termin)

        self.assertEqual(ContinuationLog.objects.all().count(), 3)

        cmd = continuation_training.Command()
        cmd.send_first_notification()

        self.assertEqual(len(mail.outbox), 2)

        self.assertIn("kontynuacje/anuluj/", mail.outbox[0].body)

        self.assertEqual(ContinuationLog.objects.all().count(), 5)
        log = ContinuationLog.objects.all()[0]
        self.assertEqual(log.notification_type, "first_notification")
        self.assertTrue(log.participant)
        self.assertTrue(log.discount_code)

        self.assertEqual(DiscountCode.objects.all().count(), 2)
        discount_code = DiscountCode.objects.all()[0]
        self.assertEqual(discount_code.discount, 7)
        self.assertEqual(discount_code.source, "continuation_training")

        # uruchamiamy zadanie jeszcze raz
        mail.outbox = []

        cmd = continuation_training.Command()
        cmd.send_first_notification()

        self.assertEqual(len(mail.outbox), 0)
        self.assertEqual(ContinuationLog.objects.all().count(), 5)
        self.assertEqual(DiscountCode.objects.all().count(), 2)

    def test_send_notification_en(self):
        termin = TerminSzkoleniaEnFactory.create(
            szkolenie=SzkolenieFactory.create(
                maile_o_kontynuacji=True,
                kontynuacja=SzkolenieFactory.create(language="en"),
                language="en",
            ),
            odbylo_sie=True,
            termin_zakonczenia=datetime.date.today() + datetime.timedelta(days=1),
        )

        UczestnikFactory.create(status=3, termin=termin)

        cmd = continuation_training.Command()
        cmd.send_first_notification()

        self.assertEqual(len(mail.outbox), 1)

        self.assertIn("en/continuations/cancel/", mail.outbox[0].body)

        self.assertEqual(ContinuationLog.objects.all().count(), 1)

    def test_send_second_notification_gdy_brak_terminow(self):
        cmd = continuation_training.Command()
        cmd.send_second_notification()

        self.assertEqual(len(mail.outbox), 0)

        TerminSzkoleniaFactory.create(
            szkolenie=SzkolenieFactory.create(
                maile_o_kontynuacji=True,
                kontynuacja=SzkolenieFactory.create(),
            ),
            odbylo_sie=True,
            termin=datetime.date.today(),
        )

        cmd = continuation_training.Command()
        cmd.send_second_notification()

        self.assertEqual(len(mail.outbox), 0)

    def test_send_second_notification_gdy_brak_uczestnikow(self):
        TerminSzkoleniaFactory.create(
            szkolenie=SzkolenieFactory.create(
                maile_o_kontynuacji=True,
                kontynuacja=SzkolenieFactory.create(),
            ),
            odbylo_sie=True,
            termin=datetime.date.today() - datetime.timedelta(days=1),
        )

        cmd = continuation_training.Command()
        cmd.send_second_notification()

        self.assertEqual(len(mail.outbox), 0)

    def test_send_second_notification_gdy_uczestnicy_bez_newslettera(self):
        termin = TerminSzkoleniaFactory.create(
            szkolenie=SzkolenieFactory.create(
                maile_o_kontynuacji=True,
                kontynuacja=SzkolenieFactory.create(),
            ),
            odbylo_sie=True,
            termin=datetime.date.today() - datetime.timedelta(days=1),
        )

        UczestnikFactory.create_batch(2, status=1, termin=termin)

        cmd = continuation_training.Command()
        cmd.send_second_notification()

        self.assertEqual(len(mail.outbox), 2)

    def test_send_second_notification_gdy_uczestnicy_wypisani(self):
        termin = TerminSzkoleniaFactory.create(
            szkolenie=SzkolenieFactory.create(
                maile_o_kontynuacji=True,
                kontynuacja=SzkolenieFactory.create(),
            ),
            odbylo_sie=True,
            termin=datetime.date.today() - datetime.timedelta(days=1),
        )

        uczestnicy = UczestnikFactory.create_batch(2, status=1, termin=termin)

        for uczestnik in uczestnicy:
            ContinuationUnsubscribedFactory.create(email=uczestnik.email)

        cmd = continuation_training.Command()
        cmd.send_first_notification()

        self.assertEqual(len(mail.outbox), 0)

    def test_send_second_notification(self):
        kontynuacja = KursFactory.create()
        TerminSzkoleniaFactory.create_batch(2, szkolenie=kontynuacja)
        termin = TerminSzkoleniaFactory.create(
            szkolenie=SzkolenieFactory.create(
                maile_o_kontynuacji=True,
                kontynuacja=kontynuacja,
            ),
            odbylo_sie=True,
            termin=datetime.date.today() - datetime.timedelta(days=1),
        )

        # ----- not related
        UczestnikFactory.create(status=1, termin=termin, email="<EMAIL>")
        ContinuationUnsubscribedFactory.create(email="<EMAIL>")
        UczestnikFactory.create_batch(3, status=0, termin=termin)
        for uczestnik in UczestnikFactory.create_batch(3, status=3, termin=termin):
            ContinuationLogFactory.create(
                training=termin.szkolenie.kontynuacja,
                email=uczestnik.email,
                discount_code=None,
            )
        # ----------------------

        UczestnikFactory.create_batch(2, status=3, termin=termin)

        self.assertEqual(ContinuationLog.objects.all().count(), 3)

        cmd = continuation_training.Command()
        cmd.send_second_notification()

        self.assertEqual(len(mail.outbox), 2)

        self.assertIn("kontynuacje/anuluj/", mail.outbox[0].body)

        self.assertEqual(ContinuationLog.objects.all().count(), 5)
        log = ContinuationLog.objects.all()[0]
        self.assertEqual(log.notification_type, "second_notification")
        self.assertTrue(log.participant)
        self.assertTrue(log.discount_code)

        self.assertEqual(DiscountCode.objects.all().count(), 2)
        discount_code = DiscountCode.objects.all()[0]
        self.assertEqual(discount_code.discount, 7)
        self.assertEqual(discount_code.source, "continuation_training")

        # uruchamiamy zadanie jeszcze raz
        mail.outbox = []

        cmd = continuation_training.Command()
        cmd.send_second_notification()

        self.assertEqual(len(mail.outbox), 0)
        self.assertEqual(ContinuationLog.objects.all().count(), 5)
        self.assertEqual(DiscountCode.objects.all().count(), 2)

    @patch.object(continuation_training.Command, "send_second_notification")
    def test_handle_noargs_wywolanie_drugiego_powiadomienia(self, patch_method):
        with freeze_time("2018-01-01"):
            cmd = continuation_training.Command()
            cmd.handle()

            self.assertEqual(patch_method.call_count, 0)

        with freeze_time("2018-02-15"):
            cmd = continuation_training.Command()
            cmd.handle()

            self.assertEqual(patch_method.call_count, 1)

    def test_handle_noargs_najpierw_tylko_pierwsze_powiadomienie(self):
        termin = TerminSzkoleniaFactory.create(
            szkolenie=SzkolenieFactory.create(
                maile_o_kontynuacji=True,
                kontynuacja=SzkolenieFactory.create(),
            ),
            odbylo_sie=True,
            termin=datetime.date(2018, 2, 9),
            termin_zakonczenia=datetime.date(2018, 2, 16),
        )
        for uczestnik in UczestnikFactory.create_batch(2, status=3, termin=termin):
            OdbiorcaFactory.create(email=uczestnik.email)

        with freeze_time("2018-02-15"):
            cmd = continuation_training.Command()
            cmd.handle()

        self.assertEqual(len(mail.outbox), 2)
        self.assertEqual(ContinuationLog.objects.all().count(), 2)
        self.assertEqual(
            ContinuationLog.objects.filter(
                notification_type="first_notification"
            ).count(),
            2,
        )

        mail.outbox = []

        with freeze_time("2018-02-15"):
            cmd = continuation_training.Command()
            cmd.handle()

        self.assertEqual(len(mail.outbox), 0)
        self.assertEqual(ContinuationLog.objects.all().count(), 2)
        self.assertEqual(
            ContinuationLog.objects.filter(
                notification_type="first_notification"
            ).count(),
            2,
        )

    def test_handle_noargs_najpierw_tylko_drugie_powiadomienie(self):
        termin = TerminSzkoleniaFactory.create(
            szkolenie=SzkolenieFactory.create(
                maile_o_kontynuacji=True,
                kontynuacja=SzkolenieFactory.create(),
            ),
            odbylo_sie=True,
            termin=datetime.date(2018, 2, 9),
            termin_zakonczenia=datetime.date(2018, 2, 14),
        )
        for uczestnik in UczestnikFactory.create_batch(2, status=3, termin=termin):
            OdbiorcaFactory.create(email=uczestnik.email)

        with freeze_time("2018-02-15"):
            cmd = continuation_training.Command()
            cmd.handle()

        self.assertEqual(len(mail.outbox), 2)
        self.assertEqual(ContinuationLog.objects.all().count(), 2)
        self.assertEqual(
            ContinuationLog.objects.filter(
                notification_type="second_notification"
            ).count(),
            2,
        )

        mail.outbox = []

        with freeze_time("2018-02-15"):
            cmd = continuation_training.Command()
            cmd.handle()

        self.assertEqual(len(mail.outbox), 0)
        self.assertEqual(ContinuationLog.objects.all().count(), 2)
        self.assertEqual(
            ContinuationLog.objects.filter(
                notification_type="second_notification"
            ).count(),
            2,
        )


@override_settings(
    DOMENY_DLA_JEZYKOW={
        "pl": "pl-domena",
        "en": "en-domena",
    },
    MAIL_OBSLUGA="<EMAIL>",
    MAIL_ARCHIVE_MONITOR="<EMAIL>",
    EMAIL_BACKEND="django.core.mail.backends.locmem.EmailBackend",
)
class TerminSzkoleniaMailDoGrupyCeleryUnitTestCase(ALXTestCase):
    def test_send_if_no_user(self):
        obj = TerminSzkoleniaMailFactory.create(
            subject="tytuł ...", message="treść ..."
        )

        www.tasks.termin_szkolenia_mail_do_grupy.delay(obj.pk)

        self.assertEqual(www.models.TerminSzkoleniaMailUczestnik.objects.count(), 0)
        self.assertEqual(len(mail.outbox), 0)

    def test_send_if_no_active_user(self):
        obj = TerminSzkoleniaMailFactory.create(
            subject="tytuł ...", message="treść ...", participant_status=1
        )
        UczestnikFactory.create_batch(2, termin=obj.term, status=2)

        www.tasks.termin_szkolenia_mail_do_grupy.delay(obj.pk)

        self.assertEqual(www.models.TerminSzkoleniaMailUczestnik.objects.count(), 0)
        self.assertEqual(len(mail.outbox), 0)

    def test_send_if_already_sent(self):
        obj = TerminSzkoleniaMailFactory.create(
            subject="tytuł ...", message="treść ..."
        )
        TerminSzkoleniaMailUczestnikFactory.create(
            term_mail=obj,
            participant=UczestnikFactory.create(termin=obj.term, status=1),
        )
        TerminSzkoleniaMailUczestnikFactory.create(
            term_mail=obj,
            participant=UczestnikFactory.create(termin=obj.term, status=1),
        )

        www.tasks.termin_szkolenia_mail_do_grupy.delay(obj.pk)

        self.assertEqual(www.models.TerminSzkoleniaMailUczestnik.objects.count(), 2)
        self.assertEqual(len(mail.outbox), 0)

    def test_send_for_status_1(self):
        obj = TerminSzkoleniaMailFactory.create(
            subject="tytuł ...",
            message="treść ...",
            author=UserFactory.create(first_name="Jan", last_name="Kowalski"),
            participant_status=1,
        )
        UczestnikFactory.create(termin=obj.term, email="", status=1)
        UczestnikFactory.create(termin=obj.term, status=1)
        UczestnikFactory.create(termin=obj.term, email="<EMAIL>", status=1)
        UczestnikFactory.create(termin=obj.term, status=-1)

        www.tasks.termin_szkolenia_mail_do_grupy.delay(obj.pk)

        self.assertEqual(www.models.TerminSzkoleniaMailUczestnik.objects.count(), 2)
        self.assertEqual(len(mail.outbox), 2)

        self.assertEqual(mail.outbox[0].to, ["<EMAIL>"])
        self.assertEqual(mail.outbox[0].from_email, "Jan Kowalski <<EMAIL>>")
        self.assertEqual(mail.outbox[0].bcc, ["<EMAIL>"])
        self.assertIn("treść ...", mail.outbox[0].body)
        self.assertIn("tytuł ...", mail.outbox[0].subject)

    def test_send_for_status_99(self):
        obj = TerminSzkoleniaMailFactory.create(
            subject="tytuł ...",
            message="treść ...",
            author=UserFactory.create(first_name="Jan", last_name="Kowalski"),
            participant_status=99,
        )
        UczestnikFactory.create(termin=obj.term, email="", status=1)
        UczestnikFactory.create(termin=obj.term, status=1, email="<EMAIL>")
        UczestnikFactory.create(termin=obj.term, status=4)
        UczestnikFactory.create(termin=obj.term, status=4)

        www.tasks.termin_szkolenia_mail_do_grupy.delay(obj.pk)

        self.assertEqual(www.models.TerminSzkoleniaMailUczestnik.objects.count(), 1)
        self.assertEqual(len(mail.outbox), 1)

        self.assertEqual(mail.outbox[0].to, ["<EMAIL>"])

    def test_send_with_file(self):
        obj = TerminSzkoleniaMailFactory.create(
            subject="tytuł ...",
            message="treść ...",
            participant_status=99,
            attachment=factory.django.ImageField(),
        )
        UczestnikFactory.create(termin=obj.term, status=1)

        www.tasks.termin_szkolenia_mail_do_grupy.delay(obj.pk)

        self.assertEqual(len(mail.outbox), 1)
        self.assertEqual(len(mail.outbox[0].attachments), 1)
