from datetime import date, timedelta
from decimal import Decimal
from django.test import TestCase, override_settings
from django.conf import settings
from www.templatetags.myfilters import myincludes
from www.models import Szkolenie, TerminSzkolenia, Lokalizacja, TagZawod, TagDlugosc, Panstwo
from i18n.models import Waluta


@override_settings(DEBUG=True, TEST_DATE=date(2025, 5, 9))
class Harmonogram2025TestCase(TestCase):
    def setUp(self):
        # Tworzymy potrzebne obiekty
        self.panstwo = Panstwo.objects.create(nazwa="Polska")
        self.waluta = Waluta.objects.create(nazwa="Polski złoty", symbol="PLN", kurs_kupna=Decimal("1.00"), zaliczka=Decimal("406.50"))
        self.tag_zawod = TagZawod.objects.create(nazwa="Programista", language="pl")
        self.tag_dlugosc = TagDlugosc.objects.create(nazwa="Szkolenie", slug="szkolenie")

        # Tworzymy testowe szkolenie
        self.szkolenie = Szkolenie.objects.create(
            nazwa="Test Szkolenie",
            slug="test-szkolenie",
            language="pl",
            aktywne=True,
            tag_zawod=self.tag_zawod,
            tag_dlugosc=self.tag_dlugosc,
            waluta=self.waluta,
            kod="TST",
            czas_dni=5,
            cena_bazowa=Decimal("100.00"),
            program="Program testowy",
            opis="Opis testowy",
            cel="Cel testowy"
        )

        # Tworzymy lokalizacje
        self.lokalizacja1 = Lokalizacja.objects.create(
            shortname="WAW",
            fullname="Warszawa",
            numer=1,
            panstwo=self.panstwo
        )

        # Tworzymy lokalizację zdalną
        self.lokalizacja_zdalna = Lokalizacja.objects.create(
            shortname="Zdalnie",
            fullname="Zdalnie",
            numer=11,  # ID zgodne z ZDALNA_IDS dla "pl"
            panstwo=self.panstwo
        )

        # Używamy daty testowej z ustawień
        self.today = settings.TEST_DATE

    def test_brak_terminow(self):
        """Test gdy nie ma żadnych terminów"""
        # Tryb dzienny, przyszłe terminy
        result = myincludes(f"[[harmonogram_2025:{self.szkolenie.slug};1;0]]")
        self.assertEqual(result, "-")

        # Tryb dzienny, przyszłe terminy, parametr przyszle_czy_w_toku ma domyślną wartość
        result = myincludes(f"[[harmonogram_2025:{self.szkolenie.slug};1;0]]")
        self.assertEqual(result, "-")

        # Tryb wieczorowy, przyszłe terminy
        result = myincludes(f"[[harmonogram_2025:{self.szkolenie.slug};2;0]]")
        self.assertEqual(result, "-")

        # Tryb weekendowy, przyszłe terminy
        result = myincludes(f"[[harmonogram_2025:{self.szkolenie.slug};3;0]]")
        self.assertEqual(result, "-")

        # Tryb dzienny, terminy w toku
        result = myincludes(f"[[harmonogram_2025:{self.szkolenie.slug};1;1]]")
        self.assertEqual(result, "-")

    def test_tryb_dzienny_jeden_termin(self):
        """Test dla trybu dziennego z jednym terminem, zgodny z formatowaniem bloków i dni tygodnia"""
        # Ustawiamy konkretną, znaną datę rozpoczęcia: poniedziałek, 19 maja 2025
        termin_date = date(2025, 5, 19)
        termin_end_date = termin_date + timedelta(days=4)

        # Tworzymy daty szczegółowe (ciąg 5 dni: 19–23.05.2025)
        daty_szczegolowo = []
        current_date = termin_date
        while current_date <= termin_end_date:
            daty_szczegolowo.append(current_date.isoformat())
            current_date += timedelta(days=1)

        TerminSzkolenia.objects.create(
            szkolenie=self.szkolenie,
            termin=termin_date,
            termin_zakonczenia=termin_end_date,
            lokalizacja=self.lokalizacja1,
            zamkniete=False,
            tryb=1,  # tryb dzienny
            daty_szczegolowo=','.join(daty_szczegolowo)
        )

        # Spodziewany wynik zgodny ze specyfikacją (bez line breaks)
        expected_result = "Warszawa: 19.05.2025 (poniedziałek), 20.05.2025 (wtorek), 21.05.2025 (środa), 22.05.2025 (czwartek), 23.05.2025 (piątek)."

        result = myincludes(f"[[harmonogram_2025:{self.szkolenie.slug};1]]")
        self.assertEqual(result, expected_result)

    def test_tryb_wieczorowy_jeden_termin(self):
        """Test dla trybu wieczorowego z jednym terminem"""
        # Tworzymy termin wieczorowy (5 dni)
        termin_date = self.today + timedelta(days=10)
        termin_end_date = termin_date + timedelta(days=4)

        # Tworzymy daty szczegółowe
        daty_szczegolowo = []
        current_date = termin_date
        while current_date <= termin_end_date:
            daty_szczegolowo.append(current_date.isoformat())
            current_date += timedelta(days=1)

        TerminSzkolenia.objects.create(
            szkolenie=self.szkolenie,
            termin=termin_date,
            termin_zakonczenia=termin_end_date,
            lokalizacja=self.lokalizacja1,
            zamkniete=False,
            tryb=2,  # tryb wieczorowy
            daty_szczegolowo=','.join(daty_szczegolowo)
        )

        # Sprawdzamy wynik
        result = myincludes(f"[[harmonogram_2025:{self.szkolenie.slug};2]]")
        self.assertEqual(result, 'Warszawa: 19.05.2025 (poniedziałek), 20.05, 21.05, 22.05, 23.05.2025.')

    def test_tryb_weekendowy_jeden_termin(self):
        """Test dla trybu weekendowego z jednym terminem"""
        # Tworzymy termin weekendowy (2 dni)
        termin_date = self.today + timedelta(days=10)
        termin_end_date = termin_date + timedelta(days=18)

        # Tworzymy daty szczegółowe
        daty_szczegolowo = ['2025-05-10', '2025-05-11', '2025-05-17', '2025-05-18']

        TerminSzkolenia.objects.create(
            szkolenie=self.szkolenie,
            termin=termin_date,
            termin_zakonczenia=termin_end_date,
            lokalizacja=self.lokalizacja1,
            zamkniete=False,
            tryb=3,  # tryb weekendowy
            daty_szczegolowo=','.join(daty_szczegolowo)
        )

        expected_result = (
            "Warszawa: 10.05.2025 (sobota), 11.05.2025 (niedziela),<br/>"
            "17.05.2025 (sobota), 18.05.2025 (niedziela)."
        )

        # Sprawdzamy wynik
        result = myincludes(f"[[harmonogram_2025:{self.szkolenie.slug};3]]")
        self.assertEqual(result, expected_result)

    def test_wiele_terminow(self):
        """Test dla wielu terminów"""
        # Tworzymy dwa terminy dzienne
        termin_date1 = self.today + timedelta(days=10)
        termin_end_date1 = termin_date1 + timedelta(days=4)

        # Tworzymy daty szczegółowe dla pierwszego terminu
        daty_szczegolowo1 = []
        current_date = termin_date1
        while current_date <= termin_end_date1:
            daty_szczegolowo1.append(current_date.isoformat())
            current_date += timedelta(days=1)

        TerminSzkolenia.objects.create(
            szkolenie=self.szkolenie,
            termin=termin_date1,
            termin_zakonczenia=termin_end_date1,
            lokalizacja=self.lokalizacja1,
            zamkniete=False,
            tryb=1,  # tryb dzienny
            daty_szczegolowo=','.join(daty_szczegolowo1)
        )

        termin_date2 = self.today + timedelta(days=30)
        termin_end_date2 = termin_date2 + timedelta(days=4)

        # Tworzymy daty szczegółowe dla drugiego terminu
        daty_szczegolowo2 = []
        current_date = termin_date2
        while current_date <= termin_end_date2:
            daty_szczegolowo2.append(current_date.isoformat())
            current_date += timedelta(days=1)

        TerminSzkolenia.objects.create(
            szkolenie=self.szkolenie,
            termin=termin_date2,
            termin_zakonczenia=termin_end_date2,
            lokalizacja=self.lokalizacja1,
            zamkniete=False,
            tryb=1,  # tryb dzienny
            daty_szczegolowo=','.join(daty_szczegolowo2)
        )

        expected = 'Warszawa: 19.05.2025 (poniedziałek), 20.05.2025 (wtorek), 21.05.2025 (środa), 22.05.2025 (czwartek), 23.05.2025 (piątek).<br/><br/>Warszawa: 08.06.2025 (niedziela), 09.06.2025 (poniedziałek), 10.06.2025 (wtorek), 11.06.2025 (środa), 12.06.2025 (czwartek).'

        # Sprawdzamy wynik
        result = myincludes(f"[[harmonogram_2025:{self.szkolenie.slug};1;0]]")
        self.assertEqual(result, expected)

    def test_termin_w_toku(self):
        """Test dla terminu w toku"""
        # Tworzymy termin w toku (start w przeszłości, koniec w przyszłości)
        termin_date = self.today - timedelta(days=2)
        termin_end_date = self.today + timedelta(days=3)

        # Tworzymy daty szczegółowe
        daty_szczegolowo = []
        current_date = termin_date
        while current_date <= termin_end_date:
            daty_szczegolowo.append(current_date.isoformat())
            current_date += timedelta(days=1)

        TerminSzkolenia.objects.create(
            szkolenie=self.szkolenie,
            termin=termin_date,
            termin_zakonczenia=termin_end_date,
            lokalizacja=self.lokalizacja1,
            zamkniete=False,
            tryb=1,  # tryb dzienny
            daty_szczegolowo=','.join(daty_szczegolowo)
        )

        # Sprawdzamy wynik dla terminu w toku
        result = myincludes(f"[[harmonogram_2025:{self.szkolenie.slug};1;1]]")
        self.assertIn(termin_date.strftime('%d.%m.%Y'), result)

        # Sprawdzamy, że termin nie pojawia się w przyszłych terminach
        result = myincludes(f"[[harmonogram_2025:{self.szkolenie.slug};1;0]]")
        self.assertEqual(result, "-")

    def test_id_zamiast_sluga(self):
        """Test gdy używamy ID zamiast sluga"""
        termin_date = self.today + timedelta(days=10)
        termin_end_date = termin_date + timedelta(days=4)

        # Tworzymy daty szczegółowe
        daty_szczegolowo = []
        current_date = termin_date
        while current_date <= termin_end_date:
            daty_szczegolowo.append(current_date.isoformat())
            current_date += timedelta(days=1)

        TerminSzkolenia.objects.create(
            szkolenie=self.szkolenie,
            termin=termin_date,
            termin_zakonczenia=termin_end_date,
            lokalizacja=self.lokalizacja1,
            zamkniete=False,
            tryb=1,  # tryb dzienny
            daty_szczegolowo=','.join(daty_szczegolowo)
        )

        result = myincludes(f"[[harmonogram_2025:{self.szkolenie.id};1;0]]")
        self.assertIn(termin_date.strftime('%d.%m.%Y'), result)

    def test_lokalizacja_zdalna(self):
        """Test dla lokalizacji zdalnej - powinna być wyświetlana jako 'Online'"""
        # Tworzymy termin z lokalizacją zdalną
        termin_date = self.today + timedelta(days=10)
        termin_end_date = termin_date + timedelta(days=4)

        # Tworzymy daty szczegółowe
        daty_szczegolowo = []
        current_date = termin_date
        while current_date <= termin_end_date:
            daty_szczegolowo.append(current_date.isoformat())
            current_date += timedelta(days=1)

        TerminSzkolenia.objects.create(
            szkolenie=self.szkolenie,
            termin=termin_date,
            termin_zakonczenia=termin_end_date,
            lokalizacja=self.lokalizacja_zdalna,  # Używamy lokalizacji zdalnej
            zamkniete=False,
            tryb=1,  # tryb dzienny
            daty_szczegolowo=','.join(daty_szczegolowo)
        )

        # Spodziewany wynik - lokalizacja powinna być wyświetlana jako "Online"
        expected_result = "Online: 19.05.2025 (poniedziałek), 20.05.2025 (wtorek), 21.05.2025 (środa), 22.05.2025 (czwartek), 23.05.2025 (piątek)."

        result = myincludes(f"[[harmonogram_2025:{self.szkolenie.slug};1]]")
        self.assertEqual(result, expected_result)

    def test_niepoprawne_parametry(self):
        """Test dla niepoprawnych parametrów"""
        # Brak trybu
        result = myincludes(f"[[harmonogram_2025:{self.szkolenie.slug}]]")
        self.assertIn("invalid parameters", result)

        # Niepoprawny tryb
        result = myincludes(f"[[harmonogram_2025:{self.szkolenie.slug};4;0]]")
        self.assertIn("invalid tryb parameter", result)

        # Niepoprawne ID szkolenia
        result = myincludes("[[harmonogram_2025:9999;1;0]]")
        self.assertIn("invalid szkolenie", result)
