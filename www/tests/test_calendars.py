import datetime
import json

from www.kalendarze import _update_termin_calendars, calendar_has_changed, ciaglosc_sal
from www.models import CalendarUpdate, TerminSzkolenia
from www.testfactories import (
    DzienSzkoleniaFactory,
    LokalizacjaFactory,
    ProwadzacyFactory,
    SalaFactory,
    SzkolenieFactory,
    TerminSzkoleniaFactory,
)
from www.testhelpers import ALXTestCase


class KalendarzeTestCase(ALXTestCase):
    def test_ciaglosc_sal(self):
        termin = TerminSzkoleniaFactory.create()
        termin.clean()
        termin.save()
        self.assertFalse(
            ciaglosc_sal(termin, termin.get_kalendarze_dane_flat()["sale"])
        )

        termin = TerminSzkoleniaFactory.create(
            sala=SalaFactory.create(calendar=""),
        )
        termin.clean()
        termin.save()
        self.assertFalse(
            ciaglosc_sal(termin, termin.get_kalendarze_dane_flat()["sale"])
        )

        termin = TerminSzkoleniaFactory.create(
            sala=SalaFactory.create(calendar="1"),
        )
        termin.clean()
        termin.save()
        self.assertTrue(ciaglosc_sal(termin, termin.get_kalendarze_dane_flat()["sale"]))

        termin = TerminSzkoleniaFactory.create()
        termin.clean()
        termin.save()
        DzienSzkoleniaFactory(
            terminszkolenia=termin,
            sala=SalaFactory.create(calendar=""),
            data=termin.termin,
        )
        self.assertFalse(
            ciaglosc_sal(termin, termin.get_kalendarze_dane_flat()["sale"])
        )

        termin = TerminSzkoleniaFactory.create()
        termin.clean()
        termin.save()
        DzienSzkoleniaFactory(
            terminszkolenia=termin,
            sala=SalaFactory.create(calendar="1"),
            data=termin.termin,
        )
        self.assertTrue(ciaglosc_sal(termin, termin.get_kalendarze_dane_flat()["sale"]))

        termin = TerminSzkoleniaFactory.create(
            sala=SalaFactory.create(calendar=""),
        )
        termin.clean()
        termin.save()
        DzienSzkoleniaFactory(
            terminszkolenia=termin,
            sala=SalaFactory.create(calendar="1"),
            data=termin.termin,
        )
        self.assertTrue(ciaglosc_sal(termin, termin.get_kalendarze_dane_flat()["sale"]))

        termin = TerminSzkoleniaFactory.create(
            szkolenie=SzkolenieFactory.create(czas_dni=2),
        )
        termin.clean()
        termin.save()
        DzienSzkoleniaFactory(
            terminszkolenia=termin,
            sala=SalaFactory.create(calendar="1"),
            data=termin.termin,
        )
        self.assertFalse(
            ciaglosc_sal(termin, termin.get_kalendarze_dane_flat()["sale"])
        )

        termin = TerminSzkoleniaFactory.create(
            szkolenie=SzkolenieFactory.create(czas_dni=2),
            sala=SalaFactory.create(calendar="1"),
        )
        termin.clean()
        termin.save()
        DzienSzkoleniaFactory(
            terminszkolenia=termin,
            sala=SalaFactory.create(calendar="1"),
            data=termin.termin,
        )
        self.assertTrue(ciaglosc_sal(termin, termin.get_kalendarze_dane_flat()["sale"]))

        termin = TerminSzkoleniaFactory.create(
            szkolenie=SzkolenieFactory.create(czas_dni=2),
        )
        termin.clean()
        termin.save()
        DzienSzkoleniaFactory(
            terminszkolenia=termin,
            sala=SalaFactory.create(calendar="1"),
            data=termin.termin,
        )
        DzienSzkoleniaFactory(
            terminszkolenia=termin,
            sala=SalaFactory.create(calendar="1"),
            data=termin.termin + datetime.timedelta(days=1),
        )
        self.assertTrue(ciaglosc_sal(termin, termin.get_kalendarze_dane_flat()["sale"]))

    def test_calendar_has_changed(self):
        termin = TerminSzkoleniaFactory.create(sala=SalaFactory.create(calendar="1"))
        termin.clean()
        termin.kalendarze_dane_flat = termin.get_kalendarze_dane_flat(as_json=True)
        termin.save()

        termin = TerminSzkolenia.objects.get(pk=termin.pk)

        self.assertFalse(
            calendar_has_changed(
                json.loads(termin._original_state["kalendarze_dane_flat"])["sale"][0],
                termin.get_kalendarze_dane_flat()["sale"],
            )
        )

        # Zmieniamy sale
        termin.sala = SalaFactory.create(calendar="1")
        termin.save()
        self.assertTrue(
            calendar_has_changed(
                json.loads(termin._original_state["kalendarze_dane_flat"])["sale"][0],
                termin.get_kalendarze_dane_flat()["sale"],
            )
        )

    def test_calendars_dla_nowego_obiektu_i_odbylo_sie_false(self):
        termin = TerminSzkoleniaFactory.create(
            odbylo_sie=False,
            sala=SalaFactory.create(calendar="1"),
            prowadzacy=ProwadzacyFactory.create(calendar="1"),
            lokalizacja=LokalizacjaFactory.create(calendar="1"),
        )
        termin.clean()
        termin.kalendarze_dane_flat = termin.get_kalendarze_dane_flat(as_json=True)
        termin.save()

        _update_termin_calendars(termin)

        # Nic nie powinno zostac dodane
        self.assertEqual(CalendarUpdate.objects.all().count(), 0)

    def test_calendars_dla_nowego_obiektu_i_odbylo_sie_null(self):
        termin = TerminSzkoleniaFactory.create(
            odbylo_sie=None,
            sala=SalaFactory.create(calendar="1"),
            prowadzacy=ProwadzacyFactory.create(calendar="1"),
            lokalizacja=LokalizacjaFactory.create(calendar="1"),
        )
        termin.clean()
        termin.save()

        _update_termin_calendars(termin)

        # Wpis dla lokalizacji
        self.assertEqual(CalendarUpdate.objects.all().count(), 1)

    def test_calendars_dla_nowego_obiektu_i_odbylo_sie_true(self):
        # Wpis dla sal i prowadzacych
        termin = TerminSzkoleniaFactory.create(
            odbylo_sie=True,
            sala=SalaFactory.create(calendar="1"),
            prowadzacy=ProwadzacyFactory.create(calendar="1"),
            lokalizacja=LokalizacjaFactory.create(calendar="1"),
        )
        termin.clean()
        termin.kalendarze_dane_flat = termin.get_kalendarze_dane_flat(as_json=True)
        termin.save()
        _update_termin_calendars(termin)
        self.assertEqual(CalendarUpdate.objects.all().count(), 2)

        # Wpis dla lokalizacji i prowadzacych
        CalendarUpdate.objects.all().delete()
        termin = TerminSzkoleniaFactory.create(
            odbylo_sie=True,
            sala=SalaFactory.create(calendar=""),
            prowadzacy=ProwadzacyFactory.create(calendar="1"),
            lokalizacja=LokalizacjaFactory.create(calendar="1"),
        )
        termin.clean()
        termin.kalendarze_dane_flat = termin.get_kalendarze_dane_flat(as_json=True)
        termin.save()

        _update_termin_calendars(termin)
        self.assertEqual(CalendarUpdate.objects.all().count(), 2)

    def test_calendars_dla_edycji_i_odbylo_sie_false(self):
        termin = TerminSzkoleniaFactory.create(
            odbylo_sie=False,
            sala=SalaFactory.create(calendar="1"),
            prowadzacy=ProwadzacyFactory.create(calendar="1"),
            lokalizacja=LokalizacjaFactory.create(calendar="1"),
        )
        termin.clean()
        termin.kalendarze_dane_flat = termin.get_kalendarze_dane_flat(as_json=True)
        termin.save()

        termin = TerminSzkolenia.objects.get(pk=termin.pk)

        _update_termin_calendars(termin)

        # Powinny zostac usuniete wpisy dla sali, prowadzacego i lokalizacji
        self.assertEqual(CalendarUpdate.objects.all().count(), 3)

    def test_calendars_dla_edycji_i_odbylo_sie_z_true_na_null(self):
        termin = TerminSzkoleniaFactory.create(
            odbylo_sie=True,
            sala=SalaFactory.create(calendar="1"),
            prowadzacy=ProwadzacyFactory.create(calendar="1"),
            lokalizacja=LokalizacjaFactory.create(calendar="1"),
        )
        termin.clean()
        termin.kalendarze_dane_flat = termin.get_kalendarze_dane_flat(as_json=True)
        termin.save()

        termin = TerminSzkolenia.objects.get(pk=termin.pk)
        termin.odbylo_sie = None
        termin.kalendarze_dane_flat = termin.get_kalendarze_dane_flat(as_json=True)
        termin.save()

        _update_termin_calendars(termin)

        # Powinny zostac usuniete wpisy dla sali, prowadzacego oraz dodane dla
        # lokalizacji
        self.assertEqual(CalendarUpdate.objects.all().count(), 3)

    def test_calendars_dla_edycji_i_odbylo_sie_z_true_na_null_ze_zmiana_lokalizacji(
        self,
    ):
        termin = TerminSzkoleniaFactory.create(
            odbylo_sie=True,
            sala=SalaFactory.create(calendar="1"),
            prowadzacy=ProwadzacyFactory.create(calendar="1"),
            lokalizacja=LokalizacjaFactory.create(calendar="1"),
        )
        termin.clean()
        termin.kalendarze_dane_flat = termin.get_kalendarze_dane_flat(as_json=True)
        termin.save()

        termin = TerminSzkolenia.objects.get(pk=termin.pk)
        termin.odbylo_sie = None
        termin.lokalizacja = LokalizacjaFactory.create(calendar="1")
        termin.kalendarze_dane_flat = termin.get_kalendarze_dane_flat(as_json=True)
        termin.save()

        _update_termin_calendars(termin)

        # Powinny zostac usuniete wpisy dla sali, prowadzacego, lokalizacji
        # oraz dodane dla nowej lokalizacji
        self.assertEqual(CalendarUpdate.objects.all().count(), 4)

    def test_calendars_dla_edycji_i_odbylo_sie_z_null_na_null(self):
        termin = TerminSzkoleniaFactory.create(
            odbylo_sie=None,
            sala=SalaFactory.create(calendar="1"),
            prowadzacy=ProwadzacyFactory.create(calendar="1"),
            lokalizacja=LokalizacjaFactory.create(calendar="1"),
        )
        termin.clean()
        termin.kalendarze_dane_flat = termin.get_kalendarze_dane_flat(as_json=True)
        termin.save()

        termin = TerminSzkolenia.objects.get(pk=termin.pk)

        _update_termin_calendars(termin)

        # Powinien zostac dodany wpis dla lokalizacji
        self.assertEqual(CalendarUpdate.objects.all().count(), 1)

    def test_calendars_dla_edycji_i_odbylo_sie_z_null_na_null_ze_zmiana_lokalizacji(
        self,
    ):
        termin = TerminSzkoleniaFactory.create(
            odbylo_sie=None,
            sala=SalaFactory.create(calendar="1"),
            prowadzacy=ProwadzacyFactory.create(calendar="1"),
            lokalizacja=LokalizacjaFactory.create(calendar="1"),
        )
        termin.clean()
        termin.kalendarze_dane_flat = termin.get_kalendarze_dane_flat(as_json=True)
        termin.save()

        termin = TerminSzkolenia.objects.get(pk=termin.pk)
        termin.lokalizacja = LokalizacjaFactory.create(calendar="1")
        termin.kalendarze_dane_flat = termin.get_kalendarze_dane_flat(as_json=True)
        termin.save()

        _update_termin_calendars(termin)

        # Powinien zostac dodany wpis usuniecia starej lokalizacji i
        # dodaniu nowej.
        self.assertEqual(CalendarUpdate.objects.all().count(), 2)

    def test_calendars_dla_edycji_i_odbylo_sie_z_true_na_true(self):
        termin = TerminSzkoleniaFactory.create(
            odbylo_sie=True,
            sala=SalaFactory.create(calendar="1"),
            prowadzacy=ProwadzacyFactory.create(calendar="1"),
            lokalizacja=LokalizacjaFactory.create(calendar="1"),
        )
        termin.clean()
        termin.kalendarze_dane_flat = termin.get_kalendarze_dane_flat(as_json=True)
        termin.save()

        termin = TerminSzkolenia.objects.get(pk=termin.pk)

        _update_termin_calendars(termin)

        # Powinny zostac odane wpisy dla sali i prowadzacego.
        self.assertEqual(CalendarUpdate.objects.all().count(), 2)

    def test_calendars_dla_edycji_i_odbylo_sie_z_true_na_true_bez_ciaglosci_sal(self):
        termin = TerminSzkoleniaFactory.create(
            odbylo_sie=True,
            prowadzacy=ProwadzacyFactory.create(calendar="1"),
            lokalizacja=LokalizacjaFactory.create(calendar="1"),
        )
        termin.clean()
        termin.kalendarze_dane_flat = termin.get_kalendarze_dane_flat(as_json=True)
        termin.save()

        termin = TerminSzkolenia.objects.get(pk=termin.pk)

        _update_termin_calendars(termin)

        # Powinny zostac odane wpisy dla prowadzacego i lokalizacji.
        self.assertEqual(CalendarUpdate.objects.all().count(), 2)

    def test_calendars_dla_edycji_i_odbylo_sie_z_none_na_true(self):
        termin = TerminSzkoleniaFactory.create(
            odbylo_sie=None,
            sala=SalaFactory.create(calendar="1"),
            prowadzacy=ProwadzacyFactory.create(calendar="1"),
            lokalizacja=LokalizacjaFactory.create(calendar="1"),
        )
        termin.clean()
        termin.kalendarze_dane_flat = termin.get_kalendarze_dane_flat(as_json=True)
        termin.save()

        termin = TerminSzkolenia.objects.get(pk=termin.pk)

        termin.odbylo_sie = True
        termin.kalendarze_dane_flat = termin.get_kalendarze_dane_flat(as_json=True)
        termin.save()

        _update_termin_calendars(termin)

        # Powinny zostac usuniete wpisy z lokalizacji i dodane dla sal
        # i prowadzacych.
        self.assertEqual(CalendarUpdate.objects.all().count(), 3)

    def test_calendars_dla_edycji_i_odbylo_sie_z_none_na_true_bez_ciaglosci_sal(self):
        termin = TerminSzkoleniaFactory.create(
            odbylo_sie=None,
            sala=SalaFactory.create(calendar=""),
            prowadzacy=ProwadzacyFactory.create(calendar="1"),
            lokalizacja=LokalizacjaFactory.create(calendar="1"),
        )
        termin.clean()
        termin.kalendarze_dane_flat = termin.get_kalendarze_dane_flat(as_json=True)
        termin.save()

        termin = TerminSzkolenia.objects.get(pk=termin.pk)

        termin.odbylo_sie = True
        termin.kalendarze_dane_flat = termin.get_kalendarze_dane_flat(as_json=True)
        termin.save()

        _update_termin_calendars(termin)

        # Powinny zostac dodane wpisy dla prowadzacych i lokalizacji.
        self.assertEqual(CalendarUpdate.objects.all().count(), 2)

    def test_calendars_dla_edycji_i_odbylo_sie_z_none_na_true_ze_zmian_sali(self):
        termin = TerminSzkoleniaFactory.create(
            odbylo_sie=None,
            sala=SalaFactory.create(calendar="1"),
            prowadzacy=ProwadzacyFactory.create(calendar="1"),
            lokalizacja=LokalizacjaFactory.create(calendar="1"),
        )
        termin.clean()
        termin.kalendarze_dane_flat = termin.get_kalendarze_dane_flat(as_json=True)
        termin.save()

        termin = TerminSzkolenia.objects.get(pk=termin.pk)

        termin.sala = SalaFactory.create(calendar="1")
        termin.odbylo_sie = True
        termin.kalendarze_dane_flat = termin.get_kalendarze_dane_flat(as_json=True)
        termin.save()

        _update_termin_calendars(termin)

        # Powinny zostac dodane wpisy dla prowadzacych i sal, oraz usuniete
        # dla sal i lokalziacji.
        self.assertEqual(CalendarUpdate.objects.all().count(), 4)

    def test_calendars_dla_edycji_i_odbylo_sie_z_none_na_true_wiele_obiektow(self):
        termin = TerminSzkoleniaFactory.create(
            odbylo_sie=None,
            sala=SalaFactory.create(calendar="1"),
            prowadzacy=ProwadzacyFactory.create(calendar="1"),
            lokalizacja=LokalizacjaFactory.create(calendar="1"),
            szkolenie=SzkolenieFactory.create(czas_dni=2),
        )
        d1 = DzienSzkoleniaFactory(
            terminszkolenia=termin,
            sala=SalaFactory.create(calendar="1"),
            data=termin.termin,
            prowadzacy=ProwadzacyFactory.create(calendar="1"),
        )
        termin.clean()
        termin.kalendarze_dane_flat = termin.get_kalendarze_dane_flat(as_json=True)
        termin.save()

        termin = TerminSzkolenia.objects.get(pk=termin.pk)

        d1.sala = SalaFactory.create(calendar="1")
        d1.save()
        termin.odbylo_sie = True
        termin.kalendarze_dane_flat = termin.get_kalendarze_dane_flat(as_json=True)
        termin.save()

        _update_termin_calendars(termin)

        # Powinny zostac dodane wpisy dla prowadzacych(2) i sal(2), oraz
        # usuniete dla sal(1) i lokalziacji(1).
        self.assertEqual(CalendarUpdate.objects.all().count(), 6)
