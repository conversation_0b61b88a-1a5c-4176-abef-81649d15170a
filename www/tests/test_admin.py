import datetime
import io
import json
import unittest

from django.contrib.admin.options import TO_FIELD_VAR
from django.core import mail
from django.core.cache import cache
from django.core.files.uploadedfile import SimpleUploadedFile
from django.test.utils import override_settings
from django.urls import reverse
from mock import patch
from selenium.common.exceptions import NoSuchElementException, TimeoutException
from selenium.webdriver.support.ui import Select

from www.forms import CustomPLNIPField
from www.locking import cache_key, locked
from www.models import (
    FakturaWysylka,
    TerminSzkolenia,
    TerminSzkoleniaLog,
    TerminSzkoleniaMail,
    Testimonial,
    Uczestnik,
    UserNotification,
)
from www.testfactories import (
    AnkietaFactory,
    AssetFactory,
    AutoryzacjaFactory,
    FakturaKorektaFactory,
    FakturaWysylkaFactory,
    GraduateFactory,
    HighlightPlacementFactory,
    KomentarzFactory,
    LokalizacjaFactory,
    ProwadzacyFactory,
    SalaFactory,
    SzkolenieFactory,
    TagTechnologiaFactory,
    TerminSzkoleniaFactory,
    UczestnikFactory,
    UserCoursesNotificationFactory,
    UserFactory,
    UserNotificationFactory,
)
from www.testhelpers import ALXLiveServerTestCase, ALXTestCase

from .patches import APIZliczaczPatch
from .test_unit import FakturaKorektaTestCase


class AdminTestCase(ALXTestCase):
    def setUp(self):
        super().setUp()

        self.admin_user = UserFactory.create(
            first_name="Jan",
            last_name="Pawłowski",
            password="passwd",
            is_superuser=True,
            is_staff=True,
        )

        # Zaloguj użytkownika, aby można było korzystać z panelu adm.
        self.assertTrue(self.admin_user.check_password("passwd"))

        self.assertTrue(
            self.client.login(username=self.admin_user.username, password="passwd")
        )

    def tearDown(self):
        super().tearDown()
        self.client.logout()


class FakturaWysylkaAdminTestCase(AdminTestCase):
    def test_brak_wymaganych_danych(self):
        faktura = FakturaWysylkaFactory(recipient_name="")

        resp = self.client.get(
            reverse("admin:postivo", args=(faktura.id,)), follow=True
        )
        self.assertContains(resp, "Uzupełnij dane adresowe!")

    @patch("www.admin.api")
    def test_nie_mozna_pobrac_faktury_pdf(self, mock_api):
        mock_api.get_invoice_pdf.return_value = None, "Błąd"

        faktura = FakturaWysylkaFactory()

        resp = self.client.get(
            reverse("admin:postivo", args=(faktura.id,)), follow=True
        )
        self.assertContains(resp, "Wystąpił błąd podczas pobierania Faktury.")

    @patch("www.admin.api")
    @patch("www.admin.postivo")
    def test_nie_mozna_wyslac_faktury(self, mock_postivo, mock_api):
        mock_api.get_invoice_pdf.return_value = io.BytesIO(b"abc"), None
        mock_postivo.dispatch.return_value = None, "Błąd"

        faktura = FakturaWysylkaFactory()

        resp = self.client.get(
            reverse("admin:postivo", args=(faktura.id,)), follow=True
        )
        self.assertContains(resp, "Wystąpił błąd podczas wysyłki faktury.")

    @patch("www.admin.api")
    @patch("www.admin.postivo")
    def test_nie_mozna_wyslac_faktury_wyjatek(self, mock_postivo, mock_api):
        mock_api.get_invoice_pdf.return_value = io.BytesIO(b"abc"), None
        mock_postivo.dispatch.side_effect = Exception("boom!")

        faktura = FakturaWysylkaFactory()

        resp = self.client.get(
            reverse("admin:postivo", args=(faktura.id,)), follow=True
        )
        self.assertContains(resp, "Wystąpił błąd podczas wysyłki faktury.")

    @patch("www.admin.api")
    @patch("www.admin.postivo")
    def test_faktura_wyslana(self, mock_postivo, mock_api):
        mock_api.get_invoice_pdf.return_value = io.BytesIO(b"abc"), None
        mock_postivo.dispatch.return_value = {"id": "123", "status": "1"}, None

        faktura = FakturaWysylkaFactory()

        self.assertFalse(faktura.dispatched_at)
        self.assertFalse(faktura.status_date)
        self.assertFalse(faktura.status)
        self.assertFalse(faktura.dispatch_id)

        resp = self.client.get(
            reverse("admin:postivo", args=(faktura.id,)), follow=True
        )
        self.assertContains(resp, "Wysyłka faktury została poprawnie zlecona.")

        faktura = FakturaWysylka.objects.get(pk=faktura.pk)

        self.assertTrue(faktura.dispatched_at)
        self.assertFalse(faktura.status_date)
        self.assertEqual(faktura.status, "1")
        self.assertEqual(faktura.dispatch_id, "123")

    def test_wysylka_juz_zlecona(self):
        faktura = FakturaWysylkaFactory(dispatched_at=datetime.datetime.now())

        resp = self.client.get(reverse("admin:postivo", args=(faktura.id,)))
        self.assertEqual(resp.status_code, 404)

    def test_button_wyslania_jest_widoczny(self):
        faktura = FakturaWysylkaFactory()

        resp = self.client.get(
            reverse("admin:www_fakturawysylka_change", args=(faktura.id,)), follow=True
        )
        self.assertContains(resp, "Zleć wysyłkę")

    def test_button_wyslania_nie_jest_widoczny(self):
        faktura = FakturaWysylkaFactory(dispatched_at=datetime.datetime.now())

        resp = self.client.get(
            reverse("admin:www_fakturawysylka_change", args=(faktura.id,)), follow=True
        )
        self.assertNotContains(resp, "Zleć wysyłkę")


@override_settings(GENERATE_INVOICE_THROUGH_API_ZLICZACZ=False)
class UczestnikAdminTestCase(APIZliczaczPatch, ALXLiveServerTestCase):
    def setUp(self):
        super().setUp()
        self.user = self.admin_login()
        self.faktura_data_xpath = (
            "//th[contains(@class, 'column-faktura_po_szkoleniu_data')]//a"
        )

    def test_przelacz_pole_wyboru_terminu_wystawienia_faktury(self):
        uczestnik = UczestnikFactory.create()

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_uczestnik_change", args=(uczestnik.id,))
        )
        self.wait_for_element_by_xpath("//h1[text()='Zmień uczestnik']")
        element = self.wait_for_element_by_xpath(
            "//div[contains(@class, 'field-faktura_po_szkoleniu_data')]"
        )
        self.assertFalse(element.is_displayed())
        self.selenium.find_element_by_id("id_faktura_po_szkoleniu").click()
        element = self.wait_for_element_by_xpath(
            "//div[contains(@class, 'field-faktura_po_szkoleniu_data')]"
        )
        self.assertTrue(element.is_displayed())

    def test_pokaz_pole_wyboru_terminu_wystawienia_faktury(self):
        uczestnik = UczestnikFactory.create(faktura_po_szkoleniu=True)

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_uczestnik_change", args=(uczestnik.id,))
        )
        self.wait_for_element_by_xpath("//h1[text()='Zmień uczestnik']")
        element = self.wait_for_element_by_xpath(
            "//div[contains(@class, 'field-faktura_po_szkoleniu_data')]"
        )
        self.assertTrue(element.is_displayed())

    def test_uczestnik_index_bez_filtra_faktura_po_szkoleniu(self):
        url = reverse("admin:www_uczestnik_changelist")
        self.selenium.get(self.live_server_url + url)
        self.wait_for_element_by_xpath("//h1[@id='site-name']")
        try:
            self.selenium.find_element_by_xpath(self.faktura_data_xpath)
            msg = "Element {0} jest na stronie".format(self.faktura_data_xpath)
            self.fail(msg)
        except NoSuchElementException:
            pass

    def test_uczestnik_index_z_filtrem_faktura_po_szkoleniu(self):
        UczestnikFactory.create(faktura_po_szkoleniu=True)

        url = (
            reverse("admin:www_uczestnik_changelist") + "?faktura_po_szkoleniu__exact=1"
        )
        self.selenium.get(self.live_server_url + url)
        element = self.wait_for_element_by_xpath(self.faktura_data_xpath)
        self.assertTrue(element.is_displayed())

    def test_uczesnik_wlacznik_prywatny(self):
        """
        Sprawdza udostępnianie/blokowanie pól przez opcję "Jestem osobą prywatną".
        """
        path = {
            "ilosc_osob": '//input[@id="id_uczestnik_wieloosobowy_ilosc_osob"]',
            "f_firma": '//input[@id="id_faktura_firma"]',
            "f_adres": '//input[@id="id_faktura_adres"]',
            "f_miejscowosc_kod": '//input[@id="id_faktura_miejscowosc_kod"]',
            "f_vat_pl": '//input[@id="id_faktura_nip"]',
            "f_vat_en": '//input[@id="id_faktura_vat_id"]',
            "publiczny": '//input[@id="id_podmiot_publiczny"]',
        }
        uczestnik = UczestnikFactory(prywatny=True)
        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_uczestnik_change", args=(uczestnik.id,))
        )
        prywatny = self.wait_for_element_by_xpath('//input[@id="id_prywatny"]')
        self.assertTrue(prywatny.get_attribute("checked"))
        for key in path:
            element = self.selenium.find_element_by_xpath(path[key])
            self.assertFalse(element.is_enabled())
        prywatny.click()
        self.assertFalse(prywatny.get_attribute("checked"))
        for key in path:
            element = self.selenium.find_element_by_xpath(path[key])
            self.assertTrue(element.is_enabled())
        uczestnik = UczestnikFactory(prywatny=False)
        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_uczestnik_change", args=(uczestnik.id,))
        )
        prywatny = self.wait_for_element_by_xpath('//input[@id="id_prywatny"]')
        self.assertFalse(prywatny.get_attribute("checked"))
        for key in path:
            element = self.selenium.find_element_by_xpath(path[key])
            self.assertTrue(element.is_enabled())
        prywatny.click()
        self.assertTrue(prywatny.get_attribute("checked"))
        for key in path:
            element = self.selenium.find_element_by_xpath(path[key])
            self.assertFalse(element.is_enabled())

    def test_uczestnik_link_dotermin(self):
        """
        Sprawdza czy link do terminu prowadzi do właściwego terminu
        """
        termin = TerminSzkoleniaFactory()
        termin_id = termin.pk
        termin_data = "{0}".format(termin.termin)
        termin_lokalizacja = termin.lokalizacja.shortname
        szkolenie_nazwa = termin.szkolenie.nazwa
        szkolenie_kod = termin.szkolenie.kod
        uczestnik = UczestnikFactory(termin=termin)
        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_uczestnik_change", args=(uczestnik.id,))
        )
        link = self.wait_for_element_by_xpath('//a[@id="termin_link"]')
        link.click()
        self.wait_for_element_by_xpath('//form[@id="terminszkolenia_form"]')
        url = self.selenium.current_url
        self.assertTrue("terminszkolenia/{0}".format(termin_id) in url)

        szkolenie_selected = self.selenium.find_element_by_xpath(
            '//div[@class="form-row field-szkolenie"][1]'
        ).text
        self.assertTrue(szkolenie_kod in szkolenie_selected)
        self.assertTrue(szkolenie_nazwa in szkolenie_selected)

        input_termin = self.selenium.find_element_by_xpath('//input[@id="id_termin"]')
        termin_value = input_termin.get_attribute("value")
        self.assertEqual(termin_value, termin_data)
        lokalizacja_selected = self.selenium.find_element_by_xpath(
            '//select[@id="id_lokalizacja"]/option[@selected=""]'
        )
        lokalizacja_value = lokalizacja_selected.text
        self.assertEqual(lokalizacja_value, termin_lokalizacja)

    def test_uczestnik_egzaminu_czyproforma(self):
        """
        Uczestnik zapisany na szkolenie, które ma ustawione "wysylaj_powiadomienia_proformy" na False,
        ma wyłączone i nie "checked" pole "wystaw_proforme_automatycznie"
        """
        szkolenie = SzkolenieFactory(wysylaj_powiadomienia_proformy=False)
        termin = TerminSzkoleniaFactory(szkolenie=szkolenie)
        uczestnik = UczestnikFactory(termin=termin)
        uczestnik_id = uczestnik.id

        uczestnik_utworzony = Uczestnik.objects.get(id=uczestnik_id)
        self.assertEqual(uczestnik_utworzony.wystaw_proforme_automatycznie, False)

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_uczestnik_change", args=(uczestnik_id,))
        )
        wystaw_proforme = self.wait_for_element_by_xpath(
            '//input[@id="id_wystaw_proforme_automatycznie"]'
        )
        czy_disabled = wystaw_proforme.get_attribute("disabled")
        czy_checked = wystaw_proforme.get_attribute("checked")
        self.assertEqual(czy_disabled, "true")
        self.assertEqual(czy_checked, None)

    def test_uczestnik_standardowego_szkolenia_czyproforma(self):
        """
        Uczestnik zapisany na szkolenie, które ma ustawione "wysylaj_powiadomienia_proformy" na True,
        ma włączone  i "checked" pole "wystaw_proforme_automatycznie"
        """
        szkolenie = SzkolenieFactory(wysylaj_powiadomienia_proformy=True)
        termin = TerminSzkoleniaFactory(szkolenie=szkolenie)
        uczestnik = UczestnikFactory(termin=termin)
        uczestnik_id = uczestnik.id

        uczestnik_utworzony = Uczestnik.objects.get(id=uczestnik_id)
        self.assertEqual(uczestnik_utworzony.wystaw_proforme_automatycznie, True)

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_uczestnik_change", args=(uczestnik_id,))
        )
        wystaw_proforme = self.wait_for_element_by_xpath(
            '//input[@id="id_wystaw_proforme_automatycznie"]'
        )
        czy_disabled = wystaw_proforme.get_attribute("disabled")
        czy_checked = wystaw_proforme.get_attribute("checked")
        self.assertEqual(czy_disabled, None)
        self.assertEqual(czy_checked, "true")

    def test_daty_szczegolowo(self):
        # Podczas edycji Uczestnika powinny pokazać się daty szkolenia
        termin = TerminSzkoleniaFactory.create(
            daty_szczegolowo="2015-01-01,2015-01-02,2015-01-03"
        )

        uczestnik = UczestnikFactory.create(termin=termin)

        self.client.login(username=self.user.username, password="test.pw")
        response = self.client.get(
            reverse("admin:www_uczestnik_change", args=(uczestnik.pk,))
        )

        self.assertIn(
            "2015-01-01, 2015-01-02, 2015-01-03", response.content.decode("utf-8")
        )

        # Podczas dodawania - brak dat
        response = self.client.get(reverse("admin:www_uczestnik_add"))

        self.assertIn("(Brak)", response.content.decode("utf-8"))

    def test_autoryzacja(self):
        # Sprawdzamy pojawienie się help-textow przy wyborze szkolen z
        # autoryzacja i bez.

        t1 = TerminSzkoleniaFactory.create(
            szkolenie=SzkolenieFactory.create(autoryzacja=AutoryzacjaFactory.create()),
            autoryzacja_aktywna=True,
        )
        t2 = TerminSzkoleniaFactory.create()

        self.selenium.get(self.live_server_url + reverse("admin:www_uczestnik_add"))

        # Wybieramy szkolenie bez autoryzacji
        self.selenium.find_element_by_xpath(
            '//select[@id="id_termin"]/optgroup[@label="{0}"]/option[@value="{1}"]'.format(
                str(t2.szkolenie), t2.pk
            )
        ).click()

        el = self.wait_for_element_by_xpath('//p[@class="help help-autoryzacja"]')
        self.assertEqual(el.text, "UWAGA: To szkolenie nie ma opcji autoryzacji.")

        # Wybieramu szkolenie z autoryzacja (help-tekst znika)
        self.selenium.find_element_by_xpath(
            '//select[@id="id_termin"]/optgroup[@label="{0}"]/option[@value="{1}"]'.format(
                str(t1.szkolenie), t1.pk
            )
        ).click()

        try:
            self.selenium.find_element_by_xpath('//p[@class="help help-autoryzacja"]')
        except NoSuchElementException:
            # Jest ok, ma nie być tego elementu
            pass
        else:
            self.fail("Błędna walidacja.")

    @override_settings(GENERATE_INVOICE_THROUGH_API_ZLICZACZ=True)
    @patch("www.forms.update_invoice_status")
    def test_zmiana_statusu_faktury(self, mock_api):
        mock_api.return_value = True, None

        uczestnik = UczestnikFactory(
            adres="adres",
            miejscowosc_kod="77-798 Miejscowosc",
            prywatny=True,
            wystaw_proforme_automatycznie=True,
            za_kurs_zaplace=5,
            imie_nazwisko_zostalo_sprawdzone=True,
        )

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_uczestnik_change", args=(uczestnik.pk,))
        )

        self.selenium.find_element_by_xpath(
            '//select[@id="id_faktura_status"]/option[@value="ready"]'
        ).click()

        # Zapisz obiekt
        self.selenium.find_element_by_xpath(
            '//input[@type="submit" and @name="_save"]'
        ).click()

        error = self.wait_for_element_by_xpath(
            '//div[@class="form-row errors field-faktura_status"][1]'
            '/ul[@class="errorlist"]'
            "/li[1]"
        ).text
        self.assertEqual(
            error,
            "Nie możesz wybrać statusu, gdyż faktura nie "
            "została wygenerowana w Zliczacz.",
        )
        self.assertEqual(mock_api.call_count, 0)

        # Dodajemy fakturę
        uczestnik.zliczacz_faktura_no = "123"
        uczestnik.save()

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_uczestnik_change", args=(uczestnik.pk,))
        )
        self.selenium.find_element_by_xpath(
            '//select[@id="id_faktura_status"]/option[@value="ready"]'
        ).click()

        # Zapisz obiekt
        self.selenium.find_element_by_xpath(
            '//input[@type="submit" and @name="_save"]'
        ).click()

        self.assertEqual(mock_api.call_count, 1)
        mock_api.call_count = 0

        # Probujemy ustawic opcje "opłacona"

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_uczestnik_change", args=(uczestnik.pk,))
        )
        self.selenium.find_element_by_xpath(
            '//select[@id="id_faktura_status"]/option[@value="paid"]'
        ).click()

        # Zapisz obiekt
        self.selenium.find_element_by_xpath(
            '//input[@type="submit" and @name="_save"]'
        ).click()

        error = self.wait_for_element_by_xpath(
            '//div[@class="form-row errors field-faktura_status"][1]'
            '/ul[@class="errorlist"]'
            "/li[1]"
        ).text
        self.assertEqual(
            error,
            "Nie możesz wybrać tego statusu, gdyż nie "
            "została wprowadzona data opłacenia "
            "(pole 'Zapłacone').",
        )
        self.assertEqual(mock_api.call_count, 0)

        # Probujemy ustawic opcje "zaliczka opłacona"

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_uczestnik_change", args=(uczestnik.pk,))
        )
        self.selenium.find_element_by_xpath(
            '//select[@id="id_faktura_status"]/option[@value="zaliczka_paid"]'
        ).click()

        # Zapisz obiekt
        self.selenium.find_element_by_xpath(
            '//input[@type="submit" and @name="_save"]'
        ).click()

        error = self.wait_for_element_by_xpath(
            '//div[@class="form-row errors field-faktura_status"][1]'
            '/ul[@class="errorlist"]'
            "/li[1]"
        ).text
        self.assertEqual(
            error,
            "Nie możesz wybrać tego statusu, gdyż nie "
            "została wprowadzona data opłacenia "
            "(pole 'Zaliczka zapłacone').",
        )
        self.assertEqual(mock_api.call_count, 0)

        # faktura opłacona

        uczestnik.zaplacone = datetime.date.today()
        uczestnik.save()

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_uczestnik_change", args=(uczestnik.pk,))
        )
        self.selenium.find_element_by_xpath(
            '//select[@id="id_faktura_status"]/option[@value="paid"]'
        ).click()

        # Zapisz obiekt
        self.selenium.find_element_by_xpath(
            '//input[@type="submit" and @name="_save"]'
        ).click()

        self.assertEqual(mock_api.call_count, 1)
        mock_api.call_count = 0

        # Probujemy zmienić status zapłaconej

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_uczestnik_change", args=(uczestnik.pk,))
        )
        self.selenium.find_element_by_xpath(
            '//select[@id="id_faktura_status"]/option[@value="ready"]'
        ).click()

        # Zapisz obiekt
        self.selenium.find_element_by_xpath(
            '//input[@type="submit" and @name="_save"]'
        ).click()

        error = self.wait_for_element_by_xpath(
            '//div[@class="form-row errors field-faktura_status"][1]'
            '/ul[@class="errorlist"]'
            "/li[1]"
        ).text
        self.assertEqual(
            error,
            "Gdy faktura jest opłacona, nie można wybrać "
            "innego statusu jak 'zapłacona'.",
        )
        self.assertEqual(mock_api.call_count, 0)

        # Zapisujemy obiket bez zmiany statusu
        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_uczestnik_change", args=(uczestnik.pk,))
        )
        self.selenium.find_element_by_id("id_osoba_do_kontaktu").send_keys("aaa")

        # Zapisz obiekt
        self.selenium.find_element_by_xpath(
            '//input[@type="submit" and @name="_save"]'
        ).click()

        self.assertEqual(mock_api.call_count, 0)

    def test_umowa_ratalna(self):
        # Sprawdzamy pojawienie się linkow do generowania umowy ratalnej.
        self.admin_user = UserFactory.create(
            first_name="Jan",
            last_name="Pawłowski",
            password="pass",
            is_superuser=True,
            is_staff=True,
        )

        # Zaloguj użytkownika, aby można było korzystać z panelu adm.
        self.assertTrue(
            self.client.login(username=self.admin_user.username, password="pass")
        )

        uczestnik = UczestnikFactory()

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_uczestnik_change", args=(uczestnik.pk,))
        )

        try:
            self.selenium.find_element_by_id("umowa_ratalna_docx")
        except NoSuchElementException:
            # Jest ok, ma nie być tego elementu
            pass
        else:
            self.fail("Błędna walidacja.")

        try:
            self.selenium.find_element_by_id("umowa_ratalna_pdf")
        except NoSuchElementException:
            # Jest ok, ma nie być tego elementu
            pass
        else:
            self.fail("Błędna walidacja.")

        # Dodajemy raty
        uczestnik.raty_panstwo = "polska"
        uczestnik.prywatny = True
        uczestnik.za_kurs_zaplace = 3
        uczestnik.save()

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_uczestnik_change", args=(uczestnik.pk,))
        )

        # Pobieramy plik (docx)
        link = self.selenium.find_element_by_id("umowa_ratalna_docx")
        response = self.client.get(link.get_attribute("href"))
        self.assertEqual(response.status_code, 200)

        # Pobieramy plik (pdf)
        link = self.selenium.find_element_by_id("umowa_ratalna_pdf")
        response = self.client.get(link.get_attribute("href"))
        self.assertEqual(response.status_code, 200)

    @override_settings(GENERATE_INVOICE_THROUGH_API_ZLICZACZ=True)
    @patch("www.admin.utils")
    def test_uczestnik_uczestnik_pro_forma(self, mock_utils):
        mock_utils.generate_invoice.return_value = True, None

        uczestnik = UczestnikFactory(
            adres="adres",
            miejscowosc_kod="77-798",
            prywatny=True,
            wystaw_proforme_automatycznie=True,
            za_kurs_zaplace=5,
            imie_nazwisko_zostalo_sprawdzone=True,
        )

        self.selenium.get(
            self.live_server_url
            + reverse("admin:uczestnik_uczestnik_pro_forma", args=(uczestnik.pk,))
        )

        page_text = self.selenium.find_element_by_tag_name("body").text
        self.assertIn("Faktura została poprawnie wygenerowana.", page_text)

    @override_settings(GENERATE_INVOICE_THROUGH_API_ZLICZACZ=True)
    @patch("www.admin.utils")
    def test_uczestnik_uczestnik_fvat(self, mock_utils):
        mock_utils.generate_invoice.return_value = True, None

        uczestnik = UczestnikFactory(
            adres="adres",
            miejscowosc_kod="77-798 Miejscowosc",
            prywatny=True,
            wystaw_proforme_automatycznie=True,
            za_kurs_zaplace=5,
            imie_nazwisko_zostalo_sprawdzone=True,
            zaplacone=datetime.date.today(),
        )

        self.selenium.get(
            self.live_server_url
            + reverse("admin:uczestnik_uczestnik_fvat", args=(uczestnik.pk,))
        )

        page_text = self.selenium.find_element_by_tag_name("body").text
        self.assertIn("Faktura została poprawnie wygenerowana.", page_text)

    @override_settings(GENERATE_INVOICE_THROUGH_API_ZLICZACZ=True)
    @patch("www.admin.utils")
    def test_uczestnik_uczestnik_fvat_raty(self, mock_utils):
        mock_utils.generate_invoice.return_value = True, None

        uczestnik = UczestnikFactory(
            adres="adres",
            miejscowosc_kod="77-798 Miejscowosc",
            prywatny=True,
            wystaw_proforme_automatycznie=True,
            za_kurs_zaplace=3,
            imie_nazwisko_zostalo_sprawdzone=True,
            zaplacone=datetime.date.today(),
        )

        self.selenium.get(
            self.live_server_url
            + reverse("admin:uczestnik_uczestnik_fvat_raty", args=(uczestnik.pk,))
        )

        page_text = self.selenium.find_element_by_tag_name("body").text
        self.assertIn("Faktura została poprawnie wygenerowana.", page_text)

    @override_settings(GENERATE_INVOICE_THROUGH_API_ZLICZACZ=True)
    @patch("www.admin.utils")
    def test_uczestnik_uczestnik_pro_forma_with_errors(self, mock_utils):
        mock_utils.generate_invoice.return_value = False, "Błąd"

        uczestnik = UczestnikFactory(
            adres="adres",
            miejscowosc_kod="77-798 Warszawa",
            prywatny=True,
            wystaw_proforme_automatycznie=True,
            za_kurs_zaplace=5,
            imie_nazwisko_zostalo_sprawdzone=True,
        )

        self.selenium.get(
            self.live_server_url
            + reverse("admin:uczestnik_uczestnik_pro_forma", args=(uczestnik.pk,))
        )

        page_text = self.selenium.find_element_by_tag_name("body").text
        self.assertIn(
            "Wystąpił błąd podczas generowania faktury. " "Treść błędu: Błąd", page_text
        )

    def test_button_wyslij_fakture_korygujaca(self):
        uczestnik = UczestnikFactory(
            adres="adres",
            miejscowosc_kod="77-798 Warszawa",
            prywatny=True,
            wystaw_proforme_automatycznie=True,
            za_kurs_zaplace=5,
            imie_nazwisko_zostalo_sprawdzone=True,
        )
        FakturaKorektaFactory.create(uczestnik=uczestnik)

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_uczestnik_change", args=(uczestnik.pk,))
        )

        self.selenium.find_element_by_xpath('//a[@class="run-modal"][1]')

        uczestnik.zliczacz_faktura_no = "12345"
        uczestnik.save()

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_uczestnik_change", args=(uczestnik.pk,))
        )

        # Powinny być dwa buttony (korekta i VAT)
        self.selenium.find_element_by_link_text("Wyślij: Faktura korygująca".upper())
        self.selenium.find_element_by_link_text(
            "Wyślij: {0}".format(uczestnik.nazwy_faktur()["vat"]).upper()
        )

        # Żadnego buttona
        uczestnik.korekta_wyslana = datetime.datetime.now()

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_uczestnik_change", args=(uczestnik.pk,))
        )

        try:
            self.selenium.find_element_by_xpath('//a[@class="run-modal"][1]')
        except NoSuchElementException:
            # Jest ok, ma nie być tego elementu
            pass

    def test_button_wyslij_fakture_proforma(self):
        uczestnik = UczestnikFactory(
            adres="adres",
            miejscowosc_kod="77-798 Warszawa",
            prywatny=True,
            wystaw_proforme_automatycznie=True,
            za_kurs_zaplace=5,
            imie_nazwisko_zostalo_sprawdzone=True,
            zliczacz_proforma_no="12345",
        )

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_uczestnik_change", args=(uczestnik.pk,))
        )

        self.selenium.find_element_by_xpath('//a[@class="run-modal"][1]')

        uczestnik.zliczacz_proforma_no = ""
        uczestnik.save()

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_uczestnik_change", args=(uczestnik.pk,))
        )

        try:
            self.selenium.find_element_by_xpath('//a[@class="run-modal"][1]')
        except NoSuchElementException:
            # Jest ok, ma nie być tego elementu
            pass

    def test_button_wyslij_fakture_vat(self):
        uczestnik = UczestnikFactory(
            adres="adres",
            miejscowosc_kod="77-798 Warszawa",
            prywatny=True,
            wystaw_proforme_automatycznie=True,
            za_kurs_zaplace=5,
            imie_nazwisko_zostalo_sprawdzone=True,
            zliczacz_faktura_no="12345",
        )

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_uczestnik_change", args=(uczestnik.pk,))
        )

        self.selenium.find_element_by_xpath('//a[@class="run-modal"][1]')

        uczestnik.zliczacz_faktura_no = ""
        uczestnik.save()

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_uczestnik_change", args=(uczestnik.pk,))
        )

        try:
            self.selenium.find_element_by_xpath('//a[@class="run-modal"][1]')
        except NoSuchElementException:
            # Jest ok, ma nie być tego elementu
            pass

    def test_button_wyslij_fakture_vat_raty(self):
        uczestnik = UczestnikFactory(
            adres="adres",
            miejscowosc_kod="77-798 Warszawa",
            prywatny=True,
            wystaw_proforme_automatycznie=True,
            za_kurs_zaplace=3,
            imie_nazwisko_zostalo_sprawdzone=True,
            zliczacz_faktura_raty_no="12345",
        )

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_uczestnik_change", args=(uczestnik.pk,))
        )

        self.selenium.find_element_by_xpath('//a[@class="run-modal"][1]')

        uczestnik.zliczacz_faktura_raty_no = ""
        uczestnik.save()

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_uczestnik_change", args=(uczestnik.pk,))
        )

        try:
            self.selenium.find_element_by_xpath('//a[@class="run-modal"][1]')
        except NoSuchElementException:
            # Jest ok, ma nie być tego elementu
            pass

    def test_button_wyslij_fakture_vat_raty_platnosc_typ_2(self):
        uczestnik = UczestnikFactory(
            adres="adres",
            miejscowosc_kod="77-798 Warszawa",
            prywatny=True,
            wystaw_proforme_automatycznie=True,
            za_kurs_zaplace=2,
            imie_nazwisko_zostalo_sprawdzone=True,
            zliczacz_faktura_raty_no="12345",
        )

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_uczestnik_change", args=(uczestnik.pk,))
        )

        self.selenium.find_element_by_xpath('//a[@class="run-modal"][1]')

        uczestnik.zliczacz_faktura_raty_no = ""
        uczestnik.save()

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_uczestnik_change", args=(uczestnik.pk,))
        )

        try:
            self.selenium.find_element_by_xpath('//a[@class="run-modal"][1]')
        except NoSuchElementException:
            # Jest ok, ma nie być tego elementu
            pass

    def test_button_wyslij_umowe(self):
        uczestnik = UczestnikFactory(
            prywatny=True, za_kurs_zaplace=10, raty_panstwo="polska"
        )

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_uczestnik_change", args=(uczestnik.pk,))
        )

        self.selenium.find_element_by_xpath('//a[@class="run-modal"][1]')

        uczestnik.prywatny = False
        uczestnik.save()

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_uczestnik_change", args=(uczestnik.pk,))
        )

        try:
            self.selenium.find_element_by_xpath('//a[@class="run-modal"][1]')
        except NoSuchElementException:
            # Jest ok, ma nie być tego elementu
            pass

    def test_wyslij_umowe_walidacja(self):
        uczestnik = UczestnikFactory(
            prywatny=True, za_kurs_zaplace=10, raty_panstwo="polska"
        )

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_uczestnik_change", args=(uczestnik.pk,))
        )

        self.selenium.find_element_by_xpath('//a[@class="run-modal"][1]').click()

        self.wait_for_element_by_id("subscription-container")

        self.selenium.find_element_by_id("id_subject").clear()
        self.selenium.find_element_by_id("id_content").clear()
        self.selenium.find_element_by_id("id_to_emails").send_keys("test1@")
        self.selenium.find_element_by_id("id_cc_emails").send_keys(
            "<EMAIL>;<EMAIL>"
        )
        self.selenium.find_element_by_id("subscription-ajax-form-submit").click()

        # Treść maila
        error = self.wait_for_element_by_xpath(
            '(//ul[@class="errorlist"])[1]' "/li[1]"
        ).text
        self.assertEqual(error, "To pole jest wymagane.")

        # Temat maila
        error = self.wait_for_element_by_xpath(
            '(//ul[@class="errorlist"])[2]' "/li[1]"
        ).text
        self.assertEqual(error, "To pole jest wymagane.")

        # Dodatkowe adresy
        error = self.wait_for_element_by_xpath(
            '(//ul[@class="errorlist"])[3]' "/li[1]"
        ).text
        self.assertEqual(error, "'test1@' jest błędnym adresem email.")

        # Dodatkowe adresy CC
        error = self.wait_for_element_by_xpath(
            '(//ul[@class="errorlist"])[4]' "/li[1]"
        ).text
        self.assertEqual(
            error, "'<EMAIL>;<EMAIL>' " "jest błędnym adresem email."
        )

        self.assertEqual(len(mail.outbox), 0)

    def test_wyslij_umowe(self):
        uczestnik = UczestnikFactory(
            prywatny=True, za_kurs_zaplace=10, raty_panstwo="polska"
        )
        self.assertFalse(uczestnik.umowa_wyslana)

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_uczestnik_change", args=(uczestnik.pk,))
        )

        self.selenium.find_element_by_xpath('//a[@class="run-modal"][1]').click()

        self.wait_for_element_by_id("subscription-container")

        self.wait_for_element_by_xpath(
            '(//div[@class="header-bg"][1])/h2[1][contains(text(), '
            '"Wysyłasz umowę do {0}")]'.format(uczestnik.email)
        )

        self.selenium.find_element_by_id("id_to_emails").send_keys("<EMAIL>")
        self.selenium.find_element_by_id("id_cc_emails").send_keys(
            "<EMAIL>, <EMAIL>"
        )
        self.selenium.find_element_by_id("subscription-ajax-form-submit").click()

        self.wait_for_element_by_xpath(
            '(//div[@id="invoice-send-status"][1])/p[1][contains(text(), '
            '"Umowa została wysłana.")]'
        )

        # Sprawdzamy, czy zapisał się log i wysłał email
        uczestnik = Uczestnik.objects.get(pk=uczestnik.pk)
        self.assertTrue(uczestnik.umowa_wyslana)

        self.assertEqual(len(mail.outbox), 1)

        self.assertEqual(
            mail.outbox[0].subject,
            "{0} - umowa płatności ratalnej".format(
                uczestnik.termin.nazwa_szkolenia_lub_snz()
            ),
        )
        self.assertIn("Oryginał proszę przekazać", mail.outbox[0].body)
        self.assertEqual(len(mail.outbox[0].attachments), 1)

        self.assertCountEqual(mail.outbox[0].to, [uczestnik.email, "<EMAIL>"])
        self.assertCountEqual(mail.outbox[0].cc, ["<EMAIL>", "<EMAIL>"])

    @override_settings(
        task_eager_propagates=True,
        task_always_eager=True,
        EMAIL_BACKEND="django.core.mail.backends.locmem.EmailBackend",
    )
    def test_wyslij_mail_o_potwierdzeniu_terminu(self):
        uczestnik = UczestnikFactory(status=1)
        self.assertIsNone(uczestnik.mail_o_potwierdzeniu_terminu)

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_uczestnik_change", args=(uczestnik.pk,))
        )

        self.selenium.execute_script("window.confirm = function(msg) { return true; }")

        self.selenium.find_element_by_xpath(
            '//a[@id="wyslij_mail_o_potwierdzeniu_terminu"]'
        ).click()

        page_text = self.wait_for_element_by_xpath("//body").text
        self.assertIn(
            "Wysyłka maila została zlecona i oczekuje na wysłanie.", page_text
        )

        # Sprawdzamy, czy zapisał się log i wysłał email
        uczestnik = Uczestnik.objects.get(pk=uczestnik.pk)
        self.assertTrue(uczestnik.mail_o_potwierdzeniu_terminu)

        self.assertEqual(len(mail.outbox), 2)

    @override_settings(GENERATE_INVOICE_THROUGH_API_ZLICZACZ=True)
    @patch("requests.get")
    def test_wyslij_fakture_walidacja(self, mock_get):
        mock_get.side_effect = self.mock_requests_get

        uczestnik = UczestnikFactory(
            adres="adres",
            miejscowosc_kod="77-798 Warszawa",
            prywatny=True,
            wystaw_proforme_automatycznie=True,
            za_kurs_zaplace=5,
            imie_nazwisko_zostalo_sprawdzone=True,
            zliczacz_faktura_no="12345",
            termin__termin=datetime.date(2015, 1, 1),
        )

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_uczestnik_change", args=(uczestnik.pk,))
        )

        self.selenium.find_element_by_xpath('//a[@class="run-modal"][1]').click()

        self.wait_for_element_by_id("subscription-container")

        self.selenium.find_element_by_id("id_subject").clear()
        self.selenium.find_element_by_id("id_content").clear()
        self.selenium.find_element_by_id("id_to_emails").send_keys("test1@")
        self.selenium.find_element_by_id("id_cc_emails").send_keys(
            "<EMAIL>;<EMAIL>"
        )
        self.selenium.find_element_by_id("subscription-ajax-form-submit").click()

        # Treść maila
        error = self.wait_for_element_by_xpath(
            '(//ul[@class="errorlist"])[1]' "/li[1]"
        ).text
        self.assertEqual(error, "To pole jest wymagane.")

        # Temat maila
        error = self.wait_for_element_by_xpath(
            '(//ul[@class="errorlist"])[2]' "/li[1]"
        ).text
        self.assertEqual(error, "To pole jest wymagane.")

        # Dodatkowe adresy
        error = self.wait_for_element_by_xpath(
            '(//ul[@class="errorlist"])[3]' "/li[1]"
        ).text
        self.assertEqual(error, "'test1@' jest błędnym adresem email.")

        # Dodatkowe adresy CC
        error = self.wait_for_element_by_xpath(
            '(//ul[@class="errorlist"])[4]' "/li[1]"
        ).text
        self.assertEqual(
            error, "'<EMAIL>;<EMAIL>' " "jest błędnym adresem email."
        )

        self.assertEqual(len(mail.outbox), 0)

    @override_settings(GENERATE_INVOICE_THROUGH_API_ZLICZACZ=True)
    @patch("requests.get")
    @patch("www.admin.api")
    def test_wyslij_fakture_vat(self, mock_api, mock_get):
        mock_get.side_effect = self.mock_requests_get
        mock_api.update_invoice_status.return_value = True, None

        uczestnik = UczestnikFactory(
            adres="adres",
            miejscowosc_kod="77-798 Warszawa",
            prywatny=True,
            wystaw_proforme_automatycznie=True,
            za_kurs_zaplace=5,
            imie_nazwisko_zostalo_sprawdzone=True,
            zliczacz_faktura_no="12345",
            termin__termin=datetime.date(2015, 1, 1),
        )
        self.assertFalse(uczestnik.faktura_wyslana)

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_uczestnik_change", args=(uczestnik.pk,))
        )

        self.selenium.find_element_by_xpath('//a[@class="run-modal"][1]').click()

        self.wait_for_element_by_id("subscription-container")

        self.wait_for_element_by_xpath(
            '(//div[@class="header-bg"][1])/h2[1][contains(text(), '
            '"Wysyłasz fakturę VAT do {0}")]'.format(uczestnik.email)
        )

        self.selenium.find_element_by_id("id_to_emails").send_keys("<EMAIL>")
        self.selenium.find_element_by_id("id_cc_emails").send_keys(
            "<EMAIL>, <EMAIL>"
        )
        self.selenium.find_element_by_id("subscription-ajax-form-submit").click()

        self.wait_for_element_by_xpath(
            '(//div[@id="invoice-send-status"][1])/p[1][contains(text(), '
            '"Faktura VAT została wysłana.")]'
        )

        # Powinna uruchomić się funckja zmiany statusu faktury.
        self.assertEqual(mock_api.update_invoice_status.call_count, 1)

        # Sprawdzamy, czy zapisał się log i zmienił status faktury
        uczestnik = Uczestnik.objects.get(pk=uczestnik.pk)
        self.assertTrue(uczestnik.faktura_wyslana)
        self.assertTrue(uczestnik.faktura_status, "sent")

        self.assertEqual(len(mail.outbox), 1)
        title = "szkolenie {0} od 2015-01-01".format(
            uczestnik.termin.nazwa_szkolenia_lub_snz()
        )

        self.assertEqual(
            mail.outbox[0].subject, "Faktura VAT - ALX - {0}".format(title)
        )
        self.assertIn(title, mail.outbox[0].body)
        self.assertIn("Jan Pawłowski", mail.outbox[0].body)
        self.assertEqual(len(mail.outbox[0].attachments), 1)

        self.assertCountEqual(mail.outbox[0].to, [uczestnik.email, "<EMAIL>"])
        self.assertCountEqual(mail.outbox[0].cc, ["<EMAIL>", "<EMAIL>"])

    @override_settings(GENERATE_INVOICE_THROUGH_API_ZLICZACZ=True)
    @patch("requests.get")
    def test_wyslij_fakture_vat_raty(self, mock_get):
        mock_get.side_effect = self.mock_requests_get

        uczestnik = UczestnikFactory(
            adres="adres",
            miejscowosc_kod="77-798 Warszawa",
            prywatny=True,
            wystaw_proforme_automatycznie=True,
            za_kurs_zaplace=3,
            imie_nazwisko_zostalo_sprawdzone=True,
            zliczacz_faktura_raty_no="12345",
            termin__termin=datetime.date(2015, 1, 1),
        )
        self.assertFalse(uczestnik.faktura_raty_wyslana)

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_uczestnik_change", args=(uczestnik.pk,))
        )

        self.selenium.find_element_by_xpath('//a[@class="run-modal"][1]').click()

        self.wait_for_element_by_id("subscription-container")

        self.wait_for_element_by_xpath(
            '(//div[@class="header-bg"][1])/h2[1][contains(text(), '
            '"Wysyłasz fakturę VAT (raty) do {0}")]'.format(uczestnik.email)
        )

        self.selenium.find_element_by_id("id_to_emails").send_keys("<EMAIL>")
        self.selenium.find_element_by_id("id_cc_emails").send_keys(
            "<EMAIL>, <EMAIL>"
        )
        self.selenium.find_element_by_id("subscription-ajax-form-submit").click()

        self.wait_for_element_by_xpath(
            '(//div[@id="invoice-send-status"][1])/p[1][contains(text(), '
            '"Faktura VAT (raty) została wysłana.")]'
        )

        # Sprawdzamy, czy zapisał się log i zmienił status faktury
        uczestnik = Uczestnik.objects.get(pk=uczestnik.pk)
        self.assertTrue(uczestnik.faktura_raty_wyslana)

        self.assertEqual(len(mail.outbox), 1)
        title = "szkolenie {0} od 2015-01-01".format(
            uczestnik.termin.nazwa_szkolenia_lub_snz()
        )

        self.assertEqual(
            mail.outbox[0].subject, "Faktura VAT - ALX - {0}".format(title)
        )
        self.assertIn(title, mail.outbox[0].body)
        self.assertIn("Jan Pawłowski", mail.outbox[0].body)
        self.assertEqual(len(mail.outbox[0].attachments), 1)

        self.assertCountEqual(mail.outbox[0].to, [uczestnik.email, "<EMAIL>"])
        self.assertCountEqual(mail.outbox[0].cc, ["<EMAIL>", "<EMAIL>"])

    @override_settings(GENERATE_INVOICE_THROUGH_API_ZLICZACZ=True)
    @patch("requests.get")
    @patch("www.admin.api")
    def test_wyslij_fakture_proforma(self, mock_api, mock_get):
        mock_get.side_effect = self.mock_requests_get

        uczestnik = UczestnikFactory(
            adres="adres",
            miejscowosc_kod="77-798 Warszawa",
            prywatny=True,
            wystaw_proforme_automatycznie=True,
            za_kurs_zaplace=5,
            imie_nazwisko_zostalo_sprawdzone=True,
            zliczacz_proforma_no="12345",
            termin__termin=datetime.date(2015, 1, 1),
        )
        self.assertFalse(uczestnik.proforma_wyslana)

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_uczestnik_change", args=(uczestnik.pk,))
        )

        self.selenium.find_element_by_xpath('//a[@class="run-modal"][1]').click()

        self.wait_for_element_by_id("subscription-container")

        self.wait_for_element_by_xpath(
            '(//div[@class="header-bg"][1])/h2[1][contains(text(), '
            '"Wysyłasz fakturę pro forma do {0}")]'.format(uczestnik.email)
        )
        self.selenium.find_element_by_id("subscription-ajax-form-submit").click()

        self.wait_for_element_by_xpath(
            '(//div[@id="invoice-send-status"][1])/p[1][contains(text(), '
            '"Faktura pro forma została wysłana.")]'
        )

        # Dla pro-forma nie zmieniamy statusu w zliczacz.
        self.assertEqual(mock_api.update_invoice_status.call_count, 0)

        # Sprawdzamy, czy zapisał się log
        uczestnik = Uczestnik.objects.get(pk=uczestnik.pk)
        self.assertTrue(uczestnik.proforma_wyslana)

        self.assertEqual(len(mail.outbox), 1)
        title = "szkolenie {0} od 2015-01-01".format(
            uczestnik.termin.nazwa_szkolenia_lub_snz()
        )

        self.assertEqual(
            mail.outbox[0].subject, "Faktura pro forma - ALX - {0}".format(title)
        )
        self.assertIn(title, mail.outbox[0].body)
        self.assertIn("Jan Pawłowski", mail.outbox[0].body)
        self.assertEqual(len(mail.outbox[0].attachments), 1)

        self.assertEqual(mail.outbox[0].to, [uczestnik.email])
        self.assertEqual(mail.outbox[0].cc, [])

    @patch("www.admin.generate_invoice_note_pdf")
    def test_wyslij_fakture_korygujaca(self, mock_pdf):
        mock_pdf.return_value = io.BytesIO(b"test"), "filename"

        uczestnik = UczestnikFactory(
            adres="adres",
            miejscowosc_kod="77-798 Warszawa",
            prywatny=True,
            wystaw_proforme_automatycznie=True,
            za_kurs_zaplace=5,
            imie_nazwisko_zostalo_sprawdzone=True,
            zliczacz_faktura_no="12345",
            faktura_wyslana=datetime.datetime.now(),
            termin__termin=datetime.date(2015, 1, 1),
        )
        FakturaKorektaFactory.create(uczestnik=uczestnik, cena_bilans=-1)
        self.assertFalse(uczestnik.korekta_wyslana)

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_uczestnik_change", args=(uczestnik.pk,))
        )

        self.selenium.find_element_by_xpath('//a[@class="run-modal"][1]').click()

        self.wait_for_element_by_id("subscription-container")

        self.wait_for_element_by_xpath(
            '(//div[@class="header-bg"][1])/h2[1][contains(text(), '
            '"Wysyłasz fakturę korygującą do {0}")]'.format(uczestnik.email)
        )

        self.selenium.find_element_by_id("id_to_emails").send_keys("<EMAIL>")
        self.selenium.find_element_by_id("id_cc_emails").send_keys(
            "<EMAIL>, <EMAIL>"
        )
        self.selenium.find_element_by_id("subscription-ajax-form-submit").click()

        self.wait_for_element_by_xpath(
            '(//div[@id="invoice-send-status"][1])/p[1][contains(text(), '
            '"Faktura korygująca została wysłana.")]'
        )

        # Sprawdzamy, czy zapisał się log i zmienił status faktury
        uczestnik = Uczestnik.objects.get(pk=uczestnik.pk)
        self.assertTrue(uczestnik.korekta_wyslana)

        self.assertEqual(len(mail.outbox), 1)
        title = "szkolenie {0} od 2015-01-01".format(
            uczestnik.termin.nazwa_szkolenia_lub_snz()
        )

        self.assertEqual(
            mail.outbox[0].subject, "Faktura korygująca - ALX - {0}".format(title)
        )
        self.assertIn(
            'Po otrzymaniu wiadomości <strong>"Potwierdzam treść faktury '
            'korygującej"</strong> dokonamy przelewu.',
            mail.outbox[0].body,
        )
        self.assertIn("Jan Pawłowski", mail.outbox[0].body)
        self.assertEqual(len(mail.outbox[0].attachments), 1)

        self.assertCountEqual(mail.outbox[0].to, [uczestnik.email, "<EMAIL>"])
        self.assertCountEqual(mail.outbox[0].cc, ["<EMAIL>", "<EMAIL>"])

    @override_settings(GENERATE_INVOICE_THROUGH_API_ZLICZACZ=True)
    @patch("requests.get")
    def test_wyslij_fakture_failed(self, mock_get):
        mock_get.side_effect = self.mock_requests_get
        self.response_get_status_code = 400
        self.response_get_text = "{'details': 'error'}"

        uczestnik = UczestnikFactory(
            adres="adres",
            miejscowosc_kod="77-798 Warszawa",
            prywatny=True,
            wystaw_proforme_automatycznie=True,
            za_kurs_zaplace=5,
            imie_nazwisko_zostalo_sprawdzone=True,
            zliczacz_proforma_no="12345",
            termin__termin=datetime.date(2015, 1, 1),
        )
        self.assertFalse(uczestnik.proforma_wyslana)

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_uczestnik_change", args=(uczestnik.pk,))
        )

        self.selenium.find_element_by_xpath('//a[@class="run-modal"][1]').click()

        self.wait_for_element_by_id("subscription-container")

        self.selenium.find_element_by_id("id_to_emails").send_keys("<EMAIL>")
        self.selenium.find_element_by_id("id_cc_emails").send_keys(
            "<EMAIL>, <EMAIL>"
        )
        self.selenium.find_element_by_id("subscription-ajax-form-submit").click()

        self.wait_for_element_by_xpath(
            '(//div[@id="invoice-send-status"])[1]/p[1][contains(text(), '
            '"Wystapił błąd podczas wysyłki. Odpowiedź serwera: '
            "{'details': 'error'}.\")]"
        )

        # Log nie powinien sie zapisać
        uczestnik = Uczestnik.objects.get(pk=uczestnik.pk)
        self.assertFalse(uczestnik.proforma_wyslana)
        self.assertEqual(len(mail.outbox), 0)

    @override_settings(GENERATE_INVOICE_THROUGH_API_ZLICZACZ=True)
    @patch("www.admin.utils")
    def test_uczestnik_uczestnik_fvat_note_link(self, mock_utils):
        uczestnik = UczestnikFactory(
            adres="adres",
            miejscowosc_kod="77-798 Miejscowosc",
            prywatny=True,
            wystaw_proforme_automatycznie=True,
            za_kurs_zaplace=5,
            imie_nazwisko_zostalo_sprawdzone=True,
            zaplacone=datetime.date.today(),
            nr_faktury="abc",
            termin__odbylo_sie=False,
        )

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_uczestnik_change", args=(uczestnik.pk,))
        )
        page_text = self.selenium.find_element_by_tag_name("body").text
        self.assertIn("(skoryguj do zera)", page_text)

        uczestnik.nr_faktury = ""
        uczestnik.save()

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_uczestnik_change", args=(uczestnik.pk,))
        )
        page_text = self.selenium.find_element_by_tag_name("body").text
        self.assertNotIn("(skoryguj do zera)", page_text)

    @override_settings(GENERATE_INVOICE_THROUGH_API_ZLICZACZ=True)
    @patch("www.admin.utils")
    def test_uczestnik_uczestnik_fvat_note(self, mock_utils):
        uczestnik = UczestnikFactory(
            adres="adres",
            miejscowosc_kod="77-798 Miejscowosc",
            prywatny=True,
            wystaw_proforme_automatycznie=True,
            za_kurs_zaplace=5,
            imie_nazwisko_zostalo_sprawdzone=True,
            zaplacone=datetime.date.today(),
            nr_faktury="abc",
            termin__odbylo_sie=False,
        )

        api_data = FakturaKorektaTestCase.get_api_data()
        api_data["uczestnik"] = uczestnik

        mock_utils.generate_invoice_note_data.return_value = api_data, None

        self.selenium.get(
            self.live_server_url
            + reverse("admin:uczestnik_uczestnik_fnote", args=(uczestnik.pk,))
        )

        page_text = self.selenium.find_element_by_tag_name("body").text
        self.assertIn("Faktura korygująca została poprawnie wygenerowana.", page_text)

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_uczestnik_change", args=(uczestnik.pk,))
        )
        page_text = self.selenium.find_element_by_tag_name("body").text

        self.assertIn(
            "(Korekta {0})".format(uczestnik.get_invoice_note().faktura_numer_korekty),
            page_text,
        )

    @override_settings(GENERATE_INVOICE_THROUGH_API_ZLICZACZ=True)
    @patch("www.admin.utils")
    def test_uczestnik_uczestnik_fvat_note_failed(self, mock_utils):
        uczestnik = UczestnikFactory(
            adres="adres",
            miejscowosc_kod="77-798 Miejscowosc",
            prywatny=True,
            wystaw_proforme_automatycznie=True,
            za_kurs_zaplace=5,
            imie_nazwisko_zostalo_sprawdzone=True,
            zaplacone=datetime.date.today(),
            nr_faktury="abc",
            termin__odbylo_sie=False,
        )

        mock_utils.generate_invoice_note_data.return_value = None, "Blad"

        self.selenium.get(
            self.live_server_url
            + reverse("admin:uczestnik_uczestnik_fnote", args=(uczestnik.pk,))
        )

        page_text = self.selenium.find_element_by_tag_name("body").text
        self.assertIn(
            "Wystąpił błąd podczas generowania faktury. Treść błędu: Blad", page_text
        )


@override_settings(
    GENERATE_INVOICE_THROUGH_API_ZLICZACZ=False,
    task_eager_propagates=True,
    task_always_eager=True,
    BROKER_BACKEND="memory",
)
class TerminSzkoleniaAdminTestCase(ALXLiveServerTestCase):
    def setUp(self):
        super().setUp()
        self.admin_user = self.admin_login()

    def test_button_wyslij_mail_do_grupy(self):
        term = TerminSzkoleniaFactory.create()

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_terminszkolenia_change", args=(term.pk,))
        )

        self.assertTrue(
            self.selenium.find_element_by_xpath('//a[@class="wyslij_mail_do_grupy"][1]')
        )

    def test_wyslij_mail_do_grupy_walidacja(self):
        term = TerminSzkoleniaFactory.create()

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_terminszkolenia_change", args=(term.pk,))
        )

        self.selenium.find_element_by_xpath(
            '//a[@class="wyslij_mail_do_grupy"][1]'
        ).click()

        self.wait_for_element_by_id("subscription-container")

        self.selenium.find_element_by_id("id_subject").clear()
        self.selenium.find_element_by_id("id_message").clear()
        self.selenium.find_element_by_id("subscription-ajax-form-submit").click()

        # Uczestnicy
        error = self.wait_for_element_by_xpath(
            '(//ul[@class="errorlist"])[1]' "/li[1]"
        ).text
        self.assertEqual(error, "Brak Uczestników o podanym statusie.")

        # Temat maila
        error = self.wait_for_element_by_xpath(
            '(//ul[@class="errorlist"])[2]' "/li[1]"
        ).text
        self.assertEqual(error, "To pole jest wymagane.")

        # Treść maila
        error = self.wait_for_element_by_xpath(
            '(//ul[@class="errorlist"])[3]' "/li[1]"
        ).text
        self.assertEqual(error, "To pole jest wymagane.")

        self.assertEqual(len(mail.outbox), 0)

    def test_wyslij_mail_do_grupy_bez_uzytkownikow(self):
        term = TerminSzkoleniaFactory.create()

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_terminszkolenia_change", args=(term.pk,))
        )

        self.selenium.find_element_by_xpath(
            '//a[@class="wyslij_mail_do_grupy"][1]'
        ).click()

        self.wait_for_element_by_id("subscription-container")
        self.selenium.find_element_by_id("subscription-ajax-form-submit").click()

        error = self.wait_for_element_by_xpath(
            '(//ul[@class="errorlist"])[1]' "/li[1]"
        ).text
        self.assertEqual(error, "Brak Uczestników o podanym statusie.")

    def test_wyslij_mail_do_grupy_gdy_nie_ma_statusu(self):
        term = TerminSzkoleniaFactory.create()

        UczestnikFactory(termin=term, status=1)

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_terminszkolenia_change", args=(term.pk,))
        )

        self.selenium.find_element_by_xpath(
            '//a[@class="wyslij_mail_do_grupy"][1]'
        ).click()

        self.wait_for_element_by_id("subscription-container")

        s = Select(
            self.selenium.find_element_by_xpath('//select[@id="id_participant_status"]')
        )
        s.select_by_visible_text("trzeba przemówić")
        self.selenium.find_element_by_id("id_subject").send_keys("abc ...")
        self.selenium.find_element_by_id("id_message").send_keys("super treść")
        self.selenium.find_element_by_id("subscription-ajax-form-submit").click()

        error = self.wait_for_element_by_xpath(
            '(//ul[@class="errorlist"])[1]' "/li[1]"
        ).text
        self.assertEqual(error, "Brak Uczestników o podanym statusie.")

    def test_wyslij_mail_do_grupy(self):
        term = TerminSzkoleniaFactory.create()

        UczestnikFactory(termin=term)
        UczestnikFactory(termin=term)
        UczestnikFactory(termin=term, status=4)

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_terminszkolenia_change", args=(term.pk,))
        )

        self.selenium.find_element_by_xpath(
            '//a[@class="wyslij_mail_do_grupy"][1]'
        ).click()

        self.wait_for_element_by_id("subscription-container")

        self.selenium.find_element_by_id("id_subject").send_keys("abc ...")
        self.selenium.find_element_by_id("id_message").send_keys("super treść")
        self.selenium.find_element_by_id("subscription-ajax-form-submit").click()

        self.wait_for_element_by_xpath(
            '(//div[@id="invoice-send-status"][1])/p[1][contains(text(), '
            '"Wysłka została zlecona.")]'
        )

        self.assertEqual(len(mail.outbox), 2)

        self.assertEqual(mail.outbox[0].subject, "abc ...")
        self.assertIn("super treść", mail.outbox[0].body)

        obj = TerminSzkoleniaMail.objects.get()
        self.assertEqual(obj.author, self.admin_user)
        self.assertEqual(obj.term, term)

    def test_wyslij_mail_do_grupy_niezrezygnowanych(self):
        term = TerminSzkoleniaFactory.create()

        UczestnikFactory(termin=term, status=1)
        UczestnikFactory(termin=term, status=-1)
        UczestnikFactory(termin=term, status=2)
        UczestnikFactory(termin=term, status=4)

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_terminszkolenia_change", args=(term.pk,))
        )

        self.selenium.find_element_by_xpath(
            '//a[@class="wyslij_mail_do_grupy"][1]'
        ).click()

        self.wait_for_element_by_id("subscription-container")

        s = Select(
            self.selenium.find_element_by_xpath('//select[@id="id_participant_status"]')
        )
        s.select_by_visible_text("WSZYSCY NIEZREZYGNOWANI")
        self.selenium.find_element_by_id("id_subject").send_keys("abc ...")
        self.selenium.find_element_by_id("id_message").send_keys("super treść")
        self.selenium.find_element_by_id("subscription-ajax-form-submit").click()

        self.wait_for_element_by_xpath(
            '(//div[@id="invoice-send-status"][1])/p[1][contains(text(), '
            '"Wysłka została zlecona.")]'
        )

        self.assertEqual(len(mail.outbox), 3)

    def test_zwijanie_rozwijanie_uczestnikow(self):
        """
        Sprawdzenie czy JavaScript działa w funkcjach pokazujących i chowających uczestników.
        """

        TerminSzkoleniaFactory.create_batch(10)

        termin = TerminSzkoleniaFactory()
        UczestnikFactory(termin=termin, imie_nazwisko="Jan Zwijalnyirozwijalny")
        url = reverse("admin:www_terminszkolenia_changelist")
        self.selenium.get(self.live_server_url + url)
        element_xpath = "//td/a[contains(text(),'Jan Zwijalnyirozwijalny')]"
        zwin = "zwiń wszystkich uczestników"
        rozwin = "rozwiń wszystkich uczestników"
        schowaj = "schowaj uczestników"
        pokaz = "pokaż uczestników"
        self.wait_for_element_by_xpath(element_xpath)
        self.selenium.find_element_by_xpath(
            "//a[contains(text(), '{0}')]".format(zwin)
        ).click()
        self.wait_for_hidden_element_by_xpath(element_xpath)
        self.selenium.find_element_by_xpath(
            "//a[contains(text(), '{0}')]".format(rozwin)
        ).click()
        self.wait_for_visible_element_by_xpath(element_xpath)
        self.wait_for_visible_element_by_xpath(
            "//table[contains(., '{0}')]/preceding-sibling::span/a[contains(text(), '{1}')]".format(
                "Zwijalnyirozwijalny", schowaj
            )
        ).click()
        self.wait_for_hidden_element_by_xpath(element_xpath)
        self.wait_for_visible_element_by_xpath(
            "//table[contains(., '{0}')]/preceding-sibling::span/a[contains(text(), '{1}')]".format(
                "Zwijalnyirozwijalny", pokaz
            )
        ).click()
        self.wait_for_visible_element_by_xpath(element_xpath)

    def test_przelacz_czy_robimy_sala_przygotowana(self):
        termin1 = TerminSzkoleniaFactory(odbylo_sie=True)
        url = reverse("admin:www_terminszkolenia_change", args=(termin1.id,))
        self.selenium.get(self.live_server_url + url)
        self.wait_for_visible_element_by_xpath(
            '//select[@id="id_odbylo_sie"]/option[contains(text(), "Tak")]'
        )
        self.wait_for_visible_element_by_xpath('//input[@id="id_sala_przygotowana"]')
        czy_robimy_select = Select(
            self.selenium.find_element_by_xpath('//select[@id="id_odbylo_sie"]')
        )
        czy_robimy_select.select_by_visible_text("Nieznany")
        self.wait_for_hidden_element_by_xpath('//input[@id="id_sala_przygotowana"]')
        czy_robimy_select.select_by_visible_text("Nie")
        self.wait_for_hidden_element_by_xpath('//input[@id="id_sala_przygotowana"]')
        czy_robimy_select.select_by_visible_text("Tak")
        self.wait_for_visible_element_by_xpath('//input[@id="id_sala_przygotowana"]')
        termin2 = TerminSzkoleniaFactory(odbylo_sie=False)
        url = reverse("admin:www_terminszkolenia_change", args=(termin2.id,))
        self.selenium.get(self.live_server_url + url)
        self.wait_for_visible_element_by_xpath(
            '//select[@id="id_odbylo_sie"]/option[contains(text(), "Nie")]'
        )
        self.wait_for_hidden_element_by_xpath('//input[@id="id_sala_przygotowana"]')
        czy_robimy_select = Select(
            self.selenium.find_element_by_xpath('//select[@id="id_odbylo_sie"]')
        )
        czy_robimy_select.select_by_visible_text("Tak")
        self.wait_for_visible_element_by_xpath('//input[@id="id_sala_przygotowana"]')

    def test_termin_link_douczestnik(self):
        """
        Sprawdza czy link od uczestnika prowadzi do właściwego uczestnika
        """
        termin = TerminSzkoleniaFactory()
        termin_id = termin.pk
        # termin_data = "{0}".format(termin.termin)
        # termin_lokalizacja = termin.lokalizacja
        # szkolenie_nazwa = termin.szkolenie.nazwa
        # szkolenie_kod = termin.szkolenie.kod
        uczestnik = []
        for i in range(0, 3):
            uczestnik.append(UczestnikFactory(termin=termin))
        for i in range(0, 3):
            self.selenium.get(
                self.live_server_url
                + reverse("admin:www_terminszkolenia_change", args=(termin_id,))
            )
            link = self.wait_for_element_by_xpath(
                '//a[@id="uczestnik_link_{0}"]'.format(i)
            )
            uczestnik_input_id = self.selenium.find_element_by_xpath(
                '//input[@id="id_uczestnik_set-{0}-id"]'.format(i)
            )
            uczestnik_id = uczestnik_input_id.get_attribute("value")
            uczestnik_imie_nazwisko = self.selenium.find_element_by_xpath(
                '//textarea[@id="id_uczestnik_set-{0}-imie_nazwisko"]'.format(i)
            ).text
            link.click()
            try:
                self.wait_for_element_by_xpath('//form[@id="uczestnik_form"]')
            except TimeoutException:
                self.wait_for_element_by_xpath('//form[@id="uczestnik_form"]')
            url = self.selenium.current_url
            self.assertTrue("uczestnik/{0}".format(uczestnik_id) in url)
            imie_nazwisko = self.selenium.find_element_by_xpath(
                '//textarea[@id="id_imie_nazwisko"]'
            ).text
            self.assertEqual(uczestnik_imie_nazwisko, imie_nazwisko)

    def test_uczestnik_admin_inline_form(self):
        """
        Sprawdza walidacje uczestników pod kątem danych do faktury.
        """

        e = TerminSzkoleniaFactory(odbylo_sie=True)
        UczestnikFactory(
            termin=e,
            wystaw_proforme_automatycznie=True,
            prywatny=True,
            email="",
        )

        url = reverse("admin:www_terminszkolenia_change", args=(e.id,))
        self.selenium.get(self.live_server_url + url)

        # Zapisz obiekt
        self.selenium.find_element_by_xpath(
            '//input[@type="submit" and @name="_save"]'
        ).click()

        # Zwróci błąd, gdyż uczestnik nie ma uzupełnionych danych do faktury
        error = self.wait_for_element_by_xpath(
            '//div[@class="form-row errors '
            'field-wystaw_proforme_automatycznie"][1]'
            '/ul[@class="errorlist"]'
            "/li[1]"
        ).text
        self.assertEqual(error, "Uzupełnij dane do faktury.")

        # Uzupełniamy resztę danych do faktury
        self.selenium.find_element_by_xpath(
            '//div[@id="uczestnik_set-0"]'
            '/fieldset[@class="module aligned collapse collapsed"][1]'
            "/h2[1]"
            '/a[@class="collapse-toggle"]'
        ).click()

        self.selenium.find_element_by_id("id_uczestnik_set-0-imie_nazwisko").send_keys(
            "Imię i Nazwisko"
        )
        self.selenium.find_element_by_id("id_uczestnik_set-0-adres").send_keys(
            "Adres ..."
        )
        self.selenium.find_element_by_id(
            "id_uczestnik_set-0-miejscowosc_kod"
        ).send_keys("00-987 Warszawa")
        self.selenium.find_element_by_id("id_uczestnik_set-0-email").send_keys(
            "<EMAIL>"
        )
        self.selenium.find_element_by_id(
            "id_uczestnik_set-0-imie_nazwisko_zostalo_sprawdzone"
        ).click()

        # Zapisz obiekt
        self.selenium.find_element_by_xpath(
            '//input[@type="submit" and @name="_save"]'
        ).click()

        try:
            self.wait_for_element_by_xpath(
                '//div[@class="form-row errors '
                'field-wystaw_proforme_automatycznie"][1]'
                '/ul[@class="errorlist"]'
                "/li[1]"
            )
        except TimeoutException:
            # To jest ok, bo błędów ma już nie być
            pass
        else:
            self.fail("Błędna walidacja")

        # Teraz zróbmy to samo ale dla zupełnie nowego obiketu Uczesnika

        e = TerminSzkoleniaFactory(odbylo_sie=True)
        url = reverse("admin:www_terminszkolenia_change", args=(e.id,))
        self.selenium.get(self.live_server_url + url)

        self.selenium.find_element_by_xpath(
            '//div[@id="uczestnik_set-group"]/div[@class="add-row"][1]/a[1]'
        ).click()

        self.selenium.find_element_by_xpath(
            '//div[@id="uczestnik_set-0"]'
            '/fieldset[@class="module aligned collapse collapsed"][1]'
            "/h2[1]"
            '/a[@class="collapse-toggle"]'
        ).click()
        self.selenium.find_element_by_xpath(
            '//div[@id="uczestnik_set-0"]'
            '/fieldset[@class="module aligned collapse collapsed"][1]'
            "/h2[1]"
            '/a[@class="collapse-toggle"]'
        ).click()

        self.selenium.find_element_by_id("id_uczestnik_set-0-imie_nazwisko").send_keys(
            "Imię i Nazwisko"
        )
        self.selenium.find_element_by_id(
            "id_uczestnik_set-0-imie_nazwisko_zostalo_sprawdzone"
        ).click()
        self.selenium.find_element_by_id("id_uczestnik_set-0-prywatny").click()
        self.selenium.find_element_by_xpath(
            '//select[@id="id_uczestnik_set-0-waluta"]/option[@value="1"]'
        ).click()

        # Zapisz obiekt
        self.selenium.find_element_by_xpath(
            '//input[@type="submit" and @name="_save"]'
        ).click()

        error = self.wait_for_element_by_xpath(
            '//div[@class="form-row errors '
            'field-wystaw_proforme_automatycznie"][1]'
            '/ul[@class="errorlist"]'
            "/li[1]"
        ).text
        self.assertEqual(error, "Uzupełnij dane do faktury.")

        # Uzupełniamy resztę danych do faktury
        self.selenium.find_element_by_xpath(
            '//div[@id="uczestnik_set-0"]'
            '/fieldset[@class="module aligned collapse collapsed"][1]'
            "/h2[1]"
            '/a[@class="collapse-toggle"]'
        ).click()

        self.selenium.find_element_by_id("id_uczestnik_set-0-adres").send_keys(
            "Adres ..."
        )
        self.selenium.find_element_by_id(
            "id_uczestnik_set-0-miejscowosc_kod"
        ).send_keys("00-987 Warszawa")
        self.selenium.find_element_by_id("id_uczestnik_set-0-email").send_keys(
            "<EMAIL>"
        )

        # Zapisz obiekt
        self.selenium.find_element_by_xpath(
            '//input[@type="submit" and @name="_save"]'
        ).click()

        try:
            self.wait_for_element_by_xpath(
                '//div[@class="form-row errors '
                'field-wystaw_proforme_automatycznie"][1]'
                '/ul[@class="errorlist"]'
                "/li[1]"
            )
        except TimeoutException:
            # To jest ok, bo błędów ma już nie być
            pass
        else:
            self.fail("Błędna walidacja")

    def test_uczestnik_admin_inline_form_nip_private_person(self):
        # Bląd - osoba prywatna nie może mieć numeru NIP

        e = TerminSzkoleniaFactory(odbylo_sie=True)
        user = UczestnikFactory(
            termin=e,
            wystaw_proforme_automatycznie=True,
            prywatny=True,
            imie_nazwisko_zostalo_sprawdzone=True,
            faktura_adres="adres",
            faktura_miejscowosc_kod="02-765 Warszawa",
            faktura_kraj="PL",
        )

        user.faktura_firma = "Firma"
        user.faktura_nip = "1"
        user.save()

        url = reverse("admin:www_terminszkolenia_change", args=(e.id,))
        self.selenium.get(self.live_server_url + url)

        self.selenium.find_element_by_xpath(
            '//input[@type="submit" and @name="_save"]'
        ).click()

        error = self.wait_for_element_by_xpath(
            '//div[@class="form-row errors '
            'field-faktura_nip"][1]'
            '/ul[@class="errorlist"]'
            "/li[1]"
        ).text
        self.assertEqual(error, "Osoba prywatna nie może mieć numeru NIP.")

    def test_uczestnik_admin_inline_form_nip_company_pl_invalid_nip(self):
        # Bląd - firma z Polski ma niepoprawny NIP

        e = TerminSzkoleniaFactory(odbylo_sie=True)
        user = UczestnikFactory(
            termin=e,
            wystaw_proforme_automatycznie=True,
            prywatny=False,
            imie_nazwisko_zostalo_sprawdzone=True,
            faktura_adres="adres",
            faktura_miejscowosc_kod="02-765 Warszawa",
            faktura_kraj="PL",
        )

        user.faktura_firma = "Firma"
        user.faktura_nip = "1"
        user.save()

        url = reverse("admin:www_terminszkolenia_change", args=(e.id,))
        self.selenium.get(self.live_server_url + url)

        self.selenium.find_element_by_xpath(
            '//input[@type="submit" and @name="_save"]'
        ).click()

        error = self.wait_for_element_by_xpath(
            '//div[@class="form-row errors '
            'field-faktura_nip"][1]'
            '/ul[@class="errorlist"]'
            "/li[1]"
        ).text
        self.assertEqual(error, CustomPLNIPField.default_error_messages["invalid"])

        # Bląd - firma z Polski ma niepoprawny NIP, mimo, że termin nie
        # startuje lub nie ma opcji automatycznej faktury.

        e = TerminSzkoleniaFactory(odbylo_sie=False)
        user = UczestnikFactory(
            termin=e,
            wystaw_proforme_automatycznie=False,
            prywatny=False,
            imie_nazwisko_zostalo_sprawdzone=True,
            faktura_adres="adres",
            faktura_miejscowosc_kod="02-765 Warszawa",
            faktura_kraj="PL",
        )

        user.faktura_firma = "Firma"
        user.faktura_nip = "1"
        user.save()

        url = reverse("admin:www_terminszkolenia_change", args=(e.id,))
        self.selenium.get(self.live_server_url + url)

        self.selenium.find_element_by_xpath(
            '//input[@type="submit" and @name="_save"]'
        ).click()

        error = self.wait_for_element_by_xpath(
            '//div[@class="form-row errors '
            'field-faktura_nip"][1]'
            '/ul[@class="errorlist"]'
            "/li[1]"
        ).text
        self.assertEqual(error, CustomPLNIPField.default_error_messages["invalid"])

    def test_uczestnik_admin_inline_form_nip_company_non_pl_success(self):
        # Jest ok - firma nie z Polski ma dowolny NIP

        e = TerminSzkoleniaFactory(odbylo_sie=True)
        user = UczestnikFactory(
            termin=e,
            wystaw_proforme_automatycznie=True,
            prywatny=False,
            imie_nazwisko_zostalo_sprawdzone=True,
            faktura_adres="adres",
            faktura_miejscowosc_kod="02-765 Warszawa",
            faktura_kraj="GB",
        )

        user.faktura_firma = "Firma"
        user.faktura_nip = "1"
        user.save()

        url = reverse("admin:www_terminszkolenia_change", args=(e.id,))
        self.selenium.get(self.live_server_url + url)

        self.selenium.find_element_by_xpath(
            '//input[@type="submit" and @name="_save"]'
        ).click()

        try:
            self.wait_for_element_by_xpath(
                '//div[@class="form-row errors '
                'field-faktura_nip"][1]'
                '/ul[@class="errorlist"]'
                "/li[1]"
            )
        except TimeoutException:
            # To jest ok, bo błędów ma już nie być
            pass
        else:
            self.fail("Błędna walidacja NIP")

    def test_uczestnik_admin_inline_form_nip_company_pl_invalid_checksum(self):
        # Bląd - firma z Polski ma poprawny NIP, ale nie zgadza się suma
        # kontrolna.

        e = TerminSzkoleniaFactory(odbylo_sie=True)
        user = UczestnikFactory(
            termin=e,
            wystaw_proforme_automatycznie=True,
            prywatny=False,
            imie_nazwisko_zostalo_sprawdzone=True,
            faktura_adres="adres",
            faktura_miejscowosc_kod="02-765 Warszawa",
            faktura_kraj="PL",
        )

        user.faktura_firma = "Firma"
        user.faktura_nip = "1234567893"
        user.save()

        url = reverse("admin:www_terminszkolenia_change", args=(e.id,))
        self.selenium.get(self.live_server_url + url)

        self.selenium.find_element_by_xpath(
            '//input[@type="submit" and @name="_save"]'
        ).click()

        error = self.wait_for_element_by_xpath(
            '//div[@class="form-row errors '
            'field-faktura_nip"][1]'
            '/ul[@class="errorlist"]'
            "/li[1]"
        ).text
        self.assertEqual(error, CustomPLNIPField.default_error_messages["checksum"])

    def test_uczestnik_admin_inline_form_nip_company_pl_success(self):
        # OK - firma z Polski ma poprawny NIP

        e = TerminSzkoleniaFactory(odbylo_sie=True)
        user = UczestnikFactory(
            termin=e,
            wystaw_proforme_automatycznie=True,
            prywatny=False,
            imie_nazwisko_zostalo_sprawdzone=True,
            faktura_adres="adres",
            faktura_miejscowosc_kod="02-765 Warszawa",
            faktura_kraj="PL",
        )

        user.faktura_firma = "Firma"
        user.faktura_nip = "527-26-42-198"
        user.save()

        url = reverse("admin:www_terminszkolenia_change", args=(e.id,))
        self.selenium.get(self.live_server_url + url)

        self.selenium.find_element_by_xpath(
            '//input[@type="submit" and @name="_save"]'
        ).click()

        try:
            self.wait_for_element_by_xpath(
                '//div[@class="form-row errors '
                'field-faktura_nip"][1]'
                '/ul[@class="errorlist"]'
                "/li[1]"
            )
        except TimeoutException:
            # To jest ok, bo błędów ma już nie być
            pass
        else:
            self.fail("Błędna walidacja NIP")

    def test_uczestnik_admin_inline_form_nip_company_pl_prefix_success(self):
        # OK - firma z Polski ma poprawny NIP z przedrostkiem PL

        e = TerminSzkoleniaFactory(odbylo_sie=True)
        user = UczestnikFactory(
            termin=e,
            wystaw_proforme_automatycznie=True,
            prywatny=False,
            imie_nazwisko_zostalo_sprawdzone=True,
            faktura_adres="adres",
            faktura_miejscowosc_kod="02-765 Warszawa",
            faktura_kraj="PL",
        )

        user.faktura_firma = "Firma"
        user.faktura_nip = "PL527-26-42-198"
        user.save()

        url = reverse("admin:www_terminszkolenia_change", args=(e.id,))
        self.selenium.get(self.live_server_url + url)

        self.selenium.find_element_by_xpath(
            '//input[@type="submit" and @name="_save"]'
        ).click()

        try:
            self.wait_for_element_by_xpath(
                '//div[@class="form-row errors '
                'field-faktura_nip"][1]'
                '/ul[@class="errorlist"]'
                "/li[1]"
            )
        except TimeoutException:
            # To jest ok, bo błędów ma już nie być
            pass
        else:
            self.fail("Błędna walidacja NIP")

    def test_uczestnik_admin_inline_form_nip_company_pl_invalid_prefix(self):
        # Bląd - firma z Polski ma poprawny NIP, ale błędny prefix.

        e = TerminSzkoleniaFactory(odbylo_sie=True)
        user = UczestnikFactory(
            termin=e,
            wystaw_proforme_automatycznie=True,
            prywatny=False,
            imie_nazwisko_zostalo_sprawdzone=True,
            faktura_adres="adres",
            faktura_miejscowosc_kod="02-765 Warszawa",
            faktura_kraj="PL",
        )

        user.faktura_firma = "Firma"
        user.faktura_nip = "GB527-26-42-198"
        user.save()

        url = reverse("admin:www_terminszkolenia_change", args=(e.id,))
        self.selenium.get(self.live_server_url + url)

        self.selenium.find_element_by_xpath(
            '//input[@type="submit" and @name="_save"]'
        ).click()

        error = self.wait_for_element_by_xpath(
            '//div[@class="form-row errors '
            'field-faktura_nip"][1]'
            '/ul[@class="errorlist"]'
            "/li[1]"
        ).text
        self.assertEqual(error, CustomPLNIPField.default_error_messages["invalid"])

    def test_after_saving_model_and_related_inlines(self):
        """
        Logika uruchamiania zadań celery została przeniesiona do AdminSite,
        należy sprawdzić, czy uruchamiane są poprawnie.
        """

        # Dane początkowe
        e = TerminSzkoleniaFactory(
            odbylo_sie=False, prowadzacy=ProwadzacyFactory(user=UserFactory.create())
        )
        e.sala = SalaFactory(lokalizacja=e.lokalizacja)
        e.save()

        UczestnikFactory.create_batch(
            10, termin=e, wystaw_proforme_automatycznie=False, prywatny=True
        )

        url = reverse("admin:www_terminszkolenia_change", args=(e.id,))
        self.selenium.get(self.live_server_url + url)

        # Ustaw, że robimy
        Select(
            self.selenium.find_element_by_xpath('//select[@id="id_odbylo_sie"]')
        ).select_by_visible_text("Tak")

        # Zapisz obiekt
        self.selenium.find_element_by_xpath(
            '//input[@type="submit" and @name="_save"]'
        ).click()

        # Powinno zstać wysłanych 12 maili:
        #  - 1 z `poinformuj_trenerow_o_potwierdzeniu_terminu`
        #  - 10 z `poinformuj_zapisanych_o_potwierdzeniu_terminu` + podsumowanie
        self.assertEqual(len(mail.outbox), 12)

        # Spróbuj zapisać jeszcze raz to samo szkolenie. Nie powinny zostać
        # wysłane żadne maile.
        mail.outbox = []
        self.selenium.get(self.live_server_url + url)

        # Zapisz obiekt
        self.selenium.find_element_by_xpath(
            '//input[@type="submit" and @name="_save"]'
        ).click()

        self.assertEqual(len(mail.outbox), 0)

    def test_printable_certificates_in_admin_inlines(self):
        # Poprawny link
        e = TerminSzkoleniaFactory(odbylo_sie=True)
        SzkolenieFactory.create(base_translation=e.szkolenie)
        UczestnikFactory(termin=e)

        url = reverse("admin:www_terminszkolenia_change", args=(e.id,))
        self.selenium.get(self.live_server_url + url)

        link = self.selenium.find_element_by_xpath('//a[@class="cert"][1]')
        self.assertEqual(link.text, "Pobierz PDF")

        link = self.selenium.find_element_by_xpath('//a[@class="cert"][2]')
        self.assertEqual(link.text, "Pobierz PDF (stara wersja)")

        link = self.selenium.find_element_by_xpath('//a[@class="cert"][3]')
        self.assertEqual(link.text, "Pobierz PDF[EN] (stara wersja)")

        # Brak linku, gdyż termin nie nadaje się do wystawiania certyfikatów.
        e.odbylo_sie = False
        e.save()

        url = reverse("admin:www_terminszkolenia_change", args=(e.id,))
        self.selenium.get(self.live_server_url + url)

        try:
            self.selenium.find_element_by_xpath('//a[@class="cert"]')
        except NoSuchElementException:
            # Jest ok, ma nie być tego elementu
            pass
        else:
            self.fail("Błędna walidacja.")

    def test_printable_en_certificate_when_base_translation(self):
        self.assertTrue(
            self.client.login(username=self.admin_user.username, password="test.pw")
        )

        e = TerminSzkoleniaFactory(odbylo_sie=True)
        SzkolenieFactory.create(base_translation=e.szkolenie, language="en")
        uczestnik = UczestnikFactory(termin=e)

        resp = self.client.get(
            reverse(
                "admin:printable_certificates_for_participant",
                args=(e.pk, "old_en", uczestnik.pk),
            ),
            follow=True,
        )
        self.assertTrue(resp.content)

    def test_printable_en_certificate_when_no_base_translation(self):
        self.assertTrue(
            self.client.login(username=self.admin_user.username, password="test.pw")
        )

        e = TerminSzkoleniaFactory(odbylo_sie=True)
        uczestnik = UczestnikFactory(termin=e)

        resp = self.client.get(
            reverse(
                "admin:printable_certificates_for_participant",
                args=(e.pk, "old_en", uczestnik.pk),
            ),
            follow=True,
        )

        self.assertIn(
            "To szkolenie nie ma odpowiednika w wersji angielskiej.".encode("utf-8"),
            resp.content,
        )

    def test_printable_en_certificate_when_en_language(self):
        e = TerminSzkoleniaFactory(odbylo_sie=True)
        UczestnikFactory(termin=e)

        url = reverse("admin:www_terminszkolenia_change", args=(e.id,))
        self.selenium.get(self.live_server_url + url)

        link = self.selenium.find_element_by_xpath('//a[@class="cert"][3]')
        self.assertEqual(link.text, "Pobierz PDF[EN] (stara wersja)")

        e.szkolenie.language = "en"
        e.szkolenie.save()

        url = reverse("admin:www_terminszkolenia_change", args=(e.id,))
        self.selenium.get(self.live_server_url + url)

        try:
            self.selenium.find_element_by_xpath('//a[@class="cert"][3]')
        except NoSuchElementException:
            # Jest ok, ma nie być tego elementu
            pass
        else:
            self.fail("Błędna walidacja.")

    def test_termin_egzaminu_czyproforma(self):
        """
        Uczestnicy zapisani na termin szkolenia, które ma ustawione "wysylaj_powiadomienia_proformy"
        na False, mają pole "wystaw_proforme_automatycznie" ustawiane na False
        """
        szkolenie = SzkolenieFactory(wysylaj_powiadomienia_proformy=False)
        termin = TerminSzkoleniaFactory(szkolenie=szkolenie)
        UczestnikFactory.create_batch(4, termin=termin)
        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_terminszkolenia_change", args=(termin.id,))
        )
        for i in range(4):
            wystaw_proforme = self.wait_for_element_by_xpath(
                '//input[contains(@id, "id_uczestnik_set-{0}-wystaw_proforme_automatycznie")]'.format(
                    i
                )
            )
            czy_checked = wystaw_proforme.get_attribute("checked")
            self.assertEqual(czy_checked, None)

    def test_termin_egzaminu_czyproforma_blad_walidacji(self):
        """
        Waliduje pole wystaw_proforme_automatycznie podczas zapisu inline.
        """

        szkolenie = SzkolenieFactory(wysylaj_powiadomienia_proformy=False)
        termin = TerminSzkoleniaFactory(szkolenie=szkolenie)
        UczestnikFactory.create_batch(4, termin=termin)
        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_terminszkolenia_change", args=(termin.id,))
        )

        for i in range(4):
            self.selenium.find_element_by_xpath(
                '//div[@id="uczestnik_set-{0}"]'
                '/fieldset[@class="module aligned collapse collapsed"][2]'
                "/h2[1]"
                '/a[@class="collapse-toggle"]'.format(i)
            ).click()

            wystaw_proforme = self.selenium.find_element_by_xpath(
                '//input[contains(@id, "id_uczestnik_set-{0}-'
                'wystaw_proforme_automatycznie")]'.format(i)
            )

            czy_checked = wystaw_proforme.get_attribute("checked")
            self.assertEqual(czy_checked, None)

            wystaw_proforme.click()

        # Zapisz obiekt
        self.selenium.find_element_by_xpath(
            '//input[@type="submit" and @name="_save"]'
        ).click()

        # Powinien być błąd walidacji
        error = self.wait_for_element_by_xpath(
            '//div[@class="form-row errors '
            'field-wystaw_proforme_automatycznie"][1]'
            '/ul[@class="errorlist"]'
            "/li[1]"
        ).text
        self.assertEqual(
            error, "Uczestnicy tego szkolenia nie mogą otrzymać " "faktury ProForma."
        )

    def test_termin_standardowego_szkolenia_czyproforma(self):
        """
        Uczestnicy zapisani na szkolenie, które ma ustawione "wysylaj_powiadomienia_proformy"
        na True, mają włączone i "checked" pole "wystaw_proforme_automatycznie"
        """
        szkolenie = SzkolenieFactory(wysylaj_powiadomienia_proformy=True)
        termin = TerminSzkoleniaFactory(szkolenie=szkolenie)
        UczestnikFactory.create_batch(4, termin=termin)

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_terminszkolenia_change", args=(termin.id,))
        )
        for i in range(4):
            wystaw_proforme = self.wait_for_element_by_xpath(
                '//input[contains(@id, "id_uczestnik_set-{0}-wystaw_proforme_automatycznie")]'.format(
                    i
                )
            )
            czy_disabled = wystaw_proforme.get_attribute("disabled")
            czy_checked = wystaw_proforme.get_attribute("checked")
            self.assertEqual(czy_disabled, None)
            self.assertEqual(czy_checked, "true")

    def test_termin_egzamin_czy_wyswietla_informacje(self):
        """
        Sprawdza, czy dla terminu szkolenia, które jest niestandardowe
        i ma "wysylaj_powiadomienia_proformy" = False jest wyświetlany tekst z informacją o tym
        """
        info = "Dla tego szkolenia nie można generować automatycznie powiadomień i proform,"
        szkolenie = SzkolenieFactory(wysylaj_powiadomienia_proformy=False)
        termin = TerminSzkoleniaFactory(szkolenie=szkolenie)

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_terminszkolenia_change", args=(termin.id,))
        )

        page_text = self.selenium.find_element_by_tag_name("body").text
        self.assertIn(info, page_text)

    def test_termin_szkolenie_standardowe_czy_niewyswietla_informacji(self):
        """
        Sprawdza, czy dla terminu szkolenia, które jest standardowe
        i ma "wysylaj_powiadomienia_proformy" = True nie ma błędnego tekstu
        """
        info = "Dla tego szkolenia nie można generować automatycznie powiadomień i proform,"
        szkolenie = SzkolenieFactory(wysylaj_powiadomienia_proformy=True)
        termin = TerminSzkoleniaFactory(szkolenie=szkolenie)

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_terminszkolenia_change", args=(termin.id,))
        )

        page_text = self.selenium.find_element_by_tag_name("body").text
        self.assertNotIn(info, page_text)

    def test_rentownosc(self):
        """
        Sprawdzamy wyswietlanie informacji o rentownosci uzytkownikow.
        """

        termin = TerminSzkoleniaFactory()

        # Tworzymy 15 rentownych i 2 nierentownych
        UczestnikFactory.create_batch(15, termin=termin)
        UczestnikFactory.create_batch(2, termin=termin, nierentowny=True)

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_terminszkolenia_changelist")
            + "?id={0}".format(termin.id)
        )

        page_text = self.selenium.find_element_by_tag_name("body").text
        self.assertIn("17 / 15R", page_text)

    def test_rentownosc_z_podzbiorami(self):
        """
        Sprawdzamy wyswietlanie informacji o rentownosci uzytkownikow z
        uwzglednieniem podzbiorow.
        """

        termin = TerminSzkoleniaFactory()

        # Tworzymy 1 rentowny i 2 nierentownych
        UczestnikFactory.create_batch(1, termin=termin)
        UczestnikFactory.create_batch(2, termin=termin, nierentowny=True)

        # Ddadjemy podzbior
        t1 = TerminSzkoleniaFactory(termin_nadrzedny=termin)
        UczestnikFactory.create_batch(3, termin=t1)
        UczestnikFactory.create_batch(4, termin=t1, nierentowny=True)

        t2 = TerminSzkoleniaFactory(termin_nadrzedny=termin)
        UczestnikFactory.create_batch(5, termin=t2)
        UczestnikFactory.create_batch(6, termin=t2, nierentowny=True)

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_terminszkolenia_changelist")
            + "?id={0}".format(termin.id)
        )

        page_text = self.selenium.find_element_by_tag_name("body").text
        self.assertIn("3 / 1R", page_text)
        self.assertIn("{0}: 7 / 3R".format(t1.szkolenie.kod), page_text)
        self.assertIn("{0}: 11 / 5R".format(t2.szkolenie.kod), page_text)

    def test_rentownosc_z_nadzbiorem(self):
        """
        Sprawdzamy wyswietlanie informacji o rentownosci uzytkownikow z
        uwzglednieniem nadzbioru.
        """

        # Ddadjemy podzbior
        t1 = TerminSzkoleniaFactory()
        UczestnikFactory.create_batch(3, termin=t1)
        UczestnikFactory.create_batch(4, termin=t1, nierentowny=True)

        t2 = TerminSzkoleniaFactory(termin_nadrzedny=t1)
        UczestnikFactory.create_batch(5, termin=t2)
        UczestnikFactory.create_batch(6, termin=t2, nierentowny=True)

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_terminszkolenia_changelist")
            + "?id={0}".format(t2.id)
        )

        page_text = self.selenium.find_element_by_tag_name("body").text
        self.assertIn("11 / 5R", page_text)
        self.assertIn("{0}: 7 / 3R".format(t1.szkolenie.kod), page_text)

    def test_autoryzacja(self):
        # Sprawdzamy pojawienie się help-textow przy wyborze szkolen z
        # autoryzacja i bez.

        s1 = SzkolenieFactory.create(autoryzacja=AutoryzacjaFactory.create())
        s2 = SzkolenieFactory.create()

        self.selenium.get(
            self.live_server_url + reverse("admin:www_terminszkolenia_add")
        )

        # Wybieramy szkolenie bez autoryzacji
        self.selenium.find_element_by_id("id_szkolenie").send_keys(s2.pk)
        self.wait_for_element_by_xpath('//p[@class="help help-autoryzacja"]')

        # Powinny byc 4 takie elemeny (1 dla "autoryzacja aktywna" i 3 dla
        # uczestnikow)
        els = self.selenium.find_elements_by_xpath(
            '//p[@class="help help-autoryzacja"]'
        )
        els = [el for el in els if el.is_displayed()]

        self.assertEqual(len(els), 4)

        for el in els:
            self.assertEqual(el.text, "UWAGA: To szkolenie nie ma opcji autoryzacji.")

        # Wybieramu szkolenie z autoryzacja (help-tekst znika)
        self.selenium.find_element_by_id("id_szkolenie").send_keys(s1.pk)

        try:
            self.selenium.find_element_by_xpath('//p[@class="help help-autoryzacja"]')
        except NoSuchElementException:
            # Jest ok, ma nie być tego elementu
            pass

    @unittest.skip("skipped")
    def test_szkolenie_lookup(self):
        # Sprawdzamy link "lupki" przy wyborze szkolenia.

        self.selenium.get(
            self.live_server_url + reverse("admin:www_terminszkolenia_add")
        )

        lookup = self.selenium.find_element_by_id("lookup_id_szkolenie")
        href = lookup.get_attribute("href")

        self.assertIn("aktywne__exact=1", href)

    @unittest.skip("skipped")
    def test_termin_nadrzedny_lookup(self):
        # Sprawdzamy pojawienie się "lupki" przy wyborze terminu
        # nadrzednego (dziala w opraciu o AJAX).

        s1 = SzkolenieFactory.create()
        s2 = SzkolenieFactory.create(szkolenie_nadrzedne=s1)
        lokalizacja = LokalizacjaFactory.create()

        self.selenium.get(
            self.live_server_url + reverse("admin:www_terminszkolenia_add")
        )

        # Brak lupy, nie wybraliśmy danych
        lookup = self.selenium.find_element_by_id("lookup_id_termin_nadrzedny")
        self.assertFalse(lookup.is_displayed())

        # Wybieramy termin, lokalizacje i szkolenie (bez nadrzednego) - brak
        # lupy
        self.selenium.find_element_by_id("id_termin").send_keys(
            datetime.date.today().strftime("%Y-%m-%d")
        )
        self.selenium.find_element_by_id("id_szkolenie").send_keys(s1.pk)
        self.selenium.find_element_by_xpath(
            '//select[@id="id_lokalizacja"]/option[@value="{0}"]'.format(lokalizacja.pk)
        ).click()

        lookup = self.selenium.find_element_by_id("lookup_id_termin_nadrzedny")
        self.assertFalse(lookup.is_displayed())

        # Teraz powinna pojawic się lupa
        self.selenium.find_element_by_id("id_szkolenie").clear()
        self.selenium.find_element_by_id("id_szkolenie").send_keys(s2.pk)

        self.wait_for_element_by_xpath('//a[@id="lookup_id_termin_nadrzedny"]')

        lookup = self.selenium.find_element_by_id("lookup_id_termin_nadrzedny")
        self.assertTrue(lookup.is_displayed())

        href = lookup.get_attribute("href")

        self.assertIn("lokalizacja__pk", href)
        self.assertIn("szkolenie__pk", href)
        self.assertIn("termin__gte", href)

    @unittest.skip("skipped")
    def test_save_as_new(self):
        """
        Sprawdzamy poprawnosc kolonowania obiektow.
        """

        termin = TerminSzkoleniaFactory()

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_terminszkolenia_change", args=(termin.pk,))
        )

        self.selenium.find_element_by_xpath('//input[@name="_saveasnew"]').click()
        self.selenium.switch_to.alert.dismiss()

        # Powinnismy byc caly czas na tym samym szkoleniu
        page_text = self.wait_for_element_by_xpath("//body").text
        self.assertIn(termin.__str__(), page_text)

        self.selenium.find_element_by_id("id_termin").clear()
        self.selenium.find_element_by_id("id_termin").send_keys(
            (termin.termin + datetime.timedelta(days=1)).strftime("%Y-%m-%d")
        )

        self.selenium.find_element_by_xpath('//input[@name="_saveasnew"]').click()
        self.selenium.switch_to.alert.accept()

        # Powinnismy juz byc w edycji nowego szkolenia
        page_text = self.wait_for_element_by_xpath("//body").text
        self.assertNotIn(termin.__str__(), page_text)

    def test_automatyczne_podrzedne_terminy(self):
        """
        Sprawdzamy, czy podczas tworzenia terminu nadrzędnego tworzone są
        terminy podrzędne.
        """

        date = datetime.date(2016, 2, 1)

        # Tworzymy regule dziedziczenia
        s1 = SzkolenieFactory.create(czas_dni=3, zawiera_obiady=False)
        s2 = SzkolenieFactory.create(
            szkolenie_nadrzedne=s1, podzbior_dni="2,3", czas_dni=2, zawiera_obiady=True
        )
        s3 = SzkolenieFactory.create(
            szkolenie_nadrzedne=s1, podzbior_dni="1", zawiera_obiady=False
        )
        location = LokalizacjaFactory.create(domyslna_cena_obiadu=100)

        self.selenium.get(
            self.live_server_url + reverse("admin:www_terminszkolenia_add")
        )

        self.selenium.find_element_by_id("id_szkolenie").send_keys(s1.pk)
        self.selenium.find_element_by_xpath(
            '//select[@id="id_lokalizacja"]/option[@value="{0}"]'.format(location.pk)
        ).click()
        self.selenium.find_element_by_id("id_termin").send_keys(
            date.strftime("%Y-%m-%d"),
        )
        self.selenium.find_element_by_id("id_daty_szczegolowo").send_keys(
            ",".join(
                [
                    date.strftime("%Y-%m-%d"),
                    (date + datetime.timedelta(days=1)).strftime("%Y-%m-%d"),
                    (date + datetime.timedelta(days=2)).strftime("%Y-%m-%d"),
                ]
            )
        )

        # Zapisz obiekt
        self.selenium.find_element_by_xpath(
            '//input[@type="submit" and @name="_save"]'
        ).click()

        # Powinny być dwa dodatkowe terminy
        self.assertTrue(
            TerminSzkolenia.objects.filter(
                lokalizacja=location,
                szkolenie=s2,
                termin=date + datetime.timedelta(days=1),
            ).count()
        )

        self.assertTrue(
            TerminSzkolenia.objects.filter(
                lokalizacja=location, szkolenie=s3, termin=date
            ).count()
        )

        # Sprawdzamy poprawnosc generowania pola z obiadami
        t1 = TerminSzkolenia.objects.get(szkolenie=s1)
        self.assertEqual(t1.obiady, "obiady-opcjonalne")

        t2 = TerminSzkolenia.objects.get(szkolenie=s2)
        self.assertEqual(t2.obiady, "obiady-wliczone")

        t3 = TerminSzkolenia.objects.get(szkolenie=s3)
        self.assertEqual(t3.obiady, "obiady-opcjonalne")

    def test_terminy_podrzedne_jako_promowane(self):
        niezwiazany_termin = TerminSzkoleniaFactory.create(
            termin_nadrzedny=TerminSzkoleniaFactory.create()
        )

        lokalizacja = LokalizacjaFactory.create()
        termin = TerminSzkoleniaFactory(
            odbylo_sie=True,
            sala=SalaFactory.create(lokalizacja=lokalizacja),
            lokalizacja=lokalizacja,
        )
        termin_podrzedny = TerminSzkoleniaFactory.create(termin_nadrzedny=termin)
        self.assertFalse(termin_podrzedny.czy_reklamowac)

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_terminszkolenia_change", args=(termin.pk,))
        )

        # Zapisz obiekt
        self.selenium.find_element_by_xpath(
            '//input[@type="submit" and @name="_save"]'
        ).click()

        termin_podrzedny = TerminSzkolenia.objects.get(pk=termin_podrzedny.pk)
        self.assertTrue(termin_podrzedny.czy_reklamowac)
        self.assertEqual(TerminSzkoleniaLog.objects.count(), 2)
        self.assertEqual(
            TerminSzkoleniaLog.objects.filter(
                term__in=[termin_podrzedny, termin]
            ).count(),
            2,
        )

        self.assertFalse(niezwiazany_termin.czy_reklamowac)

    def test_terminy_podrzedne_jako_promowane_gdy_niepotwierdzony(self):
        lokalizacja = LokalizacjaFactory.create()
        termin = TerminSzkoleniaFactory(
            czy_reklamowac=True,
            sala=SalaFactory.create(lokalizacja=lokalizacja),
            lokalizacja=lokalizacja,
        )
        termin_podrzedny = TerminSzkoleniaFactory.create(termin_nadrzedny=termin)
        self.assertFalse(termin_podrzedny.czy_reklamowac)

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_terminszkolenia_change", args=(termin.pk,))
        )

        # Zapisz obiekt
        self.selenium.find_element_by_xpath(
            '//input[@type="submit" and @name="_save"]'
        ).click()

        termin_podrzedny = TerminSzkolenia.objects.get(pk=termin_podrzedny.pk)
        self.assertTrue(termin_podrzedny.czy_reklamowac)

    @unittest.skip("skipped")
    def test_oznacz_jako_przeszkoleni(self):
        termin = TerminSzkoleniaFactory()
        UczestnikFactory.create(termin=termin, status=1)

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_terminszkolenia_change", args=(termin.pk,))
        )

        self.selenium.find_element_by_id("mark_user_as_confirmed").click()
        alert = self.selenium.switch_to.alert
        self.assertEqual(alert.text, "Liczba zmienionych rekordów: 1")
        alert.accept()

    @unittest.skip("Chwilowo wylaczone")
    def test_button_mailing_do_zainteresowanych_z_innych_miast(self):
        termin = TerminSzkoleniaFactory(odbylo_sie=True)

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_terminszkolenia_change", args=(termin.pk,))
        )

        self.selenium.find_element_by_xpath('//a[@class="run-modal"][1]')

        termin.odbylo_sie = False
        termin.save()

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_terminszkolenia_change", args=(termin.pk,))
        )

        try:
            self.selenium.find_element_by_xpath('//a[@class="run-modal"][1]')
        except NoSuchElementException:
            # Jest ok, ma nie być tego elementu
            pass

        termin.odbylo_sie = True
        termin.termin -= datetime.timedelta(days=365)
        termin.save()

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_terminszkolenia_change", args=(termin.pk,))
        )

        try:
            self.selenium.find_element_by_xpath('//a[@class="run-modal"][1]')
        except NoSuchElementException:
            # Jest ok, ma nie być tego elementu
            pass

    @unittest.skip("Chwilowo wylaczone")
    def test_mailing_do_zainteresowanych_z_innych_miast_brak_odbiorcow(self):
        termin = TerminSzkoleniaFactory(odbylo_sie=True)

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_terminszkolenia_change", args=(termin.pk,))
        )

        self.selenium.find_element_by_xpath('//a[@class="run-modal"][1]').click()

        self.wait_for_element_by_id("subscription-container")
        self.wait_for_element_by_id("no-users")

    @unittest.skip("Chwilowo wylaczone")
    def test_mailing_do_zainteresowanych_z_innych_miast(self):
        termin = TerminSzkoleniaFactory(odbylo_sie=True)
        UserCoursesNotificationFactory.create_batch(5, training=termin.szkolenie)

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_terminszkolenia_change", args=(termin.pk,))
        )

        self.selenium.find_element_by_xpath('//a[@class="run-modal"][1]').click()

        self.wait_for_element_by_id("subscription-container")

        self.selenium.find_element_by_id(
            "id_potencjalnie_zainteresowani_mail"
        ).send_keys("Jakiś test")
        self.selenium.find_element_by_id("subscription-ajax-form-submit").click()

        self.wait_for_element_by_xpath(
            '//li[@class="success"][contains(text(), "Wysyłka została zlecona.")]'
        )

        # Sprawdzamy, czy zapisał się mail i log
        termin = TerminSzkolenia.objects.get(pk=termin.pk)
        self.assertEqual(termin.potencjalnie_zainteresowani_mail, "Jakiś test")

        self.assertTrue(
            TerminSzkoleniaLog.objects.get(
                log_type="potencjalnie_zainteresowani_z_innych_miast", term=termin
            )
        )

        self.assertEqual(len(mail.outbox), 5)

    def test_walidacja_trybu_dzienna(self):
        termin = TerminSzkoleniaFactory(termin=datetime.date(2016, 2, 20), tryb=1)

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_terminszkolenia_change", args=(termin.pk,))
        )
        # Zapisz obiekt
        self.selenium.find_element_by_xpath(
            '//input[@type="submit" and @name="_save"]'
        ).click()
        error = self.selenium.find_element_by_xpath(
            '//div[@class="form-row errors field-tryb"][1]'
            '/ul[@class="errorlist"]'
            "/li[1]"
        ).text
        self.assertEqual(
            error, "Szkolenie rozpoczyna się w weekend, " "więc tryb musi być zaoczny."
        )

        # Poprawny tryb - brak bledow
        termin = TerminSzkoleniaFactory(termin=datetime.date(2016, 2, 20), tryb=3)

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_terminszkolenia_change", args=(termin.pk,))
        )
        # Zapisz obiekt
        self.selenium.find_element_by_xpath(
            '//input[@type="submit" and @name="_save"]'
        ).click()

        try:
            self.selenium.find_element_by_xpath(
                '//div[@class="form-row errors field-tryb"][1]'
                '/ul[@class="errorlist"]'
                "/li[1]"
            )
        except NoSuchElementException:
            # Jest ok, ma nie być tego elementu
            pass
        else:
            self.fail("Błędna walidacja.")

    def test_walidacja_trybu_zaoczna(self):
        termin = TerminSzkoleniaFactory(termin=datetime.date(2019, 12, 6), tryb=3)

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_terminszkolenia_change", args=(termin.pk,))
        )
        # Zapisz obiekt
        self.selenium.find_element_by_xpath(
            '//input[@type="submit" and @name="_save"]'
        ).click()
        error = self.selenium.find_element_by_xpath(
            '//div[@class="form-row errors field-tryb"][1]'
            '/ul[@class="errorlist"]'
            "/li[1]"
        ).text
        self.assertEqual(error, "Szkolenie zaoczne musi rozpoczynać się w weekend.")

        # Poprawny tryb - brak bledow
        termin = TerminSzkoleniaFactory(termin=datetime.date(2019, 12, 7), tryb=3)

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_terminszkolenia_change", args=(termin.pk,))
        )
        # Zapisz obiekt
        self.selenium.find_element_by_xpath(
            '//input[@type="submit" and @name="_save"]'
        ).click()

        try:
            self.selenium.find_element_by_xpath(
                '//div[@class="form-row errors field-tryb"][1]'
                '/ul[@class="errorlist"]'
                "/li[1]"
            )
        except NoSuchElementException:
            # Jest ok, ma nie być tego elementu
            pass
        else:
            self.fail("Błędna walidacja.")

    def test_wylacz_promowanie_podrzednych_terminow(self):
        niezwiazany_termin = TerminSzkoleniaFactory.create(
            termin_nadrzedny=TerminSzkoleniaFactory.create(), zamkniete=False
        )

        lokalizacja = LokalizacjaFactory.create()
        termin = TerminSzkoleniaFactory(
            odbylo_sie=True,
            sala=SalaFactory.create(lokalizacja=lokalizacja),
            lokalizacja=lokalizacja,
        )
        termin_podrzedny = TerminSzkoleniaFactory.create(termin_nadrzedny=termin)
        self.assertFalse(termin_podrzedny.zamkniete)

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_terminszkolenia_change", args=(termin.pk,))
        )

        # Wyłącz wyświetalnie terminu nadrzędnego
        self.selenium.find_element_by_id("id_zamkniete").click()

        # Zapisz obiekt
        self.selenium.find_element_by_xpath(
            '//input[@type="submit" and @name="_save"]'
        ).click()

        termin_podrzedny = TerminSzkolenia.objects.get(pk=termin_podrzedny.pk)
        self.assertTrue(termin_podrzedny.zamkniete)

        self.assertFalse(niezwiazany_termin.zamkniete)

    def test_wylacz_promowanie_glownego_terminu_ale_nie_podrzednych(self):
        lokalizacja = LokalizacjaFactory.create()
        termin = TerminSzkoleniaFactory(
            odbylo_sie=True,
            sala=SalaFactory.create(lokalizacja=lokalizacja),
            lokalizacja=lokalizacja,
        )
        termin_podrzedny = TerminSzkoleniaFactory.create(termin_nadrzedny=termin)
        self.assertFalse(termin_podrzedny.zamkniete)

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_terminszkolenia_change", args=(termin.pk,))
        )

        # Wyłącz wyświetalnie terminu nadrzędnego
        self.selenium.find_element_by_id("id_zamkniete").click()

        self.wait_for_element_by_xpath('//input[@id="id_wylacz_podrzedne"]').click()

        # Zapisz obiekt
        self.selenium.find_element_by_xpath(
            '//input[@type="submit" and @name="_save"]'
        ).click()

        termin_podrzedny = TerminSzkolenia.objects.get(pk=termin_podrzedny.pk)
        self.assertFalse(termin_podrzedny.zamkniete)


class TagTechnologiaAdminLiveServerTestCase(ALXLiveServerTestCase):
    def setUp(self):
        super().setUp()

        self.admin_login()

    def test_dodaj_testimonial_tag_techowi(self):
        """
        Testuje czy dla tagtechnologia doda się testimonial
        """
        tagtechnologia = TagTechnologiaFactory()
        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_tagtechnologia_change", args=(tagtechnologia.id,))
        )
        content = self.wait_for_element_by_xpath(
            '//textarea[@id="id_testimonial_set-0-content"]'
        )
        content.send_keys("Wielce pozytywny testimonial." * 10)
        self.wait_for_element_by_xpath(
            '//input[@id="id_testimonial_set-0-imie_nazwisko"]'
        ).send_keys("nazwisko to nie wszystko")
        self.wait_for_element_by_xpath(
            '//input[@id="id_testimonial_set-0-firma"]'
        ).send_keys("Byle znana firma")
        self.wait_for_element_by_xpath('//input[@id="id_testimonial_set-0-obrazek"]')
        date_today = datetime.date.today()
        self.wait_for_element_by_xpath(
            '//input[@id="id_testimonial_set-0-data_testimoniala"]'
        ).send_keys("{0}".format(date_today))
        self.selenium.find_element_by_xpath(
            '//input[@type="submit"][@value="Zapisz i kontynuuj edycję"]'
        ).click()

        # pn self.wait_for_element_by_xpath('//li[@class="success"][contains(text(), "zostały zapisane")]')
        self.wait_for_element_by_xpath(
            '//li[@class="success"][contains(., "został pomyślnie zmieniony")]'
        )

        testimonial = Testimonial.objects.filter(
            tagtechnologia=tagtechnologia
        ).order_by("-id")[0]
        self.assertEqual(testimonial.content, "Wielce pozytywny testimonial." * 10)

    def test_dodaj_uwagi_internal_i_zrodlo_opinii_testimonialowi(self):
        """
        Sprawdza czy dodane później pola uwagi_internal i id_opinii_zrodlowej dzialają prawidłowo.
        """
        tagtechnologia = TagTechnologiaFactory()

        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_tagtechnologia_change", args=(tagtechnologia.id,))
        )
        content = self.wait_for_element_by_xpath(
            '//textarea[@id="id_testimonial_set-0-content"]'
        )
        content.send_keys("Drugi wielce pozytywny testimonial." * 10)

        # Czy poprawnie dodaje się komentarz z komentarzy sprawdzane jest jeżeli istnieją komentarze w bazie.
        ankieta = AnkietaFactory.create()
        KomentarzFactory.create_batch(10, log__ankieta=ankieta)

        self.selenium.find_element_by_xpath(
            '//a[@href="/admin/ankiety/komentarz/?{0}=id"]'.format(TO_FIELD_VAR)
        ).click()
        self.selenium.switch_to.window(self.selenium.window_handles[-1])
        element = self.wait_for_element_by_xpath(
            '//a[contains(@href, "/admin/ankiety/komentarz/")][1]'
        )
        komentarz_id = element.get_attribute("href").split("/")[-3]
        element.click()

        self.selenium.switch_to.window(self.selenium.window_handles[0])
        self.wait_for_element_by_xpath(
            '//textarea[contains(@id, "uwagi_internal")]'
        ).send_keys("Uwagi internal takietam")
        self.selenium.find_element_by_xpath(
            '//input[@type="submit"][@value="Zapisz i kontynuuj edycję"]'
        ).click()

        # pn self.wait_for_element_by_xpath('//li[@class="success"][contains(text(), "zostały zapisane")]')
        self.wait_for_element_by_xpath(
            '//li[@class="success"][contains(., "został pomyślnie zmieniony")]'
        )
        self.wait_for_element_by_xpath(
            '//textarea[contains(@id, "uwagi_internal")][contains(text(), "Uwagi internal takietam")]'
        )
        self.wait_for_element_by_xpath(
            '//input[contains(@id, "id_opinii_zrodlowej")][@value="{0}"]'.format(
                komentarz_id
            )
        )

    def test_highlight_placement_linkuje_do_highlighta(self):
        """
        Sprawdza, czy formularz inline highlight placementu linkuje do highlighta.
        """
        tagtechnologia = TagTechnologiaFactory()
        placement = HighlightPlacementFactory(page=tagtechnologia)
        self.selenium.get(
            self.live_server_url
            + reverse("admin:www_tagtechnologia_change", args=(tagtechnologia.id,))
        )
        links = self.selenium.find_elements_by_xpath(
            '//a[@href="{}"]'.format(
                reverse("admin:www_highlight_change", args=(placement.highlight.id,))
            )
        )
        self.assertTrue(links)
        link = links[0]
        self.assertTrue(link.is_displayed())


#############
# Szkolenia
#############


class SzkolenieAdminTestCase(AdminTestCase):
    def test_szkolenie_egzamin_czy_wyswietla_informacje(self):
        """
        Sprawdza, czy dla szkolenia, które jest niestandardowe i ma
        "wysylaj_powiadomienia_proformy" = False jest
        wyświetlany tekst z informacją o tym
        """

        info = (
            "Dla tego szkolenia nie można generować automatycznie "
            "powiadomień i proform,"
        )
        szkolenie = SzkolenieFactory(wysylaj_powiadomienia_proformy=False)

        resp = self.client.get(
            reverse("admin:www_szkolenie_change", args=(szkolenie.id,))
        )
        self.assertContains(resp, info)

    def test_szkolenie_standardowe_czy_niewyswietla_informacji(self):
        """
        Sprawdza, czy dla szkolenia, które jest standardowe i ma
        "wysylaj_powiadomienia_proformy" = True nie ma błędnego tekstu
        """

        info = (
            "Dla tego szkolenia nie można generować automatycznie "
            "powiadomień i proform,"
        )
        szkolenie = SzkolenieFactory(wysylaj_powiadomienia_proformy=True)

        resp = self.client.get(
            reverse("admin:www_szkolenie_change", args=(szkolenie.id,))
        )
        self.assertNotContains(resp, info)

    def test_z_wylaczona_opcja_synchronizacji_pol_po_dziedziczeniu(self):
        base = SzkolenieFactory.create(language="en")

        t = SzkolenieFactory.create()
        t.base_translation_id = base.pk
        t.save()

        data = {}

        for key, value in list(t.__dict__.items()):
            if value:
                data[key] = value

        data.update(
            {
                "tag_dlugosc": t.tag_dlugosc.pk,
                "tag_zawod": t.tag_zawod.pk,
                "tagi_technologia": [tag.pk for tag in t.tagi_technologia.all()],
                "waluta": t.waluta.pk,
                "materialy_plik": "",
                "kod_autoryzacji": "12345",
                "tekstoterminach_set-TOTAL_FORMS": "0",
                "tekstoterminach_set-INITIAL_FORMS": "0",
                "warianty_szkolenia-TOTAL_FORMS": "0",
                "warianty_szkolenia-INITIAL_FORMS": "0",
            }
        )

        resp = self.client.post(
            reverse("admin:www_szkolenie_change", args=(t.id,)), data=data, follow=True
        )

        # pn
        # self.assertContains(
        #     resp,
        #     "Szkolenie &quot;{0}&quot; zostało pomyślnie "
        #     "zmienione.".format(t.__str__())
        # )
        self.assertContains(resp, "został pomyślnie zmieniony")


###########################
# Certyfikaty i Absolwenci
###########################


@override_settings(
    task_eager_propagates=True, task_always_eager=True, BROKER_BACKEND="memory"
)
class GraduateAdminTestCase(AdminTestCase):
    def test_search_query(self):
        # Tworzymy kilka obiektów
        GraduateFactory.create_batch(10)

        # Często jest tak, ze błędne pola w `search_fields` powoduja błąd 500.
        resp = self.client.get(
            "{0}?q=gęś".format(reverse("admin:www_graduate_changelist"))
        )
        self.assertContains(resp, "0 wyników")

    def test_graduate_display_certificate_link(self):
        obj = GraduateFactory.create()

        resp = self.client.get(
            "{0}?id={1}".format(reverse("admin:www_graduate_changelist"), obj.pk)
        )

        self.assertEqual(resp.status_code, 200)

        # W treści powinno być info o certyfikacie
        url = reverse("admin:graduate_generate_certificate", args=(obj.pk,))
        self.assertIn(url, resp.content.decode())

        # Nie powinno być daty wysyłki maila
        self.assertNotIn("Email wysłano: ", resp.content.decode())

        # Wysyłamy mail
        self.client.get(url)

        resp = self.client.get(
            "{0}?id={1}".format(reverse("admin:www_graduate_changelist"), obj.pk)
        )

        self.assertNotIn(url, resp.content.decode())
        self.assertIn("Email wysłano: ", resp.content.decode())

    def test_display_printable_certificate_link(self):
        obj = TerminSzkoleniaFactory(
            termin=datetime.date.today() - datetime.timedelta(days=5),
        )

        resp = self.client.get(
            "{0}?id={1}".format(reverse("admin:www_terminszkolenia_changelist"), obj.pk)
        )

        self.assertEqual(resp.status_code, 200)

        # Nie powinno być linku podglądu certyfikatów - nie
        # spełniony warunek z `allow_to_generate_certificates`
        preview_url = reverse("admin:printable_certificates", args=(obj.pk, "new"))
        self.assertNotIn(preview_url.encode("utf-8"), resp.content)

        # Oznaczamy termin szkolenia jako zakończone
        obj.odbylo_sie = True
        obj.save()

        resp = self.client.get(
            "{0}?id={1}".format(reverse("admin:www_terminszkolenia_changelist"), obj.pk)
        )

        self.assertIn(preview_url, resp.content.decode())

        # Oznaczamy, że certyfikaty już zostały wysłane
        obj.notyfikacje_o_certyfikatach = datetime.datetime.now()
        obj.save()

        resp = self.client.get(
            "{0}?id={1}".format(
                reverse("admin:www_terminszkolenia_changelist"),
                obj.pk,
            )
        )

        # Link do podglądu powinien być dalej
        self.assertIn(preview_url, resp.content.decode())

    def test_printable_certificates(self):
        obj = TerminSzkoleniaFactory(
            termin=datetime.date.today() - datetime.timedelta(days=5),
            odbylo_sie=True,
        )

        u1 = UczestnikFactory(termin=obj)
        u2 = UczestnikFactory(termin=obj)

        resp = self.client.get(
            reverse("admin:printable_certificates", args=(obj.pk, "old")),
            follow=True,
        )

        self.assertTrue(resp.content)


###################
# Powiadomienia
###################


class UserNotificationAdminTestCase(AdminTestCase):
    def test_search_query(self):
        # Tworzymy kilka obiektów
        UserCoursesNotificationFactory.create_batch(10)

        # Często jest tak, ze błędne pola w `search_fields` powoduja błąd 500.
        resp = self.client.get(
            "{0}?q=gęś".format(reverse("admin:www_usernotification_changelist"))
        )

        self.assertContains(resp, "0 wyników")

    def test_search_query_by_kod(self):
        # Testujemy wyszukiwanie po kodzie szkolenia (kod__iexact=FRAZA).

        UserCoursesNotificationFactory.create_batch(
            5, training=SzkolenieFactory.create(kod="Access 404")
        )
        UserCoursesNotificationFactory.create_batch(
            10, training=SzkolenieFactory.create(kod="Access")
        )
        UserCoursesNotificationFactory.create_batch(
            20, training=SzkolenieFactory.create(kod="LC-PHP")
        )

        # Szukamy "Access 404"
        resp = self.client.get(
            "{0}?q=Access 404".format(reverse("admin:www_usernotification_changelist"))
        )

        self.assertContains(resp, "5 wyników")

        # Szukamy "access"
        resp = self.client.get(
            "{0}?q=Access".format(reverse("admin:www_usernotification_changelist"))
        )

        self.assertContains(resp, "10 wyników")

        # Szukamy "LC-PHP"
        resp = self.client.get(
            "{0}?q=lc-php".format(reverse("admin:www_usernotification_changelist"))
        )

        self.assertContains(resp, "20 wyników")

    def test_preferences_list(self):
        """
        Sprawdzamy, czy poprawnie są wyświetlane szkolenia i lokalizacje
        użytkowników.
        """

        # Dodajemy użytkownika oraz dwa szkolenia w dwóch lokalizacjach.

        user = UserNotificationFactory.create()

        l1 = LokalizacjaFactory.create()
        l2 = LokalizacjaFactory.create()
        l3 = LokalizacjaFactory.create()

        c1 = UserCoursesNotificationFactory.create(user=user, locations=[l1, l2])

        c2 = UserCoursesNotificationFactory.create(user=user, locations=[l3])

        resp = self.client.get(
            "{0}?id={1}".format(
                reverse("admin:www_usernotification_changelist"), user.pk
            )
        )

        self.assertContains(resp, c1.training.nazwa)
        self.assertContains(resp, c2.training.nazwa)
        self.assertContains(resp, "{0}, {1}".format(l1.shortname, l2.shortname))
        self.assertContains(resp, l3.shortname)

    def test_staff_edits(self):
        """
        Sprawdzamy, czy poprawnie są wyświetlane informacje o osobie
        dokujnącej modyfikacji obiektu.
        """

        self.client.post(
            reverse("admin:www_usernotification_add"),
            data={
                "email": "<EMAIL>",
                "status": "1",
                "language": "pl",
                "usernotificationlog_set-TOTAL_FORMS": "0",
                "usernotificationlog_set-INITIAL_FORMS": "0",
                "preferences-TOTAL_FORMS": "0",
                "preferences-INITIAL_FORMS": "0",
            },
        )

        obj = UserNotification.objects.get(email="<EMAIL>")

        resp = self.client.get(
            "{0}?id={1}".format(
                reverse("admin:www_usernotification_changelist"), obj.pk
            )
        )

        self.assertContains(resp, self.admin_user.last_name)
        self.assertContains(resp, obj.staff_updated_at.strftime("%Y-%m-%d %H:%M:%S"))

    def test_save_model_method(self):
        """
        Sprawdzamy, czy:

          - podczas dodawania nowego obiektu wartość "source" jest poprawnie
            ustawiana
          - podczas dodawania lub edycji obiektu wartości "staff_*" są poprawnie
            ustawiane
          - podczas dodawania lub edycji obiektów inlines wartości "staff_*"
            są poprawnie ustawiane
        """

        # Dodajemy użytkownika.
        user = UserNotificationFactory.create()

        # Najpierw sprawdzamy, czy edytując istaniejący obiekt, jego source
        # nie ulegnie zmianie.

        data = dict(
            [(k, v if v is not None else "") for (k, v) in user.__dict__.items()]
        )
        data.update(
            {
                "usernotificationlog_set-TOTAL_FORMS": "0",
                "usernotificationlog_set-INITIAL_FORMS": "0",
                "preferences-TOTAL_FORMS": "0",
                "preferences-INITIAL_FORMS": "0",
            }
        )

        resp = self.client.post(
            reverse("admin:www_usernotification_change", args=(user.pk,)),
            data=data,
            follow=True,
        )

        # Sprawdzamy, czy jest informacja o poprawnym zapisaniu obiektu
        # pn
        # self.assertContains(
        #     resp,
        #     'Powiadomienie o szkoleniu &quot;{0}&quot; zostało pomyślnie '
        #     'zmienione.'.format(user.email)
        # )
        self.assertContains(resp, "został pomyślnie zmieniony")

        obj = UserNotification.objects.get(pk=user.pk)

        # Sprawdź, czy pole source jest jako "www"
        self.assertEqual(obj.source, "www")

        # Sprawdź, czy pola "staff_*" są puste (nie dokonaliśmy żdanej edycji).
        self.assertFalse(obj.staff_updated_at)
        self.assertFalse(obj.staff_updated_by)

        # Zmień coś w danych
        data["company_name"] = "Moja Firma!"

        self.client.post(
            reverse("admin:www_usernotification_change", args=(user.pk,)),
            data=data,
            follow=True,
        )

        obj = UserNotification.objects.get(pk=user.pk)

        # Sprawdź, czy pole source jest jako "www"
        self.assertEqual(obj.source, "www")
        # Pola "staff_*" powinny być uzupełnione
        self.assertTrue(obj.staff_updated_at)
        self.assertTrue(obj.staff_updated_by)

        # Teraz dodajemy nowy obiekt i pole "source" powinno zostać ustawione
        # na "staff" oraz powinny zostać uzupełnione pola "staff_*".

        self.client.post(
            reverse("admin:www_usernotification_add"),
            data={
                "email": "<EMAIL>",
                "status": "1",
                "language": "pl",
                "usernotificationlog_set-TOTAL_FORMS": "0",
                "usernotificationlog_set-INITIAL_FORMS": "0",
                "preferences-TOTAL_FORMS": "0",
                "preferences-INITIAL_FORMS": "0",
            },
            follow=True,
        )

        obj = UserNotification.objects.get(email="<EMAIL>")

        # Sprawdź, czy pole source jest jako "staff"
        self.assertEqual(obj.source, "staff")
        self.assertTrue(obj.staff_updated_at)
        self.assertTrue(obj.staff_updated_by)

        # Teraz dodajemy obiekt z kursami i usuwamy kurs (inlines).
        # Pola "staff_*" powinny być uzupłenione.

        user = UserNotificationFactory.create()

        p1 = UserCoursesNotificationFactory.create(
            user=user, locations=[LokalizacjaFactory.create()]
        )

        p2 = UserCoursesNotificationFactory.create(
            user=user, locations=[LokalizacjaFactory.create()]
        )

        data = dict(
            [(k, v if v is not None else "") for (k, v) in user.__dict__.items()]
        )
        data.update(
            {
                "usernotificationlog_set-TOTAL_FORMS": "0",
                "usernotificationlog_set-INITIAL_FORMS": "0",
                "preferences-TOTAL_FORMS": "2",
                "preferences-INITIAL_FORMS": "2",
                "preferences-1-id": p1.pk,
                "preferences-1-training": p1.training.pk,
                "preferences-1-locations": [l.pk for l in p1.locations.all()],
                "preferences-0-id": p2.pk,
                "preferences-0-training": p2.training.pk,
                "preferences-0-locations": [l.pk for l in p2.locations.all()],
                "preferences-0-DELETE": True,
            }
        )

        self.client.post(
            reverse("admin:www_usernotification_change", args=(user.pk,)), data=data
        )

        obj = UserNotification.objects.get(pk=user.pk)

        self.assertTrue(obj.staff_updated_at)
        self.assertTrue(obj.staff_updated_by)

    def test_lista_z_terminami_i_bez(self):
        SzkolenieFactory.create_batch(2)

        # Brak terminow
        resp = self.client.get(reverse("admin:www_szkolenie_changelist"))
        self.assertNotContains(resp, "Terminy OK")

        # Z terminami
        resp = self.client.get(
            reverse("admin:www_szkolenie_changelist") + "?list_display_extened=1"
        )
        self.assertContains(resp, "Terminy OK")


###########
# Assets
###########


class AssetAdminTestCase(AdminTestCase):
    def test_search_bar(self):
        # Sprawdzamy, czy nie ma błędu (500) podczas wyszukiwania

        AssetFactory.create_batch(10)

        resp = self.client.get(
            "{0}?q=ółążźń".format(reverse("admin:www_asset_changelist"))
        )
        self.assertEqual(resp.status_code, 200)

    def test_media_url(self):
        asset = AssetFactory.create()

        resp = self.client.get(reverse("admin:www_asset_changelist"))
        self.assertIn(asset.file.url, resp.content.decode())

    def test_adding_author(self):
        # Sprawdzamy, czy podczas dodawania zapisywany jest autor pliku.

        f = SimpleUploadedFile("sample.txt", b"tekst 1")

        resp = self.client.post(
            reverse("admin:www_asset_add"),
            data={
                "file": f,
            },
            files={
                "file": f,
            },
            follow=True,
        )

        self.assertIn("Jan Pawłowski".encode("utf-8"), resp.content)


###########
# Locking
###########


class LockingAdminTestCase(AdminTestCase):
    def test_cache_key(self):
        self.assertEqual(cache_key(1, 2, 3), "admin-locking-1-2-3")

    def test_locked(self):
        uczestnik = UczestnikFactory.create()

        self.assertFalse(locked(uczestnik, self.admin_user.pk))
        self.assertFalse(locked(uczestnik, UserFactory.create().pk))

        cache.set(cache_key("www", "uczestnik", uczestnik.pk), self.admin_user.pk, 30)

        self.assertFalse(locked(uczestnik, self.admin_user.pk))
        self.assertTrue(locked(uczestnik, UserFactory.create().pk))
