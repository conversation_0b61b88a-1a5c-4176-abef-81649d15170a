from django.test import TestCase
from www.models import MyFlatPage
from i18n.models import WersjaJezykowa, Panstwo


class TechRoutesTest(TestCase):
    def setUp(self):
        # Minimal i18n setup required by context processors
        pl = WersjaJezykowa.objects.create(nazwa="Polski", kod_jezyka="pl")
        en = WersjaJezykowa.objects.create(nazwa="English", kod_jezyka="en")
        pl_country = Panstwo.objects.create(nazwa="Polska")
        en_country = Panstwo.objects.create(nazwa="United Kingdom")
        pl.wyswietlane_panstwa.add(pl_country)
        en.wyswietlane_panstwa.add(en_country)

        # Minimal pages required by common_context_processor during 404 rendering
        for lang in ("pl", "en"):
            MyFlatPage.objects.create(header_title="Footer", slug="footer", language=lang, enabled=True)
            MyFlatPage.objects.create(header_title="Menu", slug="menu", language=lang, enabled=True)

    def test_nonexistent_pl_tech_returns_404(self):
        resp = self.client.get('/pl/tech/nonexistent-slug/', follow=False)
        self.assertEqual(resp.status_code, 404)

    def test_nonexistent_tech_redirects_once_then_404(self):
        resp = self.client.get('/tech/nonexistent-slug/', follow=True)
        # Ensure final status is 404
        self.assertEqual(resp.status_code, 404)
        # Ensure at most one redirect and no loop
        redirects = [r[0] for r in resp.redirect_chain]
        self.assertLessEqual(len(redirects), 1)
        if redirects:
            self.assertEqual(redirects[0], '/pl/tech/nonexistent-slug/')

    def test_bare_pl_tech_returns_404(self):
        resp = self.client.get('/pl/tech/', follow=False)
        self.assertEqual(resp.status_code, 404)

    def test_bare_tech_returns_404(self):
        resp = self.client.get('/tech/', follow=True)
        self.assertEqual(resp.status_code, 404)
        redirects = [r[0] for r in resp.redirect_chain]
        self.assertLessEqual(len(redirects), 1)
        if redirects:
            self.assertEqual(redirects[0], '/pl/tech/')
