from django.core import mail

from www.testfactories import TerminSzkoleniaFactory, UczestnikFactory, UserFactory
from www.testhelpers import ALXTestCase

from ..utils import send_zgloszenie_mail


class SendZgloszenieMailTestCase(ALXTestCase):
    def test_when_prywatny(self):
        uczestnik = UczestnikFactory.create(
            imie_nazwisko="Imię Nazwisko", prywatny=True, termin__szkolenie__kod="KOD!"
        )
        send_zgloszenie_mail(uczestnik)

        self.assertEqual(len(mail.outbox), 1)

        subject = mail.outbox[0].subject
        body = mail.outbox[0].body

        self.assertIn("Zgłoszenie na KOD!", subject)
        self.assertIn(" [1/1R] I<PERSON><PERSON>", subject)

        self.assertIn("W tej chwili w sumie zgłoszonych osób: 1 / 1R", body)
        self.assertIn("<PERSON><PERSON><PERSON>wi<PERSON>", body)
        self.assertIn("Byłem wcześniej: nie", body)
        self.assertIn("Użytkownik zapisany na powiadomienie: nie", body)

    def test_prywatny_i_hyb_zdalny(self):
        termin = TerminSzkoleniaFactory.create(lokalizacja__shortname="abc")
        termin_zdalny = TerminSzkoleniaFactory.create(
            lokalizacja__shortname="zdalnie",
            szkolenie=termin.szkolenie,
            termin=termin.termin,
        )
        termin.termin_zdalny = termin_zdalny
        termin.save()

        uczestnik = UczestnikFactory.create(prywatny=True, termin=termin)
        UczestnikFactory.create(prywatny=True, termin=termin_zdalny)
        UczestnikFactory.create(prywatny=True, termin=termin_zdalny)

        send_zgloszenie_mail(uczestnik)

        self.assertEqual(len(mail.outbox), 1)

        subject = mail.outbox[0].subject
        body = mail.outbox[0].body

        self.assertIn(" [3/3R] ", subject)
        self.assertIn(
            "W tej chwili w sumie zgłoszonych osób: zdalnie: 2 / 2R, "
            "stacjonarnie: 1 / 1R, razem: 3 / 3R",
            body,
        )

    def test_prywatny_i_hyb_stacjonarny(self):
        termin = TerminSzkoleniaFactory.create(lokalizacja__shortname="zdalnie")
        termin_stacjonarny = TerminSzkoleniaFactory.create(
            szkolenie=termin.szkolenie,
            termin=termin.termin,
            termin_zdalny=termin,
        )

        uczestnik = UczestnikFactory.create(prywatny=True, termin=termin)
        UczestnikFactory.create(prywatny=True, termin=termin_stacjonarny)
        UczestnikFactory.create(prywatny=True, termin=termin)

        send_zgloszenie_mail(uczestnik)

        self.assertEqual(len(mail.outbox), 1)

        subject = mail.outbox[0].subject
        body = mail.outbox[0].body

        self.assertIn(" [3/3R] ", subject)
        self.assertIn(
            "W tej chwili w sumie zgłoszonych osób: zdalnie: 2 / 2R, "
            "stacjonarnie: 1 / 1R, razem: 3 / 3R",
            body,
        )

    def test_when_prywatny_i_faktoryzacja(self):
        uczestnik = UczestnikFactory.create(
            imie_nazwisko="Imię Nazwisko", prywatny=True, termin__szkolenie__kod="KOD!"
        )

        termin_zdalny = TerminSzkoleniaFactory.create(lokalizacja__shortname="zdalnie")
        termin_podrzedny = TerminSzkoleniaFactory.create(
            szkolenie__nazwa="Szkolenie podrzędne",
            termin_zdalny=termin_zdalny,
            termin_nadrzedny=uczestnik.termin,
        )
        UczestnikFactory.create_batch(1, termin=termin_podrzedny)
        UczestnikFactory.create_batch(1, termin=termin_podrzedny, nierentowny=True)

        send_zgloszenie_mail(uczestnik)

        self.assertEqual(len(mail.outbox), 1)

        subject = mail.outbox[0].subject
        body = mail.outbox[0].body

        self.assertIn("Zgłoszenie na KOD!", subject)
        self.assertIn(" [1/1R] Imię Nazwisko", subject)

        self.assertIn("W tej chwili w sumie zgłoszonych osób: 1 / 1R", body)
        self.assertIn("Imię Nazwisko", body)
        self.assertIn("Byłem wcześniej: nie", body)
        self.assertIn("Użytkownik zapisany na powiadomienie: nie", body)
        self.assertIn(
            "Szkolenie podrzędne: zdalnie: 0 / 0R, stacjonarnie: 2 / 1R, razem: 2 / 1R",
            body,
        )

    def test_when_prywatny_i_faktoryzacja_nadzbior(self):
        termin_nadrzedny = TerminSzkoleniaFactory.create(
            szkolenie__nazwa="Szkolenie nadrzędne",
        )
        UczestnikFactory.create_batch(1, termin=termin_nadrzedny)
        UczestnikFactory.create_batch(1, termin=termin_nadrzedny, nierentowny=True)

        uczestnik = UczestnikFactory.create(
            imie_nazwisko="Imię Nazwisko",
            prywatny=False,
            termin__termin_nadrzedny=termin_nadrzedny,
        )

        send_zgloszenie_mail(uczestnik)

        self.assertEqual(len(mail.outbox), 1)

        body = mail.outbox[0].body

        self.assertIn("Szkolenie nadrzędne: 2 / 1R", body)

    def test_when_prywatny_and_extra_prefix(self):
        uczestnik = UczestnikFactory.create(prywatny=True)

        send_zgloszenie_mail(uczestnik, extra_prefix="P")

        self.assertEqual(len(mail.outbox), 1)

        self.assertIn("[1/1R] [P] ", mail.outbox[0].subject)

    def test_when_firmowy(self):
        uczestnik = UczestnikFactory.create()
        send_zgloszenie_mail(uczestnik)

        self.assertEqual(len(mail.outbox), 1)
        self.assertIn(" [1/1R] [F1] ", mail.outbox[0].subject)

    def test_when_firmowy_and_extra_prefix(self):
        uczestnik = UczestnikFactory.create()
        send_zgloszenie_mail(uczestnik, extra_prefix="P")

        self.assertEqual(len(mail.outbox), 1)
        self.assertIn(" [1/1R] [F1P] ", mail.outbox[0].subject)

    def test_when_termin_jest_promowany(self):
        uczestnik = UczestnikFactory.create(termin__czy_reklamowac=True)
        send_zgloszenie_mail(uczestnik, extra_prefix="P")

        self.assertEqual(len(mail.outbox), 1)
        self.assertIn("Termin jest promowany.", mail.outbox[0].body)

    def test_when_termin_jest_uruchomiony(self):
        termin = TerminSzkoleniaFactory.create(
            czy_reklamowac=True, odbylo_sie=True, jobs_state="2012-01-01T12:12:00"
        )
        uczestnik = UczestnikFactory.create(termin=termin)
        send_zgloszenie_mail(uczestnik, extra_prefix="P")

        self.assertEqual(len(mail.outbox), 1)
        self.assertNotIn("Termin jest promowany.", mail.outbox[0].body)
        self.assertIn(
            "Termin już potwierdzony (potwierdzono 2012-01-01 12:12).",
            mail.outbox[0].body,
        )


    def test_when_termin_jest_gwarantowany(self):
        termin = TerminSzkoleniaFactory.create(
            odbylo_sie=False, gwarantowany=True, jobs_state="2012-01-01T12:12:00"
        )
        uczestnik = UczestnikFactory.create(termin=termin)
        send_zgloszenie_mail(uczestnik, extra_prefix="P")

        self.assertEqual(len(mail.outbox), 1)

        mail_content = mail.outbox[0].body

        self.assertNotIn(
            "Termin już potwierdzony (potwierdzono 2012-01-01 12:12).",
            mail_content
        )
        self.assertIn("Termin jest gwarantowany", mail_content)


    def test_when_nierentowny(self):
        uczestnik = UczestnikFactory.create(nierentowny=True)
        send_zgloszenie_mail(uczestnik, extra_prefix="P")

        self.assertEqual(len(mail.outbox), 1)
        self.assertIn(
            "Ten uczestnik jest oznaczony jako nierentowny.", mail.outbox[0].body
        )

    def test_when_prywatny_and_closed_registration(self):
        uczestnik = UczestnikFactory.create(
            prywatny=True,
            imie_nazwisko="Imię Nazwisko",
            termin__prywatna_rejestracja=True,
        )
        send_zgloszenie_mail(uczestnik)

        self.assertEqual(len(mail.outbox), 1)

        subject = mail.outbox[0].subject

        self.assertIn(" [1/1R] [Z] Imię Nazwisko", subject)

    def test_when_prywatny_and_closed_registration_and_extra_prefix(self):
        uczestnik = UczestnikFactory.create(
            prywatny=True, termin__prywatna_rejestracja=True
        )

        send_zgloszenie_mail(uczestnik, extra_prefix="P")

        self.assertEqual(len(mail.outbox), 1)

        self.assertIn("[1/1R] [P] [Z] ", mail.outbox[0].subject)

    def test_when_firmowy_and_closed_registration(self):
        uczestnik = UczestnikFactory.create(termin__prywatna_rejestracja=True)
        send_zgloszenie_mail(uczestnik)

        self.assertEqual(len(mail.outbox), 1)
        self.assertIn(" [1/1R] [F1Z] ", mail.outbox[0].subject)

    def test_when_firmowy_and_closed_registration_and_extra_prefix(self):
        uczestnik = UczestnikFactory.create(termin__prywatna_rejestracja=True)
        send_zgloszenie_mail(uczestnik, extra_prefix="P")

        self.assertEqual(len(mail.outbox), 1)
        self.assertIn(" [1/1R] [F1ZP] ", mail.outbox[0].subject)

    def test_other_prefixes(self):
        uczestnik = UczestnikFactory.create(prywatny=True)
        send_zgloszenie_mail(uczestnik, extra_prefix="ABC")

        self.assertEqual(len(mail.outbox), 1)

        self.assertIn(" [1/1R] [ABC]", mail.outbox[0].subject)

    def test_zmieniony_termin(self):
        termin = TerminSzkoleniaFactory.create(lokalizacja__shortname="Warszawa")
        uczestnik = UczestnikFactory.create(prywatny=True)
        send_zgloszenie_mail(uczestnik, extra_prefix="ABC", object_changed=termin)

        self.assertEqual(len(mail.outbox), 1)

        self.assertIn("[z  ID: {}]".format(termin.pk), mail.outbox[0].body)
        self.assertIn("[na ID: {}]".format(uczestnik.termin.pk), mail.outbox[0].body)

    def test_dodany_przez(self):
        uczestnik = UczestnikFactory.create(prywatny=True)
        send_zgloszenie_mail(
            uczestnik,
            extra_prefix="ABC",
            moderator=UserFactory.create(
                username="username", first_name="", last_name=""
            ),
        )

        self.assertEqual(len(mail.outbox), 1)

        self.assertIn("Wprowadzone przez: [username]", mail.outbox[0].body)

        mail.outbox = []

        uczestnik = UczestnikFactory.create(prywatny=True)
        send_zgloszenie_mail(
            uczestnik,
            extra_prefix="ABC",
            moderator=UserFactory.create(
                username="username2", first_name="Alek", last_name=""
            ),
        )

        self.assertEqual(len(mail.outbox), 1)

        self.assertIn("Wprowadzone przez: Alek [username2]", mail.outbox[0].body)

    def test_zmieniony_przez(self):
        termin = TerminSzkoleniaFactory.create()
        uczestnik = UczestnikFactory.create(prywatny=True)
        send_zgloszenie_mail(
            uczestnik,
            extra_prefix="ABC",
            object_changed=termin,
            moderator=UserFactory.create(
                username="username", first_name="", last_name=""
            ),
        )

        self.assertEqual(len(mail.outbox), 1)

        self.assertIn(
            "zmienione przez: [username]".format(str(termin)), mail.outbox[0].body
        )

        mail.outbox = []

        termin = TerminSzkoleniaFactory.create()
        uczestnik = UczestnikFactory.create(prywatny=True)
        send_zgloszenie_mail(
            uczestnik,
            extra_prefix="ABC",
            object_changed=termin,
            moderator=UserFactory.create(
                username="username2", first_name="Alek", last_name=""
            ),
        )

        self.assertEqual(len(mail.outbox), 1)

        self.assertIn(
            "zmienione przez: Alek [username2]".format(str(termin)), mail.outbox[0].body
        )

    def test_rn_w_temacie_gdy_prywatny(self):
        uczestnik = UczestnikFactory.create(
            prywatny=True,
            imie_nazwisko="""
        a
        b
        c\r\n
        """,
        )
        send_zgloszenie_mail(uczestnik)

        self.assertEqual(len(mail.outbox), 1)

        self.assertIn("[1/1R] a b c", mail.outbox[0].subject)

    def test_rn_w_temacie_gdy_firmowy(self):
        uczestnik = UczestnikFactory.create(prywatny=False)
        uczestnik.faktura_firma = """
        e
        f
        g\r\n
        """
        send_zgloszenie_mail(uczestnik)

        self.assertEqual(len(mail.outbox), 1)

        self.assertIn("[1/1R] [F1] e f g", mail.outbox[0].subject)
