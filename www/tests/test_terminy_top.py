from datetime import date, timed<PERSON><PERSON>
from decimal import Decimal
from django.test import TestCase
from www.templatetags.myfilters import myincludes
from www.models import Szkolenie, TerminSzkolenia, Lokalizacja, TagZawod, TagDlugosc, Panstwo
from i18n.models import Waluta


class TerminyTopTestCase(TestCase):
    def setUp(self):
        # Tworzymy potrzebne obiekty
        self.panstwo = Panstwo.objects.create(nazwa="Polska")
        self.waluta = Waluta.objects.create(nazwa="Polski złoty", symbol="PLN", kurs_kupna=Decimal("1.00"), zaliczka=Decimal("406.50"))
        self.tag_zawod = TagZawod.objects.create(nazwa="Programista", language="pl")
        self.tag_dlugosc = TagDlugosc.objects.create(nazwa="Szkolenie", slug="szkolenie")

        # Tworzymy testowe szkolenie
        self.szkolenie = Szkolenie.objects.create(
            nazwa="Test Szkolenie",
            slug="test-szkolenie",
            language="pl",
            aktywne=True,
            tag_zawod=self.tag_zawod,
            tag_dlugosc=self.tag_dlugosc,
            waluta=self.waluta,
            kod="TST",
            czas_dni=1,
            cena_bazowa=Decimal("100.00"),
            program="Program testowy",
            opis="Opis testowy",
            cel="Cel testowy",
            tryb_dzienny_opis="(zajęcia w dni robocze)",
            tryb_wieczorowy_opis="(zajęcia w godzinach wieczornych)",
            tryb_zaoczny_opis="(zajęcia w weekendy)"
        )

        # Tworzymy lokalizacje
        self.lokalizacja1 = Lokalizacja.objects.create(
            shortname="WAW",
            fullname="Warszawa",
            numer=1,
            panstwo=self.panstwo
        )

        self.lokalizacja2 = Lokalizacja.objects.create(
            shortname="KRK",
            fullname="Kraków",
            numer=2,
            panstwo=self.panstwo
        )

        self.lokalizacja3 = Lokalizacja.objects.create(
            shortname="ZDL",
            fullname="Zdalnie",
            numer=3,
            panstwo=self.panstwo
        )

        # Dzisiejsza data
        self.today = date.today()

    def test_brak_terminow(self):
        """Test gdy nie ma żadnych terminów"""
        result = myincludes(f"[[terminy_top:{self.szkolenie.slug}]]")
        self.assertEqual(result, "Dostępne na zamówienie dla grup.")

    def test_jeden_termin(self):
        """Test gdy jest tylko jeden termin"""
        termin_date = self.today + timedelta(days=10)
        TerminSzkolenia.objects.create(
            szkolenie=self.szkolenie,
            termin=termin_date,
            lokalizacja=self.lokalizacja1,
            zamkniete=False,
            tryb=1
        )

        result = myincludes(f"[[terminy_top:{self.szkolenie.slug}]]")
        expected = f"Start {termin_date.strftime('%d.%m')}"
        self.assertEqual(result, expected)

    def test_dwa_terminy(self):
        """Test gdy są dokładnie dwa terminy"""
        termin_date1 = self.today + timedelta(days=10)
        termin_date2 = self.today + timedelta(days=20)

        TerminSzkolenia.objects.create(
            szkolenie=self.szkolenie,
            termin=termin_date1,
            lokalizacja=self.lokalizacja1,
            zamkniete=False,
            tryb=1
        )

        TerminSzkolenia.objects.create(
            szkolenie=self.szkolenie,
            termin=termin_date2,
            lokalizacja=self.lokalizacja2,
            zamkniete=False,
            tryb=1
        )

        result = myincludes(f"[[terminy_top:{self.szkolenie.slug}]]")
        expected = f"Start {termin_date1.strftime('%d.%m')} lub {termin_date2.strftime('%d.%m')}"
        self.assertEqual(result, expected)

    def test_wiele_terminow(self):
        """Test gdy jest więcej niż dwa terminy"""
        termin_date1 = self.today + timedelta(days=10)
        termin_date2 = self.today + timedelta(days=20)
        termin_date3 = self.today + timedelta(days=30)

        TerminSzkolenia.objects.create(
            szkolenie=self.szkolenie,
            termin=termin_date1,
            lokalizacja=self.lokalizacja1,
            zamkniete=False,
            tryb=1
        )

        TerminSzkolenia.objects.create(
            szkolenie=self.szkolenie,
            termin=termin_date2,
            lokalizacja=self.lokalizacja2,
            zamkniete=False,
            tryb=1
        )

        TerminSzkolenia.objects.create(
            szkolenie=self.szkolenie,
            termin=termin_date3,
            lokalizacja=self.lokalizacja3,
            zamkniete=False,
            tryb=1
        )

        result = myincludes(f"[[terminy_top:{self.szkolenie.slug}]]")
        expected = f"Start {termin_date1.strftime('%d.%m')} lub {termin_date2.strftime('%d.%m')}"
        self.assertEqual(result, expected)

    def test_duplikaty_dat(self):
        """Test gdy są duplikaty dat (ten sam dzień, różne lokalizacje)"""
        termin_date1 = self.today + timedelta(days=10)
        termin_date2 = self.today + timedelta(days=10)  # Ten sam dzień co termin1
        termin_date3 = self.today + timedelta(days=20)

        TerminSzkolenia.objects.create(
            szkolenie=self.szkolenie,
            termin=termin_date1,
            lokalizacja=self.lokalizacja1,
            zamkniete=False,
            tryb=1
        )

        TerminSzkolenia.objects.create(
            szkolenie=self.szkolenie,
            termin=termin_date2,
            lokalizacja=self.lokalizacja3,  # Inna lokalizacja
            zamkniete=False,
            tryb=1
        )

        TerminSzkolenia.objects.create(
            szkolenie=self.szkolenie,
            termin=termin_date3,
            lokalizacja=self.lokalizacja2,
            zamkniete=False,
            tryb=1
        )

        result = myincludes(f"[[terminy_top:{self.szkolenie.slug}]]")
        expected = f"Start {termin_date1.strftime('%d.%m')} lub {termin_date3.strftime('%d.%m')}"
        self.assertEqual(result, expected)

    def test_id_zamiast_sluga(self):
        """Test gdy używamy ID zamiast sluga"""
        termin_date = self.today + timedelta(days=10)
        TerminSzkolenia.objects.create(
            szkolenie=self.szkolenie,
            termin=termin_date,
            lokalizacja=self.lokalizacja1,
            zamkniete=False,
            tryb=1
        )

        result = myincludes(f"[[terminy_top:{self.szkolenie.id}]]")
        expected = f"Start {termin_date.strftime('%d.%m')}"
        self.assertEqual(result, expected)

    # Testy dla terminy_top_detailed

    def test_terminy_top_detailed_brak_terminow(self):
        """Test gdy nie ma żadnych terminów dla terminy_top_detailed"""
        result = myincludes(f"[[terminy_top_detailed:{self.szkolenie.slug}]]")
        self.assertEqual(result, "Dostępne na zamówienie dla grup.")

    def test_terminy_top_detailed_jeden_termin(self):
        """Test gdy jest tylko jeden termin dla terminy_top_detailed"""
        termin_date = self.today + timedelta(days=10)
        TerminSzkolenia.objects.create(
            szkolenie=self.szkolenie,
            termin=termin_date,
            lokalizacja=self.lokalizacja1,
            zamkniete=False,
            tryb=1  # tryb dzienny
        )

        result = myincludes(f"[[terminy_top_detailed:{self.szkolenie.slug}]]")
        expected = f"Start od {termin_date.strftime('%d.%m')} {self.szkolenie.tryb_dzienny_opis}"
        self.assertEqual(result, expected)

    def test_terminy_top_detailed_dwa_terminy(self):
        """Test gdy są dokładnie dwa terminy dla terminy_top_detailed"""
        termin_date1 = self.today + timedelta(days=10)
        termin_date2 = self.today + timedelta(days=20)

        TerminSzkolenia.objects.create(
            szkolenie=self.szkolenie,
            termin=termin_date1,
            lokalizacja=self.lokalizacja1,
            zamkniete=False,
            tryb=1  # tryb dzienny
        )

        TerminSzkolenia.objects.create(
            szkolenie=self.szkolenie,
            termin=termin_date2,
            lokalizacja=self.lokalizacja2,
            zamkniete=False,
            tryb=2  # tryb wieczorowy
        )

        result = myincludes(f"[[terminy_top_detailed:{self.szkolenie.slug}]]")
        expected = f"Start od {termin_date1.strftime('%d.%m')} {self.szkolenie.tryb_dzienny_opis} lub od {termin_date2.strftime('%d.%m')} {self.szkolenie.tryb_wieczorowy_opis}"
        self.assertEqual(result, expected)

    def test_terminy_top_detailed_wiele_terminow(self):
        """Test gdy jest więcej niż dwa terminy dla terminy_top_detailed"""
        termin_date1 = self.today + timedelta(days=10)
        termin_date2 = self.today + timedelta(days=20)
        termin_date3 = self.today + timedelta(days=30)

        TerminSzkolenia.objects.create(
            szkolenie=self.szkolenie,
            termin=termin_date1,
            lokalizacja=self.lokalizacja1,
            zamkniete=False,
            tryb=1  # tryb dzienny
        )

        TerminSzkolenia.objects.create(
            szkolenie=self.szkolenie,
            termin=termin_date2,
            lokalizacja=self.lokalizacja2,
            zamkniete=False,
            tryb=2  # tryb wieczorowy
        )

        TerminSzkolenia.objects.create(
            szkolenie=self.szkolenie,
            termin=termin_date3,
            lokalizacja=self.lokalizacja3,
            zamkniete=False,
            tryb=3  # tryb zaoczny
        )

        result = myincludes(f"[[terminy_top_detailed:{self.szkolenie.slug}]]")
        expected = f"Start od {termin_date1.strftime('%d.%m')} {self.szkolenie.tryb_dzienny_opis} lub od {termin_date2.strftime('%d.%m')} {self.szkolenie.tryb_wieczorowy_opis}"
        self.assertEqual(result, expected)

    def test_terminy_top_detailed_duplikaty_dat(self):
        """Test gdy są duplikaty dat dla terminy_top_detailed"""
        termin_date1 = self.today + timedelta(days=10)
        termin_date2 = self.today + timedelta(days=10)  # Ten sam dzień co termin1
        termin_date3 = self.today + timedelta(days=20)

        TerminSzkolenia.objects.create(
            szkolenie=self.szkolenie,
            termin=termin_date1,
            lokalizacja=self.lokalizacja1,
            zamkniete=False,
            tryb=1  # tryb dzienny
        )

        TerminSzkolenia.objects.create(
            szkolenie=self.szkolenie,
            termin=termin_date2,
            lokalizacja=self.lokalizacja3,  # Inna lokalizacja
            zamkniete=False,
            tryb=2  # tryb wieczorowy - nie powinien być uwzględniony, bo data jest duplikatem
        )

        TerminSzkolenia.objects.create(
            szkolenie=self.szkolenie,
            termin=termin_date3,
            lokalizacja=self.lokalizacja2,
            zamkniete=False,
            tryb=3  # tryb zaoczny
        )

        result = myincludes(f"[[terminy_top_detailed:{self.szkolenie.slug}]]")
        expected = f"Start od {termin_date1.strftime('%d.%m')} {self.szkolenie.tryb_dzienny_opis} lub od {termin_date3.strftime('%d.%m')} {self.szkolenie.tryb_zaoczny_opis}"
        self.assertEqual(result, expected)

    def test_terminy_top_detailed_id_zamiast_sluga(self):
        """Test gdy używamy ID zamiast sluga dla terminy_top_detailed"""
        termin_date = self.today + timedelta(days=10)
        TerminSzkolenia.objects.create(
            szkolenie=self.szkolenie,
            termin=termin_date,
            lokalizacja=self.lokalizacja1,
            zamkniete=False,
            tryb=3  # tryb zaoczny
        )

        result = myincludes(f"[[terminy_top_detailed:{self.szkolenie.id}]]")
        expected = f"Start od {termin_date.strftime('%d.%m')} {self.szkolenie.tryb_zaoczny_opis}"
        self.assertEqual(result, expected)
