import datetime
import json


class APIZliczaczPatch(object):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        self.response_post_status_code = 201
        self.response_post_text = None

        self.response_get_status_code = 200
        self.response_get_text = None

        self.response_post_json = None
        self.response_get_json = None

    def mock_requests_post(self, url, data, **kwargs):
        """
        Patch na metodę `post` bilbioteki `requests`. Dzięki temu możemy
        testować tworzenie faktur przez API bez uruchomionej aplikacji
        api-zliczacz.
        """

        parsed_data = json.loads(data)

        assert parsed_data

        class DummyResponse(object):
            status_code = self.response_post_status_code
            text = self.response_post_text
            extra_json_data = self.response_post_json

            def json(self):
                result = {
                    "faktura_id": 99999,
                    "client_created": False,
                    "faktura_termin_platnosci": datetime.date.today(),
                    "faktura_numer": "1/2/3",
                    "parsed_data": json.loads(data),
                    "status_faktury": 2,
                }
                if self.extra_json_data:
                    result.update(self.extra_json_data)
                return result

        return DummyResponse()

    def mock_requests_get(self, url, **kwargs):
        """
        Patch na metodę `get` bilbioteki `requests`. Dzięki temu możemy
        testować pobieranie faktur do API bez uruchomionej aplikacji
        api-zliczacz.
        """

        class DummyResponse(object):
            status_code = self.response_get_status_code
            text = self.response_get_text
            json_data = self.response_get_json

            @property
            def content(self):
                return "Jakiś tekst ...".encode("utf-8")

            def json(self):
                return self.json_data

        return DummyResponse()
