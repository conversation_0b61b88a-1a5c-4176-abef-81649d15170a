from django.conf import settings
from django.urls import reverse
from django.utils.encoding import force_text

from i18n.models import <PERSON><PERSON><PERSON>Jez<PERSON><PERSON>
from www.models import MyFlatPage, SiteModule


def msie(request):
    try:
        user_agent = force_text(request.META.get("HTTP_USER_AGENT", ""))
        return ("Windows NT" in user_agent) and ("Edge" in user_agent)
    except Exception:
        return False


def edge(request):
    try:
        user_agent = force_text(request.META.get("HTTP_USER_AGENT", ""))
        return ("Windows" in user_agent) and ("MSIE" in user_agent)
    except Exception:
        return False


def chrome_xp(request):
    try:
        user_agent = force_text(request.META.get("HTTP_USER_AGENT", ""))
        return ("Windows NT 5.1" in user_agent) and ("Chrome" in user_agent)
    except Exception:
        return False


def common_context_processor(request):
    if hasattr(request, "session"):
        language = request.session.get("language", settings.DEFAULT_LANGUAGE)
    else:
        language = settings.DEFAULT_LANGUAGE

    site_modules = SiteModule.objects.filter(
        language=language, enabled=True, content_group=None
    )
    content_group = getattr(request, "content_group", None)
    if content_group:
        site_modules = content_group.replace_modules(site_modules)

    return {
        "site_modules": site_modules,
        "show_analytics": getattr(settings, "SHOW_ANALYTICS", False),
        "this_url": request.build_absolute_uri(request.path_info),
        "footer": MyFlatPage.objects.get(slug="footer", language=language),
        "menu": MyFlatPage.objects.get(slug="menu", language=language),
        "default_tlumaczenia": [
            x for x in [("pl", "/pl/"), ("en", "/en/")] if x[0] != language
        ],
        "default_language": settings.DEFAULT_LANGUAGE,
        "language": language,
        "wersja_jezykowa": WersjaJezykowa.biezaca(),
        "search_url": reverse("search_" + language),
        "pl": language == "pl",
        "en": language == "en",
        "edge": edge(request),
        "ie": msie(request),
        "chrome_xp": chrome_xp(request),
        "NORECAPTCHA_INVISIBLE_SITE_KEY": settings.NORECAPTCHA_INVISIBLE_SITE_KEY,
    }


def debug(context):
    return {"DEBUG": settings.DEBUG}
