from django.conf import settings
from django.conf.urls import url
from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.http import JsonResponse


def cache_key(app_label, model_name, object_id):
    return "admin-locking-%s-%s-%s" % (app_label, model_name, object_id)


def locked(obj, user_id):
    locking = cache.get(cache_key(obj._meta.app_label, obj._meta.model_name, obj.pk))

    if locking and int(locking) != user_id:
        return True
    return False


class AdminLockingMixin:
    def locking(self, request, object_id):
        lock_url = request.POST.get("url")
        if not lock_url or not request.user.is_authenticated:
            return JsonResponse({}, status=400)

        lock_timeout = getattr(settings, "ADMIN_LOCKING_TIMEOUT", 30)
        app_label = self.model._meta.app_label
        model_name = self.model._meta.model_name

        obj_cache_key = cache_key(app_label, model_name, object_id)

        locking = cache.get(obj_cache_key)
        if locking:
            locking = int(locking)
            if locking != request.user.id:

                related_object = False

                if locking < 0:
                    locking *= -1
                    related_object = True

                user = get_user_model().objects.get(id=locking)

                if related_object:
                    message = (
                        "Obiekty zależne dla tej strony są aktualnie edytowane "
                        "przez: <b>%(name_and_email)s</b>.<br>"
                        "W momencie zakończenia edycji, strona odświeży się "
                        "automatycznie."
                        % {"name_and_email": "%s (%s)" % (user.username, user.email)}
                    )
                else:
                    message = (
                        "Ta strona jest aktualnie edytowana przez: "
                        "<b>%(name_and_email)s</b>.<br>W momencie zakończenia "
                        "edycji, odświeży się automatycznie."
                        % {"name_and_email": "%s (%s)" % (user.username, user.email)}
                    )

                return JsonResponse(
                    {
                        "lock_timeout": lock_timeout,
                        "status": "locked",
                        "message": message,
                    }
                )

            if request.POST.get("action") == "clear":
                cache.delete(obj_cache_key)
                return JsonResponse({}, status=204)

        cache.set(obj_cache_key, request.user.id, lock_timeout)

        # Blokujemy obiekty zależne
        if model_name == "uczestnik":
            obj = self.model.objects.get(pk=object_id)

            t_cache_key = cache_key(app_label, "terminszkolenia", obj.termin_id)

            # Jesli obiekt jest zablokowany przez naszego usera, to kontynuujemy ta
            # blokadę (przypadek: najpierw weszlismy do terminu, a dopiero potem do
            # Uczestnika przypisanego do tego terminu). W przeciwnym razie blokujemy
            # dostep wszystkim (ustawiajac id na -99).

            locking = cache.get(t_cache_key)

            if locking and int(locking) == request.user.id:
                cache.set(t_cache_key, request.user.id, lock_timeout)
            else:
                cache.set(t_cache_key, request.user.id * -1, lock_timeout)
        elif model_name == "terminszkolenia":
            from www.models import Uczestnik

            for uczestnik_id in Uczestnik.objects.filter(
                termin_id=object_id
            ).values_list("pk", flat=True):
                cache.set(
                    cache_key(app_label, "uczestnik", uczestnik_id),
                    request.user.id,
                    lock_timeout,
                )

        return JsonResponse({"status": "locking", "lock_timeout": lock_timeout - 10})

    def get_urls(self):
        return [
            url(r"^(.+)/change/locking/$", self.admin_site.admin_view(self.locking)),
        ] + super().get_urls()

    class Media:
        js = ("custom_admin/js/locking.js",)
