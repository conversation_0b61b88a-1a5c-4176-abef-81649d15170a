from .structure import Fak<PERSON>, Podmiot, PozycjaFaktury, StertaFaktur


def faktura_pdf_generate(output, faktura, mode="1"):
    sterta = StertaFaktur(output)
    faktura_dane = faktura

    sprzedawca = Podmiot(
        faktura_dane.faktura_nazwa_sprzedawcy,
        faktura_dane.faktura_adres_sprzedawcy,
        faktura_dane.faktura_kod_pocztowy_sprzedawcy,
        faktura_dane.faktura_miasto_sprzedawcy,
        faktura_dane.faktura_nip_sprzedawcy,
        konto=faktura_dane.faktura_numer_konta,
    )
    nabywca = Podmiot(
        faktura_dane.faktura_nazwa_nabywcy,
        faktura_dane.faktura_adres_nabywcy,
        faktura_dane.faktura_kod_pocztowy_nabywcy,
        faktura_dane.faktura_miasto_nabywcy,
        faktura_dane.faktura_nip_nabywcy,
    )
    f = Faktura(
        faktura_dane.faktura_numer,
        faktura_dane.faktura_miejsce_wystawienia,
        faktura_dane.faktura_data_wystawienia,
        faktura_dane.faktura_data_sprzedazy,
        nabywca,
        sprzedawca,
        faktura_dane.faktura_sposob_platnosci,
        uwagi=faktura_dane.faktura_uwagi,
        faktura_korekta={
            "faktura_numer_korekty": faktura_dane.faktura_numer_korekty,
            "faktura_miejsce_wystawienia_korekty": faktura_dane.faktura_miejsce_wystawienia_korekty,
            "faktura_data_wystawienia_korekty": faktura_dane.faktura_data_wystawienia_korekty,
            "faktura_numer_korekty": faktura_dane.faktura_numer_korekty,
            "faktura_numer_korekty": faktura_dane.faktura_numer_korekty,
            "faktura_powod_korekty": faktura_dane.faktura_powod_korekty,
            "cena_bilans": faktura_dane.cena_bilans,
        },
    )

    pozycje = faktura.fakturakorektapozycjaoryginalna_set.all()
    for p in pozycje:
        f.dodajPozycje(PozycjaFaktury(p))

    pozycje_korekta = faktura.fakturakorektapozycjakorygujaca_set.all()
    for p in pozycje_korekta:
        f.dodajPozycjeKorygujaca(PozycjaFaktury(p))

    if faktura_dane.faktura_typ_daty:
        f.ustawOpisDatySprzedazy(faktura_dane.get_faktura_typ_daty_display())

    f.dajWydruk(output, int(mode), faktura_dane.typ_faktury, sterta)

    numer_faktury_do_nazwy_pliku = (
        faktura_dane.faktura_numer_korekty.replace("/", "-")
        if faktura_dane.faktura_numer_korekty
        else "bez-numeru"
    )
    sterta.drukuj()
    return numer_faktury_do_nazwy_pliku
