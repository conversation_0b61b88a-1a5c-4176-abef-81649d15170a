import locale
import os
from decimal import *

from django.conf import settings
from reportlab.lib.colors import black
from reportlab.lib.enums import *
from reportlab.lib.fonts import addMapping
from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import PropertySet
from reportlab.lib.units import cm
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.pdfgen.canvas import Canvas
from reportlab.platypus import (
    Frame,
    Paragraph,
    Spacer,
    Table,
    TableStyle,
    XPreformatted,
)

from .words import *


def font(x, y):
    return TTFont(
        x,
        "{0}.ttf".format(
            os.path.join(settings.PDF_HELPERS_PATH, "zliczacz", "assets", "fonts", y)
        ),
    )


def addFamily(mapping, basename):
    for (suffix, suffix2, x, y) in [
        ("", "-regular", 0, 0),
        ("-Italic", "-italic", 0, 1),
        ("-Bold-Italic", "-bolditalic", 1, 1),
        ("-Bold", "-bold", 1, 0),
    ]:
        pdfmetrics.registerFont(font(mapping + suffix, basename + suffix2))
        addMapping(mapping, x, y, mapping + suffix)


addFamily("Regular", "texgyreheros")
addFamily("Condensed", "texgyreheroscn")


def escape(tekst):
    return tekst.replace("&", "&amp;").replace("<", "&lt;").replace(">", "&gt;")


def doPDF(tekst):
    return str(tekst)


class ParagraphStyle(PropertySet):
    defaults = {
        "fontName": "Regular",
        "fontSize": 10,
        "leading": 12,
        "leftIndent": 0,
        "rightIndent": 0,
        "firstLineIndent": 0,
        "textTransform": None,
        "alignment": TA_LEFT,
        "spaceBefore": 0,
        "spaceAfter": 0,
        "bulletFontName": "Regular",
        "bulletFontSize": 10,
        "bulletIndent": 0,
        "textColor": black,
        "backColor": None,
        "wordWrap": None,
        "borderWidth": 0,
        "borderPadding": 0,
        "borderColor": None,
        "borderRadius": None,
        "allowWidows": 1,
        "allowOrphans": 0,
        "endDots": None,
        "splitLongWords": 1,
        "underlineWidth": 1,
        "bulletAnchor": "start",
        "justifyLastLine": 0,
        "justifyBreaks": 0,
        "spaceShrinkage": 0.05,
        "strikeWidth": None,
        "underlineOffset": -1.25,
        "underlineGap": 1,
        "strikeOffset": 2.5,
        "strikeGap": 1,
        "linkUnderline": 0,
        #'hyphenationLang': en_GB,
        "uriWasteReduce": 0.3,
        "embeddedHyphenation": 1,
    }


class PozycjaFaktury:
    def __init__(self, pozycja):
        from www.models import FAKTURA_STAWKA_VAT_MAP

        self.nazwa = pozycja.pozycja_opis
        # self.vat=None if pozycja.stawka_vat_skrot=='zw.' else float(pozycja.stawka_vat_skrot)
        if FAKTURA_STAWKA_VAT_MAP[pozycja.stawka_vat]["stawka_vat_skrot"] in [
            "zw.",
            "NP",
        ]:
            self.vat = FAKTURA_STAWKA_VAT_MAP[pozycja.stawka_vat]["stawka_vat_skrot"]
        else:
            self.vat = float(
                FAKTURA_STAWKA_VAT_MAP[pozycja.stawka_vat]["stawka_vat_skrot"]
            )
        self.miara = "szt."  # pozycja.pozycja_jednostka
        self.pkwiu = ""  # pozycja.pozycja_pkwiu
        self.netto = float(pozycja.pozycja_wartosc_netto)
        self.brutto = float(pozycja.pozycja_wartosc_brutto)
        self.ilosc = float(pozycja.pozycja_liczba_jednostek)
        self.cenajednostkowa = float(pozycja.pozycja_cena_jednostkowa_netto)

    def dajDaneDoWydruku(self):
        return [
            self.nazwa,
            self.pkwiu,
            self.ilosc,
            self.miara,
            self.cenajednostkowa,
            self.netto,
            self.vat,
            self.brutto - self.netto,
            self.brutto,
        ]


class Zaliczka:
    def __init__(self, nazwa, vat, brutto):
        self.nazwa = nazwa
        self.vat = vat
        self.brutto = brutto


class Podmiot:
    def __init__(self, nazwa, adres, kod, miasto, nip, platnosc="gotówka", konto=None):
        self.nazwa = nazwa
        self.adres = adres
        self.kod = kod
        self.miasto = miasto
        self.nip = nip
        self.platnosc = platnosc
        self.konto = konto

    def dajParagrafy(self, styl):
        return [
            Paragraph(doPDF(self.nazwa), styl),
            Paragraph(doPDF(self.adres), styl),
            Paragraph(doPDF(self.kod + " " + self.miasto), styl),
        ]

    def dajWizytowke(self):
        if self.nazwa:
            nazwa = escape(self.nazwa).replace("\n", "<br/>") + "<br/>"
        else:
            nazwa = ""
        return (
            nazwa
            + escape(doPDF(self.adres))
            + "<br/>"
            + escape(doPDF(self.kod))
            + " "
            + escape(doPDF(self.miasto))
        )

    def dajNip(self):
        if self.nip is not None:
            return self.nip
        else:
            return ""


class StertaFaktur:
    def __init__(self, sink):
        self.wydruk = Canvas(sink, pagesize=A4)

    def drukuj(self):
        self.wydruk.save()


class Faktura:
    def __init__(
        self,
        numer,
        miejsce,
        datawystawienia,
        datasprzedazy,
        nabywca=None,
        sprzedawca=None,
        platnosc=None,
        konto=None,
        uwagi=None,
        faktura_oryg=None,
        faktura_korekta=None,
    ):
        self.numer = numer
        self.miejsce = miejsce
        self.datawystawienia = datawystawienia
        self.datasprzedazy = datasprzedazy
        self.nabywca = nabywca
        self.sprzedawca = sprzedawca
        self.uwagi = uwagi
        self.zaliczki = []
        self.faktura_korekta = faktura_korekta
        if konto == None:
            self.konto = sprzedawca.konto
        else:
            self.konto = konto
        if nabywca != None and platnosc == None:
            self.platnosc = nabywca.platnosc
        else:
            self.platnosc = platnosc
        self.pozycje = []
        self.pozycje_korekta = []
        self.dataDuplikatu = None
        self.opisDatySprzedazy = "Data sprzedaży"

    def ustawOpisDatySprzedazy(self, opis):
        self.opisDatySprzedazy = opis

    def ustawDuplikat(self, data):
        self.dataDuplikatu = data

    def dodajZaliczke(self, zaliczka):
        self.zaliczki.append(zaliczka)

    def dodajPozycje(self, pozycjaFaktury, pozycja=None):
        if pozycja == None:
            self.pozycje.append(pozycjaFaktury)
        else:
            self.pozycje.insert(pozycja, pozycjaFaktury)

    def dodajPozycjeKorygujaca(self, pozycjaFaktury, pozycja=None):
        if pozycja == None:
            self.pozycje_korekta.append(pozycjaFaktury)
        else:
            self.pozycje_korekta.insert(pozycja, pozycjaFaktury)

    def dajPodsumy(self):
        wynik = [
            ["", 0.0, None, 0.0, 0.0],
            ["", 0.0, "zw.", 0.0, 0.0],
            ["", 0.0, "NP", 0.0, 0.0],
            ["", 0.0, 23, 0.0, 0.0],
            ["", 0.0, 22, 0.0, 0.0],
            ["", 0.0, 8, 0.0, 0.0],
            ["", 0.0, 7, 0.0, 0.0],
            ["", 0.0, 3, 0.0, 0.0],
            ["", 0.0, 0, 0.0, 0.0],
            ["RAZEM:", 0.0, "", 0.0, 0.0],
        ]
        pomoc = {None: 0, "zw.": 1, "NP": 2, 23: 3, 22: 4, 8: 5, 7: 6, 3: 7, 0: 8}
        for pozycja in self.pozycje:
            indeks = pomoc[pozycja.vat]
            wynik[indeks][1] += pozycja.netto
            wynik[indeks][3] += pozycja.brutto - pozycja.netto
            wynik[indeks][4] += pozycja.brutto
            wynik[-1][1] += pozycja.netto
            wynik[-1][3] += pozycja.brutto - pozycja.netto
            wynik[-1][4] += pozycja.brutto
        return [x for x in wynik if x[1] > 0]

    def dajPodsumyKorekty(self):
        wynik = [
            ["", 0.0, None, 0.0, 0.0],
            ["", 0.0, "zw.", 0.0, 0.0],
            ["", 0.0, "NP", 0.0, 0.0],
            ["", 0.0, 23, 0.0, 0.0],
            ["", 0.0, 22, 0.0, 0.0],
            ["", 0.0, 8, 0.0, 0.0],
            ["", 0.0, 7, 0.0, 0.0],
            ["", 0.0, 3, 0.0, 0.0],
            ["", 0.0, 0, 0.0, 0.0],
            ["RAZEM:", 0.0, "", 0.0, 0.0],
        ]
        pomoc = {None: 0, "zw.": 1, "NP": 2, 23: 3, 22: 4, 8: 5, 7: 6, 3: 7, 0: 8}

        indeksy = []
        for pozycja in self.pozycje_korekta:
            indeks = pomoc[pozycja.vat]
            indeksy.append(indeks)
            wynik[indeks][1] += pozycja.netto
            wynik[indeks][3] += pozycja.brutto - pozycja.netto
            wynik[indeks][4] += pozycja.brutto
            wynik[-1][1] += pozycja.netto
            wynik[-1][3] += pozycja.brutto - pozycja.netto
            wynik[-1][4] += pozycja.brutto
        indeksy.append(len(wynik) - 1)
        res = []

        for index in indeksy:
            res.append(wynik[index])
        return [x for x in res if x[1] >= 0]

    def dajNumer(self):
        return (
            self.numer
            + "/"
            + self.datawystawienia.month
            + "/"
            + self.datawystawienia.year
        )

    def dajNazwePliku(self):
        return (
            "faktura-"
            + str(self.datawystawienia.year)
            + "."
            + ("%02d" % self.datawystawienia.month)
            + "."
            + str(self.numer)
            + (
                self.dataDuplikatu != None
                and self.dataDuplikatu.strftime("-%Y%m%d")
                or ""
            )
            + ".pdf"
        )

    def dajDaneDoWydruku(self):
        return [x.dajDaneDoWydruku() for x in self.pozycje]

    def dajStroneWydruku(self, wydruk, typ, oryginal):
        f = Frame(1 * cm, 1 * cm, 19 * cm, 27.7 * cm)
        tresc = []
        prawo = ParagraphStyle(name="Prawo", alignment=TA_RIGHT)
        prawowtabelce = ParagraphStyle(
            name="PrawoWTabelce", alignment=TA_RIGHT, fontName="Condensed"
        )
        tytul = ParagraphStyle(
            name="Tytul", alignment=TA_CENTER, fontSize=18, leading=22
        )
        podtytul = ParagraphStyle(
            name="PodTytul", alignment=TA_CENTER, fontSize=14, leading=16
        )
        normalny = ParagraphStyle(name="Normalny")
        normalnyprawo = ParagraphStyle(name="NormalnyPrawo", alignment=TA_RIGHT)
        wielki = ParagraphStyle(name="Wielki", fontSize=14)
        wtabelce = ParagraphStyle(
            name="WTabelce", fontSize=9, leading=10, fontName="Condensed"
        )
        srodekwtabelce = ParagraphStyle(
            name="SrodekWTabelce", fontSize=9, leading=10, alignment=TA_CENTER
        )
        srodekwtabelcecond = ParagraphStyle(
            name="SrodekWTabelce",
            fontSize=10,
            leading=12,
            alignment=TA_CENTER,
            fontName="Condensed",
        )
        srodek = ParagraphStyle(name="Srodek", alignment=TA_CENTER)
        tresc.append(
            Paragraph(doPDF("Korekta do Faktury VAT nr: " + self.numer), prawo)
        )
        tresc.append(
            Paragraph(
                doPDF(
                    "Miejsce i data wystawienia: "
                    + self.miejsce
                    + ", "
                    + self.datawystawienia.strftime("%d.%m.%Y")
                ),
                prawo,
            )
        )
        if typ == 1:
            tresc.append(
                Paragraph(
                    doPDF(
                        self.opisDatySprzedazy
                        + ": "
                        + self.datasprzedazy.strftime("%d.%m.%Y")
                    ),
                    prawo,
                )
            )
        tresc.append(Spacer(0, 0.3 * cm))
        tresc.append(
            Paragraph(
                doPDF(
                    "Miejsce i data wystawienia korekty: "
                    + self.faktura_korekta["faktura_miejsce_wystawienia_korekty"]
                    + ", "
                    + self.faktura_korekta["faktura_data_wystawienia_korekty"].strftime(
                        "%d.%m.%Y"
                    )
                ),
                prawo,
            )
        )
        tresc.append(Spacer(0, 0.7 * cm))
        typ_slownie = "VAT" if typ == 1 else "pro forma"
        tresc.append(
            Paragraph(
                doPDF(
                    "<b>Faktura %s korygująca nr %s</b>"
                    % (typ_slownie, self.faktura_korekta["faktura_numer_korekty"])
                ),
                tytul,
            )
        )
        if self.dataDuplikatu is not None:
            tresc.append(
                Paragraph(
                    doPDF(
                        "<b>Duplikat z dnia %s</b>"
                        % self.dataDuplikatu.strftime("%d.%m.%Y")
                    ),
                    podtytul,
                )
            )
            if typ == 1:
                tresc.append(
                    Paragraph(doPDF("ORYGINAŁ" if oryginal else "KOPIA"), srodek)
                )
        tresc.append(Spacer(0, 0.7 * cm))
        tresc.append(
            Table(
                [
                    [
                        Paragraph("<b>Sprzedawca:</b>", normalny),
                        Paragraph("<b>Nabywca:</b>", normalny),
                    ],
                    [
                        Paragraph(doPDF(self.sprzedawca.dajWizytowke()), normalny),
                        Paragraph(doPDF(self.nabywca.dajWizytowke()), normalny),
                    ],
                    [
                        Paragraph(
                            "<b>NIP: " + self.sprzedawca.dajNip() + "</b>", normalny
                        ),
                        Paragraph(
                            "<b>NIP: " + self.nabywca.dajNip() + "</b>", normalny
                        ),
                    ],
                ],
                colWidths=[9.5 * cm, 9.5 * cm],
            )
        )

        naglowek = [
            "Lp.",
            "Nazwa",
            "PKWiU",
            "Ilość",
            "J.m.",
            "Cena netto",
            "Wartość netto",
            "VAT (%)",
            "Kwota VAT",
            "Wartość brutto",
        ]
        dane = []
        dane.append([Paragraph(doPDF("" + x + ""), srodekwtabelce) for x in naglowek])
        lp = 1
        podsumy = None
        for pozycja in self.pozycje:
            pozycja = pozycja.dajDaneDoWydruku()
            pozycja[0:0] = [lp]
            pozycja[1] = Paragraph(doPDF(pozycja[1]), wtabelce)
            if pozycja[3] != round(pozycja[3]):
                # nie, tak sie nie da:
                # pozycja[3]=locale.format("%F", round(pozycja[3], 3))
                precyzja = len(str(pozycja[3] - int(pozycja[3]))) - 2
                precyzja = min(3, precyzja)
                format_string = "%." + str(precyzja) + "F"
                pozycja[3] = locale.format(format_string, pozycja[3])
            else:
                pozycja[3] = int(pozycja[3])
            pozycja[4] = Paragraph(doPDF(pozycja[4]), srodekwtabelcecond)
            pozycja[5] = locale.format("%5.2f", pozycja[5])
            pozycja[6] = locale.format("%5.2f", pozycja[6])
            if isinstance(pozycja[7], float) or isinstance(pozycja[7], int):
                pozycja[7] = "%2d" % pozycja[7]
            pozycja[8] = locale.format("%5.2f", pozycja[8])
            pozycja[9] = locale.format("%5.2f", pozycja[9])
            dane.append(pozycja)
            lp += 1
            podsumy = self.dajPodsumy()
        if podsumy:
            razem = podsumy[-1][-1]
            pozostale_netto = Decimal("%f" % podsumy[-1][1])
            pozostale_brutto = Decimal("%f" % podsumy[-1][4])
            pozostaly_vat = Decimal("%f" % podsumy[-1][3])
            podsumy[0][0] = "wg stawek:"
            for pozycja in podsumy:
                pozycja[0:0] = 5 * [""]
                pozycja[6] = locale.format("%5.2f", pozycja[6])
                if isinstance(pozycja[7], float) or isinstance(pozycja[7], int):
                    pozycja[7] = "%2d" % pozycja[7]
                pozycja[8] = locale.format("%5.2f", pozycja[8])
                pozycja[9] = locale.format("%5.2f", pozycja[9])
                dane.append(pozycja)
            for i in range(6, 10):
                dane[-1][i] = Paragraph(
                    doPDF("<b>" + dane[-1][i] + "</b>"), prawowtabelce
                )
            dane[-1][7] = Paragraph("", normalny)
            dane[lp][5] = Paragraph(doPDF(dane[lp][5]), normalny)
            dane[-1][5] = Paragraph(doPDF("<b>" + dane[-1][5] + "</b>"), normalnyprawo)

            for zaliczka in self.zaliczki:
                pozostale_netto -= zaliczka.brutto - zaliczka.vat
                pozostale_brutto -= zaliczka.brutto
                pozostaly_vat -= zaliczka.vat
                vat = locale.format("%5.2f", zaliczka.vat)
                brutto = locale.format("%5.2f", zaliczka.brutto)
                netto = locale.format("%5.2f", zaliczka.brutto - zaliczka.vat)
                dane.append(
                    [
                        "",
                        "",
                        "",
                        "",
                        "",
                        "- zaliczka (" + zaliczka.nazwa + ")",
                        netto,
                        "",
                        vat,
                        brutto,
                    ]
                )
            if len(self.zaliczki) > 0:
                dane.append(
                    [
                        "",
                        "",
                        "",
                        "",
                        "",
                        Paragraph(doPDF("<b>POZOSTAŁA&nbsp;KWOTA:</b>"), normalny),
                        Paragraph(
                            doPDF(
                                "<b>" + locale.format("%5.2f", pozostale_netto) + "</b>"
                            ),
                            prawowtabelce,
                        ),
                        "",
                        Paragraph(
                            doPDF(
                                "<b>" + locale.format("%5.2f", pozostaly_vat) + "</b>"
                            ),
                            prawowtabelce,
                        ),
                        Paragraph(
                            doPDF(
                                "<b>"
                                + locale.format("%5.2f", pozostale_brutto)
                                + "</b>"
                            ),
                            prawowtabelce,
                        ),
                    ]
                )
                razem = pozostale_brutto

        tabelka = Table(
            dane,
            colWidths=[
                0.6 * cm,
                6.2 * cm,
                1.3 * cm,
                1 * cm,
                1.2 * cm,
                2 * cm,
                1.9 * cm,
                0.9 * cm,
                1.9 * cm,
                1.9 * cm,
            ],
        )
        tabelka.setStyle(
            TableStyle(
                [
                    ("FONT", (0, 0), (-1, 0), "Regular", 10, 12),
                    ("FONT", (0, 1), (-1, -1), "Condensed", 10, 12),
                    ("VALIGN", (0, 0), (-1, -1), "MIDDLE"),
                    ("ALIGN", (5, 1), (-1, -1), "RIGHT"),
                    ("ALIGN", (0, 1), (0, -1), "RIGHT"),
                    ("ALIGN", (3, 1), (3, -1), "RIGHT"),
                    ("GRID", (0, 0), (-1, lp - 1), 0.1, black),
                    ("GRID", (6, lp - 1), (-1, -1), 0.1, black),
                    ("LEFTPADDING", (0, 0), (-1, -1), 3),
                    ("RIGHTPADDING", (0, 0), (-1, -1), 3),
                ]
            )
        )

        # !DEBUG
        tresc.append(Spacer(0, 0.5 * cm))
        tresc.append(Paragraph(doPDF("<b>STAN PRZED KOREKTĄ</b>\n\n"), normalny))
        # tresc.append(Spacer(0, cm))
        tresc.append(tabelka)
        tresc.append(Spacer(0, 0.5 * cm))

        tresc.append(Paragraph(doPDF("<b>STAN PO KOREKCIE</b>\n\n"), normalny))

        naglowek = [
            "Lp.",
            "Nazwa",
            "PKWiU",
            "Ilość",
            "J.m.",
            "Cena netto",
            "Wartość netto",
            "VAT (%)",
            "Kwota VAT",
            "Wartość brutto",
        ]
        dane = []
        dane.append([Paragraph(doPDF("" + x + ""), srodekwtabelce) for x in naglowek])
        lp = 1
        podsumy = None
        for pozycja in self.pozycje_korekta:
            pozycja = pozycja.dajDaneDoWydruku()
            pozycja[0:0] = [lp]
            pozycja[1] = Paragraph(doPDF(pozycja[1]), wtabelce)
            if pozycja[3] != round(pozycja[3]):
                # nie, tak sie nie da:
                # pozycja[3]=locale.format("%F", round(pozycja[3], 3))
                precyzja = len(str(pozycja[3] - int(pozycja[3]))) - 2
                precyzja = min(3, precyzja)
                format_string = "%." + str(precyzja) + "F"
                pozycja[3] = locale.format(format_string, pozycja[3])
            else:
                pozycja[3] = int(pozycja[3])
            pozycja[4] = Paragraph(doPDF(pozycja[4]), srodekwtabelcecond)
            pozycja[5] = locale.format("%5.2f", float(pozycja[5] or 0))
            pozycja[6] = locale.format("%5.2f", float(pozycja[6] or 0))
            if isinstance(pozycja[7], float) or isinstance(pozycja[7], int):
                pozycja[7] = "%2d" % pozycja[7]
            pozycja[8] = locale.format("%5.2f", float(pozycja[8] or 0))
            pozycja[9] = locale.format("%5.2f", float(pozycja[9] or 0))
            dane.append(pozycja)
            lp += 1
            podsumy = self.dajPodsumyKorekty()
        if podsumy:
            razem_korekta = podsumy[-1][-1]
            pozostale_netto = Decimal("%f" % podsumy[-1][1])
            pozostale_brutto = Decimal("%f" % podsumy[-1][4])
            pozostaly_vat = Decimal("%f" % podsumy[-1][3])
            podsumy[0][0] = "wg stawek:"
            for pozycja in podsumy:
                pozycja[0:0] = 5 * [""]
                pozycja[6] = locale.format("%5.2f", float(pozycja[6] or 0))
                if isinstance(pozycja[7], float) or isinstance(pozycja[7], int):
                    pozycja[7] = "%2d" % pozycja[7]
                pozycja[8] = locale.format("%5.2f", float(pozycja[8] or 0))
                pozycja[9] = locale.format("%5.2f", float(pozycja[9] or 0))
                dane.append(pozycja)
            for i in range(6, 10):
                dane[-1][i] = Paragraph(
                    doPDF("<b>" + dane[-1][i] + "</b>"), prawowtabelce
                )
            dane[-1][7] = Paragraph("", normalny)
            dane[lp][5] = Paragraph(doPDF(dane[lp][5]), normalny)
            dane[-1][5] = Paragraph(doPDF("<b>" + dane[-1][5] + "</b>"), normalnyprawo)

            for zaliczka in self.zaliczki:
                pozostale_netto -= zaliczka.brutto - zaliczka.vat
                pozostale_brutto -= zaliczka.brutto
                pozostaly_vat -= zaliczka.vat
                vat = locale.format("%5.2f", zaliczka.vat)
                brutto = locale.format("%5.2f", zaliczka.brutto)
                netto = locale.format("%5.2f", zaliczka.brutto - zaliczka.vat)
                dane.append(
                    [
                        "",
                        "",
                        "",
                        "",
                        "",
                        "- zaliczka (" + zaliczka.nazwa + ")",
                        netto,
                        "",
                        vat,
                        brutto,
                    ]
                )
            if len(self.zaliczki) > 0:
                dane.append(
                    [
                        "",
                        "",
                        "",
                        "",
                        "",
                        Paragraph(doPDF("<b>POZOSTAŁA&nbsp;KWOTA:</b>"), normalny),
                        Paragraph(
                            doPDF(
                                "<b>" + locale.format("%5.2f", pozostale_netto) + "</b>"
                            ),
                            prawowtabelce,
                        ),
                        "",
                        Paragraph(
                            doPDF(
                                "<b>" + locale.format("%5.2f", pozostaly_vat) + "</b>"
                            ),
                            prawowtabelce,
                        ),
                        Paragraph(
                            doPDF(
                                "<b>"
                                + locale.format("%5.2f", pozostale_brutto)
                                + "</b>"
                            ),
                            prawowtabelce,
                        ),
                    ]
                )
                razem_korekta = pozostale_brutto

        tabelka = Table(
            dane,
            colWidths=[
                0.6 * cm,
                6.2 * cm,
                1.3 * cm,
                1 * cm,
                1.2 * cm,
                2 * cm,
                1.9 * cm,
                0.9 * cm,
                1.9 * cm,
                1.9 * cm,
            ],
        )
        tabelka.setStyle(
            TableStyle(
                [
                    ("FONT", (0, 0), (-1, 0), "Regular", 10, 12),
                    ("FONT", (0, 1), (-1, -1), "Condensed", 10, 12),
                    ("VALIGN", (0, 0), (-1, -1), "MIDDLE"),
                    ("ALIGN", (5, 1), (-1, -1), "RIGHT"),
                    ("ALIGN", (0, 1), (0, -1), "RIGHT"),
                    ("ALIGN", (3, 1), (3, -1), "RIGHT"),
                    ("GRID", (0, 0), (-1, lp - 1), 0.1, black),
                    ("GRID", (6, lp - 1), (-1, -1), 0.1, black),
                    ("LEFTPADDING", (0, 0), (-1, -1), 3),
                    ("RIGHTPADDING", (0, 0), (-1, -1), 3),
                ]
            )
        )

        tresc.append(tabelka)
        tresc.append(Spacer(0, 0.5 * cm))

        cena_bilans = self.faktura_korekta["cena_bilans"]

        if cena_bilans <= 0:
            if cena_bilans < 0:
                cena_bilans = cena_bilans * -1
            tekst_bilans = "Do zwrotu"
        else:
            tekst_bilans = "Do dopłaty"

        tresc.append(
            Paragraph(
                doPDF(
                    "<b>%s: %s zł</b>"
                    % (tekst_bilans, locale.format("%5.2f", cena_bilans))
                ),
                wielki,
            )
        )
        tresc.append(Spacer(0, 0.5 * cm))
        tresc.append(
            Paragraph(
                doPDF(
                    "<b>Słownie: %s i %s</b>"
                    % (
                        slownieZlotych(int(cena_bilans)),
                        slownieGroszy(
                            int(round((cena_bilans - int(cena_bilans)) * 100))
                        ),
                    )
                ),
                normalny,
            )
        )
        # tresc.append(Spacer(0,0.5*cm))
        # tresc.append(Paragraph(doPDF(u"Forma płatności: <b>"+self.platnosc+"</b>"),normalny))
        # if self.platnosc!=u"gotówka":
        #     if self.konto==None:
        #         self.konto=self.sprzedawca.konto
        #     tresc.append(Paragraph(doPDF("Nr konta: "+str(self.konto)),normalny))
        #     if self.uwagi is not None and self.uwagi != '':
        #         tresc.append(Spacer(0,0.5*cm))
        #         tresc.append(Paragraph(doPDF(u"Uwagi: <b>"+self.uwagi+"</b>"),normalny))
        tresc.append(Spacer(0, 0.4 * cm))

        tresc.append(
            Paragraph(
                doPDF(
                    "Przyczyna korekty: <b>"
                    + self.faktura_korekta["faktura_powod_korekty"]
                    + "</b>"
                ),
                normalny,
            )
        )
        if self.uwagi is not None and self.uwagi != "":
            tresc.append(Spacer(0, 0.2 * cm))
            tresc.append(Paragraph(doPDF("Uwagi: <b>" + self.uwagi + "</b>"), normalny))

        tresc.append(Spacer(0, 2.5 * cm))
        f.addFromList(tresc, wydruk)

        # podpisy = Table(
        #     [
        #         [
        #             XPreformatted(
        #                 doPDF(
        #                     "imię i nazwisko osoby upoważnionej\ndo odbioru faktury %s"
        #                     % typ_slownie
        #                 ),
        #                 srodekwtabelce,
        #             ),
        #             XPreformatted(
        #                 doPDF(
        #                     "imię i nazwisko osoby upoważnionej\ndo wystawienia faktury %s"
        #                     % typ_slownie
        #                 ),
        #                 srodekwtabelce,
        #             ),
        #         ]
        #     ],
        #     colWidths=[10.5 * cm, 9.5 * cm],
        # )
        #
        # w, h = podpisy.wrapOn(wydruk, 21 * cm, 10 * cm)
        # podpisy.drawOn(wydruk, 0, 3.5 * cm)

        podstawaWystawieniaTekst = Paragraph(
            doPDF(
                "Podstawa do wystawienia faktury VAT bez podpisu osoby upoważnionej do jej wystawienia oraz odbioru: "
                "Rozporządzenie Ministra Finansów z 27 kwietnia 2004 roku (Dz. U. Nr. 97 poz. 971)."
            ),
            normalny,
        )
        w, h = podstawaWystawieniaTekst.wrapOn(wydruk, 17 * cm, 3 * cm)
        podstawaWystawieniaTekst.drawOn(wydruk, 1 * cm, 4 * cm)

        podstawa2 = Paragraph(
            doPDF(
                "Na podst. art. 13 ustawy z dn. 27.11.2020 r. o zmianie ust. o podatku "
                "od towarów i usług oraz niektórych innych ustaw, niniejsze Porozumienie "
                "ustala, iż uczestnicy transakcji handlowych rozliczają podatek "
                "należny/naliczony obniżający podstawę opodatkowania, wynikający z "
                "wystawionych od dnia 01.01.2021 faktur korygujących według Ustawy z "
                "dnia 11.03.2004 r. o podatku od towarów i usług, zgodnie z przepisami "
                "obowiązującymi na dzień 31.12.2020 r."
            ),
            normalny,
        )

        w, h = podstawa2.wrapOn(wydruk, 19 * cm, 3 * cm)
        podstawa2.drawOn(wydruk, 1 * cm, 1.5 * cm)

        wydruk.showPage()

    def dajWydruk(self, nazwa, tryb, typ_faktury, sterta=None):
        if sterta is None:
            wydruk = Canvas(nazwa, pagesize=A4)
        else:
            wydruk = sterta.wydruk
        if typ_faktury == 1:
            if tryb == 0 or tryb == 1:
                self.dajStroneWydruku(wydruk, typ_faktury, True)
            if tryb == 0 or tryb == 2:
                self.dajStroneWydruku(wydruk, typ_faktury, False)
        else:
            self.dajStroneWydruku(wydruk, typ_faktury, True)
        if sterta is None:
            wydruk.save()
