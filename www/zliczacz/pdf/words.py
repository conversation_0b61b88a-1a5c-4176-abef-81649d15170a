_setkiSlownie = [
    "",
    "sto ",
    "dwieście ",
    "trzysta ",
    "cz<PERSON><PERSON> ",
    "pię<PERSON>set ",
    "sze<PERSON><PERSON>set ",
    "siedemset ",
    "osiemset ",
    "dziewięćset ",
]
_dziesSlownie = [
    "",
    "",
    "dwad<PERSON><PERSON><PERSON> ",
    "trzyd<PERSON><PERSON><PERSON> ",
    "cz<PERSON><PERSON><PERSON><PERSON><PERSON> ",
    "pięćdziesiąt ",
    "sze<PERSON>ćd<PERSON><PERSON>t ",
    "siedemdziesiąt ",
    "osiemdziesiąt ",
    "dziewięćdziesiąt ",
]
_nastkiSlownie = [
    "dziesi<PERSON>ć ",
    "jedenaście ",
    "dwanaście ",
    "trzynaście ",
    "czternaście ",
    "piętna<PERSON>cie ",
    "szesnaście ",
    "siedemnaście ",
    "osiemnaście ",
    "dziewiętnaście ",
]
_jednSlownie = [
    "",
    "jeden ",
    "dwa ",
    "trzy ",
    "cztery ",
    "pię<PERSON> ",
    "sze<PERSON><PERSON> ",
    "siedem ",
    "osiem ",
    "dziewi<PERSON><PERSON> ",
]

_tysiace = ["tysiąc ", "tysi<PERSON>cy ", "tysiące "]
_miliony = ["milion ", "milionów ", "miliony "]
_zlote = ["złotych ", "złotych ", "złote ", "złoty "]
_grosze = ["groszy ", "groszy ", "grosze ", "grosz "]


def _slownieDoTysiaca(liczba):
    if liczba > 999:
        raise Exception("To sie nie moglo zdarzyc")
    jedn = int(liczba % 10)
    dzies = int(((liczba - jedn) % 100) / 10)
    setki = int((liczba - dzies * 10 - jedn) / 100)
    if liczba == 1:
        wersja = 0
    elif jedn > 1 and jedn < 5:
        wersja = 2
    else:
        wersja = 1
    wynik = ""
    wynik += _setkiSlownie[setki]
    if dzies == 1:
        wynik += _nastkiSlownie[jedn]
        wersja = 1
    else:
        wynik += _dziesSlownie[dzies]
        wynik += _jednSlownie[jedn]
    return (wynik, wersja)


def _slownie(liczba, tab):
    if liczba == 1:
        return "jeden %s" % tab[3]
    elif liczba == 0:
        return "zero %s" % tab[0]
    jedn = int(liczba % 1000)
    tysiace = int(((liczba - jedn) / 1000) % 1000)
    miliony = int((liczba - 1000 * tysiace - jedn) / 1000000)
    if miliony > 999:
        raise Exception("za duża liczba")
    (wynik, wersja) = _slownieDoTysiaca(miliony)
    if len(wynik) != 0:
        wynik += _miliony[wersja]
    (wynikpom, wersja) = _slownieDoTysiaca(tysiace)
    if len(wynikpom) != 0:
        wynik += wynikpom
        wynik += _tysiace[wersja]
    (wynikpom, wersja) = _slownieDoTysiaca(jedn)
    if len(wynikpom) == 0:
        wynik += tab[0]
    else:
        wynik += wynikpom
        wynik += tab[wersja]
    return wynik


def slownieZlotych(liczba):
    return _slownie(liczba, _zlote)


def slownieGroszy(liczba):
    return _slownie(liczba, _grosze)
