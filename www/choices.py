import unicodedata

from django.utils.encoding import force_text

sort_key = lambda item: (
    unicodedata.normalize("NFKD", item[1]).encode("ascii", "ignore").decode("ascii")
)

COMMON_COUNTRIES = {
    "PL": "Pol<PERSON>",
    "GB": "Wielka Brytania",
    "DE": "Niemcy",
}

COUNTRIES = {
    "AF": "Afganistan",
    "AX": "Wyspy Alandzkie",
    "AL": "Albania",
    "DZ": "Algeria",
    "AS": "Samoa Amerykańskie",
    "AD": "Andora",
    "AO": "Angola",
    "AI": "Anguilla",
    "AQ": "Antarktyda",
    "AG": "Antigua i Barbuda",
    "AR": "Argentyna",
    "AM": "Armenia",
    "AW": "Aruba",
    "AU": "Australia",
    "AT": "Austria",
    "AZ": "Azerbejdżan",
    "BS": "Bahamy",
    "BH": "<PERSON><PERSON><PERSON>",
    "BD": "<PERSON><PERSON><PERSON>",
    "BB": "Barbados",
    "BY": "Białoruś",
    "BE": "Belgia",
    "BZ": "Belize",
    "BJ": "Benin",
    "BM": "Bermudy",
    "BT": "Bhutan",
    "BO": "Boliwia",
    "BQ": "Bonaire, Sint Eustatius and Saba",
    "BA": "Bośnia i Hercegowina",
    "BW": "Botswana",
    "BV": "Wyspa Bouveta",
    "BR": "Brazylia",
    "IO": "Brytyjskie Terytorium Oceanu Indyjskiego",
    "BN": "Brunei",
    "BG": "Bułgaria",
    "BF": "Burkina Faso",
    "BI": "Burundi",
    "CV": "Cabo Verde",
    "KH": "Kambodża",
    "CM": "Kamerun",
    "CA": "Kanada",
    "KY": "Kajmany",
    "CF": "Republika Środkowoafrykańska",
    "TD": "Czad",
    "CL": "Chile",
    "CN": "Chiny",
    "CX": "Wyspa Bożego Narodzenia",
    "CC": "Wyspy Kokosowe",
    "CO": "Kolumbia",
    "KM": "Komory",
    "CG": "Kongo",
    "CD": "Republika Kongo",
    "CK": "Wyspy Cooka",
    "CR": "Kostaryka",
    "CI": "Wybrzeże Kości Słoniowej",
    "HR": "Chorwacja",
    "CU": "Kuba",
    "CW": "Curaçao",
    "CY": "Cyprs",
    "CZ": "Czechy",
    "DK": "Dania",
    "DJ": "Dżibuti",
    "DM": "Dominika",
    "DO": "Dominikana",
    "EC": "Ekwador",
    "EG": "Egipt",
    "SV": "Salwador",
    "GQ": "Gwinea Równikowa",
    "ER": "Erytrea",
    "EE": "Estonia",
    "ET": "Etiopia",
    "FK": "Falklandy",
    "FO": "Wyspy Owcze",
    "FJ": "Fidżi",
    "FI": "Finlandia",
    "FR": "Francja",
    "GF": "Gujana Francuska",
    "PF": "Polinezja Francuska",
    "TF": "Francuskie Terytoria Południowe i Antarktyczne",
    "GA": "Gabon",
    "GM": "Gambia",
    "GE": "Gruzja",
    "GH": "Ghana",
    "GI": "Gibraltar",
    "GR": "Grecja",
    "GL": "Grenlandia",
    "GD": "Grenada",
    "GP": "Gwadelupa",
    "GU": "Guam",
    "GT": "Gwatemala",
    "GG": "Guernsey",
    "GN": "Gwinea",
    "GW": "Gwinea Bissau",
    "GY": "Gujana",
    "HT": "Haiti",
    "HM": "Wyspy Heard i McDonalda",
    "VA": "Watykan",
    "HN": "Honduras",
    "HK": "Hong Kong",
    "HU": "Węgry",
    "IS": "Islandia",
    "IN": "Indie",
    "ID": "Indonezja",
    "IR": "Iran",
    "IQ": "Irak",
    "IE": "Irlandia",
    "IM": "Wyspa Man",
    "IL": "Izrael",
    "IT": "Włochy",
    "JM": "Jamajka",
    "JP": "Japonia",
    "JE": "Jersey",
    "JO": "Jordania",
    "KZ": "Kazachstan",
    "KE": "Kenia",
    "KI": "Kiribati",
    "KP": "Korea Północna",
    "KR": "Korea Południowa",
    "KW": "Kuwejt",
    "KG": "Kirgistan",
    "LA": "Laos",
    "LV": "Łotwa",
    "LB": "Liban",
    "LS": "Lesotho",
    "LR": "Liberia",
    "LY": "Libia",
    "LI": "Liechtenstein",
    "LT": "Litwa",
    "LU": "Luksembourg",
    "MO": "Makau",
    "MK": "Macedonia",
    "MG": "Madagaskar",
    "MW": "Malawi",
    "MY": "Malezja",
    "MV": "Malediwy",
    "ML": "Mali",
    "MT": "Malta",
    "MH": "Wyspy Marshalla",
    "MQ": "Martynika",
    "MR": "Mauritania",
    "MU": "Mauritius",
    "YT": "Majotta",
    "MX": "Meksyk",
    "FM": "Mikronezja",
    "MD": "Mołdawia",
    "MC": "Monako",
    "MN": "Mongolia",
    "ME": "Czarnogóra",
    "MS": "Montserrat",
    "MA": "Maroko",
    "MZ": "Mozambik",
    "MM": "Mjanma",
    "NA": "Nambia",
    "NR": "Nauru",
    "NP": "Nepal",
    "NL": "Holandia",
    "NC": "Nowa Kaledonia",
    "NZ": "Nowa Zelandia",
    "NI": "Nikaragua",
    "NE": "Niger",
    "NG": "Nigeria",
    "NU": "Niue",
    "NF": "Norfolk",
    "MP": "Mariany Północne",
    "NO": "Norwegia",
    "OM": "Oman",
    "PK": "Pakistan",
    "PW": "Palau",
    "PS": "Palestine, State of",
    "PA": "Palestyna",
    "PG": "Papua-Nowa Gwinea",
    "PY": "Paragwaj",
    "PE": "Peru",
    "PH": "Filipiny",
    "PN": "Pitcairn",
    "PT": "Portugalia",
    "PR": "Portoryko",
    "QA": "Katar",
    "RE": "Reunion",
    "RO": "Rumunia",
    "RU": "Rosja",
    "RW": "Rwanda",
    "BL": "Saint Barthélemy",
    "SH": "Wyspa Świętej Heleny, Wyspa Wniebowstąpienia i Tristan da Cunha",
    "KN": "Saint Kitts i Nevis",
    "LC": "Saint Lucia",
    "MF": "Saint-Martin",
    "PM": "Saint Pierre i Miquelon",
    "VC": "Saint Vincent i Grenadyny",
    "WS": "Samoa",
    "SM": "San Marino",
    "ST": "Wyspy Świętego Tomasza i Książęca",
    "SA": "Arabia Saudyjska",
    "SN": "Senegal",
    "RS": "Serbia",
    "SC": "Seszele",
    "SL": "Sierra Leone",
    "SG": "Singapur",
    "SX": "Sint Maarten",
    "SK": "Słowacja",
    "SI": "Słowenia",
    "SB": "Wyspy Salomona",
    "SO": "Somalia",
    "ZA": "Republika Południowej Afryki",
    "GS": "Georgia Południowa i Sandwich Południowy",
    "SS": "Sudan Południowy",
    "ES": "Hiszpania",
    "LK": "Sri Lanka",
    "SD": "Sudan",
    "SR": "Suriname",
    "SJ": "Svalbard i Jan Mayen",
    "SZ": "Suazi",
    "SE": "Szwecja",
    "CH": "Szwajcaria",
    "SY": "Syria",
    "TW": "Tajwan",
    "TJ": "Tadżykistan",
    "TZ": "Tanzania",
    "TH": "Tajlandia",
    "TL": "Timor Wschodni",
    "TG": "Togo",
    "TK": "Tokelau",
    "TO": "Tonga",
    "TT": "Trynidad i Tobago",
    "TN": "Tunezja",
    "TR": "Turkcja",
    "TM": "Turkmenistan",
    "TC": "Turks i Caicos",
    "TV": "Tuvalu",
    "UG": "Uganda",
    "UA": "Ukraina",
    "AE": "Zjednoczone Emiraty Arabskie",
    "US": "Stany Zjednoczone",
    "UM": "Dalekie Wyspy Mniejsze Stanów Zjednoczonych",
    "UY": "Urugwaj",
    "UZ": "Uzbekistan",
    "VU": "Vanuatu",
    "VE": "Wenezuela",
    "VN": "Wietnam",
    "VG": "Brytyjskie Wyspy Dziewicze",
    "VI": "Brytyjskie Wyspy Dziewicze",
    "WF": "Wallis i Futuna",
    "EH": "Sahara Zachodnia",
    "YE": "Jemen",
    "ZM": "Zambia",
    "ZW": "Zimbabwe",
}


countries = [(code, force_text(name)) for code, name in list(COUNTRIES.items())]

common_countries = [
    ("PL", force_text(COMMON_COUNTRIES["PL"])),
    ("GB", force_text(COMMON_COUNTRIES["GB"])),
    ("DE", force_text(COMMON_COUNTRIES["DE"])),
]

COUNTRIES_CHOICES = list(common_countries + sorted(countries, key=sort_key))
