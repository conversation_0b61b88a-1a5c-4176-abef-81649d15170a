import os

ROOT_PATH = os.path.dirname(__file__)
PROJECT_ROOT = os.path.dirname(ROOT_PATH)

DEBUG = False

ADMINS = (
    # ('Your Name', '<EMAIL>'),
    ("r<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>@g.fiok.pl"),
)

SECURE_PROXY_SSL_HEADER = ("HTTP_X_FORWARDED_PROTO", "https")

MANAGERS = ADMINS

TIME_ZONE = "Europe/Warsaw"

LANGUAGES = (
    ("pl", "polski"),
    ("en", "angielski"),
)

DEFAULT_LANGUAGE = "pl"

LANGUAGE_CODE = "pl"

USE_I18N = True

USE_L10N = True

FORMAT_MODULE_PATH = "i18n.formats"

LOCALE_PATHS = (os.path.join(ROOT_PATH, "locale"),)

# Absolute path to the directory that holds media.
# Example: "/home/<USER>/media.lawrence.com/"
MEDIA_ROOT = os.path.join(ROOT_PATH, "uploads")
ASSETS_ROOT = os.path.join(ROOT_PATH, "media")

PRIVATE_MEDIA_ROOT = os.path.join(ROOT_PATH, "uploads_restricted")

# URL that handles the media served from MEDIA_ROOT. Make sure to use a
# trailing slash if there is a path component (optional in other cases).
# Examples: "http://media.lawrence.com", "http://example.com/media/"
MEDIA_URL = "/non-existent/"
ASSETS_URL = "/media/"

STATIC_URL = "/static/"
STATIC_ROOT = os.path.join(ROOT_PATH, "static_collected")
STATICFILES_DIRS = [os.path.join(ROOT_PATH, "static")]

# Make this unique, and don't share it with anybody.
SECRET_KEY = "..."

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [
            os.path.join(ROOT_PATH, "wynajmy/templates"),
            os.path.join(ROOT_PATH, "www/templates"),
            os.path.join(ROOT_PATH, "ankiety/templates"),
            os.path.join(ROOT_PATH, "crm/templates"),
            os.path.join(ROOT_PATH, "forum/templates"),
            os.path.join(ROOT_PATH, "newsletter/templates"),
            os.path.join(ROOT_PATH, "search/templates"),
            os.path.join(ROOT_PATH, "leads/templates"),
            os.path.join(ROOT_PATH, "csvimport/templates"),
        ],
        # "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "www.context_processors.common_context_processor",
                "www.context_processors.debug",
                "headers.context_processors.dynamic_headers",
                "alx_auth.context_processors.password_remind_info",
                "django.contrib.auth.context_processors.auth",
                "django.template.context_processors.i18n",
                "django.template.context_processors.media",
                "django.template.context_processors.static",
                "django.template.context_processors.tz",
                "django.contrib.messages.context_processors.messages",
                "django.template.context_processors.request",
            ],
            "debug": DEBUG,
            "loaders": [
                (
                    "django.template.loaders.cached.Loader",
                    [
                        "django.template.loaders.filesystem.Loader",
                        "django.template.loaders.app_directories.Loader",
                    ],
                ),
            ],
        },
    }
]

MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    # Early guard to avoid any redirects on bare tech roots
    "middleware.tech.TechRootGuardMiddleware",
    "django.middleware.locale.LocaleMiddleware",
    "django.middleware.common.CommonMiddleware",
    "middleware.robots.RobotsNoindexPDFMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "middleware.common.ThreadLocalMiddleware",
    "middleware.admin.AdminLocaleMiddleware",
    "middleware.admin.AdminSearchTermLengthMiddleware",
]

SESSION_COOKIE_HTTPONLY = True
SESSION_COOKIE_AGE = 10 * 60 * 60  # 10h
SESSION_EXPIRE_AT_BROWSER_CLOSE = True
SESSION_COOKIE_SECURE = True

ROOT_URLCONF = "alxcrm.urls"

INSTALLED_APPS = [
    "django.contrib.auth",
    "django.contrib.admin",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.sitemaps",
    "django.contrib.staticfiles",
    "haystack",
    "django_ace",
    "sortedm2m",
    "localflavor",
    "wkhtmltopdf",
    "nocaptcha_recaptcha",
    "stdimage",
    "django_admin_multiple_choice_list_filter",
    "rangefilter",

    "django_select2",

    "alxcrm",
    "bin",
    "alx_auth",
    "i18n",
    "www",
    "search",
    "forum",
    "captcha",
    "newsletter",
    "ankiety",
    "wynajmy",
    "crm",
    "leads",
    "optout",
    "csvimport",
    "blacklist",
    "headers",

    # "django_extensions",
]

CAPTCHA_FONT_PATH = os.path.join(ROOT_PATH, "assets/freefonts")

PDF_HELPERS_PATH = os.path.join(ROOT_PATH, "assets/pdf")

MAIL_FROM_ADDRESS = "..."
MAIL_FROM_ADDRESS_ZGLOSZENIE = "..."
MAIL_FROM_ADDRESS_ZGLOSZENIE_EN = "..."
MAIL_FROM_ADDRESS_ZAPYTANIE = "..."
MAIL_FROM_ADDRESS_NOTIFICATION = "..."
MAIL_FROM_ADDRESS_NOTIFICATION_EN = "..."
MAIL_CONTACT_FORM_TO_ADDRESS = "..."
MAIL_FROM_ADDRESS_AUTOREPLAY = "..."  # <AUTHOR> <EMAIL>'
MAIL_FROM_ADDRESS_AUTOREPLAY_EN = (
    "..."  # <AUTHOR> <EMAIL>'
)
MAIL_FROM_ADDRESS_POTWIERDZENIE_TERMINU = "..."
ALLOWED_MAIL_CONTACT_FORM_TO_ADDRESSES = []

MAIL_ZGLOSZENIE_TO_ADDRESS = "..."
MAIL_FORUM_TO_ADDRESS = "..."
MAIL_TERMINY = "..."
MAIL_WINDYKACJA = "..."
MAIL_CERTYFIKATY = "..."
MAIL_MATERIALY = "..."
MAIL_NIEPRZYGOTOWANE_SALE = "..."
MAIL_POTENCJALNIE_CHETNI = ""
MAIL_OBSLUGA = "..."
MAIL_PLATNOSCI = "..."
MAIL_PLATNOSCI_MONITOR = "..."  # <EMAIL>
MAIL_ADMINI = "..."  # <EMAIL>
MAIL_KSIEGOWOSC = "..."  # <EMAIL>
MAIL_ARCHIVE_MONITOR = "..."  # <EMAIL>

# EMAIL_ADDR = {
#     'nowy_projekt': '<EMAIL>',
#     'nowy_pracownik': '<EMAIL>',
#     'kalendarze': '<EMAIL>',
#     'windykacja': '<EMAIL>',
#     'terminowosc': '<EMAIL>',
#     'dyzury_serwisu': '<EMAIL>',
#     'dyzury_biura': '<EMAIL>',
#     'freeconet': '<EMAIL>',
#     'kadry': '<EMAIL>',
#     'normy_godzinowe': '<EMAIL>',
#     'niezalatwione_zapytania': '<EMAIL>',
# }

EMAIL_HOST = "email.host"

SHOW_ANALYTICS = False

EMAIL_BACKEND = "django.core.mail.backends.console.EmailBackend"

UPDATE_CALENDARS_LOCK_FILE = "~/google-calendar.lock"

# USE_X_FORWARDED_HOST = True

SECURE_SSL_REDIRECT = True
FORCE_SSL = True

NEWSLETTER_FROM_ADDRESS = "<EMAIL>"

FIRST_DAY_OF_WEEK = 1

AUTHENTICATION_BACKENDS = [
    "alx_auth.backends.LDAPAuthBackend",
]

# Ustawienia w przypadku korzystania z LDAPa.
DNI_WAZNOSCI_HASLA = 30
DNI_UCISZENIA_PRZYPOMNIENIA_ZMIANY_HASLA = 30
URL_ZMIANY_HASLA = "https://mail.alx.pl/"

LISTA_OBECNOSCI_TEMPLATE = os.path.join(ROOT_PATH, "assets/lista_obecnosci.docx")

GENERATE_INVOICE_THROUGH_API_ZLICZACZ = True

API_ZLICZACZ = {
    "CREDENTIALS": {
        "USERNAME": "",
        "PASSWORD": "",
    },
    "INVOICES": {
        "ADD_INVOICE_URL": "",
        "UPDATE_INVOICE_URL": "",
        "GET_INVOICE_PDF_URL": "",
        "GET_INVOICE_URL": "",
        "LIST_INVOICE_URL": "",
        "SEARCH_INVOICE_URL": "",
    },
}

CELERY_BROKER_URL = "amqp://localhost"
CELERYD_TASK_SOFT_TIME_LIMIT = 30 * 60  # 30 minutes
CELERYD_TASK_TIME_LIMIT = CELERYD_TASK_SOFT_TIME_LIMIT + 15
CELERYD_HIJACK_ROOT_LOGGER = False
CELERY_DISABLE_RATE_LIMITS = True
CELERY_IGNORE_RESULT = True

# Minimalna cena brutto szkolenia, przy której nie ma dopłaty za papierową fakturę.
MINIMALNA_CENA_NETTO_SZKOLENIA_DLA_DARMOWEJ_FAKTURY = {
    "pl": 813,
    "en": 813,
}
CENA_NETTO_PAPIEROWEJ_FAKTURY = {
    "pl": 9,
    "en": 14,
}
CENA_NETTO_DRUKOWANEGO_CERTYFIKATU = {
    "pl": 37,
    "en": 19,
}

NIEPRZYGOTOWANE_SALE_CZAS = 7

# Zmienna określa interwał pomiędzy kolejnymi powiadomieniami o nowych
# terminach wysyłanymi do użytkowników.
USER_NOTIFICATION_SENDING_INTERVAL_HOURS = 48  # w godzinach

# Zmienna określa maksymalny "wiek" terminu szkolenia jaki będzie brany pod
# uwagę wyszukując nieprzeszkolonych użytkowników.
UNTRAINED_USERS_MAX_TRAINING_AGE = 180  # w dniach

# Zmienna określa maksymalny "wiek" zapisu na powiadomienia dokonanego przez
# automat, po którym może zostać usunięty.
EXPIRED_NOTIFICATIONS_MAX_AGE = 360  # w dniach

# Zmienna określa czas po jakim system wyśle ponownie maila aktywacyjnego dla
# niepotwierdzonych powiadomień.
USER_NOTIFICATION_RESEND_ACTIVATION_EMAIL = 48  # w godzinach

# Zmienna określa czas po jakim system oznaczy niepowterdzone powiadomienia
# jako "nigdy niepotwierdzone" (Uwaga: liczymy czas od
# USER_NOTIFICATION_RESEND_ACTIVATION_EMAIL).
USER_NOTIFICATION_MARK_AS_NEVER_ACTIVATED = 24 * 5  # w godzinach

# Ścieżka do JSON secrets Google API wygenerowanego w console.developers.google.com
GOOGLE_API_PRIVATE_KEY_PATH = ""

# Adres email do alertów powiadomień. Jeśli pusty alerty nie są wysyłane.
MAIL_TO_NOTIFICATION_ALERT = ""

# ID aplikacji facebook, która będzie zbierać statystyki sharowania
# certyfikatów.
SHARING_FACEBOOK_APP_ID = "123"

# URL wygenerowany przez linkedin do dodawania certyfikatu. Pomijamy dynamiczne
# parametry w URL. Przykład:
# https://www.linkedin.com/profile/add?_ed=0_g6(...)VnTfEs2C9ma__75hCVwJdOTZdv
LINKEDIN_CERTIFICATE_URL = ""

# Sciezka do wkhtmltopdf
WKHTMLTOPDF_STATIC_URL = "https://www.alx.pl/static/"
WKHTMLTOPDF_CMD = "/usr/bin/wkhtmltopdf"
WKHTMLTOPDF_CMD_OPTIONS = {
    "encoding": "utf8",
    "quiet": True,
    # 'disable-smart-shrinking': True,
}

# Google reCaptcha
NORECAPTCHA_SITE_KEY = ""
NORECAPTCHA_SECRET_KEY = ""

NORECAPTCHA_INVISIBLE_SITE_KEY = ""
NORECAPTCHA_INVISIBLE_SECRET_KEY = ""

NORECAPTCHA_WIDGET_TEMPLATE = "www/recaptcha/widget.html"

# Postivo
POSTIVO_SOAP_WSDL = ""
POSTIVO_LOGIN = ""
POSTIVO_PASSWORD = ""
POSTIVO_CONFIG_ID = ""
POSTIVO_SENDER_ID = ""

HAYSTACK_CONNECTIONS = {
    "default": {
        "ENGINE": "haystack.backends.whoosh_backend.WhooshEngine",
        "PATH": os.path.join(ROOT_PATH, "whoosh_index"),
        "INCLUDE_SPELLING": False,
    },
}

DATA_UPLOAD_MAX_NUMBER_FIELDS = 10000

MAX_KURSANTOW_TOTAL_OLD = 15  # pojemnosc_sali sprzed hybryd
MAX_KURSANTOW_TOTAL = 17
MAX_KURSANTOW_STACJONARNIE = 12

FILE_UPLOAD_PERMISSIONS = 0o644

SALA_ZDALNA_IDS = {
    "pl": 11,
    "en": 13,        
}

# limit frazy wyszukiwania w panelu admina - oznacza max ilość słów we frazie wyszukiwania
ADMIN_QUERY_LIMIT = 5

from local_settings import *
