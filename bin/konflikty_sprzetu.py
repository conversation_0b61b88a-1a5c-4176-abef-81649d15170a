#!/usr/bin/env python
import datetime
import os

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "settings")

import django

django.setup()

from django.conf import settings
from django.core.mail import send_mail
from django.db.models import Q
from django.template.loader import render_to_string

from i18n.models import WersjaJezykowa
from www.models import TerminSzkolenia


def get_terms():
    # Pobieramy terminy, które mają przypisany zestaw komputerowy oznaczony
    # jako `sprawdzaj_konflikty=True`.
    # Uwaga: Zestaw może być główny, lub ten ustawiony w przeciążonych dniach
    # szkolenia.

    terminy = (
        TerminSzkolenia.objects.filter(
            termin__gte=datetime.date.today(), odbylo_sie=True
        )
        .filter(
            Q(sprzet__sprawdzaj_konflikty=True)
            | Q(dni_szkolenia__sprzet__sprawdzaj_konflikty=True)
        )
        .distinct()
    )  # Bez trybów wieczorowych

    dates = {}

    for termin in terminy:
        sprzet_per_dni = termin.sprzet_per_dni()

        for row in list(sprzet_per_dni.values()):
            if row["sprzet"].sprawdzaj_konflikty:
                for d in row["dni"]:
                    key = (d, row["sprzet"])
                    if not (key in dates):
                        dates[key] = []
                    dates[key].append(termin)
    return dates


def get_conflicts():
    conflicts = []

    dates = get_terms()

    if dates:
        for k in sorted(dates.keys(), key=lambda x: x[0]):
            terminy = set(dates[k])
            terminy_ids = [t.pk for t in terminy]

            for t in [termin for termin in terminy if bool(termin.termin_nadrzedny)]:
                if t.termin_nadrzedny_id in terminy_ids:
                    terminy.remove(t)
            if len(terminy) > 1:
                t = list(terminy)

                # Przypadek, gdy sa dwa terminy i jeden jest wieczorowy,
                # wtedy puszczamy, bo są rozne godziny.
                if len(t) == 2 and (
                    (t[0].tryb == 2 and t[1].tryb != 2)
                    or (t[0].tryb != 2 and t[1].tryb == 2)
                ):
                    continue
                conflicts.append((k, terminy))
    return conflicts


if __name__ == "__main__":
    conflicts = get_conflicts()

    if conflicts:
        site_url = "https://" + WersjaJezykowa.biezaca().domain

        mesg = render_to_string(
            "www/konflikty_sprzetu.txt",
            {
                "conflicts": conflicts,
                "site_url": site_url,
            },
        )
        send_mail(
            "Konflikt sprzetu",
            mesg,
            settings.MAIL_FROM_ADDRESS,
            [settings.MAIL_TERMINY],
        )
