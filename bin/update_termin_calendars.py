#!/usr/bin/env python
import datetime
import logging
import os
import signal
import sys
import time
import traceback

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "settings")

import django

django.setup()

from django.conf import settings
from google.oauth2 import service_account
from googleapiclient.discovery import build
from googleapiclient.http import HttpError

from www.kalendarze import really_delete_event, really_set_event_from_termin
from www.models import CalendarUpdate

logger = logging.getLogger("www")


SCOPES = ["https://www.googleapis.com/auth/calendar"]


def run():
    try:
        credentials = service_account.Credentials.from_service_account_file(
            settings.GOOGLE_API_PRIVATE_KEY_PATH, scopes=SCOPES
        )
        service = build("calendar", "v3", credentials=credentials)

        delta = datetime.datetime.now() - datetime.timedelta(days=14)

        CalendarUpdate.objects.filter(
            done__in=["yes", "superseded"], creation_time__lt=delta
        ).delete()

        pending_updates = list(
            CalendarUpdate.objects.filter(done="no").order_by("creation_time")
        )
        seen = {}

        for upd in pending_updates:
            if upd.termin:
                termin_id = upd.termin.id
            else:
                termin_id = upd.deleted_termin_id

            tid = upd.pk

            if "%d_%s" % (tid, upd.calendar) in seen:
                upd.done = "superseded"
            elif upd.tries > 500:
                continue
            else:
                seen["%d_%s" % (tid, upd.calendar)] = True
                upd.tries += 1
                try:
                    if upd.update_type == "update":
                        really_set_event_from_termin(
                            service, upd.calendar, upd.termin, upd.daty_szczegolowo
                        )
                    elif upd.update_type == "delete":
                        really_delete_event(
                            service, upd.calendar, upd.deleted_termin_id
                        )
                    upd.done = "yes"
                except HttpError as error:
                    upd.last_failure_reason = traceback.format_exc()

                    try:
                        http_error_reason = error.resp.reason
                    except:
                        http_error_reason = ""

                    logger.exception(
                        "Błąd przy eksporcie do Google Calendar. "
                        "Termin: {0} Calendar: {1} Tries: {2} "
                        "Last failure: {3}. HttpErrorReason: {4}".format(
                            termin_id,
                            upd.calendar,
                            upd.tries,
                            upd.last_failure_reason,
                            http_error_reason,
                        )
                    )

                    # --- To nic nie da.
                    # if http_error_reason in ['userRateLimitExceeded',
                    #                          'quotaExceeded']:
                    #     time.sleep(3)
                except:
                    upd.last_failure_reason = traceback.format_exc()
                    logger.exception(
                        "Błąd przy eksporcie do Google Calendar. "
                        "Termin: {0} Calendar: {1} Tries: {2} "
                        "Last failure: {3}".format(
                            termin_id, upd.calendar, upd.tries, upd.last_failure_reason
                        )
                    )

                # Nowy limit Google - 500 zapytań na 100 sekund.
                # Robimy z zapasem.
                time.sleep(0.25)
            try:
                upd.save()
            except:
                # w sumie to wiemy kiedy to się wywala, wtedy kiedy termin
                # zostanie skasowany, a update go dotyczący jest w trakcie
                # procesowania (albo w kolejce) więc można by przerobić to
                # pole na niebędące referencją w bazie również w przypadku
                # update'ów, a nie tylko delete'ów ale nie chce mi się w tej
                # chwili
                logger.exception(
                    "Google Calendar - błąd przy zapisywaniu statusu update'a "
                    "dla obiektu CalendarUpdate PK: {0}.".format(upd.id)
                )

    except:
        logger.exception(
            "Problem z przetwarzaniem terminu dla Google Calendar lub "
            "połączeniem z API Google"
        )


if __name__ == "__main__":

    def unlink_lock_file():
        os.unlink(settings.UPDATE_CALENDARS_LOCK_FILE)

    def sigterm_handler(signo=None, frame=None):
        unlink_lock_file()
        sys.exit(0)

    signal.signal(signal.SIGTERM, sigterm_handler)

    try:
        fd = os.open(settings.UPDATE_CALENDARS_LOCK_FILE, os.O_CREAT | os.O_EXCL)
    except OSError:
        sys.exit()

    run()

    unlink_lock_file()
