import datetime

from django.conf import settings
from django.core import mail
from django.test import override_settings

from bin import konflikty_sprzetu, konflikty_terminow
from www.testfactories import (
    DzienSzkoleniaFactory,
    SalaFactory,
    SprzetFactory,
    SzkolenieFactory,
    TerminSzkoleniaFactory,
    UczestnikFactory,
)
from www.testhelpers import ALXTestCase


class PoinformujONieprzygotowanychSalachMailTestCase(ALXTestCase):
    def utworz_termin(self, rodzaj_terminu=0, odbylo_sie=True, sala_przygotowana=False):
        today = datetime.date.today()
        if rodzaj_terminu == 0:
            termin = today + datetime.timedelta(
                days=settings.NIEPRZYGOTOWANE_SALE_CZAS / 2.0
            )
        elif rodzaj_terminu == 1:
            termin = today + datetime.timedelta(
                days=2 * settings.NIEPRZYGOTOWANE_SALE_CZAS
            )
        elif rodzaj_terminu == 2:
            termin = today + datetime.timedelta(days=-45)
        termin_szkolenia = TerminSzkoleniaFactory(
            termin=termin, odbylo_sie=odbylo_sie, sala_przygotowana=sala_przygotowana
        )
        return termin_szkolenia

    def test_wyslij_mail_o_nieprzygotowanych_salach_0(self):
        termin_tf = self.utworz_termin(
            rodzaj_terminu=0, odbylo_sie=True, sala_przygotowana=False
        )
        termin_tt = self.utworz_termin(
            rodzaj_terminu=0, odbylo_sie=True, sala_przygotowana=True
        )
        termin_ff = self.utworz_termin(
            rodzaj_terminu=0, odbylo_sie=False, sala_przygotowana=False
        )
        exec(
            compile(
                open("bin/nieprzygotowane_sale.py", "rb").read(),
                "bin/nieprzygotowane_sale.py",
                "exec",
            )
        )
        self.assertEqual(len(mail.outbox), 1)
        mail_body = mail.outbox[0].body
        self.assertIn("Sale do przygotowania - pilne:", mail_body)
        self.assertIn(
            "/admin/www/terminszkolenia/{termin_id}".format(termin_id=termin_tf.id),
            mail_body,
        )
        self.assertNotIn(
            "/admin/www/terminszkolenia/{termin_id}".format(termin_id=termin_tt.id),
            mail_body,
        )
        self.assertNotIn(
            "/admin/www/terminszkolenia/{termin_id}".format(termin_id=termin_ff.id),
            mail_body,
        )

    def test_wyslij_mail_o_nieprzygotowanych_salach_1(self):
        termin_tf = self.utworz_termin(
            rodzaj_terminu=1, odbylo_sie=True, sala_przygotowana=False
        )
        termin_tt = self.utworz_termin(
            rodzaj_terminu=1, odbylo_sie=True, sala_przygotowana=True
        )
        termin_ff = self.utworz_termin(
            rodzaj_terminu=1, odbylo_sie=False, sala_przygotowana=False
        )
        exec(
            compile(
                open("bin/nieprzygotowane_sale.py", "rb").read(),
                "bin/nieprzygotowane_sale.py",
                "exec",
            )
        )
        self.assertEqual(len(mail.outbox), 1)
        mail_body = mail.outbox[0].body
        self.assertIn("Sale do przygotowania - nie tak pilne:", mail_body)
        self.assertIn(
            "/admin/www/terminszkolenia/{termin_id}".format(termin_id=termin_tf.id),
            mail_body,
        )
        self.assertNotIn(
            "/admin/www/terminszkolenia/{termin_id}".format(termin_id=termin_tt.id),
            mail_body,
        )
        self.assertNotIn(
            "/admin/www/terminszkolenia/{termin_id}".format(termin_id=termin_ff.id),
            mail_body,
        )

    def test_wyslij_mail_o_nieprzygotowanych_salach_2(self):
        termin_tf = self.utworz_termin(
            rodzaj_terminu=2, odbylo_sie=True, sala_przygotowana=False
        )
        termin_tt = self.utworz_termin(
            rodzaj_terminu=2, odbylo_sie=True, sala_przygotowana=True
        )
        termin_ff = self.utworz_termin(
            rodzaj_terminu=2, odbylo_sie=False, sala_przygotowana=False
        )
        exec(
            compile(
                open("bin/nieprzygotowane_sale.py", "rb").read(),
                "bin/nieprzygotowane_sale.py",
                "exec",
            )
        )
        self.assertEqual(len(mail.outbox), 1)
        mail_body = mail.outbox[0].body
        self.assertIn("Sale do przygotowania - burdel do posprzątania:", mail_body)
        self.assertIn(
            "/admin/www/terminszkolenia/{termin_id}".format(termin_id=termin_tf.id),
            mail_body,
        )
        self.assertNotIn(
            "/admin/www/terminszkolenia/{termin_id}".format(termin_id=termin_tt.id),
            mail_body,
        )
        self.assertNotIn(
            "/admin/www/terminszkolenia/{termin_id}".format(termin_id=termin_ff.id),
            mail_body,
        )


class KonliktTerminowJobTestCase(ALXTestCase):
    def test_get_qs(self):
        today = datetime.date.today()

        t1 = TerminSzkoleniaFactory.create(
            termin=today - datetime.timedelta(days=2),
            termin_zakonczenia=today + datetime.timedelta(days=2),
            sala=SalaFactory.create(sprawdzaj_konflikty=True),
            odbylo_sie=True,
        )
        TerminSzkoleniaFactory.create(
            termin=today + datetime.timedelta(days=2),
            sala=SalaFactory.create(sprawdzaj_konflikty=True),
            odbylo_sie=False,
        )
        t2 = TerminSzkoleniaFactory.create(
            termin=today + datetime.timedelta(days=2),
            sala=SalaFactory.create(sprawdzaj_konflikty=True),
            odbylo_sie=True,
        )

        result = konflikty_terminow.get_qs()
        self.assertEqual(len(result), 2)
        self.assertIn(t1, result)
        self.assertIn(t2, result)

    def test_get_terms_bez_przeciazonych_dni(self):
        szkolenie = SzkolenieFactory.create(czas_dni=2)

        TerminSzkoleniaFactory.create(
            sala=SalaFactory.create(sprawdzaj_konflikty=True),
            szkolenie=szkolenie,
            odbylo_sie=True,
        )
        TerminSzkoleniaFactory.create(
            sala=SalaFactory.create(sprawdzaj_konflikty=False),
            szkolenie=szkolenie,
            odbylo_sie=True,
        )

        # PS. weź pod uwagę liczbę dni szkolenia (`czas_dni`).
        self.assertEqual(len(konflikty_terminow.get_terms()), 2)

    def test_get_terms_z_przeciazonymi_dniami(self):
        today = datetime.date.today()

        szkolenie = SzkolenieFactory.create(czas_dni=5)

        # Nie powinno być tego terminu w wynikach
        TerminSzkoleniaFactory.create(
            sala=SalaFactory.create(sprawdzaj_konflikty=False),
            szkolenie=szkolenie,
            odbylo_sie=True,
        )
        # Nie powinno być tego terminu w wynikach
        TerminSzkoleniaFactory.create(szkolenie=szkolenie, odbylo_sie=True)

        t1 = TerminSzkoleniaFactory.create(
            termin=today + datetime.timedelta(days=2),
            sala=SalaFactory.create(sprawdzaj_konflikty=False),
            szkolenie=szkolenie,
            odbylo_sie=True,
        )
        DzienSzkoleniaFactory(
            terminszkolenia=t1,
            sala=SalaFactory.create(sprawdzaj_konflikty=True),
            data=today + datetime.timedelta(days=3),
        )
        day_plus_4 = DzienSzkoleniaFactory(
            terminszkolenia=t1,
            sala=SalaFactory.create(sprawdzaj_konflikty=True),
            data=today + datetime.timedelta(days=4),
        )
        # Nie powinno być tego dnia w wynikach
        DzienSzkoleniaFactory(
            terminszkolenia=t1,
            sala=SalaFactory.create(sprawdzaj_konflikty=False),
            data=today + datetime.timedelta(days=5),
        )
        t2 = TerminSzkoleniaFactory.create(
            termin=today + datetime.timedelta(days=4),
            sala=day_plus_4.sala,
            odbylo_sie=True,
        )

        result = konflikty_terminow.get_terms()

        # PS. weź pod uwagę liczbę dni szkolenia (`czas_dni`).
        self.assertEqual(len(konflikty_terminow.get_terms()), 2)

        row = result[(today + datetime.timedelta(days=4), day_plus_4.sala)]
        self.assertEqual(len(row), 2)
        self.assertIn(t1, row)
        self.assertIn(t2, row)

    def test_get_conflicts_bez_przeciazonych_dni(self):
        today = datetime.date.today()
        termin = today + datetime.timedelta(days=3)

        sala = SalaFactory.create(sprawdzaj_konflikty=True)

        # Tworzymy konflikt dla sali `sala`.
        t1 = TerminSzkoleniaFactory.create(sala=sala, termin=termin, odbylo_sie=True)
        t2 = TerminSzkoleniaFactory.create(sala=sala, termin=termin, odbylo_sie=True)

        conflicts = dict(konflikty_terminow.get_conflicts())

        self.assertEqual(len(conflicts), 1)
        self.assertIn((termin, sala), conflicts)

        res = conflicts[(termin, sala)]

        self.assertIn(t1, res)
        self.assertIn(t2, res)

        # Zmieniamy t2 na termin podrzedny
        t1.termin_nadrzedny = t2
        t1.save()

        conflicts = dict(konflikty_terminow.get_conflicts())
        self.assertEqual(len(conflicts), 0)

    def test_get_conflicts_z_przeciazonymi_dniami(self):
        today = datetime.date.today()
        termin = today + datetime.timedelta(days=3)

        szkolenie = SzkolenieFactory.create(czas_dni=2)
        sala = SalaFactory.create(sprawdzaj_konflikty=True)

        # Tworzymy konflikt dla sali `sala`.
        t1 = TerminSzkoleniaFactory.create(
            szkolenie=szkolenie,
            sala=SalaFactory.create(sprawdzaj_konflikty=True),
            termin=termin,
            odbylo_sie=True,
        )
        DzienSzkoleniaFactory(
            terminszkolenia=t1,
            sala=sala,
            data=t1.termin + datetime.timedelta(days=1),
        )

        t2 = TerminSzkoleniaFactory.create(
            szkolenie=szkolenie, sala=sala, termin=termin, odbylo_sie=True
        )

        conflicts = dict(konflikty_terminow.get_conflicts())

        self.assertEqual(len(conflicts), 1)
        self.assertIn((t1.termin + datetime.timedelta(days=1), sala), conflicts)

        res = conflicts[(t1.termin + datetime.timedelta(days=1), sala)]

        self.assertIn(t1, res)
        self.assertIn(t2, res)

        # Zmieniamy t2 na termin podrzedny
        t1.termin_nadrzedny = t2
        t1.save()

        conflicts = dict(konflikty_terminow.get_conflicts())
        self.assertEqual(len(conflicts), 0)

    def test_get_conflicts_z_trybem_wieczorowym(self):
        today = datetime.date.today()
        termin = today + datetime.timedelta(days=3)

        szkolenie = SzkolenieFactory.create(czas_dni=2)
        sala = SalaFactory.create(sprawdzaj_konflikty=True)

        # Tworzymy konflikt dla sali `sala`, ale jeden termin jest wieczorowy
        t1 = TerminSzkoleniaFactory.create(
            szkolenie=szkolenie,
            sala=SalaFactory.create(sprawdzaj_konflikty=True),
            termin=termin,
            odbylo_sie=True,
        )
        DzienSzkoleniaFactory(
            terminszkolenia=t1,
            sala=sala,
            data=t1.termin + datetime.timedelta(days=1),
        )

        TerminSzkoleniaFactory.create(
            szkolenie=szkolenie, sala=sala, termin=termin, odbylo_sie=True, tryb=2
        )

        conflicts = dict(konflikty_terminow.get_conflicts())
        self.assertEqual(len(conflicts), 0)

        t1.tryb = 2
        t1.save()

        conflicts = dict(konflikty_terminow.get_conflicts())
        self.assertEqual(len(conflicts), 1)


class KonliktSprzetuJobTestCase(ALXTestCase):
    def test_get_terms_bez_przeciazonych_dni(self):
        szkolenie = SzkolenieFactory.create(czas_dni=2)

        TerminSzkoleniaFactory.create(
            szkolenie=szkolenie,
            odbylo_sie=True,
            sprzet=[SprzetFactory.create(sprawdzaj_konflikty=True)],
        )
        TerminSzkoleniaFactory.create(
            szkolenie=szkolenie,
            odbylo_sie=True,
            sprzet=[SprzetFactory.create(sprawdzaj_konflikty=False)],
        )

        # PS. weź pod uwagę liczbę dni szkolenia (`czas_dni`).
        self.assertEqual(len(konflikty_sprzetu.get_terms()), 2)

    def test_get_terms_z_przeciazonymi_dniami(self):
        today = datetime.date.today()

        szkolenie = SzkolenieFactory.create(czas_dni=5)

        # Nie powinno być tego terminu w wynikach
        TerminSzkoleniaFactory.create(
            sprzet=[SprzetFactory.create(sprawdzaj_konflikty=False)],
            szkolenie=szkolenie,
            odbylo_sie=True,
        )
        # Nie powinno być tego terminu w wynikach
        TerminSzkoleniaFactory.create(szkolenie=szkolenie, odbylo_sie=True, sprzet=[])

        t1 = TerminSzkoleniaFactory.create(
            termin=today + datetime.timedelta(days=2),
            sprzet=[SprzetFactory.create(sprawdzaj_konflikty=False)],
            szkolenie=szkolenie,
            odbylo_sie=True,
        )
        DzienSzkoleniaFactory(
            terminszkolenia=t1,
            sprzet=[SprzetFactory.create(sprawdzaj_konflikty=True)],
            data=today + datetime.timedelta(days=3),
        )
        day_plus_4 = DzienSzkoleniaFactory(
            terminszkolenia=t1,
            sprzet=[SprzetFactory.create(sprawdzaj_konflikty=True)],
            data=today + datetime.timedelta(days=4),
        )
        # Nie powinno być tego dnia w wynikach
        DzienSzkoleniaFactory(
            terminszkolenia=t1,
            sprzet=[SprzetFactory.create(sprawdzaj_konflikty=False)],
            data=today + datetime.timedelta(days=5),
        )
        t2 = TerminSzkoleniaFactory.create(
            termin=today + datetime.timedelta(days=4),
            sprzet=list(day_plus_4.sprzet.all()),
            odbylo_sie=True,
        )

        result = konflikty_sprzetu.get_terms()

        # PS. weź pod uwagę liczbę dni szkolenia (`czas_dni`).
        self.assertEqual(len(konflikty_sprzetu.get_terms()), 2)

        row = result[(today + datetime.timedelta(days=4), day_plus_4.sprzet.all()[0])]
        self.assertEqual(len(row), 2)
        self.assertIn(t1, row)
        self.assertIn(t2, row)

    def test_get_conflicts_bez_przeciazonych_dni(self):
        today = datetime.date.today()
        termin = today + datetime.timedelta(days=3)

        sprzet = SprzetFactory.create(sprawdzaj_konflikty=True)

        # Tworzymy konflikt dla sprzętu `sprzet`.
        t1 = TerminSzkoleniaFactory.create(
            sprzet=[sprzet, SprzetFactory.create()], termin=termin, odbylo_sie=True
        )
        t2 = TerminSzkoleniaFactory.create(
            sprzet=[sprzet, SprzetFactory.create(), SprzetFactory.create()],
            termin=termin,
            odbylo_sie=True,
        )

        conflicts = dict(konflikty_sprzetu.get_conflicts())

        self.assertEqual(len(conflicts), 1)
        self.assertIn((termin, sprzet), conflicts)

        res = conflicts[(termin, sprzet)]

        self.assertIn(t1, res)
        self.assertIn(t2, res)

        # Zmieniamy t2 na termin podrzedny
        t1.termin_nadrzedny = t2
        t1.save()

        conflicts = dict(konflikty_sprzetu.get_conflicts())
        self.assertEqual(len(conflicts), 0)

    def test_get_conflicts_z_przeciazonymi_dniami(self):
        today = datetime.date.today()
        termin = today + datetime.timedelta(days=3)

        szkolenie = SzkolenieFactory.create(czas_dni=2)
        sprzet = SprzetFactory.create(sprawdzaj_konflikty=True)

        # Tworzymy konflikt dla sprzętu `sprzet`.
        t1 = TerminSzkoleniaFactory.create(
            szkolenie=szkolenie,
            sprzet=[SprzetFactory.create(sprawdzaj_konflikty=True)],
            termin=termin,
            odbylo_sie=True,
        )
        DzienSzkoleniaFactory(
            terminszkolenia=t1,
            sprzet=[sprzet, SprzetFactory.create()],
            data=t1.termin + datetime.timedelta(days=1),
        )

        t2 = TerminSzkoleniaFactory.create(
            szkolenie=szkolenie,
            sprzet=[sprzet, SprzetFactory.create()],
            termin=termin,
            odbylo_sie=True,
        )

        conflicts = dict(konflikty_sprzetu.get_conflicts())

        self.assertEqual(len(conflicts), 1)
        self.assertIn((t1.termin + datetime.timedelta(days=1), sprzet), conflicts)

        res = conflicts[(t1.termin + datetime.timedelta(days=1), sprzet)]

        self.assertIn(t1, res)
        self.assertIn(t2, res)

        # Zmieniamy t2 na termin podrzedny
        t1.termin_nadrzedny = t2
        t1.save()

        conflicts = dict(konflikty_sprzetu.get_conflicts())
        self.assertEqual(len(conflicts), 0)

    def test_get_conflicts_z_trybem_wieczorowym(self):
        today = datetime.date.today()
        termin = today + datetime.timedelta(days=3)

        szkolenie = SzkolenieFactory.create(czas_dni=2)
        sprzet = SprzetFactory.create(sprawdzaj_konflikty=True)

        # Tworzymy konflikt dla sprzętu `sprzet`, ale jeden termin jest
        # wieczorowy!
        t1 = TerminSzkoleniaFactory.create(
            szkolenie=szkolenie,
            sprzet=[SprzetFactory.create(sprawdzaj_konflikty=True)],
            termin=termin,
            odbylo_sie=True,
        )
        DzienSzkoleniaFactory(
            terminszkolenia=t1,
            sprzet=[sprzet, SprzetFactory.create()],
            data=t1.termin + datetime.timedelta(days=1),
        )

        TerminSzkoleniaFactory.create(
            szkolenie=szkolenie,
            sprzet=[sprzet, SprzetFactory.create()],
            termin=termin,
            odbylo_sie=True,
            tryb=2,
        )

        conflicts = dict(konflikty_sprzetu.get_conflicts())
        self.assertEqual(len(conflicts), 0)

        t1.tryb = 2
        t1.save()

        conflicts = dict(konflikty_sprzetu.get_conflicts())
        self.assertEqual(len(conflicts), 1)


@override_settings(
    DOMENY_DLA_JEZYKOW={"pl": "www.alx.dev", "en": "www.alx-training.co.dev"}
)
class PrzypominaczGrupaNieRuszaJobTestCase(ALXTestCase):
    def test_grupa_nie_rusza_hybryda(self):
        termin_zdalny = TerminSzkoleniaFactory.create(
            szkolenie__min_grupa_bazowa=5,
            lokalizacja__shortname="zdalnie",
            termin=datetime.date.today() + datetime.timedelta(days=13),
            odbylo_sie=False,
        )

        termin = TerminSzkoleniaFactory.create(
            szkolenie__min_grupa_bazowa=5,
            lokalizacja__shortname="Warszawa",
            termin=datetime.date.today() + datetime.timedelta(days=7),
            termin_zdalny=termin_zdalny,
            odbylo_sie=False,
        )
        UczestnikFactory.create_batch(
            2, termin=termin_zdalny, imie_nazwisko="Imię zdalnie"
        )
        UczestnikFactory.create_batch(
            2, termin=termin_zdalny, nierentowny=True, imie_nazwisko="Imię zdalnie"
        )

        UczestnikFactory.create_batch(1, termin=termin, imie_nazwisko="Imię Waw")
        UczestnikFactory.create_batch(
            3, termin=termin, nierentowny=True, imie_nazwisko="Imię Waw"
        )

        exec(
            compile(
                open("bin/przypominacz_grupa_rusza.py", "rb").read(),
                "bin/przypominacz_grupa_rusza.py",
                "exec",
            )
        )
        self.assertEqual(len(mail.outbox), 1)

        body = mail.outbox[0].body

        self.assertIn("Razem hybrydowo: 8 / 3R", body)

        self.assertIn("Warszawa - 4 / 1R", body)
        self.assertIn(str(termin.termin), body)
        self.assertIn(termin.szkolenie.kod, body)
        self.assertIn(
            "https://www.alx.dev/admin/www/terminszkolenia/{0}/".format(termin.pk), body
        )
        self.assertIn("Uczestnicy: Imię Waw, ", body)

        self.assertIn("zdalnie - 4 / 2R", body)
        self.assertIn(
            "https://www.alx.dev/admin/www/terminszkolenia/{0}/".format(
                termin_zdalny.pk
            ),
            body,
        )
        self.assertIn("Uczestnicy: Imię zdalnie, ", body)

        self.assertEqual(
            "Grupa nie rusza ({0} - {1})".format(termin.szkolenie.kod, termin.termin),
            mail.outbox[0].subject,
        )

    def test_grupa_nie_rusza_hybryda_i_fakoryzacja(self):
        termin_zdalny = TerminSzkoleniaFactory.create(
            szkolenie__min_grupa_bazowa=5,
            lokalizacja__shortname="zdalnie",
            termin=datetime.date.today() + datetime.timedelta(days=13),
            odbylo_sie=False,
        )

        termin = TerminSzkoleniaFactory.create(
            szkolenie__min_grupa_bazowa=5,
            lokalizacja__shortname="Warszawa",
            termin=datetime.date.today() + datetime.timedelta(days=7),
            termin_zdalny=termin_zdalny,
            odbylo_sie=False,
        )
        UczestnikFactory.create_batch(
            2, termin=termin_zdalny, imie_nazwisko="Imię zdalnie"
        )
        UczestnikFactory.create_batch(
            2, termin=termin_zdalny, nierentowny=True, imie_nazwisko="Imię zdalnie"
        )

        termin_podrzedny = TerminSzkoleniaFactory.create(
            szkolenie__nazwa="Szkolenie podrzędne",
            termin=datetime.date.today(),
            termin_nadrzedny=termin,
        )
        UczestnikFactory.create_batch(1, termin=termin_podrzedny)
        UczestnikFactory.create_batch(1, termin=termin_podrzedny, nierentowny=True)

        UczestnikFactory.create_batch(1, termin=termin, imie_nazwisko="Imię Waw")
        UczestnikFactory.create_batch(
            3, termin=termin, nierentowny=True, imie_nazwisko="Imię Waw"
        )

        exec(
            compile(
                open("bin/przypominacz_grupa_rusza.py", "rb").read(),
                "bin/przypominacz_grupa_rusza.py",
                "exec",
            )
        )
        self.assertEqual(len(mail.outbox), 1)

        body = mail.outbox[0].body

        self.assertIn("Razem hybrydowo: 8 / 3R", body)
        self.assertIn("Szkolenie podrzędne: 2 / 1R", body)
        self.assertIn("Warszawa - 4 / 1R", body)
        self.assertIn(str(termin.termin), body)
        self.assertIn(termin.szkolenie.kod, body)
        self.assertIn(
            "https://www.alx.dev/admin/www/terminszkolenia/{0}/".format(termin.pk), body
        )
        self.assertIn("Uczestnicy: Imię Waw, ", body)

        self.assertIn("zdalnie - 4 / 2R", body)
        self.assertIn(
            "https://www.alx.dev/admin/www/terminszkolenia/{0}/".format(
                termin_zdalny.pk
            ),
            body,
        )
        self.assertIn("Uczestnicy: Imię zdalnie, ", body)

        self.assertEqual(
            "Grupa nie rusza ({0} - {1})".format(termin.szkolenie.kod, termin.termin),
            mail.outbox[0].subject,
        )


@override_settings(
    DOMENY_DLA_JEZYKOW={"pl": "www.alx.dev", "en": "www.alx-training.co.dev"}
)
class PrzypominaczGrupaRuszaJobTestCase(ALXTestCase):
    def test_grupa_rusza_hybryda(self):
        termin_zdalny = TerminSzkoleniaFactory.create(
            szkolenie__min_grupa_bazowa=3,
            lokalizacja__shortname="zdalnie",
            termin=datetime.date.today() + datetime.timedelta(days=13),
            odbylo_sie=False,
        )

        termin = TerminSzkoleniaFactory.create(
            szkolenie__min_grupa_bazowa=3,
            lokalizacja__shortname="Warszawa",
            termin=datetime.date.today() + datetime.timedelta(days=7),
            termin_zdalny=termin_zdalny,
            odbylo_sie=False,
        )
        UczestnikFactory.create_batch(
            2, termin=termin_zdalny, imie_nazwisko="Imię zdalnie"
        )
        UczestnikFactory.create_batch(
            2, termin=termin_zdalny, nierentowny=True, imie_nazwisko="Imię zdalnie"
        )

        UczestnikFactory.create_batch(1, termin=termin, imie_nazwisko="Imię Waw")
        UczestnikFactory.create_batch(
            3, termin=termin, nierentowny=True, imie_nazwisko="Imię Waw"
        )

        exec(
            compile(
                open("bin/przypominacz_grupa_rusza.py", "rb").read(),
                "bin/przypominacz_grupa_rusza.py",
                "exec",
            )
        )
        self.assertEqual(len(mail.outbox), 1)

        body = mail.outbox[0].body

        self.assertIn("Razem hybrydowo: 8 / 3R", body)

        self.assertIn("Warszawa - 4 / 1R", body)
        self.assertIn(str(termin.termin), body)
        self.assertIn(termin.szkolenie.kod, body)
        self.assertIn(
            "https://www.alx.dev/admin/www/terminszkolenia/{0}/".format(termin.pk), body
        )
        self.assertIn("Uczestnicy: Imię Waw, ", body)

        self.assertIn("zdalnie - 4 / 2R", body)
        self.assertIn(
            "https://www.alx.dev/admin/www/terminszkolenia/{0}/".format(
                termin_zdalny.pk
            ),
            body,
        )
        self.assertIn("Uczestnicy: Imię zdalnie, ", body)

        self.assertEqual(
            "Grupa rusza ({0} - {1})".format(termin.szkolenie.kod, termin.termin),
            mail.outbox[0].subject,
        )

    def test_grupa_rusza_hybryda_i_faktoryzacja(self):
        termin_zdalny = TerminSzkoleniaFactory.create(
            szkolenie__min_grupa_bazowa=3,
            lokalizacja__shortname="zdalnie",
            termin=datetime.date.today() + datetime.timedelta(days=13),
            odbylo_sie=False,
        )

        termin = TerminSzkoleniaFactory.create(
            szkolenie__min_grupa_bazowa=3,
            lokalizacja__shortname="Warszawa",
            termin=datetime.date.today() + datetime.timedelta(days=7),
            termin_zdalny=termin_zdalny,
            odbylo_sie=False,
        )
        UczestnikFactory.create_batch(
            2, termin=termin_zdalny, imie_nazwisko="Imię zdalnie"
        )
        UczestnikFactory.create_batch(
            2, termin=termin_zdalny, nierentowny=True, imie_nazwisko="Imię zdalnie"
        )

        termin_podrzedny = TerminSzkoleniaFactory.create(
            szkolenie__nazwa="Szkolenie podrzędne",
            termin=datetime.date.today(),
            termin_nadrzedny=termin,
        )
        UczestnikFactory.create_batch(1, termin=termin_podrzedny)
        UczestnikFactory.create_batch(1, termin=termin_podrzedny, nierentowny=True)

        UczestnikFactory.create_batch(1, termin=termin, imie_nazwisko="Imię Waw")
        UczestnikFactory.create_batch(
            3, termin=termin, nierentowny=True, imie_nazwisko="Imię Waw"
        )

        exec(
            compile(
                open("bin/przypominacz_grupa_rusza.py", "rb").read(),
                "bin/przypominacz_grupa_rusza.py",
                "exec",
            )
        )
        self.assertEqual(len(mail.outbox), 1)

        body = mail.outbox[0].body

        self.assertIn("Razem hybrydowo: 8 / 3R", body)

        self.assertIn("Warszawa - 4 / 1R", body)
        self.assertIn(str(termin.termin), body)
        self.assertIn(termin.szkolenie.kod, body)
        self.assertIn(
            "https://www.alx.dev/admin/www/terminszkolenia/{0}/".format(termin.pk), body
        )
        self.assertIn("Uczestnicy: Imię Waw, ", body)
        self.assertIn("Szkolenie podrzędne: 2 / 1R", body)
        self.assertIn("zdalnie - 4 / 2R", body)
        self.assertIn(
            "https://www.alx.dev/admin/www/terminszkolenia/{0}/".format(
                termin_zdalny.pk
            ),
            body,
        )
        self.assertIn("Uczestnicy: Imię zdalnie, ", body)

        self.assertEqual(
            "Grupa rusza ({0} - {1})".format(termin.szkolenie.kod, termin.termin),
            mail.outbox[0].subject,
        )
