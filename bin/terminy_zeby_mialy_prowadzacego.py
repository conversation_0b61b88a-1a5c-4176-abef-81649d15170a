#!/usr/bin/env python
import datetime
import os

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "settings")

import django

django.setup()

from django.conf import settings
from django.core.mail import send_mail
from django.template.loader import render_to_string

from i18n.models import WersjaJezykowa
from www.models import TerminSzkolenia

today = datetime.date.today()

terminy = (
    TerminSzkolenia.objects.filter(
        odbylo_sie=True,
        prowadzacy__isnull=True,
        termin__lte=today,
        termin__gt=datetime.date(2012, 5, 1),
    )
    .exclude(szkolenie__kod="WS")
    .exclude(szkolenie__grupa_zaszeregowania__nazwa__startswith="999:")
)

if terminy:
    site_url = "https://" + WersjaJezykowa.biezaca().domain
    mesg = render_to_string(
        "www/terminy_zeby_mialy_prowadzacego.txt",
        {
            "terminy": terminy,
            "site_url": site_url,
        },
    )
    send_mail(
        "Terminy nie mają prowadzącego",
        mesg,
        settings.MAIL_FROM_ADDRESS,
        [settings.MAIL_TERMINY],
    )
