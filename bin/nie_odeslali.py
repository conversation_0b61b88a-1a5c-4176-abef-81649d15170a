#!/usr/bin/env python
import datetime
import os

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "settings")

import django

django.setup()

from django.conf import settings
from django.core.mail import send_mail
from django.template.loader import render_to_string

from i18n.models import WersjaJezykowa
from www.models import Uczestnik

poczatek_swiata = datetime.datetime(2012, 7, 27)
now = datetime.datetime.now()

nie_odeslali = Uczestnik.objects.filter(
    odeslal_podpisany_formularz=False,
    czas_dodania__gt=poczatek_swiata,
    czas_dodania__lt=now - datetime.timedelta(hours=48),
)

if nie_odeslali:
    site_url = "https://" + WersjaJezykowa.biezaca().domain
    mesg = render_to_string(
        "www/nie_odeslali.txt",
        {
            "nie_odeslali": nie_odeslali,
            "site_url": site_url,
        },
    )
    send_mail(
        "Nie odesłali formularza",
        mesg,
        settings.MAIL_FROM_ADDRESS,
        [settings.MAIL_TERMINY],
    )
