#!/usr/bin/env python


import datetime
import os

import django

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "settings")
django.setup()

from django.conf import settings
from django.core.mail import send_mail
from django.template.loader import render_to_string

from i18n.models import WersjaJezykowa
from www.models import Uczestnik

czekajacy = Uczestnik.objects.filter(status__in=[-3, -2, -1, 0, 1])

today = datetime.date.today()

warunki = [
    [
        'status "trzeba potwierdzić" albo "trzeba przem<PERSON>"',
        lambda c: c.status in [-3, -2, 0],
    ],
    [
        'status "potwierdzone" i szkolenie się odbyło',
        lambda c: (
            c.status == 1
            and (c.termin.termin_do() if c.termin.termin_do() else c.termin.termin)
            < today
        ),
    ],
    [
        'status "skontaktować się znów przed szkoleniem" i termin wkrótce',
        lambda c: (
            c.status == -1 and c.termin.termin < today + datetime.timedelta(days=7)
        ),
    ],
]

# delikwenci = [c for c in czekajacy if c.status in [-3, -2, 0] or (c.status == 1 and (c.termin.termin_do() if c.termin.termin_do() else c.termin.termin) < today) or (c.status==-1 and c.termin.termin < today+datetime.timedelta(days=7))]

delikwenci = []

for u in czekajacy:
    powody = [w[0] for w in warunki if w[1](u)]
    if powody:
        u.powod = ", ".join(powody)
        delikwenci.append(u)

if delikwenci:
    site_url = "https://" + WersjaJezykowa.biezaca().domain
    mesg = render_to_string(
        "www/czekajacy_na_obsluzenie.txt",
        {
            "delikwenci": delikwenci,
            "site_url": site_url,
        },
    )
    send_mail(
        "Nieobsłużeni klienci (ALX szkolenia)",
        mesg,
        settings.MAIL_FROM_ADDRESS,
        [settings.MAIL_TERMINY],
    )
