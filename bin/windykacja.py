#!/usr/bin/env python
import os

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "settings")

import django

django.setup()

from django.conf import settings
from django.core.mail import send_mail
from django.template.loader import render_to_string

from i18n.models import WersjaJezykowa
from www.models import Uczestnik

uczestnicy = Uczestnik.objects.all().order_by("termin")

zalegajacy = [x for x in uczestnicy if x.zaplacone3() is False]

for z in zalegajacy:
    z.powod = z.verbose_zaplacone3()[1]

if zalegajacy:
    site_url = "https://" + WersjaJezykowa.biezaca().domain
    mesg = render_to_string(
        "www/windykacja.txt",
        {
            "zalegajacy": zalegajacy,
            "site_url": site_url,
        },
    )
    send_mail(
        "Windykacja (ALXCRM)",
        mesg,
        settings.MAIL_FROM_ADDRESS,
        [settings.MAIL_WINDYKACJA],
    )
