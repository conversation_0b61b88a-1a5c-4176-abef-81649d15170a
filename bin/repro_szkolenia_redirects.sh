#!/usr/bin/env bash
# Reproduce and inspect redirects for /szkolenia and /pl/szkolenia using curl
#
# Usage:
#   ./bin/repro_szkolenia_redirects.sh                      # assumes BASE_URL=http://localhost:8000
#   BASE_URL=https://www.alx.pl ./bin/repro_szkolenia_redirects.sh
#   BASE_URL=https://alx.pl ./bin/repro_szkolenia_redirects.sh
#
# What it does:
# - Shows the first response headers (without following redirects) for each target
# - Follows redirects (up to 10) and prints the number of redirects and final HTTP code
# - Targets: /szkolenia/<slug>/ and /pl/szkolenia/<slug>/ for an arbitrary slug
#
# Notes about app-side redirects in this repo:
# - For Polish content the project defines unprefixed routes like /szkolenia/<slug>/ (see www/urls.py -> detail)
# - There is NO Django rule here that toggles between /pl/szkolenia/<...>/ and /szkolenia/<...>/. If you observe
#   such toggling in production, it is most likely enforced by upstream (e.g., nginx) or external middleware.
# - The only explicit language-prefix-related redirect in our code is for the homepage: frontpage() forces the
#   default language to "/" (removing "/pl/") but that does NOT affect /szkolenia/* pages.
# - CommonMiddleware may add a trailing slash (301) and LocaleMiddleware is enabled, but our www.urls aren’t wrapped
#   in i18n_patterns for these routes, so LocaleMiddleware should not force /pl/ prefixes for /szkolenia/*.
#
# Requirements: curl

set -euo pipefail

BASE_URL="${BASE_URL:-http://alx.pl}"
# Change this to a real slug you want to test. It can be nonexistent; you'll still see redirect behavior.
SLUG="${SLUG:-ai-dla-dzialu-hr}"

urls=(
  "/szkolenia/${SLUG}/"
  "/pl/szkolenia/${SLUG}/"
)

sep() {
  printf "\n============================================================\n\n"
}

info() {
  echo "[INFO] $*"
}

show_first_response() {
  local url="$1"
  info "HEAD (no follow) ${url}"
  # -I: HEAD, -s: silent, -S: show errors, -D -: dump headers, -o /dev/null: discard body
  curl -I -sS -D - -o /dev/null "${BASE_URL}${url}" || true
}

follow_and_summarize() {
  local url="$1"
  info "FOLLOW (max 10) ${url}"
  # -L: follow redirects, --max-redirs 10 limits loops, -w prints metrics
  curl -I -sS -L --max-redirs 10 -o /dev/null \
    -w "Redirects: %{num_redirects}\nFinal URL: %{url_effective}\nHTTP code: %{http_code}\n" \
    "${BASE_URL}${url}" || true
}

main() {
  info "BASE_URL=${BASE_URL}"
  info "SLUG=${SLUG}"
  sep
  for u in "${urls[@]}"; do
    show_first_response "$u"
    follow_and_summarize "$u"
    sep
  done

  cat <<EOF
Tips:
- To see the full redirect chain headers, run (non-prefixed):
    curl -I -sS -L --max-redirs 10 -D - "${BASE_URL}/szkolenia/${SLUG}/" -o /dev/null
- And prefixed:
    curl -I -sS -L --max-redirs 10 -D - "${BASE_URL}/pl/szkolenia/${SLUG}/" -o /dev/null
- Stop on the first response and view Location only:
    curl -I -sS -D - "${BASE_URL}/szkolenia/${SLUG}/"
EOF
}

main "$@"
