#!/usr/bin/env python
import datetime
import os

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "settings")

import django

django.setup()

from django.conf import settings
from django.core.mail import send_mail

from wynajmy.models import Notatka

today = datetime.date.today()

subject = "Zalegle TODO z wynajmow na dzien " + str(today)

niezrobione = Notatka.objects.select_related().filter(
    aktualna=True, todo_zrobione=False, todo_data__lte=today
)

usery = niezrobione.values_list("todo_user", "todo_user__email").distinct("todo_user")

for u in usery:
    usr = u[0]
    u_niezrobione = niezrobione.filter(todo_user=usr)
    ln = [
        str(nie.todo_data) + " | " + str(nie.czlowiek) + " \n " + str(nie.tekst) + "\n"
        for nie in u_niezrobione
    ]
    ln.sort()
    lu = str(u[1])
    if u_niezrobione:
        mesg = "\n".join(ln)
        send_mail(subject, mesg, settings.MAIL_FROM_ADDRESS, [lu], fail_silently=False)
    else:
        send_mail(
            subject, "nie ma", settings.MAIL_FROM_ADDRESS, [lu], fail_silently=False
        )
