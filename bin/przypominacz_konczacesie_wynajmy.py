#!/usr/bin/env python
import datetime
import os

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "settings")

import django

django.setup()

from django.conf import settings
from django.core.mail import send_mail

from wynajmy.models import Wynajem

today = datetime.date.today()
twoweek_forward = today + datetime.timedelta(days=14)
subject = "Brak przedlużenia wynajmu " + str(today)

endingwynajmy = (
    Wynajem.objects.select_related()
    .filter(data_do__lte=twoweek_forward, data_do__gt=today)
    .order_by("data_do")
)

poor_czlowiek = []
opiekun_of_poor = []

for ending in endingwynajmy:
    ending_czlowiek = ending.czlowiek
    ending_data_1 = ending.data_do + datetime.timedelta(days=1)
    ending_klient = ending.klient
    prolongs = Wynajem.objects.filter(
        czlowiek=ending_czlowiek, data_do__gt=twoweek_forward
    )
    if not prolongs:
        poor_czlowiek.append(
            (
                ending.czlowiek,
                ending.data_do,
                ending.czlowiek.opiekun.email,
                ending.klient,
            )
        )
        opiekun_of_poor.append(ending.czlowiek.opiekun.email)
    opiekun_of_poor = list(set(opiekun_of_poor))

for op in opiekun_of_poor:
    lpoor = [
        str(p[1]) + " | " + str(p[0]) + " | Klient: " + str(p[3]) + "\n"
        for p in poor_czlowiek
        if p[2] == op
    ]
    lpoor.sort()
    mesg = "\n".join(lpoor)
    send_mail(subject, mesg, settings.MAIL_FROM_ADDRESS, [str(op)], fail_silently=False)
