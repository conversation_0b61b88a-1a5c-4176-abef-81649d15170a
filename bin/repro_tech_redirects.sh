#!/usr/bin/env bash
# Reproduce and inspect redirects for /tech and /pl/tech using curl
#
# Usage:
#   ./bin/repro_tech_redirects.sh                 # assumes BASE_URL=http://localhost:8000
#   BASE_URL=https://www.alx.pl ./bin/repro_tech_redirects.sh
#   BASE_URL=http://127.0.0.1:8000 ./bin/repro_tech_redirects.sh
#
# What it does:
# - Shows the first response headers (without following redirects) for each target
# - Follows redirects (up to 10) and prints the number of redirects and final HTTP code
# - Targets: /tech/, /pl/tech/, and a nonexistent slug /tech/nonexistent-slug/
#
# Requirements: curl

set -euo pipefail

BASE_URL="${BASE_URL:-https://alx.pl}"
NONEXISTENT_SLUG="nonexistent-slug"

urls=(
  "/tech/"
  "/pl/tech/"
  "/tech/${NONEXISTENT_SLUG}/"
)

sep() {
  printf "\n============================================================\n\n"
}

info() {
  echo "[INFO] $*"
}

show_first_response() {
  local url="$1"
  info "HEAD (no follow) ${url}"
  # -I: HEAD, -s: silent, -S: show errors, -D -: dump headers, -o /dev/null: discard body
  curl -I -sS -D - -o /dev/null "${BASE_URL}${url}" || true
}

follow_and_summarize() {
  local url="$1"
  info "FOLLOW (max 10) ${url}"
  # -L: follow redirects, --max-redirs 10 limits loops, -w prints metrics
  curl -I -sS -L --max-redirs 10 -o /dev/null \
    -w "Redirects: %{num_redirects}\nFinal URL: %{url_effective}\nHTTP code: %{http_code}\n" \
    "${BASE_URL}${url}" || true
}

main() {
  info "BASE_URL=${BASE_URL}"
  sep
  for u in "${urls[@]}"; do
    show_first_response "$u"
    follow_and_summarize "$u"
    sep
  done

  cat <<EOF
Tips:
- To see the full redirect chain headers, run:
    curl -I -sS -L --max-redirs 10 -D - "${BASE_URL}/tech/${NONEXISTENT_SLUG}/" -o /dev/null
- To stop on the first response and view Location:
    curl -I -sS -D - "${BASE_URL}/tech/"
EOF
}

main "$@"
