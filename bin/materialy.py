#!/usr/bin/env python
import datetime
import os

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "settings")

import django

django.setup()

from django.conf import settings
from django.core.mail import send_mail
from django.template.loader import render_to_string

from i18n.models import WersjaJezykowa
from www.models import TerminSzkolenia

today = datetime.date.today()

za_dwa_dni = today + datetime.timedelta(days=2)

if za_dwa_dni.weekday() in [
    5,
    6,
]:  # żeby w czwartek krzyczało już o szkoleniach z poniedziałku, a w piątek z poniedziałku i wtorku
    za_dwa_dni += datetime.timedelta(days=2)

terminy_wawa = TerminSzkolenia.objects.filter(
    lokalizacja__fullname="Warszawa",
    materialy_wydrukowane=False,
    termin__lte=za_dwa_dni,
    odbylo_sie=True,
)
terminy_reszta = TerminSzkolenia.objects.filter(
    materialy_wydrukowane=False,
    termin__lte=today + datetime.timedelta(days=7),
    odbylo_sie=True,
).exclude(lokalizacja__fullname="Warszawa")
terminy = list(terminy_wawa) + list(terminy_reszta)

if terminy:
    site_url = "https://" + WersjaJezykowa.biezaca().domain
    mesg = render_to_string(
        "www/materialy.txt",
        {
            "terminy": terminy,
            "site_url": site_url,
        },
    )
    # print mesg
    send_mail("Materiały", mesg, settings.MAIL_FROM_ADDRESS, [settings.MAIL_MATERIALY])
