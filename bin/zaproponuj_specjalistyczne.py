#!/usr/bin/env python
import datetime
import os

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "settings")

import django

django.setup()

from django.conf import settings
from django.core.mail import send_mail
from django.template.loader import render_to_string

from i18n.models import WersjaJezykowa
from www.models import TerminSzkolenia

today = datetime.date.today()

koncza_sie_dzisiaj = TerminSzkolenia.objects.filter(
    termin_zakonczenia=today - datetime.timedelta(days=7),
    odbylo_sie=True,
    szkolenie__kod__in=["AL-ADMIN", "AL-DEV"],
)

if koncza_sie_dzisiaj:
    site_url = "https://" + WersjaJezykowa.biezaca().domain
    mesg = render_to_string(
        "www/zaproponuj_specjalistyczne.txt",
        {
            "koncza_sie_dzisiaj": koncza_sie_dzisiaj,
            "site_url": site_url,
        },
    )

    send_mail(
        "Zaproponować absolwentom kurs specjalistyczny",
        mesg,
        settings.MAIL_FROM_ADDRESS,
        [settings.MAIL_TERMINY],
    )
