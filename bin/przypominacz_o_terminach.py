#!/usr/bin/env python
import os

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "settings")

import django

django.setup()

from django.conf import settings
from django.core.mail import send_mail
from django.template.loader import render_to_string

from www.models import Szkolenie

lista_bez_terminow = [x for x in Szkolenie.objects.all() if x.terminy_ok() is False]

for t in lista_bez_terminow:
    t.powod = t.terminy_ok_verbose()[1]

if lista_bez_terminow:
    mesg = render_to_string(
        "www/przypominacz_o_terminach.txt", {"lista_bez_terminow": lista_bez_terminow}
    )
    send_mail(
        "Szkolenia bez terminów",
        mesg,
        settings.MAIL_FROM_ADDRESS,
        [settings.MAIL_TERMINY],
    )
