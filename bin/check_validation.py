#!/usr/bin/env python
import os
import re
import urllib.parse
import urllib.request

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "settings")

import django

django.setup()

from django.core.mail import mail_admins

jigsaw = "http://jigsaw.w3.org/css-validator/validator?uri="
validator = "http://validator.w3.org/check?uri="
URLS = [
    "http://www.alx.pl/",
    "http://www.alx.pl/szkolenia/",
    "http://www.alx.pl/najblizsze/",
    "http://www.alx.pl/tech/linux/",
    "http://www.alx.pl/szkolenia/wstep-do-administracji-systemem-linux/",
]

mesg = ""

for url in URLS:
    try:
        ret = urllib.request.urlopen(
            "http://validator.w3.org/check?uri=" + urllib.parse.quote(url)
        )
        result = ret.read()
        if re.search(r"\[Valid\]", result):
            pass
        elif re.search(r"\[Invalid\]", result):
            mesg += "{validator}{url} nie waliduje się.\n".format(
                validator=validator, url=url
            )
        else:
            mesg += "Coś dziwnego przy próbie walidacji %s.\n" % url
    except:
        mesg += "Błąd serwera przy próbie walidacji %s.\n" % url
    try:
        ret = urllib.request.urlopen(
            "http://jigsaw.w3.org/css-validator/validator?uri="
            + urllib.parse.quote(url)
        )
        result = ret.read()
        if re.search(r"Congratulations! No Error Found", result):
            pass
        elif re.search(r"Sorry! We found the following error", result):
            mesg += "Style na {jigsaw}{url} nie walidują się.\n".format(
                jigsaw=jigsaw, url=url
            )
        else:
            mesg += "Coś dziwnego przy próbie walidacji styli na %s.\n" % url
    except:
        mesg += "Błąd serwera przy próbie walidacji styli na %s.\n" % url

if mesg:
    mail_admins("Walidacja HTML/CSS", mesg)
