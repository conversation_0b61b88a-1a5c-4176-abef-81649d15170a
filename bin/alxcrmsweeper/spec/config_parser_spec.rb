require 'spec_helper'

describe ConfigParser do
  
  let(:config_parser) { ConfigParser.new }

  subject { config_parser }

  it { should respond_to(:first_name) }
  it { should respond_to(:last_name) }
  it { should respond_to(:last_name) }
  it { should respond_to(:user_name) } 

  it "generates vatno" do
    expect(config_parser.vatno).to match(/^(\d{3}-){2}\d{2}-\d{2}$/)
  end

  it "does not accept arguments on phone" do
    expect{config_parser.parse("phone(100)")}.to raise_error(ArgumentError)    
  end

  it "returns false for the non-null operator" do
    expect(config_parser.checkpresent?("+sentences(10)")).to be_false
  end

  it "returns true without non-null operator" do
    expect(config_parser.checkpresent?("sentences(10)")).to be_true
  end

  it "generates proper city and zip combination" do
    10.times do 
      expect(config_parser.parse("city_zip")).to match(/\s+\d{2}-\d{3}$/)
    end
  end

  it "generates valid ip address" do
    10.times do 
      expect(config_parser.parse("ip_address")).to match(/(\d{1,3}\.)\d{1,3}$/)
    end
  end

  it "returns nil" do
    expect(config_parser.parse("nullify")).to be_nil
  end

  it "generates url" do
    expect(config_parser.parse("url")).to match(/^http:\/\//)    
  end

  it "matches django password regexp" do
    expect(config_parser.parse("django_password(test)")).to match(/^sha1\$[a-z0-9]{5}\$[a-z0-9]{40}$/)
  end

  it "generates email" do
    expect(config_parser.parse("email")).to match(/@/)
  end

  describe "sequence" do
    it "returns string" do
      expect(config_parser.parse("sequence")).to be_instance_of String
    end

    it "returns sequence" do
      (1..10).each do |n|
        expect(config_parser.parse("sequence")).to eq n.to_s
      end
    end

    it "accepts prefix argument" do
      expect(config_parser.parse("sequence(rebeca)")).to eq("rebeca_1")    
      expect(config_parser.parse("sequence(rebeca)")).to eq("rebeca_2")    
      expect(config_parser.parse("sequence(rebeca)")).to eq("rebeca_3")    
    end
  end

  describe "generated random number" do
    it "accepts up to 3 parameteres" do
      expect{config_parser.parse("number(50,60,5,10)")}.to raise_error(ArgumentError)
    end

    it "is in range with step" do
      100.times do
        result = config_parser.parse("number(50,100,5)")
        expect(result).to be_between(50, 100)
        expect(result.modulo(5)).to eq(0)
      end
    end

    it "is in range" do
      100.times do
        result = config_parser.parse("number(5,10)")
        expect(result).to be_between(5, 10)
      end
    end

    it "is up to parameter" do
      100.times do
        result = config_parser.parse("number(600)")
        expect(result).to be_between(0, 600)
      end
    end
  end

  describe "generated lorem ipsum" do
    it "accepts up to 2 parameteres" do
      expect{config_parser.parse("sentences(50,60,5)")}.to raise_error(ArgumentError)
    end

    it "is one sentence" do
      result = config_parser.parse("sentences").split(".")
      expect(result.length).to eq(1)
    end

    it "is n sentences" do
      result = config_parser.parse("sentences(10)").split(".")
      expect(result.length).to eq 10
    end

    it "is between m,n sentences" do
      100.times do 
        result = config_parser.parse("sentences(5,10)").split(".")
        expect(result.length).to be_between(5,10)
      end
    end    
  end

end



