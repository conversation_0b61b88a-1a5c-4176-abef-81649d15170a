require_relative '../sweeperlib'

require 'sequel'
require 'sqlite3'

class TestDatabase
  def initialize(dbfile)
    @db_path = File.expand_path("../#{dbfile}", __FILE__)
  end
  def create_database
    File.delete(@db_path) if File.exists?(@db_path)
    db = Sequel.sqlite(@db_path)

    db.create_table :address do
      primary_key :id
      String :name
      String :address
      String :company_name
      Float :price
    end

    db.create_table :internet do
      primary_key :id
      String :email
      String :url
    end

    5.times do |n|
      db[:address].insert(name: 'aaa', address: 'aaa', company_name: 'bbb', price: 100)
      db[:internet].insert(url: 'aaa', email: 'bbb')
    end
    
    db[:address].insert(name: 'aaa', address: nil, company_name: 'bbb', price: 100)

  end
  def remove_database
    File.delete(@db_path) if File.exists?(@db_path)
  end
end

DB = TestDatabase.new('test.sqlite3')

# RSpec.configure do |config|
#   config.before(:each) do
#     DB.create_database
#   end
#   config.after(:each) do
#     DB.remove_database
#   end
# end