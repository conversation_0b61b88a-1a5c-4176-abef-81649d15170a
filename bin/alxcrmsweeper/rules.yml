# Use + sign before generator in order to overwrite ONLY
# non-null and non-blank fields.
#
# Use delete_all generator at the table level (no nesting)
# to delete all records in the table.
#
# non-trival generators:
#   sentences(5)
#     - 5 times Lorem Impsum sentence
#   sentences(4,9)
#     - 4 to 9 times Lorem Impsum sentence (random)
#   number(5)
#     - random number between 0 and 5
#   number(10,50)
#     - random number between 10 and 50
#   number(60,90,5)
#     - random number between 60 and 90 rounded to nearest 5 (60, 65, 70, ...)
#   django_password(password)
#     - salted sha1 hash of given password
#   sequence
#     - every call returns incremented number (starts at 0, does not reset)
#   sequence(username)
#     - every call returns incremented number with prefix (starts at 0, does not reset)
#     - username_1, username_2, username_3, ...
#   vatno
#     - Number matching NNN-NNN-NN-NN (no NIP validation, can start with 0)
#
# trival generators:
#   url, company_name, first_name, last_name, full_name, email, address,
#   phone, city_zip, ip_address

wynajmy_dokument:
  link: url
wynajmy_notatka:
  tekst: sentences(1,2)
wynajmy_wynajem:
  umowa: +url
  stawka_klienta: number(80, 170, 5)
  stawka_czlowieka: number(30, 70, 5)
wynajmy_oferta:
  stawka: number(80, 120, 5)
  tresc: +sentences(1,3)
wynajmy_klient:
  nazwa: company_name
  notatki: +sentences(1,3)
www_prowadzacy:
  calendar: sequence(calendar)
www_szkolenie:
  uwagi_internal: sentences(1,3)
www_uczestnik:
  imie_nazwisko: full_name
  osoba_do_kontaktu: +full_name
  email: email
  email_ksiegowosc: email
  telefon: phone
  adres: address
  miejscowosc_kod: city_zip
  uwagi: +sentences(1,3)
  uwagi_klienta: +sentences(1,3)
  faktura_firma: +company_name
  faktura_adres: +address
  faktura_miejscowosc_kod: +city_zip
  faktura_nip: +vatno
  faktura_vat_id: +number(100000, 1000000000)
  zliczacz_proforma_no: +number(10, 1000)
  zliczacz_faktura_no: +number(10, 1000)
  zliczacz_faktura_raty_no: +number(10, 1000)
  nr_proformy: +number(10,50)
  nr_faktury: +number(10,50)
  nr_faktury_raty: +number(10,50)
  odpowiedz_na_dodatkowe_pytanie: +sentences(1,3)
  bylem_na: +sentences(1,3)
  raty_nazwa_dokumentu: +sentences(1)
  raty_numer_dokumentu: +number(100000, 1000000000)
  raty_pesel: +number(100000, 1000000000)
www_graduate:
  name: full_name
  email: email
  slug: sequence(slug)
www_leave: delete_all
www_fakturawysylka: delete_all
www_fakturakorektapozycjakorygujaca: delete_all
www_fakturakorektapozycjaoryginalna: delete_all
www_fakturakorekta: delete_all
www_terminszkolenia:
  materialy_uwagi: +sentences(1,2)
www_zgloszenie:
  token: sequence(token)
  ip: ip_address
  imie_nazwisko: full_name
  osoba_do_kontaktu: +full_name
  email: email
  email_ksiegowosc: email
  telefon: phone
  adres: address
  miejscowosc_kod: city_zip
  faktura_firma: +company_name
  faktura_adres: +address
  faktura_miejscowosc_kod: +city_zip
  faktura_nip: +vatno
  faktura_vat_id: +number(100000, 1000000000)
  uwagi_klienta: +sentences(1,2)
  bylem_na: +sentences(1,2)
  raty_nazwa_dokumentu: +sentences(1)
  raty_numer_dokumentu: +number(100000, 1000000000)
  raty_pesel: +number(100000, 1000000000)
  odpowiedz_na_dodatkowe_pytanie: +sentences(1,2)
www_potencjalnychetny:
  imie_nazwisko: full_name
  email: email
  telefon: +phone
  firma: +company_name
  uwagi: +sentences(1,3)
auth_user:
  first_name: +first_name
  last_name: +last_name
  email: email
  password: django_password(test.pw)
ankiety_komentarz:
  tresc: +sentences(2,6)
ankiety_log:
  ip: ip_address
crm_firma:
  nazwa: +company_name
  email: +email
  telefon: +phone
  adres: +address
  osoba_do_kontaktu: +full_name
  wydanych_pieniedzy_zl: number(0, 20000, 10)
  wydanych_pieniedzy_gbp: number(0, 10000, 10)
  liczba_kupionych_terminow: number(10)
  liczba_przeszkolonych_uczestnikow: number(50)
crm_alternatywnanazwafirmy:
  nazwa: company_name
django_admin_log: delete_all
django_session: delete_all
www_change: delete_all
alx_auth_logininfo: delete_all
captcha_captcha: delete_all
celery_taskmeta: delete_all
djkombu_message: delete_all
djkombu_queue: delete_all
export_table: delete_all
reportengine_reportrequestrow: delete_all
reportengine_reportrequest: delete_all
www_autoresponderlog: delete_all
www_calendarupdate: delete_all
www_certyfikat: delete_all
www_terminszkolenialog: delete_all
www_continuationlog: delete_all
www_usercoursesnotification_locations: delete_all
www_usercoursesnotification: delete_all
www_usernotificationlog: delete_all
www_usernotification: delete_all
www_terminszkoleniamailuczestnik: delete_all
www_terminszkoleniamail: delete_all
www_uczestnikplik: delete_all
www_referencja:
  nazwa: sentences(1)
  uwagi: +sentences(1,10)
www_continuationunsubscribed:
  email: email
www_uczestniknotification:
  email: email
  message: sentences(2,15)
newsletter_odbiorca:
  email: email
  token: sequence(token)
  imie: +first_name
  nazwisko: +last_name
  telefon: +phone
  firma: +company_name
forum_post:
  ip: ip_address
  autor: first_name
  tresc: +sentences(2,3)
forum_watek:
  temat: +sentences(1)
www_lokalizacja:
  calendar: sequence(calendar)
www_sala:
  calendar: sequence(calendar)
www_discountcode:
  code: sequence(code)
  comment: +sentences(1,3)
leads_leaduser: delete_all
optout_optout: delete_all
