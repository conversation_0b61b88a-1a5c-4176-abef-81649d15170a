#!/usr/bin/env ruby

require 'active_record'
require 'pg'
require 'faker'
require 'yaml'

class Config<PERSON><PERSON><PERSON>
  def initialize(*args)
    Faker::Config.locale = args.first || :pl
  end

  %i[first_name last_name].each do |method|
    define_method(method) { Faker::Name.send(method) }
  end

  %i[url user_name].each do |method|
    define_method(method) { Faker::Internet.send(method) }
  end

  def email
    [Faker::Internet.email, "alx.pl"].join(".")
  end

  def sequence(*args)
    @seq ||= 0
    @seq += 1
    args.count > 0 ? "#{args.join}_#{@seq}" : @seq.to_s
  end

  def full_name
    [ first_name, last_name ].join(" ")
  end

  def phone
    Faker::PhoneNumber.phone_number
  end

  def company_name
    Faker::Company.name
  end

  def city_zip
    [ Faker::Address.city, Faker::Address.zip_code ].join(" ")
  end

  def address
    Faker::Address.street_address.squeeze
  end

  def nullify
    nil
  end

  def ip_address
    IPAddr.new(rand(2**32), Socket::AF_INET).to_s
  end

  def django_password(*args)
    algo = "sha1"
    salt = SecureRandom.hex(3)[0..4]
    digest = Digest::SHA1.hexdigest(salt + args.join)
    "%s$%s$%s" % [ algo, salt, digest ]
  end

  def vatno
    sum = 10
    nip = []
    while sum == 10
      nip = Array.new(9) { rand(10) }
      sum = nip.zip([6, 5, 7, 2, 3, 4, 5, 6, 7]).map{|a| a.inject(&:*)}.inject(&:+) % 11
    end
    (nip << sum).join
  end

  def number(*args)
    min, max, step = args.map!(&:to_i)
    case args.count
    when 1
      rand(min)
    when 2
      rand(max-min+1)+min
    when 3
      ((rand(max-min+1)+min)/step).round * step
    else
      raise ArgumentError
    end
  end

  def sentences(*args)
    args.map!(&:to_i).sort!
    case args.count
    when 0
      Faker::Lorem.sentence
    when 1
      Faker::Lorem.sentences(args).join(" ")
    when 2
      Faker::Lorem.sentences(rand((args[1]-args[0]+1).abs)+args[0]).join(" ")
    else
      raise ArgumentError
    end
  end

  def checkpresent?(method)
    method !~ /^\+/
  end

  def parse(method)
    name = method[/[a-z_]+/]
    if str = method.match(/\((.*)\)/)
      self.send name, *str[1].split(",")
    else
      self.send(name)
    end
  end
end

class Sweeper
  attr_accessor :logs, :rules, :dbconfig, :mode

  def initialize(rules_path, dbconfig_path)
    @dbconfig = YAML::load(File.read(dbconfig_path))
    @rules = YAML::load(File.read(rules_path))
    @logs = Hash.new(0)
    ActiveRecord::Base.establish_connection(@dbconfig)
    ActiveRecord::Base.connection.tables.each do |table|
      Object.send(:remove_const, table.camelize) if Object.const_defined?(table.camelize)
      Object.const_set table.camelize, Class.new(ActiveRecord::Base) { self.table_name = table }
    end
  end

  def run(cfg_parser)
    scope = @mode == :test ? ["first", 1] : "load"
    @rules.each do |klass, data|
      begin
        if data.is_a?(String) && data == "delete_all"
          klass.camelize.constantize.delete_all
        else
          klass.camelize.constantize.order(:id).send(*scope).each do |record|
            retries = 0
            begin
              attributes_hash =
                if data.is_a? Hash
                  data.inject({}) do |result, (table, method)|
                    if cfg_parser.checkpresent?(method) || record.send(table).present?
                      result.merge!(table => cfg_parser.parse(method))
                    end
                    result
                  end
                else
                  record.class.attribute_names.inject({}) do |result, name|
                    result.merge(name => cfg_parser.parse(data))
                  end
                end
              @logs[klass] += 1
              attributes_hash.delete("id")
              record.update_attributes(attributes_hash)
            rescue ActiveRecord::RecordNotUnique
              (retries += 1) <= 5 ? retry : raise
            end
          end
        end
      # rescue NameError => e
      #   puts "error: #{klass} does not exist? (#{e})"
      end
    end
  end
end
