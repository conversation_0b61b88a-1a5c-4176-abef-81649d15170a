#!/usr/bin/env python
import datetime
import os

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "settings")

import django

django.setup()

from django.conf import settings
from django.core.mail import EmailMessage, send_mail
from django.template.loader import render_to_string

from i18n.models import WersjaJezykowa
from www.models import TerminSzkolenia

today = datetime.date.today()

site_url = "https://" + WersjaJezykowa.biezaca().domain

dni = [today + datetime.timedelta(days=7)]

if today.isoweekday() == 5:
    dni += [today + datetime.timedelta(days=x) for x in [8, 9]]

terminy = TerminSzkolenia.objects.filter(termin__in=dni)

for t in terminy:
    chetnych = t.ilosc_uczestnikow(nierentowny=False, hybrydowe=True)
    if (chetnych == 0) and (t.odbylo_sie is not True):
        continue
    if (chetnych >= t.szkolenie.min_grupa) or t.odbylo_sie:
        mesg = render_to_string(
            "www/emails/przypominacz_grupa_rusza.html",
            {
                "termin": t,
                "site_url": site_url,
                "chetnych": chetnych,
            },
        )

        msg = EmailMessage(
            "Grupa rusza (%s - %s)" % (t.szkolenie.kod, t.termin),
            mesg,
            settings.MAIL_FROM_ADDRESS,
            [settings.MAIL_TERMINY],
        )
        msg.content_subtype = "html"
        msg.send()
    elif t.termin_nadrzedny and t.termin_nadrzedny.odbylo_sie:
        mesg = render_to_string(
            "www/emails/przypominacz_grupa_rusza.html",
            {
                "termin": t,
                "site_url": site_url,
                "chetnych": chetnych,
                "faktoryzacja": True,
            },
        )

        msg = EmailMessage(
            "Grupa rusza (%s - %s)" % (t.szkolenie.kod, t.termin),
            mesg,
            settings.MAIL_FROM_ADDRESS,
            [settings.MAIL_TERMINY],
        )
        msg.content_subtype = "html"
        msg.send()
    else:
        mesg = render_to_string(
            "www/emails/przypominacz_grupa_nie_rusza.html",
            {
                "termin": t,
                "site_url": site_url,
                "chetnych": chetnych,
            },
        )
        t.uczestnik_set.filter(status=1).update(status=0)

        msg = EmailMessage(
            "Grupa nie rusza (%s - %s)" % (t.szkolenie.kod, t.termin),
            mesg,
            settings.MAIL_FROM_ADDRESS,
            [settings.MAIL_TERMINY],
        )
        msg.content_subtype = "html"
        msg.send()
