#!/usr/bin/env python
import datetime
import os

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "settings")

import django

django.setup()

from django.conf import settings
from django.core.mail import send_mail
from django.template.loader import render_to_string

from i18n.models import WersjaJezykowa
from www.models import TerminSzkolenia

today = datetime.date.today()
today_7 = today + datetime.timedelta(days=settings.NIEPRZYGOTOWANE_SALE_CZAS)

terminy_0 = (
    TerminSzkolenia.objects.filter(
        sala_przygotowana=False, termin__lte=today_7, termin__gte=today
    )
    .filter(odbylo_sie=True)
    .order_by("termin")
)

terminy_1 = (
    TerminSzkolenia.objects.filter(sala_przygotowana=False, termin__gt=today_7)
    .filter(odbylo_sie=True)
    .order_by("termin")
)

terminy_old = (
    TerminSzkolenia.objects.filter(sala_przygotowana=False, termin__lt=today)
    .filter(odbylo_sie=True)
    .order_by("termin")
)

if terminy_0 or terminy_1 or terminy_old:
    site_url = "https://" + WersjaJezykowa.biezaca().domain
    mesg = render_to_string(
        "www/nieprzygotowane_sale.txt",
        {
            "terminy_0": terminy_0,
            "terminy_1": terminy_1,
            "terminy_old": terminy_old,
            "site_url": site_url,
        },
    )
    send_mail(
        "Sale do przygotowania",
        mesg,
        settings.MAIL_FROM_ADDRESS,
        [settings.MAIL_NIEPRZYGOTOWANE_SALE],
    )
