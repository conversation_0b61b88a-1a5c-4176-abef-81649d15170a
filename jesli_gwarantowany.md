# Instrukcja używania procedury jesli_gwarantowany

Procedura `jesli_gwarantowany` słu<PERSON>y do sprawdzania, czy dany termin szkolenia jest gwarantowany, i jeśli tak, to wyświetla odpowiedni tekst HTML.

## Składnia
```
[[jesli_gwarantowany:<id_szkolenia>;<indeks>;<tekst>]]
```

## Parametry
1. **id_szkolenia** (wymagany) - identyfikator szkolenia, może być liczbowym ID lub slugiem
2. **indeks** (wymagany) - indeks terminu (0 dla pierwszego terminu, 1 dla drugiego terminu, itd.)
3. **tekst** (opcjonalny) - tekst HTML do wyświetlenia, jeśli termin jest gwarantowany

## Przykłady u<PERSON>

```
[[jesli_gwarantowany:python-dla-poczatkujacych;0]]
```
<PERSON>pra<PERSON><PERSON><PERSON>, czy pierwszy termin dla szkolenia "python-dla-poczatkujacych" jest gwarantowany, i jeśli tak, to wyświetli domyślny tekst HTML.

```
[[jesli_gwarantowany:456;1;<span class="guaranteed">GWARANTOWANY</span>]]
```
Sprawdzi, czy drugi termin dla szkolenia o ID 456 jest gwarantowany, i jeśli tak, to wyświetli podany tekst HTML.

## Format wyniku
- Jeśli termin nie jest gwarantowany, zwraca pusty string
- Jeśli termin jest gwarantowany i nie podano własnego tekstu, zwraca domyślny tekst HTML:
  ```html
  <p style="color: red; margin: 0px; margin-bottom: 10px; font-size: smaller;">termin gwarantowany</p>
  ```
- Jeśli termin jest gwarantowany i podano własny tekst, zwraca podany tekst
