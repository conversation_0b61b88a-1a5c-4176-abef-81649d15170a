from django import forms
from django.urls import reverse
from django.utils.datastructures import MultiValueDict
from django.utils.safestring import mark_safe
from django.utils.translation import ugettext_lazy as _

from . import views
from .models import Captcha


class CaptchaWidget(forms.Widget):
    def render(self, name, value, attrs=None, renderer=None):
        captcha = Captcha()
        captcha.save()
        # FIXME: create django widget objects and call their render()s
        str = ""
        str += '<input type="hidden" name="%(name)s" value="%(id)d" />'
        str += (
            '<input type="text" size="10" name="%(name)s" value="" id="id_%(name)s" />'
        )
        str += (
            '<img alt="captcha" src="'
            + reverse(views.captcha, args=[captcha.id])
            + '" />'
        )
        return mark_safe(str % {"name": name, "id": captcha.id})

    def value_from_datadict(self, data, files, name):
        if isinstance(data, MultiValueDict):
            return data.getlist(name)
        return data.get(name, None)


class CaptchaField(forms.Field):
    widget = CaptchaWidget

    def clean(self, value):
        errmsg = _("Nieprawidłowy tekst")
        if not isinstance(value, (list, tuple)) or len(value) != 2:
            raise forms.ValidationError(errmsg)
        try:
            id = int(value[0])
        except ValueError:
            raise forms.ValidationError(errmsg)
        text = value[1]
        captchas = Captcha.objects.filter(id=id, text__iexact=text)
        if captchas.count() == 0:
            raise forms.ValidationError(errmsg)
        captchas.delete()
        return True
