import io
import random
from os import listdir, path

from django.conf import settings
from django.http import HttpResponse
from django.shortcuts import get_object_or_404
from django.views.decorators.cache import never_cache
from PIL import Image, ImageDraw, ImageFont

from .models import Captcha


@never_cache
def captcha(request, id):
    captcha = get_object_or_404(Captcha, id=id)
    return HttpResponse(gen_captcha(captcha.text), content_type="image/jpeg")


def gen_captcha(text):
    state = random.getstate()
    random.seed(text)

    cs = {
        "fgcolor": "#000000",  # default:  '#000000' (color for characters and lines)
        "bgcolor": "#ffffff",  # default:  '#ffffff' (color for background)
        "auto_cleanup": True,  # default:  True (delete all captchas older than 20 minutes)
        "minmaxvpos": (8, 15),  # default:  (8, 15) (vertical position of characters)
        "minmaxrotations": (-30, 31),  # default:  (-30,31) (rotate characters)
        "minmaxheight": (30, 45),  # default:  (30,45) (font size)
        "minmaxkerning": (-2, 1),  # default:  (-2,1) (space between characters)
        "num_lines": 1,  # default: 1
        "line_weight": 3,  # default: 3
        "imagesize": (200, 60),  # default: (200,60)
        "iterations": 1,  # default 1 (change to a high value (200 is a good choice)
        # for trying out new settings)
    }

    bgimage = Image.new("RGB", cs["imagesize"], cs["bgcolor"])

    imagesize = cs["imagesize"]
    posnew = 7
    fontdir = settings.CAPTCHA_FONT_PATH
    fontnames = [
        path.join(fontdir, x) for x in listdir(fontdir) if x.lower().endswith(".ttf")
    ]

    # generate characters
    solution = text

    # render characters
    for c in solution:
        fgimage = Image.new("RGB", imagesize, cs["fgcolor"])
        font = ImageFont.truetype(
            random.choice(fontnames), random.randrange(*cs["minmaxheight"])
        )
        charimage = Image.new("L", fontsize(font, " %s " % c), "#000000")
        draw = ImageDraw.Draw(charimage)
        draw.text((0, 0), " %s" % c, font=font, fill="#ffffff")
        try:
            charimage = charimage.rotate(
                random.randrange(*cs["minmaxrotations"]),
                expand=1,
                resample=Image.BICUBIC,
            )
        except TypeError:
            charimage = charimage.rotate(
                random.randrange(*cs["minmaxrotations"]), resample=Image.BICUBIC
            )
        charimage = charimage.crop(charimage.getbbox())
        maskimage = Image.new("L", imagesize)
        ypos = random.randrange(*cs["minmaxvpos"])
        maskimage.paste(
            charimage,
            (posnew, ypos, charimage.size[0] + posnew, charimage.size[1] + ypos),
        )
        bgimage = Image.composite(fgimage, bgimage, maskimage)
        posnew += charimage.size[0] + random.randrange(*cs["minmaxkerning"])

    # draw line(s)
    for dummy in range(cs.get("num_lines")):
        linex = random.choice(list(range(2, cs["minmaxheight"][1])))
        minmaxliney = (cs["minmaxvpos"][0], cs["minmaxvpos"][1] + cs["minmaxheight"][0])
        linepoints = [linex, random.randrange(*minmaxliney)]
        while linex < posnew:
            linex += random.randrange(*cs["minmaxheight"]) * 0.8
            linepoints.append(linex)
            linepoints.append(random.randrange(*minmaxliney))
        draw = ImageDraw.Draw(bgimage)
        try:
            draw.line(linepoints, width=cs["line_weight"], fill=cs["fgcolor"])
        except TypeError:
            draw.line(linepoints, fill=cs["fgcolor"])

    # save file
    f = io.BytesIO()
    bgimage.save(f, "JPEG")
    random.setstate(state)
    return f.getvalue()


def fontsize(font, text):
    # Pillow od wersji 2.1 nie uwzględnia offsetu w font.getsize()
    offset0, offset1 = 0, 0
    if hasattr(font, "getoffset"):
        offset0, offset1 = font.getoffset(text)
    return font.getsize(text)[0] + offset0, font.getsize(text)[1] + offset1
