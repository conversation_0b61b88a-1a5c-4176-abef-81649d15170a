import datetime

from django.core.management import call_command
from django.test import TestCase

from .models import Captcha


class WycinarkaCaptchyTestCase(TestCase):
    def test_wycinarki_starych_captchy(self):
        """
        Sprawd<PERSON>, czy wycinarka_starych_captchy rzeczywiście usuwa stare captche
        """
        wczoraj = datetime.date.today() + datetime.timedelta(days=-1)
        for i in range(1, 20):
            data_r = wczoraj + datetime.timedelta(days=-i)
            captcha_i = Captcha()
            captcha_i.save()
            captcha_i.created_at = data_r
            captcha_i.save()
        stare_captche = Captcha.objects.filter(created_at__lt=wczoraj)
        self.assertTrue(stare_captche.exists())
        call_command("wytnij_stare_captche")
        self.assertFalse(stare_captche.exists())

    def test_wycinarka_starych_captchy_nie_wycina_wczora<PERSON><PERSON><PERSON>(self):
        """
        Spra<PERSON><PERSON><PERSON><PERSON>, czy wczorajsze captche i nowsze są zachowywane przez wycinarkę.
        """
        wczoraj = datetime.date.today() - datetime.timedelta(days=1)
        for i in range(0, 20):
            data = wczoraj + datetime.timedelta(days=i)
            captcha = Captcha()
            captcha.save()
            captcha.created_at = data
            captcha.save()
        swieze_captche = Captcha.objects.filter(created_at__gte=wczoraj)
        liczba_przed = swieze_captche.count()
        self.assertTrue(liczba_przed)
        call_command("wytnij_stare_captche")
        liczba_po = swieze_captche.count()
        self.assertEqual(liczba_przed, liczba_po)
