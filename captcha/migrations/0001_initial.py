import datetime

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Captcha",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("text", models.TextField()),
                (
                    "created_at",
                    models.DateTimeField(
                        default=datetime.datetime.now, auto_now_add=True
                    ),
                ),
            ],
            options={},
            bases=(models.Model,),
        ),
    ]
