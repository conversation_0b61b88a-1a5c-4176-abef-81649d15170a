import random
from datetime import datetime

from django.db import models

REPERTOIRE = "abdeghkmnqrt2346789AEFGHKMNRT"
# REPERTOIRE = u'索本語集減筆字正體'
# REPERTOIRE = u'צץקרשתלמםנןסעפףזחטיכךאבגדהו'


class Captcha(models.Model):
    text = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)

    def save(self, *args, **kwargs):
        if not self.text:
            self.text = ""
            rnd = random.SystemRandom()
            for i in range(5):
                self.text += rnd.choice(REPERTOIRE)
        super().save(*args, **kwargs)

    def __str__(self):
        return "(%d) %s" % (self.id, self.text)
