import os

ROOT_PATH = os.path.dirname(__file__)

DEBUG = True

ADMINS = (
    ('Devel', '<EMAIL>'),
)

EMAIL_SUBJECT_PREFIX = '[alxcrm] '
SERVER_EMAIL = '<EMAIL>'

MANAGERS = ADMINS

SEND_BROKEN_LINK_EMAILS = False

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql_psycopg2',
        'NAME': 'dev_alxcrm',
        'USER': 'user_dev_alxcrm',
        'PASSWORD': 'postgres',
        'HOST': 'localhost',
    }
}

CELERY_BROKER_URL = 'memory://'
CELERY_ALWAYS_EAGER = True
CELERY_EAGER_PROPAGATES_EXCEPTIONS = True

GENERATE_INVOICE_THROUGH_API_ZLICZACZ = False
GENERATE_INVOICE_THROUGH_API_ZLICZACZ_DEBUG_MODE = True

API_ZLICZACZ = {
    'CREDENTIALS': {
        'USERNAME': 'api-zliczacz',
        'PASSWORD': "abc",
    },
    'INVOICES': {
        'ADD_INVOICE_URL': 'https://api-zliczacz.stdnet.local/invoices/',
        'UPDATE_INVOICE_URL': 'https://api-zliczacz.stdnet.local/invoices/{id}/',
        'GET_INVOICE_PDF_URL': 'https://api-zliczacz.stdnet.local/invoices/{id}/?format=pdf',
        'GET_INVOICE_URL': 'https://api-zliczacz.stdnet.local/invoices/{id}/',
        'LIST_INVOICE_URL': 'https://api-zliczacz.stdnet.local/invoices/',
        'SEARCH_INVOICE_URL': u'https://api-zliczacz.stdnet.local/invoices/?search={search}',
    }
}

NIEPRZYGOTOWANE_SALE_CZAS = 7

# Make this unique, and don't share it with anybody.
SECRET_KEY = 'abc'

MAIL_OBSLUGA = '<EMAIL>'

MAIL_FROM_ADDRESS = 'ALX Szkolenia Informatyczne <<EMAIL>>'
MAIL_FROM_ADDRESS_EN = 'ALX Training <<EMAIL>>'
MAIL_FROM_ADDRESS_ZGLOSZENIE = 'ALX Szkolenia Informatyczne <<EMAIL>>'
MAIL_FROM_ADDRESS_ZGLOSZENIE_EN = 'ALX Training <<EMAIL>>'
MAIL_FROM_ADDRESS_ZAPYTANIE = 'ALX <<EMAIL>>'
MAIL_FROM_ADDRESS_NOTIFICATION = 'ALX Szkolenia Informatyczne <<EMAIL>>'
MAIL_FROM_ADDRESS_NOTIFICATION_EN = 'ALX Training <<EMAIL>>'
MAIL_FROM_ADDRESS_AUTOREPLAY = 'ALX Szkolenia Informatyczne <<EMAIL>>'
MAIL_FROM_ADDRESS_AUTOREPLAY_EN = 'ALX Training <<EMAIL>>'
MAIL_FROM_ADDRESS_POTWIERDZENIE_TERMINU = 'Natalia Ruszkowska (ALX) <<EMAIL>>'
ALLOWED_MAIL_CONTACT_FORM_TO_ADDRESSES = ["<EMAIL>", "<EMAIL>"]

MAIL_POTENCJALNIE_CHETNI = '...'
MAIL_CONTACT_FORM_TO_ADDRESS = '...'
MAIL_TO_NOTIFICATION_ALERT = '...'
MAIL_ZGLOSZENIE_TO_ADDRESS = '...'
MAIL_PROPOZYCJA_TERMINU_TO_ADDRESS = '...'
MAIL_FORUM_TO_ADDRESS = '...'
MAIL_TERMINY = '...'
MAIL_WINDYKACJA = '...'
MAIL_CERTYFIKATY = '...'
MAIL_MATERIALY = '...'
MAIL_NIEPRZYGOTOWANE_SALE = '...'
NEWSLETTER_FROM_ADDRESS = '...'
MAIL_PLATNOSCI = "..."
MAIL_PLATNOSCI_MONITOR = "..."
MAIL_ADMINI = '...'
MAIL_KSIEGOWOSC = '...'
MAIL_ARCHIVE_MONITOR = '...'

EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'
EMAIL_FILE_PATH = '/tmp/email-messages/'

GOOGLE_API_PRIVATE_KEY_PATH = os.path.join(ROOT_PATH, 'google.json')
GOOGLE_API_USER_EMAIL = ''

UPDATE_CALENDARS_LOCK_FILE = '/home/<USER>/google.lock'

SECURE_SSL_REDIRECT = False
FORCE_SSL = False
SESSION_COOKIE_HTTPONLY = True
SESSION_COOKIE_AGE = 180 * 24 * 60 * 60
SESSION_EXPIRE_AT_BROWSER_CLOSE = False
SESSION_COOKIE_SECURE = False

AUTHENTICATION_BACKENDS = (
    'django.contrib.auth.backends.ModelBackend',
)

CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',

    }
}

INTERNAL_IPS = ["127.0.0.1"]

ALLOWED_HOSTS = ['*']

DOMENY_DLA_JEZYKOW = {
    'pl': 'localhost:8090',
    'en': 'localhost:8090'
}

WKHTMLTOPDF_CMD = '/usr/bin/wkhtmltopdf'

# TEST_SCREENSHOT_DIRECTORY = '.'
# SELENIUM_CHROME_SERVICE_ARGS = [
#    "--verbose",
#    "--log-path=/tmp/chromedriver.log"
# ]

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [
            os.path.join(ROOT_PATH, 'wynajmy/templates'),
            os.path.join(ROOT_PATH, 'www/templates'),
            os.path.join(ROOT_PATH, 'ankiety/templates'),
            os.path.join(ROOT_PATH, 'crm/templates'),
            os.path.join(ROOT_PATH, 'forum/templates'),
            os.path.join(ROOT_PATH, 'newsletter/templates'),
            os.path.join(ROOT_PATH, 'search/templates'),
        ],
        # "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                'www.context_processors.common_context_processor',
                'www.context_processors.debug',
                'alx_auth.context_processors.password_remind_info',

                'django.contrib.auth.context_processors.auth',
                'django.template.context_processors.debug',
                'django.template.context_processors.i18n',
                'django.template.context_processors.media',
                'django.template.context_processors.static',
                'django.template.context_processors.tz',
                'django.contrib.messages.context_processors.messages',
                'django.template.context_processors.request',
            ],
            "debug": True,
            'loaders': [
                'django.template.loaders.filesystem.Loader',
                'django.template.loaders.app_directories.Loader'
            ],
        },

    }
]

LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "handlers": {"console": {"level": "DEBUG", "class": "logging.StreamHandler"}},
    "loggers": {
        "alxcrm": {"level": "DEBUG", "handlers": ["console"]},
        "www": {"level": "DEBUG", "handlers": ["console"]},
    },
}
