# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-07 12:49+0200\n"
"PO-Revision-Date: 2013-11-28 23:09+0100\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: English <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: ankiety/forms.py:182
msgid "Wpisz adres e-mail"
msgstr ""

#: ankiety/forms.py:184
msgid "Wpisz imię i nazwisko"
msgstr ""

#: ankiety/forms.py:192
msgid "Taki adres email już istnieje."
msgstr ""

#: ankiety/forms.py:202
msgid "Taki Uczestnik już istnieje."
msgstr ""

#: ankiety/models.py:383 ankiety/templates/ankiety/wypelnianie.html:3
msgid "Ankieta dot. szkolenia"
msgstr ""

#: ankiety/templates/admin/edit_inline/stacked_with_links.html:12
#: www/templates/admin/index.html:724
msgid "Change"
msgstr ""

#: ankiety/templates/admin/edit_inline/stacked_with_links.html:12
msgid "View"
msgstr ""

#: ankiety/templates/admin/edit_inline/stacked_with_links.html:15
msgid "Edytuj"
msgstr ""

#: ankiety/templates/ankiety/podziekowanie.html:5
msgid "Dziękujemy za wypełnienie ankiety."
msgstr ""

#: ankiety/templates/ankiety/thankyou.html:4
msgid "Dziękujemy. Odbierz certyfikat."
msgstr ""

#: ankiety/templates/ankiety/thankyou.html:6
msgid ""
"Bardzo dziękujemy za wypełnienie ankiety - Twoja opinia pomoże nam cały czas "
"utrzymywać wysoki poziom naszych szkoleń i kursów."
msgstr ""

#: ankiety/templates/ankiety/thankyou.html:12
msgid "Odbierz Twój certyfikat"
msgstr ""

#: ankiety/templates/ankiety/thankyou.html:14
msgid ""
"\n"
"\t\t<p>\n"
"\t\tTwój certyfikat ukończenia szkolenia jest już gotowy! Zawiera on datę i "
"miejsce szkolenia oraz program - potwierdza umiejętności, które zdobyłeś.\n"
"\t\t</p>\n"
"\t\t<p>\n"
"\t\t<strong>Podaj adres e-mail oraz imię i nazwisko</strong> - w ciągu 24h "
"pracownik ALX prześle Ci link z certyfikatem!\n"
"\t\t</p>\n"
"    "
msgstr ""

#: ankiety/templates/ankiety/thankyou.html:35
msgid ""
"Administratorem podanych w formularzu danych osobowych jest ALX Academy sp. "
"z o.o. z siedzibą w Warszawie (00-041) przy ul. Jasnej 14/16. "
"(\"Administrator\"). Dane osobowe przetwarzane będą w zakresie niezbędnym do "
"wykonania umowy, której stroną jest osoba, której dane dotyczą (art. 6 ust. "
"1 b Ogólnego rozporządzenia Rady (UE) i PE o ochronie danych osobowych). "
"Dane będą przetwarzane przez okres niezbędny do realizacji określonego celu "
"przetwarzania. Osobie, której dane dotyczą przysługuje prawo dostępu do "
"treści swoich danych osobowych, ich sprostowania, usunięcia lub ograniczenia "
"przetwarzania, prawo do wniesienia sprzeciwu wobec przetwarzania, a także "
"prawo do żądania przenoszenia danych."
msgstr ""

#: ankiety/templates/ankiety/thankyou.html:38
msgid "Wyślij dane"
msgstr ""

#: ankiety/templates/ankiety/thankyou_done.html:4
#: ankiety/templates/ankiety/thankyou_done.html:39
#: www/templates/www/dziekujemy_za_propozycje_terminu.html:10
#: www/templates/www/partial/contact_form.html:12
#: www/templates/www/zadaj_szybkie_pytanie.html:54
#: www/templates/www/zadaj_szybkie_pytanie.html:60
msgid "Dziękujemy"
msgstr ""

#: ankiety/templates/ankiety/thankyou_done.html:21
msgid ""
"Dziękujemy za wysłanie zgłoszenia. Czy chcesz zapisać się na newsletter?"
msgstr ""

#: ankiety/templates/ankiety/thankyou_done.html:23
msgid ""
"Okresowo (średnio raz na 2-3 miesiące) wysyłamy informacje o nowych "
"technologiach o których uczymy, zmianach w naszej ofercie, pojawiających się "
"promocjach, terminach kursów i szkoleń oraz rozmaite teksty, ciekawostki itp."
msgstr ""

#: ankiety/templates/ankiety/thankyou_done.html:28
#: www/templates/www/zadaj_szybkie_pytanie.html:80
msgctxt "newsletter"
msgid ""
"Wyrażam zgodę na przetwarzanie danych osobowych w postaci adresu e-mail "
"przez ALX Academy sp. z o.o. z siedzibą w Warszawie (00-041) przy ul. Jasnej "
"14/16 w celu korzystania z usługi „Newsletter ALX”."
msgstr ""

#: ankiety/templates/ankiety/thankyou_done.html:33
#: www/templates/www/dziekujemy_za_propozycje_terminu.html:66
#: www/templates/www/zadaj_szybkie_pytanie.html:80
msgid ""
"Administratorem podanego adresu e-mail jest ALX Academy sp. z o.o. z "
"siedzibą w Warszawie (00-041) przy ul. Jasnej 14/16. Dane będą przetwarzane "
"w celu korzystania z „Newslettera ALX” przez okres niezbędny do realizacji "
"celu przetwarzania. Osobie, której dane dotyczą przysługuje prawo dostępu do "
"treści swoich danych i możliwości ich poprawiania. Powyższa zgoda może być "
"odwołana w każdym czasie, co skutkować będzie usunięciem podanego adresu e-"
"mail z listy dystrybucyjnej usługi „Newsletter ALX”. Z subskrypcji "
"„Newsletter ALX” można w dowolnym momencie zrezygnować, wystarczy wysłać "
"maila na adres: <EMAIL>."
msgstr ""

#: ankiety/templates/ankiety/thankyou_done.html:34
msgctxt "zapis do newslettera po ankiecie"
msgid "Tak"
msgstr ""

#: ankiety/templates/ankiety/thankyou_done.html:35
#: www/templates/www/dziekujemy_za_propozycje_terminu.html:61
#: www/templates/www/zadaj_szybkie_pytanie.html:86
msgid "Nie, dziękuję."
msgstr ""

#: ankiety/templates/ankiety/thankyou_done.html:49
msgid ""
"Zatem <b>nie</b> zapisujemy Pana/Pani na newsletter. Dziękujemy jeszcze raz "
"za wypełnienie ankiety!"
msgstr ""

#: ankiety/templates/ankiety/wypelnianie.html:15
msgid "Prosimy o uważne wypełnienie ankiety."
msgstr ""

#: ankiety/templates/ankiety/wypelnianie.html:41
msgid "(źle)"
msgstr ""

#: ankiety/templates/ankiety/wypelnianie.html:45
msgid "(dobrze)"
msgstr ""

#: ankiety/templates/ankiety/wypelnianie.html:80
msgctxt "wysylanie ankiety"
msgid "Wyślij"
msgstr ""

#: ankiety/views.py:418
msgid "Zostałeś poprawnie dodany do Newslettera."
msgstr ""

#: captcha/forms.py:38
msgid "Nieprawidłowy tekst"
msgstr ""

#: newsletter/models.py:151
msgid "Potwierdzenie zapisu na newsletter"
msgstr ""

#: newsletter/templates/newsletter/dziekujemy_za_zapisanie_sie.html:11
msgid "Dziękujemy za zapisanie się na nasz newsletter."
msgstr ""

#: newsletter/templates/newsletter/dziekujemy_za_zapisanie_sie.html:14
#: newsletter/templates/newsletter/juz_jest_na_liscie.html:14
#: newsletter/templates/newsletter/nie_ma_takiego_tokena.html:14
#: newsletter/templates/newsletter/ok_wyslalismy_mejla.html:14
#: www/templates/www/notifications/activate_subscription.html:22
#: www/templates/www/notifications/cancel_continuation.html:21
#: www/templates/www/notifications/cancel_subscription.html:8
#: www/templates/www/notifications/do_cancel_all_subscriptions.html:25
msgid "W razie problemów prosimy o kontakt pod adresem"
msgstr ""

#: newsletter/templates/newsletter/dziekujemy_za_zapisanie_sie.html:15
#: newsletter/templates/newsletter/juz_jest_na_liscie.html:15
#: newsletter/templates/newsletter/nie_ma_takiego_tokena.html:15
#: newsletter/templates/newsletter/ok_wyslalismy_mejla.html:15
#: www/templates/www/notifications/activate_subscription.html:23
#: www/templates/www/notifications/cancel_continuation.html:22
#: www/templates/www/notifications/cancel_subscription.html:9
#: www/templates/www/notifications/do_cancel_all_subscriptions.html:26
#: www/views.py:399 www/views.py:411
msgid "<EMAIL>"
msgstr ""

#: newsletter/templates/newsletter/juz_jest_na_liscie.html:11
msgid "Już wysłaliśmy prośbę o potwierdzenie na ten adres."
msgstr ""

#: newsletter/templates/newsletter/nie_ma_takiego_tokena.html:11
msgid ""
"Ten adres nie odpowiada żadnej prośbie o potwierdzenie. Być może został już "
"otworzony wcześniej."
msgstr ""

#: newsletter/templates/newsletter/ok_wyslalismy_mejla.html:11
msgid ""
"Aby potwierdzić chęć otrzymywania newslettera, kliknij w odnośnik, który "
"przed chwilą wysłaliśmy na podany adres e-mail."
msgstr ""

#: search/forms.py:10
msgid "Search"
msgstr ""

#: search/templates/search/search.html:39 www/templates/www/szukaj.html:5
msgid "Szukaj - ALX"
msgstr ""

#: search/templates/search/search.html:40 www/templates/www/szukaj.html:6
msgid "Wyszukiwanie"
msgstr ""

#: search/templates/search/search.html:50
msgid "Wyniki"
msgstr ""

#: search/templates/search/search.html:59
#: search/templates/search/search.html:72
msgid "Brak dopasowań."
msgstr ""

#: search/templates/search/search.html:65
msgid "Poprzednie"
msgstr ""

#: search/templates/search/search.html:67
msgid "Następne"
msgstr ""

#: www/forms.py:171
msgid "Kod rabatowy"
msgstr ""

#: www/forms.py:187 www/forms.py:297
msgid "osoba prywatna"
msgstr ""

#: www/forms.py:221
msgid "Nazwa dokumentu"
msgstr ""

#: www/forms.py:224
msgid "Numer dokumentu"
msgstr ""

#: www/forms.py:226 www/templates/www/dziekujemy_za_zgloszenie.html:198
msgid "PESEL"
msgstr ""

#: www/forms.py:228
msgid "Kraj"
msgstr ""

#: www/forms.py:231 www/models.py:4218
msgid "Polska"
msgstr ""

#: www/forms.py:232 www/models.py:4219
msgid "inny"
msgstr ""

#: www/forms.py:239 www/forms.py:739 www/views.py:1305 www/views.py:1418
msgid "Za kurs zapłacę"
msgstr ""

#: www/forms.py:243 www/forms.py:742 www/forms.py:1282
msgid "Wpisz tekst z obrazka"
msgstr ""

#: www/forms.py:244 www/forms.py:743
msgid "(wielkość liter nie ma znaczenia)"
msgstr ""

#: www/forms.py:248
msgid "Obiady wegetariańskie"
msgstr ""

#: www/forms.py:251
msgid "Zamawiam obiady"
msgstr ""

#: www/forms.py:252 www/forms.py:281
msgid "TAK"
msgstr ""

#: www/forms.py:252 www/forms.py:281
msgid "NIE"
msgstr ""

#: www/forms.py:258 www/forms.py:729
msgid "Faktura"
msgstr ""

#: www/forms.py:259 www/forms.py:730
msgid "elektroniczna"
msgstr ""

#: www/forms.py:259 www/forms.py:730
msgid "papierowa"
msgstr ""

#: www/forms.py:264 www/forms.py:735
msgid ""
"Wybór faktury elektronicznej oznacza rezygnację z faktury papierowej i "
"wyrażenie zgody na przesłanie faktury e-mailem. Zmiana formy wysyłki faktury "
"po wystawieniu faktury końcowej wiąże się z dopłatą 50 zł netto."
msgstr ""

#: www/forms.py:269 www/templates/www/certificates/certificate.html:95
#: www/templates/www/certificates/public_certificate.html:8
msgid "Certyfikat"
msgstr ""

#: www/forms.py:270
msgid "elektroniczny"
msgstr ""

#: www/forms.py:270
msgid "elektroniczny i drukowany"
msgstr ""

#: www/forms.py:275
msgid ""
"Zmiana formy wystawienia certyfikatu po wystawieniu faktury końcowej wiąże "
"się z dopłatą 50 zł netto / osobę."
msgstr ""

#: www/forms.py:280
msgid "Zamawiam autoryzację"
msgstr ""

#: www/forms.py:287 www/forms.py:746
#: www/templates/www/dziekujemy_za_zgloszenie.html:237
msgid "Liczność grupy"
msgstr ""

#: www/forms.py:290 www/forms.py:749
msgid "Wpisać łączną liczbę dla potwierdzenia"
msgstr ""

#: www/forms.py:296
msgid "Zgłaszam się jako"
msgstr ""

#: www/forms.py:297 www/models.py:4151
msgid "firma"
msgstr ""

#: www/forms.py:304
msgid "Liczba zgłaszanych osób"
msgstr ""

#: www/forms.py:305
msgid "pojedyncza osoba"
msgstr ""

#: www/forms.py:305
msgid "grupa"
msgstr ""

#: www/forms.py:421
msgid "Kod rabatowy nie jest dostępny dla tej opcji płatności."
msgstr ""

#: www/forms.py:430
msgid "Kod rabatowy jest nieważny lub został wykorzystany."
msgstr ""

#: www/forms.py:436 www/forms.py:496 www/forms.py:505 www/forms.py:601
#: www/forms.py:606 www/forms.py:614 www/forms.py:625 www/forms.py:629
#: www/forms.py:925
msgid "To pole jest wymagane."
msgstr ""

#: www/forms.py:442 www/forms.py:470
msgid "Wybierz poprawną wartość."
msgstr ""

#: www/forms.py:462
msgid "To pole jest wymagane dla klientów firmowych."
msgstr ""

#: www/forms.py:540
msgid ""
"Liczba obiadów wegetariańskich nie może być większa niż liczba uczestników."
msgstr ""

#: www/forms.py:552 www/forms.py:935
msgid "Proszę wpisać dokonane ustalenia odnośnie trybu płatności"
msgstr ""

#: www/forms.py:561
msgid "Proszę wpisać liczność grupy."
msgstr ""

#: www/forms.py:573
msgid "Osoba prywatna nie może zgłosić grupy"
msgstr ""

#: www/forms.py:574
msgid "Jeżeli chcesz zapisać na szkolenie kilka prywatnych osób"
msgstr ""

#: www/forms.py:575
msgid "każda z nich musi musi przysłać osobny formularz"
msgstr ""

#: www/forms.py:588
msgid "Raty są dostępny tylko dla osób prywatnych."
msgstr ""

#: www/forms.py:593
msgid "Opcja niedostępna dla osób prywatnych."
msgstr ""

#: www/forms.py:620
msgid "Wprowadź numer dowodu w formacie XXXYYYYYY."
msgstr ""

#: www/forms.py:711 www/forms.py:752
msgid "Chcę zapisać się na newsletter"
msgstr ""

#: www/forms.py:715 www/forms.py:756
msgid ""
"Wyrażam zgodę na otrzymywanie informacji handlowych wysyłanych przez ALX "
"Academy sp. z o.o. na wyżej podany adres e-mail zgodnie z ustawą o "
"świadczeniu usług drogą elektroniczną (Dz.U. z 2002 r. Nr 144, poz. 1204)."
msgstr ""

#: www/forms.py:719
msgid "Akceptuję regulamin"
msgstr ""

#: www/forms.py:721
msgid ""
"Oświadczam, że zapoznałam/em się z <a href=\"/pl/regulamin/\" "
"target=\"_blank\">regulaminem</a> i akceptuję jego treść oraz warunki."
msgstr ""

#: www/forms.py:813
msgid "Lista osób"
msgstr ""

#: www/forms.py:814
msgid "Może zostać uzupełnione później."
msgstr ""

#: www/forms.py:1052
msgid ""
"I declare that I have read the <a href=\"/en/terms/\" "
"target=\"_blank\">terms and conditions</a>, I fully understand them and "
"accept them."
msgstr ""

#: www/forms.py:1263
msgid "Proponowany termin (RRRR-MM-DD)"
msgstr ""

#: www/forms.py:1265
msgid "Orientacyjna liczba osób kierowanych na szkolenie"
msgstr ""

#: www/forms.py:1270
msgid "Lokalizacja"
msgstr ""

#: www/forms.py:1271 www/templates/www/dziekujemy_za_zgloszenie.html:178
#: www/templates/www/notifications/_personal_data_form.html:16
msgid "Imię i nazwisko"
msgstr ""

#: www/forms.py:1273
msgid "Nazwa firmy"
msgstr ""

#: www/forms.py:1275
msgid "Email"
msgstr ""

#: www/forms.py:1277
msgid "Telefon kontaktowy"
msgstr ""

#: www/forms.py:1280
msgid "Uwagi"
msgstr ""

#: www/forms.py:1295
msgid "inna lokalizacja"
msgstr ""

#: www/forms.py:2531
msgid "Musisz odczekać 30 minut pomiędzy kolejnymi próbami."
msgstr ""

#: www/forms.py:2541
#, python-format
msgid "Maksymalny rozmiar pliku to %(size)sMB"
msgstr ""

#: www/forms.py:2569
msgid "Limit dodanych plików został osiągnięty."
msgstr ""

#: www/management/commands/continuation_training.py:97
msgid "Chcesz kontynuować naukę? Masz rabat na kolejny kurs."
msgstr ""

#: www/management/commands/resend_activation_email.py:78
#: www/management/commands/resend_activation_email.py:148 www/views.py:2764
msgid ""
"[Wymagana reakcja] Potwierdź chęć otrzymywania powiadomień z ALX (www.alx.pl)"
msgstr ""

#: www/management/commands/sixteen_days_before_start.py:87
msgid "Przypomnienie o"
msgstr ""

#: www/management/commands/sixteen_days_before_start.py:88
msgid "kursie"
msgstr ""

#: www/management/commands/sixteen_days_before_start.py:88
msgid "szkoleniu"
msgstr ""

#: www/management/commands/sixteen_days_before_start.py:89
#: www/templates/www/dziekujemy_za_zgloszenie.html:247
msgid "dla"
msgstr ""

#: www/management/commands/user_notifications.py:274 www/tasks.py:846
msgid "Powiadomienie z firmy ALX (www.alx.pl)"
msgstr ""

#: www/models.py:645
msgid "Treść"
msgstr ""

#: www/models.py:645
msgid "Można używać znaczników HTML."
msgstr ""

#: www/models.py:649 www/models.py:4133
msgid "imię i nazwisko"
msgstr ""

#: www/models.py:655
msgid "ID opinii źródłowej"
msgstr ""

#: www/models.py:776
msgid "nazwa"
msgstr ""

#: www/models.py:777
msgid "opis krótki"
msgstr ""

#: www/models.py:779
msgid "opis długi"
msgstr ""

#: www/models.py:783 www/models.py:1331
msgid "autoryzacja"
msgstr ""

#: www/models.py:784
msgid "autoryzacje"
msgstr ""

#: www/models.py:1209
msgid "dzień"
msgstr ""

#: www/models.py:1209
msgid "dni"
msgstr ""

#: www/models.py:1279
msgid "cena"
msgstr ""

#: www/models.py:1325
msgid "opis egzaminu"
msgstr ""

#: www/models.py:1337
msgid "cena autoryzacji"
msgstr ""

#: www/models.py:1340
msgid "nazwa autoryzacji"
msgstr ""

#: www/models.py:1342
msgid "kod autoryzacji"
msgstr ""

#: www/models.py:1396
msgid "Uczestnicy szkolenia otrzymują imienne certyfikaty sygnowane przez ALX."
msgstr ""

#: www/models.py:1432
msgid ""
"na życzenie dowolne miejsce w Polsce, lub UE (zajęcia prowadzone w języku "
"angielskim)"
msgstr ""

#: www/models.py:1746
msgid "szkolenie"
msgstr ""

#: www/models.py:1746
msgid "kurs"
msgstr ""

#: www/models.py:2001 www/tasks.py:111 www/views.py:1708
msgid "dzienny"
msgstr ""

#: www/models.py:2001 www/tasks.py:111 www/views.py:1709
msgid "wieczorowy"
msgstr ""

#: www/models.py:2001 www/tasks.py:111
msgid "zaoczny"
msgstr ""

#: www/models.py:3234 www/tasks.py:153
msgid "oraz"
msgstr ""

#: www/models.py:4078
msgid "od razu pełną kwotę"
msgstr ""

#: www/models.py:4082
msgid ""
"wpłacając teraz zaliczkę 500 PLN i pozostałą kwotę do siedmiu dni przed "
"rozpoczęciem kursu"
msgstr ""

#: www/models.py:4089
msgid ""
"(tylko klient indywidualny) wpłacając teraz zaliczkę 500 PLN i pozostałą "
"kwotę w dwóch równych ratach, zgodnie z <a href='/pl/raty/'>zasadami "
"płatności ratalnej</a>"
msgstr ""

#: www/models.py:4098
msgid ""
"(tylko klient indywidualny) wpłacając kwotę w pięciu ratach, zgodnie z <a "
"href='/pl/raty/'>zasadami płatności ratalnej</a>"
msgstr ""

#: www/models.py:4105 www/models.py:4119
msgid ""
"inny tryb płatności lub umowa, ustalone indywidualnie (proszę wpisać "
"dokonane ustalenia w polu uwagi dodatkowe)"
msgstr ""

#: www/models.py:4112
msgid ""
"standard - płatność w oparciu o fakturę pro forma na 7 dni przed "
"rozpoczęciem szkolenia"
msgstr ""

#: www/models.py:4115
msgid "płatność po szkoleniu, 14 dni po zakończeniu, dopłata +5%"
msgstr ""

#: www/models.py:4134
msgid "lub lista osób, jeżeli zgłoszenie wieloosobowe"
msgstr ""

#: www/models.py:4137
msgid "osoba do kontaktu i rozliczeń"
msgstr ""

#: www/models.py:4139
msgid ""
"Jeśli inna niż uczestnik, np. pracownik organizujący szkolenie lub rodzic w "
"przypadku niepełnoletnich uczestników."
msgstr ""

#: www/models.py:4145
msgid "jestem osobą prywatną"
msgstr ""

#: www/models.py:4147
msgid "proszę o wystawienie papierowej faktury"
msgstr ""

#: www/models.py:4152
msgid "adres"
msgstr ""

#: www/models.py:4154 www/models.py:4317
msgid "miejscowość, kod pocztowy"
msgstr ""

#: www/models.py:4157
msgid "kraj"
msgstr ""

#: www/models.py:4159 www/templates/www/dziekujemy_za_zgloszenie.html:211
msgid "NIP"
msgstr ""

#: www/models.py:4161
msgid "lub ID firmy"
msgstr ""

#: www/models.py:4165
msgid "zamawiam egzamin"
msgstr ""

#: www/models.py:4169
msgid "zamawiam autoryzację"
msgstr ""

#: www/models.py:4174
msgid "dla ilu osób obiady wegetariańskie"
msgstr ""

#: www/models.py:4181
msgid ""
"byłem wcześniej u was na szkoleniu (dotyczy także innych pracowników firmy)"
msgstr ""

#: www/models.py:4187
msgid ""
"Jeśli tak, proszę podać nazwę szkolenia i orientacyjną datę jego rozpoczęcia"
msgstr ""

#: www/models.py:4228
msgid "Odpowiedź na dodatkowe pytanie"
msgstr ""

#: www/models.py:4231
msgid "wiek uczestnika"
msgstr ""

#: www/models.py:4302
msgid "oraz miasto i tryb zajęć"
msgstr ""

#: www/models.py:4303
msgid "termin"
msgstr ""

#: www/models.py:4308
msgid "email uczestnika"
msgstr ""

#: www/models.py:4308
msgid "W przypadku grup adres osoby kontaktowej."
msgstr ""

#: www/models.py:4311
msgid "email osoby do rozliczeń"
msgstr ""

#: www/models.py:4312
msgid "Jeśli inny niż powyższy"
msgstr ""

#: www/models.py:4315
msgid "telefon do kontaktu"
msgstr ""

#: www/models.py:4316
msgid "adres korespondencyjny"
msgstr ""

#: www/models.py:4320
msgid "podmiot publiczny lub finansowanie ze środków publicznych"
msgstr ""

#: www/models.py:4323
msgid ""
"Zaznaczenie opcji upoważnia do zwolnienia z VAT. W razie pytań lub "
"wątpliwości prosimy o kontakt."
msgstr ""

#: www/models.py:4327
msgid "liczność grupy"
msgstr ""

#: www/models.py:4330
msgid "w przypadku zgłaszania grupy proszę wpisać liczbę osób"
msgstr ""

#: www/models.py:4333
msgid "zamawiam obiady"
msgstr ""

#: www/models.py:4339
msgid "Uwagi dodatkowe"
msgstr ""

#: www/models.py:4527
msgid "Potwierdzam, że dane są poprawne i nadają się umieszczenia na fakturze"
msgstr ""

#: www/models.py:4530
msgid "W jednej linii powinno być tylko jedno imię i nazwisko, nic poza tym."
msgstr ""

#: www/models.py:6165
msgid "obrazek"
msgstr ""

#: www/tasks.py:66
#, python-format
msgid "[Wymagana reakcja] ALX - potwierdzenie zgłoszenia dla %(nazwa)s"
msgstr ""

#: www/tasks.py:87
msgid "kursu"
msgstr ""

#: www/tasks.py:87
msgid "szkolenia"
msgstr ""

#: www/tasks.py:90
msgid "Potwierdzenie"
msgstr ""

#: www/tasks.py:98
msgid "stycznia"
msgstr ""

#: www/tasks.py:99
msgid "lutego"
msgstr ""

#: www/tasks.py:100
msgid "marca"
msgstr ""

#: www/tasks.py:101
msgid "kwietnia"
msgstr ""

#: www/tasks.py:102
msgid "maja"
msgstr ""

#: www/tasks.py:103
msgid "czerwca"
msgstr ""

#: www/tasks.py:104
msgid "lipca"
msgstr ""

#: www/tasks.py:105
msgid "sierpnia"
msgstr ""

#: www/tasks.py:106
msgid "września"
msgstr ""

#: www/tasks.py:107
msgid "października"
msgstr ""

#: www/tasks.py:108
msgid "listopada"
msgstr ""

#: www/tasks.py:109
msgid "grudnia"
msgstr ""

#: www/tasks.py:114
msgid "tryb"
msgstr ""

#: www/tasks.py:116
msgid "tryb weekendowy"
msgstr ""

#: www/templates/404.html:10
msgid "Błąd 404"
msgstr ""

#: www/templates/404.html:121
msgid ""
"\n"
"\t\t\t\t<div>Nieprawidłowy adres - nie ma strony o takim adresie,<br> Wróć "
"do <a href=\"/\">strony głównej</a></div>\n"
"                "
msgstr ""

#: www/templates/404.html:124
msgid "BŁĄD: 404"
msgstr ""

#: www/templates/admin/ankiety/zestawienie.html:3
#: www/templates/admin/base_site.html:4 www/templates/admin/base_site.html:7
msgid "ALX - administracja"
msgstr ""

#: www/templates/admin/base_site.html:35
msgid "Change password"
msgstr ""

#: www/templates/admin/base_site.html:38
msgid "Log out"
msgstr ""

#: www/templates/admin/change_form.html:10
msgid "History"
msgstr ""

#: www/templates/admin/change_form.html:11
#: www/templates/www/admin/terminszkolenia_inline.html:13
#: wynajmy/templates/admin/edit_inline/stacked.html:9
#: wynajmy/templates/admin/edit_inline/tabular.html:36
msgid "View on site"
msgstr ""

#: www/templates/admin/dropdown_filter.html:3
#: www/templates/admin/multiple_choice_list_filter/filter.html:2
#, python-format
msgid " By %(filter_title)s "
msgstr ""

#: www/templates/admin/dropdown_filter.html:16
msgid "Apply"
msgstr ""

#: www/templates/admin/dropdown_filter.html:17
msgid "Clear"
msgstr ""

#: www/templates/admin/dropdown_filter.html:27
msgid "Wybierz prowadzących"
msgstr ""

#: www/templates/admin/index.html:704
#, python-format
msgid "Models in the %(name)s application"
msgstr ""

#: www/templates/admin/index.html:717
msgid "Add"
msgstr ""

#: www/templates/admin/index.html:735
msgid "You don't have permission to edit anything."
msgstr ""

#: www/templates/admin/www/terminszkolenia/change_list_results.html:19
msgid "Remove from sorting"
msgstr ""

#: www/templates/admin/www/terminszkolenia/change_list_results.html:20
#, python-format
msgid "Sorting priority: %(priority_number)s"
msgstr ""

#: www/templates/admin/www/terminszkolenia/change_list_results.html:21
msgid "Toggle sorting"
msgstr ""

#: www/templates/admin/www/usernotification/change_list.html:22
#, python-format
msgid "Add %(name)s"
msgstr ""

#: www/templates/admin/www/usernotification/reports.html:5
msgid "Django site admin"
msgstr ""

#: www/templates/admin/www/usernotification/reports.html:58
msgid "Home"
msgstr ""

#: www/templates/admin/www/usernotification/reports.html:78
msgid "Filter"
msgstr ""

#: www/templates/www/base.html:28
msgid ""
"Szkolenia Linux, PHP, Java, programowanie, bazy danych i Office. Warszawa, "
"Kraków, Wrocław, Łódź, Poznań, Katowice, Gdańsk. Intensywne kursy oraz "
"profesjonalne szkolenia dla firm."
msgstr ""

#: www/templates/www/base.html:98 www/templates/www/base.html:99
msgid "Szukaj"
msgstr ""

#: www/templates/www/base.html:215 www/templates/www/kurs_actions.old.html:21
#: www/templates/www/notyfikacje_i_terminy_inline.html:16
#: www/templates/www/szkolenie_actions.old.html:27
msgid "Zapisz się"
msgstr ""

#: www/templates/www/base.html:217
msgid "Zapisz się na kurs"
msgstr ""

#: www/templates/www/base.html:225
msgid "Powiadomienie o terminach"
msgstr ""

#: www/templates/www/base.html:254
msgid "Ustaw powiadomienie o kolejnych terminach"
msgstr ""

#: www/templates/www/base.html:263
msgid "Zobacz próbkę materiałów do tego szkolenia"
msgstr ""

#: www/templates/www/certificates/certificate.html:8
msgid "Zarządzaj certyfikatem"
msgstr ""

#: www/templates/www/certificates/certificate.html:42
msgid "e-Certyfikat"
msgstr ""

#: www/templates/www/certificates/certificate.html:44
#, python-format
msgid ""
"\n"
"    \t\t\t\t\t\tTo elektroniczne potwierdzenie szkolenia zrealizowanego "
"przez ALX.<br>\n"
"                                - jesteśmy na rynku szkoleń informatycznych "
"od %(years)s lat<br>\n"
"                                - szkolenia prowadzone są przez wybitnych "
"specjalistów<br>\n"
"                                - posiadamy wpis do rejestru instytucji "
"szkoleniowych (RIS)\n"
"                            "
msgstr ""

#: www/templates/www/certificates/certificate.html:70
msgid "Pobierz PDF"
msgstr ""

#: www/templates/www/certificates/certificate.html:73
msgid "Uzyskaj link do udostępniania"
msgstr ""

#: www/templates/www/certificates/certificate.html:77
msgid "Link do publicznej wersji Twojego certyfikatu"
msgstr ""

#: www/templates/www/certificates/certificate.html:81
msgid ""
"Skopiuj poniższy link i wklej gdziekolwiek zechcesz. Każdy kto otrzyma ten "
"link zobaczy Twój certyfikat."
msgstr ""

#: www/templates/www/certificates/certificate.html:97
#: www/templates/www/certificates/certificate_pdf_content.html:14
msgid "Numer certyfikatu"
msgstr ""

#: www/templates/www/certificates/certificate.html:148
#: www/templates/www/certificates/certificate.html:155
#: www/templates/www/certificates/certificate.html:161
#: www/templates/www/certificates/certificate_pdf_content.html:65
msgid "Ramowy program kursu"
msgstr ""

#: www/templates/www/certificates/certificate.html:153
msgid "czytaj dalej"
msgstr ""

#: www/templates/www/certificates/certificate.html:169
#: www/templates/www/certificates/certificate.html:198
#: www/templates/www/certificates/certificate_pdf_content.html:73
#: www/templates/www/certificates/certificate_pdf_content.html:88
msgid "Trener"
msgstr ""

#: www/templates/www/certificates/certificate.html:183
msgid "Więcej informacji o trenerze"
msgstr ""

#: www/templates/www/certificates/certificate.html:184
msgid "Zwiń informację o trenerze"
msgstr ""

#: www/templates/www/certificates/certificate_group_owner.html:21
#: www/templates/www/zaproponuj_termin.html:89
msgid "Wyślij"
msgstr ""

#: www/templates/www/certificates/certificate_group_owner.html:22
msgid "Certyfikat został wysłany."
msgstr ""

#: www/templates/www/certificates/certificate_group_owner.html:24
msgid "Wysłano dnia"
msgstr ""

#: www/templates/www/certificates/certificate_pdf_content.html:11
msgid "C e r t y f i k a t"
msgstr ""

#: www/templates/www/detail.html:36 www/templates/www/detail.html:38
#: www/templates/www/szkolenie_actions.html:12
msgid "Szkolenie"
msgstr ""

#: www/templates/www/detail.html:69
msgid "Kategoria"
msgid_plural "Kategorie"
msgstr[0] ""
msgstr[1] ""

#: www/templates/www/detail.html:91
msgid "Czas trwania"
msgstr ""

#: www/templates/www/detail.html:105
msgid "Program"
msgstr ""

#: www/templates/www/detail.html:113
msgid "Pobierz w wersji PDF"
msgstr ""

#: www/templates/www/detail.html:121
msgid "Przeznaczenie i wymagania"
msgstr ""

#: www/templates/www/detail.html:130
msgid "Brak szczegółowych wymagań wobec uczestników szkolenia."
msgstr ""

#: www/templates/www/detail.html:133
msgid "Certyfikaty"
msgstr ""

#: www/templates/www/detail.html:143
msgid "Informacje dodatkowe"
msgstr ""

#: www/templates/www/detail.html:163
msgid "Dokładne harmonogramy kursów"
msgstr ""

#: www/templates/www/detail.html:166
msgctxt "detail.html"
msgid "Najbliższe terminy"
msgstr ""

#: www/templates/www/detail.html:199
msgid "Lokalizacje"
msgstr ""

#: www/templates/www/detail.html:265
#: www/templates/www/szkolenie_actions.html:389
msgid "Cena szkolenia"
msgstr ""

#: www/templates/www/dziekujemy_za_propozycje_terminu.html:22
msgid ""
"Dziękujemy za propozycję terminu. Skontaktujemy się celem omówienia "
"szczegółów."
msgstr ""

#: www/templates/www/dziekujemy_za_propozycje_terminu.html:37
msgid "Dziękujemy za propozycję terminu"
msgstr ""

#: www/templates/www/dziekujemy_za_propozycje_terminu.html:40
msgid "Skontaktujemy się celem omówienia szczegółów"
msgstr ""

#: www/templates/www/dziekujemy_za_propozycje_terminu.html:47
msgid "Czy chcesz otrzymywać newsletter?"
msgstr ""

#: www/templates/www/dziekujemy_za_propozycje_terminu.html:50
msgid ""
"Raz w miesiącu przesyłamy informacje o nowych technologiach związanych z "
"naszymi skoleniami, o większych zmianach w naszej ofercie oraz okresowo "
"pojawiających się promocjach. Informujemy także o terminach kursów "
"zawodowych w semestrach zimowym i letnim oraz sporadycznie przesyłamy "
"artykuły, ciekawostki i zagadki."
msgstr ""

#: www/templates/www/dziekujemy_za_propozycje_terminu.html:56
msgid "Tak, chcę otrzymywać newsletter."
msgstr ""

#: www/templates/www/dziekujemy_za_propozycje_terminu.html:57
msgctxt "newsletter"
msgid ""
"Wyrażam zgodę na przetwarzanie danych osobowych w postaci adresu e-mail "
"przez ALX Academy sp. z o.o. z siedzibą w Warszawie (00-041) przy ul. "
"Jasnej\n"
"              14/16 w celu korzystania z usługi „Newsletter ALX”."
msgstr ""

#: www/templates/www/dziekujemy_za_propozycje_terminu.html:70
#: www/templates/www/notifications/_personal_data_form.html:32
#: www/templates/www/notifications/ajax_subscription_form.html:51
#: www/templates/www/notyfikacje_i_terminy_inline.html:62
msgid "Zapisz"
msgstr ""

#: www/templates/www/dziekujemy_za_zgloszenie.html:43
#: www/templates/www/dziekujemy_za_zgloszenie.html:97
msgid "Dziękujemy za wypełnienie formularza."
msgstr ""

#: www/templates/www/dziekujemy_za_zgloszenie.html:55
msgid ""
"Dla formalnego potwierdzenia zgłoszenia <strong>konieczne jest</strong> "
"wydrukowanie niniejszego formularza, podpisanie i przesłanie jako skan albo "
"zdjęcie e-mailem <NAME_EMAIL>."
msgstr ""

#: www/templates/www/dziekujemy_za_zgloszenie.html:57
msgid ""
"Dla formalnego potwierdzenia zgłoszenia <strong>konieczne jest</strong> "
"wydrukowanie niniejszego formularza, podpisanie i przesłanie nam:"
msgstr ""

#: www/templates/www/dziekujemy_za_zgloszenie.html:58
msgid ""
"faksem (22 266 06 95), lub jako skan albo zdjęcie e-mailem "
"(<EMAIL>)."
msgstr ""

#: www/templates/www/dziekujemy_za_zgloszenie.html:85
msgid ""
"Link do tej strony wysłaliśmy również na podany w zgłoszeniu adres e-mail."
msgstr ""

#: www/templates/www/dziekujemy_za_zgloszenie.html:92
#: www/templates/www/dziekujemy_za_zgloszenie.html:142
msgid "Wydrukuj formularz"
msgstr ""

#: www/templates/www/dziekujemy_za_zgloszenie.html:135
msgid ""
"Link do tej strony wysłaliśmy również na podany w zgłoszeniu adres email."
msgstr ""

#: www/templates/www/dziekujemy_za_zgloszenie.html:156
msgid "Formularz zgłoszeniowy (zamówienie) − szkolenie zamknięte"
msgstr ""

#: www/templates/www/dziekujemy_za_zgloszenie.html:158
msgid "Formularz zgłoszeniowy − zamówienie"
msgstr ""

#: www/templates/www/dziekujemy_za_zgloszenie.html:161
msgid "KOD"
msgstr ""

#: www/templates/www/dziekujemy_za_zgloszenie.html:162
#: www/templates/www/last_minute.html:21 www/templates/www/najblizsze.html:108
msgid "Termin"
msgstr ""

#: www/templates/www/dziekujemy_za_zgloszenie.html:163
msgid "Cena detaliczna"
msgstr ""

#: www/templates/www/dziekujemy_za_zgloszenie.html:163
msgid "uwzględniono rabat w wysokości"
msgstr ""

#: www/templates/www/dziekujemy_za_zgloszenie.html:168
#, python-format
msgid ""
"Cena zawiera dopłatę w wysokości %(cena_papierowej_faktury)s PLN netto za "
"przesłanie papierowej faktury."
msgstr ""

#: www/templates/www/dziekujemy_za_zgloszenie.html:176
msgid "Lista uczestników"
msgstr ""

#: www/templates/www/dziekujemy_za_zgloszenie.html:180
msgid "uczestnika"
msgstr ""

#: www/templates/www/dziekujemy_za_zgloszenie.html:186
msgid "Osoba do kontaktu"
msgstr ""

#: www/templates/www/dziekujemy_za_zgloszenie.html:190
msgid "E-mail kontaktowy"
msgstr ""

#: www/templates/www/dziekujemy_za_zgloszenie.html:192
msgid "Telefon"
msgstr ""

#: www/templates/www/dziekujemy_za_zgloszenie.html:193
#: www/templates/www/dziekujemy_za_zgloszenie.html:209
msgid "Adres"
msgstr ""

#: www/templates/www/dziekujemy_za_zgloszenie.html:194
#: www/templates/www/dziekujemy_za_zgloszenie.html:210
msgid "Miejscowość, kod pocztowy"
msgstr ""

#: www/templates/www/dziekujemy_za_zgloszenie.html:197
msgid "Numer dowodu osobistego"
msgstr ""

#: www/templates/www/dziekujemy_za_zgloszenie.html:205
msgid "Dane do faktury:"
msgstr ""

#: www/templates/www/dziekujemy_za_zgloszenie.html:208
#: www/templates/www/notifications/_personal_data_form.html:23
msgid "Firma"
msgstr ""

#: www/templates/www/dziekujemy_za_zgloszenie.html:216
msgid ""
"Zgadzam się na otrzymanie mailem faktury w wersji elektronicznej, nie "
"potrzebuję papierowej."
msgstr ""

#: www/templates/www/dziekujemy_za_zgloszenie.html:223
msgid ""
"Zamawiam certyfikat ukończenia szkolenia w wersji elektronicznej oraz "
"papierowej."
msgstr ""

#: www/templates/www/dziekujemy_za_zgloszenie.html:225
#, python-format
msgid ""
"Zamawiam certyfikat ukończenia szkolenia w wersji elektronicznej oraz "
"papierowej (dopłata %(cena_drukowanego_certyfikatu)s PLN netto za osobę)."
msgstr ""

#: www/templates/www/dziekujemy_za_zgloszenie.html:229
msgid ""
"Wybieram certyfikat ukończenia szkolenia w wersji elektronicznej, nie "
"potrzebuję papierowego."
msgstr ""

#: www/templates/www/dziekujemy_za_zgloszenie.html:233
msgid "Szkolenie finansowane ze środków publicznych."
msgstr ""

#: www/templates/www/dziekujemy_za_zgloszenie.html:240
#, python-format
msgid ""
"\n"
"       Zamawiam obiady w cenie %(termin_cena_obiadu)s "
"%(szkolenie_waluta_symbol)s %(zgloszenie_vat_info)s za obiad za osobę.\n"
"     "
msgstr ""

#: www/templates/www/dziekujemy_za_zgloszenie.html:247
msgid "Proszę o obiady wegetariańskie"
msgstr ""

#: www/templates/www/dziekujemy_za_zgloszenie.html:247
msgid "osób"
msgstr ""

#: www/templates/www/dziekujemy_za_zgloszenie.html:247
msgid "dla 1 osoby"
msgstr ""

#: www/templates/www/dziekujemy_za_zgloszenie.html:252
#, python-format
msgid ""
"\n"
"         Zamawiam autoryzację o cenie %(zts_cena_autoryzacji)s "
"%(zts_waluta_symbol)s netto.\n"
"       "
msgstr ""

#: www/templates/www/dziekujemy_za_zgloszenie.html:261
msgid ""
"Byłem wcześniej u was na szkoleniu (dotyczy także innych pracowników firmy)"
msgstr ""

#: www/templates/www/dziekujemy_za_zgloszenie.html:266
#, python-format
msgid "Za %(zdarzenie)s zapłacę: %(sposob)s."
msgstr ""

#: www/templates/www/dziekujemy_za_zgloszenie.html:271
msgid "Dodatkowe informacje:"
msgstr ""

#: www/templates/www/dziekujemy_za_zgloszenie.html:274
msgid ""
"\n"
"            <ul>\n"
"                <li>Nagrywanie, filmowanie szkolenia, w całości lub choćby w "
"części jest zabronione.</li>\n"
"            <li>Ze szkolenia można nieodpłatnie zrezygnować, lub przełożyć "
"za porozumieniem stron na inny termin, najpóźniej na 14 dni przed "
"rozpoczęciem zajęć. Szkolenie odwołane po tym terminie jest pełnopłatne.</"
"li>\n"
"            <li>Odpowiedzialność wykonawcy z tytułu niewykonania lub "
"nienależytego wykonania umowy jest ograniczona do wysokości wartości "
"zamówienia.</li>\n"
"            </ul>\n"
"        "
msgstr ""

#: www/templates/www/dziekujemy_za_zgloszenie.html:289
msgid "Uwagi dodatkowe:"
msgstr ""

#: www/templates/www/dziekujemy_za_zgloszenie.html:298
msgid "(podpis*)"
msgstr ""

#: www/templates/www/dziekujemy_za_zgloszenie.html:306
msgid ""
"Prosimy o podpisanie formularza i przesłanie go emailem na adres "
"<EMAIL>."
msgstr ""

#: www/templates/www/dziekujemy_za_zgloszenie.html:312
msgid ""
"Prosimy o podpisanie formularza i przesłanie go faksem na numer 22 266 06 95 "
"lub wskanowanie i przesłanie emailem <NAME_EMAIL>."
msgstr ""

#: www/templates/www/dziekujemy_za_zgloszenie.html:316
msgid ""
"Prosimy pamiętać, że samo wypełnienie formularza (zarówno internetowego, jak "
"i tradycyjnego) dokonuje jedynie rezerwacji wstępnej. Ostateczne "
"potwierdzenie zgłoszenia następuje po przelaniu na nasze konto zaliczki lub "
"pełnej opłaty za kurs w terminie 7 dni od dokonania zgłoszenia, bądź po "
"podpisaniu umowy szkoleniowej (w wypadku niestandardowych zgłoszeń). Umowa "
"zawarta zostaje po potwierdzeniu przyjęcia zgłoszenia przez ALX. Warunkiem "
"prawidłowego wykonania umowy przez ALX jest zapłata przez Klienta pełnej "
"opłaty za kurs lub zapłaty częściowej (zgodnie z ustalonym harmonogramem "
"płatności) lub indywidualnie ustalonej zapłaty."
msgstr ""

#: www/templates/www/dziekujemy_za_zgloszenie.html:322
msgid "Opłaty prosimy kierować na konto:"
msgstr ""

#: www/templates/www/dziekujemy_za_zgloszenie.html:324
msgid ""
"\n"
"        ALX Academy sp. z o.o., ul. Jasna 14/16A, 00-041 Warszawa<br/>\n"
"        BNP Paribas Bank Polska S.A., 93 ******** ******** ********\n"
"      "
msgstr ""

#: www/templates/www/dziekujemy_za_zgloszenie.html:333
#, python-format
msgid ""
"W tytule przelewu prosimy podawać: nazwę firmy (lub w przypadku zgłoszenia "
"indywidualnego imię i nazwisko osoby zgłaszającej), datę rozpoczęcia kursu, "
"kod kursu<br>(<strong>%(nazwa)s, %(termin)s, %(kod)s</strong>). Wykonanie "
"płatności z innym tytułem może spowodować opóźnienie zaksięgowania wpłaty."
msgstr ""

#: www/templates/www/dziekujemy_za_zgloszenie.html:337
#, python-format
msgid ""
"W tytule przelewu prosimy podawać: imię i nazwisko uczestnika (lub nazwę "
"firmy w przypadku grupy), datę rozpoczęcia kursu, kod "
"kursu<br>(<strong>%(nazwa)s, %(termin)s, %(kod)s</strong>). Wykonanie "
"płatności z innym tytułem może spowodować opóźnienie zaksięgowania wpłaty."
msgstr ""

#: www/templates/www/dziekujemy_za_zgloszenie.html:343
msgid ""
"Zastrzegamy sobie prawo odwołania szkolenia lub jego przełożenia (zwrot "
"wpłaconych pieniędzy lub akceptacja nowego terminu), w przypadku zaistnienia "
"zdarzenia losowego uniemożliwiającego realizację szkolenia."
msgstr ""

#: www/templates/www/dziekujemy_za_zgloszenie.html:345
msgid ""
"Zastrzegamy sobie prawo odwołania kursu lub jego przełożenia (zwrot "
"wpłaconych pieniędzy lub akceptacja nowego terminu), jeśli nie zgłosi się na "
"niego wystarczająca liczba uczestników."
msgstr ""

#: www/templates/www/dziekujemy_za_zgloszenie.html:350
msgid ""
"Informujemy, że nie ma możliwości dokupienia obiadów w trakcie trwania "
"szkolenia poprzez korektę naszej faktury. Podczas szkolenia, można dokonać "
"zakupu, zapłaty i uzyskać fakturę jedynie bezpośrednio w restauracji."
msgstr ""

#: www/templates/www/dziekujemy_za_zgloszenie.html:354
msgid "Na wszelkie pytania chętnie odpowiemy mailem lub telefonicznie."
msgstr ""

#: www/templates/www/dziekujemy_za_zgloszenie.html:361
msgid ""
"\n"
"    ALX Academy sp. z o.o., ul. Jasna 14/16A, 00-041 Warszawa<br/>\n"
"    tel. 22 63 64 164, faks 22 266 06 95<br/>\n"
"    <i><EMAIL> http://www.alx.pl/</i>\n"
"  "
msgstr ""

#: www/templates/www/frontpage.html:7
msgid "ALX – szkoła programowania, szkolenia IT i kursy informatyczne"
msgstr ""

#: www/templates/www/frontpage.html:34
msgid "Bootcampy programistyczne i szkolenia IT"
msgstr ""

#: www/templates/www/frontpage.html:35
#, python-format
msgid ""
"Jesteśmy na rynku od %(years)s lat. Mamy ogromne doświadczenie i najlepszych "
"trenerów."
msgstr ""

#: www/templates/www/includes/bialy_box_w_naglowku_z_wariantami.html:27
#: www/templates/www/includes/bialy_box_w_naglowku_z_wariantami.html:60
#: www/templates/www/includes/cena_kursu.html:22
#: www/templates/www/includes/include_xx_wariant_szkolenia.html:50
#: www/templates/www/includes/include_xx_wariant_szkolenia.html:80
#: www/templates/www/kurs_actions_from_flat_page.html:104
#: www/templates/www/szkolenie_actions.html:279
#: www/templates/www/szkolenie_actions.html:308
msgid "możliwość rozłożenia na <a href=\"/pl/raty/\">3 raty</a>"
msgstr ""

#: www/templates/www/includes/bialy_box_w_naglowku_z_wariantami.html:66
#: www/templates/www/includes/include_xx_wariant_szkolenia.html:54
#: www/templates/www/includes/include_xx_wariant_szkolenia.html:84
#: www/templates/www/szkolenie_actions.html:282
#: www/templates/www/szkolenie_actions.html:311
msgid "godzin"
msgstr ""

#: www/templates/www/includes/bialy_box_w_naglowku_z_wariantami.html:73
#: www/templates/www/includes/include_xx_wariant_szkolenia.html:121
#: www/templates/www/includes/kurs_installements_with_yellow_icons.html:6
#: www/templates/www/kurs_actions_from_flat_page.html:141
#: www/templates/www/szkolenie_actions.html:344
msgid "first minute (30+ dni do startu)"
msgstr ""

#: www/templates/www/includes/bialy_box_w_naglowku_z_wariantami.html:78
#: www/templates/www/includes/include_xx_wariant_szkolenia.html:137
#: www/templates/www/szkolenie_actions.html:366
msgid "akredytacja kuratorium oświaty"
msgstr ""

#: www/templates/www/includes/cena_kursu.html:5
#: www/templates/www/kurs_actions_from_flat_page.html:68
#: www/templates/www/szkolenie_actions.html:381
msgid "Cena standardowa"
msgstr ""

#: www/templates/www/includes/cena_kursu.html:9
#: www/templates/www/kurs_actions_from_flat_page.html:72
#: www/templates/www/szkolenie_actions.html:385
msgid "Promocja"
msgstr ""

#: www/templates/www/includes/cena_kursu.html:13
#: www/templates/www/kurs_actions_from_flat_page.html:76
msgid "Cena kursu"
msgstr ""

#: www/templates/www/includes/header_bez_wariantow.html:13
#: www/templates/www/includes/header_z_wariantami.html:11
msgid "Kurs"
msgstr ""

#: www/templates/www/includes/header_bez_wariantow.html:30
#: www/templates/www/includes/header_z_wariantami.html:30
msgid "Obecnie brak terminów otwartych. Dostępne na zamówienie dla grup."
msgstr ""

#: www/templates/www/includes/header_bez_wariantow.html:77
#: www/templates/www/includes/header_z_wariantami.html:76
msgid "Zaproponuj własny termin kursu"
msgstr ""

#: www/templates/www/includes/include_xx_wariant_szkolenia.html:16
#: www/templates/www/szkolenie_actions.html:241
msgid "trener"
msgstr ""

#: www/templates/www/includes/include_xx_wariant_szkolenia.html:98
#: www/templates/www/includes/kurs_installements_with_yellow_icons.html:28
#: www/templates/www/kurs_actions_from_flat_page.html:128
#: www/templates/www/szkolenie_actions.html:325
#: www/templates/www/szkolenie_actions.html:419
msgid "stanowisko komputerowe w cenie"
msgstr ""

#: www/templates/www/includes/include_xx_wariant_szkolenia.html:110
#: www/templates/www/includes/kurs_installements_with_yellow_icons.html:24
#: www/templates/www/kurs_actions_from_flat_page.html:119
#: www/templates/www/szkolenie_actions.html:334
#: www/templates/www/szkolenie_actions.html:412
msgid "poczęstunek w cenie"
msgstr ""

#: www/templates/www/includes/kurs_installements_with_yellow_icons.html:11
msgid "dostęp do nagrań w razie potrzeby"
msgstr ""

#: www/templates/www/includes/kurs_installements_with_yellow_icons.html:15
msgid "dla chętnych bezpłatnie warsztaty "
msgstr ""

#: www/templates/www/includes/kurs_installements_with_yellow_icons.html:19
msgid "praktyczne ćwiczenia i miniprojekty"
msgstr ""

#: www/templates/www/includes/kursy_zawodowe_tabelka_bez_terminow.html:8
#: www/templates/www/kursy_zawodowe_tabelka.html:8
msgid "Nazwa kursu"
msgstr ""

#: www/templates/www/includes/kursy_zawodowe_tabelka_bez_terminow.html:9
#: www/templates/www/includes/lista_szkolen_bez_terminow.html:9
#: www/templates/www/kursy_zawodowe_tabelka.html:10
#: www/templates/www/last_minute.html:25
#: www/templates/www/lista_szkolen.html:11
#: www/templates/www/lista_szkolen_header.html:11
#: www/templates/www/najblizsze.html:112
msgid "Czas"
msgstr ""

#: www/templates/www/includes/kursy_zawodowe_tabelka_bez_terminow.html:10
#: www/templates/www/includes/lista_szkolen_bez_terminow.html:12
#: www/templates/www/kursy_zawodowe_tabelka.html:11
#: www/templates/www/last_minute.html:26
#: www/templates/www/lista_szkolen.html:14
#: www/templates/www/lista_szkolen_header.html:12
#: www/templates/www/najblizsze.html:113
msgid "Cena"
msgstr ""

#: www/templates/www/includes/lista_szkolen_bez_terminow.html:8
#: www/templates/www/lista_szkolen.html:9
#: www/templates/www/lista_szkolen_header.html:9
msgctxt "header"
msgid "Nazwa szkolenia"
msgstr ""

#: www/templates/www/includes/lista_szkolen_bez_terminow.html:8
#: www/templates/www/last_minute.html:22 www/templates/www/lista_szkolen.html:9
#: www/templates/www/lista_szkolen_header.html:9
#: www/templates/www/najblizsze.html:109
msgid "Kod"
msgstr ""

#: www/templates/www/includes/lista_szkolen_bez_terminow.html:11
#: www/templates/www/lista_szkolen.html:13
#: www/templates/www/lista_szkolen_header.html:12
msgid ""
"Ceny netto. Dla podmiotów publicznych istnieje możliwość zwolnienia z VAT. "
"Dalsze informacje - patrz legenda poniżej tabeli."
msgstr ""

#: www/templates/www/includes/lista_szkolen_bez_terminow.html:22
#: www/templates/www/lista_szkolen.html:24
msgctxt "slug"
msgid "zawod"
msgstr ""

#: www/templates/www/includes/lista_szkolen_bez_terminow.html:30
#: www/templates/www/kursy_zawodowe_tabelka.html:18
#: www/templates/www/lista_szkolen.html:32
msgctxt "slug"
msgid "kurs"
msgstr ""

#: www/templates/www/includes/lista_szkolen_bez_terminow.html:46
#, python-format
msgid ""
"\n"
"                                  %(s_cena_przed_promocja)s "
"<small>%(s_waluta_symbol)s</small>\n"
"                                "
msgstr ""

#: www/templates/www/index.html:5
msgid "ALX - pełna lista szkoleń"
msgstr ""

#: www/templates/www/index.html:8
msgid "Pełna lista szkoleń"
msgstr ""

#: www/templates/www/index.html:13
msgid "Kursy zawodowe"
msgstr ""

#: www/templates/www/index.html:18
#: www/templates/www/kursy_zawodowe_naglowek.html:2
msgid ""
"Kompleksowe, długie, prowadzone w trybach: dziennym (2-3 kilkudniowe sesje w "
"dni powszednie), wieczorowym (2 razy w tygodniu od godz. 18) lub zaocznym "
"(soboty i niedziele, co drugi weekend)."
msgstr ""

#: www/templates/www/index.html:24
#: www/templates/www/index_by_technologia.html:49
#: www/templates/www/index_by_technologia.html:91
msgid "Szkolenia"
msgstr ""

#: www/templates/www/index.html:25
#: www/templates/www/index_by_technologia.html:91
msgid ""
"Klasyczne, typowo kilkudniowe. Odbywają się zazwyczaj w dni powszednie, w "
"godzinach pracy."
msgstr ""

#: www/templates/www/index_by_technologia.html:44
#: www/templates/www/index_by_technologia/top_D.html:10
#: www/templates/www/index_by_technologia/top_D.html:22
msgctxt "naglowek index_by_technologia"
msgid "Szkolenia"
msgstr ""

#: www/templates/www/index_by_technologia.html:91
#: www/templates/www/kursy_zawodowe_naglowek.html:2
msgid "stacjonarne i zdalne"
msgstr ""

#: www/templates/www/index_by_technologia.html:137
msgid "<strong>Zobacz</strong> też"
msgstr ""

#: www/templates/www/index_by_technologia.html:164
#: www/templates/www/index_by_technologia/top_A.html:16
#: www/templates/www/index_by_technologia/top_A.html:90
#: www/templates/www/index_by_technologia/top_B1.html:14
#: www/templates/www/index_by_technologia/top_B2.html:15
#: www/templates/www/index_by_technologia/top_B2.html:59
#: www/templates/www/index_by_technologia/top_C.html:15
#: www/templates/www/index_by_technologia/top_C.html:62
msgid "Czytaj więcej"
msgstr ""

#: www/templates/www/index_by_technologia/top_A.html:44
#: www/templates/www/index_by_technologia/top_A.html:122
#: www/templates/www/index_by_technologia/top_B1.html:48
msgid "Trenerzy"
msgstr ""

#: www/templates/www/index_by_technologia/top_A.html:69
#: www/templates/www/index_by_technologia/top_A.html:147
#: www/templates/www/index_by_technologia/top_B1.html:28
#: www/templates/www/index_by_technologia/top_B2.html:33
#: www/templates/www/index_by_technologia/top_B2.html:78
#: www/templates/www/index_by_technologia/top_C.html:42
#: www/templates/www/index_by_technologia/top_C.html:91
#, python-format
msgid "wyszkoliliśmy już <strong>%(count)s osób</strong>"
msgstr ""

#: www/templates/www/kurs_actions.html:13
#: www/templates/www/kurs_actions_from_flat_page.html:153
#: www/templates/www/szkolenie_actions.html:433
msgid "Akredytacja"
msgstr ""

#: www/templates/www/kursy_zawodowe_naglowek.html:2
msgid "Kursy zawodowe (długie)"
msgstr ""

#: www/templates/www/kursy_zawodowe_tabelka.html:9
#: www/templates/www/lista_szkolen.html:10
#: www/templates/www/lista_szkolen_header.html:10
msgid "Terminy"
msgstr ""

#: www/templates/www/last_minute.html:6
msgid "Last Minute"
msgstr ""

#: www/templates/www/last_minute.html:7
msgid "Szkolenia - Last Minute"
msgstr ""

#: www/templates/www/last_minute.html:23 www/templates/www/najblizsze.html:110
msgid "Nazwa szkolenia"
msgstr ""

#: www/templates/www/last_minute.html:47 www/templates/www/najblizsze.html:134
#, python-format
msgid ""
"\n"
"\t\t\t      %(szkolenie_cena_przed_promocja)s %(szkolenie_waluta_symbol)s\n"
"\t\t\t    "
msgstr ""

#: www/templates/www/last_minute.html:55 www/templates/www/najblizsze.html:142
#, python-format
msgid ""
"\n"
"\t\t\t    %(szkolenie_cena)s %(szkolenie_waluta_symbol)s\n"
"\t\t\t  "
msgstr ""

#: www/templates/www/last_minute.html:67 www/templates/www/najblizsze.html:154
msgid "Brak terminów."
msgstr ""

#: www/templates/www/lista_szkolen.html:74
msgid "szkolenie jest także organizowane na zamówienie (w trybie zamkniętym)"
msgstr ""

#: www/templates/www/lista_szkolen.html:74
#, python-format
msgid ""
"szkolenie jest także organizowane na zamówienie (w trybie zamkniętym) dla "
"grup od %(min_os)s osób"
msgstr ""

#: www/templates/www/lista_szkolen.html:76
#: www/templates/www/lista_szkolen.html:90
msgid "Na zamówienie"
msgstr ""

#: www/templates/www/lista_szkolen.html:78
#: www/templates/www/lista_szkolen.html:92
#, python-format
msgid "Na zamówienie od %(min_os)s os."
msgstr ""

#: www/templates/www/lista_szkolen.html:88
msgid "szkolenie jest organizowane na zamówienie (w trybie zamkniętym)"
msgstr ""

#: www/templates/www/lista_szkolen.html:88
#, python-format
msgid ""
"szkolenie jest organizowane na zamówienie (w trybie zamkniętym) dla grup od "
"%(min_os)s osób"
msgstr ""

#: www/templates/www/lista_szkolen.html:98
msgctxt "contat us"
msgid "tel."
msgstr ""

#: www/templates/www/lista_szkolen.html:109
#, python-format
msgid ""
"\n"
"                                      %(s_cena_przed_promocja)s "
"<small>%(s_waluta_symbol)s</small>\n"
"                                    "
msgstr ""

#: www/templates/www/my_flat_page.html:125
msgid "Program szkolenia"
msgstr ""

#: www/templates/www/na_zyczenie_info.html:10
msgid ""
"\n"
"W cenie:<br>\n"
"- stanowisko komputerowe i poczęstunek (dla zajęć&nbsp;stacjonarnych)<br>\n"
"- dla chętnych dodatkowe, bezpłatne warsztaty HR<br>\n"
"- dostęp do nagrań z zajęć w razie potrzeby\n"
msgstr ""

#: www/templates/www/na_zyczenie_info.html:24
msgid "Dostępne również na zamówienie, w terminie i lokalizacji do uzgodnienia"
msgstr ""

#: www/templates/www/na_zyczenie_info.html:24
#: www/templates/www/na_zyczenie_info.html:28
#: www/templates/www/na_zyczenie_info.html:34
#: www/templates/www/na_zyczenie_info.html:38
#, python-format
msgid " dla grup od %(szkolenie_min_grupa)s osób"
msgstr ""

#: www/templates/www/na_zyczenie_info.html:28
msgid ""
"Kursy dostępne również na zamówienie, w terminie i lokalizacji do uzgodnienia"
msgstr ""

#: www/templates/www/na_zyczenie_info.html:34
msgid "Dostępne na zamówienie, w terminie i lokalizacji do uzgodnienia"
msgstr ""

#: www/templates/www/na_zyczenie_info.html:38
msgid "Kursy dostępne na zamówienie, w terminie i lokalizacji do uzgodnienia"
msgstr ""

#: www/templates/www/najblizsze.html:6
msgid "Najbliższe terminy szkoleń"
msgstr ""

#: www/templates/www/najblizsze.html:7
msgid "Szkolenia - najbliższe terminy"
msgstr ""

#: www/templates/www/najblizsze.html:19
msgid "Miasto:"
msgstr ""

#: www/templates/www/najblizsze.html:21 www/templates/www/najblizsze.html:31
#: www/templates/www/najblizsze.html:41 www/templates/www/najblizsze.html:50
#: www/templates/www/najblizsze.html:59 www/templates/www/najblizsze.html:70
#: www/templates/www/najblizsze.html:81
msgid "wszystkie"
msgstr ""

#: www/templates/www/najblizsze.html:29
msgid "Data:"
msgstr ""

#: www/templates/www/najblizsze.html:39
msgid "Technologia:"
msgstr ""

#: www/templates/www/najblizsze.html:48
msgid "Zawód:"
msgstr ""

#: www/templates/www/najblizsze.html:57
msgid "Typ&nbsp;zajęć:"
msgstr ""

#: www/templates/www/najblizsze.html:68
msgid "Tryb:"
msgstr ""

#: www/templates/www/najblizsze.html:79
msgid "Język szkolenia"
msgstr ""

#: www/templates/www/najblizsze.html:83
msgid "angielski"
msgstr ""

#: www/templates/www/najblizsze.html:83
msgid "polski"
msgstr ""

#: www/templates/www/najblizsze.html:90
msgid "wyczyść wszystkie filtry"
msgstr ""

#: www/templates/www/notifications/_personal_data_form.html:4
msgid ""
"Jeżeli chcesz, abyśmy w razie nagłej zmiany w harmonogramie szkoleń "
"skontaktowali się z Tobą telefonicznie, pozostaw nam swój numer telefonu (i "
"opcjonalnie pozostałe dane)."
msgstr ""

#: www/templates/www/notifications/_personal_data_form.html:9
msgid "Numer telefonu"
msgstr ""

#: www/templates/www/notifications/activate_subscription.html:14
msgid "Twoja subskrypcja została aktywowana."
msgstr ""

#: www/templates/www/notifications/activate_subscription.html:15
msgid "Przejdź do ustawień"
msgstr ""

#: www/templates/www/notifications/activate_subscription.html:28
#: www/templates/www/notifications/cancel_all_subscriptions.html:56
#: www/templates/www/notifications/manage_subscriptions.html:56
msgid ""
"Administratorem danych osobowych jest ALX Academy sp. z o.o. z siedzibą w "
"Warszawie (00-041) przy ul. Jasnej 14/16. (\"Administrator\"). Dane osobowe "
"przetwarzane będą w zakresie niezbędnym do wykonania umowy, której stroną "
"jest osoba, której dane dotyczą, lub do podjęcia działań na żądanie osoby, "
"której dane dotyczą, przed zawarciem umowy (art. 6 ust. 1 b Ogólnego "
"rozporządzenia Rady (UE) i PE o ochronie danych osobowych). Dane będą "
"przetwarzane przez okres niezbędny do realizacji określonego celu "
"przetwarzania. Osobie, której dane dotyczą przysługuje prawo dostępu do "
"treści swoich danych osobowych, ich sprostowania, usunięcia lub ograniczenia "
"przetwarzania, prawo do wniesienia sprzeciwu wobec przetwarzania, a także "
"prawo do żądania przenoszenia danych. Podanie danych osobowych jest "
"dobrowolne, jednak niezbędne do podjęcia działań według dyspozycji osoby, "
"której dotyczą."
msgstr ""

#: www/templates/www/notifications/ajax_subscription_done.html:11
#: www/templates/www/notifications/ajax_subscription_form.html:10
msgid "Powiadomienie o nowych terminach"
msgstr ""

#: www/templates/www/notifications/ajax_subscription_done.html:14
msgid "Dziękujemy."
msgstr ""

#: www/templates/www/notifications/ajax_subscription_done.html:17
msgid "UWAGA!"
msgstr ""

#: www/templates/www/notifications/ajax_subscription_done.html:18
#, python-format
msgid ""
"Na podany adres (<span id=\"notifications-activation-"
"email\">%(activation_email)s</span>) wysłaliśmy wiadomość z odnośnikiem "
"prowadzącym do potwierdzenia zapisu. Aby otrzymywać wiadomości o nowych i "
"aktualizowanych terminach konieczne jest otwarcie wiadomości i kliknięcie w "
"przesłany odnośnik."
msgstr ""

#: www/templates/www/notifications/ajax_subscription_done.html:20
#: www/views.py:2963 www/views.py:2981 www/views.py:3085
msgid "Zmiany zostały zapisane."
msgstr ""

#: www/templates/www/notifications/ajax_subscription_done.html:24
msgid "Zamknij okno"
msgstr ""

#: www/templates/www/notifications/ajax_subscription_form.html:14
msgid ""
"Wyślemy Ci wiadomość e-mail, kiedy zaplanujemy nowy termin tego szkolenia, "
"lub jeden z już podanych na stronie terminów zostanie oficjalnie "
"potwierdzony. Podaj nam swój adres e-mail."
msgstr ""

#: www/templates/www/notifications/ajax_subscription_form.html:34
msgid "Adres E-Mail"
msgstr ""

#: www/templates/www/notifications/ajax_subscription_form.html:42
msgctxt "newsletter"
msgid ""
"Wyrażam zgodę na przetwarzanie danych osobowych w postaci adresu e-mail w "
"celu otrzymania informacji o terminach szkoleń organizowanych przez ALX "
"Academy sp. z o.o. z siedzibą w Warszawie."
msgstr ""

#: www/templates/www/notifications/ajax_subscription_form.html:47
msgid ""
"Administratorem adresu e-mail jest ALX Academy sp. z o.o. z siedzibą w "
"Warszawie (00-041) przy ul. Jasnej 14/16. Wyrażenie zgody jest dobrowolne. "
"Osobie, której dane dotyczą przysługuje prawo dostępu do treści swoich "
"danych i możliwości ich poprawiania. Powyższa zgoda może być odwołana w "
"każdym czasie, co skutkować będzie usunięciem podanego adresu e-mail z listy "
"dystrybucyjnej Administratora."
msgstr ""

#: www/templates/www/notifications/ajax_subscription_form.html:52
msgid "Anuluj"
msgstr ""

#: www/templates/www/notifications/ajax_subscription_form.html:53
msgid ""
"Na podany adres E-Mail będziemy przesłyać tylko informacje o wybranym "
"terminie. Każda wiadomość będzie zawierać odnośnik umożliwiający "
"zmianę preferencji oraz łatwą rezygnację z otrzymywania powiadomień."
msgstr ""

#: www/templates/www/notifications/base.html:8
#: www/templates/www/notifications/base.html:9
msgid "Notyfikacje o terminach szkoleń"
msgstr ""

#: www/templates/www/notifications/cancel_all_subscriptions.html:8
#: www/templates/www/notifications/cancel_all_subscriptions.html:9
#: www/templates/www/notifications/do_cancel_all_subscriptions.html:8
#: www/templates/www/notifications/do_cancel_all_subscriptions.html:9
msgid "Ustawienia subskrypcji"
msgstr ""

#: www/templates/www/notifications/cancel_all_subscriptions.html:36
msgid "Podaj swój adres email"
msgstr ""

#: www/templates/www/notifications/cancel_all_subscriptions.html:47
msgid "Wypisz mnie"
msgstr ""

#: www/templates/www/notifications/cancel_continuation.html:9
#: www/templates/www/notifications/cancel_continuation.html:10
msgid "Notyfikacje o kontynuacji szkoleń"
msgstr ""

#: www/templates/www/notifications/cancel_continuation.html:15
#: www/templates/www/notifications/cancel_subscription.html:5
msgid "Zostałeś wypisany."
msgstr ""

#: www/templates/www/notifications/cancel_continuation.html:17
#: www/templates/www/notifications/do_cancel_all_subscriptions.html:21
msgid "Niepoprawny token."
msgstr ""

#: www/templates/www/notifications/do_cancel_all_subscriptions.html:19
msgid "Zostałeś poprawnie wypisany z powiadomień w serwisie."
msgstr ""

#: www/templates/www/notifications/manage_subscriptions.html:23
msgid "Usuń"
msgstr ""

#: www/templates/www/notifications/manage_subscriptions.html:34
msgid "Zapisz zmiany"
msgstr ""

#: www/templates/www/notifications/manage_subscriptions.html:44
#, python-format
msgid ""
"W tej chwili nie otrzymujesz żadnych powiadomień o nowych terminach. Aby "
"zapisać się na powiadomienia wejdź na stronę <a "
"href='%(pelna_lista_szkolen_url)s'>kursu lub szkolenia</a> i wybierz miasta."
msgstr ""

#: www/templates/www/notifications/manage_subscriptions.html:52
#, python-format
msgid ""
"Kliknij <a href='%(cancel_subscription_url)s'>tutaj</a>, aby całkowicie "
"zrezygnować z otrzymywania powiadomień."
msgstr ""

#: www/templates/www/notyfikacje_i_terminy_inline.html:18
msgid "Zapisz się na ten kurs"
msgstr ""

#: www/templates/www/notyfikacje_i_terminy_inline.html:21
msgid "Wybierz interesujący Cię termin oraz miasto"
msgstr ""

#: www/templates/www/notyfikacje_i_terminy_inline.html:50
#: www/templates/www/szkolenie_actions.html:178
msgid "Żaden termin nie pasuje?"
msgstr ""

#: www/templates/www/notyfikacje_i_terminy_inline.html:52
msgid "Obecnie brak terminów"
msgstr ""

#: www/templates/www/notyfikacje_i_terminy_inline.html:55
msgid "Podaj swój adres e-mail, a my powiadomimy Cię o przyszłych terminach"
msgstr ""

#: www/templates/www/notyfikacje_i_terminy_inline.html:58
msgid "Wpisz swój e-mail"
msgstr ""

#: www/templates/www/ostrzezenie_cookie.html:5
msgid ""
"<p>Korzystając z tej strony wyrażasz zgodę na zamieszczanie plików cookies "
"na Twoim urządzeniu. Możesz określić warunki przechowywania i dostępu do "
"plików cookies w ustawieniach przeglądarki.\n"
"      Więcej informacji w <a href=\"/pl/polityka-prywatnosci/\" "
"target=\"_blank\">polityce prywatności</a>. <button id=\"cookies-"
"accept\">Zgadzam się</button></p>"
msgstr ""

#: www/templates/www/partial/contact_form.html:13
#: www/templates/www/zadaj_szybkie_pytanie.html:55
#: www/templates/www/zadaj_szybkie_pytanie.html:61
msgid "Twoja wiadomość została przesłana"
msgstr ""

#: www/templates/www/partial/contact_form.html:22
msgid "Podaj adres email lub numer telefonu"
msgstr ""

#: www/templates/www/partial/contact_form.html:26
#: www/templates/www/zadaj_szybkie_pytanie.html:40
msgid ""
"Wyrażam zgodę na przetwarzanie danych osobowych przez ALX Academy sp. z o.o. "
"z siedzibą w Warszawie w celu realizacji zgłoszenia."
msgstr ""

#: www/templates/www/partial/contact_form.html:26
#: www/templates/www/zadaj_szybkie_pytanie.html:40
msgid ""
"Administratorem danych osobowych jest ALX Academy sp. z o.o. z siedzibą w "
"Warszawie (00-041) przy ul. Jasnej 14/16. Wyrażenie zgody jest dobrowolne. "
"Dane będą przetwarzane przez okres niezbędny do realizacji określonego celu "
"przetwarzania. Osobie, której dane dotyczą przysługuje prawo dostępu do "
"treści swoich danych osobowych, ich sprostowania, usunięcia lub ograniczenia "
"przetwarzania, prawo do wniesienia sprzeciwu wobec przetwarzania, a także "
"prawo do żądania przenoszenia danych. Podanie danych osobowych jest "
"dobrowolne, jednak niezbędne do realizacji zgłoszenia."
msgstr ""

#: www/templates/www/partial/contact_form.html:35
#: www/templates/www/zadaj_szybkie_pytanie.html:48
#: www/templates/www/zadaj_szybkie_pytanie.html:91
msgctxt "newsletter"
msgid "Wyślij"
msgstr ""

#: www/templates/www/partial/staticpage_lista_trenerow_mobile.html:4
msgid "Kurs stacjonarny z trenerem"
msgstr ""

#: www/templates/www/partial/szkolenie_kontynuacja.html:4
msgid "Inne opcje nauki"
msgstr ""

#: www/templates/www/partial/szkolenie_kontynuacja.html:7
msgid "Kurs (poziom 2)"
msgstr ""

#: www/templates/www/partial/szkolenie_kontynuacja.html:11
msgid "Krótkie szkolenia"
msgstr ""

#: www/templates/www/szkolenie_actions.html:27
msgid "Dostępne na zamówienie dla grup."
msgstr ""

#: www/templates/www/szkolenie_actions.html:171
msgid "Żaden termin nie pasuje? "
msgstr ""

#: www/templates/www/szkolenie_actions.html:171
#: www/templates/www/szkolenie_actions.html:180
msgid "Zaproponuj własny termin szkolenia"
msgstr ""

#: www/templates/www/szkolenie_actions.old.html:32
msgid "Zaproponuj własny termin"
msgstr ""

#: www/templates/www/zadaj_szybkie_pytanie.html:9
msgid "Masz pytanie?"
msgstr ""

#: www/templates/www/zadaj_szybkie_pytanie.html:19
msgid "Tu możesz zadać niezobowiązujące i szybkie pytanie na temat szkolenia"
msgstr ""

#: www/templates/www/zadaj_szybkie_pytanie.html:28
msgid "Twój e-mail / numer telefonu"
msgstr ""

#: www/templates/www/zadaj_szybkie_pytanie.html:36
msgid "Treść wiadomości"
msgstr ""

#: www/templates/www/zadaj_szybkie_pytanie.html:63
msgid "Czy chcesz otrzymywać nasz firmowy Newsletter?"
msgstr ""

#: www/templates/www/zadaj_szybkie_pytanie.html:64
msgid ""
"Raz w miesiącu przesyłamy informacje o technologiach związanych z naszymi "
"szkoleniami i zmianach w naszej ofercie."
msgstr ""

#: www/templates/www/zadaj_szybkie_pytanie.html:78
msgid "Tak, chcę newsletter!"
msgstr ""

#: www/templates/www/zamkniete_zgloszenie.html:6
#: www/templates/www/zamkniete_zgloszenie.html:9
#: www/templates/www/zgloszenie.html:25 www/templates/www/zgloszenie.html:29
msgid "Formularz zgłoszeniowy"
msgstr ""

#: www/templates/www/zamkniete_zgloszenie.html:18
#: www/templates/www/zgloszenie.html:57 www/templates/www/zgloszenie.html:62
msgid "UWAGA:"
msgstr ""

#: www/templates/www/zamkniete_zgloszenie.html:19
#: www/templates/www/zgloszenie.html:58
#, python-format
msgid ""
"Wybór faktury papierowej oznacza dopłatę do ceny szkolenia w wysokości "
"%(cena_papierowej_faktury)s zł netto."
msgstr ""

#: www/templates/www/zamkniete_zgloszenie.html:27
msgid "Termin szkolenia"
msgstr ""

#: www/templates/www/zamkniete_zgloszenie.html:29
#: www/templates/www/zgloszenie.html:188
msgid "Popraw proszę poniższe błędy."
msgstr ""

#: www/templates/www/zamkniete_zgloszenie.html:55
#: www/templates/www/zgloszenie.html:238
msgid ""
"Administratorem podanych w formularzu danych osobowych jest ALX Academy sp. "
"z o.o. z siedzibą w Warszawie (00-041) przy ul. Jasnej 14/16. "
"(„Administrator“). Dane osobowe przetwarzane będą w zakresie niezbędnym do "
"wykonania umowy, której stroną jest osoba, której dane dotyczą, lub do "
"podjęcia działań na żądanie osoby, której dane dotyczą, przed zawarciem "
"umowy (zgodnie z Rozporządzeniem Parlamentu Europejskiego i Rady 2016/679). "
"W przypadku wyrażenia zgody na otrzymywanie informacji handlowych poprzez "
"subskrypcję „Newslettera ALX”, podany adres e-mail będzie przetwarzany "
"również w tym celu. Dane będą przetwarzane przez okres niezbędny do "
"realizacji określonego celu przetwarzania. Osobie, której dane dotyczą "
"przysługuje prawo dostępu do treści swoich danych osobowych, ich "
"sprostowania, usunięcia lub ograniczenia przetwarzania, prawo do wniesienia "
"sprzeciwu wobec przetwarzania, a także prawo do żądania przenoszenia danych. "
"Podanie danych osobowych jest dobrowolne, jednak niezbędne do podjęcia "
"działań według dyspozycji osoby, której dotyczą."
msgstr ""

#: www/templates/www/zamkniete_zgloszenie.html:61
#: www/templates/www/zgloszenie.html:243
msgid "wyślij zgłoszenie"
msgstr ""

#: www/templates/www/zamkniete_zgloszenie.html:67
#: www/templates/www/zgloszenie.html:247
msgid "Zgłoszenie jest wysyłane, proszę czekać..."
msgstr ""

#: www/templates/www/zamkniete_zgloszenie.html:70
#: www/templates/www/zgloszenie.html:250
msgid "Brak odpowiedzi przy wysyłaniu zgłoszenia. Możesz spróbować ponownie."
msgstr ""

#: www/templates/www/zamkniete_zgloszenie.html:75
#: www/templates/www/zgloszenie.html:255
msgid "pole wymagane"
msgstr ""

#: www/templates/www/zamkniete_zgloszenie.html:75
#: www/templates/www/zgloszenie.html:256
msgid "pole jest wymagane"
msgstr ""

#: www/templates/www/zamkniete_zgloszenie.html:78
msgid ""
"\n"
"        Po kliknięciu \"wyślij\", na kolejnej stronie, będzie dostępny "
"gotowy dokument z zamówieniem do wydruku, podpisania i przesłania do nas.\n"
"    "
msgstr ""

#: www/templates/www/zaproponuj_termin.html:7
#: www/templates/www/zaproponuj_termin.html:11
msgid "Zaproponuj termin szkolenia"
msgstr ""

#: www/templates/www/zaproponuj_termin.html:85
msgid ""
"Administratorem podanych w formularzu danych osobowych jest ALX Academy sp. "
"z o.o. z siedzibą w Warszawie (00-041) przy ul. Jasnej 14/16. "
"(„Administrator“). Dane osobowe przetwarzane będą w zakresie niezbędnym do "
"wykonania umowy, której stroną jest osoba, której dane dotyczą, lub do "
"podjęcia działań na żądanie osoby, której dane dotyczą, przed zawarciem "
"umowy (art. 6 ust. 1 b Ogólnego rozporządzenia Rady (UE) i PE o ochronie "
"danych osobowych). Dane będą przetwarzane przez okres niezbędny do "
"realizacji określonego celu przetwarzania. Osobie, której dane dotyczą "
"przysługuje prawo dostępu do treści swoich danych osobowych, ich "
"sprostowania, usunięcia lub ograniczenia przetwarzania, prawo do wniesienia "
"sprzeciwu wobec przetwarzania, a także prawo do żądania przenoszenia danych. "
"Podanie danych osobowych jest dobrowolne, jednak niezbędne do podjęcia "
"działań przed zawarciem umowy."
msgstr ""

#: www/templates/www/zaproponuj_termin.html:92
msgid "pola wymagane"
msgstr ""

#: www/templates/www/zgloszenie.html:53
msgid ""
"Jeśli posiadasz kod rabatowy <a href=\"#\" "
"id=\"discount_coupon_help_text_link\">kliknij tutaj</a>."
msgstr ""

#: www/templates/www/zgloszenie.html:66
#, python-format
msgid ""
"Wybór drukowanego certyfikatu oznacza dopłatę do ceny szkolenia w wysokości "
"%(cena_drukowanego_certyfikatu)s zł netto (cena zawiera przesyłkę pocztą we "
"wzmocnionej kopercie za potwierdzeniem, na podany adres do korespondencji)."
msgstr ""

#: www/templates/www/zgloszenie.html:77
msgid "Obiady są wliczone w cenę."
msgstr ""

#: www/templates/www/zgloszenie.html:81 www/templates/www/zgloszenie.html:87
msgid "Brak obiadów i brak możliwości ich dokupienia."
msgstr ""

#: www/templates/www/zgloszenie.html:86
msgid "Termin weekendowy."
msgstr ""

#: www/templates/www/zgloszenie.html:93
msgid "Obiady nie są wliczone w cenę."
msgstr ""

#: www/templates/www/zgloszenie.html:96
#, python-format
msgid "Koszt obiadu %(cena_obiadu)s %(waluta)s netto za osobę za obiad."
msgstr ""

#: www/templates/www/zgloszenie.html:98
#, python-format
msgid "Koszt obiadu %(cena_obiadu)s %(waluta)s za osobę za obiad."
msgstr ""

#: www/templates/www/zgloszenie.html:103
msgid ""
"W przypadku wykupienia obiadów, faktura VAT zostanie wystawiona z jedną "
"całościową pozycją \"szkolenie\" - tj. obiady nie będą wyszczególniane "
"osobno."
msgstr ""

#: www/templates/www/zgloszenie.html:106
msgid ""
"Nie ma możliwości dokupienia obiadów w trakcie trwania szkolenia poprzez "
"korektę naszej faktury. Podczas szkolenia, można dokonać zakupu, zapłaty i "
"uzyskać fakturę jedynie bezpośrednio w restauracji."
msgstr ""

#: www/templates/www/zgloszenie.html:262
msgid ""
"\n"
"        Przesłanie formularza zgłoszeniowego przez internet dokonuje "
"wstępnej\n"
"        rezerwacji, ważnej przez 48 godzin. Dla formalnego potwierdzenia\n"
"        zgłoszenia po wypełnieniu powyższego formularza <strong>jest "
"konieczne</strong> jego\n"
"        wydrukowanie (będzie to możliwe na kolejnym ekranie, po naciśnięciu\n"
"        \"wyślij zgłoszenie\"), <strong>podpisanie i przesłanie</strong> "
"nam: faksem (22 266 06 95),\n"
"        lub jako skan e-mailem (<EMAIL>).\n"
"        "
msgstr ""

#: www/templates/www/zgloszenie.html:315
msgid "To szkolenie nie ma w tej chwili ustalonych terminów"
msgstr ""

#: www/templates/www/zgloszenie.html:317
msgid "zaproponuj własny termin"
msgstr ""

#: www/templatetags/myfilters.py:236
msgid "Grupy dzienne"
msgstr ""

#: www/templatetags/myfilters.py:237
msgid "Grupy zaoczne"
msgstr ""

#: www/templatetags/myfilters.py:238
msgid "Grupy wieczorowe"
msgstr ""

#: www/templatetags/myfilters.py:274
msgid "bloki"
msgstr ""

#: www/templatetags/myfilters.py:276
msgid "od"
msgstr ""

#: www/templatetags/myfilters.py:286
msgid "zakończenie"
msgstr ""

#: www/templatetags/myfilters.py:389
msgid "kursantów uczestniczyło w kursie"
msgstr ""

#: www/templatetags/myfilters.py:1237
msgid "(zwolnione z VAT)"
msgstr ""

#: www/templatetags/myfilters.py:1240
#, python-brace-format
msgid "({0} {1} brutto)"
msgstr ""

#: www/templatetags/myfilters.py:1244
#, python-format
msgid "netto (VAT %s%%)"
msgstr ""

#: www/templatetags/myfilters.py:1254
#, python-brace-format
msgctxt "cena_info"
msgid "{0} {1} {2}"
msgstr ""

#: www/templatetags/myfilters.py:1269
#, python-brace-format
msgctxt "cena_info_short_mc"
msgid "{0} <small>{1}/mies.</small>"
msgstr ""

#: www/templatetags/myfilters.py:1271
#, python-brace-format
msgctxt "cena_info_short"
msgid "{0} <small>{1}</small>"
msgstr ""

#: www/templatetags/myfilters.py:1290
#, python-brace-format
msgctxt "cena_info_short_mc"
msgid ""
"<div style='margin-top: 6px; line-height: 0.5;'>{0}{1} <small>{2}/mies.</"
"small></div>"
msgstr ""

#: www/templatetags/myfilters.py:1293
#, python-brace-format
msgctxt "cena_info_short"
msgid ""
"<div style='margin-top: 6px; line-height: 0.5;'>{0}{1} <small>{2}</"
"small><div>"
msgstr ""

#: www/views.py:391
msgid "ul. Jasna 14/16A, 00-041 Warszawa"
msgstr ""

#: www/views.py:394
msgid "tel. 22 63 64 164, fax 22 266 06 95"
msgstr ""

#: www/views.py:399
msgid "http://www.alx.pl"
msgstr ""

#: www/views.py:405
msgid "Zapytaj o szczegóły"
msgstr ""

#: www/views.py:408
msgid "tel. 22 63 64 164"
msgstr ""

#: www/views.py:416
msgid "Najbliższe terminy"
msgstr ""

#: www/views.py:859
#, python-format
msgid "ALX - link rejestracyjny na szkolenie: %(nazwa)s"
msgstr ""

#: www/views.py:999
msgid "Błąd zapisu pliku."
msgstr ""

#: www/views.py:1303 www/views.py:1399
msgid "Za szkolenie zapłacę"
msgstr ""

#: www/views.py:1483
#, python-brace-format
msgid "{short_description}<br/>Koszt {price} {waluta} {netto} za osobę."
msgstr ""

#: www/views.py:1488
msgid "netto"
msgstr ""

#: www/views.py:1710
msgid "weekendowy (zaoczny)"
msgstr ""

#: www/views.py:2773
msgid "Nowa subskrypcja na ALX (www.alx.pl)"
msgstr ""

#: www/views.py:3177
msgid "Zobacz mój certyfikat"
msgstr ""

#: www/views.py:3185
msgid "Wiadomość została wysłana."
msgstr ""

#: www/views.py:3374
msgid "Rezygnacja z powiadomień"
msgstr ""

#: www/views.py:3385
msgid ""
"Rozpoczęlismy sprawdzanie, czy twój adres email znajduje się na naszych "
"listach. Jeśli tak wyślemy do ciebie maila z linkiem potwierdzającym "
"wypisanie z naszych list mailingowych."
msgstr ""

#: wynajmy/templates/admin/edit_inline/stacked.html:67
#: wynajmy/templates/admin/edit_inline/tabular.html:121
#, python-format
msgid "Add another %(verbose_name)s"
msgstr ""

#: wynajmy/templates/admin/edit_inline/stacked.html:70
#: wynajmy/templates/admin/edit_inline/tabular.html:124
msgid "Remove"
msgstr ""

#: wynajmy/templates/admin/edit_inline/tabular.html:17
msgid "Delete?"
msgstr ""

#~ msgid "Enter a valid email address."
#~ msgstr "Wprowadź poprawny adres email."
