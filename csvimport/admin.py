import csv
from io import StringIO

from django import forms
from django.conf.urls import url
from django.contrib import messages
from django.shortcuts import redirect, render


class CsvImportForm(forms.Form):
    csv_file = forms.FileField()


class ImportCSVMixin(object):
    change_list_template = "csvimport/csvimport_changelist.html"

    def process_csv(self, request, reader):
        raise NotImplementedError

    def get_urls(self):
        urls = super().get_urls()
        my_urls = [
            url("import-csv", self.admin_site.admin_view(self.import_csv)),
        ]
        return my_urls + urls

    def import_csv(self, request):
        if request.method == "POST":
            form = CsvImportForm(request.POST, request.FILES)
            if form.is_valid():
                try:
                    csv_file = StringIO(request.FILES["csv_file"].read().decode())
                    reader = csv.reader(csv_file, delimiter=';')
                except:
                    raise
                    messages.error(request, "Nie można przetworzyć pliku.")
                else:
                    self.process_csv(request, reader)
                    messages.success(request, "Dane zostały zaimportowane.")
                return redirect("..")
        else:
            form = CsvImportForm()

        payload = {"form": form}
        return render(
            request, "csvimport/csv_form.html", payload
        )
