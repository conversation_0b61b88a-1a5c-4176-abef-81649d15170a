# -*- mode: ruby -*-
# vi: set ft=ruby :

Vagrant.configure("2") do |config|
  config.vm.box = "ubuntu/xenial64"
  config.vm.box_check_update = false
  config.vm.box_version = "20210804.0.0"
  config.vm.hostname = "alxcrmpy3"

  config.vm.provider :virtualbox do |vb|
    vb.gui = false
    vb.cpus = 1
    vb.memory = 2048
    vb.name = "alxcrmpy3"
  end

  config.vm.network "forwarded_port", guest: 8080, host: 8090, protocol: "tcp"
  config.vm.synced_folder ".", "/home/<USER>/app", owner: "vagrant", group: "vagrant"
  config.ssh.username = "vagrant"
  config.ssh.forward_agent = true

  ###########################
  # Install script
  # don't use tabs or spaces
  ###########################

  config.vm.provision "shell" do |s|
    s.inline = <<-'SHELL'
set -x

export DEBIAN_FRONTEND=noninteractive

systemctl disable apt-daily.service
systemctl disable apt-daily.timer

timedatectl set-timezone Europe/Warsaw

cd /tmp

add-apt-repository "deb http://apt.postgresql.org/pub/repos/apt/ xenial-pgdg main"
wget --quiet -O - https://www.postgresql.org/media/keys/ACCC4CF8.asc | apt-key add -

echo 'deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main' | tee /etc/apt/sources.list.d/google-chrome.list
wget -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | apt-key add -

apt update -y
apt install -y build-essential
apt install -y locales
apt install -y language-pack-en-base

locale-gen --purge en_US.UTF-8
dpkg-reconfigure --frontend=noninteractive locales
echo -e 'LANG="en_US.UTF-8"\nLANGUAGE="en_US.UTF-8"\nLC_ALL="en_US.UTF-8"\n' > /etc/default/locale

apt install -y postgresql-13
apt install -y postgresql-client-13
apt install -y python-pip
apt install -y tcl
apt install -y python-virtualenv
apt install -y libffi-dev
apt install -y libssl-dev
apt install -y libxml2-dev
apt install -y libxslt1-dev
apt install -y libjpeg8-dev
apt install -y libfreetype6-dev
apt install -y zlib1g-dev
apt install -y libpq-dev
apt install -y libsasl2-dev
apt install -y libldap2-dev
apt install -y gettext
apt install -y dkms
apt install -y phantomjs
apt install -y bsdtar
#apt install -y google-chrome-stable=92.0.4515.131-1
apt install -y google-chrome-beta=93.0.4577.42-1
apt install -y rabbitmq-server

apt -y autoremove

PYTHON_VER="3.8.10"
PYTHON_BIN="python3.8"


if [ ! -f "/root/Python-$PYTHON_VER.tgz" ]
then
    cd /root
    wget https://www.python.org/ftp/python/$PYTHON_VER/Python-$PYTHON_VER.tgz
    tar -xvf Python-$PYTHON_VER.tgz
    cd Python-$PYTHON_VER
    ./configure
    make altinstall
    cd /tmp
fi

WKHTMLTOPDF_VER="https://github.com/wkhtmltopdf/wkhtmltopdf/releases/download/0.12.3/wkhtmltox-0.12.3_linux-generic-amd64.tar.xz"

if [ ! -f "/usr/bin/wkhtmltopdf" ]
then
    wget $WKHTMLTOPDF_VER -O wkhtmltox.tar.xz
    tar xvf wkhtmltox.tar.xz
    mv -f wkhtmltox/bin/wkhtmlto* /usr/bin/
    rm -Rf wkhtmltox*
fi

# CHROME_VERSION="85.0.4183.83-1"
#
# if [ ! -f "/root/google-chrome-stable_${CHROME_VERSION}_amd64.deb" ]
# then
#     cd /root
#     wget --no-check-certificate https://dl.google.com/linux/chrome/deb/pool/main/g/google-chrome-stable/google-chrome-stable_${CHROME_VERSION}_amd64.deb
#     dpkg -i google-chrome-stable_${CHROME_VERSION}_amd64.deb || apt -y -f --install-suggests --install-recommends install
#     cd /tmp
# fi

CHROMEDRIVER_VER="93.0.4577.15"

if [ ! -f "/usr/local/bin/chromedriver" ]
then
    curl https://chromedriver.storage.googleapis.com/93.0.4577.15/chromedriver_linux64.zip | bsdtar -xvf - -C /usr/local/bin/
    chmod 0755 /usr/local/bin/chromedriver
fi

PHANTOM_JS="phantomjs-2.1.1-linux-x86_64"

if [ ! -f "/usr/local/share/$PHANTOM_JS/bin/phantomjs" ]
then
    rm -rf $PHANTOM_JS
    rm -rf /usr/local/share/$PHANTOM_JS
    wget https://bitbucket.org/ariya/phantomjs/downloads/$PHANTOM_JS.tar.bz2
    tar xvjf $PHANTOM_JS.tar.bz2
    mv $PHANTOM_JS /usr/local/share/
    ln -sf /usr/local/share/$PHANTOM_JS/bin/phantomjs /usr/local/share/phantomjs
    ln -sf /usr/local/share/$PHANTOM_JS/bin/phantomjs /usr/local/bin/phantomjs
    ln -sf /usr/local/share/$PHANTOM_JS/bin/phantomjs /usr/bin/phantomjs
fi

su - postgres -c "psql -c \"drop database if exists dev_alxcrm;\""
su - postgres -c "psql -c \"create database dev_alxcrm;\""
su - postgres -c "psql -c \"drop role if exists user_dev_alxcrm;\""
su - postgres -c "psql -c \"create role user_dev_alxcrm with login createdb encrypted password 'postgres';\""
su - postgres -c "psql -c \"grant all privileges on database dev_alxcrm to user_dev_alxcrm;\""

su - vagrant -c "curl -s --insecure https://lujhagFecInt:<EMAIL>/alxcrm-devel.sql > /tmp/dump.sql"
su - vagrant -c "PGPASSWORD=postgres psql -h localhost -d dev_alxcrm -U user_dev_alxcrm -f /tmp/dump.sql"

PROJECT_NAME="app"
VIRTUALENV_NAME=$PROJECT_NAME
PROJECT_DIR=/home/<USER>/$PROJECT_NAME
VIRTUALENV_DIR=/home/<USER>/env

rm -rf $VIRTUALENV_DIR
rm -rf $PROJECT_DIR/static_collected

find . -name "*.pyc" -exec rm -f {} \;

su - vagrant -c "$PYTHON_BIN -m venv $VIRTUALENV_DIR"
su - vagrant -c "source $VIRTUALENV_DIR/bin/activate && cd $PROJECT_DIR && pip install --upgrade pip==20.2.3"
su - vagrant -c "source $VIRTUALENV_DIR/bin/activate && cd $PROJECT_DIR && pip install -r requirements/development.txt --no-cache-dir --use-feature=2020-resolver"
su - vagrant -c "source $VIRTUALENV_DIR/bin/activate && cd $PROJECT_DIR && python manage.py collectstatic --noinput"
su - vagrant -c "source $VIRTUALENV_DIR/bin/activate && cd $PROJECT_DIR && python manage.py compilemessages"
su - vagrant -c "source $VIRTUALENV_DIR/bin/activate && cd $PROJECT_DIR && python manage.py migrate --noinput"
su - vagrant -c "source $VIRTUALENV_DIR/bin/activate && cd $PROJECT_DIR && echo \"from django.contrib.auth.models import User; User.objects.create_superuser('localadmin', '<EMAIL>', 'pass')\" | python manage.py shell"

cat > /home/<USER>/run.sh <<-EOF
#!/bin/sh
. /home/<USER>/env/bin/activate
cd /home/<USER>/app && python manage.py runserver 0.0.0.0:8080
EOF

cat > /home/<USER>/test.sh <<-EOF
#!/bin/sh
. /home/<USER>/env/bin/activate
cd /home/<USER>/app && python manage.py alxtest -v2 --noinput
EOF

chmod +x /home/<USER>/run.sh
chmod +x /home/<USER>/test.sh
chown vagrant:vagrant /home/<USER>/run.sh
chown vagrant:vagrant /home/<USER>/test.sh
SHELL
  end
end
