class RobotsNoindexPDFMiddleware:
    """
    Adds X-Robots-Tag: noindex for:
    - /pdf/*
    - /en/pdf/*

    Minimal, path-based middleware so it works regardless of which view serves PDFs.
    """

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)

        path = request.path or ""
        # Exact requirement: cover /pdf/* and /en/pdf/*
        if path.startswith("/pdf/") or path.startswith("/en/pdf/"):
            # Set or override the header explicitly
            response["X-Robots-Tag"] = "noindex"

        return response
