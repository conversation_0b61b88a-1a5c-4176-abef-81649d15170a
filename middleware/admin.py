from django.conf import settings
from django.utils import translation
from django.contrib import messages
from django.http import HttpResponseRedirect
from urllib.parse import urlencode

class AdminLocaleMiddleware(object):
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        if request.path.startswith("/admin"):
            translation.activate(settings.LANGUAGE_CODE)
            request.LANGUAGE_CODE = translation.get_language()

        response = self.get_response(request)
        return response


class AdminSearchTermLengthMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Check if it is an admin search query
        if '/admin/' in request.path and request.GET.get('q'):
            search_term = request.GET['q']
            phrases = search_term.split()

            # Limit to 5 words
            if len(phrases) > settings.ADMIN_QUERY_LIMIT:

                # Create a mutable copy of the query dict
                modified_get_data = request.GET.copy()
                modified_get_data['q'] = " ".join(phrases[:settings.ADMIN_QUERY_LIMIT])

                messages.warning(request, 'Search term too long. Only the first 5 words will be searched.')
                # Build the new query string
                query_string = urlencode(modified_get_data)
                # Redirect to the same page with the modified query
                return HttpResponseRedirect(f"{request.path}?{query_string}")

        response = self.get_response(request)
        return response