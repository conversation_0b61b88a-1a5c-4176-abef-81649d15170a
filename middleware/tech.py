from django.http import Http404


class TechRootGuardMiddleware:
    """
    Short-circuits requests to bare tech roots to prevent any redirect loops
    that might be introduced by upstream (nginx) or Django middlewares.

    Affects exactly these paths (with or without trailing slash):
      - /tech
      - /tech/
      - /pl/tech
      - /pl/tech/

    Raises Http404 immediately so no redirect (301/302) is emitted.
    """

    BLOCKED_PATHS = {"/tech", "/tech/", "/pl/tech", "/pl/tech/"}

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        try:
            path = request.path
        except Exception:
            path = ""

        if path in self.BLOCKED_PATHS:
            raise Http404

        return self.get_response(request)
