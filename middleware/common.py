from threading import local

from django.contrib.auth.models import User

_thread_locals = local()


def get_current_user():
    return getattr(_thread_locals, "user", None)


class ThreadLocalMiddleware(object):
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        user = getattr(request, "user", None)
        if not isinstance(user, User):
            user = None
        _thread_locals.user = user

        response = self.get_response(request)

        _thread_locals.user = None
        return response
