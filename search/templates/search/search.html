{% extends "www/base.html" %}

{% load i18n highlight %}

{% block data_layer %}

<script>
  dataLayer.push({ gtp: null });
  dataLayer.push({ gbv: null });
  dataLayer.push({
    'event': 'gbv',
    'gbv': {
      'event_name': 'view_search_results',
      'items': [
          // pierwsze 5 pozycji
        {% for obj in page.object_list|slice:"5" %}
            {% if obj.object.kod  %}
              {
                'id': "{{ obj.object.kod }}",
                'google_business_vertical': 'education'
              },
            {% endif %}
        {% endfor %}
      ]
      },
    'gtp': {
      'edu_pagetype': 'searchresults',
      'edu_pid': [
          {% for obj in page.object_list|slice:"5" %}"{{ obj.object.kod }}",{% endfor %}
      ]
    }
  });
</script>



{% endblock %}

{% block title %}{% trans "Szukaj - ALX" %}{% endblock title %}
{% block h1 %}<h1>{% trans "Wyszukiwanie" %}</h1>{% endblock h1 %}

{% block extra_head %}
    {{ block.super }}
    <meta name="robots" content="noindex,follow" />
{% endblock extra_head %}

{% block search_query %}{{ query|striptags }}{% endblock %}

{% block body %}
    <h3>{% trans "Wyniki" %}</h3>

    {% if query %}

        {% for result in page.object_list %}
            <div class="search-block">
                {{ result.rendered|safe }}
            </div>
        {% empty %}
            <p>{% trans "Brak dopasowań." %}</p>
        {% endfor %}

        {% if page.has_previous or page.has_next %}
            {% if page.has_previous|striptags or page.has_next %}
                <div>
                    {% if page.has_previous %}<a href="?q={{ query }}&amp;page={{ page.previous_page_number }}">{% endif %}&laquo; {% trans "Poprzednie" %}{% if page.has_previous %}</a>{% endif %}
                    |
                    {% if page.has_next %}<a href="?q={{ query }}&amp;page={{ page.next_page_number }}">{% endif %}{% trans "Następne" %} &raquo;{% if page.has_next %}</a>{% endif %}
                </div>
            {% endif %}
        {% endif %}
    {% else %}
        <p>{% trans "Brak dopasowań." %}</p>
    {% endif %}
{% endblock body %}
