from django import forms
from django.utils.html import strip_tags
from django.utils.translation import ugettext_lazy as _
from haystack.query import EmptySearchQuerySet, SearchQuerySet


class SearchForm(forms.Form):
    q = forms.CharField(
        required=False,
        label=_("Search"),
        widget=forms.TextInput(attrs={"type": "search"}),
    )

    def __init__(self, *args, **kwargs):
        self.searchqueryset = kwargs.pop("searchqueryset", None)
        self.load_all = kwargs.pop("load_all", False)

        if self.searchqueryset is None:
            self.searchqueryset = SearchQuerySet()

        super().__init__(*args, **kwargs)

    def clean_q(self):
        q = self.cleaned_data.get("q")
        if q:
            q = strip_tags(q).replace("++", "pp")
            for char in ['"', "'", "=", "(", ")", "*"]:
                q = q.replace(char, "")
        return q

    def no_query_found(self):
        return EmptySearchQuerySet()

    def search(self):
        if not self.is_valid():
            return self.no_query_found()

        if not self.cleaned_data.get("q"):
            return self.no_query_found()

        sqs = self.searchqueryset.auto_query(self.cleaned_data["q"])

        if self.load_all:
            sqs = sqs.load_all()

        return sqs

    def get_suggestion(self):
        if not self.is_valid():
            return None

        return self.searchqueryset.spelling_suggestion(self.cleaned_data["q"])
