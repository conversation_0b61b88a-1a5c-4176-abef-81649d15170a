from django.conf import settings
from django.core.paginator import Paginator
from django.http import Http404
from django.shortcuts import render
from django.views.decorators.cache import never_cache
from haystack.query import EmptySearchQuerySet, SearchQuerySet
from haystack.views import RESULTS_PER_PAGE

from www.views import activate_translation

from .forms import SearchForm


@never_cache
@activate_translation
def search(request, template="search/search.html", language=settings.DEFAULT_LANGUAGE):
    results_per_page = None
    searchqueryset = SearchQuerySet().filter(language=language)
    extra_context = None
    load_all = True
    query = ""
    q = request.GET.get("q", "")
    results = EmptySearchQuerySet()

    if q:
        form = SearchForm(request.GET, searchqueryset=searchqueryset, load_all=load_all)
        if form.is_valid():
            query = q
            results = form.search()
    else:
        form = SearchForm(searchqueryset=searchqueryset, load_all=load_all)

    paginator = Paginator(results, results_per_page or RESULTS_PER_PAGE)

    try:
        page = paginator.page(int(request.GET.get("page", 1)))
    except Exception:
        raise Http404

    context = {
        "form": form,
        "page": page,
        "paginator": paginator,
        "query": query,
        "suggestion": None,
    }

    # if results.query.backend.include_spelling:
    #     context['suggestion'] = form.get_suggestion()

    if extra_context:
        context.update(extra_context)

    return render(
        request,
        template,
        context,
    )
