import codecs
import fnmatch
import os
import re
from decimal import Decimal

import polib
from django.conf import settings
from django.core import management
from django.test.utils import override_settings
from django.urls import reverse
from django.utils.translation import override

from www.testfactories import WalutaFactory
from www.testhelpers import ALXTestCase

from .models import Waluta, WersjaJezykowa


class WalutaTestCase(ALXTestCase):
    def test_pobieranie_kursu_kupna_waluty(self):
        """
        Sprawdza, czy metoda klasowa do pobierania kursu waluty działa.
        """
        for nazwa, symbol, kurs, zaliczka in (
            ("sum", "UZS", Decimal("4.2755"), Decimal("2.34")),
            ("rupia lankijska", "LKR", Decimal("5.0723"), Decimal("5.67")),
            ("baht", "THB", Decimal("3.2484"), Decimal("78.9")),
        ):
            WalutaFactory.create(
                nazwa=nazwa, symbol=symbol, kurs_kupna=kurs, zaliczka=zaliczka
            )

        self.assertEqual(Waluta.pobierz_kurs_kupna("sum"), Decimal("4.2755"))
        # Nazwa skrócona dla funta też powinna zadziałać.
        self.assertEqual(Waluta.pobierz_kurs_kupna("rupia"), Decimal("5.0723"))
        self.assertEqual(Waluta.pobierz_kurs_kupna("baht"), Decimal("3.2484"))

    def test_pobieranie_waluty_o_danej_nazwie(self):
        peso = WalutaFactory(
            nazwa="peso filipińskie",
            symbol="PHP",
            kurs_kupna=Decimal("5.55"),
            zaliczka=Decimal("567"),
        )
        peso.save()
        self.assertEqual(Waluta.o_nazwie("peso filipińskie"), peso)
        self.assertEqual(Waluta.o_nazwie("peso"), peso)


class WersjaJezykowaTestCase(ALXTestCase):
    def setUp(self):
        self.wersja_brytyjska = WersjaJezykowa.objects.get(kod_jezyka="en")
        self.wersja_polska = WersjaJezykowa.objects.get(kod_jezyka="pl")

    def test_pobieranie_biezacej_wersji(self):
        with override("pl"):
            self.assertEqual(WersjaJezykowa.biezaca(), self.wersja_polska)

        with override("en"):
            self.assertEqual(WersjaJezykowa.biezaca(), self.wersja_brytyjska)

    def test_cacheowanie_pobranych_wersji(self):
        """Sprawdzić, czy ponowne pobranie bieżącej wersji nie powoduje
        dodatkowego zapytania."""

        # Czyścimy cache, żeby nie był zanieczyszczony z poprzednich testów.
        WersjaJezykowa.WERSJE_CACHE = {}

        with override("pl"):
            with self.assertNumQueries(1):
                self.assertEqual(WersjaJezykowa.biezaca(), self.wersja_polska)
            with self.assertNumQueries(0):
                self.assertEqual(WersjaJezykowa.biezaca(), self.wersja_polska)
        with override("en"):
            with self.assertNumQueries(1):
                self.assertEqual(WersjaJezykowa.biezaca(), self.wersja_brytyjska)

    def test_zwracanie_domeny(self):
        with override_settings(
            DOMENY_DLA_JEZYKOW={"pl": "www.foo.pl", "en": "bar.com"}
        ):
            self.assertEqual(
                WersjaJezykowa.objects.get(kod_jezyka="pl").domain, "www.foo.pl"
            )
            self.assertEqual(
                WersjaJezykowa.objects.get(kod_jezyka="en").domain, "bar.com"
            )


class TranslationsTestCase(ALXTestCase):
    def test_translations_are_complete(self):
        """
        Ensure that no translations are missing or fuzzy in PO files.
        """

        languages = set(code for code, name in settings.LANGUAGES) - set(
            [settings.LANGUAGE_CODE]
        )
        for language in languages:
            # Szybciej pewnie byłoby podać wszystkie języki na raz, ale nie chce
            # mi to działać.
            makemessages_kwargs = {
                "locale": [language],
                "no_location": True,
                "ignore_patterns": [
                    "src",
                    "lib",
                    "*templates/admin/*",
                    "*.txt",
                    "static/admin",
                    "static_collected/admin",
                    "venv-django-*",
                ],
                "verbosity": 1,
            }
            management.call_command("makemessages", **makemessages_kwargs)
            management.call_command(
                "makemessages", domain="djangojs", **makemessages_kwargs
            )

        translations_directory = os.path.join(settings.PROJECT_ROOT, "locale")
        for root, dirnames, filenames in os.walk(translations_directory):
            for filename in fnmatch.filter(filenames, "*.po"):
                pofile_path = os.path.join(root, filename)
                with codecs.open(pofile_path, "r+", encoding="utf8") as pofile_handle:
                    pofile_content = pofile_handle.read()
                    pofile_handle.seek(0)
                    regexp = re.compile(r'^"POT-Creation-Date: .*\\n"$', re.MULTILINE)
                    pofile_content = re.sub(
                        regexp,
                        '"POT-Creation-Date: 2014-11-10 20:00+0100\\\\n"',
                        pofile_content,
                    )
                    pofile_handle.write(pofile_content)
                    pofile_handle.truncate()
                translations = polib.pofile(pofile_path)
                if translations.obsolete_entries():
                    msgids = [x.msgid for x in translations.obsolete_entries()]
                    self.fail(
                        "Znaleziono zbędne tłumaczenia w pliku {}: {}".format(
                            pofile_path, ", ".join(msgids)
                        ).encode("utf-8")
                    )
                if translations.fuzzy_entries():
                    msgids = [x.msgid for x in translations.fuzzy_entries()]
                    self.fail(
                        "Znaleziono niepewne tłumaczenia w pliku {}: {}".format(
                            pofile_path, ", ".join(msgids)
                        ).encode("utf-8")
                    )
                if translations.untranslated_entries():
                    msgids = [x.msgid for x in translations.untranslated_entries()]
                    self.fail(
                        "Znaleziono brakujące tłumaczenia w pliku {}: {}".format(
                            pofile_path, ", ".join(msgids)
                        ).encode("utf-8")
                    )

    def test_js_catalog_view_doesnt_fail(self):
        for language_code in [code for code, _name in settings.LANGUAGES]:
            response = self.client.get(reverse("jsi18n") + "?language=" + language_code)
            self.assertEqual(response.status_code, 200)
