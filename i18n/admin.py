from django.contrib import admin

from .models import Panstwo, Waluta, WersjaJezykowa


class WalutaAdmin(admin.ModelAdmin):
    list_display = ("nazwa", "symbol", "kurs_kupna")

    def has_delete_permission(self, request, obj=None):
        return False


admin.site.register(Waluta, WalutaAdmin)


class PanstwoAdmin(admin.ModelAdmin):
    def has_delete_permission(self, request, obj=None):
        return False


admin.site.register(Panstwo, PanstwoAdmin)


class WersjaJezykowaAdmin(admin.ModelAdmin):
    list_display = ("nazwa", "kod_jezyka")
    filter_horizontal = ("wyswietlane_panstwa",)

    def has_delete_permission(self, request, obj=None):
        return False


admin.site.register(WersjaJezykowa, WersjaJezykowaAdmin)
