from django.conf import settings
from django.db import models
from django.utils import translation


class Panstwo(models.Model):
    nazwa = models.CharField(max_length=50, unique=True)

    class Meta:
        verbose_name = "państwo"
        verbose_name_plural = "państwa"

    def __str__(self):
        return self.nazwa


class We<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(models.Model):

    """
    We<PERSON><PERSON> ję<PERSON>, w której wyświetlana jest strona.
    """

    nazwa = models.CharField(max_length=40, unique=True)
    kod_jezyka = models.CharField(
        "kod języka",
        max_length=10,
        unique=True,
        help_text='Zobacz <a href="http://www.i18nguy.com/unicode/language-identifiers.html">www.i18nguy.com/unicode/language-identifiers.html</a>',
    )
    wyswietlane_panstwa = models.ManyToManyField(
        Panstwo,
        verbose_name="wyświetlane_panstwa",
        help_text="Terminy z tych państw będą widoczne w danej wersji językowej.",
    )
    stawka_vat = models.DecimalField(
        "stawka VAT",
        max_digits=30,
        decimal_places=4,
        blank=True,
        null=True,
        help_text="Domyślna stawka VAT, jaka będzie przypisywana uczestnikom niezwolnionym z VAT. W przypadku pustej "
        "wartości zakładamy, że w danej wersji językowej VAT nie jest doliczany",
    )

    class Meta:
        verbose_name = "wersja językowa"
        verbose_name_plural = "wersje językowe"

    # Cache dla metody .biezaca
    WERSJE_CACHE = {}

    def __str__(self):
        return "{0} ({1})".format(self.nazwa, self.kod_jezyka)

    @classmethod
    def biezaca(cls):
        """
        Zwraca wersję językową odpowiadającą aktualnie używanemu językowi.
        """
        kod_jezyka = translation.get_language()
        if kod_jezyka not in cls.WERSJE_CACHE:
            cls.WERSJE_CACHE[kod_jezyka] = cls.objects.get(kod_jezyka=kod_jezyka)
        return cls.WERSJE_CACHE[kod_jezyka]

    @property
    def domain(self):
        """
        Zwrócić domenę odpowiadającą tej wersji językowej.
        Na podstawie ustawienia DOMENY_DLA_JEZYKOW.
        """
        return settings.DOMENY_DLA_JEZYKOW[self.kod_jezyka]

    def jezyki_szkolen(self):
        """
        Zwraca języki, w jakich mogą być prowadzone szkolenia w danej wersji językowej.
        """
        # Z bazy raczej nie ma jak tego wydedukować.
        if self.kod_jezyka == "pl":
            return ["pl", "en"]
        if self.kod_jezyka == "en":
            return ["en"]
        raise Exception("Nie wiem, co zwrocic dla tej wersji jezykowej.")


class Waluta(models.Model):
    nazwa = models.CharField(max_length=100, unique=True)
    symbol = models.CharField(
        max_length=8,
        unique=True,
        help_text="Używany przy wyświetlaniu cen na stronie, np. GBP, £",
    )
    kurs_kupna = models.DecimalField(
        max_digits=30, decimal_places=4, help_text="W złotówkach"
    )
    mnoznik_ceny = models.DecimalField(
        "mnożnik ceny",
        max_digits=7,
        decimal_places=2,
        help_text="Przez który będzie mnożona cena szkolenia-tłumaczenia, gdy nie będzie ono miało podane ceny explicite.",
        default=1,
    )
    zaliczka = models.DecimalField(
        max_digits=30,
        decimal_places=2,
        help_text="Kwota netto.",
    )

    class Meta:
        verbose_name_plural = "waluty"

    def __str__(self):
        return "{0} ({1})".format(self.nazwa, self.symbol)

    @classmethod
    def pobierz_kurs_kupna(cls, nazwa):
        """
        Zwraca kurs kupna dla waluty o danej nazwie.
        """
        return cls.o_nazwie(nazwa).kurs_kupna

    @classmethod
    def o_nazwie(cls, nazwa):
        """
        Zwraca walutę o danej nazwie.

        Dopasowanie nazwy nie musi być dokładne; w przypadku niejasności zostanie rzucony wyjątek.
        """
        return cls.objects.get(nazwa__icontains=nazwa)

    def cena_ze_zlotowek(self, cena_w_zlotowkach):
        """
        Przelicza cenę w złotówkach na daną walutę, uwzględniając mnożnik ceny.
        """
        return cena_w_zlotowkach / self.kurs_kupna * self.mnoznik_ceny
