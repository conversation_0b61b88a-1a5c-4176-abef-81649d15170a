from .utils import pretty_number


class PoleSynchronizowane(object):

    """
    Deskryptor pozwalający zwracać wartości z obiektu bazowego,
    je<PERSON><PERSON> w tłumaczeniu nie są one dostępne.
    Konstruktor przyjmuje parametr „nazwa_pola” określaj<PERSON>cy
    pole, które ma być synchronizowane.
    """

    def __init__(self, nazwa_pola):
        self.nazwa_pola = nazwa_pola

    def __get__(self, instance, owner):
        wartosc = getattr(instance, self.nazwa_pola)
        if wartosc is None:
            if instance.base_translation:
                wartosc_z_obiektu_bazowego = getattr(
                    instance.base_translation, self.nazwa_pola
                )
                wartosc = self.transform_value(
                    wartosc_z_obiektu_bazowego, instance, owner
                )
        return wartosc

    def transform_value(self, value, instance, owner):
        """
        Przekształca wartość pobraną z obiektu bazowego.
        Funkcja przeznaczona do nadpisania w podklasach.
        """
        return value

    def __set__(self, instance, value):
        setattr(instance, self.nazwa_pola, value)


class CenaDescriptor(PoleSynchronizowane):

    """
    Deskryptor pozwalający dynamicznie generować ceny dla
    tłumaczonych obiektów oraz ustawiać je na sztywno.
    Przyjmuje przy inicjalizacji nazwę pola z synchronizowaną
    ceną i parametry exp i rounding do funkcji quantize
    (zaokrąglenia).
    """

    def __init__(self, nazwa_pola, exp, rounding):
        self.exp = exp
        self.rounding = rounding
        super().__init__(nazwa_pola)

    def transform_value(self, value, instance, owner):
        """
        Przelicza i zaokrągla wartość ze złotówek.
        """
        if value is None:
            return value
        else:
            return instance.waluta.cena_ze_zlotowek(value).quantize(
                self.exp, rounding=self.rounding
            )

    def __get__(self, instance, owner):
        wartosc = getattr(instance, self.nazwa_pola)
        if not (wartosc is None and getattr(instance, 'cena_bazowa', None) is not None and self.nazwa_pola in ['cena_autoryzacji_bazowa', 'cena_przed_promocja_bazowa', 'cena_w_grupie_2_os_bazowa', 'cena_indywidualnie_bazowa']):
            # Gdy przeciazona jest cena bazowa, to nie szukamy cen promocyjnych w obiketach bazowych 
            wartosc = super().__get__(instance, owner)
        if wartosc is None:
            return wartosc
        else:
            return pretty_number(wartosc)
