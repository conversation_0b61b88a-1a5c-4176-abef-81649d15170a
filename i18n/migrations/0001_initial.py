from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Panstwo",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("nazwa", models.CharField(unique=True, max_length=50)),
            ],
            options={
                "verbose_name": "państwo",
                "verbose_name_plural": "państwa",
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="Waluta",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("nazwa", models.CharField(unique=True, max_length=100)),
                (
                    "symbol",
                    models.CharField(
                        help_text="Używany przy wyświetlaniu cen na stronie, np. GBP, \xa3",
                        unique=True,
                        max_length=8,
                    ),
                ),
                (
                    "kurs_kupna",
                    models.DecimalField(
                        help_text="W złotówkach", max_digits=30, decimal_places=4
                    ),
                ),
                (
                    "mnoznik_ceny",
                    models.DecimalField(
                        default=1,
                        help_text="Przez który będzie mnożona cena szkolenia-tłumaczenia, gdy nie będzie ono miało podane ceny explicite.",
                        verbose_name="mnożnik ceny",
                        max_digits=7,
                        decimal_places=2,
                    ),
                ),
                (
                    "zaliczka",
                    models.DecimalField(
                        help_text="Kwota netto.", max_digits=30, decimal_places=2
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "waluty",
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="WersjaJezykowa",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("nazwa", models.CharField(unique=True, max_length=40)),
                (
                    "kod_jezyka",
                    models.CharField(
                        help_text='Zobacz <a href="http://www.i18nguy.com/unicode/language-identifiers.html">www.i18nguy.com/unicode/language-identifiers.html</a>',
                        unique=True,
                        max_length=10,
                        verbose_name="kod języka",
                    ),
                ),
                (
                    "stawka_vat",
                    models.DecimalField(
                        decimal_places=4,
                        max_digits=30,
                        blank=True,
                        help_text="Domyślna stawka VAT, jaka będzie przypisywana uczestnikom niezwolnionym z VAT. W przypadku pustej wartości zakładamy, że w danej wersji językowej VAT nie jest doliczany",
                        null=True,
                        verbose_name="stawka VAT",
                    ),
                ),
                (
                    "wyswietlane_panstwa",
                    models.ManyToManyField(
                        help_text="Terminy z tych państw będą widoczne w danej wersji językowej.",
                        to="i18n.Panstwo",
                        verbose_name="wyświetlane_panstwa",
                    ),
                ),
            ],
            options={
                "verbose_name": "wersja językowa",
                "verbose_name_plural": "wersje językowe",
            },
            bases=(models.Model,),
        ),
    ]
