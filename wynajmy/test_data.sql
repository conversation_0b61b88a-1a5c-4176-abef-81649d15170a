DELETE FROM wynajmy_wynajem;
DELETE FROM wynajmy_notatka;
DELETE FROM wynajmy_dokument;
DELETE FROM wynajmy_czlowiek;
DELETE FROM wynajmy_klient;
DELETE FROM wynajmy_typ_dokumentu;
DELETE FROM wynajmy_typ_stawki;
DELETE FROM wynajmy_status_czlowieka;
DELETE FROM wynajmy_miasto;


INSERT INTO wynajmy_miasto (nazwa) values ('<PERSON>zawa');
INSERT INTO wynajmy_miasto (nazwa) values ('Wrocław');

INSERT INTO wynajmy_status_czlowieka (skrot, nazwa) values ('NK', 'Nowy kontakt');
INSERT INTO wynajmy_status_czlowieka (skrot, nazwa) values ('NEG', 'Sprawdzony negatywnie');
INSERT INTO wynajmy_status_czlowieka (skrot, nazwa) values ('UTR', 'Sprawdzony pozytywnie, trwale utracony');
INSERT INTO wynajmy_status_czlowieka (skrot, nazwa) values ('TMP', 'Tymczasowo niechciany lub niedostępny');
INSERT INTO wynajmy_status_czlowieka (skrot, nazwa) values ('ALX', 'Sprawdzony pozytywnie, zajęty przez ALX');
INSERT INTO wynajmy_status_czlowieka (skrot, nazwa) values ('WOL', 'Sprawdzony pozytywnie, wolny');
INSERT INTO wynajmy_status_czlowieka (skrot, nazwa) values ('WYN', 'Sprawdzony pozytywnie, wynajęty');

INSERT INTO wynajmy_typ_stawki (skrot, nazwa) values ('UD50', 'UD 50 netto');
INSERT INTO wynajmy_typ_stawki (skrot, nazwa) values ('UD20', 'UD 20 netto');
INSERT INTO wynajmy_typ_stawki (skrot, nazwa) values ('UP', 'UP netto');
INSERT INTO wynajmy_typ_stawki (skrot, nazwa) values ('FV', 'FV netto');


INSERT INTO wynajmy_typ_dokumentu (skrot, nazwa) values('CV', 'CV');
INSERT INTO wynajmy_typ_dokumentu (skrot, nazwa) values('UM', 'Umowa');
INSERT INTO wynajmy_typ_dokumentu (skrot, nazwa) values('NDA', 'NDA');


INSERT INTO wynajmy_klient (nazwa) values ('ALX - szkolenia');
INSERT INTO wynajmy_klient (nazwa) values ('ALX');
INSERT INTO wynajmy_klient (nazwa) values ('Sygnity');
INSERT INTO wynajmy_klient (nazwa) values ('Procter & Gamble');

INSERT INTO wynajmy_czlowiek (imie, nazwisko, opiekun_id, opis, miasto_id, status_id, typ_stawki_id, stawka, kompetencje)
 values ('Kubuś', 'Puchatek', 1, 'Miś o małym rozumku', 2, 2, 2, 23.00, 'Ma dużo czasu na jedzenie miodu.');
INSERT INTO wynajmy_czlowiek (imie, nazwisko, opiekun_id, opis, miasto_id, status_id, typ_stawki_id, stawka, kompetencje)
 values ('Zosia', 'Kokosia', 1, 'Samodzielny programista PHP', 2, 6, 3, 50.00, 'Coś tam umie...');
INSERT INTO wynajmy_czlowiek (imie, nazwisko, opiekun_id, opis, miasto_id, status_id, typ_stawki_id, stawka, kompetencje)
 values ('Fred', 'Flinstone', 1, 'Głupi, ale silny.', 1, 3, 1, 23.00, 'Umie podnosić wielkie ciężary. Chętnie się uczy.');
INSERT INTO wynajmy_czlowiek (imie, nazwisko, opiekun_id, opis, miasto_id, status_id, typ_stawki_id, stawka, kompetencje)
 values ('Zosia', 'Samosia', 1, 'Samodzielny programista PHP', 1, 4, 1, 50.00, 'Nie wiem co tam ona umie, ale robi smaczną herbatę.');


INSERT INTO wynajmy_dokument (typ_dokumentu_id, czlowiek_id, link) VALUES (3, 1 , 'http://fred.pl/nda8.pdf' ); 
INSERT INTO wynajmy_dokument (typ_dokumentu_id, czlowiek_id, link) VALUES ( 3 , 1 ,'http://fred.pl/nda9.pdf' ); 
INSERT INTO wynajmy_dokument (typ_dokumentu_id, czlowiek_id, link) VALUES ( 3 , 1 ,'http://fred.pl/nda10.pdf'); 
INSERT INTO wynajmy_dokument (typ_dokumentu_id, czlowiek_id, link) VALUES ( 1 , 1 ,'http://fred.pl/cv.txt' ); 
INSERT INTO wynajmy_dokument (typ_dokumentu_id, czlowiek_id, link) VALUES ( 2 , 2 ,'http://kubus.pl/umowa.pdf' ); 
INSERT INTO wynajmy_dokument (typ_dokumentu_id, czlowiek_id, link) VALUES ( 2 , 2 ,'http://kubus.pl/umowa2.pdf' ); 
INSERT INTO wynajmy_dokument (typ_dokumentu_id, czlowiek_id, link) VALUES ( 2 , 2 ,'http://kubus.pl/umowa3.pdf' ); 
INSERT INTO wynajmy_dokument (typ_dokumentu_id, czlowiek_id, link) VALUES ( 2 , 2 ,'http://kubus.pl/umowa4.pdf' ); 
INSERT INTO wynajmy_dokument (typ_dokumentu_id, czlowiek_id, link) VALUES ( 2 , 2 ,'http://kubus.pl/umowa5.pdf' ); 
INSERT INTO wynajmy_dokument (typ_dokumentu_id, czlowiek_id, link) VALUES (2, 2 ,'http://zosia.pl/umowa.pdf' );
INSERT INTO wynajmy_dokument (typ_dokumentu_id, czlowiek_id, link) VALUES (2, 1 ,'http://fred.pl/umowa1.pdf' );
INSERT INTO wynajmy_dokument (typ_dokumentu_id, czlowiek_id, link) VALUES (2, 2 ,'http://kubus.pl/umowa17.pdf' );
INSERT INTO wynajmy_dokument (typ_dokumentu_id, czlowiek_id, link) VALUES (2, 3 ,'http://kubus.pl/umowa15.pdf' );
INSERT INTO wynajmy_dokument (typ_dokumentu_id, czlowiek_id, link) VALUES (2, 1 ,'http://fred.pl/umowa2.pdf' );
INSERT INTO wynajmy_dokument (typ_dokumentu_id, czlowiek_id, link) VALUES (3, 4 ,'http://kokosia.pl/nda1.pdf' );
INSERT INTO wynajmy_dokument (typ_dokumentu_id, czlowiek_id, link) VALUES (1, 4 ,'http://cvzosikokosi.pl' );
INSERT INTO wynajmy_dokument (typ_dokumentu_id, czlowiek_id, link) VALUES (2, 3 ,'http://umowa.pl' );


INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 1 ,'2011-12-13','Dodano do bazy.<br>Umie podnosić wielkie ciężary. Chętnie się uczy.','t', 't');             
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 4 ,'2011-12-13','Dodano do bazy.','t', 't');         
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 3 ,'2011-12-13','Zmieniono kompetencjena: Nie wiem co tam ona umie, ale robi smaczną herbatę.','t', 't');
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 4 ,'2011-12-13','Zmieniono kompetencje na: Jak to?! Nic nie umie?','t', 't');    
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 1 ,'2011-12-14','Dodano 1: NDA','t', 't');         
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 1 ,'2011-12-14','Dodano 1: NDA','t', 't');         
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 1 ,'2011-12-14','Zmieniono opis na: Głupi, ale silny. Hej!','t', 't');     
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 1 ,'2011-12-14','Zmieniono status na: WYN','t', 't');        
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 1 ,'2011-12-14','Zmieniono stawka na: 23.00','t', 't');        
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 1 ,'2011-12-14','Zmieniono opis na: Głupi, ale silny. Hej! Ohoh','t', 't');    
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 1 ,'2011-12-14','Zmieniono miasto na: KR','t', 't');        
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 1 ,'2011-12-14','Dodano NDA: http://fred.pl/nda11.pdf','t', 't');         
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 1 ,'2011-12-14','Usunięto NDA: http://fred.pl/nda11.pdf','t', 't');         
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 1 ,'2011-12-14','Dodano CV: http://fred.pl/cv.txt','t', 't');         
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 2 ,'2011-12-16','Dodano wynajem od 2011-12-19 do 2011-12-22 dla ALX.','t', 't');    
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 3 ,'2011-12-16','Dodano wynajem od 2011-12-20 do 2011-12-21 dla ALX -0 szkolenia.','t', 't');  
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 2 ,'2011-12-16','Dodano UM: http://kubus.pl/umowa.pdf','t', 't');         
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 2 ,'2011-12-16','Dodano UM: http://kubus.pl/umowa2.pdf','t', 't');         
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 2 ,'2011-12-16','Dodano wynajem od 2011-12-19 do 2011-12-22 dla ALX.','t', 't');    
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 2 ,'2011-12-16','Dodano UM: http://kubus.pl/umowa3.pdf','t', 't');         
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 2 ,'2011-12-16','Dodano UM: http://kubus.pl/umowa4.pdf','t', 't');         
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 2 ,'2011-12-16','Dodano UM: http://kubus.pl/umowa5.pdf','t', 't');         
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 3 ,'2011-12-19','Dodano wynajem od 2011-12-14 do 2011-12-20 dla ALX.','t', 't');    
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 2 ,'2011-12-19','Dodano UM: http://zosia.pl/umowa.pdf','t', 't');         
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 1 ,'2011-12-19','Dodano wynajem od 2011-12-22 do 2011-12-22 dla ALX -0 szkolenia.','t', 't');  
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 1 ,'2011-12-19','Dodano wynajem od 2011-12-19 do 2011-12-21 dla ALX -0 szkolenia.','t', 't');  
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 1 ,'2011-12-19','Dodano wynajem od 2011-12-27 do 2011-12-28 dla ALX.','t', 't');    
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 2 ,'2011-12-19','Dodano wynajem od 2011-12-18 do 2011-12-22 dla ALX.','t', 't');    
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 2 ,'2011-12-19','W wynajmie dla ALX zmieniono data_do z 2011-12-21 na: 2011-12-20','t', 't');  
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 2 ,'2011-12-19','Dodano wynajem od 2011-12-21 do 2011-12-24 dla ALX -0 szkolenia.','t', 't');  
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 2 ,'2011-12-19','Dodano UM: http://kubus.pl/umowa15.pdf','t', 't');         
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 2 ,'2011-12-19','W wynajmie dla ALX -0 szkolenia zmieniono umowa_id z None na: 19','t', 't');
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 1 ,'2011-12-19','Dodano UM: http://fred.pl/umowa1.pdf','t', 't');         
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 1 ,'2011-12-19','W wynajmie dla ALX zmieniono umowa_id z None na: 20','t', 't');  
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 1 ,'2011-12-19','Dodano UM: http://fred.pl/umowa2.pdf','t', 't');         
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 3 ,'2011-12-19','Dodano wynajem od 2011-12-26 do 2011-12-29 dla ALX.','t', 't');    
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 2 ,'2011-12-19','Dodano wynajem od 2011-12-25 do 2011-12-28 dla ALX -0 szkolenia.','t', 't');  
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 2 ,'2011-12-19','Dodano wynajem od 2011-12-29 do 2011-12-31 dla ALX -0 szkolenia.','t', 't');  
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 2 ,'2011-12-19','Dodano wynajem od 2012-01-01 do 2012-01-28 dla ALX -0 szkolenia.','t', 't');  
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 2 ,'2011-12-19','W wynajmie dla ALX -0 szkolenia zmieniono data_do z 2012-01-28 na: 2012-01-15','t', 't');
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 2 ,'2011-12-19','Dodano wynajem od 2012-01-16 do 2012-01-28 dla ALX -0 szkolenia.','t', 't');  
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 2 ,'2011-12-19','Dodano UM: http://kubus.pl/umowa17.pdf','t', 't');         
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 3 ,'2011-12-20','Dodano wynajem od 2011-12-30 do 2012-01-17 dla ALX.','t', 't');    
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 3 ,'2011-12-20','W wynajmie dla ALX -0 szkolenia zmieniono czlowiek_id z 2 na: 3','t', 't');
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 3 ,'2011-12-20','W wynajmie dla ALX -0 szkolenia zmieniono data_do z 2011-12-24 na: 2011-12-23','t', 't');
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 1 ,'2011-12-20','W wynajmie dla ALX -0 szkolenia zmieniono data_do z 2011-12-22 na: 2011-12-23','t', 't');
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 3 ,'2011-12-20','Dodano wynajem od 2012-01-18 do 2012-01-24 dla ALX.','t', 't');    
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 2 ,'2011-12-20','Usunięto UM:','t', 't');          
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 4 ,'2011-12-20','Dodano NDA: http://kokosia.pl/nda1.pdf','t', 't');         
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 4 ,'2011-12-20','Dodano CV: http://cvzosikokosi.pl/','t', 't');         
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 3 ,'2011-12-20','Dodano UM: http://umowa.pl/','t', 't');         
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 2 ,'2011-12-31','Dodano wynajem od 2012-01-29 do 2012-01-31 dla ALX -0 szkolenia.','t', 't');  
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 2 ,'2011-12-31','W wynajmie dla ALX -0 szkolenia zmieniono data_do z 2012-01-31 na: 2012-01-30','t', 't');
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 3 ,'2011-12-31','W wynajmie dla ALX zmieniono stawka_klienta z 200.00 na: 250.00','t', 't');  
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 3 ,'2011-12-31','W wynajmie dla ALX zmieniono stawka_czlowieka z 100.00 na: 150.00','t', 't');  
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 3 ,'2011-12-31','W wynajmie dla ALX zmieniono data_do z 2012-01-24 na: 2012-01-31','t', 't');  
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 3 ,'2011-12-31','W wynajmie dla ALX zmieniono data_od z 2012-01-18 na: 2012-01-25','t', 't');  
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 3 ,'2011-12-31','W wynajmie dla ALX zmieniono data_od z 2012-01-25 na: 2012-01-18','t', 't');  
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 2 ,'2011-12-31','W wynajmie dla ALX -0 szkolenia zmieniono data_do z 2012-01-30 na: 2012-02-14','t', 't');
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 2 ,'2011-12-31','W wynajmie dla ALX -0 szkolenia zmieniono data_od z 2012-01-29 na: 2012-01-31','t', 't');
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 2 ,'2011-12-31','Dodano wynajem od 2012-01-29 do 2012-01-30 dla ALX -0 szkolenia.','t', 't');  
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 4 ,'2011-12-31','Zmieniono status na: WOL','t', 't');        
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 4 ,'2011-12-31','Zmieniono kompetencje na: Coś tam umie...','t', 't');      
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 3 ,'2012-01-02','Zmieniono typ_stawki na: UD50','t', 't');        
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, aktualna, todo_zrobione) VALUES ( 2 ,'2012-01-02','W wynajmie dla ALX -0 szkolenia zmieniono data_od z 2011-12-29 na: 2012-01-02','t', 't');

 

INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, todo_data, todo_user_id, todo_zrobione, aktualna) VALUES ( 4, '2011-12-31' ,'Sprawdzić czy Zosia nauczyła się robić kanapki. ', '2012-01-04' , 1 ,'t', 't');
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, todo_data, todo_user_id, todo_zrobione, aktualna) VALUES ( 3, '2011-12-14' ,'Upiec pierniczki. ', '2011-12-20' , 1 ,'t', 't');
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, todo_data, todo_user_id, todo_zrobione, aktualna) VALUES ( 3, '2011-12-14' ,'No dobra! Napiszę tu coś. ', '2011-12-23' , 1 ,'t', 't');
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, todo_data, todo_user_id, todo_zrobione, aktualna) VALUES ( 3, '2011-12-14' ,'Piękny kalendarz mam! ', '2011-12-16' , 1 ,'t', 't');
INSERT INTO wynajmy_notatka (czlowiek_id, data, tekst, todo_data, todo_user_id, todo_zrobione, aktualna) VALUES ( 3, '2011-12-14' ,'Kupić bułki. ', '2011-12-14' , 1 ,'t', 't');

INSERT INTO wynajmy_wynajem (czlowiek_id, klient_id, data_od, data_do, stawka_klienta, stawka_czlowieka, umowa_id) VALUES( 2 , 2 , '2011-12-18' , '2011-12-20' , 11.00 , 4.00 , 10 );
INSERT INTO wynajmy_wynajem (czlowiek_id, klient_id, data_od, data_do, stawka_klienta, stawka_czlowieka, umowa_id) VALUES( 1 , 2 , '2011-12-27' , '2011-12-28' , 200.00 , 100.00 , 6 );
INSERT INTO wynajmy_wynajem (czlowiek_id, klient_id, data_od, data_do, stawka_klienta, stawka_czlowieka, umowa_id) VALUES( 3 , 1 , '2011-12-21' , '2011-12-23' , 200.00 , 120.00 , 11 );
INSERT INTO wynajmy_wynajem (czlowiek_id, klient_id, data_od, data_do, stawka_klienta, stawka_czlowieka, umowa_id) VALUES( 1 , 1 , '2011-12-19' , '2011-12-21' , 200.00 , 100.00 , 8 );
INSERT INTO wynajmy_wynajem (czlowiek_id, klient_id, data_od, data_do, stawka_klienta, stawka_czlowieka, umowa_id) VALUES( 3 , 2 , '2012-01-18' , '2012-01-31' , 250.00 , 150.00 , 13 );
INSERT INTO wynajmy_wynajem (czlowiek_id, klient_id, data_od, data_do, stawka_klienta, stawka_czlowieka, umowa_id) VALUES( 2 , 1 , '2012-01-29' , '2012-01-30' , 200.00 , 120.00 , 7 );
INSERT INTO wynajmy_wynajem (czlowiek_id, klient_id, data_od, data_do, stawka_klienta, stawka_czlowieka, umowa_id) VALUES( 2 , 1 , '2012-01-02' , '2011-12-31' , 200.00 , 120.00 , 12 );

INSERT INTO wynajmy_wynajem (czlowiek_id, klient_id, data_od, data_do, stawka_klienta, stawka_czlowieka) VALUES( 3 , 2 , '2011-12-26' , '2011-12-29' , 200.00 , 100.00 ); 
INSERT INTO wynajmy_wynajem (czlowiek_id, klient_id, data_od, data_do, stawka_klienta, stawka_czlowieka) VALUES( 2 , 1 , '2011-12-25' , '2011-12-28' , 200.00 , 120.00 ); 
INSERT INTO wynajmy_wynajem (czlowiek_id, klient_id, data_od, data_do, stawka_klienta, stawka_czlowieka) VALUES( 2 , 1 , '2012-01-01' , '2012-01-15' , 200.00 , 120.00 ); 
INSERT INTO wynajmy_wynajem (czlowiek_id, klient_id, data_od, data_do, stawka_klienta, stawka_czlowieka) VALUES( 2 , 1 , '2012-01-16' , '2012-01-28' , 200.00 , 120.00 ); 
INSERT INTO wynajmy_wynajem (czlowiek_id, klient_id, data_od, data_do, stawka_klienta, stawka_czlowieka) VALUES( 3 , 2 , '2011-12-30' , '2012-01-17' , 200.00 , 100.00 ); 
INSERT INTO wynajmy_wynajem (czlowiek_id, klient_id, data_od, data_do, stawka_klienta, stawka_czlowieka) VALUES( 2 , 1 , '2012-01-31' , '2012-02-14' , 200.00 , 120.00 ); 
INSERT INTO wynajmy_wynajem (czlowiek_id, klient_id, data_od, data_do, stawka_klienta, stawka_czlowieka) VALUES( 3 , 2 , '2011-12-14' , '2011-12-20' , 200.00 , 100.00 ); 






