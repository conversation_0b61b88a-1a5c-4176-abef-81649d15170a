import datetime

from django.contrib.admin import SimpleListFilter
from django.contrib.auth.models import User


class AktywneWynajmyFilter(SimpleListFilter):
    title = "aktywne/wszystkie"
    parameter_name = "wynajmy"

    def lookups(self, request, model_admin):
        return (
            ("aktywne", "aktywne"),
            ("aktywneiprzyszle", "aktywne i przyszłe"),
            ("przyszle", "przyszłe"),
            ("przeszle", "przeszłe"),
        )

    def queryset(self, request, queryset):
        now = datetime.date.today()
        if self.value() == "aktywne":
            return queryset.filter(data_od__lte=now, data_do__gte=now)
        elif self.value() == "aktywneiprzyszle":
            return queryset.filter(data_do__gte=now)
        elif self.value() == "przyszle":
            return queryset.filter(data_od__gt=now)
        elif self.value() == "przeszle":
            return queryset.filter(data_do__lt=now)
        else:
            return queryset


class AktywneTodoFilter(SimpleListFilter):
    title = "TODO"
    parameter_name = "todo"

    def lookups(self, request, model_admin):
        return (("aktywne", "aktywne TODO"),)

    def queryset(self, request, queryset):
        if self.value() == "aktywne":
            # aktywne_todo = Notatka.objects.filter(todo_zrobione=False)
            # ludzie_z_todo = [ n.czlowiek.id for n in aktywne_todo ]
            # ludzie_z_todo = dict( (x, x) for x in ludzie_z_todo).keys() #remove duplicates
            # return queryset.filter(id__in = ludzie_z_todo)
            return queryset.filter(notatka__todo_zrobione=False).distinct()
        return queryset

        # FILTR WYŻSZYCH STATUSÓW dla list_display ludzi


class WyzszyStatusListFilter(SimpleListFilter):
    # Human-readable title of the filter which will be displayed in the
    # right admin sidebar just above the filter options.
    title = "Status wyższy niż"
    parameter_name = "StatusId"

    def lookups(self, request, model_admin):
        return (
            ("1", "Nowy kontakt"),
            ("2", "Sprawdzony negatywnie"),
            ("3", "Sprawdzony pozytywnie, trwale utracony"),
            ("4", "Tymczasowo niechciany lub niedostępny"),
            ("5", "Sprawdzony pozytywnie, zajęty przez ALX"),
            ("6", "Sprawdzony pozytywnie, wolny"),
        )

    def queryset(self, request, queryset):
        # Defining filter on the parameter value
        if self.value() == "1":
            return queryset.filter(status__gt="1")
        elif self.value() == "2":
            return queryset.filter(status__gt="2")
        elif self.value() == "3":
            return queryset.filter(status__gt="3")
        elif self.value() == "4":
            return queryset.filter(status__gt="4")
        elif self.value() == "5":
            return queryset.filter(status__gt="5")
        elif self.value() == "6":
            return queryset.filter(status__gt="6")


#     FILTR WYBRANYCH Users jako opiekunów dla list_display ludzi
class OpiekunPrzypisany(SimpleListFilter):
    title = "opiekun wybrany"
    parameter_name = "OpiekunId"

    def lookups(self, request, model_admin):
        i = []
        for u in (
            User.objects.select_related("czlowiek")
            .distinct("username")
            .filter(czlowiek__isnull=False)
            .order_by("username")
        ):
            l = u.id
            i.append(l)
        return ((str(ui), str(User.objects.get(id=ui).username)) for ui in i)

    def queryset(self, request, queryset):
        if self.value() is None:
            return
        return queryset.filter(opiekun=self.value())
