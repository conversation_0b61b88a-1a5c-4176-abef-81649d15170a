from django import template
from django.http import QueryDict
from django.template.defaultfilters import stringfilter


@stringfilter
def query_string_update(url, update_pair):
    url_parts = url.split("?")
    update_pair = update_pair.split("=")
    if len(url_parts) == 1:
        url_parts.append("")
    qs = QueryDict(url_parts[1], mutable=True)
    qs.__setitem__(update_pair[0], update_pair[1])
    return url_parts[0] + "?" + qs.urlencode()


register = template.Library()
register.filter("qs_update", query_string_update)
