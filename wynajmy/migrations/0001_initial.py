import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models

import wynajmy.models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="<PERSON><PERSON><PERSON><PERSON>",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("imie", models.CharField(max_length=40, verbose_name="imię")),
                ("nazwisko", models.CharField(max_length=40)),
                ("opis", models.CharField(max_length=500, verbose_name="krótki opis")),
                (
                    "stawka",
                    models.DecimalField(
                        null=True, max_digits=10, decimal_places=2, blank=True
                    ),
                ),
                (
                    "proponowana_dla_klienta",
                    models.DecimalField(
                        null=True, max_digits=10, decimal_places=2, blank=True
                    ),
                ),
                ("kompetencje", models.TextField(null=True)),
            ],
            options={
                "ordering": ["nazwisko", "imie"],
                "verbose_name_plural": "ludzie",
            },
            bases=(wynajmy.models.UpdateMonitor, models.Model),
        ),
        migrations.CreateModel(
            name="Dokument",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("link", models.URLField(unique=True)),
                (
                    "czlowiek",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="wynajmy.Czlowiek",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "dokumenty",
            },
            bases=(wynajmy.models.UpdateMonitor, models.Model),
        ),
        migrations.CreateModel(
            name="KategoriaDokumentu",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("skrot", models.CharField(max_length=3)),
                ("nazwa", models.CharField(max_length=50)),
            ],
            options={
                "verbose_name": "kategoria dokumentu",
                "verbose_name_plural": "kategorie dokumentów",
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="Klient",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("nazwa", models.CharField(unique=True, max_length=100)),
                ("notatki", models.TextField(blank=True)),
            ],
            options={
                "verbose_name_plural": "klienci",
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="Miasto",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("nazwa", models.CharField(max_length=40)),
            ],
            options={
                "ordering": ["nazwa"],
                "verbose_name_plural": "miasta",
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="Notatka",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("data", models.DateField(verbose_name="data")),
                ("tekst", models.TextField(verbose_name="tekst/akcja TODO")),
                (
                    "todo_data",
                    models.DateField(null=True, verbose_name="kiedy", blank=True),
                ),
                (
                    "todo_zrobione",
                    models.BooleanField(default=True, verbose_name="czy zrobione"),
                ),
                (
                    "aktualna",
                    models.BooleanField(default=True, verbose_name="aktualna"),
                ),
                (
                    "czlowiek",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="wynajmy.Czlowiek",
                    ),
                ),
                (
                    "todo_user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.SET_NULL,
                        verbose_name="komu przypomieć",
                        blank=True,
                        to=settings.AUTH_USER_MODEL,
                        null=True,
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "notatki",
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="Oferta",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("data", models.DateField()),
                (
                    "stawka",
                    models.DecimalField(
                        null=True, max_digits=10, decimal_places=2, blank=True
                    ),
                ),
                ("czas", models.CharField(max_length=500, blank=True)),
                ("tresc", models.TextField(verbose_name="treść")),
                (
                    "czlowiek",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        verbose_name="człowiek",
                        to="wynajmy.Czlowiek",
                    ),
                ),
                (
                    "klient",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT, to="wynajmy.Klient"
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "oferty",
            },
            bases=(wynajmy.models.UpdateMonitor, models.Model),
        ),
        migrations.CreateModel(
            name="Rola",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("nazwa", models.CharField(max_length=50)),
            ],
            options={
                "verbose_name_plural": "role",
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="StatusCzlowieka",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("skrot", models.CharField(max_length=3)),
                ("nazwa", models.CharField(max_length=50)),
            ],
            options={
                "ordering": ["id"],
                "verbose_name": "status człowieka",
                "verbose_name_plural": "statusy człowieka",
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="TypDokumentu",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("skrot", models.CharField(max_length=3)),
                ("nazwa", models.CharField(max_length=50)),
            ],
            options={
                "verbose_name": "typ dokumentu",
                "verbose_name_plural": "typy dokumentów",
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="TypStawki",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("skrot", models.CharField(max_length=4)),
                ("nazwa", models.CharField(max_length=50)),
            ],
            options={
                "verbose_name_plural": "typy stawek",
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="Wynajem",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("data_od", models.DateField(verbose_name="od")),
                ("data_do", models.DateField(verbose_name="do")),
                (
                    "stawka_klienta",
                    models.DecimalField(max_digits=10, decimal_places=2),
                ),
                (
                    "stawka_czlowieka",
                    models.DecimalField(max_digits=10, decimal_places=2),
                ),
                ("umowa", models.CharField(max_length=1000, null=True, blank=True)),
                (
                    "czlowiek",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        verbose_name="człowiek",
                        to="wynajmy.Czlowiek",
                    ),
                ),
                (
                    "klient",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT, to="wynajmy.Klient"
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "wynajmy",
            },
            bases=(wynajmy.models.UpdateMonitor, models.Model),
        ),
        migrations.AddField(
            model_name="dokument",
            name="kategoria_dokumentu",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.SET_NULL,
                blank=True,
                to="wynajmy.KategoriaDokumentu",
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="dokument",
            name="typ_dokumentu",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT, to="wynajmy.TypDokumentu"
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="czlowiek",
            name="miasto",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                verbose_name="miasto",
                to="wynajmy.Miasto",
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="czlowiek",
            name="opiekun",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                verbose_name="opiekun",
                to=settings.AUTH_USER_MODEL,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="czlowiek",
            name="role",
            field=models.ManyToManyField(
                to="wynajmy.Rola", db_table="wynajmy_role_czlowieka", blank=True
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="czlowiek",
            name="status",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                verbose_name="status",
                to="wynajmy.StatusCzlowieka",
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name="czlowiek",
            name="typ_stawki",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.SET_NULL,
                verbose_name="typ stawki",
                blank=True,
                to="wynajmy.TypStawki",
                null=True,
            ),
            preserve_default=True,
        ),
    ]
