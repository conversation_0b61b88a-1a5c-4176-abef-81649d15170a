{% extends "admin/change_form.html" %}


{% block after_field_sets %}

{% block filters %}
    <div id="changelist-filter">
        <h2>Filtr</h2>
	    <h3>Filtruj notatki:</h3>
	    <ul>
		    <li>
			{% if notatki_filtr == "wszystko" %} Wszystkie
			{% else %} <a href="?notatki=wszystko">Wszystkie</a>
			{% endif %}
		    </li>
		    <li>
			{% if notatki_filtr == "aktualne" %} Aktualne
			{% else %} <a href="?notatki=aktualne">Aktualne</a>
			{% endif %}
		    </li>
		    <li>
			{% if notatki_filtr == "todo" %} Aktywne TODO
			{% else %} <a href="?notatki=todo">Aktywne TODO</a>
			{% endif %}
		    </li>

	    </ul>
    </div>
{% endblock %}

{% endblock %}


{% block inline_field_sets %}
{% for inline_admin_formset in inline_admin_formsets %}
    {% include inline_admin_formset.opts.template %}


<script type="text/javascript">
 

    (function($) {
	$(document).ready(function($) {
	    //move add-button to the top of the inlines table
	    var table = "#{{ inline_admin_formset.formset.prefix }}-group .tabular.inline-related tbody";
	    var addButton = $(table).find("tr:last");
	    var tbody = $(addButton).parent();
	    var addButtonRow = addButton.clone(true);
	    $(addButton).remove();
	    $(tbody).prepend($(addButtonRow));

	    // on click move the empty row to the top of the inlines table
	    $(addButtonRow).find("a").click( function() {
		var last_row = $(table).find("tr.dynamic-{{inline_admin_formset.formset.prefix}}:last");
		if(! $(last_row).is(".has_original")){
		    var empty_row = $(last_row).clone(true);
		    $(last_row).remove();
		    $(empty_row).insertAfter($(addButtonRow));

		    //re-bind Date inputs to calendars
		    var date_fields = $(empty_row).find("input.vDateField");
		    var calendars_needed = date_fields.length;
		    var calendars_checklist = [];

		    for( var df=0; df< date_fields.length; df++){
			calendars_checklist[df] = 1;
		    }

		    for (var i=DateTimeShortcuts.calendarInputs.length -1; i>=0; i--)
		    {
			for( var df=0; df< date_fields.length; df++){
			    if( calendars_checklist[df] && (DateTimeShortcuts.calendarInputs[i].id == ($(date_fields)[df].id)) ){
				calendars_checklist[df] = 0;
				calendars_needed -= 1;
				DateTimeShortcuts.calendarInputs[i] = $(date_fields)[df];
			    }
			    if(calendars_needed == 0){
				break;
			    }
			}
		    }

		    // and bring the alternating rows styles back
		    var rows = "#{{ inline_admin_formset.formset.prefix }}-group .tabular.inline-related tbody tr";
		    $(rows).not(".add-row").removeClass("row1 row2");
		    $(rows).not(".add-row").filter(":even").addClass("row1");
		    $(rows).not(".add-row").filter(":odd").addClass("row2");

		}

	    });
	    
	});
    })(django.jQuery);

    </script>
   
{% endfor %}
{% endblock %}
