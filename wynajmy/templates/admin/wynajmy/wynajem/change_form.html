{% extends "admin/change_form.html" %}

{% block content %}


<script type="text/javascript"> 
(function($) {
    $(function(){
        $("#id_czlowiek").change(
            function ()
            {
                var czlowiek = $("#id_czlowiek").val();
                var url = "{% url 'admin:wynajmy_wynajem_add' %}";
 		$.ajaxSetup({ data: {csrfmiddlewaretoken: '{{ csrf_token }}' },});
                $.post(url, {"czlowiek":czlowiek},
                    function(result)
                    {
                        $("#id_stawka_czlowieka").val(result);
                    });
            }

        );

    });
})(django.jQuery);
</script>


{{ block.super }}



{% endblock content %}
