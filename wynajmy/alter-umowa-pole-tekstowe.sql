alter table wynajmy_wynajem add umowa varchar(1000);
update wynajmy_wynajem set umowa = ( select link from wynajmy_dokument where id=umowa_id);
delete from wynajmy_dokument where id in (select umowa_id from wynajmy_wynajem);
alter table wynajmy_wynajem drop umowa_id;
alter table wynajmy_dokument add constraint wynajmy_dokument_typ_dokumentu_id_fkey FOREIGN KEY(typ_dokumentu_id) references wynajmy_typ_dokumentu(id) DEFERRABLE INITIALLY DEFERRED;
