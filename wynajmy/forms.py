from django import forms
from django.forms import ModelForm
from django.utils.safestring import mark_safe

from .models import Notatka, Wynajem

# class DodajDokumentAdminForm(ModelForm):
#    def __init__(self, *args, **kwargs):
#        super(DodajDokumentAdminForm, self).__init__(*args,**kwargs);
#        self.fields['kategoria_dokumentu'].required = False
#
#    class Meta:
#        model = Dokument


class WynajemAdminForm(ModelForm):
    # def __init__(self, *args, **kwargs):
    #    super(WynajemAdminForm, self).__init__(*args, **kwargs)
    #    self.fields['umowa'].required = False

    class Meta:
        model = Wynajem
        fields = (
            "czlowiek",
            "klient",
            "data_od",
            "data_do",
            "stawka_klienta",
            "stawka_czlowieka",
            "umowa",
        )

    #    class Media:
    #        css = {"all":("http://ajax.googleapis.com/ajax/libs/jqueryui/1.8.6/themes/redmond/jquery-ui.css",)}
    #        js = ("http://ajax.googleapis.com/ajax/libs/jquery/1.4.3/jquery.min.js",
    #              "http://ajax.googleapis.com/ajax/libs/jqueryui/1.8.6/jquery-ui.min.js",)

    def clean(self):
        "sprawdza czy data_do >= data_od"
        cleaned_data = self.cleaned_data
        czlowiek = cleaned_data["czlowiek"]
        klient = cleaned_data["klient"]

        if "data_od" in cleaned_data and "data_do" in cleaned_data:
            data_od = cleaned_data["data_od"]
            data_do = cleaned_data["data_do"]
            if data_do < data_od:
                raise forms.ValidationError(
                    "Data końca wynajmu nie może być wcześniejsza niż data początku wynajmu."
                )
            kolidujace = []
            kolidujace = self.kolidujace_wynajmy(czlowiek, data_od, data_do)
            if len(kolidujace) > 0:
                text = "<b>Nowy wynajem pracownika {0} klientowi {1} od {2} do {3} koliduje z następującymi wynajmami:</b>".format(
                    czlowiek, klient, data_od, data_do
                )
                text += "<ul>"
                for k in kolidujace:
                    print(k)
                    text += "<li>Wynajem klientowi {0} od {1} do {2}</li>".format(
                        k.klient, k.data_od, k.data_do
                    )
                text += "</ul>"
                raise forms.ValidationError(mark_safe(text))

        if "stawka_czlowieka" in cleaned_data:
            if cleaned_data["stawka_czlowieka"] < 0:
                raise forms.ValidationError("Stawka człowieka nie może być ujemna.")

        if "stawka_klienta" in cleaned_data:
            if cleaned_data["stawka_klienta"] < 0:
                raise forms.ValidationError("Stawka klienta nie może być ujemna.")

            if "stawka_czlowieka" in cleaned_data:
                if cleaned_data["stawka_czlowieka"] > cleaned_data["stawka_klienta"]:
                    raise forms.ValidationError(
                        "Stawka klienta nie może być mniejsza niż stawka człowieka."
                    )

        return cleaned_data

    def kolidujace_wynajmy(self, czlowiek, data_od, data_do):
        # czy wynajem nie pokrywa sie w czasie z innymi wynajmami
        wynajem = self.instance
        kolidujace = []
        wynajmy = Wynajem.objects.filter(czlowiek=czlowiek)

        if wynajem.id:
            wynajmy = wynajmy.exclude(id=wynajem.id)
        for w in wynajmy:
            if data_od >= w.data_od and data_od <= w.data_do:
                # zaczyna sie w trakcie trwania innego wynajmu
                kolidujace.append(w)
            elif data_od < w.data_od and data_do >= w.data_od:
                # inny wynajem zaczyna sie w trakcie trwania nowego wynajmu
                kolidujace.append(w)

        return kolidujace


class NotatkaAdminForm(ModelForm):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # self.fields['todo_user'].required = False
        # self.fields['todo_data'].required = False

    class Meta:
        model = Notatka
        fields = (
            "data",
            "tekst",
            "todo_data",
            "todo_user",
            "todo_zrobione",
            "aktualna",
        )

    def clean_tekst(self):
        "nie daje się oszukać samymi spacjami"
        data = self.cleaned_data["tekst"]
        if not data.strip():
            raise forms.ValidationError(
                "Pole nie może zawierać wyłącznie białych znaków."
            )
        return data

    def clean(self):  # walidacja calego formularza
        "sprawdza, że todo_user i todo_data są jednocześnie wypełnione lub jednocześnie puste."
        cleaned_data = self.cleaned_data

        if (
            "todo_user" in cleaned_data
            and "todo_data" in cleaned_data
            and "todo_zrobione" in cleaned_data
        ):  # jesli przeszly przez walidacje do tej pory
            # user = cleaned_data['todo_user']
            data = cleaned_data["todo_data"]
            zrobione = cleaned_data["todo_zrobione"]
            if not zrobione and not data:
                raise forms.ValidationError("Niepełne dane o TODO.")

        return cleaned_data
