import datetime

from django.contrib.auth.models import User
from django.db import models
from django.db.models.signals import post_delete, post_save
from django.forms.models import model_to_dict

#
# UPDATE MONITOR                                #
#
from django.utils.safestring import mark_safe


class UpdateMonitor(object):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._reset_state()

    def _reset_state(self):
        self._original_state = model_to_dict(self)

    def get_changed_fields(self):
        old_state = self._original_state
        new_state = model_to_dict(self)
        d = {}
        for k in old_state:
            if old_state[k] != new_state[k]:
                new_val = new_state[k]
                old_val = old_state[k]
                field = self._meta.get_field_by_name(k)[0]
                if isinstance(field, models.ForeignKey):
                    new_val = field.rel.to.objects.get(id=new_state[k])
                    if old_val:
                        old_val = field.rel.to.objects.get(id=old_state[k])
                d[k] = (old_val, new_val)

        return d


#
# SŁOWNIKI                                      #
#


class Miasto(models.Model):
    nazwa = models.CharField(max_length=40)

    class Meta:
        verbose_name_plural = "miasta"
        ordering = ["nazwa"]

    def __str__(self):
        return self.nazwa


#


class StatusCzlowieka(models.Model):
    skrot = models.CharField(max_length=3)
    nazwa = models.CharField(max_length=50)

    class Meta:
        verbose_name_plural = "statusy człowieka"
        verbose_name = "status człowieka"
        ordering = ["id"]

    def __str__(self):
        return self.nazwa


#
#


class TypStawki(models.Model):
    skrot = models.CharField(max_length=4)
    nazwa = models.CharField(max_length=50)

    class Meta:
        verbose_name_plural = "typy stawek"

    def __str__(self):
        return self.skrot


#


class Rola(models.Model):
    nazwa = models.CharField(max_length=50)

    class Meta:
        verbose_name_plural = "role"

    def __str__(self):
        return self.nazwa


#
# CZLOWIEK                                      #
#
class Czlowiek(UpdateMonitor, models.Model):

    imie = models.CharField(max_length=40, verbose_name="imię")
    nazwisko = models.CharField(max_length=40)
    opiekun = models.ForeignKey(User, verbose_name="opiekun", on_delete=models.PROTECT)
    opis = models.CharField(max_length=500, verbose_name="krótki opis")
    role = models.ManyToManyField(Rola, db_table="wynajmy_role_czlowieka", blank=True)
    miasto = models.ForeignKey(Miasto, verbose_name="miasto", on_delete=models.PROTECT)
    status = models.ForeignKey(
        StatusCzlowieka, verbose_name="status", on_delete=models.PROTECT
    )
    typ_stawki = models.ForeignKey(
        TypStawki,
        verbose_name="typ stawki",
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
    )

    # STATUS_CHOICES = (
    #    ('NK', 'Nowy kontakt'),
    #    ('NEG', 'Sprawdzony negatywnie'),
    #    ('UTR', 'Sprawdzony pozytywnie, trwale utracony'),
    #    ('TMP', 'Tymczasowo niechciany lub niedostępny'),
    #    ('ALX', 'Sprawdzony pozytywnie, zajęty przez ALX'),
    #    ('WOL', 'Sprawdzony pozytywnie, wolny'),
    #    ('WYN', 'Sprawdzony pozytywnie, wynajęty'),
    # )

    # TYP_STAWKI_CHOICES = (
    #    ('UD50', 'UD 50 netto'),
    #    ('UD20', 'UD 20 netto'),
    #    ('UP', 'UP netto'),
    #    ('FV', 'FV netto'),
    # )

    stawka = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    proponowana_dla_klienta = models.DecimalField(
        max_digits=10, decimal_places=2, blank=True, null=True
    )

    kompetencje = models.TextField(null=True)

    class Meta:
        verbose_name_plural = "ludzie"
        ordering = ["nazwisko", "imie"]

    def __str__(self):
        return self.imie + " " + self.nazwisko

    def imie_nazwisko(self):
        return self.imie + " " + self.nazwisko

    imie_nazwisko.admin_order_field = "nazwisko"

    # metody potrzebne do wyświetlenia na list_display "ludzie"
    @mark_safe
    def rola_czlowieka(self):
        return "<br /> ".join([r.nazwa for r in self.role.all()])

    rola_czlowieka.short_description = "Rola"

    @mark_safe
    def data_todo_czlowieka(self):
        datatodo = ""
        today = datetime.date.today()
        notatki_sorted = self.notatka_set.all().order_by("todo_data")
        for n in notatki_sorted:
            if (
                n.todo_data is not None
                and n.aktualna is True
                and n.todo_zrobione is False
                and not datatodo
            ):
                d = str(n.todo_data)
                if n.todo_data <= today:
                    d = '<span style="color:red;">' + d + "</span>"
                datatodo = d
        if not datatodo:
            datatodo = ""
        return datatodo

    data_todo_czlowieka.short_description = "data todo"


def notatki_po_edycji_czlowieka(sender, **kwargs):
    czl = kwargs["instance"]
    changed = czl.get_changed_fields()
    if "id" in changed:
        s = changed["kompetencje"][1]
        if not s.strip():
            s = "Dodano do bazy."
        data = datetime.date.today()
        n = Notatka(czlowiek=czl, data=data, tekst=s.strip())
        n.save()
    else:
        txt = []
        for f in list(changed.items()):
            s = "Zmieniono " + str(f[0]) + " na: " + str(f[1][1]) + "."
            txt.append(s)
        txt = "\n".join(txt)
        if txt != "":
            data = datetime.date.today()
            n = Notatka(czlowiek=czl, data=data, tekst=txt)
            n.save()
    czl._reset_state()


post_save.connect(notatki_po_edycji_czlowieka, sender=Czlowiek)


def notatki_po_edycji_rol(sender, instance, pk_set, action, **kwargs):
    zmiany = {
        "post_add": "Dodano role: ",
        "post_remove": "Usunięto role: ",
        "pre_clear": "Usunięto wszystkie role.",
    }
    if action in zmiany:
        s = zmiany[action]
        if pk_set:
            role_set = Rola.objects.filter(id__in=pk_set)
            role = ", ".join([str(r.nazwa) for r in role_set])

            s += role + "."

        czl = instance
        data = datetime.date.today()
        n = Notatka(czlowiek=czl, data=data, tekst=s.strip())
        n.save()


# m2m_changed.connect(notatki_po_edycji_rol, sender=Czlowiek.role.through)

#
# DOKUMENT                                      #
#


class TypDokumentu(models.Model):
    skrot = models.CharField(max_length=3)
    nazwa = models.CharField(max_length=50)

    class Meta:
        verbose_name_plural = "typy dokumentów"
        verbose_name = "typ dokumentu"

    def __str__(self):
        return self.nazwa


class KategoriaDokumentu(
    models.Model
):  # po angielsku/po polsku, różne wyróżniki dokumentów
    skrot = models.CharField(max_length=3)
    nazwa = models.CharField(max_length=50)

    class Meta:
        verbose_name_plural = "kategorie dokumentów"
        verbose_name = "kategoria dokumentu"

    def __str__(self):
        return self.nazwa


class Dokument(UpdateMonitor, models.Model):

    typ_dokumentu = models.ForeignKey(TypDokumentu, on_delete=models.PROTECT)
    kategoria_dokumentu = models.ForeignKey(
        KategoriaDokumentu, blank=True, null=True, on_delete=models.SET_NULL
    )

    czlowiek = models.ForeignKey(Czlowiek, on_delete=models.PROTECT)
    link = models.URLField(unique=True)

    class Meta:
        verbose_name_plural = "dokumenty"

    def __str__(self):
        return self.link

    @mark_safe
    def dokument_link(self):
        return '<a href="{0}">{1}</a>'.format(self.link, self.link)

    dokument_link.short_description = "link do dokumentu"


def notatki_po_dodaniu_dokumentu(sender, **kwargs):
    dok = kwargs["instance"]
    changed = dok.get_changed_fields()
    print(changed)
    if "id" in changed:
        s = "Dodano " + str(changed["typ_dokumentu"][1].nazwa)
        if "kategoria_dokumentu" in changed:
            s += " " + str(changed["kategoria_dokumentu"][1].nazwa)
        s += ": " + str(changed["link"][1])
        data = datetime.date.today()
        czl = dok.czlowiek
        n = Notatka(czlowiek=czl, data=data, tekst=s.strip())
        n.save()
    dok._reset_state()


post_save.connect(notatki_po_dodaniu_dokumentu, sender=Dokument)


def notatki_po_usunieciu_dokumentu(sender, **kwargs):
    dok = kwargs["instance"]
    s = "Usunięto " + str(dok.typ_dokumentu) + ": "
    s += str(dok.link)
    data = datetime.date.today()
    czl = dok.czlowiek
    n = Notatka(czlowiek=czl, data=data, tekst=s.strip())
    n.save()
    dok._reset_state()


post_delete.connect(notatki_po_usunieciu_dokumentu, sender=Dokument)


#
# KLIENT                                        #
#
class Klient(models.Model):

    nazwa = models.CharField(max_length=100, unique=True)
    notatki = models.TextField(blank=True)

    class Meta:
        verbose_name_plural = "klienci"

    def __str__(self):
        return self.nazwa


#
# NOTATKA                                       #
#


class Notatka(models.Model):

    czlowiek = models.ForeignKey(Czlowiek, on_delete=models.PROTECT)
    data = models.DateField(verbose_name="data")
    tekst = models.TextField(verbose_name="tekst/akcja TODO")
    # teraz funkcjonalność todo
    todo_data = models.DateField(null=True, blank=True, verbose_name="kiedy")
    todo_user = models.ForeignKey(
        User,
        null=True,
        blank=True,
        verbose_name="komu przypomieć",
        on_delete=models.SET_NULL,
    )
    todo_zrobione = models.BooleanField(default=True, verbose_name="czy zrobione")
    aktualna = models.BooleanField(default=True, verbose_name="aktualna")

    class Meta:
        verbose_name_plural = "notatki"

    def __str__(self):
        # return "Notatka dot. " + self.czlowiek + " ("+ self.data+")"
        return self.czlowiek.imie_nazwisko()


#
# WYNAJEM                                       #
#


class Wynajem(UpdateMonitor, models.Model):

    czlowiek = models.ForeignKey(
        Czlowiek, verbose_name="człowiek", on_delete=models.PROTECT
    )
    klient = models.ForeignKey(Klient, on_delete=models.PROTECT)
    data_od = models.DateField(verbose_name="od")
    data_do = models.DateField(verbose_name="do")
    stawka_klienta = models.DecimalField(max_digits=10, decimal_places=2)
    stawka_czlowieka = models.DecimalField(max_digits=10, decimal_places=2)
    umowa = models.CharField(max_length=1000, null=True, blank=True)

    class Meta:
        verbose_name_plural = "wynajmy"


def notatki_po_edycji_wynajmu(sender, **kwargs):
    wynajem = kwargs["instance"]
    changed = wynajem.get_changed_fields()
    data = datetime.date.today()
    czl = wynajem.czlowiek

    if "id" in changed:
        s = (
            "Dodano wynajem od "
            + str(wynajem.data_od)
            + " do "
            + str(wynajem.data_do)
            + " dla "
            + str(wynajem.klient)
            + "."
        )
        n = Notatka(czlowiek=czl, data=data, tekst=s.strip())
        n.save()
    else:
        for f in list(changed.items()):
            s = (
                "W wynajmie dla "
                + str(wynajem.klient)
                + " zmieniono "
                + str(f[0])
                + " z "
                + str(f[1][0])
                + " na: "
                + str(f[1][1])
            )
            n = Notatka(czlowiek=czl, data=data, tekst=s)
            n.save()

    wynajem._reset_state()


post_save.connect(notatki_po_edycji_wynajmu, sender=Wynajem)


def kolidujace_wynajmy(nowy_wynajem):
    # czy wynajem nie pokrywa sie w czasie z innymi wynajmami
    zawierajace = []
    nachodzace = []
    wynajmy = Wynajem.objects.filter(czlowiek=nowy_wynajem.czlowiek)
    print(nowy_wynajem.id)
    if nowy_wynajem.id:
        print("nowy wynajem id!")
        wynajmy = wynajmy.exclude(id=nowy_wynajem.id)
    for w in wynajmy:
        # tu podział na nachodzace i zawierajace jest mocno na wyrost...
        if nowy_wynajem.data_od >= w.data_od and nowy_wynajem.data_od <= w.data_do:
            # zaczyna sie w trakcie trwania innego wynajmu
            if nowy_wynajem.data_do > w.data_do:
                # i konczy sie po jego zakonczeniu
                nachodzace.append(w)
            else:
                # i konczy sie przed jego zakonczeniem
                zawierajace.append(w)
        elif nowy_wynajem.data_od < w.data_od and nowy_wynajem.data_do >= w.data_od:
            # inny wynajem zaczyna sie w trakcie trwania nowego wynajmu
            if w.data_do <= nowy_wynajem.data_do:
                # a kończy się przed jego zakonczeniem
                zawierajace.append(w)
            else:
                # i kończy się po jego zakonczeniu
                nachodzace.append(w)
    # juz sprawdzone wszystkie istniejace wynajmy

    kolidujace = zawierajace
    kolidujace.extend(nachodzace)

    return kolidujace


#
# OFERTA                                        #
#


class Oferta(UpdateMonitor, models.Model):

    czlowiek = models.ForeignKey(
        Czlowiek, verbose_name="człowiek", on_delete=models.PROTECT
    )
    klient = models.ForeignKey(Klient, on_delete=models.PROTECT)
    data = models.DateField()
    stawka = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    czas = models.CharField(max_length=500, blank=True)
    tresc = models.TextField(verbose_name="treść")

    class Meta:
        verbose_name_plural = "oferty"
