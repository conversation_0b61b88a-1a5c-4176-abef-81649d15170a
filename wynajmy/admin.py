import datetime

from django.contrib import admin, messages
from django.contrib.auth.models import User
from django.shortcuts import HttpResponse
from django.utils.safestring import mark_safe

from .filters import (
    AktywneTodoFilter,
    AktywneWynajmyFilter,
    OpiekunPrzypisany,
    WyzszyStatusListFilter,
)
from .forms import NotatkaAdminForm, WynajemAdminForm
from .models import (
    Czlowiek,
    Dokument,
    KategoriaDokumentu,
    Klient,
    Miasto,
    Notatka,
    Oferta,
    Rola,
    StatusCzlowieka,
    TypDokumentu,
    TypStawki,
    Wynajem,
)

# admin.site.disable_action('delete_selected')


class KlientAdmin(admin.ModelAdmin):
    pass


class WynajemAdmin(admin.ModelAdmin):

    list_display = (
        "klient",
        "czlowiek_link",
        "data_od",
        "data_do",
        "stawka_klienta",
        "stawka_czlowieka",
        "umowa_link",
        "przedluz",
        "klonuj",
    )
    list_display_links = ("klient",)
    form = WynajemAdminForm
    list_filter = (AktywneWynajmyFilter, "klient", "czlowiek")

    def add_view(self, request, form_url="", extra_context=None):

        if request.is_ajax():
            czlowiek_id = request.POST.get("czlowiek")
            stawka = Czlowiek.objects.get(id=czlowiek_id).stawka
            return HttpResponse(stawka)

        return super().add_view(request, form_url, extra_context)

    @mark_safe
    def czlowiek_link(self, obj):
        return '<a href="/admin/wynajmy/czlowiek/%s/">%s</a>' % (
            obj.czlowiek.id,
            obj.czlowiek.imie_nazwisko(),
        )

    czlowiek_link.short_description = "człowiek"

    @mark_safe
    def umowa_link(self, obj):
        if obj.umowa:
            return "<a href=%s>%s</a>" % (obj.umowa, obj.umowa)
        else:
            return "(Brak)"

    umowa_link.short_description = "umowa"

    @mark_safe
    def przedluz(self, obj):
        url = "/admin/wynajmy/wynajem/add/"
        url += "?"
        url += "czlowiek=" + str(obj.czlowiek.id)
        url += "&klient=" + str(obj.klient.id)
        url += "&stawka_czlowieka=" + str(obj.stawka_czlowieka)
        url += "&stawka_klienta=" + str(obj.stawka_klienta)
        url += "&data_od=" + str(obj.data_do + datetime.timedelta(days=1))

        if obj.umowa:
            url += "&umowa=" + str(obj.umowa)
        return "<a href=%s> Przedłuż </a>" % (url,)

    przedluz.short_description = "przedłuż"

    @mark_safe
    def klonuj(self, obj):
        url = "/admin/wynajmy/wynajem/add/"
        url += "?"
        url += "czlowiek=" + str(obj.czlowiek.id)
        url += "&klient=" + str(obj.klient.id)
        url += "&stawka_czlowieka=" + str(obj.stawka_czlowieka)
        url += "&stawka_klienta=" + str(obj.stawka_klienta)

        if obj.umowa:
            url += "&umowa=" + str(obj.umowa)
        return "<a href=%s> Klonuj </a>" % (url,)

    klonuj.short_description = "klonuj"


class DokumentAdmin(admin.ModelAdmin):
    list_display = ("link", "dokument_link")

    # form = DodajDokumentAdminForm
    pass


class MiastoAdmin(admin.ModelAdmin):
    pass


class StatusCzlowiekaAdmin(admin.ModelAdmin):
    list_display = ("nazwa", "skrot")
    ordering = ["id"]


class TypStawkiAdmin(admin.ModelAdmin):
    list_display = ("nazwa", "skrot")


class TypDokumentuAdmin(admin.ModelAdmin):
    list_display = ("nazwa", "skrot")


class KategoriaDokumentuAdmin(admin.ModelAdmin):
    list_display = ("nazwa", "skrot")


class RolaAdmin(admin.ModelAdmin):
    list_display = ("nazwa",)


class lista_uprawnionych:
    indeksy = []

    def __init__(self):
        i = []
        for u in User.objects.all():
            l = u.id
            if u.has_module_perms("wynajmy"):
                i.append(l)
        self.indeksy = i


class NotatkaInline(admin.TabularInline):
    model = Notatka
    extra = 0
    ordering = ("-aktualna", "-data", "-id")
    form = NotatkaAdminForm
    list_filter = "aktualna"
    lista_uprawnionych = None

    def lazy_query(self):
        if self.lista_uprawnionych is None:
            self.lista_uprawnionych = lista_uprawnionych()
        return self.lista_uprawnionych.indeksy

    def queryset(self, request):
        queryset = super().queryset(request)
        notatki_filtr = request.GET.get("notatki", "aktualne")
        if notatki_filtr == "aktualne":
            return queryset.filter(aktualna=True)
        elif notatki_filtr == "todo":
            return queryset.filter(todo_zrobione=False)
        return queryset

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        if db_field.name == "todo_user" and db_field.null:
            kwargs["initial"] = request.user.id
            kwargs["queryset"] = User.objects.filter(pk__in=self.lazy_query()).order_by(
                "username"
            )
            return super().formfield_for_foreignkey(db_field, request, **kwargs)
        return


class DokumentInline(admin.TabularInline):
    model = Dokument
    extra = 0
    # readonly_fields=('dokument_link',)
    # form = DodajDokumentAdminForm


class CzlowiekAdmin(admin.ModelAdmin):
    list_display = (
        "imie_nazwisko",
        "data_todo_czlowieka",
        "rola_czlowieka",
        "opiekun",
        "opis",
        "miasto",
        "status",
        "typ_stawki",
        "stawka",
        "proponowana_dla_klienta",
    )
    inlines = [
        NotatkaInline,
        DokumentInline,
    ]
    list_filter = (
        AktywneTodoFilter,
        "miasto",
        "role",
        WyzszyStatusListFilter,
        OpiekunPrzypisany,
    )
    filter_horizontal = ("role",)

    save_on_top = True
    save_as = True

    lista_uprawnionych = None

    def lazy_query(self):
        if self.lista_uprawnionych is None:
            self.lista_uprawnionych = lista_uprawnionych()
        return self.lista_uprawnionych

    def change_view(self, request, id):
        czlowiek = Czlowiek.objects.get(id=id)
        title = czlowiek.imie + " " + czlowiek.nazwisko
        notatki_filtr = request.GET.get("notatki", "aktualne")
        extra_context = {"title": title, "notatki_filtr": notatki_filtr}
        i = self.lazy_query().indeksy

        def formfield_for_foreignkey(self, db_field, request, **kwargs):
            if db_field.name == "todo_user":
                kwargs["initial"] = Czlowiek.objects.get(id=id).opiekun
                kwargs["queryset"] = User.objects.filter(pk__in=i).order_by("username")
            return super().formfield_for_foreignkey(db_field, request, **kwargs)

        NotatkaInline.formfield_for_foreignkey = formfield_for_foreignkey
        self.inline_instances = [
            NotatkaInline(self.model, self.admin_site),
            DokumentInline(self.model, self.admin_site),
        ]
        return super().change_view(request, id, extra_context=extra_context)

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        if db_field.name == "opiekun":
            kwargs["initial"] = request.user.id
            i = self.lazy_query().indeksy
            kwargs["queryset"] = User.objects.filter(pk__in=i).order_by("username")
            return db_field.formfield(**kwargs)
        return super().formfield_for_foreignkey(db_field, request, **kwargs)

    def save_model(self, request, obj, form, change):
        if change:
            changed = obj.get_changed_fields()
            if "stawka" in changed:
                messages.warning(
                    request,
                    "Zmiana stawki {0} może mieć wpływ na obecne i przyszłe wynajmy!".format(
                        obj.imie_nazwisko()
                    ),
                )
        super().save_model(request, obj, form, change)


class OfertaAdmin(admin.ModelAdmin):

    list_display = ("klient", "czlowiek", "data", "stawka", "czas")
    list_filter = ("klient", "czlowiek")


admin.site.register(Dokument, DokumentAdmin)
admin.site.register(Czlowiek, CzlowiekAdmin)
admin.site.register(Wynajem, WynajemAdmin)
admin.site.register(Klient, KlientAdmin)
admin.site.register(Miasto, MiastoAdmin)
admin.site.register(Rola, RolaAdmin)
admin.site.register(StatusCzlowieka, StatusCzlowiekaAdmin)
admin.site.register(TypStawki, TypStawkiAdmin)
admin.site.register(TypDokumentu, TypDokumentuAdmin)
admin.site.register(KategoriaDokumentu, KategoriaDokumentuAdmin)
admin.site.register(Oferta, OfertaAdmin)
