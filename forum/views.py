from django.conf import settings
from django.core.mail import send_mail
from django.core.paginator import InvalidPage, Paginator
from django.http import Http404, HttpResponseRedirect
from django.shortcuts import get_object_or_404, render
from django.template.loader import render_to_string
from django.urls import reverse
from django.views.decorators.cache import never_cache
from ipware import get_client_ip

from i18n.models import WersjaJezykowa

from .forms import NowyPostForm, NowyWatekForm
from .models import Forum, Post, Watek


def email_notify(post):
    powiadomienie = render_to_string(
        "forum/powiadomienie_email.txt",
        {"post": post, "prefix": "http://" + WersjaJezykowa.biezaca().domain},
    )
    send_mail(
        "[AL] Nowy post na forum",
        powiadomienie,
        settings.MAIL_FROM_ADDRESS,
        [settings.MAIL_FORUM_TO_ADDRESS],
    )


@never_cache
def index(request):
    fora = Forum.objects.filter(active=True)
    return render(
        request,
        "forum/index.html",
        {
            "fora": fora,
        },
    )


@never_cache
def forum(request, slug, page=1):
    forum = get_object_or_404(Forum, slug=slug)
    if forum.redirect_url:
        return HttpResponseRedirect(forum.redirect_url)
    watki_all = Watek.objects.filter(forum=forum)
    pages = Paginator(watki_all, 10)
    try:
        watki = pages.page(page)
    except InvalidPage:
        raise Http404
    return render(
        request,
        "forum/forum.html",
        {
            "forum": forum,
            "watki": watki,
            "pages": pages,
            "page": int(page),
        },
    )


@never_cache
def watek(request, slug, id, page=None):
    forum = get_object_or_404(Forum, slug=slug)
    if forum.redirect_url:
        return HttpResponseRedirect(forum.redirect_url)
    watek = get_object_or_404(Watek, forum=forum, id=id)
    posty_all = Post.objects.filter(watek=watek).order_by("czas")
    pages = Paginator(posty_all, 10)
    if page is None:
        page = pages.num_pages
    try:
        posty = pages.page(page)
    except InvalidPage:
        raise Http404
    form = NowyPostForm()
    return render(
        request,
        "forum/watek.html",
        {
            "forum": forum,
            "watek": watek,
            "posty": posty,
            "pages": pages,
            "page": int(page),
            "form": form,
        },
    )


@never_cache
def nowy_watek(request, slug):
    forum = get_object_or_404(Forum, slug=slug)
    if request.method == "POST":
        form = NowyWatekForm(request.POST)
        if form.is_valid():
            d = form.cleaned_data
            watek = Watek(forum=forum, temat=d["temat"])
            watek.save()
            post = Post(
                watek=watek,
                tresc=d["tresc"],
                autor=d["autor"],
                ip=get_client_ip(request)[0],
            )
            post.save()
            watek.ostatni_post = post.czas
            watek.save()
            email_notify(post)
            return HttpResponseRedirect(
                reverse("forum_watek", args=[forum.slug, watek.id, 1])
            )
    else:
        form = NowyWatekForm()
    return render(
        request,
        "forum/nowy_watek.html",
        {
            "forum": forum,
            "form": form,
        },
    )


@never_cache
def nowy_post(request, slug, id):
    forum = get_object_or_404(Forum, slug=slug)
    watek = get_object_or_404(Watek, forum=forum, id=id)
    if request.method == "POST":
        form = NowyPostForm(request.POST)
        if form.is_valid():
            d = form.cleaned_data
            post = Post(
                watek=watek,
                tresc=d["tresc"],
                autor=d["autor"],
                ip=get_client_ip(request)[0],
            )
            post.save()
            watek.ostatni_post = post.czas
            watek.save()
            email_notify(post)
            return HttpResponseRedirect(
                reverse("watek_lastpost", args=[forum.slug, watek.id])
            )
    else:
        form = NowyPostForm()
    return render(
        request,
        "forum/nowy_post.html",
        {
            "forum": forum,
            "watek": watek,
            "form": form,
        },
    )
