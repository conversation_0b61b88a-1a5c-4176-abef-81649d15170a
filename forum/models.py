from django.db import models


class Forum(models.Model):
    slug = models.CharField(unique=True, max_length=40)
    nazwa = models.CharField(max_length=200)
    opis = models.TextField()
    ordering = models.IntegerField()
    active = models.BooleanField(default=True)
    redirect_url = models.URLField(null=True, blank=True)

    def __str__(self):
        return self.nazwa

    class Meta:
        ordering = ["ordering"]


class Watek(models.Model):
    forum = models.ForeignKey(Forum, on_delete=models.PROTECT)
    temat = models.CharField(max_length=200)
    ostatni_post = models.DateTimeField(blank=True, null=True)

    def __str__(self):
        return "%s (%s)" % (self.temat, self.forum.__str__())

    class Meta:
        ordering = ["-ostatni_post"]


class Post(models.Model):
    watek = models.ForeignKey(Watek, on_delete=models.CASCADE)
    tresc = models.TextField()
    czas = models.DateTimeField(auto_now_add=True)
    autor = models.CharField(max_length=30)
    ip = models.GenericIPAddressField()

    def __str__(self):
        return "%s / %s" % (self.autor, self.watek.temat)

    class Meta:
        ordering = ["-czas"]
