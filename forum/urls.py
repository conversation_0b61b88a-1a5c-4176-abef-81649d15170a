from django.urls import path, re_path
from django.http import HttpResponsePermanentRedirect
from forum import views as forum_views

urlpatterns = [
    path("", forum_views.index),
    re_path("opinie-komentarze-pytania/.*", lambda request: HttpResponsePermanentRedirect('/')),
    re_path(
        r"^(?P<slug>[a-z0-9_-]+)/page(?P<page>\d+)/$",
        forum_views.forum,
        name="forum_page",
    ),
    re_path(r"^(?P<slug>[a-z0-9_-]+)/$", forum_views.forum, name="forum_firstpage"),
    re_path(
        r"^(?P<slug>[a-z0-9_-]+)/x(?P<id>\d+)/page(?P<page>\d+)/$",
        forum_views.watek,
        name="forum_watek",
    ),
    re_path(
        r"^(?P<slug>[a-z0-9_-]+)/x(?P<id>\d+)/$",
        forum_views.watek,
        name="watek_lastpost",
    ),
]
