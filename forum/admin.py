from django.contrib import admin

from .models import Forum, Post, Watek


class PostAdmin(admin.ModelAdmin):
    list_display = ("tresc", "watek", "autor", "czas")
    list_display_links = list_display


admin.site.register(Post, PostAdmin)


class WatekAdmin(admin.ModelAdmin):
    list_display = ("temat", "forum", "ostatni_post")
    list_display_links = list_display


admin.site.register(Watek, WatekAdmin)


class ForumAdmin(admin.ModelAdmin):
    list_display = ("nazwa", "active", "redirect_url")

admin.site.register(Forum, ForumAdmin)
