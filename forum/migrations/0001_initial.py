import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Forum",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("slug", models.CharField(unique=True, max_length=40)),
                ("nazwa", models.CharField(max_length=200)),
                ("opis", models.TextField()),
                ("ordering", models.IntegerField()),
            ],
            options={
                "ordering": ["ordering"],
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="Post",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("tresc", models.TextField()),
                ("czas", models.DateTimeField(auto_now_add=True)),
                ("autor", models.<PERSON><PERSON><PERSON><PERSON>(max_length=30)),
                ("ip", models.IPAddressField()),
            ],
            options={
                "ordering": ["-czas"],
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name="Watek",
            fields=[
                (
                    "id",
                    models.AutoField(
                        verbose_name="ID",
                        serialize=False,
                        auto_created=True,
                        primary_key=True,
                    ),
                ),
                ("temat", models.CharField(max_length=200)),
                ("ostatni_post", models.DateTimeField(null=True, blank=True)),
                (
                    "forum",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT, to="forum.Forum"
                    ),
                ),
            ],
            options={
                "ordering": ["-ostatni_post"],
            },
            bases=(models.Model,),
        ),
        migrations.AddField(
            model_name="post",
            name="watek",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="forum.Watek"
            ),
            preserve_default=True,
        ),
    ]
