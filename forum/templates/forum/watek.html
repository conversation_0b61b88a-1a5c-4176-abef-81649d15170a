{% extends "forum/base.html" %}

{% block title %}{{ watek.temat }} - Opinie, pytania - Forum ALX{% endblock %}

{% block body %}

<p>nawigacja: <a href="/forum/">powrót do listy forów</a> | <a href="{% url 'forum_firstpage' slug=forum.slug %}">{{ forum.nazwa }}</a></p>

<h2><a href="{% url 'forum_firstpage' slug=forum.slug %}">{{ forum.nazwa }}</a></h2>
<h3>{{ watek.temat }}</h3>

{% if posty.has_other_pages %}
<p>
strony:
{% if posty.has_previous %}
<a href="{% url 'forum_watek' slug=forum.slug id=watek.id page=posty.previous_page_number %}">&lt;&lt;</a>
{% endif %}
{% for p in pages.page_range %}
{% if p == page %}
<strong>[{{ p }}]</strong>
{% else %}
<a href="{% url 'forum_watek' slug=forum.slug id=watek.id page=p %}">{{ p }}</a>
{% endif %}
{% endfor %}
{% if posty.has_next %}
<a href="{% url 'forum_watek' slug=forum.slug id=watek.id page=posty.next_page_number %}">&gt;&gt;</a>
{% endif %}
<br />
</p>
{% endif %}


<table>
{% for p in posty.object_list %}
<tr>
<th class="sectiontableheader">{{ p.autor }}</th><td class="tableborderforum">{{ p.czas|date:"Y-m-d H:i" }} ({{ p.czas|timesince }} temu)</td></tr>
<tr><td></td><td><p>{{ p.tresc|urlize|linebreaksbr }}</p></td></tr>
{% endfor %}
</table>

{% if posty.has_other_pages %}
<p>
strony:
{% if posty.has_previous %}
<a href="{% url 'forum_watek' slug=forum.slug id=watek.id page=posty.previous_page_number %}">&lt;&lt;</a>
{% endif %}
{% for p in pages.page_range %}
{% if p == page %}
<strong>[{{ p }}]</strong>
{% else %}
<a href="{% url 'forum_watek' slug=forum.slug id=watek.id page=p %}">{{ p }}</a>
{% endif %}
{% endfor %}
{% if posty.has_next %}
<a href="{% url 'forum_watek' slug=forum.slug id=watek.id page=posty.next_page_number %}">&gt;&gt;</a>
{% endif %}
<br />
</p>
{% endif %}



<p>nawigacja: <a href="/forum/">powrót do listy forów</a> | <a href="{% url 'forum_firstpage' slug=forum.slug %}">{{ forum.nazwa }}</a></p>

{% endblock body %}
