{% extends "forum/base.html" %}

{% block title %}{{ forum.nazwa }} - Opinie, pytania - Forum ALX{% endblock %}

{% block body %}

<h2>{{ forum.nazwa }}</h2>
<p>{{ forum.opis }}</p>



<table class="contentpane" width="100%">
<tr><th class="sectiontableheader">Temat</th>
	<th class="sectiontableheader">Liczba postów</th>
	<th class="sectiontableheader">Autor</th>
	<th class="sectiontableheader">Data</th></tr>
{% for w in watki.object_list %}
<tr class="{% cycle 'sectiontableentry2' 'sectiontableentry1' %}">
<td><a href="{% url 'forum_watek' slug=forum.slug id=w.id page=1 %}">{{ w.temat }}</a></td>
<td>
{{ w.post_set.count }}
</td>
<td>
{% with w.post_set.all.0 as ostatni_post %}
{{ ostatni_post.autor }}</td><td>
{{ ostatni_post.czas|date:"Y-m-d H:i" }}
<a href="{% url 'watek_lastpost' slug=forum.slug id=w.id %}">
<img src="/static/icons/icon_newest_reply.gif" alt="do najnowszych" border="0" />
</a>
{% endwith %}
</td>
</tr>
{% endfor %}
</table>

{% if watki.has_other_pages %}
<p>
strony:
{% if watki.has_previous %}
<a href="{% url 'forum_page' slug=forum.slug page=watki.previous_page_number %}">&lt;&lt;</a>
{% endif %}
{% for p in pages.page_range %}
{% if p == page %}
<strong>[{{ p }}]</strong>
{% else %}
<a href="{% url 'forum_page' slug=forum.slug page=p %}">{{ p }}</a>
{% endif %}
{% endfor %}
{% if watki.has_next %}
<a href="{% url 'forum_page' slug=forum.slug page=watki.next_page_number %}">&gt;&gt;</a>
{% endif %}
</p>
{% endif %}

{% endblock body %}
