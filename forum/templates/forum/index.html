{% extends "forum/base.html" %}

{% block body %}

<h2>Witamy na forum.</h2>

<table class="contentpane">
<tr><th class="sectiontableheader">Forum</th><th class="sectiontableheader">Tematy</th>
	<th class="sectiontableheader" colspan="2">Ostatni post</th></tr>
{% for f in fora %}
<tr class="{% cycle 'sectiontableentry2' 'sectiontableentry1' %}">
<td><a href="{% url 'forum_firstpage' slug=f.slug %}">{{ f.nazwa }}</a><br/>{{ f.opis }}</td>
<td>{{ f.watek_set.count }}</td>
<td>
{% if f.watek_set.all %}
{{ f.watek_set.all.0.post_set.all.0.czas|date:"Y-m-d H:i" }}<br /> {{f.watek_set.all.0.post_set.all.0.autor}}
</td>
<td><a href="{% url 'watek_lastpost' slug=f.slug id=f.watek_set.all.0.id %}">
	<img src="/static/icons/icon_newest_reply.gif" alt="do najnowszych" border="0" /></a></td>
{% endif %}
</tr>
{% endfor %}
</table>

{% endblock body %}
