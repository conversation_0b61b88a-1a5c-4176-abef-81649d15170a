import factory
from pyfaker import Fake

from leads.models import LeadCategory, LeadUser


class LeadCategoryFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = LeadCategory

    slug = factory.Sequence(lambda n: "slug{0}".format(n))


class LeadUserFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = LeadUser

    full_name = factory.Sequence(lambda n: "Nazwa {0}".format(n))
    phone_number = factory.Sequence(lambda n: "{0}".format(n))
    email = factory.Sequence(lambda n: "email@email{0}.com".format(n))
    lead_category = factory.SubFactory(LeadCategoryFactory)
