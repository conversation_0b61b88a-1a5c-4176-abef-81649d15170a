import logging

from django.contrib import admin

from .models import LeadCategory, LeadUser

logger = logging.getLogger(__name__)


class LeadCategoryAdmin(admin.ModelAdmin):
    list_display = (
        # 'title',
        "slug",
        "internal_comments",
        "is_active",
        "language",
        "created_at",
    )
    list_filter = (
        "is_active",
        "language",
        "created_at",
        "updated_at",
    )

    search_fields = (
        "slug",
        # 'title',
        "internal_comments",
    )
    fieldsets = (
        (
            None,
            {
                "fields": (
                    # 'title',
                    "slug",
                    "content",
                    "language",
                    "is_active",
                    "internal_comments",
                )
            },
        ),
    )
    save_on_top = True
    date_hierarchy = "created_at"
    actions = None


admin.site.register(LeadCategory, LeadCategoryAdmin)


class LeadUserAdmin(admin.ModelAdmin):
    list_display = (
        "email",
        "full_name",
        "phone_number",
        "internal_comments",
        "lead_category",
        "finished",
        "created_at",
    )
    search_fields = (
        "full_name",
        "phone_number",
        "email",
        "internal_comments",
    )
    list_filter = (
        "lead_category__slug",
        "finished",
        "created_at",
    )
    fieldsets = (
        (
            None,
            {
                "fields": (
                    "lead_category",
                    "full_name",
                    "phone_number",
                    "email",
                    "internal_comments",
                    "finished",
                    "remote_addr",
                )
            },
        ),
    )
    readonly_fields = ("remote_addr",)
    list_select_related = True
    save_on_top = True
    date_hierarchy = "created_at"
    actions = None

    def has_add_permission(self, request):
        return False

    def has_delete_permission(self, request, obj=None):
        return False


admin.site.register(LeadUser, LeadUserAdmin)
