# Generated by Django 1.11.29 on 2021-02-12 14:51
import uuid

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="LeadCategory",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "title",
                    models.CharField(
                        help_text="Widoczny na stronie.",
                        max_length=250,
                        verbose_name="Tytu\u0142",
                    ),
                ),
                (
                    "slug",
                    models.SlugField(
                        help_text="Unikalna nazwa. Widoczna w URL.",
                        max_length=100,
                        unique=True,
                    ),
                ),
                (
                    "content",
                    models.TextField(
                        blank=True,
                        help_text="Mo\u017ce zawiera\u0107 HTML. Widoczny na stronie.",
                        verbose_name="Opis",
                    ),
                ),
                (
                    "internal_comments",
                    models.TextField(blank=True, verbose_name="uwagi internal"),
                ),
                (
                    "is_active",
                    models.<PERSON><PERSON>an<PERSON>ield(default=True, verbose_name="aktywny?"),
                ),
                (
                    "language",
                    models.CharField(
                        choices=[("pl", "polski"), ("en", "angielski")],
                        default="pl",
                        max_length=2,
                        verbose_name="j\u0119zyk",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True,
                        db_index=True,
                        null=True,
                        verbose_name="utworzono",
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        auto_now=True,
                        db_index=True,
                        null=True,
                        verbose_name="aktualizowano",
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
                "verbose_name": "Kategoria lead",
                "verbose_name_plural": "Kategorie lead",
            },
        ),
        migrations.CreateModel(
            name="LeadUser",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "full_name",
                    models.CharField(
                        max_length=200, verbose_name="imi\u0119 i nazwisko"
                    ),
                ),
                (
                    "phone_number",
                    models.CharField(max_length=150, verbose_name="telefon"),
                ),
                (
                    "email",
                    models.EmailField(max_length=150, verbose_name="adres email"),
                ),
                (
                    "key",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        verbose_name="klucz dost\u0119pu",
                    ),
                ),
                (
                    "finished",
                    models.BooleanField(
                        default=False, verbose_name="obs\u0142uga zako\u0144czona?"
                    ),
                ),
                (
                    "internal_comments",
                    models.TextField(blank=True, verbose_name="uwagi internal"),
                ),
                (
                    "remote_addr",
                    models.GenericIPAddressField(
                        blank=True, editable=False, null=True, verbose_name="adres IP"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True,
                        db_index=True,
                        null=True,
                        verbose_name="utworzono",
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        auto_now=True, null=True, verbose_name="aktualizowano"
                    ),
                ),
                (
                    "verified_at",
                    models.DateTimeField(
                        blank=True,
                        editable=False,
                        null=True,
                        verbose_name="zweryfikowany",
                    ),
                ),
                (
                    "lead_category",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="leads.LeadCategory",
                        verbose_name="kategoria",
                    ),
                ),
            ],
            options={
                "verbose_name": "U\u017cytkownik lead",
                "verbose_name_plural": "U\u017cytkownicy lead",
            },
        ),
        migrations.AlterUniqueTogether(
            name="leaduser",
            unique_together=set([("lead_category", "email")]),
        ),
    ]
