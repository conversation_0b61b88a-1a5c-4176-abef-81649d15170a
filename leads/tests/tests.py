# -*- coding: utf-8 -*-
from __future__ import unicode_literals

import os

from django.core import mail

from leads.models import LeadCategory, LeadUser
from leads.testfactories import LeadCategoryFactory, LeadUserFactory
from www.testhelpers import ALXTestCase


class LeadTestCase(ALXTestCase):
    def setUp(self):
        super(LeadTestCase, self).setUp()
        os.environ["NORECAPTCHA_TESTING"] = "True"
        self.lead_category = LeadCategoryFactory.create()

    def test_inactive_lead_category(self):
        self.lead_category.is_active = False
        self.lead_category.save()

        response = self.client.get(self.lead_category.get_absolute_url(), follow=True)
        self.assertEqual(response.status_code, 404)

    def test_save_user(self):
        LeadUserFactory.create(email="<EMAIL>")

        response = self.client.post(
            self.lead_category.get_absolute_url(),
            {
                "full_name": "nazwa",
                "phone_number": "123",
                "email": "<EMAIL>",
                "g-recaptcha-response": "PASSED",
                "tos": 1,
            },
            follow=True,
        )
        self.assertContains(response, text="<PERSON><PERSON><PERSON><PERSON>", status_code=200)

        self.assertEqual(LeadCategory.objects.count(), 2)

        user = LeadUser.objects.filter(lead_category=self.lead_category).get()
        self.assertTrue(user.remote_addr)
        self.assertEqual(user.lead_category, self.lead_category)
        self.assertEqual(user.full_name, "nazwa")
        self.assertEqual(user.email, "<EMAIL>")
        self.assertEqual(user.phone_number, "123")

    def test_user_already_exist(self):
        LeadUserFactory.create(
            email="<EMAIL>", lead_category=self.lead_category
        )

        response = self.client.post(
            self.lead_category.get_absolute_url(),
            {
                "full_name": "nazwa",
                "phone_number": "123",
                "email": "<EMAIL>",
                "g-recaptcha-response": "PASSED",
                "tos": 1,
            },
            follow=True,
        )
        self.assertEqual(response.status_code, 200)

        self.assertEqual(LeadCategory.objects.count(), 1)

        form = response.context["form"]
        self.assertIn("email", form.errors)

    def test_mail(self):
        self.client.post(
            self.lead_category.get_absolute_url(),
            {
                "full_name": "nazwa",
                "phone_number": "123",
                "email": "<EMAIL>",
                "g-recaptcha-response": "PASSED",
                "tos": 1,
            },
            follow=True,
        )

        self.assertEqual(len(mail.outbox), 1)

        body = mail.outbox[0].body
        subject = mail.outbox[0].subject

        self.assertEqual(subject, "[Nowy lead] - <EMAIL>")
        self.assertIn("nazwa", body)

    def test_no_captcha(self):
        response = self.client.post(
            self.lead_category.get_absolute_url(),
            {
                "full_name": "nazwa",
                "phone_number": "123",
                "email": "<EMAIL>",
                "g-recaptcha-response": "NOT_PASSED",
                "tos": 1,
            },
            follow=True,
        )
        self.assertNotContains(response, text="Dzięki", status_code=200)
