from django import forms
from django.conf import settings
from django.forms import ValidationError
from nocaptcha_recaptcha.fields import NoReCaptchaField

from .models import LeadUser


class LeadUserForm(forms.ModelForm):
    tos = forms.BooleanField()
    captcha = NoReCaptchaField(
        site_key=settings.NORECAPTCHA_INVISIBLE_SITE_KEY,
        secret_key=settings.NORECAPTCHA_INVISIBLE_SECRET_KEY,
        error_messages={"captcha_invalid": "Niepoprawna weryfikacja reCaptcha."},
    )

    class Meta:
        model = LeadUser
        fields = ("email", "full_name", "phone_number", "tos", "captcha")

    def __init__(self, *args, **kwargs):
        self.lead_category = kwargs.pop("lead_category")
        self.remote_addr = kwargs.pop("remote_addr")
        super(LeadUserForm, self).__init__(*args, **kwargs)

        self.fields["email"].widget.attrs["placeholder"] = "Adres email"
        self.fields["full_name"].widget.attrs["placeholder"] = "Imię i nazwisko"
        self.fields["phone_number"].widget.attrs["placeholder"] = "Numer telefonu"

    def clean_email(self):
        email = self.cleaned_data.get("email")

        if email:
            value = email.lower()
            if LeadUser.objects.filter(
                email=value, lead_category=self.lead_category
            ).exists():
                raise ValidationError(
                    "Taki adres email znajduje sie już w naszej bazie."
                )
        return email

    def save(self, commit=True):
        obj = super(LeadUserForm, self).save(commit=False)
        obj.lead_category = self.lead_category
        obj.remote_addr = self.remote_addr

        if commit:
            obj.save()
        return obj
