import uuid

from django.conf import settings
from django.db import models
from django.db.models import UUIDField
from django.urls import reverse


class LeadCategory(models.Model):
    title = models.CharField("Tytuł", max_length=250, help_text="Widoczny na stronie.")
    slug = models.SlugField(
        max_length=100, unique=True, help_text="Unikalna nazwa. Widoczna w URL."
    )
    content = models.TextField(
        "Opis", blank=True, help_text="Może zawierać HTML. Widoczny na stronie."
    )
    internal_comments = models.TextField("uwagi internal", blank=True)
    is_active = models.BooleanField("aktywny?", default=True)
    language = models.CharField(
        "język",
        max_length=2,
        choices=settings.LANGUAGES,
        default=settings.DEFAULT_LANGUAGE,
    )

    # Daty
    created_at = models.DateTimeField(
        "utworzono", auto_now_add=True, null=True, db_index=True
    )
    updated_at = models.DateTimeField(
        "aktualizowano", auto_now=True, null=True, db_index=True
    )

    class Meta:
        verbose_name = "Kategoria lead"
        verbose_name_plural = "Kategorie lead"
        ordering = ["-created_at"]

    def get_absolute_url(self):
        return reverse(
            "leads:lead", kwargs={"language": self.language, "slug": self.slug}
        )

    def __str__(self):
        return self.slug


class LeadUser(models.Model):
    lead_category = models.ForeignKey(
        LeadCategory, verbose_name="kategoria", on_delete=models.PROTECT
    )

    full_name = models.CharField(
        "imię i nazwisko",
        max_length=200,
    )
    phone_number = models.CharField(
        "telefon",
        max_length=150,
    )
    email = models.EmailField("adres email", max_length=150)

    # Unikalny, losowy klucz
    key = UUIDField(default=uuid.uuid4, editable=False, verbose_name="klucz dostępu")

    finished = models.BooleanField("obsługa zakończona?", default=False)
    internal_comments = models.TextField("uwagi internal", blank=True)
    remote_addr = models.GenericIPAddressField(
        "adres IP", blank=True, null=True, editable=False
    )

    # Daty
    created_at = models.DateTimeField(
        "utworzono", auto_now_add=True, null=True, db_index=True
    )
    updated_at = models.DateTimeField("aktualizowano", auto_now=True, null=True)

    verified_at = models.DateTimeField(
        "zweryfikowany",
        null=True,
        blank=True,
        editable=False,
    )

    class Meta:
        verbose_name = "Użytkownik lead"
        verbose_name_plural = "Użytkownicy lead"
        unique_together = ("lead_category", "email")

    def __str__(self):
        return self.email
