import logging

from django.conf import settings
from django.shortcuts import get_object_or_404, redirect, render
from django.views.decorators.cache import never_cache
from ipware import get_client_ip

from leads.forms import LeadUserForm
from leads.models import LeadCategory
from www.tasks import user_lead
from www.views import activate_translation

logger = logging.getLogger(__name__)


@never_cache
@activate_translation
def lead(request, slug, language=settings.DEFAULT_LANGUAGE):
    lead_category = get_object_or_404(
        LeadCategory, slug=slug, is_active=True, language=language
    )

    form_kwargs = {
        "lead_category": lead_category,
        "remote_addr": get_client_ip(request)[0],
    }

    if request.method == "POST":
        form = LeadUserForm(request.POST, **form_kwargs)

        if form.is_valid():
            lead = form.save()

            user_lead.delay(lead_id=lead.pk, language=language)

            return redirect(
                "leads:lead_done",
                **{
                    "language": language,
                    "slug": slug,
                },
            )
    else:
        form = LeadUserForm(**form_kwargs)

    return render(
        request,
        "leads/lead_form.html",
        {
            "lead_category": lead_category,
            "language": language,
            "form": form,
        },
    )


@never_cache
@activate_translation
def lead_done(request, slug, language=settings.DEFAULT_LANGUAGE):
    lead_category = get_object_or_404(
        LeadCategory, slug=slug, is_active=True, language=language
    )

    return render(
        request,
        "leads/lead_done.html",
        {
            "lead_category": lead_category,
            "language": language,
        },
    )
