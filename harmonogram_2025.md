# Instrukcja używania procedury harmonogram_2025

Procedura `harmonogram_2025` służy do generowania szczegółowego harmonogramu terminów szkolenia w formie tekstowej.

## Sk<PERSON><PERSON>ia
```
[[harmonogram_2025:<id/slug>;<tryb>;<przyszle_czy_w_toku>]]
```

## Parametry
1. **id/slug** (wymagany) - identyfikator szkolenia, może by<PERSON> liczbowym ID lub slugiem
2. **tryb** (wymagany) - tryb szkolenia:
   - `1` - tryb dzienny
   - `2` - tryb wieczorowy
   - `3` - tryb weekendowy/zaoczny
3. **przyszle_czy_w_toku** (opcjonalny) - domyślnie `0`:
   - `0` - przyszłe terminy (jeszcze się nie rozpoczęły)
   - `1` - terminy w toku (roz<PERSON><PERSON><PERSON><PERSON>, ale niezakończone)

## Przykłady użycia

```
[[harmonogram_2025:python-dla-poczatkujacych;1;0]]
```
Wyświetli harmonogram przyszłych terminów szkolenia "python-dla-poczatkujacych" w trybie dziennym.

```
[[harmonogram_2025:123;2;1]]
```
Wyświetli harmonogram terminów w toku szkolenia o ID 123 w trybie wieczorowym.

```
[[harmonogram_2025:java-advanced;3]]
```
Wyświetli harmonogram przyszłych terminów szkolenia "java-advanced" w trybie weekendowym.

## Format wyniku
- Jeśli nie ma terminów dla podanych parametrów, zwraca "-"
- Dla trybu wieczorowego (2): pełna data z nazwą dnia tygodnia, następnie daty pośrednie w formacie DD.MM, na końcu pełna data ostatniego dnia
- Dla trybu dziennego (1) i weekendowego (3): daty pogrupowane w bloki po 2 dni, oddzielone znacznikiem `,<br/>`

## Dodatkowe informacje
Jeśli termin jest oznaczony jako gwarantowany, zostanie to wskazane w następujący sposób:
```html
<p style="color: red; margin: 0px; margin-bottom: 10px; font-size: smaller;">termin gwarantowany</p>
```
