from django.db.models import Q
from django.urls import reverse
from django.conf import settings

from blacklist.models import BlackList


def user_blacklisted(email=None, user_name=None, company_name=None, nip=None):
    filters = {}
    query = Q(email__iexact=email)
    
    if user_name:
        query.add(Q(user_name__icontains=user_name), Q.OR)
    if company_name:
        query.add(Q(company_name__icontains=company_name), Q.OR)
    if nip:
        query.add(Q(nip__iexact=nip), Q.OR)
    return BlackList.objects.filter(query)


def url_to_list(qs):
    links = []
    http_start = "https://" if getattr(settings, "FORCE_SSL", False) else "http://"
    for obj in qs:
        links.append('<a href="{}{}" target="_blank">Typ listy: <strong>{}</strong></a>'.format(
                http_start + settings.DOMENY_DLA_JEZYKOW["pl"],
                reverse("admin:blacklist_blacklist_change", args=(obj.pk,)), 
                obj.get_list_type_display()
            ))
    return links
