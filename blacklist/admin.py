from django.contrib import admin

from .models import BlackList


class BlackListAdmin(admin.ModelAdmin):
    list_display = (
        "email",
        "user_name",
        "company_name",
        "nip",
        "internal_comments",
        "list_type",
        "created_at",
    )
    search_fields = (
        "email",
        "user_name",
        "company_name",
        "nip",
        "internal_comments",
    )
    list_filter = ("list_type", "created_at",)
    fieldsets = (
        (
            None,
            {
                "fields": (
                    "list_type",
                    "email",
                    "user_name",
                    "company_name",
                    "nip",                    
                    "internal_comments",
                )
            },
        ),
    )
    save_on_top = True
    date_hierarchy = "created_at"

admin.site.register(BlackList, BlackListAdmin)
