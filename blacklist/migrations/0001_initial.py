# Generated by Django 2.2.16 on 2023-03-04 14:32

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='BlackList',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email', models.EmailField(blank=True, max_length=200, null=True, unique=True, verbose_name='adres email')),
                ('user_name', models.CharField(blank=True, help_text='Wyszukiwanie po nazwie nie jest tak precyzyjne jak po adresie email/NIP.', max_length=250, null=True, unique=True, verbose_name='nazwa użytkownika')),
                ('company_name', models.CharField(blank=True, help_text='Wyszukiwanie po nazwie nie jest tak precyzyjne jak po adresie email/NIP.', max_length=250, null=True, unique=True, verbose_name='nazwa firmy')),
                ('nip', models.Char<PERSON>ield(blank=True, help_text='Numer NIP bez myślników.', max_length=40, null=True, unique=True, verbose_name='NIP')),
                ('list_type', models.CharField(choices=[('black', 'czarna'), ('grey', 'szara')], default='black', max_length=20, verbose_name='typ listy')),
                ('internal_comments', models.TextField(verbose_name='uwagi internal')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True, null=True, verbose_name='utworzono')),
                ('updated_at', models.DateTimeField(auto_now=True, null=True, verbose_name='aktualizowano')),
            ],
            options={
                'verbose_name': 'Czarna/szara lista',
                'verbose_name_plural': 'Czarna/szara lista',
            },
        ),
    ]
